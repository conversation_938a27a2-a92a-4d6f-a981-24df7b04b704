#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
import sys
import os

def inspect_excel(file_path):
    """
    检查Excel文件结构，显示列名和前几行数据
    
    Args:
        file_path: Excel文件路径
    """
    print(f"检查文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"错误: 文件 {file_path} 不存在")
        return
    
    # 使用pandas读取
    try:
        print("\n=== 使用pandas读取 ===")
        df = pd.read_excel(file_path)
        print(f"列名: {list(df.columns)}")
        print("\n前5行数据:")
        print(df.head())
        
        # 检查是否有空列名
        null_cols = [i for i, col in enumerate(df.columns) if pd.isna(col)]
        if null_cols:
            print(f"\n警告: 发现空列名 (索引): {null_cols}")
    except Exception as e:
        print(f"pandas读取失败: {e}")
    
    # 使用openpyxl直接读取单元格
    try:
        print("\n=== 使用openpyxl直接读取 ===")
        wb = openpyxl.load_workbook(file_path)
        ws = wb.active
        
        # 显示表头
        headers = [ws.cell(row=1, column=i).value for i in range(1, ws.max_column + 1)]
        print(f"表头: {headers}")
        
        # 显示数据结构
        print("\n数据结构:")
        for r in range(1, min(5, ws.max_row) + 1):
            row_data = [ws.cell(row=r, column=c).value for c in range(1, ws.max_column + 1)]
            print(f"行 {r}: {row_data}")
        
        # 检查合并单元格
        if ws.merged_cells:
            print("\n合并单元格:")
            for merged_range in ws.merged_cells.ranges:
                print(f"  - {merged_range} (值: {ws.cell(merged_range.min_row, merged_range.min_col).value})")
    except Exception as e:
        print(f"openpyxl读取失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = input("请输入Excel文件路径: ")
    
    inspect_excel(file_path) 
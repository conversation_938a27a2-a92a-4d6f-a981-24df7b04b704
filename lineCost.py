# Description: 读取广域网线路表，根据运营商费用报价表计算费用，并保存到新的 Excel 文件中。

import pandas as pd

# 读取运营商费用报价表
df_cost = pd.read_excel('files/广域网线路表.xlsx', sheet_name='运营商费用报价')

# 构建费用字典
cost_map = {}
for idx, row in df_cost.iterrows():
    line_type = row['月度费用']
    bandwidth_str = str(row['带宽'])
    operators = ['电信', '联通', '移动', '中鹏通', '电信通']

    # 处理带宽
    bandwidth = None
    if line_type in ['同城专线', '长途专线', '本地互联网']:
        digits = ''.join(filter(str.isdigit, bandwidth_str))
        if digits:
            bandwidth = int(digits)
    else:
        bandwidth = bandwidth_str.strip()  # 如裸光纤的59KM

    if bandwidth is None:
        continue

    # 存储运营商费用
    op_costs = {}
    for op in operators:
        cost = row[op]
        if pd.notna(cost):
            op_costs[op] = cost

    if op_costs:
        cost_map[(line_type, bandwidth)] = op_costs


def calculate_fee(row):
    line_type = row['线路类型']
    bandwidth = row['带宽']
    operator = row['运营商']

    # 处理带宽值
    parsed_bandwidth = None
    if line_type in ['同城专线', '长途专线', '本地互联网']:
        if pd.isna(bandwidth):
            return None
        # 提取数字部分
        if isinstance(bandwidth, (int, float)):
            parsed_bandwidth = int(bandwidth)
        else:
            digits = ''.join(filter(str.isdigit, str(bandwidth)))
            if digits:
                parsed_bandwidth = int(digits)
    else:
        # 其他类型如裸光纤，使用原始带宽字符串（但现状中多为n/a）
        parsed_bandwidth = str(bandwidth).strip() if pd.notna(bandwidth) else None

    if parsed_bandwidth is None:
        return None

    # 查找费用
    key = (line_type, parsed_bandwidth)
    if key not in cost_map:
        return None

    return cost_map[key].get(operator, None)


# 处理现状明细表
df_current = pd.read_excel('files/广域网线路表.xlsx', sheet_name='现状明细')
df_current['费用'] = df_current.apply(calculate_fee, axis=1)

# 处理2026年规划表
df_plan = pd.read_excel('files/广域网线路表.xlsx', sheet_name='2026年规划')
df_plan['费用'] = df_plan.apply(calculate_fee, axis=1)

# 保存结果
with pd.ExcelWriter('output/费用.xlsx') as writer:
    df_current.to_excel(writer, sheet_name='现状明细', index=False)
    df_plan.to_excel(writer, sheet_name='2026年规划', index=False)
    df_cost.to_excel(writer, sheet_name='运营商费用报价', index=False)

print("费用计算完成，结果已保存到-费用.xlsx")

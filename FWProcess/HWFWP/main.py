import subprocess

# 按顺序执行多个Python文件
H3CFWP_to_execute = ['HWFWP_西五环互联网A区DMZ.py',
                     'HWFWP_西五环互联网B区DMZ.py',
                     'HWFWP_西五环外联区DMZ.py',
                     'HWFWP_BJUAT1FWA03.py']

JTE_to_execute = [
    'JTE_西五环互联网A区DMZ.py',
    'JTE_西五环互联网B区DMZ.py',
    'JTE_西五环外联区DMZ.py',
    'JTE_BJUAT1FWA03.py',
]

for file in H3CFWP_to_execute:
    subprocess.run(['python', file])

for file in JTE_to_execute:
    subprocess.run(['python', file])

import json
from pathlib import Path
from openpyxl import Workbook


def read_json_file(file_path):
    """读取并返回JSON文件的内容。"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except json.JSONDecodeError as e:
        print(f"读取JSON文件错误 {file_path}: {e}")
        return None


def create_directory_if_not_exists(directory_path):
    """如果目录不存在，则创建目录。"""
    directory_path.mkdir(parents=True, exist_ok=True)


def get_addresses(entry):
    """从条目中提取并返回地址作为字符串。"""
    addresses_list = entry.get('addresses', [])
    return ', '.join(addr.get('ip') or addr.get('range') or addr.get('address-set') or '' for addr in addresses_list)


def get_service(entry):
    """从条目中提取并返回TCP和UDP端口。"""
    services_list = entry.get('service', [])
    tcp_ports = ', '.join(
        svc.get('destination-port') for svc in services_list if svc.get('protocol', '').lower() == 'tcp')
    udp_ports = ', '.join(
        svc.get('destination-port') for svc in services_list if svc.get('protocol', '').lower() == 'udp')
    return tcp_ports, udp_ports


def write_data_to_excel(json_data, wb, sheet_name, headers, data_extractor):
    """根据提供的标题和数据提取函数，将JSON数据写入Excel工作表。"""
    ws = wb.create_sheet(title=sheet_name)
    ws.append(headers)

    for vsys_name, items in json_data.items():
        for item in items:
            data_row = data_extractor(vsys_name, item)
            ws.append(data_row)

    print(f"{sheet_name} 工作表已成功添加到工作簿中")


def extract_service_data(vsys_name, service):
    """提取服务数据并作为行返回。"""
    service_name = service.get('name', '')
    tcp_ports, udp_ports = get_service(service)
    return [vsys_name, service_name, tcp_ports, udp_ports]


def extract_address_data(vsys_name, entry):
    """提取地址数据并作为行返回。"""
    address_name = entry.get('address_name', '')
    addresses = get_addresses(entry)
    return [vsys_name, address_name, addresses]


def extract_rule_data(vsys_name, entry):
    """提取规则数据并作为行返回。"""
    rule_data = [
        vsys_name,
        entry.get('rule_name', ''),
        entry.get('action', ''),
        entry.get('source_zone', ''),
        entry.get('source_ip', ''),
        entry.get('destination_ip', ''),
        entry.get('destination_zone', ''),
        entry.get('service', '')
    ]
    return rule_data


def process_json_to_excel(json_folder_path, excel_output_path):
    """处理文件夹中的所有JSON文件，并将它们写入Excel文件。"""
    json_folder_path = Path(json_folder_path)
    excel_output_path = Path(excel_output_path)

    create_directory_if_not_exists(excel_output_path.parent)

    wb = Workbook()
    wb.remove(wb.active)  # 移除默认创建的空工作表

    # 定义一个字典来映射文件名到处理函数和标题
    processing_functions = {
        'service.json': {
            'headers': ['VSYS_NAME', 'service_name', 'tcp', 'udp'],
            'function': extract_service_data
        },
        'address.json': {
            'headers': ['VSYS_NAME', 'address_name', 'address'],
            'function': extract_address_data
        },
        'rule.json': {
            'headers': ['VSYS_NAME', 'rule_name', 'action', 'source_zone', 'source_ip', 'destination_ip',
                        'destination_zone', 'service'],
            'function': extract_rule_data
        }
    }

    for json_file_path in json_folder_path.glob('*.json'):
        sheet_name = json_file_path.stem
        json_data = read_json_file(json_file_path)
        if json_data is None:
            continue

        processing_info = processing_functions.get(json_file_path.name)
        if processing_info:
            write_data_to_excel(
                json_data,
                wb,
                sheet_name,
                processing_info['headers'],
                processing_info['function']
            )
        else:
            print(f"未知的JSON文件类型: {json_file_path.name}")

    wb.save(excel_output_path)
    print(f"Excel文件已成功创建，路径为：{excel_output_path}")


# 调用处理函数
json_folder_path = '../files/HuaWei/json/BJUAT1FWA03'
excel_folder_path = '../files/HuaWei/Excel'
excel_output_path = Path(excel_folder_path) / 'BJUAT1FWA03_18.4.0.24.xlsx'

process_json_to_excel(json_folder_path, excel_output_path)

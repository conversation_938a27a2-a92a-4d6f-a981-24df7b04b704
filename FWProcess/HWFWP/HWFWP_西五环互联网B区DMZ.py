import json
import os
import re


def process_service(block):
    service_sets = block.split('#')
    for service_set in service_sets:
        lines = service_set.strip().splitlines()
        entry = None

        for line in lines:
            if line.startswith('ip service-set'):
                if entry:  # 如果已经开始了一个新的 address set，则先 yield 当前的 entry
                    yield entry

                header = re.split(r'(?!\B"[^"]*)\s(?![^"]*"\B)', line)

                service_name = header[2].strip("\"")
                service_type = header[4]

                entry = {  # 开始一个新的 address set
                    'name': service_name,
                    'type': service_type,
                    'service': [],
                }

            elif line.startswith(' description') and entry:
                entry['description'] = line.split(' description', 1)[1].strip()

            elif line.startswith(' service') and entry:
                parts = line.split()
                service_details = {
                    'protocol': parts[3],
                    'destination-port': parts[-1]
                }
                entry['service'].append(service_details)

        if entry:  # 确保最后一个 address set 也被 yield
            yield entry


def process_address(block):
    address_sets = block.split('#')
    for address_set in address_sets:
        lines = address_set.strip().splitlines()
        entry = None

        for line in lines:
            if line.startswith('ip address-set'):
                if entry:  # 如果已经开始了一个新的 address set，则先 yield 当前的 entry
                    yield entry

                header = re.split(r'(?!\B"[^"]*)\s(?![^"]*"\B)', line)

                address_name = header[2].strip("\"")
                address_type = header[4]

                entry = {  # 开始一个新的 address set
                    'address_name': address_name,
                    'type': address_type,
                    'addresses': [],
                }

            elif line.startswith(' description') and entry:
                entry['description'] = line.split(' description', 1)[1].strip()

            elif line.startswith(' address') and entry:
                parts = line.split()
                if entry['type'] == 'object':
                    if parts[2] == 'range':
                        entry['addresses'].append({'range': f"{parts[3]} {parts[4]}"})
                    else:
                        if 'mask' in parts:
                            cidr = f"{parts[2]}/{parts[4]}"
                            entry['addresses'].append({'ip': cidr})
                        else:
                            cidr = f"{parts[2]}"
                            print(cidr)
                            entry['addresses'].append({'ip': cidr})
                elif entry['type'] == 'group':
                    entry['addresses'].append({'address-set': f"{parts[3]}"})

        if entry:  # 确保最后一个 address set 也被 yield
            yield entry


def process_rule(block):
    lines = block.splitlines()
    rules = []
    current_rule = None

    for line in lines:
        line = line.strip()

        if line.startswith('rule name'):
            if current_rule:
                # 将当前规则的IP、服务以及区域列表转换为字符串
                for key in ['source_ip', 'destination_ip', 'service', 'source_zone', 'destination_zone']:
                    if current_rule[key]:  # 如果列表不为空
                        current_rule[key] = ','.join(current_rule[key])
                    else:
                        current_rule[key] = 'any'
                rules.append(current_rule)

            rule_name = re.search(r'rule name\s+"?([^"\n]+)"?', line).group(1)
            current_rule = {
                'rule_name': rule_name,
                'action': 'drop',
                'source_zone': [],
                'source_ip': [],
                'destination_ip': [],
                'destination_zone': [],
                'service': [],
            }

        elif current_rule:
            if 'source-zone' in line:
                current_rule['source_zone'].append(line.split()[-1])
            elif 'destination-zone' in line:
                current_rule['destination_zone'].append(line.split()[-1])
            elif 'source-address address-set' in line:
                current_rule['source_ip'].append(line.split()[-1])
            elif 'destination-address address-set' in line:
                current_rule['destination_ip'].append(line.split()[-1])
            elif line.startswith('service'):
                current_rule['service'].append(line.split(' ')[1])
            elif 'action' in line:
                current_rule['action'] = line.split()[-1]

    # 处理最后一条规则
    if current_rule:
        # 将当前规则的IP、服务以及区域列表转换为字符串
        for key in ['source_ip', 'destination_ip', 'service', 'source_zone', 'destination_zone']:
            if current_rule[key]:  # 如果列表不为空
                current_rule[key] = ','.join(current_rule[key])
            else:
                current_rule[key] = 'any'
        rules.append(current_rule)

    return rules


def process_blocks(filepath, output_dir):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    address_data = {}
    service_data = {}
    rule_data = {}

    with open(filepath, 'r', encoding='utf-8') as file:
        content = file.read()

    vsys_blocks = content.split('switch vsys')[1:]
    default_block = content.split('switch vsys')[0]
    address_data['default'] = list(process_address(default_block))
    service_data['default'] = list(process_service(default_block))
    rule_data['default'] = list(process_rule(default_block))

    for block in vsys_blocks:
        block_name = block.splitlines()[0].strip()
        address_data[block_name] = list(process_address(block))

    for block in vsys_blocks:
        block_name = block.splitlines()[0].strip()
        service_data[block_name] = list(process_service(block))

    for block in vsys_blocks:
        block_name = block.splitlines()[0].strip()
        rule_data[block_name] = list(process_rule(block))

    # 生成 address.json
    address_output_filename = os.path.join(output_dir, 'address.json')
    generate_json_file(address_data, address_output_filename)

    # 生成 service.json
    service_output_filename = os.path.join(output_dir, 'service.json')
    generate_json_file(service_data, service_output_filename)

    # 生成 rule.json
    rule_output_filename = os.path.join(output_dir, 'rule.json')
    generate_json_file(rule_data, rule_output_filename)

    # 返回路径信息
    return os.path.abspath(address_output_filename), os.path.abspath(service_output_filename), os.path.abspath(
        rule_output_filename)


# 专门的函数用于生成 JSON 文件
def generate_json_file(data, output_filepath):
    with open(output_filepath, 'w', encoding='utf-8') as outfile:
        json.dump(data, outfile, indent=4, ensure_ascii=False)


def main():
    input_filepath = '../files/HuaWei/XWLCFW01-B1F05.txt'
    output_directory = '../files/HuaWei/json/西五环互联网B区DMZ'
    address_file_path, service_file_path, rule_file_path = process_blocks(input_filepath, output_directory)
    print(f"地址数据 JSON 文件已创建在: {address_file_path}")
    print(f"地址数据 JSON 文件已创建在: {service_file_path}")
    print(f"地址数据 JSON 文件已创建在: {rule_file_path}")


if __name__ == '__main__':
    main()

# 西五环互联网A区DMZ_XWBCFW01-B1F01_4.255.253.229

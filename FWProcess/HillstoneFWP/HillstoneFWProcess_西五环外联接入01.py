import os
import json
import re

'''
这个函数用于处理输入文件中的文本内容，并将其转换为JSON格式后写入输出文件中。
函数中的convert_text_to_json函数用于将文本中的特定信息提取出来，并以JSON对象的形式进行表示。
在函数执行过程中，输入文件被打开并读取其中的文本内容，然后将其传递给convert_text_to_json函数进行处理。
最后，生成的JSON数据被写入输出文件中，以便后续使用。
'''


def process_zone(input_filepath, output_filepath):
    def convert_text_to_json(text):
        pattern = re.compile(r'zone "(.+)"\s+vrouter "(.+)"')
        matches = pattern.findall(text)
        return [{'zone': zone, 'vrouter': vrouter} for zone, vrouter in matches]

    with open(input_filepath, 'r', encoding='utf-8') as file:
        text = file.read()

    json_data = convert_text_to_json(text)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(json_data, file, ensure_ascii=False, indent=4)


def process_service(input_filepath, output_filepath):
    services = []
    with open(input_filepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith('service'):
                service = line.split('"')[1]  # 提取双引号中的内容作为服务名称
                i += 1
                tcp = {}
                udp = {}
                while i < len(lines) and not lines[i].strip() == 'exit':
                    parts = lines[i].strip().split(' ')
                    if parts[0] == 'tcp':
                        if 'timeout' in parts:
                            tcp['dst-port'] = parts[2]
                            tcp['timeout'] = parts[4]
                        else:
                            tcp['dst-port'] = ' '.join(parts[2:])
                    elif parts[0] == 'udp':
                        if 'timeout' in parts:
                            udp['dst-port'] = parts[2]
                            udp['timeout'] = parts[4]
                        else:
                            udp['dst-port'] = ' '.join(parts[2:])
                    i += 1
                service_dict = {'service': service}
                if tcp:
                    service_dict['tcp'] = tcp
                if udp:
                    service_dict['udp'] = udp
                services.append(service_dict)
            i += 1
    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(services, file, ensure_ascii=False, indent=4)


def process_rules(input_filepath, output_filepath):
    rules = []
    with open(input_filepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith('rule id'):
                rule_id = line.split(' ')[2]
                rule = {"rule": rule_id}
                i += 1
                while i < len(lines) and not lines[i].strip() == 'exit':
                    parts = lines[i].strip().split(' ', 1)
                    key = parts[0]
                    value = parts[1].replace('\"', '') if len(parts) > 1 else None

                    # convert src-ip, src-range to src-addr, and dst-ip, dst-range to dst-addr
                    if key == 'src-ip' or key == 'src-range':
                        key = 'src-addr'
                    elif key == 'dst-ip' or key == 'dst-range':
                        key = 'dst-addr'

                    if key in rule:
                        if isinstance(rule[key], list):
                            rule[key].append(value)
                        else:
                            rule[key] = [rule[key], value]
                        rule[key] = ', '.join(rule[key])
                    else:
                        rule[key] = value
                    i += 1

                if 'disable' in rule and rule['disable'] is None:
                    rule['disable'] = True  # Set disable to True if it is None

                rules.append(rule)
            i += 1
    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(rules, file, ensure_ascii=False, indent=4)


def process_address(input_filepath, output_filepath):
    data = []
    with open(input_filepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith('address'):
                address_name = line.split(' ')[1].replace('"', '')  # 移除双引号
                i += 1
                ip_name = []
                while not lines[i].strip() == 'exit':
                    values = lines[i].strip().split(' ')
                    ip_type = values[0]
                    ip_value = ' '.join(values[1:])
                    ip_name.append({'type': ip_type, 'value': ip_value})
                    i += 1
                data.append({'address_name': address_name, 'ip': ip_name})
            i += 1
    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)


def process_nat(input_filepath, output_filepath):
    data = {}

    with open(input_filepath, 'r', encoding="utf-8") as file:
        lines = file.readlines()

    vrouter = None
    for line in lines:
        if line.startswith('ip vrouter'):
            vrouter = re.search(r'ip vrouter "(.*?)"', line).group(1)
            data[vrouter] = []
        elif 'snatrule' in line or 'dnatrule' in line:
            rule_type = 'snatrule' if 'snatrule' in line else 'dnatrule'
            rule = {"Type": rule_type}

            id_match = re.search(r'id (\d+)', line)
            if id_match:
                rule['id'] = id_match.group(1)

            ingress_match = re.search(r'ingress-interface "(.*?)"', line)
            if ingress_match:
                rule['ingress-interface'] = ingress_match.group(1)

            if 'from address-book' in line:
                from_match = re.search(r'from address-book "(.*?)"', line)
                if from_match:
                    rule['from'] = from_match.group(1).strip("\"")
            elif 'from ip' in line:
                from_match = re.search(r'from ip (\S+)', line)
                if from_match:
                    rule['from'] = from_match.group(1).strip("\"")
            elif 'from' in line:
                from_match = re.search(r'from "(.*?)"', line)
                if from_match:
                    rule['from'] = from_match.group(1).strip("\"")

            if 'to address-book' in line:
                to_match = re.search(r'to address-book "(.*?)"', line)
                if to_match:
                    rule['to'] = to_match.group(1).strip("\"")
            elif 'to ip' in line:
                to_match = re.search(r'to ip (\S+)', line)
                if to_match:
                    rule['to'] = to_match.group(1).strip("\"")
            elif 'to' in line:
                to_match = re.search(r'to (\S+)', line)
                if to_match:
                    rule['to'] = to_match.group(1).strip("\"")

            service_match = re.search(r'service "(.*?)"', line)
            if service_match:
                rule['service'] = service_match.group(1).strip("\"")

            eif_match = re.search(r'eif (.*?) ', line)
            if eif_match:
                rule['eif'] = eif_match.group(1)

            if 'trans-to address-book' in line:
                trans_to_match = re.search(r'trans-to address-book "(.*?)"', line)
                if trans_to_match:
                    rule['trans-to'] = trans_to_match.group(1).strip("\"")
            elif 'trans-to ip' in line:
                trans_to_match = re.search(r'trans-to ip (\S+)', line)
                if trans_to_match:
                    rule['trans-to'] = trans_to_match.group(1).strip("\"")
            elif 'trans-to' in line:
                trans_to_match = re.search(r'trans-to (\S+)', line)
                if trans_to_match:
                    rule['trans-to'] = trans_to_match.group(1).strip("\"")

            port_match = re.search(r'port (\d+)', line)
            if port_match:
                rule['port'] = port_match.group(1).strip("\"")

            mode_match = re.search(r'mode (\w+)', line)
            if mode_match:
                rule['mode'] = mode_match.group(1).strip("\"")

            rule['log'] = 'log' in line

            data[vrouter].append(rule)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)



'''
这段代码首先定义了输入和输出文件的路径。
然后通过调用check_directory函数，检查并创建输出文件夹（如果不存在）。
接下来，依次调用process_zone、process_service、process_rules、process_address和process_nat函数来处理相应的输入文件，并将处理结果写入输出文件中。
最后，输出处理完成的提示信息。
'''


# 创建输出文件夹，如果不存在
def check_directory(output_filepath):
    os.makedirs(os.path.dirname(output_filepath), exist_ok=True)


if __name__ == "__main__":
    print('开始处理!')

    # 定义输入和输出文件路径
    # 生产一区防火墙
    zone_input_filepath = "../files/HillStone/西五环外联接入01/zone.txt"
    zone_output_filepath = "../files/HillStone/json/西五环外联接入01/zone.json"
    service_input_filepath = "../files/HillStone/西五环外联接入01/service.txt"
    service_output_filepath = "../files/HillStone/json/西五环外联接入01/service.json"
    rules_input_filepath = "../files/HillStone/西五环外联接入01/rule.txt"
    rules_output_filepath = "../files/HillStone/json/西五环外联接入01/rule.json"
    address_input_filepath = "../files/HillStone/西五环外联接入01/address.txt"
    address_output_filepath = "../files/HillStone/json/西五环外联接入01/address.json"
    nat_input_filepath = '../files/HillStone/西五环外联接入01/nat.txt'
    nat_output_filepath = '../files/HillStone/json/西五环外联接入01/nat.json'

    # 检查并创建输出文件夹（如果不存在）
    check_directory(zone_output_filepath)
    check_directory(service_output_filepath)
    check_directory(rules_output_filepath)
    check_directory(address_output_filepath)
    check_directory(nat_output_filepath)

    # 调用函数处理文件
    process_zone(zone_input_filepath, zone_output_filepath)
    process_service(service_input_filepath, service_output_filepath)
    process_rules(rules_input_filepath, rules_output_filepath)
    process_address(address_input_filepath, address_output_filepath)
    process_nat(nat_input_filepath, nat_output_filepath)

    print('处理完成!')

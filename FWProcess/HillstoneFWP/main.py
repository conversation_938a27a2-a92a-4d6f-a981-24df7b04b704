import subprocess

# 按顺序执行多个Python文件
HillstoneFWP_to_execute = ['HillstoneFWProcess_1区.py',
                           'HillstoneFWProcess_2区.py',
                           'HillstoneFWProcess_亦庄外联DMZ.py',
                           'HillstoneFWProcess_亦庄核心.py',
                           'HillstoneFWProcess_亦庄测试.py',
                           'HillstoneFWProcess_亦庄管理防火墙.py',
                           'HillstoneFWProcess_竞猜DMZ.py',
                           'HillstoneFWProcess_西五环互联网A接入.py',
                           'HSFWP_西五环互联网B接入.py',
                           'HillstoneFWProcess_西五环内联接入.py',
                           'HillstoneFWProcess_西五环外联接入01.py',
                           'HillstoneFWProcess_西五环外联接入02.py',
                           'HillstoneFWProcess_西五环测试B3.py',
                           'HillstoneFWProcess_西五环管理防火墙.py']

JTE_to_execute = [
    'JTE_1区.py',
    'JTE_2区.py',
    'JTE_亦庄外联DMZ.py',
    'JTE_亦庄核心.py',
    'JTE_亦庄测试.py',
    'JTE_亦庄管理防火墙.py',
    'JTE_竞猜DMZ区.py',
    'JTE_西五环互联网A接入.py',
    'JTE_西五环互联网B接入.py',
    'JTE_西五环内联接入.py',
    'JTE_西五环外联接入01.py',
    'JTE_西五环外联接入02.py',
    'JTE_西五环测试B3.py',
    'JTE_西五环管理防火墙.py',
]

for file in HillstoneFWP_to_execute:
    subprocess.run(['python', file])

for file in JTE_to_execute:
    subprocess.run(['python', file])

import os
import json
import re

''' 
统一处理配置文件，不再拆分多个文件 


这段代码首先定义了输入和输出文件的路径。
然后通过调用check_directory函数，检查并创建输出文件夹（如果不存在）。
接下来，依次调用process_zone、process_service、process_rules、process_address和process_nat函数来处理相应的输入文件，并将处理结果写入输出文件中。
最后，输出处理完成的提示信息。
'''


def process_service(input_filepath, output_filepath):
    services = []
    with open(input_filepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith('service'):
                service = line.split('"')[1]  # 提取双引号中的内容作为服务名称
                print(service)
                i += 1
                if i < len(lines):
                    next_line = lines[i].strip()
                    if next_line.startswith('tcp') or next_line.startswith('udp'):
                        protocol = next_line.split(' ')[0]  # 提取协议类型，如tcp或udp
                        ports = ' '.join(next_line.split(' ')[2:])  # 提取端口信息，包含整行所有字符
                        service_dict = {'service': service}
                        if protocol == 'tcp':
                            service_dict['tcp'] = {'dst-port': ports}
                        elif protocol == 'udp':
                            service_dict['udp'] = {'dst-port': ports}
                        services.append(service_dict)
            i += 1
    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(services, file, ensure_ascii=False, indent=4)


'''
def process_address(input_filepath, output_filepath):
    data = []
    with open(input_filepath, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith('address'):
                address_name = line.split(' ')[1].replace('"', '')  # 移除双引号
                i += 1
                ip_name = []
                while not lines[i].strip() == 'exit':
                    values = lines[i].strip().split(' ')
                    ip_type = values[0]
                    ip_value = ' '.join(values[1:]).strip("\"")
                    ip_name.append({'type': ip_type, 'value': ip_value})
                    i += 1
                data.append({'address_name': address_name, 'ip': ip_name})
            i += 1
    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)
'''


def process_address(input_filepath, output_filepath):
    data = []

    with open(input_filepath, 'r', encoding='utf-8') as file:
        address_name = None
        ip_name = []

        for line in file:
            line = line.strip()

            if line.startswith('address'):
                if address_name and ip_name:
                    data.append({'address_name': address_name, 'ip': ip_name})

                address_name = line.split(' ')[1].replace('"', '')  # 移除双引号
                ip_name = []
            elif line.startswith('exit'):
                if address_name and ip_name:
                    data.append({'address_name': address_name, 'ip': ip_name})
                address_name = None
                ip_name = []
            else:
                values = line.strip().split(' ')
                ip_type = values[0]
                ip_value = ' '.join(values[1:]).strip("\"")
                ip_name.append({'type': ip_type, 'value': ip_value})

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)


def process_zone(input_filepath, output_filepath):
    def convert_text_to_json(text):
        pattern = re.compile(r'zone "(.+)"\s+vrouter "(.+)"')
        matches = pattern.findall(text)
        return [{'zone': zone, 'vrouter': vrouter} for zone, vrouter in matches]

    with open(input_filepath, 'r', encoding='utf-8') as file:
        text = file.read()

    json_data = convert_text_to_json(text)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(json_data, file, ensure_ascii=False, indent=4)


def process_nat(input_filepath, output_filepath):
    data = {}

    with open(input_filepath, 'r', encoding="utf-8") as file:
        vrouter = None
        for line in file:
            line = line.strip()

            if line.startswith('ip vrouter'):
                vrouter = re.search(r'ip vrouter "(.*?)"', line).group(1)
                data[vrouter] = []
            elif 'snatrule' in line or 'dnatrule' in line:
                rule_type = 'snatrule' if 'snatrule' in line else 'dnatrule'
                rule = {"Type": rule_type}

                id_match = re.search(r'id (\d+)', line)
                if id_match:
                    rule['id'] = id_match.group(1)

                ingress_match = re.search(r'ingress-interface "(.*?)"', line)
                if ingress_match:
                    rule['ingress-interface'] = ingress_match.group(1)

                if 'from address-book' in line:
                    from_match = re.search(r'from address-book "(.*?)"', line)
                    if from_match:
                        rule['from'] = from_match.group(1).strip("\"")
                elif 'from ip' in line:
                    from_match = re.search(r'from ip (\S+)', line)
                    if from_match:
                        rule['from'] = from_match.group(1).strip("\"")
                elif 'from' in line:
                    from_match = re.search(r'from "(.*?)"', line)
                    if from_match:
                        rule['from'] = from_match.group(1).strip("\"")

                if 'to address-book' in line:
                    to_match = re.search(r'to address-book "(.*?)"', line)
                    if to_match:
                        rule['to'] = to_match.group(1).strip("\"")
                elif 'to ip' in line:
                    to_match = re.search(r'to ip (\S+)', line)
                    if to_match:
                        rule['to'] = to_match.group(1).strip("\"")
                elif 'to' in line:
                    to_match = re.search(r'to (\S+)', line)
                    if to_match:
                        rule['to'] = to_match.group(1).strip("\"")

                service_match = re.search(r'service "(.*?)"', line)
                if service_match:
                    rule['service'] = service_match.group(1).strip("\"")

                eif_match = re.search(r'eif (.*?) ', line)
                if eif_match:
                    rule['eif'] = eif_match.group(1)

                if 'trans-to address-book' in line:
                    trans_to_match = re.search(r'trans-to address-book "(.*?)"', line)
                    if trans_to_match:
                        rule['trans-to'] = trans_to_match.group(1).strip("\"")
                elif 'trans-to ip' in line:
                    trans_to_match = re.search(r'trans-to ip (\S+)', line)
                    if trans_to_match:
                        rule['trans-to'] = trans_to_match.group(1).strip("\"")
                elif 'trans-to' in line:
                    trans_to_match = re.search(r'trans-to (\S+)', line)
                    if trans_to_match:
                        rule['trans-to'] = trans_to_match.group(1).strip("\"")

                port_match = re.search(r'port (\d+)', line)
                if port_match:
                    rule['port'] = port_match.group(1).strip("\"")

                mode_match = re.search(r'mode (\w+)', line)
                if mode_match:
                    rule['mode'] = mode_match.group(1).strip("\"")

                rule['log'] = 'log' in line

                data[vrouter].append(rule)

    # Remove empty lists from the data
    data = {k: v for k, v in data.items() if v}

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)


def process_rules(input_filepath, output_filepath):
    rules = []

    with open(input_filepath, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()
            if line.startswith('rule id'):
                rule_id = line.split(' ')[2]
                rule = {"rule": rule_id}
                for line in file:
                    line = line.strip()
                    if line.strip() == 'exit':
                        break
                    parts = line.strip().split(' ', 1)
                    key = parts[0]
                    value = parts[1].replace('\"', '') if len(parts) > 1 else None

                    # convert src-ip, src-range to src-addr, and dst-ip, dst-range to dst-addr
                    if key == 'src-ip' or key == 'src-range':
                        key = 'src-addr'
                    elif key == 'dst-ip' or key == 'dst-range':
                        key = 'dst-addr'

                    if key in rule:
                        if isinstance(rule[key], list):
                            rule[key].append(value)
                        else:
                            rule[key] = [rule[key], value]
                        rule[key] = ', '.join(rule[key])
                    else:
                        rule[key] = value

                if 'disable' in rule and rule['disable'] is None:
                    rule['disable'] = True  # Set disable to True if it is None

                rules.append(rule)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(rules, file, ensure_ascii=False, indent=4)


# 创建输出文件夹，如果不存在
def check_directory(output_filepath):
    os.makedirs(os.path.dirname(output_filepath), exist_ok=True)


if __name__ == "__main__":
    print('开始处理!')

    # 检查并创建输出文件夹（如果不存在）
    input_filepath = "../files/HillStone/XWLAFW01-B1F03.txt"
    zone_output_filepath = "../files/HillStone/json/西五环互联网B接入/zone.json"
    service_output_filepath = "../files/HillStone/json/西五环互联网B接入/service.json"
    rules_output_filepath = "../files/HillStone/json/西五环互联网B接入/rule.json"
    address_output_filepath = "../files/HillStone/json/西五环互联网B接入/address.json"
    nat_output_filepath = '../files/HillStone/json/西五环互联网B接入/nat.json'

    check_directory(zone_output_filepath)
    check_directory(service_output_filepath)
    check_directory(rules_output_filepath)
    check_directory(address_output_filepath)
    check_directory(nat_output_filepath)

    # 调用函数处理文件
    process_service(input_filepath, service_output_filepath)
    process_address(input_filepath, address_output_filepath)
    process_zone(input_filepath, zone_output_filepath)
    process_nat(input_filepath, nat_output_filepath)
    process_rules(input_filepath, rules_output_filepath)
    print('处理完成!')

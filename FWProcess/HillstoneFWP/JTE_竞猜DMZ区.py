import pandas as pd
import json
import os


def process_rule_json(rule_data, zone_data):
    zone_dict = {item['zone']: item['vrouter'] for item in zone_data}

    for rule in rule_data:
        src_zone = rule.get('src-zone', 'default_zone')
        dst_zone = rule.get('dst-zone', 'default_zone')

        rule['src-vrouter'] = zone_dict.get(src_zone, '')
        rule['dst-vrouter'] = zone_dict.get(dst_zone, '')

    return rule_data


def process_address_json(address_data):
    processed_address_data = []
    for entry in address_data:
        address_name = entry.get('address_name', '')
        ip_values = []
        ip_list = entry.get('ip', [])
        for ip_entry in ip_list:
            ip_value = ip_entry.get('value', '')
            ip_values.append(ip_value)
        ip_values_str = ', '.join(ip_values)
        processed_address_data.append({'address_name': address_name, 'value': ip_values_str})

    return processed_address_data


def process_service_json(service_data):
    processed_service_data = []
    for entry in service_data:
        service_name = entry.get('service', '')
        tcp_value = entry.get('tcp', '')
        udp_value = entry.get('udp', '')
        processed_service_data.append({'service': service_name, 'tcp': tcp_value, 'udp': udp_value})

    return processed_service_data


def load_json_files(input_directory, filenames):
    data_dict = {}

    for filename in filenames:
        filepath = os.path.join(input_directory, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data_dict[filename] = json.load(f)
        except FileNotFoundError:
            print(f"文件未找到：{filepath}")
            data_dict[filename] = []
        except json.JSONDecodeError:
            print(f"读取JSON文件时出错：{filepath}")
            data_dict[filename] = []

    return data_dict


def process_json_files(input_directory, output_filepath):
    data_dict = load_json_files(input_directory, ['address.json', 'service.json', 'zone.json', 'rule.json', 'nat.json'])

    address_data = data_dict['address.json']
    service_data = data_dict['service.json']
    zone_data = data_dict['zone.json']
    rules_data = data_dict['rule.json']
    nat_data = []
    if 'nat.json' in data_dict:
        for key, value in data_dict['nat.json'].items():
            if isinstance(value, list) and all('Type' in entry for entry in value):
                nat_data.extend(value)

    processed_address_data = process_address_json(address_data)
    processed_service_data = process_service_json(service_data)
    processed_rule_data = process_rule_json(rules_data, zone_data)
    data_dict = {
        'address': (processed_address_data, ['address_name', 'value']),
        'service': (processed_service_data, ['service', 'tcp', 'udp']),
        'zone': (zone_data, ['zone', 'vrouter']),
        'rule': (processed_rule_data,
                 ['rule', 'name', 'action', 'src-vrouter', 'src-zone', 'src-addr', 'dst-addr', 'dst-zone',
                  'dst-vrouter', 'service', 'description', 'log']),
        'nat': (nat_data, ['Type', 'id', 'from', 'to', 'service', 'trans-to', 'mode', 'log'])
    }

    os.makedirs(os.path.dirname(output_filepath), exist_ok=True)

    with pd.ExcelWriter(output_filepath, engine='openpyxl') as writer:
        for sheet, (data, columns) in data_dict.items():
            df = pd.DataFrame(data)
            print(f"{sheet}的DataFrame中的列：{df.columns.tolist()}")
            if not set(columns).issubset(df.columns):
                print(f"错误：{sheet}的DataFrame中不存在所有指定的列。跳过该工作表。")
                continue

            # 去掉JSON值中的引号和花括号
            df = df.astype(str).replace({"'": "", "{": "", "}": ""}, regex=True)

            # 去掉值中的 "dst-port:" 前缀
            if sheet == 'service':
                df['tcp'] = df['tcp'].str.replace('dst-port:', '')
                df['udp'] = df['udp'].str.replace('dst-port:', '')

            try:
                df.to_excel(writer, sheet_name=sheet, index=False, columns=columns)
            except Exception as e:
                print(f"写入{sheet}工作表时出错：{e}")


input_directory = '../files/HillStone/json/竞猜DMZ/'
output_filepath = '../files/HillStone/Excel/竞猜DMZ_XWHPD-SR2B3C02-F~(M)_4.176.0.7.xlsx'

process_json_files(input_directory, output_filepath)

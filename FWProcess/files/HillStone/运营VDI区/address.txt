address "Any" predefined
  ip 0.0.0.0/0
exit
address "IPv6-any" ipv6 predefined
  ip ::/0
exit
address "private_network" predefined
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "V<PERSON>_YunZhuoMIan"
  ip **********/24
exit
address "VDI_TC"
  ip **********/24
  ip **********/24
  ip **********/24
  ip **********/24
exit
address "CSC_Client"
  ip ************/25
exit
address "SFTP-*********"
  ip *********/32
exit
address "TestVDI"
  ip ***********/32
  ip *************/32
  ip *************/32
  ip *************/32
  ip **********/16
  ip **********/16
  range ************* *************
  range *********** ***********
exit
address "VDI-172.100"
  ip *************/32
  ip *************/32
  range ************* ***************
exit
address "**********"
  ip **********/16
exit
address "********"
  ip ********/16
exit
address "************"
  ip ************/24
exit
address "DHCP_**********-2"
  range ********** **********
exit
address "**********"
  ip **********/24
exit
address "**********"
  ip **********/24
exit
address "**********"
  ip **********/24
exit
address "**********"
  ip **********/24
exit
address "172.100"
  ip ***********/16
exit
address "**********1"
  ip **********1/32
exit
address "*************"
  ip *************/32
exit
address "VPN-10-208"
  ip ***********/24
  ip ***********/24
exit
address "***********"
  ip ***********/32
exit
address "Print"
  range ************ ************
  range ************ ************
  range ************ ************
exit
address "LotteryVDI"
  range ************ **************
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "CSC_VDI"
  ip **********/25
exit
address "************/26"
  ip ************/26
exit
address "***********-15"
  range *********** ***********
exit
address "************-56"
  range ************ ************
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "************"
  ip ************/32
exit
address "************-244"
  range ************ ************
exit
address "************-244"
  range ************ ************
exit
address "************-246"
  range ************ ************
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/16
exit
address "**********"
  ip **********/16
exit
address "*********"
  ip *********/22
exit
address "**********1-12"
  range **********1 **********2
exit

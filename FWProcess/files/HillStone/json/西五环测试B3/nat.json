{"T3": [{"Type": "s<PERSON>rule", "id": "244", "from": "T3_18.5.32.201to217", "to": "10.217.1.1/32", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "201", "from": "192.168.213.0/24", "to": "10.211.0.0/16", "service": "Any", "trans-to": "18.6.3.159", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "183", "from": "T3_GW_18.5.34.1-18", "to": "104.200.101.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "103", "from": "T3_GW_18.5.34.1-18", "to": "18.6.19.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "124", "from": "18.5.144.118", "to": "172.26.100.205", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "140", "from": "18.5.144.0/24", "to": "18.1.13.0/24", "service": "Any", "trans-to": "18.6.3.200", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "1", "from": "T3-TESTCLIET-TOINTERNET", "to": "T3 SNAT-INTERNET- POOL", "service": "Any", "trans-to": "T3 SNAT-INTERNET- POOL", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "2", "from": "Any", "to": "18.6.115.2", "service": "Any", "trans-to": "18.6.3.129", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "3", "from": "Any", "to": "18.6.115.1", "service": "Any", "trans-to": "18.6.3.128", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "4", "from": "Any", "to": "18.2.1.244", "service": "Any", "trans-to": "18.6.3.130", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "6", "from": "Any", "to": "18.2.1.245", "service": "Any", "trans-to": "18.6.3.131", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "9", "from": "T3_GW_18.5.34.1-18", "to": "103.254.188.138", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "18", "from": "T3_GW_18.5.34.1-18", "to": "101.201.41.194", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "24", "from": "T3_GW_18.5.34.1-18", "to": "124.127.94.46", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "21", "from": "T3_GW_18.5.34.1-18", "to": "172.26.1.177", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "19", "from": "18.5.144.0/24", "to": "172.26.1.177", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "10", "from": "T3_GW_18.5.34.1-18", "to": "172.26.100.205", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "27", "from": "T3_GW_18.5.34.1-18", "to": "172.26.22.50", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "31", "from": "T3_GW_18.5.34.1-18", "to": "172.26.77.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "33", "from": "T3_GW_18.5.34.1-18", "to": "104.21.2.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "11", "from": "T3_GW_18.5.34.1-18", "to": "T3-CSLAPP", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "22", "from": "T3_GW_18.5.34.1-18", "to": "103.20.3.125", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "38", "from": "T3_GW_18.5.34.1-18", "to": "123.125.97.251", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "40", "from": "T3_GW_18.5.34.1-18", "to": "123.125.97.228", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "52", "from": "T3_GW_18.5.34.1-18", "to": "38.0.0.0/8", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "20", "from": "18.5.144.0/24", "to": "103.20.3.125", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "34", "from": "18.5.144.0/24", "to": "104.24.0.0/24", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "39", "from": "18.5.144.0/24", "to": "123.125.97.251", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "41", "from": "18.5.144.0/24", "to": "123.125.97.228", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "12", "from": "Any", "to": "192.168.181.224", "service": "Any", "trans-to": "18.6.3.135", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "13", "from": "Any", "to": "192.168.181.221", "service": "Any", "trans-to": "18.6.3.136", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "14", "from": "192.168.213.0/24", "to": "500wan_JCWeb_DXWG", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "32", "from": "192.168.213.0/24", "to": "103.254.188.138", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "35", "from": "192.168.213.0/24", "to": "104.24.0.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "56", "from": "192.168.213.0/24", "to": "104.255.225.1", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "16", "from": "Any", "to": "18.6.115.3", "service": "Any", "trans-to": "18.6.3.138", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "17", "from": "Any", "to": "18.6.115.4", "service": "Any", "trans-to": "18.6.3.139", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "23", "from": "Any", "to": "10.0.1.210", "service": "Any", "trans-to": "18.6.3.140", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "25", "from": "18.5.14.231", "to": "192.168.182.130", "service": "Any", "trans-to": "18.6.3.141", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "26", "from": "18.5.14.232", "to": "192.168.182.130", "service": "Any", "trans-to": "18.6.3.141", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "28", "from": "18.4.21.51", "to": "172.26.1.59", "service": "Any", "trans-to": "18.6.3.142", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "29", "from": "ELP-18.5.32.131-147", "to": "172.26.1.0/24", "service": "Any", "trans-to": "18.6.3.143", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "30", "from": "18.5.48.101", "to": "192.168.32.15", "service": "Any", "trans-to": "18.6.3.144", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "37", "from": "Any", "to": "203.119.206.132", "service": "Any", "trans-to": "18.6.3.145", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "42", "from": "T3_GW_18.5.34.1-18", "to": "172.26.12.244", "service": "Any", "trans-to": "18.6.3.146", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "43", "from": "ELP-18.5.32.131-147", "to": "172.26.1.0/24", "service": "Any", "trans-to": "18.6.3.147", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "44", "from": "ELP-18.5.32.131-147", "to": "104.21.2.98", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "45", "from": "ELP-18.5.32.131-147", "to": "104.21.2.20", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "46", "from": "18.5.144.0/24", "to": "172.20.16.0/24", "service": "Any", "trans-to": "18.6.3.149", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "47", "from": "T3_GW_18.5.34.1-18", "to": "104.24.0.106", "service": "Any", "trans-to": "18.6.3.146", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "48", "from": "G3-Information publishing platform", "to": "GSM", "service": "Any", "trans-to": "18.6.3.150", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "51", "from": "Bisab-18.0.4.41-45", "to": "172.28.4.20", "service": "Any", "trans-to": "18.6.3.151", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "53", "from": "18.5.32.147", "to": "38.0.0.0/8", "service": "Any", "trans-to": "18.6.3.152", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "54", "from": "ELP-18.5.32.131-147", "to": "38.0.0.0/8", "service": "Any", "trans-to": "18.6.3.153", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "55", "from": "18.5.32.133", "to": "38.0.0.0/8", "service": "Any", "trans-to": "18.6.3.153", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "57", "from": "18.5.144.0/24", "to": "172.26.100.205", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "58", "from": "18.4.21.53", "to": "104.21.2.96", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "59", "from": "18.5.32.142", "to": "104.21.2.0/23", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "60", "from": "18.5.12.131-139", "to": "172.26.1.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "61", "from": "18.5.12.131-139", "to": "104.21.2.98", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "62", "from": "18.5.12.131-139", "to": "38.0.160.11", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "93", "from": "18.5.12.0/24", "to": "38.0.160.11", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "63", "from": "18.5.12.131-139", "to": "38.0.5.204", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "65", "from": "T3_GW_18.5.34.1-18", "to": "218.94.54.82", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "64", "from": "T3_GW_18.5.34.1-18", "to": "172.20.22.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "66", "from": "T3_GW_18.5.34.1-18", "to": "172.20.29.0/24", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "67", "from": "T3_GW_18.5.34.1-18", "to": "42.81.147.159", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "68", "from": "192.168.213.0/24", "to": "42.81.147.159", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "69", "from": "T3_GW_18.5.34.1-18", "to": "172.20.23.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "70", "from": "T3_GW_18.5.34.1-18", "to": "104.21.53.245", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "71", "from": "T3_GW_18.5.34.1-18", "to": "104.21.53.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "72", "from": "18.5.144.0/24", "to": "202.105.96.226", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "73", "from": "18.4.21.51", "to": "104.21.2.0/24", "service": "Any", "trans-to": "18.6.3.142", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "112", "from": "18.0.4.0/24", "to": "104.21.4.0/24", "service": "Any", "trans-to": "18.6.3.142", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "75", "from": "18.5.144.0/24", "to": "172.26.22.0/24", "service": "Any", "trans-to": "18.6.3.149", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "76", "from": "18.5.144.0/24", "to": "104.0.0.0/8", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "77", "from": "18.5.144.0/24", "to": "23.0.0.0/8", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "78", "from": "18.5.144.0/24", "to": "122.0.0.0/8", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "79", "from": "18.5.144.0/24", "to": "184.0.0.0/8", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "92", "from": "18.5.144.0/24", "to": "105.106.61.95", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "91", "from": "18.5.144.0/24", "to": "172.230.178.235", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "74", "from": "192.168.213.0/24", "to": "111.205.92.111", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "80", "from": "18.5.12.131-139", "to": "172.26.77.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "81", "from": "192.168.213.0/24", "to": "104.23.11.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "82", "from": "Any", "to": "104.24.0.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "83", "from": "18.5.144.0/24", "to": "192.144.193.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "84", "from": "18.5.144.0/24", "to": "140.143.52.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "85", "from": "18.4.21.0/24", "to": "104.21.4.0/24", "service": "Any", "trans-to": "18.6.3.142", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "86", "from": "18.4.21.0/24", "to": "172.26.1.0/24", "service": "Any", "trans-to": "18.6.3.142", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "87", "from": "T3_GW_18.5.34.1-18", "to": "104.21.4.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "88", "from": "T3_GW_18.5.34.1-18", "to": "104.21.1.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "89", "from": "192.168.213.0/24", "to": "104.21.4.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "95", "from": "18.5.57.1/32", "to": "78.1.15.101/32", "service": "Any", "trans-to": "18.6.3.155", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "96", "from": "ELP-18.5.32.131-147", "to": "104.21.4.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "97", "from": "192.168.213.0/24", "to": "172.26.1.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "98", "from": "192.168.213.0/24", "to": "172.26.3.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "99", "from": "18.0.7.250", "to": "172.16.20.2", "service": "Any", "trans-to": "18.6.3.156", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "100", "from": "18.0.1.7", "to": "18.6.4.0/24", "service": "Any", "trans-to": "18.6.3.157", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "105", "from": "Any", "to": "18.6.19.0/24", "service": "Any", "trans-to": "18.6.3.158", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "106", "from": "ELP-18.5.32.131-147", "to": "CSLC_172.26.77.0/24", "service": "Any", "trans-to": "18.6.3.143", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "107", "from": "18.5.144.0/24", "to": "172.20.22.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "8", "from": "18.5.144.0/24", "to": "172.26.5.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "108", "from": "18.5.144.0/24", "to": "106.14.144.12", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "109", "from": "18.5.12.0/24", "to": "104.21.2.20", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "110", "from": "192.168.213.0/24", "to": "104.21.2.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "113", "from": "T3_GW_18.5.34.1-18", "to": "124.127.94.59", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "114", "from": "18.5.32.51", "to": "124.127.94.59", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "115", "from": "192.168.213.0/24", "to": "104.21.3.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "116", "from": "T3_GW_18.5.34.1-18", "to": "128.192.179.101", "service": "Any", "trans-to": "18.6.3.146", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "117", "from": "T3_GW_18.5.34.1-18", "to": "104.23.0.0/24", "service": "Any", "trans-to": "18.6.3.155", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "118", "from": "18.5.12.0/24", "to": "104.23.0.0/24", "service": "Any", "trans-to": "18.6.3.155", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "119", "from": "192.168.213.0/24", "to": "104.23.0.0/24", "service": "Any", "trans-to": "18.6.3.155", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "120", "from": "18.5.32.142", "to": "106.52.153.206", "service": "Any", "trans-to": "18.6.3.148", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "121", "from": "18.5.32.0/24", "to": "104.23.11.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "122", "from": "18.5.32.0/24", "to": "104.23.0.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "123", "from": "18.5.32.0/24", "to": "104.23.3.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "127", "from": "T3_GW_18.5.34.1-18", "to": "124.127.94.56", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "125", "from": "T3_GW_18.5.34.1-18", "to": "Any", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "135", "from": "18.5.144.0/24", "to": "58.240.29.244", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "126", "from": "18.5.144.118", "to": "Any", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "129", "from": "T3-18.4.3.11-15", "to": "124.127.94.56", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "130", "from": "T3-18.4.4.11-15", "to": "124.127.94.56", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "131", "from": "192.168.213.0/24", "to": "172.26.76.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "132", "from": "18.5.144.0/24", "to": "18.1.3.30", "service": "Any", "trans-to": "18.6.3.200", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "133", "from": "18.5.144.0/24", "to": "38.0.0.0/8", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "134", "from": "18.4.4.21", "to": "124.127.94.56", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "136", "from": "192.168.213.0/24", "to": "104.23.12.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "137", "from": "192.168.213.0/24", "to": "104.21.5.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "138", "from": "192.168.213.0/24", "to": "104.21.60.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "139", "from": "192.168.213.0/24", "to": "104.21.55.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "141", "from": "192.168.213.0/24", "to": "104.21.1.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "142", "from": "18.4.3.102", "to": "124.127.94.56", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "143", "from": "18.5.144.0/24", "to": "18.5.97.0/24", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "144", "from": "18.0.1.7", "to": "18.0.1.183", "service": "Any", "trans-to": "18.6.3.157", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "145", "from": "192.168.213.0/24", "to": "104.21.0.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "146", "from": "18.0.1.7", "to": "104.23.11.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "147", "from": "192.168.213.0/24", "to": "10.211.0.0/24", "service": "Any", "trans-to": "10.248.254.1", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "148", "from": "18.5.10.0/24", "to": "172.26.22.50", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "149", "from": "18.5.50.0/24", "to": "172.26.22.50", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "150", "from": "192.168.213.0/24", "to": "172.26.22.50", "service": "Any", "trans-to": "18.6.3.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "151", "from": "192.168.213.0/24", "to": "104.24.0.0/24", "service": "Any", "trans-to": "18.6.3.146", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "152", "from": "18.5.10.1-51", "to": "104.21.3.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "153", "from": "18.5.50.1-2", "to": "104.21.3.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "239", "from": "192.168.213.0/24", "to": "10.213.31.2/32", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "154", "from": "192.168.213.0/24", "to": "10.213.0.0/16", "service": "Any", "trans-to": "10.248.254.2", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "155", "from": "T3_GW_18.5.34.1-18", "to": "10.213.0.200", "service": "Any", "trans-to": "18.6.3.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "156", "from": "18.5.144.0/24", "to": "104.23.13.0/24", "service": "Any", "trans-to": "18.6.3.134", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "157", "from": "18.5.32.41", "to": "104.21.3.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "158", "from": "ELP-18.5.32.131-147", "to": "104.21.110.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "159", "from": "ELP-18.5.32.131-147", "to": "104.21.19.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "160", "from": "ELP-18.5.32.131-147", "to": "104.200.0.0/16", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "161", "from": "192.168.213.0/24", "to": "104.21.19.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "162", "from": "192.168.213.0/24", "to": "104.200.100.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "172", "from": "192.168.213.0/24", "to": "104.21.51.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "173", "from": "192.168.213.0/24", "to": "104.22.9.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "175", "from": "192.168.213.0/24", "to": "106.52.153.206", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "176", "from": "18.5.12.131-147", "to": "104.21.17.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "177", "from": "18.5.12.131-147", "to": "104.21.110.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "184", "from": "18.0.7.250", "to": "172.16.20.6", "service": "Any", "trans-to": "18.6.3.156", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "185", "from": "18.5.32.41", "to": "104.200.101.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "186", "from": "ELP-18.5.32.131-147", "to": "10.20.4.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "187", "from": "18.5.32.41", "to": "10.216.19.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "188", "from": "18.5.32.41", "to": "10.216.31.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "189", "from": "18.5.55.1", "to": "104.200.100.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "190", "from": "18.5.55.1", "to": "104.200.101.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "193", "from": "18.5.144.0/24", "to": "10.216.42.8", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "194", "from": "18.5.55.1", "to": "10.216.0.0/16", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "195", "from": "18.5.55.1", "to": "104.22.9.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "196", "from": "18.5.55.1", "to": "104.21.5.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "197", "from": "18.5.55.1", "to": "104.21.17.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "200", "from": "18.0.1.7", "to": "10.213.0.0/16", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "203", "from": "18.0.1.7", "to": "10.213.3.0/24", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "204", "from": "18.5.55.1", "to": "104.21.99.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "207", "from": "18.0.1.7", "to": "192.168.200.3", "service": "Any", "trans-to": "18.6.3.157", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "208", "from": "18.5.144.0/24", "to": "10.216.0.0/16", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "209", "from": "ELP-18.5.32.131-147", "to": "10.20.2.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "210", "from": "ELP-18.5.32.144", "to": "10.20.5.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "211", "from": "18.5.144.0/24", "to": "10.219.0.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "212", "from": "18.5.144.0/24", "to": "10.213.0.0/24", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "215", "from": "18.5.144.0/24", "to": "Any", "service": "Any", "trans-to": "18.6.3.154", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "216", "from": "18.5.32.41", "to": "10.216.5.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "234", "from": "18.5.57.0/24", "to": "104.22.9.42", "service": "Any", "trans-to": "18.6.3.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "235", "from": "18.5.57.0/24", "to": "10.216.5.0/24", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "240", "from": "18.5.144.0/24", "to": "10.216.50.17/32", "service": "Any", "trans-to": "18.6.3.154/32", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "243", "from": "ELP18.5.32.211-217", "to": "AD_104.9.0.20", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "245", "from": "ELP18.5.32.211-217", "to": "10.217.1.203", "service": "Any", "trans-to": "18.6.3.148", "mode": "dynamicport", "log": false}, {"Type": "dnatrule", "id": "122", "from": "Any", "to": "18.6.3.10", "service": "TCP-3389", "trans-to": "18.0.2.177", "port": "3389", "log": false}, {"Type": "dnatrule", "id": "193", "from": "123.207.171.243", "to": "18.6.3.76", "service": "Any", "trans-to": "18.5.57.34", "log": false}, {"Type": "dnatrule", "id": "197", "from": "58.87.118.188", "to": "18.6.3.76", "service": "Any", "trans-to": "18.5.57.34", "log": false}, {"Type": "dnatrule", "id": "194", "from": "123.207.171.243", "to": "18.6.3.4", "service": "Any", "trans-to": "18.5.57.10", "log": false}, {"Type": "dnatrule", "id": "196", "from": "58.87.118.188", "to": "18.6.3.4", "service": "Any", "trans-to": "18.5.57.10", "log": false}, {"Type": "dnatrule", "id": "195", "from": "Any", "to": "18.6.3.10", "service": "TCP_6001", "trans-to": "18.5.57.20", "port": "7001", "log": false}, {"Type": "dnatrule", "id": "1", "from": "Any", "to": "18.6.3.1", "service": "Any", "trans-to": "192.168.213.6", "log": false}, {"Type": "dnatrule", "id": "2", "from": "Any", "to": "18.6.3.2", "service": "Any", "trans-to": "192.168.213.221", "log": true}, {"Type": "dnatrule", "id": "3", "from": "Any", "to": "18.6.3.3", "service": "Any", "trans-to": "192.168.213.2", "log": false}, {"Type": "dnatrule", "id": "40", "from": "Any", "to": "18.6.3.4", "service": "Any", "trans-to": "18.5.57.10", "log": false}, {"Type": "dnatrule", "id": "41", "from": "Any", "to": "18.6.3.5", "service": "Any", "trans-to": "192.168.213.62", "log": false}, {"Type": "dnatrule", "id": "42", "from": "Any", "to": "18.6.3.6", "service": "Any", "trans-to": "18.5.57.14", "log": false}, {"Type": "dnatrule", "id": "43", "from": "Any", "to": "18.6.3.7", "service": "Any", "trans-to": "18.5.57.15", "log": false}, {"Type": "dnatrule", "id": "44", "from": "Any", "to": "18.6.3.8", "service": "Any", "trans-to": "18.5.57.16", "log": false}, {"Type": "dnatrule", "id": "45", "from": "Any", "to": "18.6.3.9", "service": "Any", "trans-to": "18.5.56.17", "log": false}, {"Type": "dnatrule", "id": "47", "from": "Any", "to": "18.6.3.11", "service": "Any", "trans-to": "18.4.11.11", "log": false}, {"Type": "dnatrule", "id": "48", "from": "Any", "to": "18.6.3.12", "service": "Any", "trans-to": "18.4.64.41", "log": false}, {"Type": "dnatrule", "id": "49", "from": "Any", "to": "18.6.3.13", "service": "Any", "trans-to": "18.5.144.1", "log": false}, {"Type": "dnatrule", "id": "50", "from": "Any", "to": "18.6.3.14", "service": "Any", "trans-to": "18.1.21.11", "log": true}, {"Type": "dnatrule", "id": "51", "from": "Any", "to": "18.6.3.15", "service": "TCP-8001", "trans-to": "18.5.57.20", "port": "7001", "log": false}, {"Type": "dnatrule", "id": "240", "from": "Any", "to": "18.6.3.15/32", "service": "TCP-8553", "trans-to": "18.5.57.20/32", "port": "7001", "log": false}, {"Type": "dnatrule", "id": "46", "from": "Any", "to": "18.6.3.10", "service": "Any", "trans-to": "18.5.57.20", "log": false}, {"Type": "dnatrule", "id": "53", "from": "Any", "to": "18.6.3.16", "service": "Any", "trans-to": "18.5.57.12", "log": false}, {"Type": "dnatrule", "id": "54", "from": "Any", "to": "18.6.3.17", "service": "TCP-32514", "trans-to": "18.5.57.13", "port": "21514", "log": true}, {"Type": "dnatrule", "id": "55", "from": "Any", "to": "18.6.3.18", "service": "Any", "trans-to": "18.5.9.4", "log": false}, {"Type": "dnatrule", "id": "56", "from": "Any", "to": "18.6.3.19", "service": "Any", "trans-to": "18.5.9.8", "log": false}, {"Type": "dnatrule", "id": "57", "from": "Any", "to": "18.6.3.20", "service": "Any", "trans-to": "18.5.9.14", "log": false}, {"Type": "dnatrule", "id": "58", "from": "Any", "to": "18.6.3.21", "service": "Any", "trans-to": "18.5.9.104", "log": false}, {"Type": "dnatrule", "id": "105", "from": "Any", "to": "18.6.3.21", "service": "Any", "trans-to": "18.5.32.68", "log": false}, {"Type": "dnatrule", "id": "59", "from": "Any", "to": "18.6.3.22", "service": "Any", "trans-to": "18.5.9.144", "log": false}, {"Type": "dnatrule", "id": "60", "from": "Any", "to": "18.6.3.23", "service": "Any", "trans-to": "18.5.8.4", "log": false}, {"Type": "dnatrule", "id": "61", "from": "Any", "to": "18.6.3.24", "service": "Any", "trans-to": "18.5.8.8", "log": false}, {"Type": "dnatrule", "id": "62", "from": "Any", "to": "18.6.3.25", "service": "Any", "trans-to": "18.5.8.14", "log": false}, {"Type": "dnatrule", "id": "63", "from": "Any", "to": "18.6.3.26", "service": "Any", "trans-to": "18.5.8.18", "log": false}, {"Type": "dnatrule", "id": "64", "from": "Any", "to": "18.6.3.27", "service": "Any", "trans-to": "18.5.8.24", "log": false}, {"Type": "dnatrule", "id": "65", "from": "Any", "to": "18.6.3.28", "service": "Any", "trans-to": "18.5.8.34", "log": false}, {"Type": "dnatrule", "id": "66", "from": "Any", "to": "18.6.3.29", "service": "Any", "trans-to": "18.5.8.38", "log": false}, {"Type": "dnatrule", "id": "67", "from": "Any", "to": "18.6.3.30", "service": "Any", "trans-to": "18.5.8.44", "log": false}, {"Type": "dnatrule", "id": "68", "from": "Any", "to": "18.6.3.31", "service": "Any", "trans-to": "18.5.8.48", "log": false}, {"Type": "dnatrule", "id": "69", "from": "Any", "to": "18.6.3.32", "service": "Any", "trans-to": "18.5.8.54", "log": false}, {"Type": "dnatrule", "id": "70", "from": "Any", "to": "18.6.3.33", "service": "Any", "trans-to": "18.5.8.58", "log": false}, {"Type": "dnatrule", "id": "71", "from": "Any", "to": "18.6.3.34", "service": "Any", "trans-to": "18.5.8.64", "log": false}, {"Type": "dnatrule", "id": "72", "from": "Any", "to": "18.6.3.35", "service": "Any", "trans-to": "18.5.8.68", "log": false}, {"Type": "dnatrule", "id": "73", "from": "Any", "to": "18.6.3.36", "service": "Any", "trans-to": "18.5.8.74", "log": false}, {"Type": "dnatrule", "id": "74", "from": "Any", "to": "18.6.3.37", "service": "Any", "trans-to": "18.5.8.84", "log": false}, {"Type": "dnatrule", "id": "75", "from": "Any", "to": "18.6.3.38", "service": "Any", "trans-to": "18.5.8.88", "log": false}, {"Type": "dnatrule", "id": "76", "from": "Any", "to": "18.6.3.39", "service": "Any", "trans-to": "18.5.8.94", "log": false}, {"Type": "dnatrule", "id": "77", "from": "Any", "to": "18.6.3.40", "service": "Any", "trans-to": "18.5.8.98", "log": false}, {"Type": "dnatrule", "id": "78", "from": "Any", "to": "18.6.3.41", "service": "Any", "trans-to": "18.5.8.104", "log": false}, {"Type": "dnatrule", "id": "79", "from": "Any", "to": "18.6.3.42", "service": "Any", "trans-to": "18.5.8.114", "log": false}, {"Type": "dnatrule", "id": "80", "from": "Any", "to": "18.6.3.43", "service": "Any", "trans-to": "18.5.8.118", "log": false}, {"Type": "dnatrule", "id": "81", "from": "Any", "to": "18.6.3.44", "service": "Any", "trans-to": "18.5.8.124", "log": false}, {"Type": "dnatrule", "id": "82", "from": "Any", "to": "18.6.3.45", "service": "Any", "trans-to": "18.5.8.128", "log": false}, {"Type": "dnatrule", "id": "83", "from": "Any", "to": "18.6.3.46", "service": "Any", "trans-to": "18.5.8.134", "log": false}, {"Type": "dnatrule", "id": "84", "from": "Any", "to": "18.6.3.47", "service": "Any", "trans-to": "18.5.8.138", "log": false}, {"Type": "dnatrule", "id": "85", "from": "Any", "to": "18.6.3.48", "service": "Any", "trans-to": "18.5.9.144", "log": false}, {"Type": "dnatrule", "id": "86", "from": "Any", "to": "18.6.3.49", "service": "Any", "trans-to": "18.5.8.148", "log": false}, {"Type": "dnatrule", "id": "87", "from": "Any", "to": "18.6.3.50", "service": "Any", "trans-to": "18.5.8.154", "log": false}, {"Type": "dnatrule", "id": "88", "from": "Any", "to": "18.6.3.51", "service": "Any", "trans-to": "18.5.8.164", "log": false}, {"Type": "dnatrule", "id": "89", "from": "Any", "to": "18.6.3.52", "service": "Any", "trans-to": "18.5.8.174", "log": false}, {"Type": "dnatrule", "id": "90", "from": "Any", "to": "18.6.3.53", "service": "Any", "trans-to": "18.5.8.178", "log": false}, {"Type": "dnatrule", "id": "91", "from": "Any", "to": "18.6.3.54", "service": "Any", "trans-to": "18.5.8.184", "log": false}, {"Type": "dnatrule", "id": "92", "from": "Any", "to": "18.6.3.55", "service": "Any", "trans-to": "18.5.8.188", "log": false}, {"Type": "dnatrule", "id": "93", "from": "Any", "to": "18.6.3.56", "service": "Any", "trans-to": "18.5.8.194", "log": false}, {"Type": "dnatrule", "id": "94", "from": "Any", "to": "18.6.3.57", "service": "Any", "trans-to": "18.5.8.198", "log": false}, {"Type": "dnatrule", "id": "95", "from": "Any", "to": "18.6.3.58", "service": "Any", "trans-to": "18.5.8.204", "log": false}, {"Type": "dnatrule", "id": "96", "from": "Any", "to": "18.6.3.59", "service": "Any", "trans-to": "18.5.8.208", "log": false}, {"Type": "dnatrule", "id": "97", "from": "Any", "to": "18.6.3.60", "service": "Any", "trans-to": "18.5.8.214", "log": false}, {"Type": "dnatrule", "id": "98", "from": "Any", "to": "18.6.3.61", "service": "Any", "trans-to": "18.5.8.224", "log": false}, {"Type": "dnatrule", "id": "99", "from": "Any", "to": "18.6.3.62", "service": "Any", "trans-to": "18.5.8.228", "log": false}, {"Type": "dnatrule", "id": "100", "from": "Any", "to": "18.6.3.63", "service": "Any", "trans-to": "18.5.8.234", "log": false}, {"Type": "dnatrule", "id": "101", "from": "Any", "to": "18.6.3.64", "service": "Any", "trans-to": "18.5.8.238", "log": false}, {"Type": "dnatrule", "id": "102", "from": "Any", "to": "18.6.3.65", "service": "Any", "trans-to": "18.5.8.244", "log": false}, {"Type": "dnatrule", "id": "103", "from": "Any", "to": "18.6.3.66", "service": "Any", "trans-to": "18.5.14.114", "log": false}, {"Type": "dnatrule", "id": "148", "from": "Any", "to": "18.6.3.67", "service": "Any", "trans-to": "18.5.32.51", "log": false}, {"Type": "dnatrule", "id": "106", "from": "Any", "to": "18.6.3.68", "service": "Any", "trans-to": "18.5.57.31", "log": false}, {"Type": "dnatrule", "id": "107", "from": "Any", "to": "18.6.3.69", "service": "Any", "trans-to": "18.5.57.18", "log": false}, {"Type": "dnatrule", "id": "108", "from": "Any", "to": "18.6.3.70", "service": "Any", "trans-to": "18.5.57.19", "log": false}, {"Type": "dnatrule", "id": "109", "from": "Any", "to": "18.6.3.71", "service": "Any", "trans-to": "18.5.57.32", "log": true}, {"Type": "dnatrule", "id": "110", "from": "Any", "to": "18.6.3.72", "service": "Any", "trans-to": "18.5.9.124", "log": false}, {"Type": "dnatrule", "id": "20", "from": "Any", "to": "18.6.3.73", "service": "Any", "trans-to": "18.5.8.88", "log": false}, {"Type": "dnatrule", "id": "21", "from": "Any", "to": "18.6.3.74", "service": "Any", "trans-to": "18.5.8.84", "log": false}, {"Type": "dnatrule", "id": "22", "from": "Any", "to": "18.6.3.75", "service": "Any", "trans-to": "18.5.8.94", "log": false}, {"Type": "dnatrule", "id": "117", "from": "Any", "to": "18.6.3.76", "service": "Any", "trans-to": "18.5.57.34", "log": false}, {"Type": "dnatrule", "id": "118", "from": "Any", "to": "18.6.3.77", "service": "Any", "trans-to": "18.5.58.11", "log": false}, {"Type": "dnatrule", "id": "119", "from": "Any", "to": "18.6.3.78", "service": "Any", "trans-to": "18.5.58.12", "log": false}, {"Type": "dnatrule", "id": "120", "from": "Any", "to": "18.6.3.79", "service": "Any", "trans-to": "18.5.58.13", "log": false}, {"Type": "dnatrule", "id": "121", "from": "Any", "to": "18.6.3.80", "service": "Any", "trans-to": "18.5.58.14", "log": false}, {"Type": "dnatrule", "id": "123", "from": "Any", "to": "18.6.3.81", "service": "Any", "trans-to": "18.4.21.53", "log": false}, {"Type": "dnatrule", "id": "124", "from": "Any", "to": "18.6.3.82", "service": "Any", "trans-to": "18.5.32.142", "log": false}, {"Type": "dnatrule", "id": "125", "from": "Any", "to": "18.6.3.83", "service": "Any", "trans-to": "18.5.32.41", "log": false}, {"Type": "dnatrule", "id": "126", "from": "Any", "to": "18.6.3.25", "service": "Any", "trans-to": "18.5.57.17", "log": false}, {"Type": "dnatrule", "id": "127", "from": "Any", "to": "18.6.3.84", "service": "Any", "trans-to": "18.5.32.143", "log": false}, {"Type": "dnatrule", "id": "129", "from": "Any", "to": "18.6.3.85", "service": "Any", "trans-to": "18.5.12.138", "log": false}, {"Type": "dnatrule", "id": "130", "from": "Any", "to": "18.6.3.86", "service": "Any", "trans-to": "18.5.12.139", "log": false}, {"Type": "dnatrule", "id": "131", "from": "Any", "to": "18.6.3.87", "service": "Any", "trans-to": "18.5.12.136", "log": false}, {"Type": "dnatrule", "id": "134", "from": "Any", "to": "18.6.3.88", "service": "Any", "trans-to": "18.5.144.117", "log": false}, {"Type": "dnatrule", "id": "135", "from": "Any", "to": "18.6.3.89", "service": "Any", "trans-to": "18.5.32.138", "log": false}, {"Type": "dnatrule", "id": "136", "from": "Any", "to": "18.6.3.90", "service": "Any", "trans-to": "18.5.32.139", "log": false}, {"Type": "dnatrule", "id": "137", "from": "Any", "to": "18.6.3.91", "service": "Any", "trans-to": "18.5.32.136", "log": false}, {"Type": "dnatrule", "id": "139", "from": "Any", "to": "18.6.3.92", "service": "Any", "trans-to": "18.0.2.231", "log": false}, {"Type": "dnatrule", "id": "140", "from": "Any", "to": "18.6.3.93", "service": "Any", "trans-to": "18.0.2.232", "log": false}, {"Type": "dnatrule", "id": "141", "from": "Any", "to": "18.6.3.94", "service": "Any", "trans-to": "18.0.7.250", "log": false}, {"Type": "dnatrule", "id": "142", "from": "Any", "to": "18.6.3.95", "service": "Any", "trans-to": "18.0.1.7", "log": false}, {"Type": "dnatrule", "id": "143", "from": "Any", "to": "18.6.3.96", "service": "Any", "trans-to": "18.0.1.101", "log": false}, {"Type": "dnatrule", "id": "155", "from": "Any", "to": "18.6.3.97", "service": "TCP-9001", "trans-to": "18.5.57.41", "port": "7001", "log": true}, {"Type": "dnatrule", "id": "149", "from": "Any", "to": "18.6.3.97", "service": "Any", "trans-to": "18.5.57.41", "log": false}, {"Type": "dnatrule", "id": "150", "from": "Any", "to": "18.6.3.98", "service": "TCP-42514", "trans-to": "18.5.57.45", "port": "21514", "log": false}, {"Type": "dnatrule", "id": "151", "from": "219.133.170.230", "to": "18.6.3.99", "service": "Any", "trans-to": "18.5.32.71", "log": false}, {"Type": "dnatrule", "id": "152", "from": "219.133.170.230", "to": "18.6.3.100", "service": "Any", "trans-to": "18.5.32.142", "log": false}, {"Type": "dnatrule", "id": "153", "from": "Any", "to": "18.6.3.101", "service": "Any", "trans-to": "18.5.32.143", "log": false}, {"Type": "dnatrule", "id": "154", "from": "Any", "to": "18.6.3.102", "service": "Any", "trans-to": "18.5.57.39", "log": false}, {"Type": "dnatrule", "id": "156", "from": "Any", "to": "18.6.3.103", "service": "Any", "trans-to": "18.5.12.124", "log": false}, {"Type": "dnatrule", "id": "157", "from": "Any", "to": "18.6.3.104", "service": "Any", "trans-to": "18.5.12.14", "log": false}, {"Type": "dnatrule", "id": "158", "from": "Any", "to": "18.6.3.105", "service": "Any", "trans-to": "18.5.12.34", "log": false}, {"Type": "dnatrule", "id": "159", "from": "Any", "to": "18.6.3.106", "service": "Any", "trans-to": "18.5.12.38", "log": false}, {"Type": "dnatrule", "id": "160", "from": "Any", "to": "18.6.3.107", "service": "Any", "trans-to": "18.5.12.4", "log": false}, {"Type": "dnatrule", "id": "161", "from": "Any", "to": "18.6.3.108", "service": "Any", "trans-to": "18.5.12.44", "log": false}, {"Type": "dnatrule", "id": "162", "from": "Any", "to": "18.6.3.109", "service": "Any", "trans-to": "18.5.12.48", "log": false}, {"Type": "dnatrule", "id": "163", "from": "Any", "to": "18.6.3.110", "service": "Any", "trans-to": "18.5.12.54", "log": false}, {"Type": "dnatrule", "id": "164", "from": "Any", "to": "18.6.3.111", "service": "Any", "trans-to": "18.5.12.58", "log": false}, {"Type": "dnatrule", "id": "165", "from": "Any", "to": "18.6.3.112", "service": "Any", "trans-to": "18.5.12.64", "log": false}, {"Type": "dnatrule", "id": "166", "from": "Any", "to": "18.6.3.113", "service": "Any", "trans-to": "18.5.12.68", "log": false}, {"Type": "dnatrule", "id": "167", "from": "Any", "to": "18.6.3.114", "service": "Any", "trans-to": "18.5.12.74", "log": false}, {"Type": "dnatrule", "id": "168", "from": "Any", "to": "18.6.3.115", "service": "Any", "trans-to": "18.5.12.78", "log": false}, {"Type": "dnatrule", "id": "169", "from": "Any", "to": "18.6.3.116", "service": "Any", "trans-to": "18.5.12.8", "log": false}, {"Type": "dnatrule", "id": "170", "from": "Any", "to": "18.6.3.117", "service": "Any", "trans-to": "18.5.57.42", "log": false}, {"Type": "dnatrule", "id": "171", "from": "Any", "to": "18.6.3.118", "service": "Any", "trans-to": "18.5.57.36", "log": false}, {"Type": "dnatrule", "id": "172", "from": "Any", "to": "18.6.3.119", "service": "Any", "trans-to": "18.5.144.214", "log": false}, {"Type": "dnatrule", "id": "173", "from": "Any", "to": "18.6.3.120", "service": "Any", "trans-to": "18.5.32.52", "log": false}, {"Type": "dnatrule", "id": "174", "from": "Any", "to": "18.6.3.121", "service": "Any", "trans-to": "18.5.32.53", "log": false}, {"Type": "dnatrule", "id": "175", "from": "Any", "to": "18.6.3.122", "service": "Any", "trans-to": "18.5.32.54", "log": false}, {"Type": "dnatrule", "id": "176", "from": "Any", "to": "18.6.3.123", "service": "Any", "trans-to": "18.6.203.2", "log": false}, {"Type": "dnatrule", "id": "177", "from": "Any", "to": "18.6.3.124", "service": "Any", "trans-to": "18.5.144.117", "log": true}, {"Type": "dnatrule", "id": "178", "from": "Any", "to": "18.6.3.125", "service": "Any", "trans-to": "18.5.144.214", "log": true}, {"Type": "dnatrule", "id": "179", "from": "Any", "to": "18.6.3.126", "service": "Any", "trans-to": "18.5.144.215", "log": true}, {"Type": "dnatrule", "id": "180", "from": "Any", "to": "18.6.3.127", "service": "Any", "trans-to": "18.5.144.211", "log": false}, {"Type": "dnatrule", "id": "181", "from": "Any", "to": "18.6.23.1", "service": "Any", "trans-to": "18.0.2.111", "log": false}, {"Type": "dnatrule", "id": "182", "from": "Any", "to": "18.6.23.2", "service": "Any", "trans-to": "18.0.2.112", "log": false}, {"Type": "dnatrule", "id": "183", "from": "Any", "to": "18.6.23.3", "service": "Any", "trans-to": "18.0.2.113", "log": false}, {"Type": "dnatrule", "id": "268", "from": "Any", "to": "18.6.3.128", "service": "SSH", "trans-to": "18.6.203.34", "port": "22", "log": false}, {"Type": "dnatrule", "id": "187", "from": "Any", "to": "18.6.3.128", "service": "Any", "trans-to": "18.5.48.51", "log": false}, {"Type": "dnatrule", "id": "188", "from": "Any", "to": "18.6.23.4", "service": "Any", "trans-to": "18.5.144.101", "log": false}, {"Type": "dnatrule", "id": "189", "from": "Any", "to": "18.6.23.5", "service": "Any", "trans-to": "18.5.144.105", "log": false}, {"Type": "dnatrule", "id": "190", "from": "Any", "to": "18.6.23.6", "service": "Any", "trans-to": "18.5.144.131", "log": false}, {"Type": "dnatrule", "id": "191", "from": "Any", "to": "18.6.23.7", "service": "Any", "trans-to": "18.5.144.135", "log": false}, {"Type": "dnatrule", "id": "192", "from": "Any", "to": "18.6.23.8", "service": "Any", "trans-to": "18.5.57.38", "log": false}, {"Type": "dnatrule", "id": "198", "from": "Any", "to": "18.6.23.9", "service": "Any", "trans-to": "18.5.95.8", "log": true}, {"Type": "dnatrule", "id": "199", "from": "Any", "to": "18.6.23.10", "service": "Any", "trans-to": "18.5.60.1", "log": false}, {"Type": "dnatrule", "id": "200", "from": "Any", "to": "18.6.23.11", "service": "Any", "trans-to": "18.5.14.111", "log": false}, {"Type": "dnatrule", "id": "201", "from": "Any", "to": "18.6.23.12", "service": "Any", "trans-to": "18.5.14.112", "log": false}, {"Type": "dnatrule", "id": "202", "from": "Any", "to": "18.6.23.13", "service": "Any", "trans-to": "18.5.14.113", "log": false}, {"Type": "dnatrule", "id": "203", "from": "Any", "to": "18.6.23.15", "service": "Any", "trans-to": "18.5.14.115", "log": false}, {"Type": "dnatrule", "id": "206", "from": "Any", "to": "18.6.23.16", "service": "Any", "trans-to": "18.5.14.232", "log": false}, {"Type": "dnatrule", "id": "208", "from": "Any", "to": "18.6.23.17", "service": "Any", "trans-to": "192.168.213.23", "log": false}, {"Type": "dnatrule", "id": "209", "from": "Any", "to": "18.6.23.18", "service": "Any", "trans-to": "192.168.213.197", "log": false}, {"Type": "dnatrule", "id": "210", "from": "Any", "to": "18.6.23.19", "service": "Any", "trans-to": "18.5.144.11", "log": false}, {"Type": "dnatrule", "id": "211", "from": "Any", "to": "18.6.23.20", "service": "Any", "trans-to": "18.5.57.37", "log": false}, {"Type": "dnatrule", "id": "217", "from": "Any", "to": "18.6.23.21", "service": "Any", "trans-to": "18.5.32.55", "log": false}, {"Type": "dnatrule", "id": "218", "from": "Any", "to": "18.6.23.22", "service": "Any", "trans-to": "18.5.61.1", "log": false}, {"Type": "dnatrule", "id": "219", "from": "Any", "to": "18.6.23.23", "service": "Any", "trans-to": "18.4.4.11", "log": false}, {"Type": "dnatrule", "id": "220", "from": "Any", "to": "18.6.23.24", "service": "Any", "trans-to": "18.0.96.12", "log": false}, {"Type": "dnatrule", "id": "221", "from": "Any", "to": "18.6.23.25", "service": "Any", "trans-to": "18.0.96.120", "log": false}, {"Type": "dnatrule", "id": "222", "from": "Any", "to": "18.6.23.26", "service": "Any", "trans-to": "18.5.32.144", "log": false}, {"Type": "dnatrule", "id": "223", "from": "Any", "to": "18.6.23.27", "service": "Any", "trans-to": "18.0.96.11", "log": false}, {"Type": "dnatrule", "id": "224", "from": "Any", "to": "18.6.23.28", "service": "Any", "trans-to": "18.5.57.48", "log": false}, {"Type": "dnatrule", "id": "225", "from": "Any", "to": "18.6.3.252", "service": "Any", "trans-to": "18.0.2.234", "log": false}, {"Type": "dnatrule", "id": "226", "from": "Any", "to": "18.6.3.251", "service": "Any", "trans-to": "18.0.2.233", "log": false}, {"Type": "dnatrule", "id": "227", "from": "Any", "to": "18.6.3.253", "service": "Any", "trans-to": "18.0.2.235", "log": false}, {"Type": "dnatrule", "id": "228", "from": "Any", "to": "18.6.3.254", "service": "Any", "trans-to": "18.0.2.236", "log": false}, {"Type": "dnatrule", "id": "229", "from": "Any", "to": "18.6.23.29", "service": "Any", "trans-to": "18.5.144.103", "log": false}, {"Type": "dnatrule", "id": "230", "from": "Any", "to": "18.6.23.30", "service": "Any", "trans-to": "18.5.57.24", "log": false}, {"Type": "dnatrule", "id": "231", "from": "Any", "to": "18.6.23.31", "service": "Any", "trans-to": "18.5.57.25", "log": false}, {"Type": "dnatrule", "id": "233", "from": "Any", "to": "18.6.23.32", "service": "Any", "trans-to": "18.5.144.17", "log": false}, {"Type": "dnatrule", "id": "238", "from": "Any", "to": "18.6.23.33", "service": "Any", "trans-to": "192.168.213.88", "log": false}, {"Type": "dnatrule", "id": "239", "from": "Any", "to": "18.6.3.250", "service": "Any", "trans-to": "18.5.57.50", "log": false}, {"Type": "dnatrule", "id": "247", "from": "Any", "to": "18.6.3.249", "service": "Any", "trans-to": "192.168.213.224", "log": false}, {"Type": "dnatrule", "id": "252", "from": "Any", "to": "18.6.3.10", "service": "TCP8099", "trans-to": "18.5.57.20/32", "port": "8099", "log": false}, {"Type": "dnatrule", "id": "254", "from": "Any", "to": "18.6.3.201", "trans-to": "18.5.32.201", "log": false}, {"Type": "dnatrule", "id": "255", "from": "Any", "to": "18.6.3.202", "trans-to": "18.5.32.202", "log": false}, {"Type": "dnatrule", "id": "256", "from": "Any", "to": "18.6.3.203", "trans-to": "18.5.32.203", "log": false}, {"Type": "dnatrule", "id": "257", "from": "Any", "to": "18.6.3.211", "trans-to": "18.5.32.211", "log": false}, {"Type": "dnatrule", "id": "258", "from": "Any", "to": "18.6.3.213", "service": "Any", "trans-to": "18.5.32.213", "log": false}, {"Type": "dnatrule", "id": "259", "from": "Any", "to": "18.6.3.212", "trans-to": "18.5.32.212", "log": false}, {"Type": "dnatrule", "id": "260", "from": "Any", "to": "18.6.3.214", "trans-to": "18.5.32.214", "log": false}, {"Type": "dnatrule", "id": "261", "from": "Any", "to": "18.6.3.215", "trans-to": "18.5.32.215", "log": false}, {"Type": "dnatrule", "id": "262", "from": "Any", "to": "18.6.3.216", "trans-to": "18.5.32.216", "log": false}, {"Type": "dnatrule", "id": "263", "from": "Any", "to": "18.6.3.217", "trans-to": "18.5.32.217", "log": false}, {"Type": "dnatrule", "id": "264", "from": "Any", "to": "18.6.3.151", "service": "TCP-7001", "trans-to": "18.5.144.102", "port": "7001", "log": false}, {"Type": "dnatrule", "id": "269", "from": "Any", "to": "18.6.3.15", "service": "SSH", "trans-to": "ELP-18.5.32.143", "port": "22", "log": false}, {"Type": "dnatrule", "id": "272", "from": "Any", "to": "18.6.3.129", "service": "Any", "trans-to": "18.5.57.51", "log": false}], "T4": [{"Type": "s<PERSON>rule", "id": "242", "from": "18.5.82.41", "to": "************", "service": "Any", "trans-to": "18.6.4.166", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "164", "from": "18.5.95.1", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.23", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "163", "from": "18.5.95.2", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.24", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "165", "from": "18.5.95.3", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.25", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "166", "from": "18.5.95.4", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.26", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "167", "from": "18.5.95.5", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.27", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "168", "from": "18.5.95.6", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.28", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "169", "from": "18.5.95.7", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.29", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "170", "from": "18.5.95.8", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.30", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "171", "from": "18.5.95.9", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.31", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "5", "from": "Any", "to": "18.5.67.100", "service": "Any", "trans-to": "18.6.4.129", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "7", "from": "T4-18.5.95.1-9", "to": "Any", "service": "Any", "trans-to": "18.6.4.130", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "15", "from": "T4-18.5.95.252-253", "to": "Any", "service": "Any", "trans-to": "18.6.4.132", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "36", "from": "18.0.120.10", "to": "18.5.83.11", "service": "Any", "trans-to": "18.6.4.133", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "49", "from": "*********/24", "to": "18.6.115.1", "service": "Any", "trans-to": "**********", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "50", "from": "*********/24", "to": "18.5.115.2", "service": "Any", "trans-to": "**********", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "90", "from": "192.168.214.0/24", "to": "Any", "service": "Any", "trans-to": "18.6.4.135", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "94", "from": "*********/24", "to": "192.168.182.64/26", "service": "Any", "trans-to": "18.6.4.136", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "128", "from": "18.5.48.161", "to": "172.16.20.252", "service": "Any", "trans-to": "18.6.4.137", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "174", "from": "Any", "to": "10.215.41.0/24", "service": "Any", "trans-to": "18.6.4.138", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "178", "from": "18.5.74.111", "to": "104.22.9.42", "service": "Any", "trans-to": "18.6.4.139", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "179", "from": "18.5.74.112", "to": "104.22.9.42", "service": "Any", "trans-to": "18.6.4.139", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "180", "from": "18.5.74.111", "to": "104.22.9.41", "service": "Any", "trans-to": "18.6.4.139", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "181", "from": "18.5.74.112", "to": "104.22.9.41", "service": "Any", "trans-to": "18.6.4.139", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "182", "from": "*********/24", "to": "10.212.0.0/24", "service": "Any", "trans-to": "**********", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "191", "from": "18.5.94.1", "to": "104.21.50.125", "service": "Any", "trans-to": "18.6.4.140", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "192", "from": "18.5.94.2", "to": "104.21.50.125", "service": "Any", "trans-to": "18.6.4.141", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "198", "from": "18.5.95.5", "to": "10.213.0.0/24", "service": "Any", "trans-to": "18.6.4.27", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "199", "from": "18.5.95.4", "to": "10.213.3.0/24", "service": "Any", "trans-to": "18.6.4.26", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "202", "from": "18.5.95.7", "to": "10.213.0.0/24", "service": "Any", "trans-to": "18.6.4.29", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "205", "from": "18.5.74.0/24", "to": "104.22.9.42", "service": "Any", "trans-to": "18.6.4.139", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "206", "from": "shuangyinsu_test", "to": "104.21.50.125", "service": "Any", "trans-to": "18.6.4.139", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "213", "from": "Any", "to": "10.216.3.48", "service": "Any", "trans-to": "18.6.4.142", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "214", "from": "*********/24", "to": "**********", "service": "Any", "trans-to": "**********", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "222", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "219", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "dynamicip", "log": true}, {"Type": "s<PERSON>rule", "id": "218", "from": "***********/32", "to": "************", "service": "Any", "trans-to": "**********", "mode": "dynamicip", "log": false}, {"Type": "s<PERSON>rule", "id": "220", "from": "***********/32", "to": "************", "service": "Any", "trans-to": "**********", "mode": "dynamicip", "log": false}, {"Type": "s<PERSON>rule", "id": "223", "from": "**********/32", "to": "************", "service": "Any", "trans-to": "**********", "mode": "dynamicip", "log": false}, {"Type": "s<PERSON>rule", "id": "224", "from": "***********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "226", "from": "***********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "225", "from": "*********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "dynamicip", "log": false}, {"Type": "s<PERSON>rule", "id": "227", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "228", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "229", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "231", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "230", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "217", "from": "**********", "to": "************", "service": "Any", "trans-to": "**********", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "232", "from": "18.5.90.100", "to": "10.20.0.0/16", "service": "Any", "trans-to": "18.6.4.156", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "221", "from": "18.5.90.101", "to": "10.252.1.0/24", "service": "Any", "trans-to": "18.6.4.157", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "233", "from": "*********/24", "to": "172.26.100.205", "service": "Any", "trans-to": "18.6.4.158", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "236", "from": "18.5.70.0/24", "to": "78.47.82.46", "service": "Any", "trans-to": "18.6.4.159", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "237", "from": "18.5.81.3", "to": "18.6.32.40&41", "service": "Any", "trans-to": "18.6.4.160", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "238", "from": "18.5.81.200", "to": "192.168.215.18", "service": "Any", "trans-to": "18.6.4.160", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "241", "from": "18.5.74.11/32", "to": "66.209.95.141/32", "service": "Any", "trans-to": "18.6.4.161/32", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "246", "from": "18.5.80.120", "to": "Any", "service": "Any", "trans-to": "18.6.4.162", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "247", "from": "18.5.72.36", "to": "10.210.9.201", "service": "Any", "trans-to": "18.6.4.163", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "248", "from": "18.5.72.37", "to": "10.210.9.201", "service": "Any", "trans-to": "18.6.4.163", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "249", "from": "18.5.90.176", "to": "39.105.161.118", "service": "Any", "trans-to": "18.6.4.164", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "250", "from": "18.5.90.177", "to": "39.105.161.118", "service": "Any", "trans-to": "18.6.4.164", "mode": "static", "log": false}, {"Type": "dnatrule", "id": "133", "from": "10.20.0.0/16", "to": "18.6.4.24", "service": "Any", "trans-to": "18.5.95.2", "log": true}, {"Type": "dnatrule", "id": "4", "from": "Any", "to": "18.6.4.1", "service": "Any", "trans-to": "18.5.80.101", "log": false}, {"Type": "dnatrule", "id": "5", "from": "Any", "to": "18.6.4.2", "service": "Any", "trans-to": "18.5.80.102", "log": false}, {"Type": "dnatrule", "id": "6", "from": "Any", "to": "18.6.4.3", "service": "Any", "trans-to": "18.5.80.103", "log": false}, {"Type": "dnatrule", "id": "7", "from": "Any", "to": "18.6.4.4", "service": "Any", "trans-to": "18.5.80.111", "log": false}, {"Type": "dnatrule", "id": "8", "from": "Any", "to": "18.6.4.5", "service": "Any", "trans-to": "18.5.80.112", "log": false}, {"Type": "dnatrule", "id": "9", "from": "Any", "to": "18.6.4.6", "service": "Any", "trans-to": "18.5.80.113", "log": false}, {"Type": "dnatrule", "id": "10", "from": "Any", "to": "18.6.4.7", "service": "Any", "trans-to": "18.5.80.114", "log": false}, {"Type": "dnatrule", "id": "11", "from": "Any", "to": "18.6.4.8", "service": "Any", "trans-to": "18.5.80.121", "log": false}, {"Type": "dnatrule", "id": "12", "from": "Any", "to": "18.6.4.9", "service": "Any", "trans-to": "18.5.80.122", "log": false}, {"Type": "dnatrule", "id": "13", "from": "Any", "to": "18.6.4.10", "service": "Any", "trans-to": "18.5.80.123", "log": false}, {"Type": "dnatrule", "id": "14", "from": "Any", "to": "18.6.4.11", "service": "Any", "trans-to": "18.5.80.124", "log": false}, {"Type": "dnatrule", "id": "15", "from": "Any", "to": "18.6.4.12", "service": "Any", "trans-to": "18.5.80.125", "log": false}, {"Type": "dnatrule", "id": "16", "from": "Any", "to": "18.6.4.13", "service": "Any", "trans-to": "18.5.80.131", "log": false}, {"Type": "dnatrule", "id": "17", "from": "Any", "to": "18.6.4.14", "service": "Any", "trans-to": "18.5.80.132", "log": false}, {"Type": "dnatrule", "id": "18", "from": "Any", "to": "18.6.4.15", "service": "Any", "trans-to": "18.5.80.133", "log": false}, {"Type": "dnatrule", "id": "19", "from": "Any", "to": "18.6.4.16", "service": "Any", "trans-to": "18.5.80.134", "log": false}, {"Type": "dnatrule", "id": "24", "from": "Any", "to": "18.6.4.21", "service": "Any", "trans-to": "18.5.95.100", "log": false}, {"Type": "dnatrule", "id": "25", "from": "Any", "to": "18.6.4.22", "service": "Any", "trans-to": "18.5.95.101", "log": false}, {"Type": "dnatrule", "id": "26", "from": "Any", "to": "18.6.4.23", "service": "Any", "trans-to": "18.5.95.1", "log": true}, {"Type": "dnatrule", "id": "27", "from": "Any", "to": "18.6.4.24", "service": "Any", "trans-to": "18.5.95.2", "log": true}, {"Type": "dnatrule", "id": "144", "from": "18.6.3.157", "to": "18.6.4.25", "service": "Any", "trans-to": "18.5.95.3", "log": false}, {"Type": "dnatrule", "id": "28", "from": "Any", "to": "18.6.4.25", "service": "NFS", "trans-to": "18.5.95.3", "log": false}, {"Type": "dnatrule", "id": "132", "from": "Any", "to": "18.6.4.25", "service": "Any", "trans-to": "18.5.95.3", "log": false}, {"Type": "dnatrule", "id": "29", "from": "Any", "to": "18.6.4.26", "service": "Any", "trans-to": "18.5.95.4", "log": false}, {"Type": "dnatrule", "id": "30", "from": "Any", "to": "18.6.4.27", "service": "Any", "trans-to": "18.5.95.5", "log": false}, {"Type": "dnatrule", "id": "31", "from": "Any", "to": "18.6.4.28", "service": "Any", "trans-to": "18.5.95.6", "log": false}, {"Type": "dnatrule", "id": "32", "from": "Any", "to": "18.6.4.29", "service": "Any", "trans-to": "18.5.95.7", "log": false}, {"Type": "dnatrule", "id": "33", "from": "Any", "to": "18.6.4.30", "service": "Any", "trans-to": "18.5.95.8", "log": false}, {"Type": "dnatrule", "id": "34", "from": "Any", "to": "18.6.4.31", "service": "Any", "trans-to": "18.5.95.9", "log": true}, {"Type": "dnatrule", "id": "35", "from": "Any", "to": "18.6.4.32", "service": "Any", "trans-to": "18.5.97.11", "log": false}, {"Type": "dnatrule", "id": "36", "from": "Any", "to": "18.6.4.33", "service": "Any", "trans-to": "18.5.97.12", "log": false}, {"Type": "dnatrule", "id": "37", "from": "Any", "to": "18.6.4.34", "service": "Any", "trans-to": "18.5.97.13", "log": false}, {"Type": "dnatrule", "id": "38", "from": "Any", "to": "18.6.4.35", "service": "Any", "trans-to": "18.5.97.14", "log": false}, {"Type": "dnatrule", "id": "39", "from": "Any", "to": "18.6.4.36", "service": "Any", "trans-to": "18.5.99.11", "log": false}, {"Type": "dnatrule", "id": "52", "from": "Any", "to": "18.6.4.37", "service": "Any", "trans-to": "18.5.252.1", "log": false}, {"Type": "dnatrule", "id": "23", "from": "Any", "to": "18.6.4.38", "service": "Any", "trans-to": "18.5.88.41", "log": false}, {"Type": "dnatrule", "id": "111", "from": "Any", "to": "18.6.4.39", "service": "Any", "trans-to": "18.5.88.81", "log": false}, {"Type": "dnatrule", "id": "112", "from": "Any", "to": "18.6.4.40", "service": "Any", "trans-to": "18.5.88.62", "log": false}, {"Type": "dnatrule", "id": "113", "from": "Any", "to": "18.6.4.41", "service": "Any", "trans-to": "18.5.88.71", "log": false}, {"Type": "dnatrule", "id": "114", "from": "Any", "to": "18.6.4.42", "service": "Any", "trans-to": "18.5.88.61", "log": false}, {"Type": "dnatrule", "id": "115", "from": "Any", "to": "18.6.4.43", "service": "Any", "trans-to": "18.5.88.91", "log": false}, {"Type": "dnatrule", "id": "116", "from": "Any", "to": "18.6.4.44", "service": "Any", "trans-to": "18.5.88.131", "log": false}, {"Type": "dnatrule", "id": "128", "from": "Any", "to": "18.6.4.45", "service": "Any", "trans-to": "***********", "log": false}, {"Type": "dnatrule", "id": "204", "from": "Any", "to": "18.6.4.46", "service": "Any", "trans-to": "18.5.97.6", "log": false}, {"Type": "dnatrule", "id": "205", "from": "Any", "to": "18.6.4.47", "service": "Any", "trans-to": "18.5.97.8", "log": false}, {"Type": "dnatrule", "id": "207", "from": "Any", "to": "18.6.4.48", "service": "Any", "trans-to": "18.5.95.99", "log": false}, {"Type": "dnatrule", "id": "212", "from": "Any", "to": "18.6.4.49", "service": "Any", "trans-to": "18.5.97.18", "log": false}, {"Type": "dnatrule", "id": "213", "from": "104.22.9.42", "to": "18.6.4.50", "service": "Any", "trans-to": "18.5.97.19", "log": false}, {"Type": "dnatrule", "id": "214", "from": "104.22.9.41", "to": "18.6.4.50", "service": "Any", "trans-to": "18.5.97.19", "log": false}, {"Type": "dnatrule", "id": "216", "from": "Any", "to": "18.6.4.51", "service": "Any", "trans-to": "***********", "log": false}, {"Type": "dnatrule", "id": "232", "from": "Any", "to": "18.6.4.52", "service": "Any", "trans-to": "18.5.96.2", "log": false}, {"Type": "dnatrule", "id": "234", "from": "Any", "to": "18.6.4.53", "service": "Any", "trans-to": "18.5.97.1", "log": false}, {"Type": "dnatrule", "id": "235", "from": "Any", "to": "18.6.4.54", "service": "Any", "trans-to": "18.0.98.11", "log": false}, {"Type": "dnatrule", "id": "236", "from": "Any", "to": "18.6.4.55", "service": "Any", "trans-to": "18.0.98.12", "log": false}, {"Type": "dnatrule", "id": "237", "from": "Any", "to": "18.6.4.56", "service": "Any", "trans-to": "18.0.98.120", "log": false}, {"Type": "dnatrule", "id": "241", "from": "Any", "to": "18.6.4.57", "trans-to": "18.5.95.249", "log": false}, {"Type": "dnatrule", "id": "242", "from": "Any", "to": "18.6.4.58", "trans-to": "18.5.95.250", "log": false}, {"Type": "dnatrule", "id": "243", "from": "Any", "to": "18.6.4.59", "trans-to": "18.5.84.53", "log": false}, {"Type": "dnatrule", "id": "244", "from": "Any", "to": "18.6.4.60", "service": "Any", "trans-to": "18.5.70.51", "log": false}, {"Type": "dnatrule", "id": "245", "from": "Any", "to": "18.6.4.61", "trans-to": "18.5.80.161", "log": false}, {"Type": "dnatrule", "id": "246", "from": "Any", "to": "18.6.4.62", "trans-to": "18.5.95.252", "log": false}, {"Type": "dnatrule", "id": "248", "from": "Any", "to": "18.6.4.63", "trans-to": "18.5.70.11", "log": false}, {"Type": "dnatrule", "id": "249", "from": "Any", "to": "18.6.4.64", "trans-to": "18.5.70.12", "log": false}, {"Type": "dnatrule", "id": "250", "from": "Any", "to": "18.6.4.65", "service": "Any", "trans-to": "18.5.83.91", "log": false}, {"Type": "dnatrule", "id": "251", "from": "Any", "to": "18.6.4.66", "service": "Any", "trans-to": "18.5.83.92", "log": false}, {"Type": "dnatrule", "id": "253", "from": "Any", "to": "18.6.4.72", "service": "Any", "trans-to": "18.5.97.25", "log": false}, {"Type": "dnatrule", "id": "265", "from": "Any", "to": "18.6.4.76", "service": "Any", "trans-to": "18.5.90.176", "log": false}, {"Type": "dnatrule", "id": "266", "from": "Any", "to": "18.6.4.77", "service": "Any", "trans-to": "18.5.90.177", "log": false}, {"Type": "dnatrule", "id": "267", "from": "Any", "to": "18.6.4.78", "service": "Any", "trans-to": "18.5.90.178", "log": false}, {"Type": "dnatrule", "id": "270", "from": "Any", "to": "18.6.4.79", "service": "Any", "trans-to": "18.5.90.250", "log": false}, {"Type": "dnatrule", "id": "271", "from": "Any", "to": "18.6.4.80", "service": "Any", "trans-to": "18.5.80.120", "log": false}, {"Type": "dnatrule", "id": "273", "from": "Any", "to": "18.6.4.81", "service": "Any", "trans-to": "18.5.97.27", "log": false}], "T19": [{"Type": "s<PERSON>rule", "id": "101", "from": "Any", "to": "104.21.2.0/24", "service": "Any", "trans-to": "18.6.19.1", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "102", "from": "Any", "to": "172.26.1.59", "service": "Any", "trans-to": "18.6.19.2", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "104", "from": "Any", "to": "18.6.3.67", "service": "Any", "trans-to": "18.6.19.3", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "111", "from": "Any", "to": "18.2.1.245", "service": "Any", "trans-to": "18.6.19.4", "mode": "dynamicport", "log": false}, {"Type": "dnatrule", "id": "145", "from": "Any", "to": "18.6.19.129", "service": "Any", "trans-to": "18.4.11.11", "log": false}, {"Type": "dnatrule", "id": "138", "from": "Any", "to": "18.6.19.130", "service": "Any", "trans-to": "18.4.64.41", "log": false}, {"Type": "dnatrule", "id": "146", "from": "Any", "to": "18.6.19.131", "service": "Any", "trans-to": "18.1.21.11", "log": false}, {"Type": "dnatrule", "id": "147", "from": "Any", "to": "18.6.19.132", "service": "Any", "trans-to": "18.0.5.101", "log": false}, {"Type": "dnatrule", "id": "104", "from": "Any", "to": "18.6.19.133", "service": "Any", "trans-to": "18.0.5.104", "log": false}, {"Type": "dnatrule", "id": "184", "from": "Any", "to": "18.6.19.134", "service": "Any", "trans-to": "18.0.2.111", "log": false}, {"Type": "dnatrule", "id": "185", "from": "Any", "to": "18.6.19.135", "service": "Any", "trans-to": "18.0.2.112", "log": false}, {"Type": "dnatrule", "id": "186", "from": "Any", "to": "18.6.19.136", "service": "Any", "trans-to": "18.0.2.113", "log": false}], "T22": [{"Type": "dnatrule", "id": "215", "from": "4.189.0.0/16", "to": "78.0.0.0/16", "service": "Any", "trans-to": "18.0.0.0/16", "log": false}]}
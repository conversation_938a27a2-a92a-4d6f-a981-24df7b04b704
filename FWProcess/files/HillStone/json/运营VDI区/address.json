[{"address_name": "Any", "ip": [{"type": "ip", "value": "0.0.0.0/0"}]}, {"address_name": "IPv6-any", "ip": [{"type": "ip", "value": "::/0"}]}, {"address_name": "private_network", "ip": [{"type": "ip", "value": "10.0.0.0/8"}, {"type": "ip", "value": "**********/12"}, {"type": "ip", "value": "***********/16"}]}, {"address_name": "VDI_YunZhu<PERSON>", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "VDI_TC", "ip": [{"type": "ip", "value": "**********/24"}, {"type": "ip", "value": "**********/24"}, {"type": "ip", "value": "**********/24"}, {"type": "ip", "value": "**********/24"}]}, {"address_name": "CSC_Client", "ip": [{"type": "ip", "value": "************/25"}]}, {"address_name": "SFTP-*********", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "TestVDI", "ip": [{"type": "ip", "value": "***********/32"}, {"type": "ip", "value": "*************/32"}, {"type": "ip", "value": "*************/32"}, {"type": "ip", "value": "*************/32"}, {"type": "ip", "value": "**********/16"}, {"type": "ip", "value": "**********/16"}, {"type": "range", "value": "************* *************"}, {"type": "range", "value": "*********** ***********"}]}, {"address_name": "VDI-172.100", "ip": [{"type": "ip", "value": "*************/32"}, {"type": "ip", "value": "*************/32"}, {"type": "range", "value": "************* ***************"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/16"}]}, {"address_name": "********", "ip": [{"type": "ip", "value": "********/16"}]}, {"address_name": "************", "ip": [{"type": "ip", "value": "************/24"}]}, {"address_name": "DHCP_**********-2", "ip": [{"type": "range", "value": "********** **********"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "172.100", "ip": [{"type": "ip", "value": "***********/16"}]}, {"address_name": "**********1", "ip": [{"type": "ip", "value": "**********1/32"}]}, {"address_name": "*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "VPN-10-208", "ip": [{"type": "ip", "value": "***********/24"}, {"type": "ip", "value": "***********/24"}]}, {"address_name": "***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "Print", "ip": [{"type": "range", "value": "************ ************"}, {"type": "range", "value": "************ ************"}, {"type": "range", "value": "************ ************"}]}, {"address_name": "LotteryVDI", "ip": [{"type": "range", "value": "************ **************"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "CSC_VDI", "ip": [{"type": "ip", "value": "**********/25"}]}, {"address_name": "************/26", "ip": [{"type": "ip", "value": "************/26"}]}, {"address_name": "***********-15", "ip": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "************-56", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "************-244", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "************-244", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "************-246", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/16"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/16"}]}, {"address_name": "*********", "ip": [{"type": "ip", "value": "*********/22"}]}, {"address_name": "**********1-12", "ip": [{"type": "range", "value": "**********1 ***********"}]}]
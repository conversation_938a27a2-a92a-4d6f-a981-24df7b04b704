[{"rule": "56", "action": "deny", "disable": true, "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "OCS地址段", "dst-addr": "4.10.1.81-82", "service": "tcp-3389", "name": "OCS禁止访问PKI跳板机"}, {"rule": "68", "action": "permit", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "4.255.10.101-4.255.10.117, 4.255.10.131-132, 4.255.10.10-12", "dst-addr": "3.255.240.0", "service": "ICMP, HTTP, HTTPS", "name": "OCS_TO_NEW-WAF"}, {"rule": "61", "action": "deny", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "4.255.10.0", "dst-addr": "4.13.10.41", "service": "TCP-6379, TCP-26379", "name": "OCS_To_redis_Deny"}, {"rule": "57", "action": "permit", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "4.255.10.131-132, 4.255.10.151", "dst-addr": "Security_Monitoring_Service", "service": "TCP-443, TCP-80, tcp-8443, tcp-8834, TCP-9991, TCP-5601, TCP-1400, TCP-14001, TCP-8888, SSH, TCP-18081, TCP-15601, TCP-18088, tcp-8081, tcp-6110, tcp-81, TCP-6677, TCP-7788, TCP-8001, TCP-8002, TCP-8090, TCP-8400, UDP-514", "name": "Security_Monitoring_Service-Permit"}, {"rule": "64", "action": "permit", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "4.255.10.132/32", "dst-addr": "3.252.235.201 3.252.235.202", "service": "TCP_1443, TCP-6379, TCP_30024-30029", "name": "Security_Monitoring_Check01"}, {"rule": "65", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "3.252.235.202/32", "dst-addr": "4.255.10.132/32", "service": "TCP_30024-30029", "name": "Security_Monitoring_Check02"}, {"rule": "59", "action": "permit", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "4.255.10.131-132", "dst-addr": "4.255.240.51-52, 4.255.240.81", "service": "tcp-3000, tcp-3443", "name": "Security_Monitoring_Service-Permit-1"}, {"rule": "58", "action": "deny", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "4.255.10.131-132", "dst-addr": "Any", "service": "Any", "name": "Security_Monitoring_Service-<PERSON>y"}, {"rule": "55", "action": "deny", "src-zone": "Any", "dst-zone": "YGPT _trust", "src-addr": "Any", "dst-addr": "4.100.220.0/24", "service": "389", "name": "禁止389"}, {"rule": "42", "action": "permit", "src-zone": "Any", "dst-zone": "Any", "src-addr": "4.255.235.0/24", "dst-addr": "4.0.0.0/8", "service": "ICMP, UDP-161, TCP-443, tcp-22, TCP-8999, tcp-3389", "name": "SOC系统访问区域内主机设备"}, {"rule": "51", "action": "permit", "src-zone": "Any", "dst-zone": "Any", "src-addr": "3.109.50.120-3.109.50.126", "dst-addr": "Any", "service": "tcp-3389, tcp-139, TELNET, SSH", "name": "SOC"}, {"rule": "43", "action": "permit", "src-zone": "Any", "dst-zone": "Any", "src-addr": "Any", "dst-addr": "4.255.209.0/24", "service": "TCP-10051", "name": "访问zabbix"}, {"rule": "44", "action": "permit", "src-zone": "Any", "dst-zone": "Any", "src-addr": "4.255.209.0/24", "dst-addr": "Any", "service": "TCP-10050, tcp-8080", "name": "zabbix访问所有"}, {"rule": "7", "action": "permit", "src-zone": "Any", "dst-zone": "Any", "src-addr": "二中心安全运维系统", "dst-addr": "Any", "service": "Any", "name": "二中心安全运维系统"}, {"rule": "52", "action": "permit", "disable": true, "src-zone": "Any", "dst-zone": "Any", "src-addr": "3.20.10.0/24", "dst-addr": "Any", "service": "Any", "name": "citrix访问"}, {"rule": "38", "action": "permit", "src-zone": "Any", "dst-zone": "Any", "src-addr": "4.255.240.70, 4.255.240.116, 4.255.240.120, 4.255.240.119, 4.255.240.50/32", "dst-addr": "Any", "service": "Any", "name": "漏洞扫描"}, {"rule": "1", "action": "permit", "src-zone": "YGPT _trust", "dst-zone": "YGPT _untrust", "src-addr": "4.100.220.0/24", "dst-addr": "Any", "service": "Any", "name": "云管平台trust-untrust"}, {"rule": "2", "action": "permit", "src-zone": "YGPT _untrust", "dst-zone": "YGPT _trust", "src-addr": "4.191.0.0/16", "dst-addr": "4.100.220.110-113, 4.100.220.130, 4.100.220.121-122", "service": "TCP-443", "name": "骏彩G3访问云管平台"}, {"rule": "3", "action": "permit", "src-zone": "YGPT _untrust", "dst-zone": "YGPT _trust", "src-addr": "骏彩三地BOCC", "dst-addr": "4.100.220.10, 4.100.220.11, 4.100.220.110", "service": "TCP-443", "name": "骏彩三地BOCC访问云管平台-1"}, {"rule": "4", "action": "permit", "src-zone": "YGPT _untrust", "dst-zone": "YGPT _trust", "src-addr": "骏彩三地BOCC", "dst-addr": "4.100.220.160", "service": "TCP-80", "name": "骏彩三地BOCC访问云管平台-2"}, {"rule": "5", "action": "permit", "src-zone": "YGPT _untrust", "dst-zone": "YGPT _trust", "src-addr": "4.191.0.0/16", "dst-addr": "4.100.220.155", "service": "UDP-40002, TCP-40002, UDP-40001, TCP-40001, UDP-2049, TCP-2049, UDP-111, TCP-111", "name": "骏彩G3模拟运营的虚拟机访问云管平台NFS服务器"}, {"rule": "6", "action": "permit", "src-zone": "YGPT _untrust", "dst-zone": "YGPT _trust", "src-addr": "Any", "dst-addr": "4.100.220.0/24", "service": "Any", "name": "云平台untrust-trust"}, {"rule": "22", "action": "permit", "disable": true, "src-zone": "ECC-untrust", "dst-zone": "ECC-trust", "src-addr": "Any", "dst-addr": "Any", "service": "Any", "name": "ECC-untrust-trust"}, {"rule": "8", "action": "deny", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "主中心域控", "service": "tcp-3389", "name": "ECC主机拒绝访问域控3389端口"}, {"rule": "9", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "4.20.1.1-2, 3.9.1.201/32, 3.9.1.203/32, 3.20.1.1 3.20.1.2", "service": "Any", "name": "ECC访问主中心域控-1"}, {"rule": "10", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "3.20.1.3/32", "service": "445, tcp-110, tcp-25", "name": "ECC访问主中心域控-2"}, {"rule": "11", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "3.20.1.10/32", "service": "tcp-1688", "name": "ECC访问主中心域控-3"}, {"rule": "12", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "3.20.1.30/32, 3.20.1.20/32", "service": "TCP-80, tcp-1433, tcp-8801, tcp-8444, tcp-8443, tcp-8082, tcp-8081, tcp-8080, TCP-443", "name": "ECC访问主中心域控-4"}, {"rule": "13", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "3.20.1.11/32", "service": "tcp-8530, TCP-80", "name": "ECC访问主中心域控-5"}, {"rule": "14", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "********** **********", "service": "TCP-80", "name": "ECC访问Citrix-1"}, {"rule": "15", "action": "permit", "disable": true, "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "***********/28, ***********/32, *********** ***********, *********** ***********, *********** ***********, *********** ***********", "service": "tcp-2598, tcp-1494", "description": "8.26", "name": "ECC访问Citrix-2"}, {"rule": "16", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "***********/32, ***********/32, *********** ***********", "service": "tcp-8008, TCP-80", "name": "ECC访问Citrix-3"}, {"rule": "67", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "ECC终端机", "dst-addr": "************ ************", "service": "TCP-443, TCP-80, tcp-8443, TCP-8888", "name": "ECC-mipingSSL"}, {"rule": "17", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "********** **********", "service": "tcp-1935", "name": "ECC访问主中心域控-6"}, {"rule": "18", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "*********-204, *********-214, *********/32", "service": "ICMP, tcp-445, tcp-3269, tcp-3268, 464, 88, tcp-139, tcp-135, udp-138, 137, 389", "name": "ECC访问主中心域控-7"}, {"rule": "19", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "***********1, ************, ************, **********/32, ************/32, ************/32, **********/32, ***********/32, ***********/32", "service": "tcp-8080", "name": "ECC大屏工作站8080"}, {"rule": "20", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "*************/32, ***********/32", "service": "TCP-80", "name": "ECC大屏工作站80"}, {"rule": "21", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "4.254.203.0/24", "service": "udp- 8010, tcp-8003, tcp-8000", "name": "ECC工作站访问BQQ"}, {"rule": "23", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "*************/32", "service": "tcp-6066", "name": "ECC大屏工作站6066"}, {"rule": "24", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "OCS代理机, 4.20.10.10 4.20.10.12", "service": "TCP-443, tcp-10102", "name": "ECC访问安全运维系统代理机"}, {"rule": "25", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "发布机, OCS域控, 4.20.10.101 4.20.10.120, 4.20.10.21 4.20.10.22", "service": "tcp-3389, 389", "name": "ECC访问安全运维系统发布机和域控"}, {"rule": "26", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "4.30.11.100 4.30.11.199", "dst-addr": "3.20.102.0/24", "service": "Any", "name": "ECC访问呼叫中心"}, {"rule": "28", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "198.3.10.26", "service": "TCP-1883", "name": "ECC"}, {"rule": "29", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "3.13.10.130", "service": "udp- 8010, tcp-8003, tcp-8000", "name": "ECC大屏工作站BQQ"}, {"rule": "31", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC印务终端", "dst-addr": "主中心印务citrix", "service": "tcp-1494, tcp-2598", "name": "ECC访问主中心印务citrix"}, {"rule": "32", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "3.30.11.105, 3.30.11.135", "service": "tcp-3389", "name": "ECC访问主中心ECC大屏"}, {"rule": "34", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "ECC终端机", "dst-addr": "OCS代理机", "service": "tcp-10102, TCP-443", "name": "操作终端访问OCS"}, {"rule": "35", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "ECC终端机, 二中心安全运维系统", "dst-addr": "发布机, OCS域控", "service": "389, tcp-3389", "name": "操作终端访问发布机和域控"}, {"rule": "36", "action": "permit", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "OCS地址段", "dst-addr": "Any", "service": "Any", "name": "OCS出向访问"}, {"rule": "37", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "Any", "dst-addr": "OCS地址段", "service": "ICMP", "name": "OCS-ICMP"}, {"rule": "39", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "18.2.1.0_18.2.12.0_18.2.22.0, 4.128.2.0/24, 4.128.1.0/24, 4.128.0.0/24, 4.128.10.0/24", "dst-addr": "4.255.10.10-12", "service": "tcp-10102, TCP-443", "name": "骏彩OPCC访问OCS代理机"}, {"rule": "40", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "18.2.1.0_18.2.12.0_18.2.22.0, 4.128.2.0/24, 4.128.1.0/24, 4.128.0.0/24", "dst-addr": "4.255.10.151-160, OCS地址段", "service": "389, tcp-3389", "name": "骏彩OPCC访问OCS发布机"}, {"rule": "41", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "18.2.1.0_18.2.12.0_18.2.22.0, 4.128.2.0/24, 4.128.1.0/24, 4.128.0.0/24", "dst-addr": "4.255.10.21-22", "service": "389, tcp-3389", "name": "骏彩OPCC访问OCS域控"}, {"rule": "45", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "4.101.91.21, 4.101.91.31, 4.101.91.11", "dst-addr": "OCS域控", "service": "udp-49152-65535, tcp- 49152-65535, tcp-139, 137, udp-2535, udp-67, tcp-9389, udp-138, 464, tcp-5722, tcp-135, tcp-25, 445, 88, tcp-3269, tcp-3268, tcp-636, udp-389, 389, DNS", "name": "云盘访问CIMS系统域控"}, {"rule": "46", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "运维平台", "service": "TCP-80, tcp-8080", "name": "二中心ECC访问运维平台"}, {"rule": "27", "action": "permit", "src-zone": "YY_OCS-untrust", "dst-zone": "YY_OCS-trust", "src-addr": "3.30.16.1-3, 198.1.10.1-3, 3.15.50.201-203", "dst-addr": "***********", "service": "TCP-443, tcp-10102", "name": "亦庄5层&翌景B1访问运营代理机-1"}, {"rule": "30", "action": "permit", "src-zone": "YY_OCS-untrust", "dst-zone": "YY_OCS-trust", "src-addr": "3.30.16.1-3, 198.1.10.1-3, 3.15.50.201-203", "dst-addr": "************", "service": "TCP-389, tcp-3389", "name": "亦庄5层&翌景B1访问运营代理机-2"}, {"rule": "33", "action": "permit", "src-zone": "YY_OCS-trust", "dst-zone": "YY_OCS-untrust", "src-addr": "************, ***********", "dst-addr": "4.101.15.21-22, 4.101.15.11-12, 4.9.11.11-12, 4.9.11.61-62, 4.9.11.41-42", "service": "SSH, tcp-6110, tcp-81, HTTP", "name": "运营代理机访问即开设奖资源服务器-1"}, {"rule": "47", "action": "permit", "src-zone": "YY_OCS-trust", "dst-zone": "YY_OCS-untrust", "src-addr": "************, ***********", "dst-addr": "4.9.11.21-22", "service": "tcp-3389", "name": "运营代理机访问即开设奖资源服务器-2"}, {"rule": "48", "action": "permit", "src-zone": "YY_OCS-trust", "dst-zone": "YY_OCS-untrust", "src-addr": "************, ***********", "dst-addr": "*********-32", "service": "SSH, tcp-8088", "name": "运营代理机访问即开设奖资源服务器-3"}, {"rule": "49", "action": "permit", "disable": true, "src-zone": "CODING-trust", "dst-zone": "CODING-untrust", "src-addr": "***********/24", "dst-addr": "Any", "service": "Any", "description": "8.26", "name": "CODING出向访问策略"}, {"rule": "50", "action": "permit", "src-zone": "CODING-untrust", "dst-zone": "CODING-trust", "src-addr": "*********** ***********", "dst-addr": "************/32", "service": "TCP-80", "name": "研发测试环境制品库数据同步"}, {"rule": "53", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "*********", "service": "TCP-443, tcp-8080, TCP-80", "name": "ECC访问数据中心新mcafee主机"}, {"rule": "54", "action": "permit", "src-zone": "ECC-trust", "dst-zone": "ECC-untrust", "src-addr": "ECC", "dst-addr": "************/32, ***********/32", "service": "tcp-16320-16323, tcp-16310-16316", "name": "ECC访问运维平台"}, {"rule": "60", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "cmdbnginx, ***********, ************, *************-106, *********** ***********", "dst-addr": "cmdb", "service": "TCP-8880, tcp-8080", "name": "nginx_to_cmdb"}, {"rule": "62", "action": "permit", "src-zone": "OCS-untrust", "dst-zone": "OCS-trust", "src-addr": "4.9.1.100/32", "dst-addr": "4.255.10.0", "service": "tcp-22", "name": "ZiDongHua_To_OCS"}, {"rule": "63", "action": "permit", "src-addr": "4.255.209.0/24", "dst-addr": "4.35.32.0", "service": "ICMP", "name": "zabbix-to-mifu"}, {"rule": "66", "action": "permit", "src-zone": "OCS-trust", "dst-zone": "OCS-untrust", "src-addr": "4.255.10.0", "dst-addr": "3.252.101.200-203, 10.71.10.0/24", "service": "tcp-8080, tcp-25, tcp-465, tcp-22, SSH, PING", "name": "ocs-to-mail.pt.prod"}]
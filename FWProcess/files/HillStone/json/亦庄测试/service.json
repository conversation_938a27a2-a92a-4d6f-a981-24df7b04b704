[{"service": "TCP-8443", "tcp": {"dst-port": "8443"}}, {"service": "TCP/UDP-4172", "tcp": {"dst-port": "4172 src-port 0 65535"}, "udp": {"dst-port": "4172 src-port 0 65535"}}, {"service": "tcp-80", "tcp": {"dst-port": "80"}}, {"service": "TCP5080", "tcp": {"dst-port": "5080"}}, {"service": "TCP6006", "tcp": {"dst-port": "6006"}}, {"service": "connectserver-port", "tcp": {"dst-port": "32111"}, "udp": {"dst-port": "4172"}}, {"service": "CONNECTSERVER-TCPPORT", "tcp": {"dst-port": "32111"}}, {"service": "connectserver-udpport", "udp": {"dst-port": "32111"}}, {"service": "TCP&UDP135-139", "tcp": {"dst-port": "135 139"}, "udp": {"dst-port": "135 139"}}, {"service": "TCP445", "tcp": {"dst-port": "445"}}, {"service": "8081", "tcp": {"dst-port": "8081"}}, {"service": "TCP-8082", "tcp": {"dst-port": "8082"}}, {"service": "TCP-8083", "tcp": {"dst-port": "8083"}}, {"service": "TCP-9443", "tcp": {"dst-port": "9443"}}, {"service": "TCP-8080", "tcp": {"dst-port": "8080"}}, {"service": "TCP-15601", "tcp": {"dst-port": "15601"}}, {"service": "TCP-18081", "tcp": {"dst-port": "18081"}}, {"service": "TCP-18088", "tcp": {"dst-port": "18088"}}, {"service": "TCP-9092", "tcp": {"dst-port": "9092"}}, {"service": "TCP-8400", "tcp": {"dst-port": "8400"}}, {"service": "UDP-514", "udp": {"dst-port": "514"}}, {"service": "tcp-52200", "tcp": {"dst-port": "52200"}}, {"service": "TCP8080-8089", "tcp": {"dst-port": "8080 8089"}}, {"service": "TCP-3306", "tcp": {"dst-port": "3306"}}, {"service": "TCP-3307", "tcp": {"dst-port": "3307"}}, {"service": "TCP-6446", "tcp": {"dst-port": "6446"}}, {"service": "tcp-1521", "tcp": {"dst-port": "1521"}}, {"service": "TCP2883", "tcp": {"dst-port": "2883"}}, {"service": "TCP-2181", "tcp": {"dst-port": "2181"}}, {"service": "TCP-60000", "tcp": {"dst-port": "60000"}}, {"service": "TCP-60002", "tcp": {"dst-port": "60002"}}, {"service": "TCP-60020", "tcp": {"dst-port": "60020"}}, {"service": "TCP-4433", "tcp": {"dst-port": "4433"}}, {"service": "TCP-441", "tcp": {"dst-port": "441"}}, {"service": "UDP-443", "udp": {"dst-port": "443"}}, {"service": "UDP-441", "udp": {"dst-port": "441"}}, {"service": "TCP-8063", "tcp": {"dst-port": "0 65535 src-port 8063"}}, {"service": "TCP-9030", "tcp": {"dst-port": "9030"}}, {"service": "UDP-8063", "udp": {"dst-port": "8063"}}, {"service": "8065", "tcp": {"dst-port": "8065"}, "udp": {"dst-port": "8065"}}]
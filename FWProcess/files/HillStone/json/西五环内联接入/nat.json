{"trust-vr": [{"Type": "s<PERSON>rule", "id": "1", "from": "*********", "to": "***********", "service": "Any", "trans-to": "***********", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "2", "from": "************/24", "to": "Any", "service": "Any", "trans-to": "TC_APP_NAT_Address", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "3", "from": "*********/24", "to": "Any", "service": "Any", "trans-to": "*******/32", "mode": "dynamicport", "log": true}, {"Type": "dnatrule", "id": "6", "from": "Any", "to": "***********/32", "service": "Any", "trans-to": "*********/32", "log": true}, {"Type": "dnatrule", "id": "1", "from": "Any", "to": "*********/32", "service": "Any", "trans-to": "*********/32", "log": true}, {"Type": "dnatrule", "id": "7", "from": "Any", "to": "***********/32", "service": "Any", "trans-to": "*********/32", "log": true}, {"Type": "dnatrule", "id": "8", "from": "Any", "to": "***********/32", "service": "Any", "trans-to": "*********/32", "log": true}, {"Type": "dnatrule", "id": "9", "from": "Any", "to": "***********/32", "service": "Any", "trans-to": "*********/32", "log": true}]}
[{"address_name": "private_network", "ip": [{"type": "ip", "value": "10.0.0.0/8"}, {"type": "ip", "value": "**********/12"}, {"type": "ip", "value": "***********/16"}]}, {"address_name": "citrix", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "NTP", "ip": [{"type": "ip", "value": "*******/32"}]}, {"address_name": "NFS_*********", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "NFS_*********", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "NFS_*********", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "NFS_*********", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "Group_10.x.0.0", "ip": [{"type": "ip", "value": "10.0.0.0/8"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "********-2", "ip": [{"type": "range", "value": "******** ********"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "********-3", "ip": [{"type": "range", "value": "******** ********"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "********-2", "ip": [{"type": "range", "value": "******** ********"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "********-4", "ip": [{"type": "range", "value": "******** ********"}]}, {"address_name": "**********/24", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "*******", "ip": [{"type": "ip", "value": "*******/32"}]}, {"address_name": "***********-***********", "ip": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "************-************", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "*******", "ip": [{"type": "ip", "value": "*******/32"}]}, {"address_name": "************-************", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "***********-***********", "ip": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "***********-***********", "ip": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "*******10", "ip": [{"type": "ip", "value": "*******10/32"}]}, {"address_name": "*********", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "************-15", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "********-9", "ip": [{"type": "range", "value": "******** ********"}]}, {"address_name": "************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "**********-14", "ip": [{"type": "range", "value": "********** **********"}]}, {"address_name": "************-13", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "**********-2", "ip": [{"type": "ip", "value": "**********/32"}, {"type": "ip", "value": "**********/32"}]}, {"address_name": "*********/24", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "**********/21", "ip": [{"type": "ip", "value": "**********/21"}]}, {"address_name": "**********/21", "ip": [{"type": "ip", "value": "**********/21"}]}, {"address_name": "**********/21", "ip": [{"type": "ip", "value": "**********/21"}]}, {"address_name": "***********/21", "ip": [{"type": "ip", "value": "***********/21"}]}, {"address_name": "**********/24", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "**********-2", "ip": [{"type": "range", "value": "********** 4.190.88.2"}]}, {"address_name": "4.9.1.0/24", "ip": [{"type": "ip", "value": "4.9.1.0/24"}]}, {"address_name": "4.190.0.0/16", "ip": [{"type": "ip", "value": "4.190.0.0/16"}]}, {"address_name": "**********2", "ip": [{"type": "ip", "value": "**********2/32"}]}, {"address_name": "4.190.0.41", "ip": [{"type": "ip", "value": "4.190.0.41/32"}]}, {"address_name": "10.194.40.0/24", "ip": [{"type": "ip", "value": "10.194.40.0/24"}]}]
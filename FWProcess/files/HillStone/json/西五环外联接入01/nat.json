{"E-FW": [{"Type": "s<PERSON>rule", "id": "1", "from": "range_4.13.10.1-12", "to": "Any", "service": "Any", "trans-to": "4.99.0.10/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "2", "from": "BJDC_4.11.5.141", "to": "Any", "service": "Any", "trans-to": "4.99.11.11/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "3", "from": "BJDC_4.11.5.142", "to": "Any", "service": "Any", "trans-to": "4.99.11.12/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "4", "from": "BJDC_4.11.5.143", "to": "Any", "service": "Any", "trans-to": "4.99.11.13/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "5", "from": "4.9.11.30/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.30/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "6", "from": "4.9.11.31/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.31/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "7", "from": "4.9.11.32/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.32/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "8", "from": "3.9.11.30/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.40/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "9", "from": "3.9.11.31/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.41/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "10", "from": "3.9.11.32/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.42/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1001", "from": "192.168.18.2/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.11/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1002", "from": "192.168.8.50/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.12/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1003", "from": "192.168.8.155/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.13/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1004", "from": "192.168.8.164/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.14/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1005", "from": "10.100.108.2/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.15/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1006", "from": "10.100.108.3/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.16/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1007", "from": "10.100.108.4/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.17/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1008", "from": "104.200.100.1/32", "to": "Any", "service": "Any", "trans-to": "*********/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "1009", "from": "*************/32", "to": "Any", "service": "Any", "trans-to": "*********/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "12", "from": "range_4.28.10.13-15", "to": "Any", "service": "Any", "trans-to": "**********1/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "1010", "from": "Range_Coding-NG", "to": "Any", "service": "Any", "trans-to": "*********", "mode": "dynamicip", "log": true}, {"Type": "s<PERSON>rule", "id": "13", "from": "************/24", "to": "Any", "service": "Any", "trans-to": "**********", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "15", "from": "********/24", "to": "Any", "service": "Any", "trans-to": "**********", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "14", "from": "************/24", "to": "Any", "service": "Any", "trans-to": "**********", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "16", "from": "CZB_4.14.100.23/32", "to": "Any", "service": "Any", "trans-to": "CZB_NAT_172.31.18.81/32", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "17", "from": "CZB_4.14.100.24/32", "to": "Any", "service": "Any", "trans-to": "CZB_NAT_172.31.18.82/32", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "18", "from": "CZB_4.14.100.25/32", "to": "Any", "service": "Any", "trans-to": "CZB_NAT_172.31.18.83/32", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "19", "from": "CZB_4.14.100.26/32", "to": "Any", "service": "Any", "trans-to": "CZB_NAT_172.31.18.84/32", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "20", "from": "CZB_4.14.100.27/32", "to": "Any", "service": "Any", "trans-to": "CZB_NAT_172.31.18.85/32", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "21", "from": "Range_Zabbix", "to": "Any", "service": "Any", "trans-to": "4.99.11.20/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "23", "from": "VDI_10.211.7.0", "to": "Any", "service": "Any", "trans-to": "4.98.1.20", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "30", "from": "198.2.1.11/32", "to": "Any", "service": "Any", "trans-to": "4.99.11.42/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "32", "from": "198.2.1.12/32", "to": "Any", "service": "Any", "trans-to": "4.99.11.43/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "34", "from": "JG_HI_4.101.51.20", "to": "Any", "service": "Any", "trans-to": "JG_HI_4.101.51.20_NAT", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "35", "from": "HaiNan_10.23.6.100", "to": "Any", "service": "Any", "trans-to": "4.98.46.100/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "36", "from": "HaiNan_10.23.6.11", "to": "Any", "service": "Any", "trans-to": "4.98.46.11/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "37", "from": "HaiNan_10.23.6.21", "to": "Any", "service": "Any", "trans-to": "4.98.46.21/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "40", "from": "JG_HI_4.101.51.10", "to": "Any", "service": "Any", "trans-to": "JG_HI_4.101.51.10_NAT", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "45", "from": "172.16.31.28", "to": "Any", "service": "Any", "trans-to": "4.98.12.28/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "46", "from": "172.16.31.29", "to": "Any", "service": "Any", "trans-to": "4.98.12.29/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "47", "from": "4.35.31.11", "to": "Any", "service": "Any", "trans-to": "4.99.12.11/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "48", "from": "4.35.31.12", "to": "Any", "service": "Any", "trans-to": "4.99.12.12/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "51", "from": "jiankong_4.254.127.0/24", "to": "Any", "service": "Any", "trans-to": "GD_4.99.11.127", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "52", "from": "10.194.123.0/24", "to": "Any", "service": "Any", "trans-to": "4.99.11.124/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "53", "from": "192.168.100.2", "to": "Any", "service": "Any", "trans-to": "4.98.10.20/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "54", "from": "192.168.100.11", "to": "Any", "service": "Any", "trans-to": "4.98.10.21/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "55", "from": "192.168.100.12", "to": "Any", "service": "Any", "trans-to": "4.98.10.22/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "61", "from": "4.101.90.31/32", "to": "Any", "service": "Any", "trans-to": "4.99.1.14/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "62", "from": "4.101.90.32/32", "to": "Any", "service": "Any", "trans-to": "4.99.1.15/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "68", "from": "10.210.3.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.16", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "63", "from": "4.101.6.1/32", "to": "Any", "service": "Any", "trans-to": "4.99.1.35/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "64", "from": "4.101.7.1/32", "to": "Any", "service": "Any", "trans-to": "4.99.1.36/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "65", "from": "4.101.9.111/32", "to": "Any", "service": "Any", "trans-to": "4.99.1.37/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "66", "from": "4.101.9.112/32", "to": "Any", "service": "Any", "trans-to": "4.99.1.38/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "70", "from": "10.210.4.13/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.17/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "11", "from": "Range_SJJM-SSL", "to": "Any", "service": "Any", "trans-to": "4.99.1.10", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "71", "from": "10.217.131.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.18/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "72", "from": "10.217.132.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.19/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "73", "from": "10.217.133.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.21/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "74", "from": "10.217.134.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.22/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "75", "from": "10.217.135.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.23/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "76", "from": "10.217.136.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.24/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "77", "from": "10.217.137.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.25/32", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "80", "from": "172.16.31.55", "to": "Any", "service": "Any", "trans-to": "4.98.12.35/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "81", "from": "172.16.31.56", "to": "Any", "service": "Any", "trans-to": "4.98.12.36/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "82", "from": "10.217.0.4/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.34/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "83", "from": "4.255.205.34/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.171/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "84", "from": "4.255.205.35/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.172/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "85", "from": "4.255.205.36/32", "to": "Any", "service": "Any", "trans-to": "4.99.10.173/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "86", "from": "10.217.7.11/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.26/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "87", "from": "10.217.7.12/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.27/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "88", "from": "10.217.7.13/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.28/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "89", "from": "10.217.7.14/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.29/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "90", "from": "10.217.7.18/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.30/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "91", "from": "10.217.7.19/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.31/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "92", "from": "10.217.0.2/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.32/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "93", "from": "10.196.5.0/24", "to": "Any", "service": "Any", "trans-to": "**********2", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "100", "from": "172.16.31.15", "to": "Any", "service": "Any", "trans-to": "4.98.12.41/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "102", "from": "172.16.32.31", "to": "Any", "service": "Any", "trans-to": "4.98.12.43/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "101", "from": "172.16.32.16", "to": "Any", "service": "Any", "trans-to": "4.98.12.42/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "22", "from": "10.217.7.21/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.35/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "24", "from": "10.217.7.22/32", "to": "Any", "service": "Any", "trans-to": "4.98.1.36/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "94", "from": "192.168.5.12/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.23/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "95", "from": "192.168.5.11/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.24/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "96", "from": "192.168.2.13/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.25/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "97", "from": "192.168.4.11/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.26/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "98", "from": "192.168.4.12/32", "to": "Any", "service": "Any", "trans-to": "4.98.10.27/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "25", "from": "10.210.13.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.40", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "26", "from": "192.168.33.2", "to": "Any", "service": "Any", "trans-to": "4.98.10.30", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "27", "from": "192.168.33.3", "to": "Any", "service": "Any", "trans-to": "4.98.10.31", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "28", "from": "172.9.10.66", "to": "Any", "service": "Any", "trans-to": "4.98.10.32", "mode": "static", "log": false}, {"Type": "s<PERSON>rule", "id": "29", "from": "10.223.25.0/24", "to": "Any", "service": "Any", "trans-to": "4.98.1.50", "mode": "dynamicport", "log": false}, {"Type": "s<PERSON>rule", "id": "31", "from": "4.101.90.71/32", "to": "Any", "service": "Any", "trans-to": "4.99.12.71/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "33", "from": "4.101.90.72/32", "to": "Any", "service": "Any", "trans-to": "4.99.12.72/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "38", "from": "10.90.0.0/16", "to": "Any", "service": "Any", "trans-to": "4.99.11.17", "mode": "dynamicport", "log": true}, {"Type": "s<PERSON>rule", "id": "39", "from": "4.101.90.81", "to": "Any", "service": "Any", "trans-to": "4.99.1.81/32", "mode": "static", "log": true}, {"Type": "s<PERSON>rule", "id": "41", "from": "4.101.90.82", "to": "Any", "service": "Any", "trans-to": "4.99.1.82/32", "mode": "static", "log": true}, {"Type": "dnatrule", "id": "1001", "from": "Any", "to": "BJDC_4.98.11.10", "service": "Any", "trans-to": "192.168.21.100/32", "log": true}, {"Type": "dnatrule", "id": "1002", "from": "Any", "to": "BJDC_4.98.11.20", "service": "Any", "trans-to": "192.168.21.20/32", "log": true}, {"Type": "dnatrule", "id": "1", "from": "Any", "to": "4.98.10.150", "service": "Any", "trans-to": "192.168.1.150", "log": true}, {"Type": "dnatrule", "id": "2", "from": "Any", "to": "4.98.1.10", "service": "Any", "trans-to": "104.200.100.35", "log": true}, {"Type": "dnatrule", "id": "11", "from": "Any", "to": "4.99.10.11/32", "service": "Any", "trans-to": "4.101.15.11/32", "log": true}, {"Type": "dnatrule", "id": "12", "from": "Any", "to": "4.99.10.12/32", "service": "Any", "trans-to": "4.101.15.12/32", "log": true}, {"Type": "dnatrule", "id": "13", "from": "Any", "to": "4.99.10.13/32", "service": "Any", "trans-to": "4.101.15.21/32", "log": true}, {"Type": "dnatrule", "id": "14", "from": "Any", "to": "4.99.10.14/32", "service": "Any", "trans-to": "4.101.15.22/32", "log": true}, {"Type": "dnatrule", "id": "15", "from": "Any", "to": "*********/32", "service": "Any", "trans-to": "4.101.5.13/32", "log": true}, {"Type": "dnatrule", "id": "1003", "from": "Any", "to": "4.98.11.40/32", "service": "Any", "trans-to": "192.168.30.3/32", "log": true}, {"Type": "dnatrule", "id": "1004", "from": "Any", "to": "4.98.11.41/32", "service": "Any", "trans-to": "************/32", "log": true}, {"Type": "dnatrule", "id": "1005", "from": "Any", "to": "*********", "service": "Any", "trans-to": "************", "log": true}, {"Type": "dnatrule", "id": "1006", "from": "Any", "to": "**********", "service": "Any", "trans-to": "***********", "log": true}, {"Type": "dnatrule", "id": "1007", "from": "Any", "to": "**********", "service": "Any", "trans-to": "**********", "log": true}, {"Type": "dnatrule", "id": "10", "from": "Any", "to": "IP_4.98.1.13", "service": "Any", "trans-to": "IP_104.12.10.163", "log": true}, {"Type": "dnatrule", "id": "3", "from": "Any", "to": "CZB_NAT_4.98.10.18/32", "service": "Any", "trans-to": "CZB_172.2.141.21/32", "log": true}, {"Type": "dnatrule", "id": "4", "from": "Any", "to": "CZB_NAT_4.98.10.19/32", "service": "Any", "trans-to": "CZB_172.21.12.15/32", "log": false}, {"Type": "dnatrule", "id": "22", "from": "Any", "to": "*********/32", "service": "Any", "trans-to": "**********/32", "log": true}, {"Type": "dnatrule", "id": "24", "from": "Any", "to": "*********/32", "service": "Any", "trans-to": "**********/32", "log": true}, {"Type": "dnatrule", "id": "25", "from": "Any", "to": "*********/32", "service": "Any", "trans-to": "***********/32", "log": true}, {"Type": "dnatrule", "id": "26", "from": "Any", "to": "*********/32", "service": "Any", "trans-to": "***********/32", "log": true}, {"Type": "dnatrule", "id": "27", "from": "Any", "to": "*********/32", "service": "Any", "trans-to": "***********/32", "log": true}, {"Type": "dnatrule", "id": "28", "from": "Any", "to": "4.99.1.34/32", "service": "Any", "trans-to": "4.101.90.14/32", "log": true}, {"Type": "dnatrule", "id": "31", "from": "Any", "to": "4.98.11.42/32", "service": "Any", "trans-to": "192.168.30.20/32", "log": true}, {"Type": "dnatrule", "id": "33", "from": "Any", "to": "4.98.11.43/32", "service": "Any", "trans-to": "192.168.31.20/32", "log": true}, {"Type": "dnatrule", "id": "38", "from": "Any", "to": "JG_HI_4.101.51.10_NAT", "service": "Any", "trans-to": "JG_HI_4.101.51.10", "log": true}, {"Type": "dnatrule", "id": "39", "from": "Any", "to": "HaiNan_10.23.9.200", "service": "Any", "trans-to": "4.98.46.200/32", "log": true}, {"Type": "dnatrule", "id": "41", "from": "Any", "to": "4.98.46.100/32", "service": "Any", "trans-to": "10.23.6.100/32", "log": true}, {"Type": "dnatrule", "id": "42", "from": "Any", "to": "4.98.46.11/32", "service": "Any", "trans-to": "10.23.6.11/32", "log": true}, {"Type": "dnatrule", "id": "43", "from": "Any", "to": "4.98.46.21/32", "service": "Any", "trans-to": "10.23.6.21/32", "log": true}, {"Type": "dnatrule", "id": "44", "from": "Any", "to": "4.99.46.1/32", "service": "Any", "trans-to": "4.101.50.1/32", "log": true}, {"Type": "dnatrule", "id": "49", "from": "Any", "to": "4.99.12.18/32", "service": "Any", "trans-to": "4.101.5.18/32", "log": true}, {"Type": "dnatrule", "id": "50", "from": "Any", "to": "4.98.12.19/32", "service": "Any", "trans-to": "172.16.31.19/32", "log": true}, {"Type": "dnatrule", "id": "56", "from": "Any", "to": "4.99.10.170/32", "service": "Any", "trans-to": "4.101.170.1/32", "log": true}, {"Type": "dnatrule", "id": "57", "from": "Any", "to": "BJDC_4.98.11.117", "service": "Any", "trans-to": "192.168.21.117/32", "log": true}, {"Type": "dnatrule", "id": "60", "from": "Any", "to": "4.98.1.14", "service": "Any", "trans-to": "104.200.100.38", "log": true}, {"Type": "dnatrule", "id": "67", "from": "Any", "to": "4.98.1.15/32", "service": "Any", "trans-to": "10.210.1.15/32", "log": true}, {"Type": "dnatrule", "id": "69", "from": "Any", "to": "4.99.1.16/32", "service": "Any", "trans-to": "3.254.20.12/32", "log": true}, {"Type": "dnatrule", "id": "68", "from": "Any", "to": "4.99.2.1/32", "service": "Any", "trans-to": "4.190.160.5/32", "log": true}, {"Type": "dnatrule", "id": "70", "from": "Any", "to": "4.98.1.26/32", "service": "Any", "trans-to": "10.217.7.11/32", "log": true}, {"Type": "dnatrule", "id": "71", "from": "Any", "to": "4.98.1.27/32", "service": "Any", "trans-to": "10.217.7.12/32", "log": true}, {"Type": "dnatrule", "id": "72", "from": "Any", "to": "4.98.1.28/32", "service": "Any", "trans-to": "10.217.7.13/32", "log": true}, {"Type": "dnatrule", "id": "73", "from": "Any", "to": "4.98.1.29/32", "service": "Any", "trans-to": "10.217.7.14/32", "log": true}, {"Type": "dnatrule", "id": "74", "from": "Any", "to": "4.98.1.33/32", "service": "Any", "trans-to": "10.217.0.3/32", "log": true}, {"Type": "dnatrule", "id": "75", "from": "Any", "to": "4.99.1.23/32", "service": "Any", "trans-to": "4.101.5.23/32", "log": true}, {"Type": "dnatrule", "id": "76", "from": "Any", "to": "4.99.1.19/32", "service": "Any", "trans-to": "4.101.5.19/32", "log": true}, {"Type": "dnatrule", "id": "77", "from": "Any", "to": "4.99.1.25/32", "service": "Any", "trans-to": "4.101.5.25/32", "log": true}, {"Type": "dnatrule", "id": "78", "from": "Any", "to": "4.99.1.26/32", "service": "Any", "trans-to": "4.101.90.51/32", "log": true}, {"Type": "dnatrule", "id": "79", "from": "Any", "to": "4.99.1.27/32", "service": "Any", "trans-to": "4.101.90.52/32", "log": true}, {"Type": "dnatrule", "id": "80", "from": "Any", "to": "4.99.1.28/32", "service": "Any", "trans-to": "4.101.90.53/32", "log": true}, {"Type": "dnatrule", "id": "81", "from": "Any", "to": "4.99.1.29/32", "service": "Any", "trans-to": "4.101.90.54/32", "log": true}, {"Type": "dnatrule", "id": "5", "from": "Any", "to": "4.99.1.251/32", "service": "Any", "trans-to": "4.101.91.251/32", "log": true}, {"Type": "dnatrule", "id": "6", "from": "Any", "to": "4.99.1.50/32", "service": "Any", "trans-to": "4.101.5.27/32", "log": true}, {"Type": "dnatrule", "id": "82", "from": "Any", "to": "4.99.1.51/32", "service": "Any", "trans-to": "4.101.5.16/32", "log": true}, {"Type": "dnatrule", "id": "7", "from": "Any", "to": "4.98.1.80", "service": "Any", "trans-to": "10.224.1.100", "log": true}, {"Type": "dnatrule", "id": "8", "from": "Any", "to": "4.99.1.121", "service": "Any", "trans-to": "4.101.5.28", "log": false}]}
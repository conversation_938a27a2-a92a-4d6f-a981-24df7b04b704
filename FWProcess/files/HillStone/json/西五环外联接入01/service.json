[{"service": "TCP-1521"}, {"service": "udp-162", "udp": {"dst-port": "162"}}, {"service": "udp-514", "udp": {"dst-port": "514"}}, {"service": "tcp-8890", "tcp": {"dst-port": "8890"}}, {"service": "tcp-8891", "tcp": {"dst-port": "8891"}}, {"service": "udp-161", "udp": {"dst-port": "161"}}, {"service": "rdp-3389", "tcp": {"dst-port": "3389"}}, {"service": "tcp-8999", "tcp": {"dst-port": "8999"}}, {"service": "tcp-443", "tcp": {"dst-port": "443"}}, {"service": "1", "tcp": {"dst-port": "80"}}, {"service": "TCP_6666", "tcp": {"dst-port": "6666"}}, {"service": "AD-PORT", "tcp": {"dst-port": "53"}, "udp": {"dst-port": "123"}}, {"service": "TCP-23000", "tcp": {"dst-port": "23000"}}, {"service": "TCP-8022", "tcp": {"dst-port": "8022"}}, {"service": "TCP-54102", "tcp": {"dst-port": "54102"}}, {"service": "TCP-54103", "tcp": {"dst-port": "54103"}}, {"service": "TCP-54104", "tcp": {"dst-port": "54104"}}, {"service": "TCP-28080", "tcp": {"dst-port": "28080"}}, {"service": "TCP-80", "tcp": {"dst-port": "80"}}, {"service": "TCP-8080", "tcp": {"dst-port": "8080"}}, {"service": "TCP-8081", "tcp": {"dst-port": "8081"}}, {"service": "tcp-4422", "tcp": {"dst-port": "4422"}}, {"service": "TCP-30000", "tcp": {"dst-port": "30000"}}, {"service": "tcp_9001", "tcp": {"dst-port": "9001"}}, {"service": "TCP-3000", "tcp": {"dst-port": "3000"}}, {"service": "TCP-10102", "tcp": {"dst-port": "10102"}}, {"service": "tcp-389", "tcp": {"dst-port": "389"}}, {"service": "UDP-1234", "udp": {"dst-port": "1234"}}, {"service": "TCP-6006", "tcp": {"dst-port": "6006"}}, {"service": "TCP-8931", "tcp": {"dst-port": "8931"}}, {"service": "TCP_34443", "tcp": {"dst-port": "34443"}}, {"service": "TCP_18081", "tcp": {"dst-port": "18081"}}, {"service": "tcp-29093", "tcp": {"dst-port": "29093"}}, {"service": "SFTP22", "tcp": {"dst-port": "22"}}, {"service": "tcp-8443", "tcp": {"dst-port": "8443"}}, {"service": "tcp-32600", "tcp": {"dst-port": "32600"}}, {"service": "tcp-7443", "tcp": {"dst-port": "7443"}}, {"service": "SecOCS-PORT", "tcp": {"dst-port": "6000 8000"}}, {"service": "TCP-10000", "tcp": {"dst-port": "10000"}}, {"service": "tcp-4433", "tcp": {"dst-port": "4433"}}]
[{"rule": "45", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3_MS_AIDB_4.190.122.121-123", "dst-addr": "G3_Mail_*********/32", "service": "SMTP, TCP-25"}, {"rule": "11", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "V3_MS_4.190.120.101/32", "dst-addr": "Zabbix-Proxy-*********51", "service": "SSH", "name": "V3-11"}, {"rule": "4", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/32, *********/32", "dst-addr": "Mail_106.39.30.49, DNS_114.114.114.114, DNS_8.8.8.8", "service": "SMTP, SMTPS, POP3, DNS", "name": "V3_DMZ_TO_Internet"}, {"rule": "6", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "DNS-4.190.80.52, DNS-4.190.80.51", "service": "DNS", "name": "V3-1"}, {"rule": "7", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "*********/16, 4A-18.2.64.0/24, BOCC_18.2.12.0, BOCC-18.2.22.0, BOCC-18.2.1.0/24, 4.128.0.0, YZBOCC, YJBOCC_9.66.32.0", "dst-addr": "*********/16, DMZ_NAS_4.191.56.0/22", "service": "SSH, HTTP, HTTPS, TCP-8443"}, {"rule": "8", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "AlarManager-************-72, Solarwinds-4.176.28.1, OPSTOOL-***********81-182", "dst-addr": "*********/32, *********/32", "service": "SMTP, SMTPS", "name": "V3-2"}, {"rule": "9", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "TCP-4.190.120.0", "dst-addr": "*********/32, *********/32", "service": "TCP_31050", "name": "V3-3"}, {"rule": "10", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "Zabbix-Proxy-*********51", "dst-addr": "ZabbixServer, 4.190.163.15", "service": "TCP-31051, TCP_31050", "name": "V3-4"}, {"rule": "90", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA11-**********", "dst-addr": "4.14.100.32", "service": "TCP_21050", "name": "juncai_impala"}, {"rule": "12", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "NTP-18.0.1.173, NTp-18.0.11.173", "service": "HTTP, NTP", "name": "V3-5"}, {"rule": "13", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "ZabbixServer, MS-4.190.120.0/24", "dst-addr": "Zabbix-Proxy-*********51, *********/16", "service": "TCP_31050, TCP-31051, TCP-31306", "name": "V3-3-6"}, {"rule": "14", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "Solarwinds-18.253.64.240", "dst-addr": "*********/16", "service": "SNMP, SYSLOG"}, {"rule": "15", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "Solarwinds-18.253.64.240", "service": "SNMP"}, {"rule": "16", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/32, *********/32", "dst-addr": "G2-Mail-18.1.16.11, G2_Mail_18.1.16.101/32", "service": "SMTPS, SMTP", "name": "G3-to_G2-Mail"}, {"rule": "2", "action": "permit", "log": "session-start", "src-zone": "Any", "dst-zone": "Any", "src-addr": "Any", "dst-addr": "Any", "service": "OSPF, ICMP"}, {"rule": "17", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "4.190.120.104, ************1", "dst-addr": "V3_DMZ_*********/24", "service": "Any", "name": "Jinkong"}, {"rule": "19", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "DMZ_NAS_4.191.56.0/22", "dst-addr": "NAS_Public_4.191.40.0/24", "service": "Any"}, {"rule": "20", "action": "permit", "src-zone": "Internet", "dst-zone": "Inside", "src-addr": "G3-Test-4.176.9.100/32, G3-Test-4.176.9.0/24", "dst-addr": "G3-Test-4.190.79.0/24, ***********/24", "service": "Any", "name": "G3-Test"}, {"rule": "3", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G2OCS-18.0.2.163/32", "dst-addr": "*********/16", "service": "TCP_3558, TCP_3555, TCP-31306, SSH"}, {"rule": "18", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "BOCC&4A, CSLC-baoleiji-*********/24, CSLC-baoleiji-**********/24, 4.128.0.0, YZBOCC", "dst-addr": "G3_*********/16", "service": "TCP_8000, TCP_3555, TCP_3558, TCP-31306, TCP_8080, SSH, HTTP, HTTPS, TCP_8888, TCP-8021-8025, TCP-9443, SMTPS, SMTP", "name": "G3-V1.3-20200429-02"}, {"rule": "21", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "NTp-18.0.11.173, NTP-18.0.1.173", "service": "NTP, UDP_123"}, {"rule": "23", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "18.2.11.74, YJBOCC_9.66.32.0", "dst-addr": "SSLF5-***********", "service": "HTTPS", "name": "G3-V131-TAS"}, {"rule": "75", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "DMZ-SSL-*********/24", "dst-addr": "WCS-F5-***********", "service": "TCP-8086", "name": "G3-V131-RMOAS"}, {"rule": "24", "action": "permit", "log": "session-start, session-end", "src-zone": "dmz", "dst-zone": "Inside", "src-addr": "DMZ-SSL-*********/24", "dst-addr": "WCS-F5-***********", "service": "TCP-8086", "description": "TAS-赛果接入", "name": "G3-V131"}, {"rule": "25", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "CSLC-baoleiji-*********/24, CSLC-baoleiji-**********/24", "dst-addr": "G3_*********/16", "service": "TCP_3389, TCP_8889, TCP_8888, TCP_8013, TCP-8090, TCP-31306, TCP_3558, TCP_3555, TCP_8000, SSH, HTTP, HTTPS", "name": "CSLC-baoleiji"}, {"rule": "26", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "V3_DMZ_*********/24", "dst-addr": "***********-6", "service": "TCP-29092", "name": "TO-ELK"}, {"rule": "27", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "*************-212, MS-K8S-***********/24, AlarManager-************-72, *********/16", "dst-addr": "*********/32, *********/32", "service": "TCP-25"}, {"rule": "29", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "***********/24", "dst-addr": "G2-TAS-SSL-*********", "service": "HTTPS"}, {"rule": "28", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "FOC-***********, FOC-*********", "dst-addr": "Any", "service": "Any", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"rule": "30", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3_CORE_4.190.83.1, G3_CORE_4.190.83.2", "dst-addr": "*********/16", "service": "TCP_8080, TCP-3191, TCP-5003, TCP-7001, TCP_3558, TCP_3555, TCP-6370, TCP-2379, HTTP, SSH", "name": "V3_OPERHOST_4.190"}, {"rule": "31", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Internet", "src-addr": "G3WCSINFOS-*********1-22, SBSIRMBMO-**********-32", "dst-addr": "Any", "service": "HTTP", "name": "RMX1.1.0_01"}, {"rule": "32", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Internet", "src-addr": "G3WCSINFOS-*********1-22, G3TOHERAPROXY-*********1-12, SBSIRMBMO-**********-32, V3_DMZ_*********/24", "dst-addr": "DNS_114.114.114.114, DNS-************, DNS-*********", "service": "DNS, ICMP, PING", "name": "RMX1.1.0_02"}, {"rule": "33", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "G3WCSINFOS-*********1-22", "dst-addr": "GWREDIS-**********/24", "service": "TCP-7001", "name": "RMX1.1.0_03"}, {"rule": "34", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "G3WCSINFOS-*********1-22", "dst-addr": "G3WCSINFOSFTP-F5-***********, G3WCSINFOSFTP-***********-42", "service": "SSH", "name": "RMX1.1.0_04"}, {"rule": "35", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "G3TOHERAPROXY-*********1-12", "dst-addr": "G3GAIA-F5-4.190.163.20", "service": "TCP-28080", "name": "JCJK1.3.7_01"}, {"rule": "36", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3MONITORGAIA-4.190.122.161-162, MS-4.190.120.0/22", "dst-addr": "G3TOHERAPROXY-F5-4.190.160.3", "service": "TCP-28083", "name": "JCJK1.3.7_02"}, {"rule": "37", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3MONITORGAIA-4.190.122.161-162", "dst-addr": "G3-MS-K8S-4.190.44.0/24", "service": "HTTP", "name": "JCJK1.3.7_03"}, {"rule": "38", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Internet", "src-addr": "G3TOHERAPROXY-*********1-12", "dst-addr": "Any", "service": "HTTPS", "name": "JCJK1.3.7_04"}, {"rule": "39", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "G3TOHERAPROXY-*********1-12", "dst-addr": "G3JCFAUTHCASSERVER-F5-4.190.163.13, G3MONITORPORTAL-F5-4.190.163.11", "service": "HTTPS, TCP-28080", "name": "JCJK1.3.7_05"}, {"rule": "40", "action": "permit", "log": "policy-deny, session-start, session-end", "src-zone": "Internet", "dst-zone": "DMZ", "src-addr": "Any", "dst-addr": "G3TOHERAPROXY-F5-4.190.160.3", "service": "TCP-28080, TCP-28081, TCP-28082", "name": "JCJK1.3.7_06"}, {"rule": "41", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "OJobServer-18.0.2.161-162", "dst-addr": "V3_DMZ_*********/24", "service": "SSH", "name": "OJobServer-TO-DMZ"}, {"rule": "42", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "V3_DMZ_*********/24", "dst-addr": "OCS-FTP-18.0.2.171", "service": "FTP", "name": "OCS-FTP"}, {"rule": "43", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "V3_DMZ_*********/24", "dst-addr": "ConfigCenter-F5-4.190.162.5, ConfigCenter-4.190.80.141-143", "service": "TCP-28081, TCP-28070, HTTP", "name": "TO-ConfigCenter"}, {"rule": "44", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "SYSLOG-F5-4.190.163.16", "service": "SYSLOG", "name": "TO-SYSLOG"}, {"rule": "46", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "G3_CORE_NTP_4.190.80.251-252", "service": "NTP"}, {"rule": "47", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA12-**********, SBSG2BISRVA11-**********, SBSG2BISRVA13-4.190.0.43", "dst-addr": "BASDB-4.190.88.43, BLIDB-4.190.88.104, 4.35.10.10/32", "service": "TCP_3555, TCP-31306, TCP_8080", "name": "SDAS-V2.15.1_01"}, {"rule": "48", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA11-**********, SBSG2BISRVA12-**********", "dst-addr": "BLIDB-4.190.88.104", "service": "TCP-31306", "name": "SDAS-V2.15.1_02"}, {"rule": "59", "action": "permit", "log": "session-start", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA12-**********, SBSG2BISRVA11-**********, **********", "dst-addr": "CSLC-KYLIN-************, CSLC-PHOENIX-************-57, ***********/24", "service": "TCP-2181, TCP-7070, TCP-16020, TCP-16010, TCP-16000", "name": "SDAS-V2.15.1_03"}, {"rule": "60", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA11-**********, SBSG2BISRVA12-**********", "dst-addr": "RTQDBVIP-***********", "service": "TCP_3555", "name": "SDAS-V2.15.1_04"}, {"rule": "49", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "Ansbile-************", "dst-addr": "*********/16", "service": "SSH"}, {"rule": "50", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSIRMBMO-**********-32", "dst-addr": "SBSG2IRMDBVIP-**********", "service": "TCP_3555", "name": "IRM1.15.0_01"}, {"rule": "51", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "DMZ-SSL-*********/24", "dst-addr": "IRM-F5-************", "service": "TCP-8443", "name": "IRM1.15.0_02"}, {"rule": "52", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "SBSG2OPSFTP01-************", "service": "FTP, SSH", "name": "IRM1.15.0_03"}, {"rule": "54", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3_CORE_4.190.83.2, G3_CORE_4.190.83.1", "dst-addr": "G3_*********/16", "service": "UDP-8472, TCP-5003, TCP-3191, TCP_8080, TCP-7001, TCP_3555, TCP_3558, TCP-2379, TCP-6370, HTTP, SSH", "name": "IRM1.15.0_04"}, {"rule": "55", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "ItoSchedule-*************-172", "dst-addr": "*********/32, *********/32", "service": "TCP-25", "name": "ItoSchedule"}, {"rule": "58", "action": "permit", "src-zone": "DMZ", "dst-zone": "Internet", "src-addr": "*********/32, *********/32", "dst-addr": "Mail_106.39.30.49", "service": "SMTP, TCP-25"}, {"rule": "56", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "SBSG2OTJob-4.190.88.71", "dst-addr": "*********/16", "service": "SSH", "name": "G3-R160_01"}, {"rule": "57", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA12-**********, SBSG2BISRVA11-**********", "dst-addr": "CSLC-Hive-4.35.10.5/32", "service": "TCP-6100-6200"}, {"rule": "61", "action": "permit", "log": "session-start", "src-zone": "Internet", "dst-zone": "DMZ", "src-addr": "CSLC-SSL4.103.211.0/24", "dst-addr": "SSL-4.190.4.3", "service": "TCP-8023-8024, <PERSON><PERSON><PERSON>, TCP-8021, TCP-8022, TCP-8025, TCP-9443, TCP-8443, PING", "name": "LinShiCeShi-20201208"}, {"rule": "62", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA11-**********, SBSG2BISRVA12-**********", "dst-addr": "G2BASDB-18.0.1.75", "service": "TCP_3555"}, {"rule": "63", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3-CORE-SDAS-4.190.80.191-192", "dst-addr": "SBSG2BISRVA11-**********", "service": "TCP-8023-8024"}, {"rule": "64", "action": "permit", "src-zone": "Internet", "dst-zone": "DMZ", "src-addr": "Any", "dst-addr": "SSL-4.190.4.3", "service": "TCP-8021-8025, TCP-8443, TCP-9443, HTTPS", "name": "SDAS-V2.15.1_05"}, {"rule": "65", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "RMOAS-4.98.130.0/24", "dst-addr": "SSLF5-***********", "service": "HTTPS", "name": "RMOAS"}, {"rule": "66", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "FOC-4.176.1.136-137", "dst-addr": "V3_DMZ_*********/24", "service": "Any", "name": "SCAN"}, {"rule": "68", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "*********/16", "dst-addr": "SFTP-4.176.28.100", "service": "SSH", "name": "TO-SFTP"}, {"rule": "69", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "MS-K8S-***********/24", "dst-addr": "G3TOHERAPROXY-F5-4.190.160.3", "service": "TCP-28083", "name": "G3_JianCe2.0_01"}, {"rule": "71", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "***********/24, 4.98.1.18-25", "dst-addr": "W5RRMS<PERSON><PERSON><PERSON>, CSLRMSFSP01", "service": "SSH"}, {"rule": "72", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "NAT-4.98.130.151-154, NAT-4.98.130.155-156, NAT-4.98.130.199, 4.98.1.18-25", "dst-addr": "W5RRMSFSC01-F5", "service": "SSH"}, {"rule": "73", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "CSLCOPCC-198.1.1.0, BOCC&4A", "dst-addr": "IRM-F5-4.190.4.2", "service": "TCP-8443"}, {"rule": "76", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "W5RBOCC, YZBOCC", "dst-addr": "*********/16", "service": "SSH, HTTP, HTTPS, TCP-8443"}, {"rule": "77", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "Ansbile-4.190.120.12", "dst-addr": "*********/16", "service": "SSH"}, {"rule": "78", "action": "deny", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "YZBOCC", "dst-addr": "Any", "service": "TCP_3555, TCP_3558, SSH, TCP-31306, TCP_3389"}, {"rule": "79", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "YZBOCC_4.128.10.3", "dst-addr": "*********/32, G3_Mail_*********/32", "service": "TCP-25"}, {"rule": "80", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3TSPAPP01-4.190.122.201, G3TSPAPP02-4.190.122.202", "dst-addr": "G3_*********/16", "service": "SSH, <PERSON><PERSON><PERSON>, TCP_8080, UDP-8472"}, {"rule": "81", "action": "permit", "src-zone": "Internet", "dst-zone": "Inside", "src-addr": "4.103.120.41-42, 4.103.19.11-14", "dst-addr": "WCS-F5-***********", "service": "TCP-8088"}, {"rule": "82", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SDAS-F5-4.190.166.0/24", "dst-addr": "USAP-LB-4.103.19.10", "service": "TCP-19080"}, {"rule": "83", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "USAP-4.103.19.11-14", "dst-addr": "SDAS-F5-4.190.166.2", "service": "TCP-8021"}, {"rule": "84", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "K8S-CORE-4.190.84.0/24", "dst-addr": "*********/32, *********/32", "service": "SMTP, TCP-25"}, {"rule": "85", "action": "permit", "src-zone": "DMZ", "dst-zone": "Internet", "src-addr": "*********/32, *********/32", "dst-addr": "Sporttery-Mail-111.205.58.68", "service": "SMTP, TCP-25"}, {"rule": "86", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA11-**********", "dst-addr": "4.35.10.10", "service": "TCP_8080", "name": "SDAS-V22.05.03"}, {"rule": "87", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "G3BISMONTORCOLLECT", "dst-addr": "*********/16", "service": "TCP-31306, TCP_3558, TCP_3555, TCP-3191, TCP-7001, TCP-6370, TCP-10251-10252, TCP-2379, TCP_8080, TCP-4100-4130, TCP-5000-5030, TCP-9100, SSH, HTTP, HTTPS, TCP-7100", "name": "G3BISMONTORCOLLECT_R340"}, {"rule": "88", "action": "permit", "src-zone": "Inside", "dst-zone": "DMZ", "src-addr": "VulnerabilityScan-4.255.240.50", "dst-addr": "*********/16", "service": "Any", "name": "VulnerabilityScan_NetworkDMZZone"}, {"rule": "89", "action": "permit", "src-zone": "Inside", "dst-zone": "Internet", "src-addr": "VulnerabilityScan-4.255.240.50", "dst-addr": "*********/16", "service": "Any", "name": "VulnerabilityScan_NetworkInternetZone"}, {"rule": "91", "action": "permit", "src-zone": "DMZ", "dst-zone": "Inside", "src-addr": "SBSG2BISRVA11-**********, SBSG2BISRVA12-**********, SBSG2BISRVA13-4.190.0.43", "dst-addr": "3.252.235.10/32", "service": "TCP-8443, TCP-6677, TCP-7788, TCP-8001, TCP-8002, TCP-80", "name": "to_EDRagent"}, {"rule": "1", "action": "deny", "log": "session-start", "src-zone": "Any", "dst-zone": "Any", "src-addr": "Any", "dst-addr": "Any", "service": "Any"}]
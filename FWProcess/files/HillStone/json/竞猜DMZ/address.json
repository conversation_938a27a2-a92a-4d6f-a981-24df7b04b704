[{"address_name": "private_network", "ip": [{"type": "ip", "value": "10.0.0.0/8"}, {"type": "ip", "value": "**********/12"}, {"type": "ip", "value": "***********/16"}]}, {"address_name": "V3_MS_*************/32", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "V3_DMZ_*********/24", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "BOCC-********/24", "ip": [{"type": "ip", "value": "********/24"}]}, {"address_name": "BOCC_*********", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "BOCC-*********", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "*********/32", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "*********/32", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "Mail_************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "tiaobanji-**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "DNS_*******", "ip": [{"type": "ip", "value": "*******/32"}]}, {"address_name": "DNS_***************", "ip": [{"type": "ip", "value": "***************/32"}]}, {"address_name": "DNS-***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "DNS-***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "*********/16", "ip": [{"type": "ip", "value": "*********/16"}]}, {"address_name": "AlarManager-************-72", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "MS-***********/24", "ip": [{"type": "ip", "value": "***********/24"}]}, {"address_name": "TCP-***********", "ip": [{"type": "ip", "value": "***********/24"}]}, {"address_name": "Zabbix-Proxy-*********51", "ip": [{"type": "ip", "value": "*********51/32"}]}, {"address_name": "ZabbixServer", "ip": [{"type": "range", "value": "************ ************"}]}, {"address_name": "NTP-**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "NTp-***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "4A-*********/24", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "Solarwinds-*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "Solarwinds-*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "G2-Mail-**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "MS-***********/24", "ip": [{"type": "ip", "value": "***********/24"}]}, {"address_name": "*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "DNS-***********-52", "ip": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "DMZ_NAS_**********/22", "ip": [{"type": "ip", "value": "**********/22"}]}, {"address_name": "NAS_Public_**********/24", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "Solarwinds-**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "G3-Test-***********/32", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "G3-Test-**********/24", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "G3-Test-*********/24", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "***********/24", "ip": [{"type": "ip", "value": "***********/24"}, {"type": "ip", "value": "***********/24"}, {"type": "ip", "value": "***********/24"}, {"type": "ip", "value": "***********/24"}]}, {"address_name": "G2OCS-**********/32", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "BOCC&4A", "ip": [{"type": "ip", "value": "********/24"}, {"type": "ip", "value": "*********/24"}, {"type": "ip", "value": "*********/24"}, {"type": "ip", "value": "*********/24"}, {"type": "ip", "value": "*********/24"}, {"type": "ip", "value": "*********/24"}, {"type": "ip", "value": "*********/24"}, {"type": "ip", "value": "********/24"}, {"type": "ip", "value": "********/24"}, {"type": "ip", "value": "********/24"}]}, {"address_name": "G3_*********/16", "ip": [{"type": "ip", "value": "*********/16"}]}, {"address_name": "G2_Mail_***********/32", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "SSLF5-***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "DMZ-SSL-*********/24", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "WCS-F5-***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "*********", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "CSLC-baoleiji-*********/24", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "4.190.121.1-6", "ip": [{"type": "range", "value": "4.190.121.1 4.190.121.5"}]}, {"address_name": "MS-K8S-4.190.124.0/24", "ip": [{"type": "ip", "value": "4.190.124.0/24"}]}, {"address_name": "4.190.121.211-212", "ip": [{"type": "range", "value": "4.190.121.211 4.190.121.212"}]}, {"address_name": "FOC-18.4.32.2", "ip": [{"type": "ip", "value": "18.4.32.2/32"}]}, {"address_name": "FOC-18.5.225.15", "ip": [{"type": "ip", "value": "18.5.225.15/32"}]}, {"address_name": "G2-TAS-SSL-18.1.17.1", "ip": [{"type": "ip", "value": "18.1.17.1/32"}]}, {"address_name": "OPSTOOL-4.190.121.181-182", "ip": [{"type": "range", "value": "4.190.121.181 4.190.121.182"}]}, {"address_name": "G3_CORE_4.190.83.1", "ip": [{"type": "ip", "value": "4.190.83.1/32"}]}, {"address_name": "G3_CORE_4.190.83.2", "ip": [{"type": "ip", "value": "4.190.83.2/32"}]}, {"address_name": "G3WCSINFOS-*********1-22", "ip": [{"type": "range", "value": "*********1 *********2"}]}, {"address_name": "DNS-180.76.76.76", "ip": [{"type": "ip", "value": "180.76.76.76/32"}]}, {"address_name": "DNS-223.5.5.5", "ip": [{"type": "ip", "value": "223.5.5.5/32"}]}, {"address_name": "GWREDIS-4.190.40.0/24", "ip": [{"type": "ip", "value": "4.190.40.0/24"}]}, {"address_name": "G3WCSINFOSFTP-4.190.40.41-42", "ip": [{"type": "range", "value": "4.190.40.41 4.190.40.42"}]}, {"address_name": "G3WCSINFOSFTP-F5-4.190.161.6", "ip": [{"type": "ip", "value": "4.190.161.6/32"}]}, {"address_name": "G3TOHERAPROXY-*********1-12", "ip": [{"type": "range", "value": "*********1 *********2"}]}, {"address_name": "G3GAIA-F5-4.190.163.20", "ip": [{"type": "ip", "value": "4.190.163.20/32"}]}, {"address_name": "G3MONITORGAIA-4.190.122.161-162", "ip": [{"type": "range", "value": "4.190.122.161 4.190.122.162"}]}, {"address_name": "G3TOHERAPROXY-F5-4.190.160.3", "ip": [{"type": "ip", "value": "4.190.160.3/32"}]}, {"address_name": "G3-MS-K8S-4.190.44.0/24", "ip": [{"type": "ip", "value": "4.190.44.0/24"}]}, {"address_name": "G3MONITORPORTAL-F5-4.190.163.11", "ip": [{"type": "ip", "value": "4.190.163.11/32"}]}, {"address_name": "G3JCFAUTHCASSERVER-F5-4.190.163.13", "ip": [{"type": "ip", "value": "4.190.163.13/32"}]}, {"address_name": "OJobServer-18.0.2.161-162", "ip": [{"type": "range", "value": "18.0.2.161 18.0.2.162"}]}, {"address_name": "OCS-FTP-18.0.2.171", "ip": [{"type": "ip", "value": "18.0.2.171/32"}]}, {"address_name": "ConfigCenter-F5-4.190.162.5", "ip": [{"type": "ip", "value": "4.190.162.5/32"}]}, {"address_name": "ConfigCenter-4.190.80.141-143", "ip": [{"type": "range", "value": "4.190.80.141 4.190.80.143"}]}, {"address_name": "SYSLOG-F5-4.190.163.16", "ip": [{"type": "ip", "value": "4.190.163.16/32"}]}, {"address_name": "G3_MS_AIDB_4.190.122.121-123", "ip": [{"type": "range", "value": "4.190.122.121 4.190.122.123"}]}, {"address_name": "G3_Mail_*********/32", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "G3_CORE_NTP_4.190.80.251-252", "ip": [{"type": "range", "value": "4.190.80.251 4.190.80.252"}]}, {"address_name": "SBSG2BISRVA11-4.190.0.41", "ip": [{"type": "ip", "value": "4.190.0.41/32"}]}, {"address_name": "SBSG2BISRVA12-4.190.0.42", "ip": [{"type": "ip", "value": "4.190.0.42/32"}]}, {"address_name": "SBSG2BISRVA13-4.190.0.43", "ip": [{"type": "ip", "value": "4.190.0.43/32"}]}, {"address_name": "BASDB-4.190.88.43", "ip": [{"type": "ip", "value": "4.190.88.43/32"}]}, {"address_name": "BLIDB-4.190.88.104", "ip": [{"type": "ip", "value": "4.190.88.104/32"}]}, {"address_name": "Ansbile-4.190.120.11", "ip": [{"type": "ip", "value": "4.190.120.11/32"}]}, {"address_name": "SBSIRMBMO-4.190.0.31-32", "ip": [{"type": "range", "value": "4.190.0.31 4.190.0.32"}]}, {"address_name": "SBSG2IRMDBVIP-4.190.88.3", "ip": [{"type": "ip", "value": "4.190.88.3/32"}]}, {"address_name": "IRM-F5-***********6", "ip": [{"type": "ip", "value": "***********6/32"}]}, {"address_name": "SBSG2OPSFTP01-4.190.88.131", "ip": [{"type": "ip", "value": "4.190.88.131/32"}]}, {"address_name": "ItoSchedule-4.190.122.171-172", "ip": [{"type": "range", "value": "4.190.122.171 4.190.122.172"}]}, {"address_name": "SBSG2OTJob-4.190.88.71", "ip": [{"type": "ip", "value": "4.190.88.71/32"}]}, {"address_name": "CSLC-Hive-4.35.10.5/32", "ip": [{"type": "ip", "value": "4.35.10.5/32"}]}, {"address_name": "MS-***********/22", "ip": [{"type": "ip", "value": "***********/22"}]}, {"address_name": "CSLC-KYLIN-198.3.100.53", "ip": [{"type": "ip", "value": "198.3.100.53/32"}]}, {"address_name": "CSLC-PHOENIX-198.3.100.53-57", "ip": [{"type": "range", "value": "198.3.100.53 198.3.100.57"}]}, {"address_name": "RTQDBVIP-4.190.88.33", "ip": [{"type": "ip", "value": "4.190.88.33/32"}]}, {"address_name": "CSLC-SSL4.103.211.0/24", "ip": [{"type": "ip", "value": "4.103.211.0/24"}]}, {"address_name": "SSL-4.190.4.3", "ip": [{"type": "ip", "value": "4.190.4.3/32"}]}, {"address_name": "G2BASDB-18.0.1.75", "ip": [{"type": "ip", "value": "18.0.1.75/32"}]}, {"address_name": "4.190.19.254", "ip": [{"type": "ip", "value": "4.190.19.254/32"}]}, {"address_name": "198.3.100.0/24", "ip": [{"type": "ip", "value": "198.3.100.0/24"}]}, {"address_name": "4.176.12.0", "ip": [{"type": "ip", "value": "4.176.12.0/24"}]}, {"address_name": "G3-CORE-SDAS-4.190.80.191-192", "ip": [{"type": "range", "value": "4.190.80.191 4.190.80.192"}]}, {"address_name": "10************", "ip": [{"type": "ip", "value": "10************/32"}]}, {"address_name": "CSLC-baoleiji-**********/24", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "RMOAS-**********/24", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "FOC-***********-137", "ip": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "QiYeWeiXin-***********/16", "ip": [{"type": "ip", "value": "***********/16"}]}, {"address_name": "QiYeWeiXin-*************/32", "ip": [{"type": "host", "value": "\"*************\""}]}, {"address_name": "SFTP-************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "W5RRMSFSC01", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "NAT-************-154", "ip": [{"type": "ip", "value": "************/32"}, {"type": "ip", "value": "************/32"}, {"type": "ip", "value": "************/32"}, {"type": "ip", "value": "************/32"}]}, {"address_name": "W5RRMSFSC01-F5", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "CSLRMSFSP01", "ip": [{"type": "ip", "value": "********/32"}]}, {"address_name": "CSLCOPCC-*********", "ip": [{"type": "ip", "value": "*********/24"}]}, {"address_name": "IRM-F5-*********", "ip": [{"type": "ip", "value": "*********/32"}]}, {"address_name": "*********", "ip": [{"type": "ip", "value": "*********/16"}]}, {"address_name": "W5RBOCC", "ip": [{"type": "ip", "value": "*********/24"}, {"type": "ip", "value": "*********/24"}]}, {"address_name": "Ansbile-************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "YZBOCC", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "YZBOCC_**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "G3TSPAPP01-*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "G3TSPAPP02-*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "***********-14", "ip": [{"type": "ip", "value": "***********/32"}, {"type": "ip", "value": "***********/32"}, {"type": "ip", "value": "***********/32"}, {"type": "ip", "value": "***********/32"}]}, {"address_name": "************-42", "ip": [{"type": "ip", "value": "************/32"}, {"type": "ip", "value": "************/32"}]}, {"address_name": "USAP-LB-***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "SDAS-F5-***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "SDAS-F5-***********/24", "ip": [{"type": "ip", "value": "***********/24"}]}, {"address_name": "USAP-***********-14", "ip": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "K8S-CORE-**********/24", "ip": [{"type": "ip", "value": "**********/24"}]}, {"address_name": "Sporttery-Mail-*************", "ip": [{"type": "ip", "value": "*************/32"}]}, {"address_name": "NAT-************-156", "ip": [{"type": "ip", "value": "************/32"}, {"type": "ip", "value": "************/32"}]}, {"address_name": "NAT-************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "**********", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "G3BISMONTORCOLLECT", "ip": [{"type": "range", "value": "********** **********"}]}, {"address_name": "VulnerabilityScan-************", "ip": [{"type": "ip", "value": "************/32"}]}, {"address_name": "*********-25", "ip": [{"type": "range", "value": "********* *********"}]}, {"address_name": "**********/32", "ip": [{"type": "ip", "value": "**********/32"}]}, {"address_name": "***********", "ip": [{"type": "ip", "value": "***********/32"}]}, {"address_name": "YJBOCC_*********", "ip": [{"type": "ip", "value": "*********/24"}]}]
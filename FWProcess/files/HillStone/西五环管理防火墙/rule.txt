
rule id 56
  action deny
  disable
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "OCS地址段"
  dst-addr "4.10.1.81-82"
  service "tcp-3389"
  name "OCS禁止访问PKI跳板机"
exit
rule id 68
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "4.255.10.101-4.255.10.117"
  src-addr "************-132"
  src-addr "4.255.10.10-12"
  dst-addr "3.255.240.0"
  service "ICMP"
  service "HTTP"
  service "HTTPS"
  name "OCS_TO_NEW-WAF"
exit
rule id 61
  action deny
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "**********"
  dst-addr "4.13.10.41"
  service "TCP-6379"
  service "TCP-26379"
  name "OCS_To_redis_Deny"
exit
rule id 57
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "************-132"
  src-addr "4.255.10.151"
  dst-addr "Security_Monitoring_Service"
  service "TCP-443"
  service "TCP-80"
  service "tcp-8443"
  service "tcp-8834"
  service "TCP-9991"
  service "TCP-5601"
  service "TCP-1400"
  service "TCP-14001"
  service "TCP-8888"
  service "SSH"
  service "TCP-18081"
  service "TCP-15601"
  service "TCP-18088"
  service "tcp-8081"
  service "tcp-6110"
  service "tcp-81"
  service "TCP-6677"
  service "TCP-7788"
  service "TCP-8001"
  service "TCP-8002"
  service "TCP-8090"
  service "TCP-8400"
  service "UDP-514"
  name "Security_Monitoring_Service-Permit"
exit
rule id 64
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-ip ************/32
  dst-range ************* *************
  service "TCP_1443"
  service "TCP-6379"
  service "TCP_30024-30029"
  name "Security_Monitoring_Check01"
exit
rule id 65
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-ip *************/32
  dst-ip ************/32
  service "TCP_30024-30029"
  name "Security_Monitoring_Check02"
exit
rule id 59
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "************-132"
  dst-addr "************-52"
  dst-addr "************"
  service "tcp-3000"
  service "tcp-3443"
  name "Security_Monitoring_Service-Permit-1"
exit
rule id 58
  action deny
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "************-132"
  dst-addr "Any"
  service "Any"
  name "Security_Monitoring_Service-Deny"
exit
rule id 55
  action deny
  src-zone "Any"
  dst-zone "YGPT _trust"
  src-addr "Any"
  dst-addr "***********/24"
  service "389"
  name "禁止389"
exit
rule id 42
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***********/24"
  dst-addr "*******/8"
  service "ICMP"
  service "UDP-161"
  service "TCP-443"
  service "tcp-22"
  service "TCP-8999"
  service "tcp-3389"
  name "SOC系统访问区域内主机设备"
exit
rule id 51
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "3.109.50.120-3.109.50.126"
  dst-addr "Any"
  service "tcp-3389"
  service "tcp-139"
  service "TELNET"
  service "SSH"
  name "SOC"
exit      
rule id 43
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "***********/24"
  service "TCP-10051"
  name "访问zabbix"
exit
rule id 44
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***********/24"
  dst-addr "Any"
  service "TCP-10050"
  service "tcp-8080"
  name "zabbix访问所有"
exit
rule id 7
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "二中心安全运维系统"
  dst-addr "Any"
  service "Any"
  name "二中心安全运维系统"
exit
rule id 52
  action permit
  disable
  src-zone "Any"
  dst-zone "Any"
  src-ip *********/24
  dst-addr "Any"
  service "Any"
  name "citrix访问"
exit
rule id 38
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "************"
  src-addr "*************"
  src-addr "*************"
  src-addr "*************"
  src-ip ************/32
  dst-addr "Any"
  service "Any"
  name "漏洞扫描"
exit
rule id 1
  action permit
  src-zone "YGPT _trust"
  dst-zone "YGPT _untrust"
  src-addr "***********/24"
  dst-addr "Any"
  service "Any"
  name "云管平台trust-untrust"
exit
rule id 2
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "*********/16"
  dst-addr "************0-113"
  dst-addr "*************"
  dst-addr "*************-122"
  service "TCP-443"
  name "骏彩G3访问云管平台"
exit
rule id 3 
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "骏彩三地BOCC"
  dst-addr "************"
  dst-addr "************"
  dst-addr "************0"
  service "TCP-443"
  name "骏彩三地BOCC访问云管平台-1"
exit
rule id 4
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "骏彩三地BOCC"
  dst-addr "*************"
  service "TCP-80"
  name "骏彩三地BOCC访问云管平台-2"
exit
rule id 5
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "*********/16"
  dst-addr "4.100.220.155"
  service "UDP-40002"
  service "TCP-40002"
  service "UDP-40001"
  service "TCP-40001"
  service "UDP-2049"
  service "TCP-2049"
  service "UDP-111"
  service "TCP-111"
  name "骏彩G3模拟运营的虚拟机访问云管平台NFS服务器"
exit
rule id 6
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "Any"
  dst-addr "***********/24"
  service "Any"
  name "云平台untrust-trust"
exit
rule id 22
  action permit
  disable
  src-zone "ECC-untrust"
  dst-zone "ECC-trust"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "ECC-untrust-trust"
exit
rule id 8
  action deny
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "主中心域控"
  service "tcp-3389"
  name "ECC主机拒绝访问域控3389端口"
exit
rule id 9
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "********-2"
  dst-addr "*********/32"
  dst-addr "*********/32"
  dst-range ******** ********
  service "Any"
  name "ECC访问主中心域控-1"
exit
rule id 10
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ********/32
  service "445"
  service "tcp-110"
  service "tcp-25"
  name "ECC访问主中心域控-2"
exit
rule id 11
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip *********/32
  service "tcp-1688"
  name "ECC访问主中心域控-3"
exit
rule id 12
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ********0/32
  dst-ip *********/32
  service "TCP-80"
  service "tcp-1433"
  service "tcp-8801"
  service "tcp-8444"
  service "tcp-8443"
  service "tcp-8082"
  service "tcp-8081"
  service "tcp-8080"
  service "TCP-443"
  name "ECC访问主中心域控-4"
exit
rule id 13
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ********1/32
  service "tcp-8530"
  service "TCP-80"
  name "ECC访问主中心域控-5"
exit
rule id 14
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-range ********** **********
  service "TCP-80"
  name "ECC访问Citrix-1"
exit
rule id 15
  action permit
  disable
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ***********/28
  dst-ip ***********/32
  dst-range *********** ***********
  dst-range *********** ***********
  dst-range *********** ***********
  dst-range *********** ***********
  service "tcp-2598"
  service "tcp-1494"
  description "8.26"
  name "ECC访问Citrix-2"
exit
rule id 16
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ***********/32
  dst-ip ***********/32
  dst-range *********** ***********
  service "tcp-8008"
  service "TCP-80"
  name "ECC访问Citrix-3"
exit
rule id 67
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "ECC终端机"
  dst-range ************ ************
  service "TCP-443"
  service "TCP-80"
  service "tcp-8443"
  service "TCP-8888"
  name "ECC-mipingSSL"
exit
rule id 17
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-range ********** *********2
  service "tcp-1935"
  name "ECC访问主中心域控-6"
exit
rule id 18
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "*********-204"
  dst-addr "*********-214"
  dst-ip *********/32
  service "ICMP"
  service "tcp-445"
  service "tcp-3269"
  service "tcp-3268"
  service "464"
  service "88"
  service "tcp-139"
  service "tcp-135"
  service "udp-138"
  service "137"
  service "389"
  name "ECC访问主中心域控-7"
exit
rule id 19
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "***********1"
  dst-addr "************"
  dst-addr "************"
  dst-ip **********/32
  dst-ip ************/32
  dst-ip ************/32
  dst-ip **********/32
  dst-ip ***********/32
  dst-ip ***********/32
  service "tcp-8080"
  name "ECC大屏工作站8080"
exit
rule id 20
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip *************/32
  dst-ip ***********/32
  service "TCP-80"
  name "ECC大屏工作站80"
exit
rule id 21
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ***********/24
  service "udp- 8010"
  service "tcp-8003"
  service "tcp-8000"
  name "ECC工作站访问BQQ"
exit
rule id 23
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip *************/32
  service "tcp-6066"
  name "ECC大屏工作站6066"
exit
rule id 24
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "OCS代理机"
  dst-range ********** **********
  service "TCP-443"
  service "tcp-10102"
  name "ECC访问安全运维系统代理机"
exit
rule id 25
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "发布机"
  dst-addr "OCS域控"
  dst-range **********1 **********0
  dst-range ********** **********
  service "tcp-3389"
  service "389"
  name "ECC访问安全运维系统发布机和域控"
exit
rule id 26
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-range *********** ***********
  dst-ip **********/24
  service "Any"
  name "ECC访问呼叫中心"
exit
rule id 28
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "***********"
  service "TCP-1883"
  name "ECC"
exit
rule id 29
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "***********"
  service "udp- 8010"
  service "tcp-8003"
  service "tcp-8000"
  name "ECC大屏工作站BQQ"
exit
rule id 31
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC印务终端"
  dst-addr "主中心印务citrix"
  service "tcp-1494"
  service "tcp-2598"
  name "ECC访问主中心印务citrix"
exit
rule id 32
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "***********"
  dst-addr "***********"
  service "tcp-3389"
  name "ECC访问主中心ECC大屏"
exit
rule id 34
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "ECC终端机"
  dst-addr "OCS代理机"
  service "tcp-10102"
  service "TCP-443"
  name "操作终端访问OCS"
exit
rule id 35
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "ECC终端机"
  src-addr "二中心安全运维系统"
  dst-addr "发布机"
  dst-addr "OCS域控"
  service "389"
  service "tcp-3389"
  name "操作终端访问发布机和域控"
exit
rule id 36
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "OCS地址段"
  dst-addr "Any"
  service "Any"
  name "OCS出向访问"
exit
rule id 37
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "Any"
  dst-addr "OCS地址段"
  service "ICMP"
  name "OCS-ICMP"
exit
rule id 39
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "18.2.1.0_18.2.12.0_18.2.22.0"
  src-addr "4.128.2.0/24"
  src-addr "4.128.1.0/24"
  src-addr "4.128.0.0/24"
  src-addr "4.128.10.0/24"
  dst-addr "4.255.10.10-12"
  service "tcp-10102"
  service "TCP-443"
  name "骏彩OPCC访问OCS代理机"
exit
rule id 40
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "18.2.1.0_18.2.12.0_18.2.22.0"
  src-addr "4.128.2.0/24"
  src-addr "4.128.1.0/24"
  src-addr "4.128.0.0/24"
  dst-addr "4.255.10.151-160"
  dst-addr "OCS地址段"
  service "389"
  service "tcp-3389"
  name "骏彩OPCC访问OCS发布机"
exit
rule id 41
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "18.2.1.0_18.2.12.0_18.2.22.0"
  src-addr "4.128.2.0/24"
  src-addr "4.128.1.0/24"
  src-addr "4.128.0.0/24"
  dst-addr "4.255.10.21-22"
  service "389"
  service "tcp-3389"
  name "骏彩OPCC访问OCS域控"
exit
rule id 45
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "4.101.91.21"
  src-addr "4.101.91.31"
  src-addr "4.101.91.11"
  dst-addr "OCS域控"
  service "udp-49152-65535"
  service "tcp- 49152-65535"
  service "tcp-139"
  service "137"
  service "udp-2535"
  service "udp-67"
  service "tcp-9389"
  service "udp-138"
  service "464"
  service "tcp-5722"
  service "tcp-135"
  service "tcp-25"
  service "445"
  service "88"
  service "tcp-3269"
  service "tcp-3268"
  service "tcp-636"
  service "udp-389"
  service "389"
  service "DNS"
  name "云盘访问CIMS系统域控"
exit
rule id 46
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "运维平台"
  service "TCP-80"
  service "tcp-8080"
  name "二中心ECC访问运维平台"
exit
rule id 27
  action permit
  src-zone "YY_OCS-untrust"
  dst-zone "YY_OCS-trust"
  src-addr "3.30.16.1-3"
  src-addr "198.1.10.1-3"
  src-addr "3.15.50.201-203"
  dst-addr "***********"
  service "TCP-443"
  service "tcp-10102"
  name "亦庄5层&翌景B1访问运营代理机-1"
exit      
rule id 30
  action permit
  src-zone "YY_OCS-untrust"
  dst-zone "YY_OCS-trust"
  src-addr "3.30.16.1-3"
  src-addr "198.1.10.1-3"
  src-addr "3.15.50.201-203"
  dst-addr "************"
  service "TCP-389"
  service "tcp-3389"
  name "亦庄5层&翌景B1访问运营代理机-2"
exit
rule id 33
  action permit
  src-zone "YY_OCS-trust"
  dst-zone "YY_OCS-untrust"
  src-addr "************"
  src-addr "***********"
  dst-addr "4.101.15.21-22"
  dst-addr "4.101.15.11-12"
  dst-addr "4.9.11.11-12"
  dst-addr "4.9.11.61-62"
  dst-addr "4.9.11.41-42"
  service "SSH"
  service "tcp-6110"
  service "tcp-81"
  service "HTTP"
  name "运营代理机访问即开设奖资源服务器-1"
exit
rule id 47
  action permit
  src-zone "YY_OCS-trust"
  dst-zone "YY_OCS-untrust"
  src-addr "************"
  src-addr "***********"
  dst-addr "*********-22"
  service "tcp-3389"
  name "运营代理机访问即开设奖资源服务器-2"
exit
rule id 48
  action permit
  src-zone "YY_OCS-trust"
  dst-zone "YY_OCS-untrust"
  src-addr "************"
  src-addr "***********"
  dst-addr "*********-32"
  service "SSH"
  service "tcp-8088"
  name "运营代理机访问即开设奖资源服务器-3"
exit
rule id 49
  action permit
  disable
  src-zone "CODING-trust"
  dst-zone "CODING-untrust"
  src-ip ***********/24
  dst-addr "Any"
  service "Any"
  description "8.26"
  name "CODING出向访问策略"
exit
rule id 50
  action permit
  src-zone "CODING-untrust"
  dst-zone "CODING-trust"
  src-range *********** ***********
  dst-ip ************/32
  service "TCP-80"
  name "研发测试环境制品库数据同步"
exit
rule id 53
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "*********"
  service "TCP-443"
  service "tcp-8080"
  service "TCP-80"
  name "ECC访问数据中心新mcafee主机"
exit
rule id 54
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ************/32
  dst-ip ***********/32
  service "tcp-16320-16323"
  service "tcp-16310-16316"
  name "ECC访问运维平台"
exit
rule id 60
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "cmdbnginx"
  src-addr "***********"
  src-addr "************"
  src-addr "*************-106"
  src-range *********** ***********
  dst-addr "cmdb"
  service "TCP-8880"
  service "tcp-8080"
  name "nginx_to_cmdb"
exit
rule id 62
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "*********/32"
  dst-addr "**********"
  service "tcp-22"
  name "ZiDongHua_To_OCS"
exit
rule id 63
  action permit
  src-addr "***********/24"
  dst-addr "*********"
  service "ICMP"
  name "zabbix-to-mifu"
exit
rule id 66
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "**********"
  dst-addr "*************-203"
  dst-addr "**********/24"
  service "tcp-8080"
  service "tcp-25"
  service "tcp-465"
  service "tcp-22"
  service "SSH"
  service "PING"
  name "ocs-to-mail.pt.prod"
exit
XWMFW02(M)# show config

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "YGPT _vr"
exit
ip vrouter "ECC_vr"
exit
ip vrouter "OCS_vr"
exit
ip vrouter "YY_OCS_vrf"
exit
ip vrouter "CODING_vrf"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit      
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
zone "YGPT _trust"
exit
zone "YGPT _untrust"
exit
zone "ECC-untrust"
exit      
zone "ECC-trust"
exit
zone "OCS-trust"
exit
zone "OCS-untrust"
exit
zone "YY_OCS-trust"
exit
zone "YY_OCS-untrust"
exit
zone "CODING-trust"
exit
zone "CODING-untrust"
exit
interface vswitchif1
exit
interface MGT0 local
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit      
interface ethernet0/2
exit
interface ethernet0/3
exit
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit      
interface aggregate1
exit
interface aggregate1.151
exit
interface aggregate1.152
exit
interface aggregate1.153
exit
interface aggregate1.154
exit
interface aggregate1.155
exit
interface aggregate1.156
exit
interface aggregate1.157
exit
interface aggregate1.158
exit
interface aggregate1.159
exit
interface aggregate1.160
exit
address "private_network"
exit      
address "***********/24"
exit
address "*********/16"
exit
address "*************-113"
exit
address "*************"
exit
address "*************-122"
exit
address "骏彩三地BOCC"
exit
address "************"
exit
address "************"
exit
address "*************"
exit
address "*************"
exit
address "*************"
exit
address "二中心安全运维系统"
exit      
address "ECC"
exit
address "主中心域控"
exit
address "***********"
exit
address "************"
exit
address "************"
exit
address "************"
exit
address "***********"
exit
address "**********"
exit
address "ECC印务终端"
exit
address "主中心印务citrix"
exit
address "***********"
exit
address "***********"
exit      
address "***********"
exit
address "ECC终端机"
exit
address "OCS代理机"
exit
address "发布机"
exit
address "OCS域控"
exit
address "OCS地址段"
exit
address "************"
exit
address "********_*********_*********"
exit
address "***********-12"
exit
address "************-160"
exit
address "***********-22"
exit
address "**********/24"
exit      
address "*******/8"
exit
address "***********/24"
exit
address "***********/24"
exit
address "*********-204"
exit
address "*********-214"
exit
address "*************"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "运维平台"
exit
address "********-2"
exit
address "***********"
exit      
address "***********"
exit
address "*********-3"
exit
address "**********-3"
exit
address "***********-22"
exit
address "***********-12"
exit
address "*********-12"
exit
address "*********-22"
exit
address "*********-32"
exit
address "*********-62"
exit
address "***********"
exit
address "************"
exit
address "************-************"
exit      
address "***********-12"
exit
address "*********"
exit
address "*********/24"
exit
address "*********/24"
exit
address "*********/24"
exit
address "*********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "***********"
exit
address "**********"
exit
address "*********/32"
exit      
address "*********/32"
exit
address "*************"
exit
address "*************"
exit
address "**********/24"
exit
address "*********-42"
exit
address "*********-82"
exit
address "************-132"
exit
address "Security_Monitoring_Service"
exit
address "************-52"
exit
address "************"
exit
address "cmdb"
exit
address "cmdbnginx"
exit      
address "***********-203"
exit
address "***********"
exit
address "************"
exit
address "**********"
exit
address "**********"
exit
address "*********/32"
exit
address "*************-106"
exit
address "************"
exit
address "*********"
exit
address "*************-203"
exit
address "**********/24"
exit
address "************-************"
exit      
address "***********"
exit
address "***********"
exit
aaa-server "local" type local
exit
track "track-ha"
exit
service "TCP-443"
  tcp dst-port 443 
exit
service "TCP-80"
  tcp dst-port 80 
exit
service "TCP-111"
  tcp dst-port 111 
exit
service "UDP-111"
  udp dst-port 111 
exit
service "TCP-2049"
  tcp dst-port 2049 
exit
service "UDP-2049"
  udp dst-port 2049 
exit
service "TCP-40001"
  tcp dst-port 40001 
exit
service "UDP-40001"
  udp dst-port 40001 
exit
service "TCP-40002"
  tcp dst-port 40002 
exit
service "UDP-40002"
  udp dst-port 40002 
exit
service "tcp-3389"
  tcp dst-port 3389 
exit
service "445"
  tcp dst-port 445 
  udp dst-port 445 
exit
service "tcp-25"
  tcp dst-port 25 
exit      
service "tcp-110"
  tcp dst-port 110 
exit
service "tcp-1688"
  tcp dst-port 1688 
exit
service "tcp-8080"
  tcp dst-port 8080 
exit
service "tcp-8081"
  tcp dst-port 8081 
exit
service "tcp-8082"
  tcp dst-port 8082 
exit
service "tcp-8443"
  tcp dst-port 8443 
exit
service "tcp-8444"
  tcp dst-port 8444 
exit
service "tcp-8801"
  tcp dst-port 8801 
exit      
service "tcp-1433"
  tcp dst-port 1433 
exit
service "tcp-8530"
  tcp dst-port 8530 
exit
service "tcp-1494"
  tcp dst-port 1494 
exit
service "tcp-2598"
  tcp dst-port 2598 
exit
service "tcp-8008"
  tcp dst-port 8008 
exit
service "tcp-1935"
  tcp dst-port 1935 
exit
service "389"
  tcp dst-port 389 
  udp dst-port 389 
exit
service "137"
  tcp dst-port 137 
  udp dst-port 137 
exit
service "udp-138"
  udp dst-port 138 
exit
service "tcp-135"
  tcp dst-port 135 
exit
service "tcp-139"
  tcp dst-port 139 
exit
service "88"
  tcp dst-port 88 
  udp dst-port 88 
exit
service "464"
  tcp dst-port 464 
  udp dst-port 464 
exit
service "tcp-3268"
  tcp dst-port 3268 
exit
service "tcp-3269"
  tcp dst-port 3269 
exit
service "tcp-445"
  tcp dst-port 445 
exit
service "tcp-8000"
  tcp dst-port 8000 
exit
service "tcp-8003"
  tcp dst-port 8003 
exit
service "udp- 8010"
  udp dst-port 8010 
exit
service "tcp-6066"
  tcp dst-port 6066 
exit
service "TCP-1883"
  tcp dst-port 1883 
exit
service "tcp-9526"
  tcp dst-port 9526 
exit
service "tcp-10102"
  tcp dst-port 10102 
exit
service "tcp-22"
  tcp dst-port 22 
exit
service "UDP-161"
  udp dst-port 161 
exit
service "TCP-8999"
  tcp dst-port 8999 
exit
service "TCP-10051"
  tcp dst-port 10051 
exit
service "TCP-10050"
  tcp dst-port 10050 
exit
service "udp-389"
  udp dst-port 389 
exit
service "tcp-636"
  tcp dst-port 636 
exit
service "tcp-5722"
  tcp dst-port 5722 
exit
service "tcp-9389"
  tcp dst-port 9389 
exit
service "udp-67"
  udp dst-port 67 
exit
service "udp-2535"
  udp dst-port 2535 
exit
service "tcp- 49152-65535"
  tcp dst-port 49152 65535 
exit
service "udp-49152-65535"
  udp dst-port 49152 65535 
exit
service "tcp-8088"
  tcp dst-port 8088 
exit
service "TCP-389"
  tcp dst-port 389 
exit
service "tcp-16310-16316"
  tcp dst-port 16310 16316 
exit
service "tcp-16320-16323"
  tcp dst-port 16320 16323 
exit
service "UDP 1812"
  udp dst-port 1812 
exit
service "tcp-81"
  tcp dst-port 81 
exit
service "tcp-6110"
  tcp dst-port 6110 
exit
service "tcp-8834"
  tcp dst-port 8834 
exit
service "tcp-3000"
  tcp dst-port 3000 
exit
service "tcp-3443"
  tcp dst-port 3443 
exit
service "TCP-9991"
  tcp dst-port 9991 
exit
service "TCP-5601"
  tcp dst-port 5601 
exit
service "TCP-1400"
  tcp dst-port 1400 
exit
service "TCP-14001"
  tcp dst-port 14001 
exit
service "TCP-8880"
  tcp dst-port 8880 
exit
service "TCP-8888"
  tcp dst-port 8888 
exit
service "TCP-6379"
  tcp dst-port 6379 
exit
service "TCP-26379"
  tcp dst-port 26379 
exit
service "TCP-18081"
  tcp dst-port 18081 
exit
service "TCP-15601"
  tcp dst-port 15601 
exit
service "TCP-18088"
  tcp dst-port 18088 
exit
service "TCP-6677"
  tcp dst-port 6677 
exit
service "TCP-7788"
  tcp dst-port 7788 
exit
service "TCP-8001"
  tcp dst-port 8001 
exit
service "TCP-8002"
  tcp dst-port 8002 
exit
service "TCP-8090"
  tcp dst-port 8090 
exit
service "TCP-8400"
  tcp dst-port 8400 
exit
service "UDP-514"
  udp dst-port 514 
exit
service "TCP_1443"
  tcp dst-port 1443 
exit
service "TCP_30024-30029"
  tcp dst-port 30024 30029 
exit
service "tcp-465"
  tcp dst-port 465 
exit
ips sigset "dns" template dns
  max-scan-bytes 30720
exit
ips sigset "ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit      
ips sigset "pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "imap" template imap
  max-scan-bytes 30720
exit
ips sigset "finger" template finger
  max-scan-bytes 30720
exit
ips sigset "sunrpc" template sunrpc
  max-scan-bytes 30720
exit      
ips sigset "nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "netbios" template netbios
  max-scan-bytes 30720
exit      
ips sigset "dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "voip" template voip
  max-scan-bytes 30720
exit
ips sigset "default_dns" template dns
  max-scan-bytes 30720
exit
ips sigset "default_ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "default_http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "default_pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "default_smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "default_telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "default_other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "default_other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "default_imap" template imap
  max-scan-bytes 30720
exit
ips sigset "default_finger" template finger
  max-scan-bytes 30720
exit
ips sigset "default_sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "default_nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "default_tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "default_snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "default_mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "default_mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "default_oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "default_msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "default_netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "default_dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "default_ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "default_voip" template voip
  max-scan-bytes 30720
exit
ips profile "no-ips"
exit
ips profile "predef_default"
  sigset "default_dns"
  sigset "default_ftp"
  sigset "default_http"
  sigset "default_pop3"
  sigset "default_smtp"
  sigset "default_telnet"
  sigset "default_other-tcp"
  sigset "default_other-udp"
  sigset "default_imap"
  sigset "default_finger"
  sigset "default_sunrpc"
  sigset "default_nntp"
  sigset "default_tftp"
  sigset "default_snmp"
  sigset "default_mysql"
  sigset "default_mssql"
  sigset "default_oracle"
  sigset "default_msrpc"
  sigset "default_netbios"
  sigset "default_dhcp"
  sigset "default_ldap"
  sigset "default_voip"
  filter-class 1 
    severity "Low" 
    severity "Medium" 
    severity "High" 
    action reset
  exit
exit
url-category "custom1"
exit
url-category "custom2"
exit
url-category "custom3"
exit
contentfilter
exit
sandbox-profile "predef_low"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_middle"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_high"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  file-type swf
  file-type rar
  file-type zip
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
sandbox-profile "predef_pe"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
url-profile "no-url"
exit
track "track-ha"
  interface aggregate1 
exit
admin user "hillstone"
  password /QhR5o9rPYsH83yQGFSqo1GQSL
        password-expiration 12345678
  role "admin"
  access console
  access telnet
  access ssh
  access http
  access https
exit
admin user "admin"
  password 3rb86yDNMHpXd0NEmKRUcGSQKV
        password-expiration 1631069002
  role "admin"
  access console
  access ssh
  access https
exit
admin user "cslcnet"
  password AQ4nCHSFjUEsg5rklaPJveCQ6a
        password-expiration 1578973061
  access ssh
  access https
exit
logging event to syslog severity warnings
logging threat to buffer severity informational
logging threat to syslog custom-format  severity warnings
logging syslog ************ vrouter "trust-vr" udp 514 type event
logging syslog 4.255.235.1 vrouter "trust-vr" udp 514 type event
logging syslog *********** vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type config
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type network
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type nbc
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type threat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic session
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic nat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic pbr
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type sandbox
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog ************ vrouter "mgt-vr" udp 514 type config
logging syslog ************ vrouter "mgt-vr" udp 514 type network
logging syslog ************ vrouter "mgt-vr" udp 514 type threat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic session
logging syslog additional-information
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit      
address "***********/24"
  ip ***********/24
exit
address "*********/16"
  ip *********/16
exit
address "*************-113"
  range ************* ************3
exit
address "*************"
  ip *************/32
exit
address "*************-122"
  range ************* *************
exit
address "骏彩三地BOCC"
  ip ********/24
  ip *********/24
  ip *********/24
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "*************"
  ip *************/32
exit
address "*************"
  ip *************/32
exit
address "*************"
  ip *************/32
exit
address "二中心安全运维系统"
  range ********** **********
  range **********1 ***********
  range *********** ***********
  range ************ ***********0
  range ************ ************
exit
address "ECC"
  ip *********/24
exit
address "主中心域控"
  ip ********/32
  ip ********/32
exit
address "***********"
  ip ***********/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "**********"
  ip **********/32
exit
address "ECC印务终端"
  ip *********/32
  ip *********/32
exit
address "主中心印务citrix"
  range *********** ***********
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "ECC终端机"
  ip *********/24
  ip *********/24
  ip *********/24
  ip ***********/32
  range *********** ***********
exit
address "OCS代理机"
  range *********** ***********
exit
address "发布机"
  range ************ ***********0
  range ************ ************
  range ************ ************
exit
address "OCS域控"
  range *********** ***********
exit
address "OCS地址段"
  ip **********/24
exit
address "************"
  ip ************/32
exit
address "********_*********_*********"
  ip ********/24
  ip *********/24
  ip *********/24
exit
address "***********-12"
  range *********** ***********
exit
address "************-160"
  range ************ ************
exit
address "***********-22"
  range *********** ***********
exit
address "**********/24"
  ip **********/24
exit
address "*******/8"
  ip *******/8
exit
address "***********/24"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "*********-204"
  range ********* 4.9.1.204
exit
address "*********-214"
  range ********* 4.9.1.214
exit
address "*************"
  ip *************/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "运维平台"
  ip ***********/32
  ip ***********/32
  ip ***********/32
  ip ***********/32
  range *********** ***********
  range *********** ***********
  range ************ ************
  range *********** ***********
  range ************ ************
exit
address "********-2"
  range ******** ********
exit
address "***********"
  ip ***********/32
exit      
address "***********"
  ip ***********/32
exit
address "*********-3"
  range ********* *********
exit
address "**********-3"
  range ********** **********
exit
address "***********-22"
  range *********** ***********
exit
address "***********-12"
  range *********** ***********
exit
address "*********-12"
  range ********* *********
exit
address "*********-22"
  range ********* *********
exit
address "*********-32"
  range ********* *********
exit      
address "*********-62"
  range ********* *********
exit
address "***********"
  ip ***********/32
exit
address "************"
  ip ************/32
exit
address "************-************"
  range ************ ************
exit
address "***********-12"
  range *********** ***********
exit
address "*********"
  ip *********/32
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit      
address "*********/24"
  ip *********/24
exit
address "*********"
  ip *********/24
exit
address "**********"
  ip **********/21
exit
address "**********"
  ip **********/21
exit
address "**********"
  ip **********/21
exit
address "***********"
  ip ***********/21
exit
address "**********"
  ip **********/24
exit
address "*********/32"
  ip *********/32
exit      
address "*********/32"
  ip *********/32
exit
address "*************"
  ip *************/32
exit
address "*************"
  ip *************/32
exit
address "**********/24"
  ip **********/24
exit
address "*********-42"
  range ********* *********
exit
address "*********-82"
  range ********* *********
exit
address "************-132"
  ip ************/32
  ip ************/32
exit
address "Security_Monitoring_Service"
  ip ************/32
  ip *************/32
  ip *************/32
  ip 4.103.14.11/32
  ip 4.255.201.33/32
  ip 4.255.201.31/32
  ip 4.255.201.32/32
  ip ************/32
  ip ************/32
  ip *************/32
  ip ************/32
  ip *********/32
  ip 3.9.20.36/32
  ip ************/32
  ip 4.255.240.88/32
  ip 4.255.240.92/32
  ip 4.255.240.97/32
  ip 4.255.240.121/32
  ip 4.255.201.37/32
  ip 4.255.240.93/32
  ip 10.193.41.103/32
  ip 4.255.240.122/32
  ip 4.255.235.250/32
  ip 4.255.240.65/32
  ip 3.252.235.0/24
  ip 4.103.14.31/32
  ip 4.103.150.61/32
exit
address "************-52"
  ip ************/32
  ip 4.255.240.52/32
exit
address "************"
  ip ************/32
exit
address "cmdb"
  range 4.255.10.31 4.255.10.34
exit
address "cmdbnginx"
  ip 3.101.5.41/32
exit
address "***********-203"
  range *********** 3.15.50.203
exit
address "***********"
  ip ***********/24
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/24
exit
address "**********"
  ip **********/32
exit
address "*********/32"
  ip *********/32
exit
address "*************-106"
  range ************* 3.252.235.106
exit
address "************"
  ip ************/32
exit
address "*********"
  ip *********/24
exit
address "*************-203"
  range ************* 3.252.101.203
exit
address "**********/24"
  ip **********/24
exit
address "************-************"
  range ************ ************
exit
address "***********"
  description "NEW-SEC-DEV"
  ip ***********/24
exit
address "***********"
  ip ***********/24
exit
zone "mgt"
  vrouter "mgt-vr"
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
zone "YGPT _trust"
  vrouter "YGPT _vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "YGPT _untrust"
  vrouter "YGPT _vr"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 150000
  ad udp-flood source-threshold 150000
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "ECC-untrust"
  vrouter "ECC_vr"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "ECC-trust"
  vrouter "ECC_vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "OCS-trust"
  vrouter "OCS_vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit      
zone "OCS-untrust"
  vrouter "OCS_vr"
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "YY_OCS-trust"
  vrouter "YY_OCS_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "YY_OCS-untrust"
  vrouter "YY_OCS_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CODING-trust"
  vrouter "CODING_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CODING-untrust"
  vrouter "CODING_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
hostname "XWMFW02"
admin host any any
web same-account-login enable
no https client-auth match
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit
          
isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit

isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit
          
ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit      

interface MGT0 local
  zone  "mgt"
  ip address 4.255.253.66 *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet4/1
  aggregate aggregate1
exit
interface xethernet4/2
  aggregate aggregate1
exit
interface aggregate1
  zone  "l2-trust"
  lacp enable
exit
interface aggregate1.151
  zone  "YGPT _trust"
  ip address *********** ***************
  manage ping
  no reverse-route
exit
interface aggregate1.152
  zone  "YGPT _untrust"
  ip address *********** ***************
  manage ping
  no reverse-route
exit
interface aggregate1.153
  zone  "ECC-trust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.154
  zone  "ECC-untrust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.155
  zone  "OCS-trust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.156
  zone  "OCS-untrust"
  ip address ***********2 ***************
  manage ping
  no reverse-route
exit
interface aggregate1.157
  zone  "YY_OCS-trust"
  ip address ***********6 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
interface aggregate1.158
  zone  "YY_OCS-untrust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
interface aggregate1.159
  zone  "CODING-trust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
interface aggregate1.160
  zone  "CODING-untrust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 *************
exit
ip vrouter "YGPT _vr"
  ip route 0.0.0.0/0 ***********
  ip route ***********/24 ***********
exit
ip vrouter "ECC_vr"
  ip route 0.0.0.0/0 ***********3
  ip route *********/24 ***********
exit
ip vrouter "OCS_vr"
  ip route 0.0.0.0/0 ***********1
  ip route **********/24 ***********7
exit
ip vrouter "YY_OCS_vrf"
  ip route 0.0.0.0/0 ***********9
  ip route **********/24 ***********5
exit
ip vrouter "CODING_vrf"
  ip route 0.0.0.0/0 ************
  ip route ***********/24 ************
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit    
exit
ntp enable
ntp server ******* vrouter mgt-vr
clock zone china
rule id 56
  action deny
  disable
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "OCS地址段"
  dst-addr "*********-82"
  service "tcp-3389"
  name "OCS禁止访问PKI跳板机"
exit
rule id 68
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "************-************"
  src-addr "************-132"
  src-addr "***********-12"
  dst-addr "***********"
  service "ICMP"
  service "HTTP"
  service "HTTPS"
  name "OCS_TO_NEW-WAF"
exit
rule id 61
  action deny
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "**********"
  dst-addr "**********"
  service "TCP-6379"
  service "TCP-26379"
  name "OCS_To_redis_Deny"
exit
rule id 57
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "************-132"
  src-addr "************"
  dst-addr "Security_Monitoring_Service"
  service "TCP-443"
  service "TCP-80"
  service "tcp-8443"
  service "tcp-8834"
  service "TCP-9991"
  service "TCP-5601"
  service "TCP-1400"
  service "TCP-14001"
  service "TCP-8888"
  service "SSH"
  service "TCP-18081"
  service "TCP-15601"
  service "TCP-18088"
  service "tcp-8081"
  service "tcp-6110"
  service "tcp-81"
  service "TCP-6677"
  service "TCP-7788"
  service "TCP-8001"
  service "TCP-8002"
  service "TCP-8090"
  service "TCP-8400"
  service "UDP-514"
  name "Security_Monitoring_Service-Permit"
exit
rule id 64
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-ip ************/32
  dst-range ************* *************
  service "TCP_1443"
  service "TCP-6379"
  service "TCP_30024-30029"
  name "Security_Monitoring_Check01"
exit
rule id 65
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-ip *************/32
  dst-ip ************/32
  service "TCP_30024-30029"
  name "Security_Monitoring_Check02"
exit
rule id 59
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "************-132"
  dst-addr "************-52"
  dst-addr "************"
  service "tcp-3000"
  service "tcp-3443"
  name "Security_Monitoring_Service-Permit-1"
exit
rule id 58
  action deny
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "************-132"
  dst-addr "Any"
  service "Any"
  name "Security_Monitoring_Service-Deny"
exit
rule id 55
  action deny
  src-zone "Any"
  dst-zone "YGPT _trust"
  src-addr "Any"
  dst-addr "***********/24"
  service "389"
  name "禁止389"
exit
rule id 42
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***********/24"
  dst-addr "*******/8"
  service "ICMP"
  service "UDP-161"
  service "TCP-443"
  service "tcp-22"
  service "TCP-8999"
  service "tcp-3389"
  name "SOC系统访问区域内主机设备"
exit
rule id 51
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "************-************"
  dst-addr "Any"
  service "tcp-3389"
  service "tcp-139"
  service "TELNET"
  service "SSH"
  name "SOC"
exit      
rule id 43
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "***********/24"
  service "TCP-10051"
  name "访问zabbix"
exit
rule id 44
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***********/24"
  dst-addr "Any"
  service "TCP-10050"
  service "tcp-8080"
  name "zabbix访问所有"
exit
rule id 7
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "二中心安全运维系统"
  dst-addr "Any"
  service "Any"
  name "二中心安全运维系统"
exit
rule id 52
  action permit
  disable
  src-zone "Any"
  dst-zone "Any"
  src-ip *********/24
  dst-addr "Any"
  service "Any"
  name "citrix访问"
exit
rule id 38
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "************"
  src-addr "*************"
  src-addr "*************"
  src-addr "*************"
  src-ip ************/32
  dst-addr "Any"
  service "Any"
  name "漏洞扫描"
exit
rule id 1
  action permit
  src-zone "YGPT _trust"
  dst-zone "YGPT _untrust"
  src-addr "***********/24"
  dst-addr "Any"
  service "Any"
  name "云管平台trust-untrust"
exit
rule id 2
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "*********/16"
  dst-addr "*************-113"
  dst-addr "*************"
  dst-addr "*************-122"
  service "TCP-443"
  name "骏彩G3访问云管平台"
exit
rule id 3 
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "骏彩三地BOCC"
  dst-addr "************"
  dst-addr "************"
  dst-addr "*************"
  service "TCP-443"
  name "骏彩三地BOCC访问云管平台-1"
exit
rule id 4
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "骏彩三地BOCC"
  dst-addr "*************"
  service "TCP-80"
  name "骏彩三地BOCC访问云管平台-2"
exit
rule id 5
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "*********/16"
  dst-addr "*************"
  service "UDP-40002"
  service "TCP-40002"
  service "UDP-40001"
  service "TCP-40001"
  service "UDP-2049"
  service "TCP-2049"
  service "UDP-111"
  service "TCP-111"
  name "骏彩G3模拟运营的虚拟机访问云管平台NFS服务器"
exit
rule id 6
  action permit
  src-zone "YGPT _untrust"
  dst-zone "YGPT _trust"
  src-addr "Any"
  dst-addr "***********/24"
  service "Any"
  name "云平台untrust-trust"
exit
rule id 22
  action permit
  disable
  src-zone "ECC-untrust"
  dst-zone "ECC-trust"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "ECC-untrust-trust"
exit
rule id 8
  action deny
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "主中心域控"
  service "tcp-3389"
  name "ECC主机拒绝访问域控3389端口"
exit
rule id 9
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "********-2"
  dst-addr "*********/32"
  dst-addr "*********/32"
  dst-range ******** ********
  service "Any"
  name "ECC访问主中心域控-1"
exit
rule id 10
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ********/32
  service "445"
  service "tcp-110"
  service "tcp-25"
  name "ECC访问主中心域控-2"
exit
rule id 11
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ********0/32
  service "tcp-1688"
  name "ECC访问主中心域控-3"
exit
rule id 12
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ********0/32
  dst-ip ********0/32
  service "TCP-80"
  service "tcp-1433"
  service "tcp-8801"
  service "tcp-8444"
  service "tcp-8443"
  service "tcp-8082"
  service "tcp-8081"
  service "tcp-8080"
  service "TCP-443"
  name "ECC访问主中心域控-4"
exit
rule id 13
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ********1/32
  service "tcp-8530"
  service "TCP-80"
  name "ECC访问主中心域控-5"
exit
rule id 14
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-range ********** **********
  service "TCP-80"
  name "ECC访问Citrix-1"
exit
rule id 15
  action permit
  disable
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ***********/28
  dst-ip ***********/32
  dst-range *********** ***********
  dst-range *********** ***********
  dst-range *********** ***********
  dst-range *********** ***********
  service "tcp-2598"
  service "tcp-1494"
  description "8.26"
  name "ECC访问Citrix-2"
exit
rule id 16
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ***********/32
  dst-ip ***********/32
  dst-range *********** ***********
  service "tcp-8008"
  service "TCP-80"
  name "ECC访问Citrix-3"
exit
rule id 67
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "ECC终端机"
  dst-range ************ ************
  service "TCP-443"
  service "TCP-80"
  service "tcp-8443"
  service "TCP-8888"
  name "ECC-mipingSSL"
exit
rule id 17
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-range ********** **********
  service "tcp-1935"
  name "ECC访问主中心域控-6"
exit
rule id 18
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "*********-204"
  dst-addr "*********-214"
  dst-ip *********/32
  service "ICMP"
  service "tcp-445"
  service "tcp-3269"
  service "tcp-3268"
  service "464"
  service "88"
  service "tcp-139"
  service "tcp-135"
  service "udp-138"
  service "137"
  service "389"
  name "ECC访问主中心域控-7"
exit
rule id 19
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "************"
  dst-addr "************"
  dst-addr "************"
  dst-ip **********/32
  dst-ip ************/32
  dst-ip ************/32
  dst-ip **********/32
  dst-ip ***********/32
  dst-ip ***********/32
  service "tcp-8080"
  name "ECC大屏工作站8080"
exit
rule id 20
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip *************/32
  dst-ip ***********/32
  service "TCP-80"
  name "ECC大屏工作站80"
exit
rule id 21
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ***********/24
  service "udp- 8010"
  service "tcp-8003"
  service "tcp-8000"
  name "ECC工作站访问BQQ"
exit
rule id 23
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip *************/32
  service "tcp-6066"
  name "ECC大屏工作站6066"
exit
rule id 24
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "OCS代理机"
  dst-range ********** **********
  service "TCP-443"
  service "tcp-10102"
  name "ECC访问安全运维系统代理机"
exit
rule id 25
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "发布机"
  dst-addr "OCS域控"
  dst-range **********1 ***********
  dst-range ********** **********
  service "tcp-3389"
  service "389"
  name "ECC访问安全运维系统发布机和域控"
exit
rule id 26
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-range **********0 ***********
  dst-ip **********/24
  service "Any"
  name "ECC访问呼叫中心"
exit
rule id 28
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "***********"
  service "TCP-1883"
  name "ECC"
exit
rule id 29
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "***********"
  service "udp- 8010"
  service "tcp-8003"
  service "tcp-8000"
  name "ECC大屏工作站BQQ"
exit
rule id 31
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC印务终端"
  dst-addr "主中心印务citrix"
  service "tcp-1494"
  service "tcp-2598"
  name "ECC访问主中心印务citrix"
exit
rule id 32
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "***********"
  dst-addr "***********"
  service "tcp-3389"
  name "ECC访问主中心ECC大屏"
exit
rule id 34
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "ECC终端机"
  dst-addr "OCS代理机"
  service "tcp-10102"
  service "TCP-443"
  name "操作终端访问OCS"
exit
rule id 35
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "ECC终端机"
  src-addr "二中心安全运维系统"
  dst-addr "发布机"
  dst-addr "OCS域控"
  service "389"
  service "tcp-3389"
  name "操作终端访问发布机和域控"
exit
rule id 36
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "OCS地址段"
  dst-addr "Any"
  service "Any"
  name "OCS出向访问"
exit
rule id 37
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "Any"
  dst-addr "OCS地址段"
  service "ICMP"
  name "OCS-ICMP"
exit
rule id 39
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "********_*********_*********"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "**********/24"
  dst-addr "***********-12"
  service "tcp-10102"
  service "TCP-443"
  name "骏彩OPCC访问OCS代理机"
exit
rule id 40
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "********_*********_*********"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "************-160"
  dst-addr "OCS地址段"
  service "389"
  service "tcp-3389"
  name "骏彩OPCC访问OCS发布机"
exit
rule id 41
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "********_*********_*********"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "***********-22"
  service "389"
  service "tcp-3389"
  name "骏彩OPCC访问OCS域控"
exit
rule id 45
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "***********"
  src-addr "***********"
  src-addr "***********"
  dst-addr "OCS域控"
  service "udp-49152-65535"
  service "tcp- 49152-65535"
  service "tcp-139"
  service "137"
  service "udp-2535"
  service "udp-67"
  service "tcp-9389"
  service "udp-138"
  service "464"
  service "tcp-5722"
  service "tcp-135"
  service "tcp-25"
  service "445"
  service "88"
  service "tcp-3269"
  service "tcp-3268"
  service "tcp-636"
  service "udp-389"
  service "389"
  service "DNS"
  name "云盘访问CIMS系统域控"
exit
rule id 46
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "运维平台"
  service "TCP-80"
  service "tcp-8080"
  name "二中心ECC访问运维平台"
exit
rule id 27
  action permit
  src-zone "YY_OCS-untrust"
  dst-zone "YY_OCS-trust"
  src-addr "*********-3"
  src-addr "**********-3"
  src-addr "***********-203"
  dst-addr "***********"
  service "TCP-443"
  service "tcp-10102"
  name "亦庄5层&翌景B1访问运营代理机-1"
exit      
rule id 30
  action permit
  src-zone "YY_OCS-untrust"
  dst-zone "YY_OCS-trust"
  src-addr "*********-3"
  src-addr "**********-3"
  src-addr "***********-203"
  dst-addr "************"
  service "TCP-389"
  service "tcp-3389"
  name "亦庄5层&翌景B1访问运营代理机-2"
exit
rule id 33
  action permit
  src-zone "YY_OCS-trust"
  dst-zone "YY_OCS-untrust"
  src-addr "************"
  src-addr "***********"
  dst-addr "***********-22"
  dst-addr "***********-12"
  dst-addr "*********-12"
  dst-addr "*********-62"
  dst-addr "*********-42"
  service "SSH"
  service "tcp-6110"
  service "tcp-81"
  service "HTTP"
  name "运营代理机访问即开设奖资源服务器-1"
exit
rule id 47
  action permit
  src-zone "YY_OCS-trust"
  dst-zone "YY_OCS-untrust"
  src-addr "************"
  src-addr "***********"
  dst-addr "*********-22"
  service "tcp-3389"
  name "运营代理机访问即开设奖资源服务器-2"
exit
rule id 48
  action permit
  src-zone "YY_OCS-trust"
  dst-zone "YY_OCS-untrust"
  src-addr "************"
  src-addr "***********"
  dst-addr "*********-32"
  service "SSH"
  service "tcp-8088"
  name "运营代理机访问即开设奖资源服务器-3"
exit
rule id 49
  action permit
  disable
  src-zone "CODING-trust"
  dst-zone "CODING-untrust"
  src-ip ***********/24
  dst-addr "Any"
  service "Any"
  description "8.26"
  name "CODING出向访问策略"
exit
rule id 50
  action permit
  src-zone "CODING-untrust"
  dst-zone "CODING-trust"
  src-range *********** ***********
  dst-ip ************/32
  service "TCP-80"
  name "研发测试环境制品库数据同步"
exit
rule id 53
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-addr "*********"
  service "TCP-443"
  service "tcp-8080"
  service "TCP-80"
  name "ECC访问数据中心新mcafee主机"
exit
rule id 54
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "ECC"
  dst-ip ************/32
  dst-ip ***********/32
  service "tcp-16320-16323"
  service "tcp-16310-16316"
  name "ECC访问运维平台"
exit
rule id 60
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "cmdbnginx"
  src-addr "***********"
  src-addr "************"
  src-addr "*************-106"
  src-range *********** ***********
  dst-addr "cmdb"
  service "TCP-8880"
  service "tcp-8080"
  name "nginx_to_cmdb"
exit
rule id 62
  action permit
  src-zone "OCS-untrust"
  dst-zone "OCS-trust"
  src-addr "*********/32"
  dst-addr "**********"
  service "tcp-22"
  name "ZiDongHua_To_OCS"
exit
rule id 63
  action permit
  src-addr "***********/24"
  dst-addr "*********"
  service "ICMP"
  name "zabbix-to-mifu"
exit
rule id 66
  action permit
  src-zone "OCS-trust"
  dst-zone "OCS-untrust"
  src-addr "**********"
  dst-addr "*************-203"
  dst-addr "**********/24"
  service "tcp-8080"
  service "tcp-25"
  service "tcp-465"
  service "tcp-22"
  service "SSH"
  service "PING"
  name "ocs-to-mail.pt.prod"
exit
l2-nonip-action drop
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server vrouter "mgt-vr"
snmp-server engineID "cslc_snmp"
snmp-server host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg ro
snmp-server host ************ version 2c community ckeI3SSNGQhyyPo3P+lMJmQ8mGku ro
snmp-server host *********** version 2c community DWbf06+a4P0FlgO+Yrtfp2YzSHMK ro
snmp-server trap-host ************ version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq port 162
snmp-server trap-host ************ version 2c community iYrqDOGOtTqwQ38yjPmPA3Sw3FEJ port 162
snmp-server trap-host *********** version 2c community IQKm6jOQzQw3266DzxLL5hlJXP8D port 162
snmp-server trap-host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
  icmp-unreachable-session-keep
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional
exit      
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application
exit
query-groups
  dashboard-query-group "admin-**********873-dashboard-query-group" user "admin"
    rule "license" create-time ********** id 1 query-string "%7B%22time%22%3A1699888609493%2C%22ignore%22%3Atrue%7D"
  exit
exit
no sms disable
ha link interface HA0
ha link ip ******* *************
ha group 0
  priority 150
exit
ha cluster 1

End

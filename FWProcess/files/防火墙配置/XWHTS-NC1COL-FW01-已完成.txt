XWHTS-NC1COL-FW01# show conf
XWHTS-NC1COL-FW01# show configuration 

Building configuration..
Running configuration:
!
Version 5.5R2

ip vrouter "mgt-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "T1"
exit
ip vrouter "T2"
exit
ip vrouter "T3"
exit
ip vrouter "T4"
exit
ip vrouter "T5"
exit
ip vrouter "T6"
exit
ip vrouter "T7"
exit
ip vrouter "T8"
exit
ip vrouter "T9"
exit
ip vrouter "T10"
exit
ip vrouter "T11"
exit
ip vrouter "T12"
exit
ip vrouter "T13"
exit
ip vrouter "T14"
exit
ip vrouter "T15"
exit
ip vrouter "T16"
exit
ip vrouter "T17"
exit
ip vrouter "T18"
exit
ip vrouter "T19"
exit
ip vrouter "T20"
exit
ip vrouter "T21"
exit
ip vrouter "T22"
exit
ip vrouter "T23"
exit
ip vrouter "T24"
exit
ip vrouter "T25"
exit
ip vrouter "T26"
exit
ip vrouter "T27"
exit
ip vrouter "T28"
exit
ip vrouter "T29"
exit
ip vrouter "T30"
exit
vswitch "vswitch1"
exit      
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "T1-OUTSIDE"
exit
zone "T1-INSIDE"
exit
zone "T2-OUSIDE"
exit
zone "T2-OUTSIDE"
exit
zone "T2-INSIDE"
exit
zone "T3-OUTSIDE"
exit
zone "T3-INSIDE"
exit
zone "T4-OUTSIDE"
exit
zone "T4-INSIDE"
exit
zone "T5-OUTSIDE"
exit
zone "T5-INSIDE"
exit
zone "T6-OUTSIDE"
exit
zone "T6-INSIDE"
exit
zone "T7-OUTSIDE"
exit
zone "T7-INSIDE"
exit
zone "T8-OUTSIDE"
exit
zone "T8-INSIDE"
exit
zone "T9-OUTSIDE"
exit
zone "T9-INSIDE"
exit
zone "T10-OUTSIDE"
exit
zone "T10-INSIDE"
exit
zone "T11-OUTSIDE"
exit
zone "T11-INSIDE"
exit
zone "T12-OUTSIDE"
exit
zone "T12-INSIDE"
exit      
zone "T13-OUTSIDE"
exit
zone "T13-INSIDE"
exit
zone "T14-OUTSIDE"
exit
zone "T14-INSIDE"
exit
zone "T15-OUTSIDE"
exit
zone "T15-INSIDE"
exit
zone "T16-OUTSIDE"
exit
zone "T16-INSIDE"
exit
zone "T17-OUTSIDE"
exit
zone "T17-INSIDE"
exit
zone "T18-OUTSIDE"
exit
zone "T18-INSIDE"
exit
zone "T19-OUTSIDE"
exit
zone "T19-INSIDE"
exit
zone "T20-OUTSIDE"
exit
zone "T20-INSIDE"
exit
zone "T21-OUTSIDE"
exit
zone "T21-INSIDE"
exit
zone "T22-OUTSIDE"
exit
zone "T22-INSIDE"
exit
zone "T23-INSIDE"
exit
zone "T23-OUTSIDE"
exit
zone "T24-INSIDE"
exit
zone "T24-OUTSIDE"
exit
zone "T25-INSIDE"
exit
zone "T25-OUTSIDE"
exit
zone "T26-INSIDE"
exit
zone "T26-OUTSIDE"
exit
zone "T27-INSIDE"
exit
zone "T27-OUTSIDE"
exit
zone "T28-INSIDE"
exit
zone "T28-OUTSIDE"
exit
zone "T29-INSIDE"
exit
zone "T29-OUTSIDE"
exit      
zone "T30-INSIDE"
exit
zone "T30-OUTSIDE"
exit
interface vswitchif1
exit
interface MGT0
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit
interface aggregate1
exit
interface aggregate1.3080
exit
interface aggregate1.3081
exit
interface aggregate1.3082
exit
interface aggregate1.3083
exit
interface aggregate1.3084
exit
interface aggregate1.3085
exit
interface aggregate1.3086
exit
interface aggregate1.3087
exit
interface aggregate1.3088
exit
interface aggregate1.3089
exit
interface aggregate1.3090
exit
interface aggregate1.3091
exit
interface aggregate1.3092
exit
interface aggregate1.3093
exit      
interface aggregate1.3094
exit
interface aggregate1.3095
exit
interface aggregate1.3096
exit
interface aggregate1.3097
exit
interface aggregate1.3098
exit
interface aggregate1.3099
exit
interface aggregate1.3180
exit
interface aggregate1.3181
exit
interface aggregate1.3182
exit
interface aggregate1.3183
exit
interface aggregate1.3184
exit
interface aggregate1.3185
exit
interface aggregate1.3186
exit
interface aggregate1.3187
exit
interface aggregate1.3188
exit
interface aggregate1.3189
exit
interface aggregate1.3190
exit
interface aggregate1.3191
exit
interface aggregate1.3192
exit
interface aggregate1.3193
exit
interface aggregate1.3194
exit
interface aggregate1.3195
exit
interface aggregate1.3196
exit
interface aggregate1.3197
exit
interface aggregate1.3198
exit
interface aggregate1.3199
exit
interface aggregate1.3143
exit
interface aggregate1.3144
exit
interface aggregate1.3145
exit
interface aggregate1.3146
exit
interface aggregate1.3147
exit
interface aggregate1.3148
exit
interface aggregate1.3149
exit
interface aggregate1.3151
exit      
interface aggregate1.3152
exit
interface aggregate1.3150
exit
interface aggregate1.3153
exit
interface aggregate1.3154
exit
interface aggregate1.3155
exit
interface aggregate1.3156
exit
interface aggregate1.3157
exit
interface aggregate1.3158
exit
interface aggregate1.3159
exit
interface aggregate1.3160
exit
interface aggregate1.3161
exit
interface aggregate1.3162
exit
address "private_network"
exit
address "monitor_address"
exit
address "T3 SNAT-INTERNET- POOL"
exit
address "T3-SNAT-INTER-POOL"
exit
address "T3-TESTCLIET-TOINTERNET"
exit
address "OPS-*************/24"
exit
address "T3-harbor-***********/32"
exit
address "T4-*********-9"
exit
address "T4-********/24"
exit
address "T4-G3-WCSROUTER"
exit
address "T4-TRANSROUTER"
exit
address "T4_OLTP-ECGATEWAY"
exit
address "T4-G3-MATGATEWAY"
exit
address "*********-18-19"
exit
address "*********-2-3"
exit
address "***********."
exit
address "***********."
exit
address "***********."
exit
address "**********/24"
exit
address "*********-9"
exit
address "*********-31"
exit
address "************/24"
exit      
address "********/24"
exit
address "********/24"
exit
address "*********/21"
exit
address "*********/21"
exit
address "BG-**************-236"
exit
address "T4-*********/32"
exit
address "T4-*********/32"
exit
address "CSLC-***********/24"
exit
address "XWHT3-tiaobanji-**********"
exit
address "*********/24"
exit
address "**********/24"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********0"
exit
address "*********1"
exit
address "*********2"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "T3_GW_*********-18"
exit
address "CSLC-**********"
exit
address "T4-*********-8"
exit
address "CSLC-**********/32"
exit
address "T4-*********"
exit      
address "T4-*********"
exit
address "T4-*********"
exit
address "T4-*********"
exit
address "T3_CSLAPP_1"
exit
address "T3-CSLAPP-2"
exit
address "T3-CSLAPP"
exit
address "NAT-*********"
exit
address "NAT-*********"
exit
address "NAT-*********"
exit
address "NAT-*********"
exit
address "NAT-*********"
exit
address "NAT-*********-30"
exit
address "**********/32"
exit
address "**********/32"
exit
address "********/24"
exit
address "*********/27"
exit
address "********1/32"
exit
address "********2/32"
exit
address "********/32"
exit
address "***********"
exit
address "************/32"
exit
address "*********/32"
exit
address "CSLC-**********/32"
exit
address "NAT-********30"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********33/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "***********."
exit
address "***********/24"
exit      
address "*********/24"
exit
address "**********23/124"
exit
address "***************/221"
exit
address "************"
exit
address "*********/32"
exit
address "NAT-*********"
exit
address "NAT-*********"
exit
address "T1-**********"
exit
address "500wan-**************/32"
exit
address "JCWEB-**************/32"
exit
address "DXWG-**************/32"
exit
address "500wan_JCWeb_DXWG"
exit
address "*************/24"
exit
address "************/32"
exit
address "500WAN-*************"
exit
address "500WAM-**************"
exit
address "500wan1-*************"
exit
address "500wan2-**************"
exit
address "T4-***********"
exit
address "***********"
exit
address "T4-***********-253"
exit
address "***********"
exit
address "**************"
exit
address "NAT-********6"
exit
address "UnionPay-***************"
exit
address "********7"
exit
address "bangongwang-**************"
exit
address "************"
exit
address "DXYXS-**************"
exit
address "T3-*********"
exit
address "CA1-**********"
exit
address "CA2-**********"
exit
address "************"
exit
address "T3_NAT_********7"
exit      
address "500wan3-***************"
exit
address "********/32"
exit
address "**********/24"
exit
address "***************"
exit
address "***********"
exit
address "***********"
exit
address "T1OPS-********11"
exit
address "T1OPS-********12"
exit
address "T1OPS-********13"
exit
address "*********"
exit
address "500W-jingcaiwang"
exit
address "email_*************"
exit
address "QC-*************"
exit
address "ELP-***********/32"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********-147"
exit
address "***********/32"
exit
address "CSLC_***********/24"
exit
address "CSLC_infogw_************"
exit
address "Info.sporttery.cn"
exit
address "CSLC-**********/24"
exit
address "18.5.57.18"
exit
address "18.5.57.19"
exit
address "CSLC-SIE-104.21.2.38"
exit
address "**********/24"
exit
address "CSLC-104.24.0.0/24"
exit      
address "********/24"
exit
address "T1-18.0.120.10"
exit
address "18.6.4.38"
exit
address "***********/32"
exit
address "CSLC-104.21.2.51/32"
exit
address "203.119.206.132/32"
exit
address "unicom-123.125.97.251"
exit
address "Unicon-**************"
exit
address "T1-18.4.21.214"
exit
address "T1-*********"
exit
address "172.26.12.244/32"
exit
address "CSLC-SIT"
exit
address "172.26.12.50/32"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "***********"
exit
address "ELP-***********"
exit
address "ELP-***********"
exit
address "************/32"
exit
address "**********/24"
exit
address "***********/32"
exit
address "***********/32"
exit
address "*************/32"
exit
address "**************/32"
exit
address "**************-204/32"
exit
address "************/32"
exit
address "T1-*********"
exit
address "T1-*********"
exit
address "T1-*********"
exit
address "T1-*********"
exit
address "T1-*********"
exit
address "T1-*********"
exit      
address "T1-*********-26"
exit
address "GSM"
exit
address "G3-Information publishing platform"
exit
address "MarketingDate"
exit
address "*********/32"
exit
address "***********/32"
exit
address "Bisab-*********-45"
exit
address "T1_18.0.2.0/24"
exit
address "**************/32"
exit
address "**************/32"
exit
address "**************/32"
exit
address "*********-44"
exit
address "********/32"
exit
address "jiamiji-**********"
exit
address "jiamiji-**********"
exit
address "jiamiji-**********"
exit
address "***********"
exit
address "***********"
exit
address "*********/32"
exit
address "*********/32"
exit
address "***********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "***********/32"
exit
address "**************/32"
exit
address "**************/32"
exit
address "***********/32"
exit
address "***********-139"
exit
address "************/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "************/32"
exit
address "********/32"
exit      
address "CSLC-***********/24"
exit
address "CSLC-***********/24"
exit
address "********5/32"
exit
address "*********/32"
exit
address "T1-jiamiji-**********"
exit
address "JiKai_124.65.173.138"
exit
address "SuNingXiaoDian_221. 226.125.130"
exit
address "********4/32"
exit
address "***********/32"
exit
address "*************/32"
exit
address "CSLC-***********/24"
exit
address "CSLC-***********/24"
exit
address "*************/32"
exit
address "*************/32"
exit
address "***********/24"
exit
address "************/32"
exit
address "**********18/32"
exit
address "**************/32"
exit
address "**************/32"
exit
address "*********/32"
exit
address "**************/32"
exit
address "************/32"
exit
address "*********/8"
exit
address "********/8"
exit
address "*********/8"
exit
address "*********/8"
exit
address "***********/24"
exit
address "SSL_GATEWAY-**************"
exit
address "*************/24"
exit
address "************/24"
exit
address "*************/32"
exit
address "**********/16"
exit
address "***********/24"
exit
address "***********"
exit      
address "***********/24"
exit
address "*************/24"
exit
address "************/24"
exit
address "********8/32"
exit
address "**************/32"
exit
address "***************/32"
exit
address "*********/24"
exit
address "**********/24"
exit
address "**********/24"
exit
address "Internet-**************"
exit
address "**************/26"
exit
address "***********/32"
exit
address "********9/32"
exit
address "********0/32"
exit
address "**********/24"
exit
address "***********/32"
exit
address "********3/32"
exit
address "***********/32"
exit
address "*************/32"
exit
address "********2/32"
exit
address "************/32"
exit
address "***********/32"
exit
address "***************/32"
exit
address "************/32"
exit
address "47.105.221.226/32"
exit
address "104.21.4.110/32"
exit
address "*************/32"
exit
address "10.20.1.0/24"
exit
address "10.20.2.0/24"
exit
address "********7/32"
exit
address "124.127.94.59/32"
exit
address "**********/24"
exit
address "***************/32"
exit
address "*********/24"
exit      
address "*********/24"
exit
address "**********/24"
exit
address "*************/32"
exit
address "*************/32"
exit
address "*********2/32"
exit
address "*************/24"
exit
address "*************/24"
exit
address "106.11.192.0/19"
exit
address "************/22"
exit
address "*************/17"
exit
address "**************/32"
exit
address "***********/32"
exit
address "********18/32"
exit
address "***************/32"
exit
address "*************/32"
exit
address "**************/32"
exit
address "T3-*********-15"
exit
address "T3-*********-15"
exit
address "***********/24"
exit
address "********21"
exit
address "*************00/32"
exit
address "***********/24"
exit
address "**********/24"
exit
address "VDI-TO-T3-Tiaobanji"
exit
address "VDI-TO-T19-Tiaobanji"
exit
address "***************/32"
exit
address "***************/32"
exit
address "***********/32"
exit
address "********8/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "********28/32"
exit      
address "**********/24"
exit
address "***********/24"
exit
address "***********/24"
exit
address "*************/32"
exit
address "***********/32"
exit
address "************/32"
exit
address "************/32"
exit
address "********20/32"
exit
address "************/32"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "18.2.1.34"
exit
address "104.21.0.220/32"
exit
address "104.24.0.101"
exit
address "192.144.193.39"
exit
address "test-1.1.1.1"
exit
address "**************/32"
exit
address "YINLIAN"
exit
address "104.24.0.104/32"
exit
address "104.24.0.141/32"
exit
address "*********0/32"
exit
address "18.0.93.0/24"
exit
address "**********/24"
exit
address "104.23.11.4"
exit
address "104.24.0.136"
exit
address "18.5.10.1-51"
exit
address "18.5.50.1-2"
exit
address "124.193.71.186/32"
exit
address "10.213.0.200/32"
exit
address "47.93.16.203/32"
exit
address "104.24.0.31/32"
exit
address "CSLC-172.26.100.200/32"
exit      
address "104.21.51.51/32"
exit
address "104.23.13.0/24"
exit
address "104.23.11.10/32"
exit
address "192.168.32.109/32"
exit
address "********6/32"
exit
address "*********1-15"
exit
address "104.21.51.0/24"
exit
address "18.6.4.46"
exit
address "18.6.4.47"
exit
address "18.5.97.8"
exit
address "18.5.97.6"
exit
address "吉祥宝8.131.227.169"
exit
address "104.21.110.232/32"
exit
address "104.200.101.3/32"
exit
address "104.200.100.69/32"
exit
address "104.21.19.20/32"
exit
address "104.21.19.98/32"
exit
address "104.21.110.222/32"
exit
address "********1/32"
exit
address "104.21.19.99/32"
exit
address "104.24.0.3/32"
exit
address "*********/16"
exit
address "********"
exit
address "10.216.3.0/24"
exit
address "T1-18.0.1.0/24"
exit
address "T2-28.0.1.0/24"
exit
address "T4-48.0.1.0/24"
exit
address "T5-58.0.1.0/24"
exit
address "18.6.4.48/32"
exit
address "123.57.49.51-吉林大乐透"
exit
address "104.11.1.0/32"
exit
address "10.213.5.241/32"
exit
address "wangliang-10.211.3.187/32"
exit
address "*********7"
exit      
address "*********8/32"
exit
address "*********9/32"
exit
address "104.21.19.53/32"
exit
address "TXY-10.215.41.178"
exit
address "10.215.41.170"
exit
address "********38"
exit
address "***********-147"
exit
address "***************"
exit
address "10.216.31.0/24"
exit
address "************/32"
exit
address "W5RBOCC"
exit
address "10.20.4.0/24"
exit
address "************/24"
exit
address "10.213.3.0/24"
exit
address "192.168.200.0/24"
exit
address "shuangyinsu_test"
exit
address "*************/32"
exit
address "**********11-118"
exit
address "10.219.0.161/32"
exit
address "10.219.0.164/32"
exit
address "10.211.3.116"
exit
address "192.168.213.88"
exit
address "T18-CDDING-NET-10.217-0&1.0-24"
exit
address "T18-PUBLICSERVICE-10.217.2.0-24"
exit
address "T18-MONITOR-10.217.3.0-24"
exit
address "T18-SEC-10.217.4.0/24"
exit
address "T18-MS-10.217.5.0-24"
exit
address "T18-DMZ-10.217.6.0-24"
exit
address "T18-VDI-**********-16-OUT"
exit
address "T18-LISHUAIQILINSHIWEIHU-************-24-OUT"
exit
address "T18-LISHUAIQILINSHIWEIHUA-***********-32"
exit
address "104.23.15.0/24"
exit
address "104.11.1.0/24"
exit
address "104.21.51.41-46"
exit      
address "10.217.0.1/32-coding"
exit
address "10.213.0.9_G3zidonghuiTest"
exit
address "10.213.0.63_G3AutoTest"
exit
address "10.216.1.49_ads"
exit
address "104.23.14.0/24"
exit
address "CSL-office"
exit
address "YZ-10.217.129.0/24"
exit
address "10.217.2.0/24"
exit
address "YZ-10.218.129.0/24"
exit
address "VDI-10.217.129-133.0/24"
exit
address "T1-********38"
exit
address "************"
exit
address "***********"
exit
address "zabbix-**********1"
exit
address "10.218.0.0/16"
exit
address "solarwids-***********"
exit
address "10.219.4.253"
exit
address "10.217.6.1-2"
exit
address "10.219.4.254"
exit
address "10.219.0.252"
exit
address "10.217.6.3"
exit
address "***********/32"
exit
address "YZTESTLAB-104-SNAT-18.6.30.0/24"
exit
address "YZTESTLAB-10-SNAT-18.6.31.0/24"
exit
address "VC-172.16.23.1"
exit
address "10.214.2.0/24"
exit
address "Zidonghua"
exit
address "CSLOOffice"
exit
address "OBDB-10.220.4.4-6"
exit
address "Ceshi_104.21.0.0/16"
exit
address "10.211.11.0/24"
exit
address "10.211.3."
exit
address "NAS-************-202"
exit
address "K8SNode-18.5.74.1-5"
exit      
address "K8SNode-18.5.74.6-10"
exit
address "K8SNode-18.5.82.1-5"
exit
address "K8SNode-18.5.82.6-10"
exit
address "K8SNode-18.5.82.11-15"
exit
address "K8SNode-18.5.82.16-20"
exit
address "K8SNode-18.5.82.21-25"
exit
address "K8SNode-18.5.82.26-30"
exit
address "K8SNode-18.5.82.31-35"
exit
address "K8SNode-**********-40"
exit
address "Nexus-************-204"
exit
address "CSLC-Office_**********/22"
exit
address "**********/24"
exit
address "**********/24"
exit
address "***********/24"
exit
address "**********/32"
exit
address "**********/24"
exit
address "************/24"
exit
address "AD_**********"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "**********05/32"
exit
address "***********/32"
exit
address "**********/24"
exit
address "SMTP-**********"
exit
address "*************/32"
exit
address "**********-4"
exit
address "**********1-14"
exit
address "**********1-18"
exit
address "***********/32"
exit
address "***********-42"
exit
address "***********-65"
exit      
address "**********/*************/***********"
exit
address "shujuzhongtai_**********/24"
exit
address "************"
exit
address "************"
exit
address "quexiangaojing"
exit
address "**********/22"
exit
address "***********/32"
exit
address "**********/32"
exit
address "************/32"
exit
address "CSLC-***********/24"
exit
address "CSLC-172.20.16-19.0/24"
exit
address "yun-**********/16"
exit
address "yun-**********/16"
exit
address "G3-**********/24"
exit
address "G3-**********/24"
exit
address "10.219.1.100_jiaobenzhixing"
exit
address "**********-3(guanli)"
exit
address "**********5_17_18(code ku)"
exit
address "***********/32"
exit
address "************/32"
exit
address "***********"
exit
address "************/32"
exit
address "*************/32"
exit
address "G3BISMONTORCOLLECT"
exit
address "***********"
exit
address "**********"
exit
address "***********"
exit
address "10.219.5.52_jiagoubu"
exit
address "Maven/vue"
exit
address "************/32"
exit
address "***********"
exit
address "***********"
exit
address "********/24"
exit
address "********/24"
exit      
address "********/24"
exit
address "乐透归集库"
exit
address "T4-数据中台01"
exit
address "**********/24"
exit
address "腾讯云10.216.40"
exit
address "T4-**********/24"
exit
address "T3-**********/24"
exit
address "报表集市G32云上"
exit
address "ADS***********"
exit
address "82_157_106.153"
exit
address "JCVSCC"
exit
address "111_204_38_0/24"
exit
address "10_104/8"
exit
address "T3_体彩APP"
exit
address "10/8"
exit
address "************"
exit
address "*********/32"
exit
address "*********"
exit
address "*********"
exit
address "T3_***********to217"
exit
address "**********/32"
exit
address "***********/32"
exit
address "************"
exit
address "需求管理工具***********"
exit
address "ELP18.5.32.211-217"
exit
address "**********"
exit
address "ELP18.5.32.202-217"
exit
address "**********&41"
exit
address "************"
exit
address "**********"
exit
address "**********"
exit
address "*********1"
exit
address "*********1"
exit
address "*************"
exit      
address "**********/24"
exit
address "**********"
exit
address "VDI-LINUXSERVER"
exit
address "V_RMOAS18.6.114.252"
exit
address "R_WCS18.5.70.51"
exit
address "10.220.7.6_25"
exit
address "**********"
exit
address "10.211.5&6&12.0/24"
exit
address "SMTP-**********"
exit
address "VDI-10.217.134-140.0/24"
exit
address "************"
exit
address "************"
exit
address "***********-178"
exit
address "************/32"
exit
address "**********11/32"
exit
address "***********/24"
exit
address "DEV_DESK"
exit
address "SHUJUJIANMO-TOOLS"
exit
address "YUNYINGVDI-138"
exit
address "*********1"
exit
address "*************-168"
exit
address "**************-*************"
exit
address "***********"
exit
address "T4OB10.220.0.173"
exit
address "************"
exit
address "***********"
exit
address "***********"
exit
address "************/24"
exit
address "**********/24"
exit
address "************"
exit
address "BT"
exit
address "***********"
exit
address "ACLLB-SNAT"
exit
address "ACLLB-SELF"
exit      
address "************/24"
exit
address "***********"
exit
address "************/24"
exit
address "**********"
exit
address "**********-1.0/24"
exit
address "**********"
exit
address "***********/16"
exit
address "**********"
exit
address "***********"
exit
address "*********"
exit
address "***********-14"
exit
address "**********-4"
exit
address "***********-19"
exit
address "***********-173"
exit
address "*******/24"
exit
address "***********"
exit
address "************"
exit
address "*********"
exit
address "*********/16"
exit
address "***********"
exit
address "***********/张跃的VDI测试"
exit
address "**********"
exit
address "***************"
exit
address "************"
exit
address "**********"
exit
address "***********"
exit
address "***********/32"
exit
address "************"
exit
address "************"
exit
address "************"
exit
address "***********-15"
exit
address "*************-20"
exit
address "*********"
exit
address "***********"
exit      
address "***********/24"
exit
address "10.220.129.1/32"
exit
address "10.220.132.101-106"
exit
address "104.23.20.220-222"
exit
address "10.220.3.50"
exit
address "************"
exit
address "***********/32"
exit
address "18.0.160.1/32"
exit
address "10.216.71.0"
exit
address "18.5.81.5"
exit
address "10.129.4.135"
exit
address "***********"
exit
address "10.219.5.159"
exit
address "10.248.121.1"
exit
address "**************"
exit
address "**********02"
exit
address "***********-30"
exit
address "********40"
exit
address "*********4"
exit
address "*********5"
exit
address "***********/24"
exit
address "***********/24"
exit
address "***********/24"
exit
address "***********/24"
exit
address "**********"
exit
address "**********-42"
exit
address "***********/24"
exit
address "**********-22"
exit
address "********/24"
exit
address "**********"
exit
address "************"
exit
address "************"
exit
address "**********"
exit
address "**********"
exit      
address "***********-72"
exit
address "*************"
exit
address "**********-16"
exit
address "***********"
exit
address "**********"
exit
address "**********"
exit
address "**********6"
exit
address "**********8"
exit
address "新机器开通到运行ca以及radk服务的端口"
exit
address "新机器开通到KM服务的端口"
exit
address "新机器开通到运行ca、km服务以及radk服务的端口"
exit
address "新机器开通到运行ca、km数据库服务的端口"
exit
address "新机器开通到新ldap和ibmldap服务的端口"
exit
address "新机器开通到新ldap和oracle服务的端口"
exit
aaa-server "local" type local
exit
logging session content hostname
logging session content username
service "TCP-30400"
  tcp dst-port 30400 
exit
service "TCP-31306"
  tcp dst-port 31306 
exit
service "TCP-5000"
  tcp dst-port 5000 
exit
service "TCP-8011"
  tcp dst-port 8011 
exit
service "TCP-32600"
  tcp dst-port 32600 
exit
service "TCP-3389"
  tcp dst-port 3389 
exit
service "TCP-29200"
  tcp dst-port 29200 
exit
service "NFS-NEW"
  tcp dst-port 111 
  udp dst-port 111 
  tcp dst-port 2049 
  udp dst-port 2049 
  tcp dst-port 32768 65535 
  udp dst-port 32768 65535 
exit
service "TCP-7001"
  tcp dst-port 7001 
exit
service "TCP-5672"
  tcp dst-port 5672 
exit
service "TCP-8004"
  tcp dst-port 8004 
exit
service "TCP-8443"
  tcp dst-port 8443 
exit
service "TCP-21"
  tcp dst-port 21 
exit
service "TCP-52701"
  tcp dst-port 52701 
exit
service "TCP-8001"
  tcp dst-port 8001 
exit
service "TCP_8086"
  tcp dst-port 8086 
exit
service "TCP-8000-8010"
  tcp dst-port 8000 8010 
exit
service "TCP-28080"
  tcp dst-port 28080 
exit
service "TCP-5044"
  tcp dst-port 5044 
exit
service "TCP-20203"
  tcp dst-port 20203 
exit
service "TCP-8087"
  tcp dst-port 8087 
exit
service "TCP-8089"
  tcp dst-port 8089 
exit
service "TCP-21514"
  tcp dst-port 21514 
exit
service "TCP-32514"
  tcp dst-port 32514 
exit
service "TCP-8080"
  tcp dst-port 8080 
exit
service "TCP-9999"
  tcp dst-port 9999 
exit
service "TCP-8000"
  tcp dst-port 8000 
exit
service "TCP-9080"
  tcp dst-port 9080 
exit
service "TCP-8024"
  tcp dst-port 8024 
exit
service "TCP-30000"
  tcp dst-port 30000 
exit
service "TCP-8090"
  tcp dst-port 8090 
exit
service "TCP-9090"
  tcp dst-port 9090 
exit
service "TCP-389"
  tcp dst-port 389 
exit      
service "TCP-20050"
  tcp dst-port 20050 
exit
service "TCP-2001-2003"
  tcp dst-port 2001 2003 
exit
service "TCP-20167"
  tcp dst-port 20167 
exit
service "TCP-55382"
  tcp dst-port 55382 
exit
service "TCP-10050"
  tcp dst-port 10050 
exit
service "TCP-9091"
  tcp dst-port 9091 
exit
service "TCP-6008"
  tcp dst-port 6008 
exit
service "TCP-4433"
  tcp dst-port 4433 
exit
service "TCP-6006"
  tcp dst-port 6006 
exit
service "TCP-20150"
  tcp dst-port 20150 
exit
service "TCP_34443"
  tcp dst-port 34443 
exit
service "TCP-8013"
  tcp dst-port 8013 
exit
service "TCP-8018"
  tcp dst-port 8018 
exit
service "TCP-8088"
  tcp dst-port 8088 
exit
service "TCP-6021"
  tcp dst-port 6021 
exit
service "TCP-6201"
  tcp dst-port 6201 
exit
service "TCP-8082"
  tcp dst-port 8082 
exit
service "TCP-50094/50095"
  tcp dst-port 50094 50095 
exit
service "TCP-15000"
  tcp dst-port 15000 
exit
service "TCP-24433"
  tcp dst-port 24433 
exit
service "TCP-8083"
  tcp dst-port 8083 
exit
service "TCP-8161"
  tcp dst-port 8161 
exit
service "TCP-9021"
  tcp dst-port 9021 
exit
service "TCP-25601"
  tcp dst-port 25601 
exit
service "TCP-28081"
  tcp dst-port 28081 
exit
service "TCP-54102-54104"
  tcp dst-port 54102 54104 
exit
service "TCP-55672"
  tcp dst-port 55672 
exit
service "TCP-8181"
  tcp dst-port 8181 
exit
service "TCP-2001"
  tcp dst-port 2001 
exit
service "TCP-10011"
  tcp dst-port 10011 
exit
service "TCP-34433"
  tcp dst-port 34433 
exit
service "TCP-9092"
  tcp dst-port 9092 
exit
service "TCP-14433"
  tcp dst-port 14433 
exit
service "TCP-8098"
  tcp dst-port 8098 
exit
service "TCP-4422"
  tcp dst-port 4422 
exit
service "TCP-8101"
  tcp dst-port 8101 
exit
service "TCP-8103"
  tcp dst-port 8103 
exit
service "TCP-5533"
  tcp dst-port 5533 
exit
service "TCP-30071"
  tcp dst-port 30071 
exit
service "TCP-8022"
  tcp dst-port 8022 
exit
service "TCP-42514"
  tcp dst-port 42514 
exit
service "TCP-9001"
  tcp dst-port 9001 
exit
service "TCP-8989"
  tcp dst-port 8989 
exit
service "TCP-3000"
  tcp dst-port 3000 
exit
service "TCP-445"
  tcp dst-port 445 
exit
service "TCP-29092"
  tcp dst-port 29092 
exit
service "TCP-1521"
  tcp dst-port 1521 
exit
service "TCP-19092"
  tcp dst-port 19092 
exit
service "TCP-5080"
  tcp dst-port 5080 
exit
service "TCP-18500"
  tcp dst-port 18500 
exit
service "TCP-10248"
  tcp dst-port 10248 
exit
service "TCP-8330"
  tcp dst-port 8330 
exit
service "tcp7003"
  tcp dst-port 7003 src-port 0 65535 
exit
service "TCP_9443"
  tcp dst-port 9443 
exit
service "TCP_6001"
  tcp dst-port 6001 
exit
service "TCP-1433"
  tcp dst-port 1433 
exit
service "5000-5008"
  tcp dst-port 5000 5008 src-port 65535 
exit
service "TCP6370"
  tcp dst-port 6370 src-port 0 65535 
exit
service "tcp5000-5008"
  tcp dst-port 5000 5008 src-port 0 65535 
exit
service "tcp28090"
  tcp dst-port 28090 src-port 0 65535 
exit
service "TCP-8081"
  tcp dst-port 8081 src-port 0 65535 
exit
service "TCP-8011-8020"
  tcp dst-port 8011 8020 
exit
service "3191"
  tcp dst-port 3191 
exit
service "TCP-3191"
  tcp dst-port 3191 
exit
service "TCP-34431-34435"
  tcp dst-port 34431 34435 src-port 0 65535 
exit
service "TCP/UDP-111"
  tcp dst-port 111 src-port 0 65535 
  udp dst-port 111 src-port 0 65535 
exit
service "TCP/UDP-2049"
  tcp dst-port 2049 src-port 0 65535 
  udp dst-port 2049 src-port 0 65535 
exit
service "TCP/UDP-32768-65535"
  tcp dst-port 32768 65535 src-port 0 65535 
  udp dst-port 32768 65535 src-port 0 65535 
exit
service "TCP-7051"
  tcp dst-port 7051 
exit
service "TCP-7061"
  tcp dst-port 7061 
exit
service "TCP-8061"
  tcp dst-port 8061 
exit
service "TCP-21598"
  tcp dst-port 21598 
exit
service "TCP-10000"
  tcp dst-port 10000 src-port 0 65535 
exit
service "TCP-135"
  tcp dst-port 135 src-port 0 65535 
exit
service "TCP-139"
  tcp dst-port 139 src-port 0 65535 
exit
service "TCP-49681"
  tcp dst-port 49681 src-port 0 65535 
exit
service "TCP-49712"
  tcp dst-port 49712 src-port 0 65535 
exit
service "TCP-30031"
  tcp dst-port 30031 src-port 0 65535 
exit
service "TCP-8553"
  tcp dst-port 8553 src-port 0 65535 
exit
service "TCP-6443"
  tcp dst-port 6443 src-port 0 65535 
exit
service "TCP-2222"
  tcp dst-port 2222 src-port 0 65535 
exit
service "TCP-8000-8100"
  tcp dst-port 8000 8100 src-port 0 65535 
exit
service "TCP-31099"
  tcp dst-port 31099 src-port 0 65535 
exit
service "TCP-10251-10252"
  tcp dst-port 10251 10252 
exit
service "TCP-4100-4130"
  tcp dst-port 4100 4130 
exit
service "TCP7809"
  tcp dst-port 7809 
exit
service "TCP3306"
  tcp dst-port 3306 
exit
service "TCP7810"
  tcp dst-port 7810 
exit
service "TCP7180-50070-25010-19888-61000"
  tcp dst-port 7180 
  tcp dst-port 50070 
  tcp dst-port 25010 
  tcp dst-port 19888 
  tcp dst-port 61000 
exit
service "TCP8081-8089"
  tcp dst-port 8081 8089 
exit
service "TCP8099"
  tcp dst-port 8099 
exit
service "TCP58001"
  tcp dst-port 58001 
exit
service "TCP7810-7859"
  tcp dst-port 7810 7859 
exit
service "TCP8000UDP"
  tcp dst-port 8000 
  udp dst-port 8000 
exit
service "TCP-587"
  tcp dst-port 587 
exit
service "10288-10289"
  tcp dst-port 10288 10289 
exit
service "pkica-s"
  tcp dst-port 18060 18062 
  tcp dst-port 3306 
  tcp dst-port 17070 17071 
  tcp dst-port 379 389 
  tcp dst-port 17090 17092 
  tcp dst-port 22 
  tcp dst-port 10288 10289 
  tcp dst-port 4444 
exit
service "TCP4444"
  tcp dst-port 4444 
exit
service "8081"
  tcp dst-port 8081 
exit
service "TCP2882"
  tcp dst-port 2882 
exit
service "TCP8001-8049"
  tcp dst-port 8001 8049 
exit
service "25"
  tcp dst-port 25 
exit
service "7443"
  tcp dst-port 7443 
exit
service "IMAP-993"
  tcp dst-port 993 
exit
service "POP-995"
  tcp dst-port 995 
exit
service "TCP-18060"
  tcp dst-port 18060 
exit
service "TCP-17080"
  tcp dst-port 17080 
exit
service "TCP-17090"
  tcp dst-port 17090 
exit
service "tcp-6446"
  tcp dst-port 6446 
exit
service "tcp-3307"
  tcp dst-port 3307 
exit
service "TCP-3555"
  tcp dst-port 3555 
exit
service "TCP-7002"
  tcp dst-port 7002 
exit
service "tcp:20443"
  tcp dst-port 20443 
exit
service "udp-161"
  udp dst-port 161 
exit
service "TCP-18443"
  tcp dst-port 18443 
exit
service "TCP-18090"
  tcp dst-port 18090 
exit
service "TCP-19080"
  tcp dst-port 19080 
exit
ips sigset "dns" template dns
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "ftp" template ftp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "http" template http
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
  web-server "default"
  exit
exit
ips sigset "pop3" template pop3
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "smtp" template smtp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "telnet" template telnet
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "other-tcp" template other-tcp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "other-udp" template other-udp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "imap" template imap
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "finger" template finger
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "sunrpc" template sunrpc
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "nntp" template nntp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "tftp" template tftp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "snmp" template snmp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "mysql" template mysql
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "mssql" template mssql
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "oracle" template oracle
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "msrpc" template msrpc
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit      
ips sigset "netbios" template netbios
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "dhcp" template dhcp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "ldap" template ldap
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "voip" template voip
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_dns" template dns
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_ftp" template ftp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_http" template http
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
  web-server "default"
  exit
exit
ips sigset "default_pop3" template pop3
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_smtp" template smtp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_telnet" template telnet
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_other-tcp" template other-tcp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit      
ips sigset "default_other-udp" template other-udp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_imap" template imap
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_finger" template finger
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_sunrpc" template sunrpc
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_nntp" template nntp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_tftp" template tftp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_snmp" template snmp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_mysql" template mysql
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_mssql" template mssql
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_oracle" template oracle
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_msrpc" template msrpc
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_netbios" template netbios
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_dhcp" template dhcp
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_ldap" template ldap
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips sigset "default_voip" template voip
  max-scan-bytes 30720
  attack-level critical action reset 
  attack-level info action reset 
  attack-level warning action reset 
exit
ips profile "no-ips"
exit
ips profile "predef_default"
  sigset "default_dns"
  sigset "default_ftp"
  sigset "default_http"
  sigset "default_pop3"
  sigset "default_smtp"
  sigset "default_telnet"
  sigset "default_other-tcp"
  sigset "default_other-udp"
  sigset "default_imap"
  sigset "default_finger"
  sigset "default_sunrpc"
  sigset "default_nntp"
  sigset "default_tftp"
  sigset "default_snmp"
  sigset "default_mysql"
  sigset "default_mssql"
  sigset "default_oracle"
  sigset "default_msrpc"
  sigset "default_netbios"
  sigset "default_dhcp"
  sigset "default_ldap"
  sigset "default_voip"
exit
contentfilter
  url-category "custom1"
  url-category "custom2"
  url-category "custom3"
  category "url control"
  keyword "inforequest.sporttery.cn" simple category "url control" confidence 100
exit
admin user "hillstone"
  password hIGSO7q/IT91mW2n14/5vodQaC
  role "admin"
  access console
  access telnet
  access ssh
  access http
  access https
exit
admin user "admin"
  password 4WoKYcyJEFvi02MmzCltucAgkN
        password-expiration 1666158955
  role "admin"
  access console
  access telnet
  access ssh
  access http
  access https
exit
admin user "operator"
  password 0bBc5tSOsx30UM+IGLM9A5XgqB
        password-expiration 1628820040
  access console
  access ssh
  access https
exit
admin user "test"
  password qAEw3j7r9qo2l9aim0H/FtHQ88
        password-expiration 1628841638
  role "admin"
  access telnet
  access ssh
  access http
  access https
exit
admin user "netadmin"
  password KgOQrk3aUm6ZzyAago0PuUwAEY
        password-expiration 1685426676
  role "admin"
  access console
  access telnet
  access ssh
  access http
  access https
exit
admin user "gaochen"
  password gtImAZ6oDxzwf6ai7py6e3pAm7
        password-expiration 1688977384
  role "admin"
  access ssh
  access https
exit
sso-agent aaa-server local
sso-agent idle-timeout 15

logging event to syslog 
logging network to syslog 
logging traffic session on
logging traffic session to syslog custom-format 
logging traffic nat on
logging traffic nat to syslog custom-format 
logging syslog 18.5.99.18 vrouter "mgt-vr" udp 514 type event
logging syslog 18.5.99.18 vrouter "mgt-vr" udp 514 type network
logging syslog 18.5.99.18 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 18.5.99.18 vrouter "mgt-vr" udp 514 type traffic nat
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "monitor_address"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "T3 SNAT-INTERNET- POOL"
  description "T3 SNAT-INTERNET- POOL"
  ip ********48/29
exit
address "T3-TESTCLIET-TOINTERNET"
  range *************** ***************
exit
address "OPS-*************/24"
  ip *************/24
exit
address "T3-harbor-***********/32"
  ip ***********/32
exit
address "T4-*********-9"
  range ********* *********
exit
address "T4-********/24"
  ip ********/24
exit
address "T4-G3-WCSROUTER"
  ip **********/32
  ip *********/32
exit
address "T4-TRANSROUTER"
  ip **********/32
  ip *********/32
exit
address "T4_OLTP-ECGATEWAY"
  ip **********/32
  ip *********/32
exit
address "T4-G3-MATGATEWAY"
  ip **********/32
  ip *********/32
exit
address "*********-18-19"
  ip *********/32
  ip *********/32
  ip *********/32
exit
address "*********-2-3"
  ip *********/32
  ip *********/32
  ip *********/32
exit
address "***********."
  ip ***********/32
exit
address "***********."
  ip ***********/32
exit
address "***********."
  ip ***********/32
exit
address "**********/24"
  ip **********/24
exit
address "*********-9"
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
exit
address "*********-31"
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
exit
address "************/24"
  ip ************/24
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "*********/21"
  ip *********/21
exit
address "*********/21"
  ip *********/21
exit
address "BG-**************-236"
  range ************** **************
exit
address "T4-*********/32"
  ip *********/32
exit
address "T4-*********/32"
  ip *********/32
exit
address "CSLC-***********/24"
  ip ***********/24
exit
address "XWHT3-tiaobanji-**********"
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "**********/24"
  ip **********/24
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit      
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********0"
  ip *********0/32
exit
address "*********1"
  ip *********1/32
exit
address "*********2"
  ip *********2/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "T3_GW_*********-18"
  member "*********"
  member "*********0"
  member "*********1"
  member "*********2"
  member "**********"
  member "**********"
  member "**********"
  member "**********"
  member "**********"
  member "**********"
  member "*********"
  member "*********"
  member "*********"
  member "*********"
  member "*********"
  member "*********"
  member "*********"
  member "*********"
exit      
address "CSLC-**********"
  ip **********/32
exit
address "T4-*********-8"
  range ********* *********
exit
address "CSLC-**********/32"
  ip **********/32
exit
address "T4-*********"
  ip *********/32
exit
address "T4-*********"
  ip *********/32
exit
address "T4-*********"
  ip *********/32
exit
address "T4-*********"
  ip *********/32
exit
address "T3_CSLAPP_1"
  ip 10.0.1.0/24
exit
address "T3-CSLAPP-2"
  ip 10.20.2.0/24
  ip 10.20.1.0/24
exit
address "T3-CSLAPP"
  member "T3_CSLAPP_1"
  member "T3-CSLAPP-2"
exit
address "NAT-*********"
  ip *********/32
exit
address "NAT-*********"
  ip *********/32
exit
address "NAT-*********"
  ip *********/32
exit
address "NAT-*********"
  ip *********/32
exit
address "NAT-*********"
  ip *********/30
exit
address "NAT-*********-30"
  range ********* *********
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "********/24"
  ip ********/24
exit
address "*********/27"
  ip *********/27
exit
address "********1/32"
  ip ********1/32
exit
address "********2/32"
  ip ********2/32
exit      
address "********/32"
  ip ********/32
exit
address "***********"
  ip ***********/32
exit
address "************/32"
  ip ************/32
exit
address "*********/32"
  ip *********/32
exit
address "CSLC-**********/32"
  ip **********/32
exit
address "NAT-********30"
  ip ********30/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********33/32"
  ip **********33/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "***********."
  ip ***********/32
exit
address "***********/24"
  ip ***********/24
exit
address "*********/24"
  ip *********/24
exit
address "**********23/124"
  ip **********23/32
  ip **********24/32
exit
address "***************/221"
  ip ***************/32
  ip 192.168.181.221/32
exit
address "************"
  ip ************/32
exit
address "*********/32"
  ip *********/32
exit
address "NAT-*********"
  ip *********/32
exit      
address "NAT-*********"
  ip *********/32
exit
address "T1-**********"
  ip **********/32
exit
address "500wan-**************/32"
  ip **************/32
exit
address "JCWEB-**************/32"
  ip **************/32
exit
address "DXWG-**************/32"
  ip **************/32
exit
address "500wan_JCWeb_DXWG"
  member "500wan-**************/32"
  member "DXWG-**************/32"
  member "JCWEB-**************/32"
  member "500WAM-**************"
  member "500WAN-*************"
  member "500wan3-***************"
  member "500W-jingcaiwang"
  member "Info.sporttery.cn"
exit
address "*************/24"
  ip *************/24
exit
address "************/32"
  ip ************/32
exit
address "500WAN-*************"
  ip *************/32
exit
address "500WAM-**************"
  ip **************/32
exit
address "500wan1-*************"
  ip *************/32
exit
address "500wan2-**************"
  ip **************/32
exit
address "T4-***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "T4-***********-253"
  member "***********"
  member "T4-***********"
exit
address "***********"
  ip ***********/32
exit
address "**************"
  ip **************/32
exit
address "NAT-********6"
  ip ********6/32
exit
address "UnionPay-***************"
  ip ***************/32
exit
address "********7"
  ip ********7/32
exit      
address "bangongwang-**************"
  ip **************/32
exit
address "************"
  ip ************/32
exit
address "DXYXS-**************"
  ip **************/32
exit
address "T3-*********"
  ip *********/32
exit
address "CA1-**********"
  ip **********/32
exit
address "CA2-**********"
  ip **********/32
exit
address "************"
  ip ************/32
exit
address "T3_NAT_********7"
  ip ********7/32
exit
address "500wan3-***************"
  ip ***************/32
exit
address "********/32"
  ip ********/32
exit
address "**********/24"
  ip **********/24
exit
address "***************"
  ip ***************/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "T1OPS-********11"
  ip ********11/32
exit
address "T1OPS-********12"
  ip ********12/32
exit
address "T1OPS-********13"
  ip ********13/32
exit
address "*********"
  ip *********/32
exit
address "500W-jingcaiwang"
  range ************** **************
exit
address "email_*************"
  ip *************/32
exit
address "QC-*************"
  ip *************/32
exit
address "ELP-***********/32"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********-147"
  member "ELP-***********/32"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  member "ELP-***********"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "CSLC_***********/24"
  ip ***********/24
exit
address "CSLC_infogw_************"
  ip ************/32
exit
address "Info.sporttery.cn"
  ip **************/32
exit
address "CSLC-**********/24"
  ip **********/24
exit
address "18.5.57.18"
  ip 18.5.57.18/32
exit
address "18.5.57.19"
  ip 18.5.57.19/32
exit
address "CSLC-SIE-104.21.2.38"
  ip 104.21.2.38/32
exit      
address "**********/24"
  ip **********/24
exit
address "CSLC-104.24.0.0/24"
  ip 104.24.0.0/24
exit
address "********/24"
  ip ********/24
exit
address "T1-18.0.120.10"
  ip 18.0.120.10/32
exit
address "18.6.4.38"
  ip 18.6.4.38/32
exit
address "***********/32"
  ip ***********/32
exit
address "CSLC-104.21.2.51/32"
  ip 104.21.2.51/32
exit
address "203.119.206.132/32"
  ip 203.119.206.132/32
exit
address "unicom-123.125.97.251"
  ip 123.125.97.251/32
exit
address "Unicon-**************"
  ip **************/32
exit
address "T1-18.4.21.214"
  ip 18.4.21.214/32
exit
address "T1-*********"
  ip *********/32
exit
address "172.26.12.244/32"
  ip 172.26.12.244/32
exit
address "CSLC-SIT"
  ip 172.26.12.244/32
  ip ************/32
exit
address "172.26.12.50/32"
  ip 172.26.12.50/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "ELP-***********"
  ip ***********/32
exit
address "************/32"
  ip ************/32
exit
address "**********/24"
  ip **********/24
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*************/32"
  ip *************/32
exit
address "**************/32"
  ip **************/32
exit
address "**************-204/32"
  ip **************/32
  ip 172.26.100.203/32
  ip 172.26.100.204/32
exit
address "************/32"
  ip ************/32
exit
address "T1-*********"
  ip *********/32
exit
address "T1-*********"
  ip *********/32
exit
address "T1-*********"
  ip *********/32
exit
address "T1-*********"
  ip *********/32
exit
address "T1-*********"
  ip *********/32
exit
address "T1-*********"
  ip *********/32
exit
address "T1-*********-26"
  member "T1-*********"
  member "T1-*********"
  member "T1-*********"
  member "T1-*********"
  member "T1-*********"
  member "T1-*********"
exit
address "GSM"
  ip 104.98.114.26/32
  ip 104.98.114.33/32
  ip 23.62.226.203/32
  ip 23.62.226.209/32
  ip 122.224.45.110/32
  ip 23.63.32.240/32
  ip 104.107.191.160/32
exit
address "G3-Information publishing platform"
  range **********14 **********18
exit
address "MarketingDate"
  ip 18.5.9.124/32
  ip 18.5.8.88/32
  ip 18.5.8.84/32
  ip 18.5.8.94/32
exit
address "*********/32"
  ip *********/32
exit
address "***********/32"
  ip ***********/32
exit
address "Bisab-*********-45"
  range ********* *********
exit
address "T1_18.0.2.0/24"
  ip 18.0.2.0/24
exit
address "**************/32"
  ip **************/32
exit
address "**************/32"
  ip **************/32
exit
address "**************/32"
  ip **************/32
exit
address "*********-44"
  range ********* 18.6.4.44
exit
address "********/32"
  ip ********/32
exit
address "jiamiji-**********"
  ip **********/32
exit
address "jiamiji-**********"
  ip **********/32
exit
address "jiamiji-**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "***********/32"
  ip ***********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
exit
address "**************/32"
  ip **************/32
exit
address "**************/32"
  ip **************/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********-139"
  range *********** 18.5.12.139
exit
address "************/32"
  ip ************/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "************/32"
  ip ************/32
exit
address "********/32"
  ip ********/32
exit
address "CSLC-***********/24"
  ip ***********/24
exit
address "CSLC-***********/24"
  ip ***********/24
exit
address "********5/32"
  ip ********5/32
exit
address "*********/32"
  ip *********/32
exit
address "T1-jiamiji-**********"
  ip **********/32
exit
address "JiKai_124.65.173.138"
  ip 124.65.173.138/32
exit
address "SuNingXiaoDian_221. 226.125.130"
  ip 221.226.125.130/32
exit
address "********4/32"
  ip ********4/32
exit
address "***********/32"
  ip ***********/32
exit
address "*************/32"
  ip *************/32
exit
address "CSLC-***********/24"
  ip ***********/24
exit
address "CSLC-***********/24"
  ip ***********/24
exit
address "*************/32"
  ip *************/32
exit
address "*************/32"
  ip *************/32
exit
address "***********/24"
  ip ***********/24
exit      
address "************/32"
  ip ************/32
exit
address "**********18/32"
  ip **********18/32
exit
address "**************/32"
  ip **************/32
exit
address "**************/32"
  ip **************/32
exit
address "*********/32"
  ip *********/32
exit
address "**************/32"
  ip **************/32
exit
address "************/32"
  ip ************/32
exit
address "*********/8"
  ip *********/8
exit
address "********/8"
  ip ********/8
exit
address "*********/8"
  ip *********/8
exit
address "***********/24"
  ip ***********/24
exit
address "SSL_GATEWAY-**************"
  ip **************/32
exit
address "*************/24"
  ip *************/24
exit
address "************/24"
  ip ************/24
exit
address "*************/32"
  ip *************/32
exit
address "**********/16"
  ip **********/16
exit
address "***********/24"
  ip ***********/24
exit
address "***********"
  ip ***********/32
exit
address "***********/24"
  ip ***********/24
exit
address "*************/24"
  ip *************/24
exit
address "************/24"
  ip ************/24
exit
address "********8/32"
  ip ********8/32
exit
address "**************/32"
  ip **************/32
exit
address "***************/32"
  ip ***************/32
exit
address "*********/24"
  ip *********/24
exit
address "**********/24"
  ip **********/24
exit
address "**********/24"
  ip **********/24
exit
address "Internet-**************"
  ip **************/32
exit
address "**************/26"
  ip **************/26
exit
address "***********/32"
  ip ***********/32
exit
address "********9/32"
  ip ********9/32
exit
address "********0/32"
  ip ********0/32
exit
address "**********/24"
  ip **********/24
exit
address "***********/32"
  ip ***********/32
exit
address "********3/32"
  ip ********3/32
exit
address "***********/32"
  ip ***********/32
exit
address "*************/32"
  ip *************/32
exit
address "********2/32"
  ip ********2/32
exit
address "************/32"
  ip ************/32
exit
address "***********/32"
  ip ***********/32
exit
address "***************/32"
  ip ***************/32
exit
address "************/32"
  ip ************/32
exit
address "47.105.221.226/32"
  ip 47.105.221.226/32
exit
address "104.21.4.110/32"
  ip 104.21.4.110/32
exit
address "*************/32"
  ip *************/32
exit
address "10.20.1.0/24"
  ip 10.20.1.0/24
exit
address "10.20.2.0/24"
  ip 10.20.2.0/24
exit
address "********7/32"
  ip ********7/32
exit
address "124.127.94.59/32"
  ip 124.127.94.59/32
exit
address "**********/24"
  ip **********/24
exit
address "***************/32"
  ip ***************/32
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "**********/24"
  ip **********/24
exit
address "*************/32"
  ip *************/32
exit
address "*************/32"
  ip *************/32
exit
address "*********2/32"
  ip *********2/32
exit
address "*************/24"
  ip *************/24
exit
address "*************/24"
  ip *************/24
exit
address "************/22"
  ip ************/22
exit
address "*************/17"
  ip *************/17
exit
address "**************/32"
  ip **************/32
exit
address "***********/32"
  ip ***********/32
exit
address "********18/32"
  ip ********18/32
exit
address "***************/32"
  ip ***************/32
exit
address "*************/32"
  ip *************/32
exit
address "**************/32"
  ip **************/32
exit
address "T3-*********-15"
  range ********* *********
exit      
address "T3-*********-15"
  range ********* *********
exit
address "***********/24"
  ip ***********/24
exit
address "********21"
  ip ********21/32
exit
address "*************00/32"
  ip *************00/32
exit
address "***********/24"
  ip ***********/24
exit
address "**********/24"
  ip **********/24
exit
address "VDI-TO-T3-Tiaobanji"
  range ********* *********
exit
address "VDI-TO-T19-Tiaobanji"
  range *********** ***********
exit
address "***************/32"
  ip ***************/32
exit
address "***************/32"
  ip ***************/32
exit
address "***********/32"
  ip ***********/32
exit
address "********8/32"
  ip ********8/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "********28/32"
  ip ********28/32
exit
address "**********/24"
  ip **********/24
exit
address "***********/24"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "*************/32"
  ip *************/32
exit
address "***********/32"
  ip ***********/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "********20/32"
  ip ********20/32
exit
address "************/32"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "18.2.1.34"
  ip 18.2.1.34/32
exit
address "104.21.0.220/32"
  ip 104.21.0.220/32
exit
address "104.24.0.101"
  ip 104.24.0.101/32
exit
address "192.144.193.39"
  ip 192.144.193.39/32
exit
address "test-1.1.1.1"
  ip 1.1.1.1/32
exit
address "**************/32"
  ip **************/32
exit
address "YINLIAN"
  ip 116.246.38.72/32
  ip 101.231.204.80/32
  ip 202.101.25.176/32
exit
address "104.24.0.104/32"
  ip 104.24.0.104/32
exit
address "104.24.0.141/32"
  ip 104.24.0.141/32
exit
address "*********0/32"
  ip *********0/32
exit
address "18.0.93.0/24"
  ip 18.0.93.0/24
exit
address "**********/24"
  ip **********/24
exit
address "104.23.11.4"
  ip 104.23.11.4/32
exit
address "104.24.0.136"
  ip 104.24.0.136/32
exit
address "18.5.10.1-51"
  range 18.5.10.1 18.5.10.51
exit
address "18.5.50.1-2"
  range 18.5.50.1 18.5.50.2
exit
address "124.193.71.186/32"
  ip 124.193.71.186/32
exit
address "10.213.0.200/32"
  ip 10.213.0.200/32
exit
address "47.93.16.203/32"
  ip 47.93.16.203/32
exit
address "104.24.0.31/32"
  ip 104.24.0.31/32
exit
address "CSLC-172.26.100.200/32"
  ip 172.26.100.200/32
exit
address "104.21.51.51/32"
  ip 104.21.51.51/32
exit
address "104.23.13.0/24"
  ip 104.23.13.0/24
exit
address "104.23.11.10/32"
  ip 104.23.11.10/32
exit
address "192.168.32.109/32"
  ip 192.168.32.109/32
exit
address "********6/32"
  ip ********6/32
exit
address "*********1-15"
  range *********1 *********5
exit
address "104.21.51.0/24"
  ip 104.21.51.0/24
exit
address "18.6.4.46"
  ip 18.6.4.46/32
exit
address "18.6.4.47"
  ip 18.6.4.47/32
exit
address "18.5.97.8"
  ip 18.5.97.8/32
exit
address "18.5.97.6"
  ip 18.5.97.6/32
exit
address "吉祥宝8.131.227.169"
  ip 8.131.227.169/32
exit
address "104.21.110.232/32"
  ip 104.21.110.232/32
exit
address "104.200.101.3/32"
  ip 104.200.101.3/32
exit
address "104.200.100.69/32"
  ip 104.200.100.69/32
exit
address "104.21.19.20/32"
  ip 104.21.19.20/32
exit
address "104.21.19.98/32"
  ip 104.21.19.98/32
exit
address "104.21.110.222/32"
  ip 104.21.110.222/32
exit
address "********1/32"
  ip ********1/32
exit
address "104.21.19.99/32"
  ip 104.21.19.99/32
exit
address "104.24.0.3/32"
  ip 104.24.0.3/32
exit
address "*********/16"
  ip *********/16
exit
address "********"
  ip ********/24
exit
address "10.216.3.0/24"
  ip 10.216.3.0/24
exit
address "T1-18.0.1.0/24"
  ip 18.0.1.0/24
exit
address "T2-28.0.1.0/24"
  ip 28.0.1.0/24
exit
address "T4-48.0.1.0/24"
  ip 48.0.1.0/24
exit
address "T5-58.0.1.0/24"
  ip 58.0.1.0/24
exit
address "18.6.4.48/32"
  ip 18.6.4.48/32
exit
address "123.57.49.51-吉林大乐透"
  ip 123.57.49.51/32
exit
address "104.11.1.0/32"
  ip 104.11.1.0/24
exit
address "10.213.5.241/32"
  ip 10.213.5.241/32
exit
address "wangliang-10.211.3.187/32"
  ip 10.211.3.187/32
exit
address "*********7"
  ip *********7/32
exit
address "*********8/32"
  ip *********8/32
exit
address "*********9/32"
  ip *********9/32
exit
address "104.21.19.53/32"
  ip 104.21.19.53/32
exit
address "TXY-10.215.41.178"
  ip 10.215.41.178/32
exit
address "10.215.41.170"
  ip 10.215.41.170/32
exit      
address "********38"
  ip ********38/32
exit
address "***********-147"
  range *********** 18.5.12.147
exit
address "***************"
  ip ***************/32
exit
address "10.216.31.0/24"
  ip 10.216.31.0/24
exit
address "************/32"
  ip 19.6.103.201/32
exit
address "W5RBOCC"
  ip 4.128.0.0/24
  ip 4.128.1.0/24
  ip 18.2.12.0/24
exit
address "10.20.4.0/24"
  ip 10.20.4.0/24
exit
address "************/24"
  ip ************/24
exit
address "10.213.3.0/24"
  ip 10.213.3.0/24
exit
address "192.168.200.0/24"
  ip 192.168.200.0/24
exit
address "shuangyinsu_test"
  ip 18.5.84.91/32
  ip 18.5.80.111/32
  ip 18.5.83.1/32
  ip 18.5.80.122/32
  ip 18.5.89.2/32
  ip *********2/32
exit
address "*************/32"
  ip *************/32
exit
address "**********11-118"
  range **********11 **********18
exit
address "10.219.0.161/32"
  ip 10.219.0.161/32
exit
address "10.219.0.164/32"
  ip 10.219.0.164/32
exit
address "10.211.3.116"
  ip 10.211.3.116/32
exit
address "192.168.213.88"
  ip 192.168.213.88/32
exit
address "T18-CDDING-NET-10.217-0&1.0-24"
  description "CODING address range"
  ip 10.217.0.0/24
  ip **********/24
exit
address "T18-PUBLICSERVICE-10.217.2.0-24"
  ip 10.217.2.0/24
exit
address "T18-MONITOR-10.217.3.0-24"
  ip 10.217.3.0/24
exit
address "T18-SEC-10.217.4.0/24"
  ip 10.217.4.0/24
exit
address "T18-MS-10.217.5.0-24"
  ip 10.217.5.0/24
exit
address "T18-DMZ-10.217.6.0-24"
  ip 10.217.6.0/24
exit
address "T18-VDI-**********-16-OUT"
  ip **********/16
exit
address "T18-LISHUAIQILINSHIWEIHU-************-24-OUT"
  ip ************/24
exit
address "T18-LISHUAIQILINSHIWEIHUA-***********-32"
  ip ***********/32
exit
address "104.23.15.0/24"
  ip 104.23.15.0/24
exit
address "104.11.1.0/24"
  ip 104.11.1.0/24
exit
address "104.21.51.41-46"
  range 104.21.51.41 104.21.51.46
exit
address "10.217.0.1/32-coding"
  ip 10.217.0.1/32
exit
address "10.213.0.9_G3zidonghuiTest"
  ip 10.213.0.9/32
exit
address "10.213.0.63_G3AutoTest"
  ip 10.213.0.63/32
exit
address "10.216.1.49_ads"
  ip 10.216.1.49/32
exit
address "104.23.14.0/24"
  ip 104.23.14.0/24
exit
address "CSL-office"
  member "CSLC-***********/24"
  member "CSLC-***********/24"
  member "CSLC-***********/24"
  member "CSLC-***********/24"
  member "CSLC-***********/24"
  ip 172.21.13.0/24
  ip ***********/24
  ip ***********/24
  ip 172.20.17.0/24
  ip 172.20.19.0/24
  ip 172.21.17.0/24
  ip **********/22
  ip 172.21.8.0/22
exit
address "YZ-10.217.129.0/24"
  ip 10.217.129.0/24
exit
address "10.217.2.0/24"
  ip 10.217.2.0/24
exit
address "YZ-10.218.129.0/24"
  ip 10.218.129.0/24
exit
address "VDI-10.217.129-133.0/24"
  ip 10.217.129.0/24
  ip 10.217.130.0/24
  ip 10.217.131.0/24
  ip 10.217.132.0/24
  ip 10.217.133.0/24
exit
address "T1-********38"
  ip ********38/32
exit
address "************"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "zabbix-**********1"
  ip **********1/32
exit
address "10.218.0.0/16"
  ip 10.218.0.0/16
exit
address "solarwids-***********"
  ip ***********/32
exit
address "10.219.4.253"
  ip 10.219.4.253/32
exit
address "10.217.6.1-2"
  range 10.217.6.1 10.217.6.2
exit
address "10.219.4.254"
  ip 10.219.4.254/32
exit
address "10.219.0.252"
  ip 10.219.0.252/32
exit
address "10.217.6.3"
  ip 10.217.6.3/32
exit
address "***********/32"
  ip ***********/32
exit
address "YZTESTLAB-104-SNAT-18.6.30.0/24"
  ip 18.6.30.0/24
exit
address "YZTESTLAB-10-SNAT-18.6.31.0/24"
  ip 18.6.31.0/24
exit
address "VC-172.16.23.1"
  ip 172.16.23.1/32
exit
address "10.214.2.0/24"
  ip 10.214.2.0/24
exit
address "Zidonghua"
  range 18.4.21.212 18.4.21.214
  range 18.0.1.7 ********0
exit
address "CSLOOffice"
  ip 10.248.145.1/32
  ip 10.248.133.3/32
exit
address "OBDB-10.220.4.4-6"
  range 10.220.4.4 10.220.4.6
exit
address "Ceshi_104.21.0.0/16"
  ip 104.21.0.0/16
exit      
address "10.211.11.0/24"
  ip 10.211.11.0/24
exit
address "10.211.3."
  ip **********/24
exit
address "NAS-************-202"
  range ************ ************
exit
address "K8SNode-18.5.74.1-5"
  range 18.5.74.1 18.5.74.5
exit
address "K8SNode-18.5.74.6-10"
  range 18.5.74.6 18.5.74.10
exit
address "K8SNode-18.5.82.1-5"
  range 18.5.82.1 18.5.82.5
exit
address "K8SNode-18.5.82.6-10"
  range 18.5.82.6 18.5.82.10
exit
address "K8SNode-18.5.82.11-15"
  range 18.5.82.11 18.5.82.15
exit
address "K8SNode-18.5.82.16-20"
  range 18.5.82.16 18.5.82.20
exit
address "K8SNode-18.5.82.21-25"
  range 18.5.82.21 18.5.82.25
exit
address "K8SNode-18.5.82.26-30"
  range 18.5.82.26 18.5.82.30
exit
address "K8SNode-18.5.82.31-35"
  range 18.5.82.31 18.5.82.35
exit
address "K8SNode-**********-40"
  range ********** **********
exit
address "Nexus-************-204"
  range ************ ************
exit
address "CSLC-Office_**********/22"
  ip **********/22
exit
address "**********/24"
  ip **********/24
exit
address "**********/24"
  ip **********/24
exit
address "***********/24"
  ip ***********/24
exit
address "**********/32"
  ip **********/32
exit
address "**********/24"
  ip **********/24
exit
address "************/24"
  ip ************/24
exit
address "AD_**********"
  ip *********/24
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "**********05/32"
  ip **********05/32
exit
address "***********/32"
  ip ***********/32
exit
address "**********/24"
  ip **********/24
exit
address "SMTP-**********"
  ip **********/32
exit
address "*************/32"
  ip *************/32
exit
address "**********-4"
  ip **********/32
  ip **********/32
  ip **********/32
  ip **********/32
exit
address "**********1-14"
  ip **********1/32
  ip **********2/32
  ip **********3/32
  ip **********4/32
exit
address "**********1-18"
  ip **********1/32
  ip **********2/32
  ip **********3/32
  ip **********4/32
  ip **********5/32
  ip **********6/32
  ip **********7/32
  ip **********8/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********-42"
  ip ***********/32
  ip ***********/32
exit
address "***********-65"
  range *********** ***********
exit
address "**********/*************/***********"
  ip **********/32
  ip *************/32
  ip ***********/32
exit
address "shujuzhongtai_**********/24"
  ip **********/24
exit      
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "quexiangaojing"
  ip **********50/32
  ip **********83/32
  range **********7 ***********
  range *********** ***********
  range *********** ***********
  range *********** ***********
exit
address "**********/22"
  ip **********/22
exit
address "***********/32"
  ip ***********/32
exit
address "**********/32"
  ip **********/32
exit
address "************/32"
  ip ************/32
exit
address "CSLC-***********/24"
  ip ***********/24
exit
address "CSLC-172.20.16-19.0/24"
  range *********** *************
exit
address "yun-**********/16"
  ip **********/16
exit
address "yun-**********/16"
  ip **********/16
exit
address "G3-**********/24"
  ip **********/24
exit
address "G3-**********/24"
  ip **********/24
exit
address "10.219.1.100_jiaobenzhixing"
  ip 10.219.1.100/32
exit
address "**********-3(guanli)"
  range ********** 10.217.1.3
exit
address "**********5_17_18(code ku)"
  ip **********5/32
  ip **********7/32
  ip **********8/32
exit
address "***********/32"
  ip ***********/32
exit
address "************/32"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "************/32"
  ip ************/32
exit
address "*************/32"
  ip *************/32
exit
address "G3BISMONTORCOLLECT"
  range 4.190.85.1 4.190.85.2
exit
address "***********"
  ip ***********/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "10.219.5.52_jiagoubu"
  ip 10.219.5.52/32
exit
address "Maven/vue"
  ip 10.219.0.163/32
  ip 10.219.0.165/32
  ip 10.219.0.166/32
  ip 10.219.0.167/32
exit
address "************/32"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "乐透归集库"
  ip ************/32
exit
address "T4-数据中台01"
  ip **********/24
  range 10.220.7.6 **********0
exit
address "**********/24"
  ip **********/24
exit
address "腾讯云10.216.40"
  ip ***********/24
exit
address "T4-**********/24"
  ip **********/24
exit
address "T3-**********/24"
  ip **********/24
exit
address "报表集市G32云上"
  ip 10.216.48.0/24
exit
address "ADS***********"
  ip ***********/32
exit
address "82_157_106.153"
  ip **************/32
exit
address "JCVSCC"
  ip 172.16.20.151/32
exit
address "111_204_38_0/24"
  ip 111.204.38.0/24
exit
address "10_104/8"
  ip 10.0.0.0/8
  ip *********/8
exit
address "T3_体彩APP"
  ip 18.6.23.26/32
exit
address "10/8"
  ip 10.0.0.0/8
exit
address "************"
  ip ************/32
exit
address "*********/32"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "T3_***********to217"
  range *********** ***********
exit
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
  ip 100.100.100.1/32
exit
address "************"
  ip 10.217.0.0/21
exit
address "需求管理工具***********"
  ip ***********/32
exit
address "ELP18.5.32.211-217"
  range 18.5.32.211 ***********
exit
address "**********"
  ip **********/32
exit
address "ELP18.5.32.202-217"
  range 8.5.32.202 8.5.32.217
exit
address "**********&41"
exit
address "************"
  ip ************/32
exit
address "**********"
  host "**********"
exit
address "**********"
  host "**********"
exit
address "*********1"
  ip *********1/32
exit      
address "*********1"
  ip *********1/32
exit
address "*************"
  ip *************/32
exit
address "**********/24"
  ip **********/24
exit
address "**********"
  ip **********/32
exit
address "VDI-LINUXSERVER"
  ip **********05/32
  ip **********09/32
  ip ************/32
  ip 10.219.5.52/32
  ip ************/32
exit
address "V_RMOAS18.6.114.252"
  ip 18.6.114.252/32
exit
address "R_WCS18.5.70.51"
  ip 18.5.70.51/32
exit
address "10.220.7.6_25"
  range 10.220.7.6 **********5
exit
address "**********"
  ip **********/32
exit
address "10.211.5&6&12.0/24"
  ip 10.211.5.0/24
  ip 10.211.6.0/24
  ip 10.211.12.0/24
exit
address "SMTP-**********"
  ip **********/32
exit
address "VDI-10.217.134-140.0/24"
  ip 10.217.134.0/24
  ip 10.217.135.0/24
  ip 10.217.136.0/22
  ip 10.217.140.0/24
exit
address "************"
  description "本地终端（赵高阳）"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "***********-178"
  ip ***********/32
  ip ***********/32
  ip ***********/32
exit
address "************/32"
  ip ************/32
exit
address "**********11/32"
  ip **********11/32
exit
address "***********/24"
  ip ***********/24
exit
address "DEV_DESK"
  ip 10.210.6.101/32
exit
address "SHUJUJIANMO-TOOLS"
  range 10.219.1.227 10.219.1.229
exit
address "YUNYINGVDI-138"
  ip 10.217.138.0/24
exit
address "*********1"
  ip *********1/32
exit
address "*************-168"
  range ************* 104.21.55.168
exit
address "**************-*************"
  ip *************/32
  ip **************/32
exit
address "***********"
  ip ***********/32
exit
address "T4OB10.220.0.173"
  ip 10.220.0.173/32
exit
address "************"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "************/24"
  ip ************/24
exit
address "**********/24"
  ip **********/24
exit
address "************"
  ip ************/32
exit
address "BT"
  range ******** ********
exit
address "***********"
  ip ***********/32
exit
address "ACLLB-SNAT"
  ip 10.218.47.51/32
  ip 10.218.47.52/32
  ip 10.218.47.53/32
  ip 10.218.47.54/32
  ip 10.218.47.55/32
  ip 10.218.47.56/32
exit
address "ACLLB-SELF"
  ip 10.218.20.233/32
exit
address "************/24"
  ip ************/24
exit
address "***********"
  ip ***********/32
exit
address "************/24"
  ip ************/24
exit
address "**********"
  ip **********/32
exit
address "**********-1.0/24"
  ip **********/24
  ip 10.219.1.0/24
exit
address "**********"
  ip **********/16
exit
address "***********/16"
  ip ***********/16
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "*********"
  ip ***********/32
  ip 4.99.10.172/32
  ip 4.99.10.173/32
  ip *********/32
exit
address "***********-14"
  ip ***********/32
  ip 10.217.7.12/32
  ip 10.217.7.13/32
  ip 10.217.7.14/32
exit
address "**********-4"
  ip **********/32
  ip 10.217.0.3/32
  ip 10.217.0.4/32
exit
address "***********-19"
  ip ***********/32
  ip 10.217.7.19/32
exit
address "***********-173"
  ip ***********/32
  ip 4.99.10.172/32
  ip 4.99.10.173/32
exit
address "*******/24"
  ip *******/24
exit
address "***********"
  ip ***********/32
exit
address "************"
  ip ************/32
exit
address "*********"
  ip *********/32
  ip ***********/32
exit
address "*********/16"
  ip *********/16
exit
address "***********"
  ip ***********/32
exit
address "***********/张跃的VDI测试"
  ip ***********/32
exit
address "**********"
  ip **********/24
exit
address "***************"
  ip ***************/32
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "***********-15"
  range *********** 10.212.1.15
exit
address "*************-20"
  range ************* 10.220.130.20
exit
address "*********"
  ip *********/32
exit
address "***********"
  ip ***********/32
exit
address "***********/24"
  ip ***********/24
exit
address "10.220.129.1/32"
  ip 10.220.129.1/32
exit
address "10.220.132.101-106"
  range 10.220.132.101 10.220.132.106
exit
address "104.23.20.220-222"
  range 104.23.20.220 104.23.20.222
exit
address "10.220.3.50"
  ip 10.220.3.50/32
exit
address "************"
  ip ************/24
exit
address "***********/32"
  ip ***********/32
exit
address "18.0.160.1/32"
  ip 18.0.160.1/32
exit
address "10.216.71.0"
  ip 10.216.71.0/24
exit
address "18.5.81.5"
  ip 18.5.81.5/32
exit
address "10.129.4.135"
  ip 10.129.4.135/32
exit
address "***********"
  ip ***********/32
exit
address "10.219.5.159"
  ip 10.219.5.159/32
exit
address "10.248.121.1"
  ip 10.248.121.1/32
exit
address "**************"
  ip **************/32
exit
address "**********02"
  ip **********02/32
exit
address "***********-30"
  range *********** ***********
exit
address "********40"
  ip ********40/32
exit
address "*********4"
  ip *********4/32
exit
address "*********5"
  ip *********5/32
exit
address "***********/24"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "**********"
  ip **********/32
exit
address "**********-42"
  ip **********/32
  ip **********/32
exit
address "***********/24"
  ip ***********/24
exit
address "**********-22"
  ip **********/32
  ip **********/32
exit
address "********/24"
  ip ********/24
exit
address "**********"
  ip **********/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/24
exit
address "**********"
  ip **********/32
exit
address "***********-72"
  range *********** ***********
exit
address "*************"
  ip *************/32
exit
address "**********-16"
  range ********** ***********
exit
address "***********"
  ip ***********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********6"
  ip **********6/32
exit
address "**********8"
  ip **********8/32
exit
address "新机器开通到运行ca以及radk服务的端口"
  ip **********/32
  ip **********/32
  ip **********6/32
  ip **********8/32
exit
address "新机器开通到KM服务的端口"
  ip **********/32
  ip **********/32
  ip **********7/32
exit
address "新机器开通到运行ca、km服务以及radk服务的端口"
  ip ***********/32
exit
address "新机器开通到运行ca、km数据库服务的端口"
  range ********** **********
exit
address "新机器开通到新ldap和ibmldap服务的端口"
  range **********0 **********5
exit
address "新机器开通到新ldap和oracle服务的端口"
  range ********** **********
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
exit      
zone "l2-untrust" l2
  type wan
exit
zone "T1-OUTSIDE"
  vrouter "T1"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T1-INSIDE"
  vrouter "T1"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T2-OUSIDE"
  vrouter "T2"
exit
zone "T2-OUTSIDE"
  vrouter "T2"
exit
zone "T2-INSIDE"
  vrouter "T2"
exit
zone "T3-OUTSIDE"
  vrouter "T3"
exit
zone "T3-INSIDE"
  vrouter "T3"
exit
zone "T4-OUTSIDE"
  vrouter "T4"
exit
zone "T4-INSIDE"
  vrouter "T4"
exit
zone "T5-OUTSIDE"
  vrouter "T5"
exit
zone "T5-INSIDE"
  vrouter "T5"
exit
zone "T6-OUTSIDE"
  vrouter "T6"
exit
zone "T6-INSIDE"
  vrouter "T6"
exit
zone "T7-OUTSIDE"
  vrouter "T7"
exit
zone "T7-INSIDE"
  vrouter "T7"
exit
zone "T8-OUTSIDE"
  vrouter "T8"
exit
zone "T8-INSIDE"
  vrouter "T8"
exit
zone "T9-OUTSIDE"
  vrouter "T9"
exit
zone "T9-INSIDE"
  vrouter "T9"
exit
zone "T10-OUTSIDE"
  vrouter "T10"
exit
zone "T10-INSIDE"
  vrouter "T10"
exit
zone "T11-OUTSIDE"
  vrouter "T11"
exit
zone "T11-INSIDE"
  vrouter "T11"
exit
zone "T12-OUTSIDE"
  vrouter "T12"
exit
zone "T12-INSIDE"
  vrouter "T12"
exit
zone "T13-OUTSIDE"
  vrouter "T13"
exit
zone "T13-INSIDE"
  vrouter "T13"
exit
zone "T14-OUTSIDE"
  vrouter "T14"
exit
zone "T14-INSIDE"
  vrouter "T14"
exit
zone "T15-OUTSIDE"
  vrouter "T15"
exit
zone "T15-INSIDE"
  vrouter "T15"
exit
zone "T16-OUTSIDE"
  vrouter "T16"
exit
zone "T16-INSIDE"
  vrouter "T16"
exit
zone "T17-OUTSIDE"
  vrouter "T17"
exit
zone "T17-INSIDE"
  vrouter "T17"
exit
zone "T18-OUTSIDE"
  vrouter "T18"
exit
zone "T18-INSIDE"
  vrouter "T18"
exit
zone "T19-OUTSIDE"
  vrouter "T19"
exit
zone "T19-INSIDE"
  vrouter "T19"
exit
zone "T20-OUTSIDE"
  vrouter "T20"
exit
zone "T20-INSIDE"
  vrouter "T20"
exit
zone "T21-OUTSIDE"
  vrouter "T21"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T21-INSIDE"
  vrouter "T21"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T22-OUTSIDE"
  vrouter "T22"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T22-INSIDE"
  vrouter "T22"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T23-INSIDE"
  vrouter "T23"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T23-OUTSIDE"
  vrouter "T23"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T24-INSIDE"
  vrouter "T24"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T24-OUTSIDE"
  vrouter "T24"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T25-INSIDE"
  vrouter "T25"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T25-OUTSIDE"
  vrouter "T25"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T26-INSIDE"
  vrouter "T26"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T26-OUTSIDE"
  vrouter "T26"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T27-INSIDE"
  vrouter "T27"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T27-OUTSIDE"
  vrouter "T27"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit      
zone "T28-INSIDE"
  vrouter "T28"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T28-OUTSIDE"
  vrouter "T28"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T29-INSIDE"
  vrouter "T29"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T29-OUTSIDE"
  vrouter "T29"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T30-INSIDE"
  vrouter "T30"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "T30-OUTSIDE"
  vrouter "T30"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
hostname "XWHTS-NC1COL-FW01"
snmp-server location "XWH-B3A03-17-18U"
admin host any any
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit

isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit
          
ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface MGT0
  zone  "mgt"
  ip address *********** *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet0/8
  aggregate aggregate1
exit
interface xethernet0/9
  aggregate aggregate1
exit
interface xethernet4/0
  aggregate aggregate1
exit
interface xethernet4/1
  aggregate aggregate1
exit
interface aggregate1
  zone  "l2-trust"
exit
interface aggregate1.3080
  zone  "T1-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3081
  zone  "T2-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3082
  zone  "T3-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3083
  zone  "T4-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3084
  zone  "T5-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3085
  zone  "T6-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3086
  zone  "T7-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3087
  zone  "T8-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3088
  zone  "T9-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3089
  zone  "T10-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3090
  zone  "T11-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3091
  zone  "T12-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3092
  zone  "T13-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3093
  zone  "T14-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3094
  zone  "T15-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3095
  zone  "T16-INSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3096
  zone  "T17-INSIDE"
  ip address ********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3097
  zone  "T18-INSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3098
  zone  "T19-INSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3099
  zone  "T20-INSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit      
interface aggregate1.3180
  zone  "T1-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3181
  zone  "T2-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3182
  zone  "T3-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3183
  zone  "T4-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3184
  zone  "T5-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3185
  zone  "T6-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3186
  zone  "T7-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3187
  zone  "T8-OUTSIDE"
  ip address ***********2 ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3188
  zone  "T9-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3189
  zone  "T10-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3190
  zone  "T11-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3191
  zone  "T12-OUTSIDE"
  ip address ************ ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3192
  zone  "T13-OUTSIDE"
  ip address ********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3193
  zone  "T14-OUTSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3194
  zone  "T15-OUTSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3195
  zone  "T16-OUTSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3196
  zone  "T17-OUTSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3197
  zone  "T18-OUTSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3198
  zone  "T19-OUTSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3199
  zone  "T20-OUTSIDE"
  ip address *********** ***************
  manage telnet
  manage ssh
  manage ping
  manage http
  manage https
  reverse-route prefer
exit
interface aggregate1.3143
  zone  "T21-INSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3144
  zone  "T22-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3145
  zone  "T23-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3146
  zone  "T24-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3147
  zone  "T25-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3148
  zone  "T26-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3149
  zone  "T27-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3151
  zone  "T29-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3152
  zone  "T30-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3150
  zone  "T28-INSIDE"
  ip address ************ ***************
  reverse-route prefer
exit
interface aggregate1.3153
  zone  "T21-OUTSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3154
  zone  "T22-OUTSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3155
  zone  "T23-OUTSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3156
  zone  "T24-OUTSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3157
  zone  "T25-OUTSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3158
  zone  "T26-OUTSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3159
  zone  "T27-OUTSIDE"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3160
  zone  "T28-OUTSIDE"
  ip address ********** ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3161
  zone  "T29-OUTSIDE"
  ip address *********** ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.3162
  zone  "T30-OUTSIDE"
  ip address *********** ***************
  manage ping
  reverse-route prefer
exit
ip vrouter "trust-vr"
  ip route 0.0.0.0/0 ************
  ip route 0.0.0.0/0 ***********
exit
ip vrouter "T1"
  ip route 0.0.0.0/0 ************ description "TANENT01 OUTSIDE DEFAULT"
  router ospf 1100
    router-id ************
    default-information originate always metric 2
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T2"
  router ospf 1200
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T3"
  snatrule id 244 from "T3_***********to217" to "**********/32" service "Any" trans-to ********48 mode dynamicport log 
  snatrule id 201 from "*************/24" to "**********/16" service "Any" trans-to *********9 mode dynamicport 
  snatrule id 183 from "T3_GW_*********-18" to "*************/24" service "Any" trans-to ********48 mode dynamicport log 
  snatrule id 103 from "T3_GW_*********-18" to "*********/24" service "Any" trans-to ********33 mode dynamicport log 
  snatrule id 124 from "**********18" to "**************" service "Any" trans-to ********32 mode dynamicport log 
  snatrule id 140 from "**********/24" to "*********/24" service "Any" trans-to ********00 mode dynamicport log 
  snatrule id 1 from "T3-TESTCLIET-TOINTERNET" to "Any" service "Any" trans-to address-book "T3 SNAT-INTERNET- POOL" mode dynamicport 
  snatrule id 2 from "Any" to "**********" service "Any" trans-to ********29 mode dynamicport 
  snatrule id 3 from "Any" to "**********" service "Any" trans-to ********28 mode dynamicport 
  snatrule id 4 from "Any" to "**********" service "Any" trans-to ********30 mode dynamicport log 
  snatrule id 6 from "Any" to "**********" service "Any" trans-to ********31 mode dynamicport 
  snatrule id 9 from "T3_GW_*********-18" to "***************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 18 from "T3_GW_*********-18" to "**************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 24 from "T3_GW_*********-18" to "*************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 21 from "T3_GW_*********-18" to "************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 19 from "**********/24" to "************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 10 from "T3_GW_*********-18" to "**************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 27 from "T3_GW_*********-18" to "************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 31 from "T3_GW_*********-18" to "***********/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 33 from "T3_GW_*********-18" to "**********/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 11 from "T3_GW_*********-18" to "T3-CSLAPP" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 22 from "T3_GW_*********-18" to "************" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 38 from "T3_GW_*********-18" to "123.125.97.251" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 40 from "T3_GW_*********-18" to "**************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 52 from "T3_GW_*********-18" to "38.0.0.0/8" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 20 from "**********/24" to "************" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 34 from "**********/24" to "104.24.0.0/24" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 39 from "**********/24" to "123.125.97.251" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 41 from "**********/24" to "**************" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 12 from "Any" to "***************" service "Any" trans-to ********35 mode dynamicport 
  snatrule id 13 from "Any" to "192.168.181.221" service "Any" trans-to ********36 mode dynamicport 
  snatrule id 14 from "*************/24" to "500wan_JCWeb_DXWG" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 32 from "*************/24" to "***************" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 35 from "*************/24" to "104.24.0.0/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 56 from "*************/24" to "104.255.225.1" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 16 from "Any" to "**********" service "Any" trans-to ********38 mode dynamicport 
  snatrule id 17 from "Any" to "**********" service "Any" trans-to ********39 mode dynamicport 
  snatrule id 23 from "Any" to "**********" service "Any" trans-to ********40 mode dynamicport 
  snatrule id 25 from "***********" to "***************" service "Any" trans-to ********41 mode dynamicport 
  snatrule id 26 from "***********" to "***************" service "Any" trans-to ********41 mode dynamicport 
  snatrule id 28 from "**********" to "***********" service "Any" trans-to ********42 mode dynamicport 
  snatrule id 29 from "ELP-***********-147" to "**********/24" service "Any" trans-to ********43 mode dynamicport 
  snatrule id 30 from "***********" to "*************" service "Any" trans-to ********44 mode dynamicport 
  snatrule id 37 from "Any" to "203.119.206.132" service "Any" trans-to ********45 mode dynamicport 
  snatrule id 42 from "T3_GW_*********-18" to "172.26.12.244" service "Any" trans-to ********46 mode dynamicport 
  snatrule id 43 from "ELP-***********-147" to "**********/24" service "Any" trans-to ********47 mode dynamicport 
  snatrule id 44 from "ELP-***********-147" to "***********" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 45 from "ELP-***********-147" to "***********" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 46 from "**********/24" to "***********/24" service "Any" trans-to ********49 mode dynamicport log 
  snatrule id 47 from "T3_GW_*********-18" to "************" service "Any" trans-to ********46 mode dynamicport 
  snatrule id 48 from "G3-Information publishing platform" to "GSM" service "Any" trans-to *********0 mode dynamicport 
  snatrule id 51 from "Bisab-*********-45" to "***********" service "Any" trans-to *********1 mode dynamicport 
  snatrule id 53 from "***********" to "38.0.0.0/8" service "Any" trans-to *********2 mode dynamicport 
  snatrule id 54 from "ELP-***********-147" to "38.0.0.0/8" service "Any" trans-to *********3 mode dynamicport 
  snatrule id 55 from "***********" to "38.0.0.0/8" service "Any" trans-to *********3 mode dynamicport 
  snatrule id 57 from "**********/24" to "**************" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 58 from "**********" to "***********" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 59 from "***********" to "**********/23" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 60 from "***********-139" to "**********/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 61 from "***********-139" to "***********" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 62 from "***********-139" to "***********" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 93 from "*********/24" to "***********" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 63 from "***********-139" to "**********" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 65 from "T3_GW_*********-18" to "************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 64 from "T3_GW_*********-18" to "***********/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 66 from "T3_GW_*********-18" to "***********/24" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 67 from "T3_GW_*********-18" to "*************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 68 from "*************/24" to "*************" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 69 from "T3_GW_*********-18" to "***********/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 70 from "T3_GW_*********-18" to "104.21.53.245" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 71 from "T3_GW_*********-18" to "***********/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 72 from "**********/24" to "**************" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 73 from "**********" to "**********/24" service "Any" trans-to ********42 mode dynamicport 
  snatrule id 112 from "18.0.4.0/24" to "**********/24" service "Any" trans-to ********42 mode dynamicport 
  snatrule id 75 from "**********/24" to "172.26.22.0/24" service "Any" trans-to ********49 mode dynamicport 
  snatrule id 76 from "**********/24" to "*********/8" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 77 from "**********/24" to "********/8" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 78 from "**********/24" to "*********/8" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 79 from "**********/24" to "*********/8" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 92 from "**********/24" to "105.106.61.95" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 91 from "**********/24" to "172.230.178.235" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 74 from "*************/24" to "**************" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 80 from "***********-139" to "***********/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 81 from "*************/24" to "***********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 82 from "Any" to "104.24.0.0/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 83 from "**********/24" to "*************/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 84 from "**********/24" to "************/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 85 from "*********/24" to "**********/24" service "Any" trans-to ********42 mode dynamicport 
  snatrule id 86 from "*********/24" to "**********/24" service "Any" trans-to ********42 mode dynamicport 
  snatrule id 87 from "T3_GW_*********-18" to "**********/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 88 from "T3_GW_*********-18" to "**********/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 89 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 95 from "18.5.57.1/32" to "78.1.15.101/32" service "Any" trans-to *********5 mode dynamicport 
  snatrule id 96 from "ELP-***********-147" to "**********/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 97 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 98 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 99 from "18.0.7.250" to "172.16.20.2" service "Any" trans-to *********6 mode dynamicport 
  snatrule id 100 from "18.0.1.7" to "********/24" service "Any" trans-to *********7 mode dynamicport 
  snatrule id 105 from "Any" to "*********/24" service "Any" trans-to *********8 mode dynamicport 
  snatrule id 106 from "ELP-***********-147" to "CSLC_***********/24" service "Any" trans-to ********43 mode dynamicport 
  snatrule id 107 from "**********/24" to "***********/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 8 from "**********/24" to "172.26.5.0/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 108 from "**********/24" to "*************" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 109 from "*********/24" to "***********" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 110 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 113 from "T3_GW_*********-18" to "124.127.94.59" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 114 from "18.5.32.51" to "124.127.94.59" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 115 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 116 from "T3_GW_*********-18" to "***************" service "Any" trans-to ********46 mode dynamicport 
  snatrule id 117 from "T3_GW_*********-18" to "**********/24" service "Any" trans-to *********5 mode dynamicport 
  snatrule id 118 from "*********/24" to "**********/24" service "Any" trans-to *********5 mode dynamicport 
  snatrule id 119 from "*************/24" to "**********/24" service "Any" trans-to *********5 mode dynamicport 
  snatrule id 120 from "***********" to "106.52.153.206" service "Any" trans-to ********48 mode static 
  snatrule id 121 from "*********/24" to "***********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 122 from "*********/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 123 from "*********/24" to "104.23.3.0/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 127 from "T3_GW_*********-18" to "*************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 125 from "T3_GW_*********-18" to "Any" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 135 from "**********/24" to "58.240.29.244" service "Any" trans-to ********34 mode dynamicport log 
  snatrule id 126 from "**********18" to "Any" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 129 from "T3-*********-15" to "*************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 130 from "T3-*********-15" to "*************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 131 from "*************/24" to "***********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 132 from "**********/24" to "18.1.3.30" service "Any" trans-to ********00 mode dynamicport log 
  snatrule id 133 from "**********/24" to "38.0.0.0/8" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 134 from "18.4.4.21" to "*************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 136 from "*************/24" to "***********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 137 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 138 from "*************/24" to "***********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 139 from "*************/24" to "***********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 141 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 142 from "18.4.3.102" to "*************" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 143 from "**********/24" to "18.5.97.0/24" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 144 from "18.0.1.7" to "********83" service "Any" trans-to *********7 mode dynamicport log 
  snatrule id 145 from "*************/24" to "104.21.0.0/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 146 from "18.0.1.7" to "***********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 147 from "*************/24" to "**********/24" service "Any" trans-to 10.248.254.1 mode dynamicport 
  snatrule id 148 from "18.5.10.0/24" to "************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 149 from "18.5.50.0/24" to "************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 150 from "*************/24" to "************" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 151 from "*************/24" to "104.24.0.0/24" service "Any" trans-to ********46 mode dynamicport 
  snatrule id 152 from "18.5.10.1-51" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 153 from "18.5.50.1-2" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 239 from "*************/24" to "10.213.31.2/32" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 154 from "*************/24" to "**********/16" service "Any" trans-to 10.248.254.2 mode dynamicport 
  snatrule id 155 from "T3_GW_*********-18" to "10.213.0.200" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 156 from "**********/24" to "104.23.13.0/24" service "Any" trans-to ********34 mode dynamicport 
  snatrule id 157 from "18.5.32.41" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 158 from "ELP-***********-147" to "104.21.110.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 159 from "ELP-***********-147" to "104.21.19.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 160 from "ELP-***********-147" to "104.200.0.0/16" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 161 from "*************/24" to "104.21.19.0/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 162 from "*************/24" to "104.200.100.0/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 172 from "*************/24" to "104.21.51.0/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 173 from "*************/24" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 175 from "*************/24" to "106.52.153.206" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 176 from "***********-147" to "104.21.17.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 177 from "***********-147" to "104.21.110.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 184 from "18.0.7.250" to "172.16.20.6" service "Any" trans-to *********6 mode dynamicport 
  snatrule id 185 from "18.5.32.41" to "*************/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 186 from "ELP-***********-147" to "10.20.4.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 187 from "18.5.32.41" to "10.216.19.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 188 from "18.5.32.41" to "10.216.31.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 189 from "18.5.55.1" to "104.200.100.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 190 from "18.5.55.1" to "*************/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 193 from "**********/24" to "10.216.42.8" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 194 from "18.5.55.1" to "**********/16" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 195 from "18.5.55.1" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 196 from "18.5.55.1" to "**********/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 197 from "18.5.55.1" to "104.21.17.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 200 from "18.0.1.7" to "**********/16" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 203 from "18.0.1.7" to "10.213.3.0/24" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 204 from "18.5.55.1" to "104.21.99.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 207 from "18.0.1.7" to "192.168.200.3" service "Any" trans-to *********7 mode dynamicport 
  snatrule id 208 from "**********/24" to "**********/16" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 209 from "ELP-***********-147" to "10.20.2.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 210 from "ELP-***********" to "10.20.5.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 211 from "**********/24" to "**********/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 212 from "**********/24" to "**********/24" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 215 from "**********/24" to "Any" service "Any" trans-to *********4 mode dynamicport 
  snatrule id 216 from "18.5.32.41" to "10.216.5.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 234 from "18.5.57.0/24" to "104.22.9.42" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 235 from "18.5.57.0/24" to "10.216.5.0/24" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 240 from "**********/24" to "10.216.50.17/32" service "Any" trans-to *********4/32 mode dynamicport 
  snatrule id 243 from "ELP18.5.32.211-217" to "AD_**********" service "Any" trans-to ********48 mode dynamicport 
  snatrule id 245 from "ELP18.5.32.211-217" to "************" service "Any" trans-to ********48 mode dynamicport 
  dnatrule id 122 from "Any" to "*********" service "TCP-3389" trans-to "18.0.2.177" port 3389 
  dnatrule id 193 from "***************" to "*********" service "Any" trans-to "**********" 
  dnatrule id 197 from "*************" to "*********" service "Any" trans-to "**********" 
  dnatrule id 194 from "***************" to "********" service "Any" trans-to "**********" 
  dnatrule id 196 from "*************" to "********" service "Any" trans-to "**********" 
  dnatrule id 195 from "Any" to "*********" service "TCP_6001" trans-to "**********" port 7001 
  dnatrule id 1 from "Any" to "********" service "Any" trans-to "*************" description "T3 transfer desk" 
  dnatrule id 2 from "Any" to "********" service "Any" trans-to "***************" log 
  dnatrule id 3 from "Any" to "********" service "Any" trans-to "*************" description "T3-harbor" 
  dnatrule id 40 from "Any" to "********" service "Any" trans-to "**********" disable 
  dnatrule id 41 from "Any" to "********" service "Any" trans-to "*************2" 
  dnatrule id 42 from "Any" to "********" service "Any" trans-to "**********" 
  dnatrule id 43 from "Any" to "********" service "Any" trans-to "**********" 
  dnatrule id 44 from "Any" to "********" service "Any" trans-to "**********" 
  dnatrule id 45 from "Any" to "********" service "Any" trans-to "**********" 
  dnatrule id 47 from "Any" to "********1" service "Any" trans-to "**********" 
  dnatrule id 48 from "Any" to "********2" service "Any" trans-to "**********" 
  dnatrule id 49 from "Any" to "********3" service "Any" trans-to "**********" 
  dnatrule id 50 from "Any" to "********4" service "Any" trans-to "**********" log 
  dnatrule id 51 from "Any" to "*********" service "TCP-8001" trans-to "**********" port 7001 
  dnatrule id 240 from "Any" to "*********/32" service "TCP-8553" trans-to "**********/32" port 7001 
  dnatrule id 46 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 53 from "Any" to "********6" service "Any" trans-to "18.5.57.12" 
  dnatrule id 54 from "Any" to "********7" service "TCP-32514" trans-to "18.5.57.13" port 21514 log 
  dnatrule id 55 from "Any" to "********8" service "Any" trans-to "18.5.9.4" 
  dnatrule id 56 from "Any" to "********9" service "Any" trans-to "18.5.9.8" 
  dnatrule id 57 from "Any" to "********0" service "Any" trans-to "18.5.9.14" 
  dnatrule id 58 from "Any" to "********1" service "Any" trans-to "18.5.9.104" 
  dnatrule id 105 from "Any" to "********1" service "Any" trans-to "18.5.32.68" 
  dnatrule id 59 from "Any" to "********2" service "Any" trans-to "18.5.9.144" 
  dnatrule id 60 from "Any" to "********3" service "Any" trans-to "18.5.8.4" 
  dnatrule id 61 from "Any" to "********4" service "Any" trans-to "18.5.8.8" 
  dnatrule id 62 from "Any" to "********5" service "Any" trans-to "18.5.8.14" 
  dnatrule id 63 from "Any" to "********6" service "Any" trans-to "18.5.8.18" 
  dnatrule id 64 from "Any" to "********7" service "Any" trans-to "18.5.8.24" 
  dnatrule id 65 from "Any" to "********8" service "Any" trans-to "18.5.8.34" 
  dnatrule id 66 from "Any" to "********9" service "Any" trans-to "18.5.8.38" 
  dnatrule id 67 from "Any" to "********0" service "Any" trans-to "18.5.8.44" 
  dnatrule id 68 from "Any" to "********1" service "Any" trans-to "18.5.8.48" 
  dnatrule id 69 from "Any" to "********2" service "Any" trans-to "18.5.8.54" 
  dnatrule id 70 from "Any" to "********3" service "Any" trans-to "18.5.8.58" 
  dnatrule id 71 from "Any" to "********4" service "Any" trans-to "18.5.8.64" 
  dnatrule id 72 from "Any" to "********5" service "Any" trans-to "18.5.8.68" 
  dnatrule id 73 from "Any" to "********6" service "Any" trans-to "18.5.8.74" 
  dnatrule id 74 from "Any" to "********7" service "Any" trans-to "18.5.8.84" 
  dnatrule id 75 from "Any" to "********8" service "Any" trans-to "18.5.8.88" 
  dnatrule id 76 from "Any" to "********9" service "Any" trans-to "18.5.8.94" 
  dnatrule id 77 from "Any" to "********0" service "Any" trans-to "18.5.8.98" 
  dnatrule id 78 from "Any" to "********1" service "Any" trans-to "18.5.8.104" 
  dnatrule id 79 from "Any" to "********2" service "Any" trans-to "18.5.8.114" 
  dnatrule id 80 from "Any" to "********3" service "Any" trans-to "18.5.8.118" 
  dnatrule id 81 from "Any" to "********4" service "Any" trans-to "18.5.8.124" 
  dnatrule id 82 from "Any" to "********5" service "Any" trans-to "18.5.8.128" 
  dnatrule id 83 from "Any" to "********6" service "Any" trans-to "18.5.8.134" 
  dnatrule id 84 from "Any" to "********7" service "Any" trans-to "18.5.8.138" 
  dnatrule id 85 from "Any" to "********8" service "Any" trans-to "18.5.9.144" 
  dnatrule id 86 from "Any" to "********9" service "Any" trans-to "18.5.8.148" 
  dnatrule id 87 from "Any" to "********0" service "Any" trans-to "18.5.8.154" 
  dnatrule id 88 from "Any" to "********1" service "Any" trans-to "18.5.8.164" 
  dnatrule id 89 from "Any" to "********2" service "Any" trans-to "18.5.8.174" 
  dnatrule id 90 from "Any" to "********3" service "Any" trans-to "18.5.8.178" 
  dnatrule id 91 from "Any" to "********4" service "Any" trans-to "18.5.8.184" 
  dnatrule id 92 from "Any" to "********5" service "Any" trans-to "18.5.8.188" 
  dnatrule id 93 from "Any" to "********6" service "Any" trans-to "18.5.8.194" 
  dnatrule id 94 from "Any" to "********7" service "Any" trans-to "18.5.8.198" 
  dnatrule id 95 from "Any" to "********8" service "Any" trans-to "18.5.8.204" 
  dnatrule id 96 from "Any" to "********9" service "Any" trans-to "18.5.8.208" 
  dnatrule id 97 from "Any" to "********0" service "Any" trans-to "18.5.8.214" 
  dnatrule id 98 from "Any" to "********1" service "Any" trans-to "18.5.8.224" 
  dnatrule id 99 from "Any" to "********2" service "Any" trans-to "18.5.8.228" 
  dnatrule id 100 from "Any" to "********3" service "Any" trans-to "18.5.8.234" 
  dnatrule id 101 from "Any" to "********4" service "Any" trans-to "18.5.8.238" 
  dnatrule id 102 from "Any" to "********5" service "Any" trans-to "18.5.8.244" 
  dnatrule id 103 from "Any" to "********6" service "Any" trans-to "18.5.14.114" 
  dnatrule id 148 from "Any" to "********7" service "Any" trans-to "18.5.32.51" 
  dnatrule id 106 from "Any" to "********8" service "Any" trans-to "18.5.57.31" 
  dnatrule id 107 from "Any" to "********9" service "Any" trans-to "18.5.57.18" 
  dnatrule id 108 from "Any" to "********0" service "Any" trans-to "18.5.57.19" 
  dnatrule id 109 from "Any" to "********1" service "Any" trans-to "18.5.57.32" log 
  dnatrule id 110 from "Any" to "********2" service "Any" trans-to "18.5.9.124" 
  dnatrule id 20 from "Any" to "********3" service "Any" trans-to "18.5.8.88" 
  dnatrule id 21 from "Any" to "********4" service "Any" trans-to "18.5.8.84" 
  dnatrule id 22 from "Any" to "********5" service "Any" trans-to "18.5.8.94" 
  dnatrule id 117 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 118 from "Any" to "********7" service "Any" trans-to "18.5.58.11" 
  dnatrule id 119 from "Any" to "********8" service "Any" trans-to "18.5.58.12" 
  dnatrule id 120 from "Any" to "********9" service "Any" trans-to "18.5.58.13" 
  dnatrule id 121 from "Any" to "********0" service "Any" trans-to "18.5.58.14" 
  dnatrule id 123 from "Any" to "********1" service "Any" trans-to "**********" 
  dnatrule id 124 from "Any" to "********2" service "Any" trans-to "***********" 
  dnatrule id 125 from "Any" to "********3" service "Any" trans-to "18.5.32.41" 
  dnatrule id 126 from "Any" to "********5" service "Any" trans-to "18.5.57.17" 
  dnatrule id 127 from "Any" to "********4" service "Any" trans-to "***********" 
  dnatrule id 129 from "Any" to "********5" service "Any" trans-to "18.5.12.138" 
  dnatrule id 130 from "Any" to "********6" service "Any" trans-to "18.5.12.139" 
  dnatrule id 131 from "Any" to "********7" service "Any" trans-to "18.5.12.136" 
  dnatrule id 134 from "Any" to "********8" service "Any" trans-to "**********17" 
  dnatrule id 135 from "Any" to "********9" service "Any" trans-to "***********" 
  dnatrule id 136 from "Any" to "********0" service "Any" trans-to "***********" 
  dnatrule id 137 from "Any" to "********1" service "Any" trans-to "***********" 
  dnatrule id 139 from "Any" to "********2" service "Any" trans-to "18.0.2.231" 
  dnatrule id 140 from "Any" to "********3" service "Any" trans-to "18.0.2.232" 
  dnatrule id 141 from "Any" to "********4" service "Any" trans-to "18.0.7.250" 
  dnatrule id 142 from "Any" to "********5" service "Any" trans-to "18.0.1.7" 
  dnatrule id 143 from "Any" to "********6" service "Any" trans-to "********01" 
  dnatrule id 155 from "Any" to "********7" service "TCP-9001" trans-to "18.5.57.41" port 7001 log 
  dnatrule id 149 from "Any" to "********7" service "Any" trans-to "18.5.57.41" 
  dnatrule id 150 from "Any" to "********8" service "TCP-42514" trans-to "18.5.57.45" port 21514 
  dnatrule id 151 from "***************" to "********9" service "Any" trans-to "18.5.32.71" 
  dnatrule id 152 from "***************" to "*********0" service "Any" trans-to "***********" 
  dnatrule id 153 from "Any" to "*********1" service "Any" trans-to "***********" 
  dnatrule id 154 from "Any" to "*********2" service "Any" trans-to "18.5.57.39" 
  dnatrule id 156 from "Any" to "*********3" service "Any" trans-to "18.5.12.124" 
  dnatrule id 157 from "Any" to "*********4" service "Any" trans-to "18.5.12.14" 
  dnatrule id 158 from "Any" to "*********5" service "Any" trans-to "18.5.12.34" 
  dnatrule id 159 from "Any" to "*********6" service "Any" trans-to "18.5.12.38" 
  dnatrule id 160 from "Any" to "*********7" service "Any" trans-to "18.5.12.4" 
  dnatrule id 161 from "Any" to "*********8" service "Any" trans-to "18.5.12.44" 
  dnatrule id 162 from "Any" to "*********9" service "Any" trans-to "18.5.12.48" 
  dnatrule id 163 from "Any" to "********10" service "Any" trans-to "18.5.12.54" 
  dnatrule id 164 from "Any" to "********11" service "Any" trans-to "18.5.12.58" 
  dnatrule id 165 from "Any" to "********12" service "Any" trans-to "18.5.12.64" 
  dnatrule id 166 from "Any" to "********13" service "Any" trans-to "18.5.12.68" 
  dnatrule id 167 from "Any" to "********14" service "Any" trans-to "18.5.12.74" 
  dnatrule id 168 from "Any" to "********15" service "Any" trans-to "18.5.12.78" 
  dnatrule id 169 from "Any" to "********16" service "Any" trans-to "18.5.12.8" 
  dnatrule id 170 from "Any" to "********17" service "Any" trans-to "18.5.57.42" 
  dnatrule id 171 from "Any" to "********18" service "Any" trans-to "18.5.57.36" 
  dnatrule id 172 from "Any" to "********19" service "Any" trans-to "18.5.144.214" 
  dnatrule id 173 from "Any" to "********20" service "Any" trans-to "18.5.32.52" 
  dnatrule id 174 from "Any" to "********21" service "Any" trans-to "18.5.32.53" 
  dnatrule id 175 from "Any" to "********22" service "Any" trans-to "18.5.32.54" 
  dnatrule id 176 from "Any" to "********23" service "Any" trans-to "**********" 
  dnatrule id 177 from "Any" to "********24" service "Any" trans-to "**********17" log 
  dnatrule id 178 from "Any" to "********25" service "Any" trans-to "18.5.144.214" log 
  dnatrule id 179 from "Any" to "********26" service "Any" trans-to "18.5.144.215" log 
  dnatrule id 180 from "Any" to "********27" service "Any" trans-to "18.5.144.211" 
  dnatrule id 181 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 182 from "Any" to "18.6.23.2" service "Any" trans-to "**********" 
  dnatrule id 183 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 268 from "Any" to "********28" service "SSH" trans-to "***********" port 22 
  dnatrule id 187 from "Any" to "********28" service "Any" trans-to "18.5.48.51" 
  dnatrule id 188 from "Any" to "*********" service "Any" trans-to "**********01" 
  dnatrule id 189 from "Any" to "*********" service "Any" trans-to "**********05" 
  dnatrule id 190 from "Any" to "*********" service "Any" trans-to "**********31" 
  dnatrule id 191 from "Any" to "*********" service "Any" trans-to "**********35" 
  dnatrule id 192 from "Any" to "18.6.23.8" service "Any" trans-to "18.5.57.38" 
  dnatrule id 198 from "Any" to "18.6.23.9" service "Any" trans-to "*********" log 
  dnatrule id 199 from "Any" to "*********0" service "Any" trans-to "18.5.60.1" 
  dnatrule id 200 from "Any" to "*********1" service "Any" trans-to "18.5.14.111" 
  dnatrule id 201 from "Any" to "*********2" service "Any" trans-to "18.5.14.112" 
  dnatrule id 202 from "Any" to "*********3" service "Any" trans-to "18.5.14.113" 
  dnatrule id 203 from "Any" to "*********5" service "Any" trans-to "18.5.14.115" 
  dnatrule id 206 from "Any" to "*********6" service "Any" trans-to "***********" 
  dnatrule id 208 from "Any" to "*********7" service "Any" trans-to "*************3" 
  dnatrule id 209 from "Any" to "*********8" service "Any" trans-to "192.168.213.197" 
  dnatrule id 210 from "Any" to "*********9" service "Any" trans-to "**********1" 
  dnatrule id 211 from "Any" to "18.6.23.20" service "Any" trans-to "18.5.57.37" 
  dnatrule id 217 from "Any" to "18.6.23.21" service "Any" trans-to "18.5.32.55" 
  dnatrule id 218 from "Any" to "18.6.23.22" service "Any" trans-to "18.5.61.1" 
  dnatrule id 219 from "Any" to "18.6.23.23" service "Any" trans-to "*********" 
  dnatrule id 220 from "Any" to "18.6.23.24" service "Any" trans-to "18.0.96.12" 
  dnatrule id 221 from "Any" to "18.6.23.25" service "Any" trans-to "18.0.96.120" 
  dnatrule id 222 from "Any" to "18.6.23.26" service "Any" trans-to "***********" 
  dnatrule id 223 from "Any" to "18.6.23.27" service "Any" trans-to "18.0.96.11" 
  dnatrule id 224 from "Any" to "18.6.23.28" service "Any" trans-to "18.5.57.48" 
  dnatrule id 225 from "Any" to "********52" service "Any" trans-to "18.0.2.234" 
  dnatrule id 226 from "Any" to "********51" service "Any" trans-to "18.0.2.233" 
  dnatrule id 227 from "Any" to "********53" service "Any" trans-to "18.0.2.235" 
  dnatrule id 228 from "Any" to "********54" service "Any" trans-to "18.0.2.236" 
  dnatrule id 229 from "Any" to "18.6.23.29" service "Any" trans-to "**********03" 
  dnatrule id 230 from "Any" to "*********0" service "Any" trans-to "18.5.57.24" 
  dnatrule id 231 from "Any" to "*********1" service "Any" trans-to "18.5.57.25" 
  dnatrule id 233 from "Any" to "*********2" service "Any" trans-to "**********7" 
  dnatrule id 238 from "Any" to "*********3" service "Any" trans-to "192.168.213.88" 
  dnatrule id 239 from "Any" to "********50" service "Any" trans-to "18.5.57.50" 
  dnatrule id 247 from "Any" to "********49" service "Any" trans-to "*************24" 
  dnatrule id 252 from "Any" to "*********" service "TCP8099" trans-to "**********/32" port 8099 
  dnatrule id 254 from "Any" to "********01" trans-to "***********" 
  dnatrule id 255 from "Any" to "********02" trans-to "18.5.32.202" 
  dnatrule id 256 from "Any" to "********03" trans-to "18.5.32.203" 
  dnatrule id 257 from "Any" to "********11" trans-to "18.5.32.211" 
  dnatrule id 258 from "Any" to "********13" service "Any" trans-to "18.5.32.213" 
  dnatrule id 259 from "Any" to "********12" trans-to "18.5.32.212" 
  dnatrule id 260 from "Any" to "********14" trans-to "***********" 
  dnatrule id 261 from "Any" to "********15" trans-to "***********" 
  dnatrule id 262 from "Any" to "********16" trans-to "***********" 
  dnatrule id 263 from "Any" to "********17" trans-to "***********" 
  dnatrule id 264 from "Any" to "*********1" service "TCP-7001" trans-to "**********02" port 7001 
  dnatrule id 269 from "Any" to "*********" service "SSH" trans-to "ELP-***********" port 22 
  dnatrule id 272 from "Any" to "********29" service "Any" trans-to "**********" 
  ip route 0.0.0.0/0 aggregate1.3182 ************ description "default"
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ***********/32 ************
  ip route ********83/32 ************
  ip route **********/32 ************
  router ospf 1300
    router-id ************
    default-information originate always metric 2
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T4"
  snatrule id 242 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 164 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport 
  snatrule id 163 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport log 
  snatrule id 165 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport 
  snatrule id 166 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport 
  snatrule id 167 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport log 
  snatrule id 168 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport 
  snatrule id 169 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport 
  snatrule id 170 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport 
  snatrule id 171 from "*********" to "*********/16" service "Any" trans-to ********* mode dynamicport log 
  snatrule id 5 from "Any" to "18.5.67.100" service "Any" trans-to ********29 mode dynamicport 
  snatrule id 7 from "T4-*********-9" to "Any" service "Any" trans-to ********30 mode dynamicport 
  snatrule id 15 from "T4-***********-253" to "Any" service "Any" trans-to ********32 mode dynamicport 
  snatrule id 36 from "18.0.120.10" to "18.5.83.11" service "Any" trans-to ********33 mode dynamicport 
  snatrule id 49 from "*********/24" to "**********" service "Any" trans-to ********** mode dynamicport 
  snatrule id 50 from "*********/24" to "18.5.115.2" service "Any" trans-to ********** mode dynamicport 
  snatrule id 90 from "192.168.214.0/24" to "Any" service "Any" trans-to ********35 mode dynamicport 
  snatrule id 94 from "*********/24" to "**************/26" service "Any" trans-to ********36 mode dynamicport 
  snatrule id 128 from "18.5.48.161" to "172.16.20.252" service "Any" trans-to ********37 mode dynamicport 
  snatrule id 174 from "Any" to "10.215.41.0/24" service "Any" trans-to ********38 mode dynamicport disable 
  snatrule id 178 from "***********" to "104.22.9.42" service "Any" trans-to ********** mode dynamicport 
  snatrule id 179 from "18.5.74.112" to "104.22.9.42" service "Any" trans-to ********** mode dynamicport 
  snatrule id 180 from "***********" to "104.22.9.41" service "Any" trans-to ********** mode dynamicport 
  snatrule id 181 from "18.5.74.112" to "104.22.9.41" service "Any" trans-to ********** mode dynamicport 
  snatrule id 182 from "*********/24" to "**********/24" service "Any" trans-to ********** mode dynamicport 
  snatrule id 191 from "18.5.94.1" to "*************" service "Any" trans-to ********40 mode dynamicport 
  snatrule id 192 from "18.5.94.2" to "*************" service "Any" trans-to ********41 mode dynamicport 
  snatrule id 198 from "*********" to "**********/24" service "Any" trans-to ********* mode dynamicport 
  snatrule id 199 from "*********" to "10.213.3.0/24" service "Any" trans-to ********* mode dynamicport 
  snatrule id 202 from "*********" to "**********/24" service "Any" trans-to ********* mode dynamicport 
  snatrule id 205 from "18.5.74.0/24" to "104.22.9.42" service "Any" trans-to ********** mode dynamicport 
  snatrule id 206 from "shuangyinsu_test" to "*************" service "Any" trans-to ********** mode dynamicport 
  snatrule id 213 from "Any" to "***********" service "Any" trans-to ********** mode dynamicport 
  snatrule id 214 from "*********/24" to "**********" service "Any" trans-to ********** mode dynamicport 
  snatrule id 222 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 219 from "**********" to "************" service "Any" trans-to ********** mode dynamicip log 
  snatrule id 218 from "***********/32" to "************" service "Any" trans-to ********** mode dynamicip 
  snatrule id 220 from "***********/32" to "************" service "Any" trans-to ********** mode dynamicip 
  snatrule id 223 from "**********/32" to "************" service "Any" trans-to ********** mode dynamicip 
  snatrule id 224 from "***********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 226 from "***********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 225 from "*********" to "************" service "Any" trans-to ********** mode dynamicip 
  snatrule id 227 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 228 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 229 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 231 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 230 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 217 from "**********" to "************" service "Any" trans-to ********** mode static 
  snatrule id 232 from "***********" to "*********/16" service "Any" trans-to ********** mode dynamicport 
  snatrule id 221 from "***********" to "**********/24" service "Any" trans-to ********** mode dynamicport log 
  snatrule id 233 from "*********/24" to "**************" service "Any" trans-to ********** mode dynamicport log 
  snatrule id 236 from "*********/24" to "***********" service "Any" trans-to ********59 mode dynamicport 
  snatrule id 237 from "*********" to "**********&41" service "Any" trans-to ********60 mode dynamicport 
  snatrule id 238 from "*********00" to "192.168.215.18" service "Any" trans-to ********60 mode dynamicport disable 
  snatrule id 241 from "18.5.74.11/32" to "66.209.95.141/32" service "Any" trans-to ********61/32 mode dynamicport 
  snatrule id 246 from "***********" to "Any" service "Any" trans-to ********62 mode static 
  snatrule id 247 from "18.5.72.36" to "10.210.9.201" service "Any" trans-to ********63 mode static 
  snatrule id 248 from "18.5.72.37" to "10.210.9.201" service "Any" trans-to ********63 mode static 
  snatrule id 249 from "***********" to "39.105.161.118" service "Any" trans-to ********64 mode static log 
  snatrule id 250 from "***********" to "39.105.161.118" service "Any" trans-to ********64 mode static 
  dnatrule id 133 from "*********/16" to "*********" service "Any" trans-to "*********" log 
  dnatrule id 4 from "Any" to "********" service "Any" trans-to "18.5.80.101" 
  dnatrule id 5 from "Any" to "18.6.4.2" service "Any" trans-to "18.5.80.102" 
  dnatrule id 6 from "Any" to "18.6.4.3" service "Any" trans-to "18.5.80.103" 
  dnatrule id 7 from "Any" to "18.6.4.4" service "Any" trans-to "18.5.80.111" 
  dnatrule id 8 from "Any" to "18.6.4.5" service "Any" trans-to "18.5.80.112" 
  dnatrule id 9 from "Any" to "18.6.4.6" service "Any" trans-to "18.5.80.113" 
  dnatrule id 10 from "Any" to "********" service "Any" trans-to "18.5.80.114" 
  dnatrule id 11 from "Any" to "18.6.4.8" service "Any" trans-to "18.5.80.121" 
  dnatrule id 12 from "Any" to "18.6.4.9" service "Any" trans-to "18.5.80.122" 
  dnatrule id 13 from "Any" to "********0" service "Any" trans-to "18.5.80.123" 
  dnatrule id 14 from "Any" to "********1" service "Any" trans-to "18.5.80.124" 
  dnatrule id 15 from "Any" to "********2" service "Any" trans-to "18.5.80.125" 
  dnatrule id 16 from "Any" to "********3" service "Any" trans-to "18.5.80.131" 
  dnatrule id 17 from "Any" to "********4" service "Any" trans-to "18.5.80.132" 
  dnatrule id 18 from "Any" to "********5" service "Any" trans-to "18.5.80.133" 
  dnatrule id 19 from "Any" to "********6" service "Any" trans-to "18.5.80.134" 
  dnatrule id 24 from "Any" to "18.6.4.21" service "Any" trans-to "*********00" 
  dnatrule id 25 from "Any" to "18.6.4.22" service "Any" trans-to "*********01" 
  dnatrule id 26 from "Any" to "*********" service "Any" trans-to "*********" log 
  dnatrule id 27 from "Any" to "*********" service "Any" trans-to "*********" log 
  dnatrule id 144 from "*********7" to "*********" service "Any" trans-to "*********" 
  dnatrule id 28 from "Any" to "*********" service "NFS" trans-to "*********" 
  dnatrule id 132 from "Any" to "*********" service "Any" trans-to "*********" 
  dnatrule id 29 from "Any" to "*********" service "Any" trans-to "*********" 
  dnatrule id 30 from "Any" to "*********" service "Any" trans-to "*********" 
  dnatrule id 31 from "Any" to "*********" service "Any" trans-to "*********" 
  dnatrule id 32 from "Any" to "*********" service "Any" trans-to "*********" 
  dnatrule id 33 from "Any" to "*********" service "Any" trans-to "*********" 
  dnatrule id 34 from "Any" to "*********" service "Any" trans-to "*********" log 
  dnatrule id 35 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 36 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 37 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 38 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 39 from "Any" to "*********" service "Any" trans-to "18.5.99.11" 
  dnatrule id 52 from "Any" to "*********" service "Any" trans-to "18.5.252.1" 
  dnatrule id 23 from "Any" to "18.6.4.38" service "Any" trans-to "18.5.88.41" 
  dnatrule id 111 from "Any" to "*********" service "Any" trans-to "18.5.88.81" 
  dnatrule id 112 from "Any" to "18.6.4.40" service "Any" trans-to "18.5.88.62" 
  dnatrule id 113 from "Any" to "18.6.4.41" service "Any" trans-to "18.5.88.71" 
  dnatrule id 114 from "Any" to "18.6.4.42" service "Any" trans-to "18.5.88.61" 
  dnatrule id 115 from "Any" to "18.6.4.43" service "Any" trans-to "18.5.88.91" 
  dnatrule id 116 from "Any" to "18.6.4.44" service "Any" trans-to "18.5.88.131" 
  dnatrule id 128 from "Any" to "*********" service "Any" trans-to "***********" 
  dnatrule id 204 from "Any" to "18.6.4.46" service "Any" trans-to "18.5.97.6" 
  dnatrule id 205 from "Any" to "18.6.4.47" service "Any" trans-to "18.5.97.8" 
  dnatrule id 207 from "Any" to "18.6.4.48" service "Any" trans-to "*********9" 
  dnatrule id 212 from "Any" to "18.6.4.49" service "Any" trans-to "18.5.97.18" 
  dnatrule id 213 from "104.22.9.42" to "18.6.4.50" service "Any" trans-to "18.5.97.19" 
  dnatrule id 214 from "104.22.9.41" to "18.6.4.50" service "Any" trans-to "18.5.97.19" 
  dnatrule id 216 from "Any" to "18.6.4.51" service "Any" trans-to "***********" 
  dnatrule id 232 from "Any" to "18.6.4.52" service "Any" trans-to "18.5.96.2" 
  dnatrule id 234 from "Any" to "*********" service "Any" trans-to "18.5.97.1" 
  dnatrule id 235 from "Any" to "18.6.4.54" service "Any" trans-to "18.0.98.11" 
  dnatrule id 236 from "Any" to "18.6.4.55" service "Any" trans-to "18.0.98.12" 
  dnatrule id 237 from "Any" to "18.6.4.56" service "Any" trans-to "18.0.98.120" 
  dnatrule id 241 from "Any" to "18.6.4.57" trans-to "*********49" 
  dnatrule id 242 from "Any" to "18.6.4.58" trans-to "*********50" 
  dnatrule id 243 from "Any" to "18.6.4.59" trans-to "18.5.84.53" 
  dnatrule id 244 from "Any" to "18.6.4.60" service "Any" trans-to "18.5.70.51" 
  dnatrule id 245 from "Any" to "18.6.4.61" trans-to "18.5.80.161" 
  dnatrule id 246 from "Any" to "18.6.4.62" trans-to "***********" 
  dnatrule id 248 from "Any" to "18.6.4.63" trans-to "18.5.70.11" 
  dnatrule id 249 from "Any" to "18.6.4.64" trans-to "18.5.70.12" 
  dnatrule id 250 from "Any" to "18.6.4.65" service "Any" trans-to "18.5.83.91" 
  dnatrule id 251 from "Any" to "18.6.4.66" service "Any" trans-to "18.5.83.92" 
  dnatrule id 253 from "Any" to "*********" service "Any" trans-to "**********" 
  dnatrule id 265 from "Any" to "*********" service "Any" trans-to "***********" 
  dnatrule id 266 from "Any" to "*********" service "Any" trans-to "***********" 
  dnatrule id 267 from "Any" to "*********" service "Any" trans-to "***********" 
  dnatrule id 270 from "Any" to "*********" service "Any" trans-to "***********" 
  dnatrule id 271 from "Any" to "*********" service "Any" trans-to "***********" 
  dnatrule id 273 from "Any" to "*********" service "Any" trans-to "**********" 
  ip route 0.0.0.0/0 aggregate1.3183 ************ description "default"
  ip route ********/32 ************
  ip route ********/32 ************
  ip route ********38/32 ************
  ip route ************/32 ************
  router ospf 1400
    router-id ************
    default-information originate always metric 2
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T5"
  router ospf 1500
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T6"
  router ospf 1600
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T7"
  router ospf 1700
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T8"
  router ospf 1800
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T9"
  router ospf 1900
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T10"
  router ospf 2000
    router-id ************
    default-information originate always metric 2
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T11"
  router ospf 2100
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T12"
  router ospf 2200
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T13"
  router ospf 2300
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T14"
  router ospf 2400
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T15"
  router ospf 2500
    router-id ************
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T16"
  ip route 0.0.0.0/0 ***********
  router ospf 2600
    router-id ************
    default-information originate always metric 2
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T17"
  ip route 0.0.0.0/0 ***********
  router ospf 2700
    router-id ************
    default-information originate always metric 2
    network **********/32 area 0.0.0.0
  exit
exit
ip vrouter "T18"
  ip route 0.0.0.0/0 ***********
  router ospf 2800
    router-id ************
    default-information originate always metric 2
    network ***********/32 area 0.0.0.0
  exit
exit
ip vrouter "T19"
  snatrule id 101 from "Any" to "**********/24" service "Any" trans-to ********* mode dynamicport 
  snatrule id 102 from "Any" to "***********" service "Any" trans-to ********* mode dynamicport 
  snatrule id 104 from "Any" to "********7" service "Any" trans-to ********* mode dynamicport 
  snatrule id 111 from "Any" to "**********" service "Any" trans-to ********* mode dynamicport 
  dnatrule id 145 from "Any" to "*********29" service "Any" trans-to "**********" 
  dnatrule id 138 from "Any" to "*********30" service "Any" trans-to "**********" 
  dnatrule id 146 from "Any" to "***********" service "Any" trans-to "**********" 
  dnatrule id 147 from "Any" to "*********32" service "Any" trans-to "**********" 
  dnatrule id 104 from "Any" to "*********33" service "Any" trans-to "**********" 
  dnatrule id 184 from "Any" to "***********" service "Any" trans-to "**********" 
  dnatrule id 185 from "Any" to "*********35" service "Any" trans-to "**********" 
  dnatrule id 186 from "Any" to "***********" service "Any" trans-to "**********" 
  ip route 0.0.0.0/0 aggregate1.3198 ***********
  router ospf 2900
    router-id 172.17.20.59
    default-information originate always metric 2
    network ***********/32 area 0.0.0.0
  exit
exit
ip vrouter "T20"
  router ospf 3000
    router-id 172.17.20.60
    network ***********/32 area 0.0.0.0
  exit
exit
ip vrouter "T21"
  ip route 0.0.0.0/0 aggregate1.3153 172.17.6.201 description "t21-default"
  router ospf 3100
    router-id 172.17.20.61
    network ************/32 area 0.0.0.0
  exit
exit
ip vrouter "T22"
  dnatrule id 215 from "4.189.0.0/16" to "78.0.0.0/16" service "Any" trans-to "18.0.0.0/16" 
  ip route 0.0.0.0/0 aggregate1.3154 172.17.6.209 description "T22 default"
  router ospf 3200
    router-id 172.17.20.62
    network ************/32 area 0.0.0.0
  exit
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp enable
ntp server ********73 vrouter mgt-vr
clock zone china
rule id 3000
  src-zone "T22-OUTSIDE"
  name "*******/24"
exit
rule id 308
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "Any"
  dst-addr "*********"
  service "Any"
  name "TO-************"
exit
rule id 300
  action permit
  log session-start
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "***********"
  src-addr "yun-**********/16"
  dst-addr "***********"
  service "Any"
  name "**********/16-***********"
exit
rule id 293
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************"
  dst-addr "T4OB10.220.0.173"
  service "TCP2882"
  name "腾讯云10.216toT4OB10.220.0.173"
exit
rule id 315
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "***********"
  src-addr "*************"
  dst-addr "**********"
  service "Any"
  name "20230625李景一需求"
exit
rule id 317
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "Any"
  dst-addr "************"
  service "Any"
  name "TO************"
exit
rule id 303
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "Any"
  dst-addr "SMTP-**********"
  service "Any"
  name "TO**********"
exit
rule id 321
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "10.220.3.50"
  dst-addr "Any"
  service "Any"
  name "testlinshi"
exit
rule id 311
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***********"
  dst-addr "**********"
  service "Any"
  name "赵树军需求"
exit
rule id 310
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************/32"
  src-addr "***********/张跃的VDI测试"
  dst-addr "R_WCS18.5.70.51"
  service "Any"
  name "魏宏安全监控"
exit
rule id 336
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "*********5"
  dst-addr "***********/32"
  service "Any"
  name "王轩跳板机"
exit
rule id 290
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "*********1"
  dst-addr "Any"
  service "Any"
  name "魏宏安全扫描*********1"
exit
rule id 332
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "***********"
  dst-addr "************"
  service "TCP-8088"
  name "统一开发测试网络内的VDI访问业务系统"
exit
rule id 327
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************"
  dst-addr "************"
  service "TCP-8088"
  name "鹏龙21F专网访问测试业务"
exit
rule id 289
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************/24"
  src-ip **************/32
  dst-ip ************/32
  service "ICMP"
  service "TCP-8081"
  description "骏彩办公网************-51.0地址转换访问"
  name "全国数据管理平台项目网络访问的权限"
exit
rule id 245
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "Any"
  dst-addr "***********/32"
  service "SMTP"
  service "ICMP"
  service "PING"
  service "HTTPS"
  service "POP-995"
  service "IMAP-993"
  name "SMTP"
exit
rule id 330
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************/24"
  src-addr "**********"
  dst-addr "**********/24"
  service "SSH"
  service "TCP-2222"
  name "Coding系统备集群访问主集群"
exit
rule id 329
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "**********/24"
  dst-addr "***********/32"
  service "TCP3306"
  name "VDI访问Coding系统数据库"
exit
rule id 241
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "T1-********38"
  src-addr "************"
  src-addr "***********"
  src-addr "solarwids-***********"
  dst-addr "Any"
  service "Any"
  name "ZBBIX"
exit
rule id 288
  action permit
  disable
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "123456789"
exit
rule id 286
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-ip **************/32
  src-ip ********03/32
  src-ip *********/32
  src-range ********* *********
  src-range ********* *********
  dst-ip *********/32
  dst-range ********** **********5
  service "pkica-s"
  service "ICMP"
  service "TCP4444"
  name "PKICA-1"
exit
rule id 260
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "10_104/8"
  dst-addr "Any"
  service "PING"
  name "PING ANY"
exit
rule id 272
  action permit
  log policy-deny
  log session-start
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "Any"
  dst-addr "*********1"
  service "SSH"
  name "KEFUTELEPHONE"
exit
rule id 255
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "T4-**********/24"
  dst-addr "T3-**********/24"
  service "TCP-31306"
  service "PING"
  name "T4-**********/24"
exit
rule id 220
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***************/32"
  src-addr "***************"
  dst-addr "********21"
  service "TCP-30000"
exit
rule id 209
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "104.21.51.51/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********6/32"
  dst-addr "*********1-15"
  service "TCP-3191"
  service "ICMP"
exit
rule id 205
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "CSLC-172.26.100.200/32"
  src-addr "104.21.51.51/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********28/32"
  service "TCP-29092"
exit
rule id 328
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "10.216.71.0"
  src-addr "***********/张跃的VDI测试"
  dst-addr "18.5.81.5"
  service "TCP-3555"
  name "WCS性能测试"
exit
rule id 54
  action permit
  log session-start
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "Any"
  dst-addr "*********-31"
  dst-addr "*********-9"
  dst-addr "*********/21"
  service "Any"
exit
rule id 207
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "104.23.11.10/32"
  src-addr "************/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "NAT-*********"
  service "TCP-8011-8020"
exit
rule id 204
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "**********/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "NAT-*********"
  service "TCP-9090"
exit
rule id 177
  action permit
  log session-start
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************/32"
  src-addr "***********"
  src-addr "**********"
  dst-addr "Any"
  service "Any"
  name "test"
exit
rule id 199
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "124.193.71.186/32"
  src-addr "123.57.49.51-吉林大乐透"
  dst-addr "********7/32"
  service "TCP-9001"
exit
rule id 176
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**********/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********20/32"
  service "TCP_34443"
exit
rule id 60
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "*************/32"
  src-addr "**************/32"
  src-addr "YINLIAN"
  dst-addr "********8/32"
  service "TCP-42514"
exit
rule id 48
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**********/24"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  dst-addr "VDI-TO-T3-Tiaobanji"
  service "TCP-3389"
  service "TCP-445"
  service "ICMP"
exit
rule id 59
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "CSLC-***********/24"
  dst-addr "XWHT3-tiaobanji-**********"
  service "TCP-3389"
  name "CSLC-To-XWHT3-tiaobanji"
exit
rule id 41
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "OPS-*************/24"
  dst-addr "T3-harbor-***********/32"
  service "HTTP"
exit
rule id 294
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "**********/24"
  service "HTTP"
  service "HTTPS"
  name "TESTPCTOVDICONNECTSERVER"
exit
rule id 313
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "***************"
  service "Any"
  name "魏宏需求实体渠道"
exit
rule id 1
  action permit
  src-zone "T1-OUTSIDE"
  dst-zone "T1-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 2
  action permit
  src-zone "T1-INSIDE"
  dst-zone "T1-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 3
  action permit
  src-zone "T2-OUTSIDE"
  dst-zone "T2-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 4
  action permit
  src-zone "T2-INSIDE"
  dst-zone "T2-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 20
  action permit
  src-zone "T10-INSIDE"
  dst-zone "T10-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 67
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**********/32"
  src-addr "**********/32"
  dst-addr "********/24"
  service "TCP-7001"
  service "TCP-8443"
  service "HTTP"
  service "HTTPS"
  service "TCP-52701"
  service "ICMP"
  service "TCP-30000"
  name "500WAN"
exit
rule id 68
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "*********/27"
  src-addr "********/24"
  dst-addr "********1/32"
  dst-addr "********2/32"
  dst-addr "********/32"
  dst-addr "********21"
  service "TCP-21"
  service "HTTP"
  service "HTTPS"
  service "TCP-52701"
  service "TCP-30000"
  name "yipingtai"
exit
rule id 69
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "************/32"
  dst-addr "*********/32"
  dst-addr "********/32"
  service "TCP-7001"
  service "HTTPS"
exit
rule id 72
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "Any"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "TCP-8001"
  service "TCP-7001"
  service "TCP-8553"
  service "TCP_6001"
  name "OPENAPI"
exit
rule id 73
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********."
  src-addr "**********/24"
  dst-addr "********/24"
  service "HTTPS"
  service "TCP-8443"
  service "TCP-30000"
  service "TCP-52701"
  service "SSH"
  service "HTTP"
  name "JingCaiWang"
exit
rule id 88
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**************"
  dst-addr "NAT-********6"
  service "TCP-20203"
exit
rule id 90
  action permit
  log session-start
  log session-end
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "UnionPay-***************"
  src-addr "bangongwang-**************"
  src-addr "*************/32"
  dst-addr "********7"
  service "TCP-32514"
  name "Unionpay"
exit
rule id 91
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**************"
  src-addr "**************/32"
  src-addr "**************-204/32"
  src-addr "*************/32"
  src-addr "***********/24"
  src-addr "************/32"
  src-addr "CSLC_***********/24"
  dst-addr "********/24"
  service "TCP-31306"
  service "TCP-7001"
  service "TCP-8080"
exit
rule id 97
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "Any"
  dst-addr "T3_NAT_********7"
  service "SSH"
  name "CCB_to_T3_SFTP"
exit
rule id 217
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "104.11.1.0/32"
  dst-addr "10.213.5.241/32"
  service "Any"
exit
rule id 210
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  src-addr "**********18/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "104.21.51.0/24"
  dst-addr "CSLC-104.24.0.0/24"
  service "TCP-8089"
  service "TCP-8081"
exit
rule id 206
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/24"
  dst-addr "104.23.13.0/24"
  service "TCP-29092"
exit
rule id 203
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "10.213.0.200/32"
  service "TCP-8000"
exit
rule id 188
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "104.24.0.141/32"
  dst-addr "104.24.0.31/32"
  dst-addr "104.21.19.20/32"
  service "TCP-8080"
exit
rule id 105
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "**********/24"
  dst-addr "***********/24"
  dst-addr "***********/24"
  service "TCP-18500"
  service "TCP-5080"
  service "TCP-19092"
  service "SSH"
  service "TCP-31306"
  service "TCP-1521"
  service "TCP-8080"
  service "TCP-20050"
  service "HTTP"
exit
rule id 168
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "***************/32"
  dst-addr "*************/32"
  service "TCP-30071"
exit
rule id 95
  action permit
  log session-start
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "T3-CSLAPP"
  dst-addr "192.144.193.39"
  dst-addr "104.24.0.136"
  dst-addr "104.23.11.4"
  service "TCP-8080"
  service "TCP-8181"
  service "TCP-8088"
  name "T3_to_CSLCAPP"
exit
rule id 146
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********18/32"
  dst-addr "**************/32"
  service "ICMP"
  service "TCP-8088"
exit
rule id 125
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "Bisab-*********-45"
  dst-addr "***********/32"
  service "ICMP"
  service "TCP_34443"
exit      
rule id 111
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "CSLC-104.21.2.51/32"
  service "TCP-20167"
  service "TCP-28080"
exit
rule id 112
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "203.119.206.132/32"
  service "HTTPS"
exit
rule id 98
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "***********"
  src-addr "***********"
  dst-addr "***************"
  service "Any"
  name "T3_to_JiGuanShouKongKu"
exit
rule id 99
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  src-addr "**********/24"
  dst-addr "************/32"
  service "TCP-8090"
  service "ICMP"
exit
rule id 106
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "CSLC-**********/24"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********/24"
  service "HTTPS"
  service "SSH"
  service "TCP-7001"
  service "TCP-5672"
  service "TCP-9080"
  service "TCP-9001"
  service "TCP-8989"
exit
rule id 130
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********"
  dst-addr "*********/32"
  service "TCP-3389"
  name "yunying_to_SBC"
exit
rule id 140
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  dst-addr "********/32"
  dst-addr "********/32"
  service "TCP-8443"
  service "HTTPS"
  name "CSLC-to_T3_F5"
exit
rule id 143
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "JiKai_124.65.173.138"
  src-addr "SuNingXiaoDian_221. 226.125.130"
  src-addr "***********/32"
  src-addr "************/32"
  src-addr "*************/24"
  src-addr "************/24"
  src-addr "CSLC-***********/24"
  src-addr "*************/32"
  src-addr "**********/16"
  src-addr "47.105.221.226/32"
  src-addr "104.21.4.110/32"
  src-addr "*************/24"
  src-addr "*************/24"
  src-addr "106.11.192.0/19"
  src-addr "************/22"
  src-addr "*************/17"
  src-addr "**************/32"
  src-addr "***************/32"
  src-addr "***************/32"
  src-addr "***********/32"
  src-addr "*************/32"
  src-addr "吉祥宝8.131.227.169"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  src-addr "111_204_38_0/24"
  dst-addr "********4/32"
  service "SSH"
exit
rule id 152
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********/24"
  src-addr "**************/32"
  src-addr "***************/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********8/32"
  service "TCP-10011"
exit
rule id 157
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********/32"
  src-addr "104.21.19.20/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********9/32"
  dst-addr "********0/32"
  service "TCP-5672"
exit
rule id 159
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********/32"
  src-addr "***************/32"
  src-addr "************/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********3/32"
  dst-addr "********2/32"
  service "SSH"
  service "ICMP"
  service "HTTP"
exit
rule id 166
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "10.20.1.0/24"
  src-addr "10.20.2.0/24"
  dst-addr "********7/32"
  service "TCP-7001"
exit
rule id 170
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "*************/32"
  src-addr "*************/32"
  dst-addr "*********2/32"
  service "ICMP"
  service "TCP-8443"
exit
rule id 171
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********/32"
  src-addr "104.21.19.53/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********18/32"
  service "TCP-8080"
exit
rule id 76
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**************"
  dst-addr "*********/32"
  dst-addr "*********/32"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "TCP-31306"
exit
rule id 85
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********28/32"
  service "TCP-29092"
exit
rule id 189
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "Any"
  dst-addr "*********0/32"
  service "HTTP"
  service "HTTPS"
exit
rule id 258
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "82_157_106.153"
  dst-addr "*********/32"
  service "ICMP"
  service "TCP8099"
  name "数字化"
exit
rule id 86
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "500wan_JCWeb_DXWG"
  dst-addr "UnionPay-***************"
  dst-addr "*************/32"
  dst-addr "SSL_GATEWAY-**************"
  service "Any"
  name "*************/24_to_internet"
exit
rule id 75
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********23/124"
  dst-addr "***************/221"
  service "SSH"
  service "HTTPS"
exit
rule id 89
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "**************"
  service "TCP-8089"
  service "TCP-8087"
  service "TCP-8083"
exit
rule id 132
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/24"
  dst-addr "**************"
  service "ICMP"
  service "TCP-8087"
exit
rule id 92
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  src-addr "********/24"
  dst-addr "************"
  dst-addr "************"
  service "ICMP"
  service "HTTPS"
  service "TCP-9080"
  service "HTTP"
exit
rule id 110
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "***********/32"
  service "TCP-8443"
  service "ICMP"
  service "TCP-24433"
  service "TCP-4433"
  service "HTTPS"
  service "TCP-8080"
exit
rule id 101
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "QC-*************"
  service "TCP-8080"
  name "*************_TO_qc"
exit
rule id 102
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "***********/32"
  dst-addr "email_*************"
  service "SMTP"
  service "ICMP"
  name "***********_to_email"
exit
rule id 93
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "UnionPay-***************"
  dst-addr "*************/32"
  service "HTTPS"
  service "TCP-8080"
exit
rule id 116
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "172.26.12.244/32"
  dst-addr "************/32"
  service "ICMP"
  service "TCP-8080"
  service "TCP-8090"
exit
rule id 94
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "DXYXS-**************"
  service "TCP-9999"
exit
rule id 96
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3-*********"
  dst-addr "CA1-**********"
  dst-addr "CA2-**********"
  service "TCP-8000"
exit
rule id 117
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "ELP-***********-147"
  src-addr "***********-139"
  dst-addr "************/32"
  service "TCP-9080"
  service "ICMP"
exit
rule id 103
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "ELP-***********-147"
  dst-addr "CSLC_***********/24"
  service "TCP-389"
  service "TCP-9090"
  service "HTTPS"
exit
rule id 104
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "CSLC_infogw_************"
  service "HTTP"
  name "To_CSLC_infogw"
exit
rule id 107
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "CSLC-SIE-104.21.2.38"
  service "TCP-20050"
exit
rule id 108
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/24"
  dst-addr "CSLC-104.24.0.0/24"
  service "TCP-2001-2003"
exit
rule id 113
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/24"
  src-addr "T3_GW_*********-18"
  dst-addr "unicom-123.125.97.251"
  service "TCP-55382"
  name "营销消息中心到联通"
exit
rule id 118
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "ELP-***********-147"
  dst-addr "**********/24"
  service "ICMP"
  service "TCP-6006"
  service "TCP-4433"
  service "HTTPS"
  service "TCP-6008"
exit
rule id 119
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "ELP-***********-147"
  dst-addr "***********/32"
  dst-addr "***********/32"
  service "ICMP"
  service "TCP-20150"
  service "TCP-8080"
exit
rule id 120
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********23/124"
  dst-addr "*************/32"
  service "TCP-4422"
exit
rule id 122
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "CSLC-104.24.0.0/24"
  dst-addr "***********/24"
  dst-addr "**********/24"
  service "ICMP"
  service "HTTP"
  service "TCP-2001"
  service "TCP-8181"
  service "TCP-8090"
  service "TCP-8330"
exit
rule id 124
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "G3-Information publishing platform"
  dst-addr "GSM"
  service "ICMP"
  service "HTTP"
exit
rule id 126
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "***********/32"
  service "ICMP"
  service "TCP-8080"
exit
rule id 131
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "***********"
  dst-addr "*********/32"
  dst-addr "*********/32"
  dst-addr "***********/32"
  service "TCP-6201"
  service "TCP-6021"
  service "TCP-8088"
  service "TCP-28080"
  service "SSH"
  service "ICMP"
  service "TCP-8087"
exit
rule id 133
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/32"
  dst-addr "***********/32"
  service "TCP-8443"
exit
rule id 134
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/32"
  dst-addr "***********/32"
  service "TCP-21"
exit
rule id 136
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "***********-139"
  dst-addr "**********/24"
  service "TCP-6006"
  service "HTTPS"
  service "TCP-4433"
exit
rule id 137
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "***********-139"
  dst-addr "***********/32"
  dst-addr "***********/32"
  dst-addr "**********/32"
  service "TCP-50094/50095"
  service "TCP-8082"
  service "TCP-20150"
exit
rule id 139
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "************/32"
  service "ICMP"
  service "TCP-15000"
exit
rule id 141
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "CSLC-***********/24"
  dst-addr "CSLC-***********/24"
  service "HTTPS"
  service "TCP-9090"
exit
rule id 142
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*********/32"
  dst-addr "T1-jiamiji-**********"
  service "TCP-8000"
exit
rule id 144
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "*************/32"
  service "ICMP"
  service "TCP-8080"
exit
rule id 147
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "************/32"
  service "ICMP"
  service "TCP-8089"
exit
rule id 148
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/24"
  dst-addr "*********/8"
  dst-addr "********/8"
  dst-addr "*********/8"
  dst-addr "*********/8"
  service "ICMP"
  service "HTTPS"
exit
rule id 149
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "***********/24"
  service "TCP-55672"
  service "TCP-54102-54104"
  service "TCP-28081"
  service "TCP-28080"
  service "TCP-25601"
  service "TCP-9999"
  service "TCP-9021"
  service "TCP-7001"
  service "TCP-8161"
  service "TCP-8083"
  service "TCP-8088"
  service "TCP-8080"
  service "HTTP"
  service "SSH"
exit
rule id 151
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/24"
  dst-addr "*************/24"
  dst-addr "************/24"
  service "HTTP"
exit
rule id 153
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  src-addr "*********/24"
  dst-addr "**********/24"
  dst-addr "**********/24"
  service "ICMP"
  service "TCP-34433"
  service "HTTP"
  service "TCP-21"
exit
rule id 154
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "**********/24"
  service "ICMP"
  service "HTTP"
  service "TCP-8080"
exit
rule id 156
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "ELP-***********-147"
  src-addr "*************/24"
  dst-addr "**********/24"
  dst-addr "**********/24"
  dst-addr "************/32"
  service "TCP-20150"
  service "TCP-8080"
  service "TCP-24433"
  service "TCP-14433"
  service "TCP-6006"
  service "TCP-9080"
  service "TCP-34433"
exit
rule id 158
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "**********/24"
  service "ICMP"
  service "HTTPS"
exit
rule id 160
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********/24"
  dst-addr "CSLC-***********/24"
  service "ICMP"
  service "TCP-8098"
exit
rule id 161
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********18/32"
  dst-addr "***********/32"
  dst-addr "*************/32"
  service "TCP-9999"
  service "ICMP"
  service "HTTP"
exit
rule id 163
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "**********/24"
  dst-addr "CSLC-**********/24"
  service "ICMP"
  service "SSH"
exit
rule id 164
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "104.21.4.110/32"
  service "ICMP"
  service "TCP-8080"
  service "TCP-8090"
exit
rule id 165
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "*************/32"
  dst-addr "124.127.94.59/32"
  service "TCP-8103"
  service "TCP-8101"
  service "TCP-5533"
exit
rule id 167
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "**********/24"
  dst-addr "**********/24"
  service "ICMP"
  service "TCP-34433"
  service "TCP-24433"
  service "TCP-14433"
  service "TCP-8080"
exit
rule id 169
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  src-addr "*********/24"
  src-addr "T3_GW_*********-18"
  dst-addr "**********/24"
  service "TCP-28080"
  service "TCP-8022"
  service "TCP-8087"
  service "TCP-8082"
  service "TCP-52701"
  service "TCP_8086"
exit
rule id 172
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "***************/32"
  service "HTTPS"
exit
rule id 173
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "***********/24"
  service "PING"
  service "TCP-3389"
exit
rule id 47
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "***********/24"
  service "ICMP"
  service "SSH"
exit
rule id 138
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "***********/32"
  dst-addr "************/32"
  service "TCP-10248"
  service "TCP-8090"
exit
rule id 182
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "104.21.0.220/32"
  service "TCP-8080"
exit
rule id 195
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "**********/24"
  service "Any"
exit
rule id 225
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "ELP-***********-147"
  dst-addr "10.20.4.0/24"
  service "ICMP"
  service "TCP-8080"
  name "t3_TO_TICAIAPP"
exit
rule id 6
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 9
  action permit
  src-zone "T5-OUTSIDE"
  dst-zone "T5-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 10
  action permit
  src-zone "T5-INSIDE"
  dst-zone "T5-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 11
  action permit
  src-zone "T6-OUTSIDE"
  dst-zone "T6-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 12
  action permit
  src-zone "T6-INSIDE"
  dst-zone "T6-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 13
  action permit
  src-zone "T7-OUTSIDE"
  dst-zone "T7-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 14
  action permit
  src-zone "T7-INSIDE"
  dst-zone "T7-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 15
  action permit
  src-zone "T8-OUTSIDE"
  dst-zone "T8-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 16
  action permit
  src-zone "T8-INSIDE"
  dst-zone "T8-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 17
  action permit
  src-zone "T9-OUTSIDE"
  dst-zone "T9-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 18
  action permit
  src-zone "T9-INSIDE"
  dst-zone "T9-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 19
  action permit
  src-zone "T10-OUTSIDE"
  dst-zone "T10-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 21
  action permit
  src-zone "T11-OUTSIDE"
  dst-zone "T11-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 22
  action permit
  src-zone "T11-INSIDE"
  dst-zone "T11-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 23
  action permit
  src-zone "T12-OUTSIDE"
  dst-zone "T12-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 24
  action permit
  src-zone "T12-INSIDE"
  dst-zone "T12-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 25
  action permit
  src-zone "T13-OUTSIDE"
  dst-zone "T13-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 26
  action permit
  src-zone "T13-INSIDE"
  dst-zone "T13-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 27
  action permit
  src-zone "T14-OUTSIDE"
  dst-zone "T14-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 28
  action permit
  src-zone "T14-INSIDE"
  dst-zone "T14-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 29
  action permit
  src-zone "T15-OUTSIDE"
  dst-zone "T15-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 30
  action permit
  src-zone "T15-INSIDE"
  dst-zone "T15-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 31
  action permit
  src-zone "T16-OUTSIDE"
  dst-zone "T16-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 32
  action permit
  src-zone "T16-INSIDE"
  dst-zone "T16-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 34
  action permit
  src-zone "T17-INSIDE"
  dst-zone "T17-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 36
  action permit
  src-zone "T18-INSIDE"
  dst-zone "T18-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 51
  action permit
  src-zone "T19-OUTSIDE"
  dst-zone "T19-INSIDE"
  src-addr "**********/24"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  dst-addr "VDI-TO-T19-Tiaobanji"
  service "ICMP"
  service "TCP-445"
  service "TCP-3389"
exit
rule id 162
  action permit
  src-zone "T19-OUTSIDE"
  dst-zone "T19-INSIDE"
  src-addr "***************/32"
  dst-addr "***********/32"
  service "ICMP"
  service "TCP-52701"
exit
rule id 37
  action permit
  src-zone "T19-OUTSIDE"
  dst-zone "T19-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 38
  action permit
  src-zone "T19-INSIDE"
  dst-zone "T19-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 39
  action permit
  src-zone "T20-OUTSIDE"
  dst-zone "T20-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 40
  action permit
  src-zone "T20-INSIDE"
  dst-zone "T20-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 42
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "T4-********/24"
  dst-addr "T4-G3-WCSROUTER"
  service "HTTP"
  name "WCS-UI_to_T4-F5"
exit
rule id 43
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "T4-********/24"
  dst-addr "T4-TRANSROUTER"
  service "HTTP"
  name "BT-_to_TRANSROUTER"
exit
rule id 278
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "V_RMOAS18.6.114.252"
  dst-addr "R_WCS18.5.70.51"
  service "HTTP"
  name "IRSROUTER"
exit
rule id 44
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "T4-********/24"
  dst-addr "T4_OLTP-ECGATEWAY"
  service "HTTP"
  name "ELP_to_OLTP-ECGATEWAY"
exit
rule id 45
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "T4-********/24"
  dst-addr "T4-G3-MATGATEWAY"
  service "HTTP"
  name "AMS_to_G3-MATGATEWAY"
exit
rule id 226
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "Any"
  dst-addr "************/24"
  dst-addr "10.213.3.0/24"
  dst-addr "192.168.200.0/24"
  service "Any"
exit
rule id 222
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "Any"
  dst-addr "************/32"
  service "Any"
exit
rule id 219
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "************"
  src-addr "********38"
  dst-addr "TXY-10.215.41.178"
  dst-addr "10.215.41.170"
  service "tcp7003"
  service "HTTPS"
  service "ICMP"
  service "HTTP"
exit
rule id 71
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-*********-8"
  src-addr "NAT-********30"
  dst-addr "CSLC-**********/32"
  dst-addr "CSLC-**********/32"
  service "SSH"
exit
rule id 46
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-18-19"
  src-addr "*********-2-3"
  dst-addr "***********."
  service "TCP-30400"
  name "new_G3_to_old_G3"
exit
rule id 49
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-2-3"
  src-addr "*********-18-19"
  dst-addr "***********."
  service "TCP-31306"
  name "new_G3_to_old_G3_1"
exit      
rule id 50
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-2-3"
  src-addr "*********-18-19"
  dst-addr "***********."
  service "TCP-31306"
  name "new_G3_to_old_G3_2"
exit
rule id 52
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-9"
  src-addr "*********-31"
  dst-addr "**********/24"
  service "HTTP"
  name "new_G3_to_old_G3_3"
exit
rule id 53
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-9"
  src-addr "*********-31"
  dst-addr "************/24"
  service "TCP-5000"
  name "new_G3_to_bangong"
exit
rule id 87
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-***********"
  dst-addr "Any"
  service "Any"
exit
rule id 58
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-*********/32"
  src-addr "T4-*********/32"
  dst-addr "BG-**************-236"
  service "TCP-32600"
  name "XWHG3-To-BG"
exit
rule id 77
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-9"
  src-addr "NAT-********30"
  dst-addr "************"
  service "SSH"
exit
rule id 121
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-9"
  dst-addr "QC-*************"
  service "ICMP"
  service "TCP-8080"
  service "TCP-1433"
exit
rule id 70
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-*********-8"
  dst-addr "CSLC-**********/32"
  dst-addr "CSLC-**********/32"
  service "SSH"
  service "TCP-10050"
  service "TCP-9091"
exit
rule id 80
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-*********/32"
  dst-addr "CSLC-**********/32"
  service "TCP-8000-8010"
exit
rule id 82
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-*********-8"
  dst-addr "T1-**********"
  service "TCP-28080"
exit
rule id 83
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-*********/32"
  dst-addr "CSLC-**********/32"
  service "HTTP"
  service "HTTPS"
exit
rule id 114
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "T4-*********/32"
  dst-addr "T1-18.4.21.214"
  dst-addr "T1-*********"
  service "TCP-10050"
  service "TCP-9091"
exit
rule id 55
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********-31"
  src-addr "*********-9"
  dst-addr "Any"
  service "Any"
exit
rule id 123
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********/24"
  dst-addr "T1-*********-26"
  service "TCP-7001"
exit
rule id 127
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********/21"
  src-addr "*********/24"
  dst-addr "T1_18.0.2.0/24"
  service "TCP-8080"
exit
rule id 129
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********/24"
  dst-addr "jiamiji-**********"
  dst-addr "jiamiji-**********"
  dst-addr "jiamiji-**********"
  service "TCP-8018"
  service "TCP-8013"
  name "T4-to-jiamiji"
exit
rule id 135
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "**************/32"
  src-addr "**************/32"
  dst-addr "***********/32"
  service "Any"
exit
rule id 155
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********/24"
  dst-addr "**************/26"
  service "TCP-9092"
exit
rule id 179
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "***********"
  dst-addr "***********"
  service "TELNET"
  service "PING"
  service "TCP-8080"
exit
rule id 227
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "shuangyinsu_test"
  dst-addr "*************/32"
  service "Any"
exit
rule id 145
  action permit
  log session-start
  log session-end
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "**********/32"
  dst-addr "**********/24"
  dst-addr "************/24"
  service "TCP-9092"
  name "OMSTOKAFKA"
exit
rule id 56
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "********/24"
  dst-addr "********/24"
  dst-addr "*********/21"
  dst-addr "*********/21"
  service "TCP-31306"
  service "SSH"
exit
rule id 57
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "BG-**************-236"
  dst-addr "T4-*********/32"
  dst-addr "T4-*********/32"
  service "TCP-8011"
  name "BG-To-XWHG3"
exit
rule id 61
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "T4-*********-8"
  dst-addr "NAT-*********-30"
  service "NFS-NEW"
exit
rule id 62
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "T4-*********"
  dst-addr "NAT-*********"
  service "TCP-5000"
exit
rule id 63
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "T4-*********"
  dst-addr "NAT-*********"
  service "TCP-31306"
exit
rule id 64
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "T4-*********"
  dst-addr "NAT-*********"
  service "TCP-7001"
exit
rule id 65
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "T4-*********"
  dst-addr "NAT-*********"
  service "TCP-8004"
  service "TCP-5672"
exit
rule id 66
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "T4-*********/32"
  dst-addr "NAT-*********"
  service "SSH"
  service "TCP-8011"
  service "TCP-9091"
  service "TCP-9090"
  service "TCP-8001"
exit
rule id 74
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "***********/24"
  dst-addr "*********/24"
  service "TCP_8086"
exit
rule id 78
  action permit
  src-zone "T14-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************"
  dst-addr "**********/24"
  service "SSH"
exit
rule id 79
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************"
  dst-addr "*********/32"
  service "HTTP"
exit
rule id 81
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "NAT-*********"
  service "NTP"
exit
rule id 84
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "CSLC-**********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "NAT-*********"
  service "TCP-5044"
exit
rule id 100
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "T1OPS-********11"
  src-addr "T1OPS-********12"
  src-addr "T1OPS-********13"
  dst-addr "*********"
  service "Any"
  name "T1OPS-to_T4MS"
exit
rule id 109
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "T1-18.0.120.10"
  dst-addr "18.6.4.38"
  service "TCP-8080"
exit
rule id 115
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "T1-*********"
  dst-addr "T4-*********/32"
  service "TCP-9090"
exit
rule id 128
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "**************/32"
  src-addr "**************/32"
  src-addr "**************/32"
  src-addr "**************/32"
  src-addr "**************/32"
  dst-addr "18.6.4.38"
  dst-addr "*********-44"
  dst-addr "********/32"
  dst-addr "*********/32"
  service "Any"
exit
rule id 150
  action permit
  log session-start
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "***********"
  dst-addr "NAT-*********"
  service "Any"
  name "V3_*********"
exit
rule id 178
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "***********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "***********"
  service "TELNET"
  service "ICMP"
  service "tcp7003"
exit
rule id 180
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "***********"
  src-addr "***********"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "18.2.1.34"
  service "TELNET"
  service "ICMP"
  service "HTTP"
exit
rule id 181
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "18.2.1.34"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "***********"
  dst-addr "***********"
  service "TCP-20050"
  service "TCP-34433"
exit
rule id 183
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "104.24.0.101"
  src-addr "JiKai_124.65.173.138"
  src-addr "47.93.16.203/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "*********/32"
  service "TCP-7001"
  service "TCP_6001"
exit
rule id 184
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "JiKai_124.65.173.138"
  dst-addr "*********/32"
  service "TCP-7001"
exit
rule id 185
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "************"
  dst-addr "***********/24"
  service "PING"
  service "HTTP"
exit
rule id 187
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "********/24"
  dst-addr "CSLC-104.24.0.0/24"
  service "TCP-8080"
exit
rule id 190
  action permit
  src-zone "T21-INSIDE"
  dst-zone "T21-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "T21-ANY-IN-TO-OUT"
exit
rule id 191
  action permit
  src-zone "T21-OUTSIDE"
  dst-zone "T21-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "T21-ANY-OUT-TO-IN"
exit
rule id 192
  action permit
  src-zone "T22-OUTSIDE"
  dst-zone "T22-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "T22-ANY-OUT-TO-IN"
exit      
rule id 193
  action permit
  src-zone "T22-INSIDE"
  dst-zone "T22-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "T22-ANY-IN-TO-OUT"
exit
rule id 194
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "*********/24"
  dst-addr "18.0.93.0/24"
  service "tcp28090"
  service "tcp5000-5008"
  service "TCP6370"
  service "TCP-5000"
exit
rule id 8
  action permit
  src-zone "T4-INSIDE"
  dst-zone "T4-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 196
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********0"
  src-addr "*********1"
  src-addr "*********2"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "104.23.11.4"
  dst-addr "104.24.0.136"
  service "TCP-8081"
  service "TCP-8083"
exit
rule id 197
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*********0"
  src-addr "*********"
  src-addr "*********1"
  src-addr "*********2"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  src-addr "*********"
  dst-addr "104.24.0.136"
  dst-addr "104.23.11.4"
  service "PING"
  service "TCP-8081"
  service "TCP-8088"
exit
rule id 198
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "18.5.50.1-2"
  src-addr "18.5.10.1-51"
  dst-addr "**********/24"
  service "TCP-8090"
  service "PING"
  name "T3K8S_to_IN"
exit
rule id 211
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "ELP-***********-147"
  dst-addr "104.21.19.20/32"
  dst-addr "104.200.100.69/32"
  dst-addr "104.200.101.3/32"
  dst-addr "104.21.110.232/32"
  dst-addr "104.21.19.98/32"
  service "TCP-20150"
  service "TCP-8080"
  service "TCP-24433"
  service "TCP-14433"
  service "TCP-6006"
  service "TCP-9080"
exit
rule id 212
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "104.21.110.222/32"
  src-addr "104.21.19.99/32"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********1/32"
  dst-addr "********20/32"
  service "TCP_34443"
  service "TCP-8080"
exit
rule id 213
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "*************/24"
  dst-addr "104.200.100.69/32"
  service "TCP-34431-34435"
  service "TCP-24433"
  service "TCP-14433"
exit
rule id 214
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "T3_GW_*********-18"
  dst-addr "104.24.0.3/32"
  service "TCP-8081"
exit
rule id 218
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "wangliang-10.211.3.187/32"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  dst-addr "*********7"
  dst-addr "*********8/32"
  dst-addr "*********9/32"
  service "TELNET"
  service "SSH"
  service "TCP-21"
  name "BigG3VDI_to_T3_213"
exit
rule id 221
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "10.216.31.0/24"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  dst-addr "********3/32"
  service "TCP-21598"
  service "TCP-8061"
  service "TCP-7061"
  service "TCP-7051"
  service "TCP-8103"
exit
rule id 228
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "***********/24"
  src-addr "CSLC-***********/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "********4/32"
  service "PING"
  service "SSH"
exit
rule id 229
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "**********23/124"
  dst-addr "10.219.0.161/32"
  dst-addr "10.219.0.164/32"
  service "PING"
  service "HTTP"
  service "SSH"
exit
rule id 234
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "T18-VDI-**********-16-OUT"
  src-addr "**********/24"
  src-addr "104.23.15.0/24"
  src-addr "104.11.1.0/24"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  dst-addr "T18-SEC-10.217.4.0/24"
  dst-addr "T18-PUBLICSERVICE-10.217.2.0-24"
  dst-addr "T18-MS-10.217.5.0-24"
  dst-addr "T18-MONITOR-10.217.3.0-24"
  dst-addr "T18-DMZ-10.217.6.0-24"
  dst-addr "T18-CDDING-NET-10.217-0&1.0-24"
  service "Any"
  name "VDI-TO-XIETONGGONGZUO"
exit
rule id 235
  action permit
  disable
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "T18-LISHUAIQILINSHIWEIHU-************-24-OUT"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  dst-addr "T18-LISHUAIQILINSHIWEIHUA-***********-32"
  service "Any"
  name "LISHUAIQILINSHIWEIHU"
exit
rule id 236
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "104.21.51.41-46"
  src-addr "10.213.0.9_G3zidonghuiTest"
  src-addr "10.213.0.63_G3AutoTest"
  src-addr "10.216.1.49_ads"
  src-addr "YZTESTLAB-10-SNAT-18.6.31.0/24"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  src-addr "***********-65"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "************/24"
  src-addr "***********/24"
  src-addr "************"
  src-addr "***********-72"
  dst-addr "10.217.0.1/32-coding"
  service "HTTP"
exit
rule id 237
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************"
  src-addr "*********/8"
  src-addr "YZTESTLAB-104-SNAT-18.6.30.0/24"
  src-addr "YUNYINGVDI-138"
  dst-addr "10.217.0.1/32-coding"
  service "HTTP"
  service "HTTPS"
  name "20211129"
exit
rule id 270
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "需求管理工具***********"
  dst-addr "10.217.0.1/32-coding"
  service "HTTP"
  service "HTTPS"
  name "需求管理工具"
exit
rule id 238
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "CSL-office"
  dst-addr "10.217.0.1/32-coding"
  service "HTTP"
  service "HTTPS"
  name "Linshi-daoqishijian20210228-wangqingshuai"
exit
rule id 239
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "YZ-10.218.129.0/24"
  src-addr "VDI-10.217.129-133.0/24"
  src-addr "AD_**********"
  src-addr "VDI-10.217.134-140.0/24"
  dst-addr "10.217.2.0/24"
  service "Any"
  name "luyu-DNS-01"
exit
rule id 242
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T8-INSIDE"
  src-addr "10.218.0.0/16"
  dst-addr "zabbix-**********1"
  service "ICMP"
  service "TCP-10050"
  service "SNMP"
  name "YZUDT-zabbix"
exit
rule id 243
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "10.219.4.253"
  src-addr "**********/22"
  dst-addr "10.217.6.1-2"
  service "NTP"
  name "NTP"
exit
rule id 244
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "10.219.4.254"
  src-addr "10.219.0.252"
  dst-addr "10.217.6.3"
  service "HTTPS"
  service "HTTP"
  name "YUM"
exit
rule id 249
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "CSLOOffice"
  dst-addr "10.217.0.1/32-coding"
  service "HTTP"
  service "HTTPS"
  name "YunyyingOffice-Coding"
exit
rule id 251
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "Ceshi_104.21.0.0/16"
  src-addr "10.211.11.0/24"
  src-addr "10.211.3."
  dst-addr "OBDB-10.220.4.4-6"
  service "Any"
exit
rule id 250
  action deny
  disable
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "Any"
  dst-addr "OBDB-10.220.4.4-6"
  service "Any"
exit
rule id 322
  action permit
  src-zone "T8-OUTSIDE"
  dst-zone "T8-INSIDE"
  src-addr "**********/24"
  dst-addr "***********/32"
  service "TCP3306"
  name "20230804"
exit
rule id 323
  action permit
  src-zone "T8-OUTSIDE"
  dst-zone "T8-INSIDE"
  src-addr "************/24"
  dst-addr "**********/24"
  service "SSH"
  service "TCP-2222"
  name "20230804(2)"
exit
rule id 326
  action permit
  src-zone "T8-OUTSIDE"
  dst-zone "T8-INSIDE"
  src-addr "************"
  dst-addr "************"
  service "TCP-8088"
  name "20230804(3)"
exit
rule id 35
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-Office_**********/22"
  src-addr "**********/24"
  src-addr "**********/24"
  src-addr "***********/24"
  src-addr "***********/32"
  src-addr "10.219.1.100_jiaobenzhixing"
  src-addr "*************/32"
  src-addr "Maven/vue"
  src-addr "10.219.5.159"
  dst-addr "Nexus-************-204"
  service "TCP-8088"
  service "HTTP"
  service "TCP-8081"
exit
rule id 186
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "**********05/32"
  src-addr "************/32"
  dst-addr "10.217.0.1/32-coding"
  service "HTTP"
  service "HTTPS"
  service "SSH"
  name "yanfa_coding"
exit
rule id 200
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "**********05/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  dst-addr "***********/32"
  service "HTTPS"
exit
rule id 201
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "10.213.3.0/24"
  src-addr "**********/24"
  dst-addr "10.217.0.1/32-coding"
  service "HTTP"
  name "sftp-to-coding"
exit
rule id 202
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "quexiangaojing"
  dst-addr "SMTP-**********"
  service "SMTP"
  name "RDCOPS"
exit
rule id 208
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "*************/32"
  dst-addr "***********/32"
  dst-addr "**********-4"
  dst-addr "**********1-14"
  dst-addr "**********1-18"
  service "TCP-49712"
  service "TCP-49681"
  service "TCP-139"
  service "TCP-135"
  service "TCP-445"
  service "TCP-10000"
  name "xietongbeifen"
exit
rule id 215
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "***********/32"
  dst-addr "***********-42"
  service "TCP-30031"
  name "jiankong"
exit      
rule id 175
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "**********/*************/***********"
  dst-addr "shujuzhongtai_**********/24"
  service "TCP-3389"
  service "HTTPS"
  service "HTTP"
  service "SSH"
exit
rule id 230
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************/32"
  src-addr "************/32"
  dst-addr "**********/32"
  dst-addr "**********11/32"
  service "HTTPS"
  service "TCP7810-7859"
exit
rule id 254
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "腾讯云10.216.40"
  dst-addr "shujuzhongtai_**********/24"
  service "PING"
  service "TCP-8083"
  name "腾讯云10.216"
exit
rule id 233
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "**********/24"
  src-addr "**********/24"
  dst-addr "shujuzhongtai_**********/24"
  service "TCP-8000-8100"
  service "HTTP"
  service "HTTPS"
  service "SSH"
  service "TCP7180-50070-25010-19888-61000"
  service "ICMP"
  name "BigG3VDI"
exit
rule id 275
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "**********/24"
  src-addr "**********/24"
  dst-addr "**********"
  service "Any"
  name "BIGG3VDI TO 18.5"
exit
rule id 248
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "***********"
  src-addr "腾讯云10.216.40"
  src-addr "需求管理工具***********"
  dst-addr "**********"
  service "ICMP"
  service "HTTPS"
  service "SSH"
exit
rule id 257
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "报表集市G32云上"
  dst-addr "T4-数据中台01"
  service "ICMP"
  service "PING"
  service "TCP-8089"
  service "TCP-8081"
  service "TCP8081-8089"
exit
rule id 256
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "ADS***********"
  dst-addr "T4-数据中台01"
  service "PING"
  service "SSH"
  name "ADS ***********"
exit
rule id 253
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "乐透归集库"
  src-ip ***********/24
  src-ip ***********/24
  src-ip ***********/24
  src-ip ***********/24
  src-ip ***********/24
  src-ip ***********/24
  dst-addr "T4-数据中台01"
  service "TCP7809"
  service "TCP3306"
  service "ICMP"
  service "TCP-8087"
  service "SSH"
  service "TCP7810"
  name "乐透归集访问T4数据中台"
exit
rule id 262
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "*********/8"
  src-addr "10/8"
  dst-addr "shujuzhongtai_**********/24"
  service "Any"
  name "104&10to10_220"
exit
rule id 259
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "JCVSCC"
  dst-addr "Any"
  service "Any"
  name "JCVSC"
exit
rule id 279
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "***********/24"
  dst-addr "10.220.7.6_25"
  service "SSH"
  name "DWS"
exit
rule id 223
  action deny
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "Any"
  dst-addr "shujuzhongtai_**********/24"
  service "Any"
exit
rule id 224
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************"
  src-addr "************"
  src-addr "10.219.5.52_jiagoubu"
  src-addr "************/32"
  dst-addr "10.217.0.1/32-coding"
  service "SSH"
exit
rule id 231
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "10.213.3.0/24"
  src-addr "*********/8"
  src-addr "CSLC-Office_**********/22"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-***********/24"
  src-addr "CSLC-172.20.16-19.0/24"
  src-addr "yun-**********/16"
  src-addr "yun-**********/16"
  src-addr "G3-**********/24"
  src-addr "G3-**********/24"
  src-addr "***********"
  src-addr "***********"
  src-addr "***********"
  src-addr "10.248.121.1"
  dst-addr "Nexus-************-204"
  service "TCP-8088"
  service "TCP-8081"
exit
rule id 232
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "10.219.1.100_jiaobenzhixing"
  dst-addr "**********-3(guanli)"
  dst-addr "**********5_17_18(code ku)"
  service "TCP-2222"
  service "TCP-6443"
  service "TCP-31099"
exit
rule id 240
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************/32"
  dst-addr "10.217.2.0/24"
  service "Any"
exit
rule id 261
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "10_104/8"
  dst-addr "T3_体彩APP"
  service "TCP-8080"
  service "PING"
  name "营销中心上云"
exit
rule id 263
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************"
  dst-addr "*********"
  service "Any"
  name "安全漏扫"
exit
rule id 264
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "Any"
  dst-addr "*********"
  service "HTTPS"
  service "HTTP"
  name "风险处置系统"
exit
rule id 265
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**********/24"
  dst-addr "********/24"
  service "Any"
  name "coding backuptest"
exit
rule id 266
  action permit
  src-zone "T3-INSIDE"
  dst-zone "T3-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "anytoany"
exit
rule id 267
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "*********/24"
  src-addr "********/24"
  dst-addr "**********/32"
  dst-addr "************"
  service "TCP58001"
  service "TCP-8088"
  name "codingbackuptest"
exit
rule id 269
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "***********/32"
  src-addr "********/24"
  dst-addr "**********/32"
  dst-addr "************"
  service "TCP-2222"
  service "TCP-8088"
  service "TCP-8081"
  name "codingbackup"
exit
rule id 271
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "**********"
  dst-addr "**********"
  service "HTTP"
  name "BOS-v22.11.11"
exit
rule id 282
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "************"
  dst-addr "***********-178"
  dst-range ********* *********
  service "SSH"
  service "TCP-8000"
  name "************-***********-178"
exit
rule id 299
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "ACLLB-SELF"
  src-addr "ACLLB-SNAT"
  dst-addr "Any"
  service "Any"
  name "internetservicelinshitest"
exit
rule id 7
  action permit
  src-zone "T4-OUTSIDE"
  dst-zone "T4-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 273
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "*************"
  dst-addr "*********1"
  service "TCP-7001"
  name "数据提取接入开放平台"
exit
rule id 274
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "Any"
  dst-addr "**********/24"
  service "SSH"
  name "**********/24"
exit
rule id 276
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "VDI-LINUXSERVER"
  dst-addr "************"
  service "TCP-8088"
  name "VDI-LINXUSERVER"
exit
rule id 277
  action permit
  src-zone "T14-INSIDE"
  dst-zone "T14-OUTSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "T14 INSIDE TO OUTSIDE ANY"
exit
rule id 280
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "**********/24"
  src-addr "**********/24"
  src-addr "10.211.5&6&12.0/24"
  dst-addr "**********/24"
  dst-addr "**********"
  service "Any"
  name "OPS"
exit
rule id 281
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "需求管理工具***********"
  dst-addr "SMTP-**********"
  dst-addr "SMTP-**********"
  service "SMTP"
  service "SMTPS"
  service "TCP-587"
  service "HTTP"
  service "HTTPS"
  name "xqtctool-smtp"
exit
rule id 216
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "ICMP"
exit
rule id 283
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-ip ************/32
  dst-ip ***********/32
  service "TCP8000UDP"
  name "IAST工具"
exit
rule id 284
  action permit
  src-zone "T17-OUTSIDE"
  dst-zone "T17-INSIDE"
  src-addr "DEV_DESK"
  dst-addr "SHUJUJIANMO-TOOLS"
  service "TCP-31306"
  service "SSH"
  name "jujujianmo-tools"
exit
rule id 33
  action permit
  src-zone "T17-OUTSIDE"
  dst-zone "T17-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 287
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "*************-168"
  src-range ************ ************
  dst-ip ***********/32
  service "ICMP"
  service "SSH"
  name "信息发布中心-开放平台SFTP"
exit
rule id 291
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-ip ***********/32
  dst-ip ************/32
  service "ICMP"
  service "8081"
  name "成分分析工具tomaven仓库中"
exit
rule id 292
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**************-*************"
  dst-addr "********28/32"
  dst-addr "***********"
  service "SSH"
  name "运营数字化两网三端访问G32开放平台SFTP"
exit
rule id 319
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "*********"
  dst-addr "***********"
  service "TCP-17090"
  service "TCP-389"
  service "TCP3306"
  service "TCP-17080"
  service "TCP-18060"
  service "SSH"
  name "电子安全认证服务pki/ca"
exit
rule id 297
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "************"
  dst-addr "BT"
  service "SSH"
  name "SFTPTOBT"
exit
rule id 298
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "ACLLB-SELF"
  src-addr "ACLLB-SNAT"
  dst-addr "********/24"
  service "TCP8001-8049"
  name "internetservice linshitest"
exit
rule id 296
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************/24"
  src-addr "************/24"
  dst-addr "10.217.0.1/32-coding"
  dst-addr "**********/24"
  service "Any"
  name "coding-23.03.28"
exit
rule id 5
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
rule id 295
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "需求管理工具***********"
  src-addr "*************"
  dst-addr "***********"
  service "TCP3306"
  name "codingbackuptoxuqiutongchoutools"
exit
rule id 301
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************/24"
  src-addr "**********-1.0/24"
  dst-addr "**********"
  service "Any"
  name "OMS"
exit
rule id 302
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "Any"
  dst-addr "**********"
  service "Any"
  name "**********/16-**********"
exit
rule id 316
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "CSLC-**********/32"
  dst-addr "***********/32"
  service "TCP-32600"
  name "agent_to_apos"
exit
rule id 304
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "*********"
  src-addr "***********-173"
  dst-addr "***********-19"
  dst-addr "**********-4"
  dst-addr "***********-14"
  service "TCP-8443"
  service "HTTPS"
  service "HTTP-EXT"
  service "HTTP"
  service "TCP-32600"
  service "7443"
  service "TCP-8080"
  name "4.99to10.217"
exit
rule id 312
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***************"
  dst-addr "Any"
  service "Any"
  name "***************"
exit
rule id 314
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************"
  dst-addr "10.217.0.1/32-coding"
  service "Any"
  name "coding"
exit
rule id 331
  action permit
  src-zone "T16-OUTSIDE"
  dst-zone "T16-INSIDE"
  src-addr "***********/张跃的VDI测试"
  src-addr "***********"
  dst-addr "10.129.4.135"
  service "TCP-9001"
  name "VDI访问WCS / BOS"
exit
rule id 333
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "**************"
  dst-addr "**********02"
  service "TCP-7002"
  name "运营公司访问开放平台NGINX"
exit
rule id 334
  action permit
  src-zone "T8-OUTSIDE"
  dst-zone "T8-INSIDE"
  src-addr "***********-30"
  dst-addr "**********"
  service "tcp:20443"
  name "20230926李洋需求"
exit
rule id 337
  action permit
  src-addr "***********/24"
  src-addr "***********/24"
  src-addr "***********/24"
  src-addr "***********/24"
  dst-addr "**********"
  service "TCP-8080"
  name "G3chuanzu-to-zhongtai"
exit
rule id 338
  action permit
  src-addr "**********-22"
  src-addr "**********-42"
  dst-addr "********/24"
  service "TCP-8000"
  service "TCP-8088"
  name "tance-to-mifu"
exit
rule id 339
  action permit
  src-addr "***********/24"
  dst-addr "********/24"
  service "udp-161"
  name "mifujiankong"
exit
rule id 341
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "************"
  dst-addr "**********"
  service "tcp:20443"
  service "IMAP-993"
  name "安卓终端编译器开通邮箱服务器网络信息"
exit
rule id 342
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "**********"
  dst-addr "***********/32"
  service "TCP-8080"
  service "HTTP"
  name "中国体育彩票集成发布与部署系统 AOPS"
exit
rule id 343
  action permit
  src-zone "T18-OUTSIDE"
  dst-zone "T18-INSIDE"
  src-addr "**********-16"
  src-addr "***********"
  dst-addr "**********"
  service "25"
  name "]国家实验室二用户体验环境用户需求使用lottery邮箱进行功能测试体验"
exit
rule id 344
  action permit
  src-zone "T3-OUTSIDE"
  dst-zone "T3-INSIDE"
  src-addr "*********5"
  dst-addr "新机器开通到运行ca以及radk服务的端口"
  dst-addr "新机器开通到KM服务的端口"
  dst-addr "新机器开通到运行ca、km服务以及radk服务的端口"
  dst-addr "新机器开通到运行ca、km数据库服务的端口"
  dst-addr "新机器开通到新ldap和ibmldap服务的端口"
  dst-addr "新机器开通到新ldap和oracle服务的端口"
  service "TCP-1521"
  service "TCP4444"
  service "TCP-389"
  service "TCP-19080"
  service "TCP-18090"
  service "SSH"
  service "TCP-18443"
  service "HTTPS"
  service "10288-10289"
  service "TCP3306"
  service "TCP-17080"
  service "TCP-18060"
  name "PKICA全流程测试"
exit
tcp-seq-check-disable
l2-nonip-action drop
no alg auto
no alg ftp
no alg tftp
no alg msrpc
no alg sqlnetv2
no alg rsh
no alg rtsp
no alg http
no alg sunrpc
no alg ras
no alg q931
no alg sip
no alg pptp
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server host ************ version 2c community 8z6q26pTxt8qDeu9NBCo3DJ7I8YZ ro
snmp-server host *********** version 2c community O/ZrKDXHq+9MGZ//6vY9DGQ8mGky ro
snmp-server host *********** version 2c community 3EAGb4o6D7oDLWajqklzO2uLRWcp ro
snmp-server host *********** version 2c community 3EAGb4o6D7oDLWajqklzO2uLRWcp ro
snmp-server trap-host *********** version 2c community 8z6q26pTxt8qDeu9NBCo3DJ7I8YZ port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application
exit
statistics-set "predef_intrusion_app"
  target-data intrusion id 32 record-history
exit
statistics-set "predef_virus_virus"
  target-data virus-num id 33 record-history
exit
statistics-set "predef_if_sess"
  target-data session id 6 record-history
  group-by interface
exit
statistics-set "predef_zone_bw"
  target-data bandwidth id 7 record-history
  group-by zone directional
exit
statistics-set "predef_zone_sess"
  target-data session id 8 record-history
  group-by zone
exit      
no longlife-sess-percent
no sms disable
lan-addr private_network

End

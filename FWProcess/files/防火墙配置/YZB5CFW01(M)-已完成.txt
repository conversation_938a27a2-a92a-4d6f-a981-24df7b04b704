YZB5CFW01(M)# show config

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "WSJC_vrf"
exit
ip vrouter "PWJC_vrf"
exit
ip vrouter "TCloud_vrf"
exit
ip vrouter "BlackBox_vrf"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit      
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
zone "UAT-untrust"
exit
zone "UAT-trust"
exit
zone "WSJC-untrust"
exit
zone "WSJC-trust"
exit      
zone "PWJC-untrust"
exit
zone "PWJC-trust"
exit
zone "TCloud-untrust"
exit
zone "TCloud-trust"
exit
zone "BlackBox-untrust"
exit
zone "BlackBox-trust"
exit
interface vswitchif1
exit
interface MGT0 local
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit
interface ethernet0/2
exit      
interface ethernet0/3
exit
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit
interface aggregate1
exit      
interface aggregate1.102
exit
interface aggregate1.103
exit
interface aggregate1.104
exit
interface aggregate1.105
exit
interface aggregate1.106
exit
interface aggregate1.107
exit
interface aggregate1.108
exit
interface aggregate1.109
exit
interface aggregate1.110
exit
interface aggregate1.111
exit
address "private_network"
exit
address "CIMS_Servers"
exit      
address "net_**********"
exit
address "net_********"
exit
address "IP_********"
exit
address "IP_*******"
exit
address "net_**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "IP_**********"
exit
address "IP_***********"
exit
address "*********"
exit
address "*********"
exit      
address "**********"
exit
address "Monitor_IP"
exit
address "IP_***********"
exit
address "IP_**********-24"
exit
address "net_***********"
exit
address "***********"
exit
address "**********-128"
exit
address "**********"
exit
address "OCS"
exit
address "********/24"
exit
address "************/24"
exit
address "**********"
exit      
address "*********-62"
exit
address "*********-4"
exit
address "**********-55"
exit
address "**********-74"
exit
address "************-66"
exit
address "************-98"
exit
address "***********-40"
exit
address "**********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit      
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit      
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "*********/16"
exit
address "***********/24"
exit
address "********/32"
exit
address "********/32"
exit
address "*********/24"
exit
address "*************/32"
exit
address "**********/32"
exit
address "**********/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "***********/28"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "************/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit      
address "*********/32"
exit
address "*********0/32"
exit
address "*********1/32"
exit
address "*********2/32"
exit
address "**********/27"
exit
address "*******/32"
exit
address "**********/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "**********/28"
exit      
address "*********/32"
exit
address "*********/32"
exit
address "********/32"
exit
address "********/32"
exit
address "********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "3.20.1.101/32"
exit
address "3.21.15.1/32"
exit
address "3.21.15.2/32"
exit
address "3.21.15.21/32"
exit
address "3.21.15.22/32"
exit
address "3.21.15.28/32"
exit      
address "3.21.15.0/24"
exit
address "3.50.10.61/32"
exit
address "3.50.10.62/32"
exit
address "3.50.10.63/32"
exit
address "3.50.10.64/32"
exit
address "3.20.11.11/32"
exit
address "3.20.11.12/32"
exit
address "198.3.100.94/32"
exit
address "3.9.1.23/32"
exit
address "*********/32"
exit
address "4.13.11.21/32"
exit
address "**********/32"
exit      
address "4.13.11.30/32"
exit
address "4.13.11.31/32"
exit
address "4.13.11.32/32"
exit
address "4.13.11.40/32"
exit
address "*********/22"
exit
address "198.3.10.0/24"
exit
address "3.20.1.3/32"
exit
address "4.50.10.11/32"
exit
address "4.50.10.12/32"
exit
address "4.50.10.13/32"
exit
address "4.50.10.14/32"
exit
address "*******/32"
exit      
address "4.50.10.21/32"
exit
address "4.50.10.22/32"
exit
address "***********/32"
exit
address "4.14.100.22/32"
exit
address "3.22.16.7/32"
exit
address "3.22.16.1/32"
exit
address "4.14.100.23/32"
exit
address "4.14.100.24/32"
exit
address "4.14.100.25/32"
exit
address "4.14.100.26/32"
exit
address "4.14.100.27/32"
exit
address "4.14.100.28/32"
exit      
address "4.14.100.29/32"
exit
address "4.14.100.30/32"
exit
address "4.14.100.31/32"
exit
address "4.14.100.32/32"
exit
address "4.14.100.33/32"
exit
address "4.14.100.34/32"
exit
address "4.14.100.35/32"
exit
address "4.14.100.36/32"
exit
address "4.14.100.37/32"
exit
address "4.14.100.38/32"
exit
address "4.14.100.39/32"
exit
address "***********/32"
exit      
address "4.14.100.0/26"
exit
address "4.12.1.0/29"
exit
address "4.12.1.8/32"
exit
address "************/32"
exit
address "4.102.12.11/32"
exit
address "4.102.12.12/32"
exit
address "***********/32"
exit
address "4.102.12.31/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "4.103.20.11/32"
exit      
address "4.103.20.12/32"
exit
address "3.29.12.11/32"
exit
address "3.29.12.12/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "********/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit      
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit      
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "************/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********4/32"
exit
address "*********5/32"
exit
address "*********8/32"
exit
address "*********9/32"
exit      
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "*********0/32"
exit
address "**********/24"
exit
address "198.3.100.0/25"
exit
address "***********/24"
exit
address "************/32"
exit
address "3.28.20.0/24"
exit
address "3.100.220.200/32"
exit
address "3.9.1.201/32"
exit
address "************/24"
exit      
address "10.194.119.8/32"
exit
address "*********/24"
exit
address "4.27.10.101/32"
exit
address "4.27.10.102/32"
exit
address "4.27.10.121/32"
exit
address "4.27.10.122/32"
exit
address "4.35.10.10/32"
exit
address "3.252.18.251/32"
exit
address "4.27.10.110/32"
exit
address "4.27.10.130/32"
exit
address "4.27.10.111/32"
exit
address "4.27.10.112/32"
exit      
address "4.27.10.131/32"
exit
address "4.27.10.132/32"
exit
address "4.27.10.11/32"
exit
address "4.27.10.12/32"
exit
address "4.28.10.0/24"
exit
address "4.103.15.20/32"
exit
address "4.103.15.21/32"
exit
address "4.103.15.22/32"
exit
address "4.103.15.10/32"
exit
address "4.103.15.11/32"
exit
address "4.103.15.12/32"
exit
address "4.103.15.13/32"
exit      
address "***********/32"
exit
address "4.18.10.10/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "4.103.19.10/32"
exit
address "*********/32"
exit
address "3.22.0.0/16"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "3.22.10.1/32"
exit
address "3.22.10.2/32"
exit
address "3.22.10.66/32"
exit
address "3.22.10.67/32"
exit
address "*********1/32"
exit
address "*********2/32"
exit
address "*********3/32"
exit
address "*********2/32"
exit
address "***********/32"
exit      
address "**********1/32"
exit
address "**********2/32"
exit
address "**********/32"
exit
address "*********7/32"
exit
address "*********8/32"
exit
address "*********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "***********/32"
exit
address "*********0/32"
exit      
address "**********0/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "*********/32"
exit
address "**********/32"
exit
address "*********/32"
exit      
address "*********/32"
exit
address "**********/32"
exit
address "*********5/32"
exit
address "*********6/32"
exit
address "*********9/32"
exit
address "*********1/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "*********/32"
exit
address "***********/32"
exit      
address "***********/32"
exit
address "***********/32"
exit
address "**********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "***********/32"
exit
address "*********/32"
exit      
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "*********/24"
exit
address "*********/28"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "*********/24"
exit
address "*********/24"
exit
address "**********/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "**********/32"
exit      
address "**********/32"
exit
address "**********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "*********/32"
exit
address "*********/32"
exit
address "*********/24"
exit
address "***********/32"
exit
address "*********"
exit      
address "*************"
exit
address "************"
exit
address "*************"
exit
address "*************"
exit
address "***********"
exit
address "4.9.5.1"
exit
address "***********-134"
exit
address "ip_public"
exit
address "***********/32"
exit
address "************"
exit
address "********-2"
exit
address "*********-42"
exit      
address "**********-22"
exit
address "**********-52"
exit
address "**********"
exit
address "*********-12"
exit
address "**********"
exit
address "***********"
exit
address "*********-23"
exit
address "*********"
exit
address "********1"
exit
address "************"
exit
address "************/24"
exit
address "************/24"
exit      
address "********/24"
exit
address "*******/32"
exit
address "************-35"
exit
address "NAS_*********"
exit
address "*************"
exit
address "DNS_**********"
exit
address "**********-42"
exit
address "***********/24"
exit
address "************/24"
exit
aaa-server "local" type local
exit
aaa-server "test" type radius
exit
track "trackobj1"
exit      
service "TCP_8080"
  tcp dst-port 8080 
exit
service "TCP_4100"
  tcp dst-port 4100 
exit
service "TCP_4200"
  tcp dst-port 4200 
exit
service "TCP_4300"
  tcp dst-port 4300 
exit
service "TCP_4400"
  tcp dst-port 4400 
exit
service "TCP_10050"
  tcp dst-port 10050 
exit
service "TCP_10051"
  tcp dst-port 10051 
exit
service "tcp-10014"
  tcp dst-port 10014 
exit      
service "tcp-10006"
  tcp dst-port 10006 
exit
service "tcp-10007"
  tcp dst-port 10007 
exit
service "tcp-443"
  tcp dst-port 443 
exit
service "tcp-10001-10009"
  tcp dst-port 10001 10009 
exit
service "tcp-10012"
  tcp dst-port 10012 
exit
service "tcp-10013"
  tcp dst-port 10013 
exit
service "tcp-25"
  tcp dst-port 25 
exit
service "tcp-110"
  tcp dst-port 110 
exit      
service "tcp-3306"
  tcp dst-port 3306 
exit
service "tcp-21"
  tcp dst-port 21 
exit
service "tcp-20"
  tcp dst-port 20 
exit
service "tcp-9092"
  tcp dst-port 9092 
exit
service "tcp-1918"
  tcp dst-port 1918 
exit
service "tcp-63358"
  tcp dst-port 63358 
exit
service "tcp-80"
  tcp dst-port 80 
exit
service "tcp-135"
  tcp dst-port 135 
exit      
service "udp-135"
  udp dst-port 135 
exit
service "tcp-136"
  tcp dst-port 136 
exit
service "udp-136"
  udp dst-port 136 
exit
service "tcp-137"
  tcp dst-port 137 
exit
service "udp-137"
  udp dst-port 137 
exit
service "tcp-138"
  tcp dst-port 138 
exit
service "udp-138"
  udp dst-port 138 
exit
service "tcp-139"
  tcp dst-port 139 
exit      
service "udp-139"
  udp dst-port 139 
exit
service "tcp-445"
  tcp dst-port 445 
exit
service "udp-445"
  udp dst-port 445 
exit
service "tcp-22"
  tcp dst-port 22 
exit
service "tcp-7800-8849"
  tcp dst-port 7800 8849 
exit
service "tcp-8020"
  tcp dst-port 8020 
exit
service "tcp-10000"
  tcp dst-port 10000 
exit
service "tcp-10001"
  tcp dst-port 10001 
exit      
service "tcp-7001"
  tcp dst-port 7001 
exit
service "tcp-514"
  tcp dst-port 514 
exit
service "udp-514"
  udp dst-port 514 
exit
service "tcp-10005"
  tcp dst-port 10005 
exit
service "tcp-10009"
  tcp dst-port 10009 
exit
service "tcp-10010"
  tcp dst-port 10010 
exit
service "tcp-10011"
  tcp dst-port 10011 
exit
service "tcp-31306"
  tcp dst-port 31306 
exit      
service "udp-162"
  udp dst-port 162 
exit
service "tcp-111"
  tcp dst-port 111 
exit
service "udp-111"
  udp dst-port 111 
exit
service "tcp-2049"
  tcp dst-port 2049 
exit
service "udp-2049"
  udp dst-port 2049 
exit
service "tcp-4046"
  tcp dst-port 4046 
exit
service "udp-4046"
  udp dst-port 4046 
exit
service "tcp-635"
  tcp dst-port 635 
exit      
service "udp-635"
  udp dst-port 635 
exit
service "tcp-1521"
  tcp dst-port 1521 
exit
service "tcp-3389"
  tcp dst-port 3389 
exit
service "tcp-28081-28090"
  tcp dst-port 28081 28090 
exit
service "tcp-28080"
  tcp dst-port 28080 
exit
service "tcp-8080-8089"
  tcp dst-port 8080 8089 
exit
service "tcp-19080"
  tcp dst-port 19080 
exit
service "tcp-7004"
  tcp dst-port 7004 
exit      
service "tcp-23"
  tcp dst-port 23 
exit
service "tcp-161"
  tcp dst-port 161 
exit
service "tcp-8999"
  tcp dst-port 8999 
exit
service "tcp-9080"
  tcp dst-port 9080 
exit
service "tcp-7809-7829"
  tcp dst-port 7809 7829 
exit
service "tcp-8443"
  tcp dst-port 8443 
exit
service "tcp-1443"
  tcp dst-port 1443 
exit
service "tcp-6001"
  tcp dst-port 6001 
exit      
service "tcp-61894"
  tcp dst-port 61894 
exit
service "tcp-35640"
  tcp dst-port 35640 
exit
service "tcp-35641"
  tcp dst-port 35641 
exit
service "tcp-7070"
  tcp dst-port 7070 
exit
service "tcp-5222"
  tcp dst-port 5222 
exit
service "tcp-6002"
  tcp dst-port 6002 
exit
service "udp-53"
  udp dst-port 53 
exit
service "TCP-10050"
  tcp dst-port 10050 
exit      
service "tcp-30220"
  tcp dst-port 30220 
exit
service "tcp-34443"
  tcp dst-port 34443 
exit
service "tcp-30223"
  tcp dst-port 30223 
exit
service "tcp-30221"
  tcp dst-port 30221 
exit
service "tcp-30000"
  tcp dst-port 30000 
exit
service "tcp-11001"
  tcp dst-port 11001 
exit
service "tcp-11002"
  tcp dst-port 11002 
exit
service "tcp-8000"
  tcp dst-port 8000 
exit      
service "tcp-8083"
  tcp dst-port 8083 
exit
service "tcp-52200"
  tcp dst-port 52200 
exit
service "TCP4045-4049"
  tcp dst-port 4045 4049 
exit
service "UDP4045-4049"
  udp dst-port 4045 4049 
exit
service "tcp-11025"
  tcp dst-port 11025 
exit
service "tcp-8088"
  tcp dst-port 8088 
exit
service "udp-161"
  udp dst-port 161 
exit
sandbox-profile "predef_low"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_middle"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_high"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  file-type swf
  file-type rar
  file-type zip
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
sandbox-profile "predef_pe"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
url-profile "no-url"
exit
track "trackobj1"
  interface aggregate1 
exit
aaa-server "test" type radius
  host ********** vrouter "mgt-vr"
  port 1821
  secret 7haEjPhcWOQnehfCbd+xw2uLRWc5
exit
admin user "hillstone"
  password bR/cTIQhMsIH2BStCVC994aweZ
        password-expiration 1628601161
  role "admin"
  access console
  access telnet
  access ssh
  access http
  access https
exit
admin user "netadmin"
  password yrcQtyCIXfaQ9psNgXcywoNQ+V
        password-expiration 1619465132
  role "admin"
  access console
  access telnet
  access ssh
  access http
  access https
exit
no logging traffic session to buffer
no logging traffic nat to buffer
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog ************ vrouter "mgt-vr" udp 514 type config
logging syslog ************ vrouter "mgt-vr" udp 514 type network
logging syslog ************ vrouter "mgt-vr" udp 514 type threat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic session
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
  crl configure
  exit
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
pki trust-domain "testaaa"
  enrollment terminal
  crl configure
  exit
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "CIMS_Servers"
  ip *********/24
  range ********** **********
  range **********1 **********0
  range *********** ***********
  range ************ ***********0
  range ************ ************
exit
address "net_**********"
  ip **********/16
exit
address "net_********"
  ip ********/16
exit
address "IP_********"
  ip ********/32
exit
address "IP_*******"
  ip *******/32
exit
address "net_**********"
  ip **********/16
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "IP_**********"
  ip **********/32
exit
address "IP_***********"
  ip ***********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "**********"
  ip **********/32
exit
address "Monitor_IP"
  ip **********/32
  ip *********/32
  ip **********/32
exit
address "IP_***********"
  ip ***********/32
exit
address "IP_**********-24"
  range ********** **********
exit
address "net_***********"
  ip ***********/24
exit
address "***********"
  ip ***********/32
exit
address "**********-128"
  ip **********/32
  ip ********24/32
  ip **********/32
  ip **********/32
  ip **********/32
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "OCS"
  ip **********/24
exit      
address "********/24"
  ip ********/24
exit
address "************/24"
  ip ************/24
exit
address "**********"
  ip **********/32
exit
address "*********-62"
  range ********* *********
exit
address "*********-4"
  range ********* *********
exit
address "**********-55"
  range ********** **********
exit
address "**********-74"
  range ********** **********
exit
address "************-66"
  range ************ ************
exit      
address "************-98"
  range ************ ************
exit
address "***********-40"
  range *********** ***********
exit
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit      
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit      
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********/16"
  ip *********/16
exit      
address "***********/24"
  ip ***********/24
exit
address "********/32"
  ip ********/32
exit
address "********/32"
  ip ********/32
exit
address "*********/24"
  ip *********/24
exit
address "*************/32"
  ip *************/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "***********/28"
  ip ***********/28
exit
address "***********/32"
  ip ***********/32
exit      
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "************/32"
  ip ************/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********/32"
  ip *********/32
exit      
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit      
address "*********0/32"
  ip *********0/32
exit
address "*********1/32"
  ip *********1/32
exit
address "*********2/32"
  ip *********2/32
exit
address "**********/27"
  ip **********/27
exit
address "*******/32"
  ip *******/32
exit
address "**********/32"
  ip **********/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit      
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "**********/28"
  ip **********/28
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "********/32"
  ip ********/32
exit
address "********/32"
  ip ********/32
exit
address "********/32"
  ip ********/32
exit      
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "3.20.1.101/32"
  ip 3.20.1.101/32
exit
address "3.21.15.1/32"
  ip 3.21.15.1/32
exit
address "3.21.15.2/32"
  ip 3.21.15.2/32
exit      
address "3.21.15.21/32"
  ip 3.21.15.21/32
exit
address "3.21.15.22/32"
  ip 3.21.15.22/32
exit
address "3.21.15.28/32"
  ip 3.21.15.28/32
exit
address "3.21.15.0/24"
  ip 3.21.15.0/24
exit
address "3.50.10.61/32"
  ip 3.50.10.61/32
exit
address "3.50.10.62/32"
  ip 3.50.10.62/32
exit
address "3.50.10.63/32"
  ip 3.50.10.63/32
exit
address "3.50.10.64/32"
  ip 3.50.10.64/32
exit      
address "3.20.11.11/32"
  ip 3.20.11.11/32
exit
address "3.20.11.12/32"
  ip 3.20.11.12/32
exit
address "198.3.100.94/32"
  ip 198.3.100.94/32
exit
address "3.9.1.23/32"
  ip 3.9.1.23/32
exit
address "*********/32"
  ip *********/32
exit
address "4.13.11.21/32"
  ip 4.13.11.21/32
exit
address "**********/32"
  ip **********/32
exit
address "4.13.11.30/32"
  ip 4.13.11.30/32
exit      
address "4.13.11.31/32"
  ip 4.13.11.31/32
exit
address "4.13.11.32/32"
  ip 4.13.11.32/32
exit
address "4.13.11.40/32"
  ip 4.13.11.40/32
exit
address "*********/22"
  ip *********/22
exit
address "198.3.10.0/24"
  ip 198.3.10.0/24
exit
address "3.20.1.3/32"
  ip 3.20.1.3/32
exit
address "4.50.10.11/32"
  ip 4.50.10.11/32
exit
address "4.50.10.12/32"
  ip 4.50.10.12/32
exit      
address "4.50.10.13/32"
  ip 4.50.10.13/32
exit
address "4.50.10.14/32"
  ip 4.50.10.14/32
exit
address "*******/32"
  ip *******/32
exit
address "4.50.10.21/32"
  ip 4.50.10.21/32
exit
address "4.50.10.22/32"
  ip 4.50.10.22/32
exit
address "***********/32"
  ip ***********/32
exit
address "4.14.100.22/32"
  ip 4.14.100.22/32
exit
address "3.22.16.7/32"
  ip 3.22.16.7/32
exit      
address "3.22.16.1/32"
  ip 3.22.16.1/32
exit
address "4.14.100.23/32"
  ip 4.14.100.23/32
exit
address "4.14.100.24/32"
  ip 4.14.100.24/32
exit
address "4.14.100.25/32"
  ip 4.14.100.25/32
exit
address "4.14.100.26/32"
  ip 4.14.100.26/32
exit
address "4.14.100.27/32"
  ip 4.14.100.27/32
exit
address "4.14.100.28/32"
  ip 4.14.100.28/32
exit
address "4.14.100.29/32"
  ip 4.14.100.29/32
exit      
address "4.14.100.30/32"
  ip 4.14.100.30/32
exit
address "4.14.100.31/32"
  ip 4.14.100.31/32
exit
address "4.14.100.32/32"
  ip 4.14.100.32/32
exit
address "4.14.100.33/32"
  ip 4.14.100.33/32
exit
address "4.14.100.34/32"
  ip 4.14.100.34/32
exit
address "4.14.100.35/32"
  ip 4.14.100.35/32
exit
address "4.14.100.36/32"
  ip 4.14.100.36/32
exit
address "4.14.100.37/32"
  ip 4.14.100.37/32
exit      
address "4.14.100.38/32"
  ip 4.14.100.38/32
exit
address "4.14.100.39/32"
  ip 4.14.100.39/32
exit
address "***********/32"
  ip ***********/32
exit
address "4.14.100.0/26"
  ip 4.14.100.0/26
exit
address "4.12.1.0/29"
  ip 4.12.1.0/29
exit
address "4.12.1.8/32"
  ip 4.12.1.8/32
exit
address "************/32"
  ip ************/32
exit
address "4.102.12.11/32"
  ip 4.102.12.11/32
exit      
address "4.102.12.12/32"
  ip 4.102.12.12/32
exit
address "***********/32"
  ip ***********/32
exit
address "4.102.12.31/32"
  ip 4.102.12.31/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "4.103.20.11/32"
  ip 4.103.20.11/32
exit
address "4.103.20.12/32"
  ip 4.103.20.12/32
exit      
address "3.29.12.11/32"
  ip 3.29.12.11/32
exit
address "3.29.12.12/32"
  ip 3.29.12.12/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "********/32"
  ip ********/32
exit
address "************/32"
  ip ************/32
exit      
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit      
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit      
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "************/32"
  ip ************/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit      
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********4/32"
  ip *********4/32
exit
address "*********5/32"
  ip *********5/32
exit
address "*********8/32"
  ip *********8/32
exit
address "*********9/32"
  ip *********9/32
exit
address "***********/32"
  ip ***********/32
exit      
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********0/32"
  ip *********0/32
exit
address "**********/24"
  ip **********/24
exit
address "198.3.100.0/25"
  ip 198.3.100.0/25
exit
address "***********/24"
  ip ***********/24
exit
address "************/32"
  ip ************/32
exit
address "3.28.20.0/24"
  ip 3.28.20.0/24
exit      
address "3.100.220.200/32"
  ip 3.100.220.200/32
exit
address "3.9.1.201/32"
  ip 3.9.1.201/32
exit
address "************/24"
  ip ************/24
exit
address "10.194.119.8/32"
  ip 10.194.119.8/32
exit
address "*********/24"
  ip *********/24
exit
address "4.27.10.101/32"
  ip 4.27.10.101/32
exit
address "4.27.10.102/32"
  ip 4.27.10.102/32
exit
address "4.27.10.121/32"
  ip 4.27.10.121/32
exit      
address "4.27.10.122/32"
  ip 4.27.10.122/32
exit
address "4.35.10.10/32"
  ip 4.35.10.10/32
exit
address "3.252.18.251/32"
  ip 3.252.18.251/32
exit
address "4.27.10.110/32"
  ip 4.27.10.110/32
exit
address "4.27.10.130/32"
  ip 4.27.10.130/32
exit
address "4.27.10.111/32"
  ip 4.27.10.111/32
exit
address "4.27.10.112/32"
  ip 4.27.10.112/32
exit
address "4.27.10.131/32"
  ip 4.27.10.131/32
exit      
address "4.27.10.132/32"
  ip 4.27.10.132/32
exit
address "4.27.10.11/32"
  ip 4.27.10.11/32
exit
address "4.27.10.12/32"
  ip 4.27.10.12/32
exit
address "4.28.10.0/24"
  ip 4.28.10.0/24
exit
address "4.103.15.20/32"
  ip 4.103.15.20/32
exit
address "4.103.15.21/32"
  ip 4.103.15.21/32
exit
address "4.103.15.22/32"
  ip 4.103.15.22/32
exit
address "4.103.15.10/32"
  ip 4.103.15.10/32
exit      
address "4.103.15.11/32"
  ip 4.103.15.11/32
exit
address "4.103.15.12/32"
  ip 4.103.15.12/32
exit
address "4.103.15.13/32"
  ip 4.103.15.13/32
exit
address "***********/32"
  ip ***********/32
exit
address "4.18.10.10/32"
  ip 4.18.10.10/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit      
address "***********/32"
  ip ***********/32
exit
address "4.103.19.10/32"
  ip 4.103.19.10/32
exit
address "*********/32"
  ip *********/32
exit
address "3.22.0.0/16"
  ip 3.22.0.0/16
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "3.22.10.1/32"
  ip 3.22.10.1/32
exit
address "3.22.10.2/32"
  ip 3.22.10.2/32
exit
address "3.22.10.66/32"
  ip 3.22.10.66/32
exit
address "3.22.10.67/32"
  ip 3.22.10.67/32
exit
address "*********1/32"
  ip *********1/32
exit
address "*********2/32"
  ip *********2/32
exit      
address "*********3/32"
  ip *********3/32
exit
address "*********2/32"
  ip *********2/32
exit
address "***********/32"
  ip ***********/32
exit
address "**********1/32"
  ip **********1/32
exit
address "**********2/32"
  ip **********2/32
exit
address "**********/32"
  ip **********/32
exit
address "*********7/32"
  ip *********7/32
exit
address "*********8/32"
  ip *********8/32
exit      
address "*********/32"
  ip *********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********0/32"
  ip *********0/32
exit
address "**********0/32"
  ip **********0/32
exit      
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "*********/32"
  ip *********/32
exit
address "**********/32"
  ip **********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "**********/32"
  ip **********/32
exit
address "*********5/32"
  ip *********5/32
exit
address "*********6/32"
  ip *********6/32
exit
address "*********9/32"
  ip *********9/32
exit      
address "*********1/32"
  ip *********1/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********/32"
  ip *********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit      
address "***********/32"
  ip ***********/32
exit
address "**********/32"
  ip **********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit      
address "*********/32"
  ip *********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit      
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "*********/28"
  ip *********/28
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit      
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit      
address "*********/24"
  ip *********/24
exit
address "***********/32"
  ip ***********/32
exit
address "*********"
  ip *********/32
exit
address "*************"
  ip *************/32
exit
address "************"
  ip ************/24
exit
address "*************"
  ip *************/32
exit
address "*************"
  ip *************/32
exit
address "***********"
  ip ***********/32
exit      
address "4.9.5.1"
  ip 4.9.5.1/32
exit
address "***********-134"
  range *********** 4.27.13.134
exit
address "ip_public"
  ip *******/8
  ip *******/8
  ip 0.0.0.0/0
exit
address "***********/32"
  ip ***********/32
exit
address "************"
  ip ************/32
exit
address "********-2"
  ip ********/32
  ip ********/32
exit
address "*********-42"
  ip *********/32
  ip *********/32
  ip *********/32
exit
address "**********-22"
  ip **********/32
  ip **********/32
exit
address "**********-52"
  ip **********/32
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "*********-12"
  ip *********/32
  ip *********/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "*********-23"
  ip *********/32
  ip *********/32
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "********1"
  ip ********1/32
exit
address "************"
  ip ************/32
exit
address "************/24"
  ip ************/24
exit
address "************/24"
  ip ************/24
exit
address "********/24"
  ip ********/24
exit
address "*******/32"
  ip *******/32
exit
address "************-35"
  ip ************/32
  ip ************/32
  ip ************/32
exit
address "NAS_*********"
  ip *********/32
exit
address "*************"
  ip *************/32
exit
address "DNS_**********"
  ip **********/32
exit
address "**********-42"
  ip **********/32
  ip **********/32
exit
address "***********/24"
  ip ***********/24
exit
address "************/24"
  ip ************/24
exit
zone "mgt"
  vrouter "mgt-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
zone "UAT-untrust"
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "UAT-trust"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "WSJC-untrust"
  vrouter "WSJC_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "WSJC-trust"
  vrouter "WSJC_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "PWJC-untrust"
  vrouter "PWJC_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "PWJC-trust"
  vrouter "PWJC_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "TCloud-untrust"
  vrouter "TCloud_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "TCloud-trust"
  vrouter "TCloud_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "BlackBox-untrust"
  vrouter "BlackBox_vrf"
exit
zone "BlackBox-trust"
  vrouter "BlackBox_vrf"
exit
hostname "YZB5CFW01"
admin auth-server "test"
admin host any any
web same-account-login enable
no https client-auth match
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit      

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit

isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit      

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit      

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface MGT0 local
  zone  "mgt"
  ip address ********** *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet0/8
  aggregate aggregate1
exit
interface xethernet0/9
  aggregate aggregate1
exit
interface aggregate1
  zone  "l2-trust"
  bandwidth downstream 20000000000
  bandwidth upstream 20000000000
  lacp enable
exit
interface aggregate1.102
  zone  "UAT-untrust"
  ip address ************ ***************
  bandwidth downstream 20000000000
  bandwidth upstream 20000000000
  manage ping
  no reverse-route
exit
interface aggregate1.103
  zone  "UAT-trust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.104
  zone  "WSJC-untrust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.105
  zone  "WSJC-trust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.106
  zone  "PWJC-untrust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.107
  zone  "PWJC-trust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.108
  zone  "TCloud-untrust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.109
  zone  "TCloud-trust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.110
  zone  "BlackBox-untrust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.111
  zone  "BlackBox-trust"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 ***********
exit
ip vrouter "trust-vr"
  ip route 0.0.0.0/0 ************
  ip route ********/24 ***********
  ip route ********/24 ***********
  ip route ********/24 ***********
  ip route ********/24 ***********
  ip route *********/24 ***********
  ip route *********/24 ***********
exit
ip vrouter "WSJC_vrf"
  ip route 0.0.0.0/0 3.255.250.17
  ip route *********/24 3.255.250.21
  ip route 3.22.0.0/16 3.255.250.21
exit
ip vrouter "PWJC_vrf"
  ip route 0.0.0.0/0 3.255.250.25
  ip route ********/24 3.255.250.29
exit
ip vrouter "TCloud_vrf"
  ip route 0.0.0.0/0 3.255.250.33
  ip route **********/16 3.255.250.37
exit
ip vrouter "BlackBox_vrf"
  ip route 0.0.0.0/0 3.255.250.41
  ip route ********/24 3.255.250.45
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp server ******* vrouter mgt-vr
rule id 94
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "ICMP"
  name "icmp"
exit
rule id 1
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "CIMS_Servers"
  dst-addr "ip_public"
  service "Any"
  name "CIMS-Management"
exit
rule id 2
  action permit
  src-zone "UAT-untrust"
  dst-zone "UAT-trust"
  src-addr "IP_********"
  dst-addr "IP_*******"
  service "NTP"
  name "UAT访问NTP"
exit
rule id 3
  action permit
  src-zone "UAT-trust"
  dst-zone "UAT-untrust"
  src-addr "net_**********"
  src-addr "net_**********"
  dst-addr "net_********"
  service "Any"
  name "云平台访问UAT"
exit
rule id 4
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "UAT-trust"
  dst-zone "UAT-untrust"
  src-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  service "SSH"
  name "归集库访问准生产ttdb及ocdb"
exit
rule id 5
  action permit
  src-zone "UAT-untrust"
  dst-zone "UAT-trust"
  src-addr "IP_**********"
  src-addr "**********-128"
  dst-addr "IP_***********"
  service "TCP_8080"
  name "NG_TO_TcpCopy"
exit
rule id 6
  action permit
  src-zone "UAT-untrust"
  dst-zone "UAT-trust"
  src-addr "**********"
  src-addr "**********-128"
  dst-addr "*********"
  dst-addr "*********"
  service "Any"
  name "UAT_TO_NAS"
exit
rule id 7
  action permit
  src-zone "UAT-trust"
  dst-zone "UAT-untrust"
  src-addr "*********"
  src-addr "*********"
  dst-addr "**********"
  dst-addr "**********-128"
  service "Any"
  name "NAS_TO_UAT"
exit
rule id 8
  action permit
  src-zone "UAT-trust"
  dst-zone "UAT-untrust"
  src-addr "IP_***********"
  dst-addr "IP_**********-24"
  service "TCP_4100"
  service "TCP_4200"
  service "TCP_4300"
  service "TCP_4400"
  name "Monitor-Console_TO_Event-platform"
exit
rule id 9
  action permit
  src-zone "UAT-untrust"
  dst-zone "UAT-trust"
  src-addr "Monitor_IP"
  dst-addr "net_***********"
  service "TCP_10050"
  service "TCP_10051"
  name "UAT_TO_Zabbix"
exit
rule id 10
  action permit
  src-zone "UAT-trust"
  dst-zone "UAT-untrust"
  src-addr "net_***********"
  dst-addr "Monitor_IP"
  service "TCP_10051"
  service "TCP_10050"
  name "Zabbix_TO_UAT"
exit
rule id 11
  action permit
  src-zone "UAT-untrust"
  dst-zone "UAT-trust"
  src-addr "**********"
  src-addr "**********"
  dst-addr "***********"
  service "Any"
  name "UAT_TO_WIRESHARK"
exit
rule id 12
  action permit
  src-zone "UAT-trust"
  dst-zone "UAT-untrust"
  src-addr "***********"
  dst-addr "**********"
  dst-addr "**********"
  service "Any"
  name "WIRESHARK_TO_UAT"
exit
rule id 13
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-ip *********/24
  dst-ip *********/24
  service "Any"
  name "juecexitong-trust-untrust-01"
exit
rule id 14
  action permit
  src-zone "PWJC-trust"
  dst-zone "PWJC-untrust"
  src-addr "********/24"
  dst-addr "ip_public"
  service "Any"
  name "mifu-external"
exit
rule id 15
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "OCS"
  dst-addr "ip_public"
  service "Any"
  name "OCS-mifu"
exit
rule id 16
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "*********-4"
  dst-addr "*********-62"
  service "tcp-10014"
  name "QianMing-mifu"
exit
rule id 17
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "*************"
  src-addr "************/24"
  src-ip *************/32
  dst-addr "**********"
  dst-addr "*********"
  service "tcp-10006"
  service "tcp-10007"
  name "tkec-yz-ba-mifuAPI"
exit
rule id 18
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "**********-55"
  src-addr "**********-74"
  src-addr "************-66"
  src-addr "************-98"
  src-addr "***********-40"
  dst-addr "**********"
  dst-addr "*********"
  service "tcp-10006"
  service "tcp-10007"
  name "shujuzhongtai-mifuAPI"
exit
rule id 19
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  dst-addr "***********/32"
  service "tcp-443"
  service "tcp-10001-10009"
  service "tcp-10012"
  service "tcp-10013"
  service "HTTP"
  name "WSJC-trust-untrust-9"
exit
rule id 20
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********1/32"
  src-addr "**********2/32"
  src-addr "*********7/32"
  src-addr "*********8/32"
  src-addr "*********0/32"
  src-addr "**********0/32"
  dst-addr "4.102.12.31/32"
  service "tcp-25"
  service "tcp-110"
  service "HTTP"
  service "TCP_8080"
  name "WSJC-trust-untrust-10"
exit
rule id 21
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "**********2/32"
  dst-addr "4.102.12.31/32"
  service "tcp-25"
  name "WSJC-trust-untrust-11"
exit
rule id 22
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/22"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "tcp-3306"
  name "WSJC-trust-untrust-12"
exit
rule id 23
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/32"
  src-addr "3.22.0.0/16"
  dst-addr "3.21.15.28/32"
  dst-addr "3.21.15.0/24"
  service "Any"
  name "WSJC-trust-untrust"
exit
rule id 24
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********1/32"
  src-addr "**********2/32"
  src-addr "*********7/32"
  src-addr "*********8/32"
  dst-addr "3.20.11.11/32"
  dst-addr "3.20.11.12/32"
  service "tcp-21"
  service "tcp-20"
  name "WSJC-trust-untrust-1"
exit
rule id 25
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  src-addr "***********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "*********/22"
  dst-addr "198.3.100.94/32"
  dst-addr "************/32"
  dst-addr "************/32"
  dst-addr "************/32"
  dst-addr "************/32"
  dst-addr "************/32"
  dst-addr "************/32"
  dst-addr "************/32"
  dst-addr "************/32"
  service "tcp-9092"
  service "TCP_8080"
  name "WSJC-trust-untrust-2"
exit
rule id 26
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.10.66/32"
  src-addr "3.22.10.67/32"
  dst-addr "3.9.1.23/32"
  dst-addr "********/32"
  dst-addr "*********/32"
  service "Any"
  name "WSJC-trust-untrust-3"
exit
rule id 27
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.0.0/16"
  dst-addr "198.3.10.0/24"
  service "tcp-1918"
  service "tcp-63358"
  service "tcp-80"
  name "WSJC-trust-untrust-4"
exit
rule id 28
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.0.0/16"
  dst-addr "3.20.1.3/32"
  service "tcp-135"
  service "udp-135"
  service "tcp-136"
  service "udp-136"
  service "tcp-137"
  service "udp-137"
  service "tcp-138"
  service "udp-138"
  service "tcp-139"
  service "udp-139"
  service "tcp-445"
  service "udp-445"
  name "WSJC-trust-untrust-5"
exit
rule id 29
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.10.66/32"
  src-addr "3.22.10.67/32"
  src-addr "*********1/32"
  src-addr "*********2/32"
  src-addr "*********3/32"
  dst-addr "4.13.11.21/32"
  dst-addr "**********/32"
  dst-addr "4.13.11.30/32"
  dst-addr "4.13.11.31/32"
  dst-addr "4.13.11.32/32"
  dst-addr "4.13.11.40/32"
  service "tcp-1521"
  service "tcp-22"
  service "tcp-7800-8849"
  name "WSJC-trust-untrust-6"
exit
rule id 30
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.10.1/32"
  src-addr "3.22.10.2/32"
  src-addr "*********/22"
  dst-addr "*******/32"
  service "NTP"
  name "WSJC-trust-untrust-7"
exit
rule id 31
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********1/32"
  src-addr "**********2/32"
  dst-addr "***********/32"
  dst-addr "4.14.100.22/32"
  dst-addr "4.14.100.23/32"
  dst-addr "4.14.100.24/32"
  dst-addr "4.14.100.25/32"
  dst-addr "4.14.100.26/32"
  dst-addr "4.14.100.27/32"
  dst-addr "4.14.100.28/32"
  dst-addr "4.14.100.29/32"
  dst-addr "4.14.100.30/32"
  dst-addr "4.14.100.31/32"
  dst-addr "4.14.100.32/32"
  dst-addr "4.14.100.33/32"
  dst-addr "4.14.100.34/32"
  dst-addr "4.14.100.35/32"
  dst-addr "4.14.100.36/32"
  dst-addr "4.14.100.37/32"
  dst-addr "4.14.100.38/32"
  dst-addr "4.14.100.39/32"
  dst-addr "***********/32"
  service "tcp-8020"
  service "tcp-10000"
  service "tcp-10001"
  name "WSJC-trust-untrust-8"
exit
rule id 32
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "*********/22"
  src-addr "*********/24"
  dst-addr "**********/32"
  service "tcp-7001"
  name "WSJC-trust-untrust-22"
exit
rule id 33
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/32"
  src-addr "**********/32"
  dst-addr "********/32"
  dst-addr "********/32"
  dst-addr "3.20.1.3/32"
  dst-addr "*********/32"
  dst-addr "**********/32"
  service "Any"
  name "WSJC-trust-untrust-23"
exit
rule id 34
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/22"
  src-addr "*********/24"
  dst-addr "***********/32"
  service "udp-514"
  service "tcp-514"
  name "WSJC-trust-untrust-24"
exit
rule id 35
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  dst-addr "*********4/32"
  service "SSH"
  name "WSJC-trust-untrust-25"
exit      
rule id 36
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "***********/32"
  dst-addr "***********/32"
  service "tcp-10005"
  service "tcp-10009"
  service "tcp-10010"
  service "tcp-10011"
  name "WSJC-trust-untrust-26"
exit
rule id 37
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/22"
  dst-addr "*********0/32"
  service "tcp-31306"
  name "WSJC-trust-untrust-27"
exit
rule id 38
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.0.0/16"
  dst-addr "***********/24"
  name "WSJC-trust-untrust-28"
exit
rule id 39
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.0.0/16"
  dst-addr "************/32"
  service "udp-514"
  service "udp-162"
  name "WSJC-trust-untrust-29"
exit
rule id 40
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "3.22.0.0/16"
  dst-addr "*******/32"
  service "NTP"
  name "WSJC-trust-untrust-30"
exit
rule id 41
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "3.9.1.201/32"
  service "tcp-111"
  service "udp-111"
  service "tcp-2049"
  service "udp-2049"
  service "tcp-4046"
  service "udp-4046"
  service "tcp-635"
  service "udp-635"
  name "WSJC-trust-untrust-31"
exit
rule id 42
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  src-addr "**********/32"
  dst-addr "3.100.220.200/32"
  service "HTTP"
  name "WSJC-trust-untrust-32"
exit
rule id 43
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "3.28.20.0/24"
  service "HTTP"
  name "WSJC-trust-untrust-33"
exit
rule id 44
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "10.194.119.8/32"
  service "HTTP"
  name "WSJC-trust-untrust-34"
exit
rule id 45
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "***********/32"
  src-addr "*********/24"
  dst-addr "3.20.1.3/32"
  service "tcp-25"
  name "WSJC-trust-untrust-35"
exit
rule id 46
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  dst-addr "4.27.10.101/32"
  dst-addr "4.27.10.102/32"
  dst-addr "4.27.10.121/32"
  dst-addr "4.27.10.122/32"
  service "tcp-22"
  service "tcp-1521"
  name "WSJC-trust-untrust-36"
exit
rule id 47
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/32"
  src-addr "*********/32"
  dst-addr "3.252.18.251/32"
  service "tcp-3389"
  name "WSJC-trust-untrust-37"
exit
rule id 48
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "4.35.10.10/32"
  service "TCP_8080"
  name "WSJC-trust-untrust-38"
exit
rule id 49
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "4.27.10.101/32"
  dst-addr "4.27.10.102/32"
  dst-addr "4.27.10.121/32"
  dst-addr "4.27.10.122/32"
  dst-addr "4.27.10.110/32"
  dst-addr "4.27.10.111/32"
  dst-addr "4.27.10.112/32"
  dst-addr "4.27.10.130/32"
  dst-addr "4.27.10.131/32"
  dst-addr "4.27.10.132/32"
  service "tcp-1521"
  name "WSJC-trust-untrust-39"
exit
rule id 50
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "4.103.15.20/32"
  dst-addr "4.103.15.21/32"
  dst-addr "4.103.15.22/32"
  service "tcp-28081-28090"
  name "WSJC-trust-untrust-40"
exit
rule id 51
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "4.18.10.10/32"
  service "tcp-28080"
  name "WSJC-trust-untrust-41"
exit
rule id 52
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "**********/32"
  service "tcp-7001"
  name "WSJC-trust-untrust-42"
exit
rule id 53
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "***********/32"
  dst-addr "4.103.15.11/32"
  dst-addr "4.103.15.12/32"
  dst-addr "4.103.15.13/32"
  dst-addr "***********/32"
  service "tcp-22"
  name "WSJC-trust-untrust-43"
exit
rule id 54
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "4.35.10.10/32"
  dst-addr "198.3.100.94/32"
  service "tcp-8080-8089"
  name "WSJC-trust-untrust-44"
exit
rule id 55
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "***********/32"
  dst-addr "***********/32"
  dst-addr "***********/32"
  dst-addr "***********/32"
  dst-addr "***********/32"
  dst-addr "4.103.19.10/32"
  service "tcp-19080"
  name "WSJC-trust-untrust-45"
exit
rule id 56
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.102.12.11/32"
  src-addr "4.102.12.12/32"
  src-addr "***********/32"
  src-addr "4.102.12.31/32"
  src-addr "3.29.12.11/32"
  src-addr "3.29.12.12/32"
  src-addr "4.103.20.11/32"
  src-addr "4.103.20.12/32"
  dst-addr "3.22.0.0/16"
  dst-addr "*********/22"
  service "tcp-443"
  service "HTTP"
  service "udp-53"
  name "HW2020"
exit
rule id 57
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "*********/16"
  src-addr "***********/24"
  src-addr "*********/24"
  src-addr "*************/32"
  src-addr "***********/28"
  src-addr "***********/32"
  src-addr "3.20.1.101/32"
  src-addr "3.21.15.1/32"
  src-addr "3.21.15.2/32"
  src-addr "3.21.15.21/32"
  src-addr "3.21.15.22/32"
  src-addr "3.21.15.28/32"
  src-addr "**********/24"
  dst-addr "3.22.0.0/16"
  dst-addr "*********/22"
  service "Any"
  name "WSJC-untrust-tust-2"
exit
rule id 58
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "********/32"
  src-addr "********/32"
  src-addr "********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "************/24"
  dst-addr "***********/32"
  service "tcp-7004"
  name "WSJC-untrust-trust-12"
exit
rule id 59
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "**********/32"
  dst-addr "*********/32"
  service "Any"
  name "WSJC-untrust-trust-1"
exit
rule id 60
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "Any"
  dst-addr "ip_public"
  service "ICMP"
  name "Juecexitong-icmp"
exit
rule id 61
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "**********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "************/32"
  dst-addr "ip_public"
  service "Any"
  name "Juecexitong-ls"
exit
rule id 62
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  dst-addr "**********/32"
  service "Any"
  name "WSJC-untrust-trust-3"
exit
rule id 63
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "**********/27"
  dst-addr "ip_public"
  service "FTP"
  service "tcp-22"
  service "tcp-23"
  service "tcp-1521"
  service "tcp-3306"
  service "tcp-3389"
  service "tcp-8999"
  name "S0C-1"
exit
rule id 64
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "************/32"
  dst-addr "ip_public"
  name "S0C-2"
exit
rule id 65
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "**********/32"
  src-addr "**********/32"
  dst-addr "**********/32"
  dst-addr "**********/32"
  dst-addr "**********/32"
  dst-addr "**********/32"
  service "tcp-9080"
  service "TCP_8080"
  name "WSJC-untrust-tust-4"
exit
rule id 66
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  dst-addr "**********/32"
  service "tcp-7809-7829"
  name "WSJC-untrust-tust-5"
exit
rule id 67
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********/32"
  src-addr "*********0/32"
  src-addr "*********1/32"
  src-addr "*********2/32"
  dst-addr "**********/32"
  service "tcp-8443"
  name "WSJC-untrust-tust-6"
exit
rule id 68
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "*******/32"
  src-addr "**********/32"
  dst-addr "**********/32"
  dst-addr "**********/32"
  dst-addr "**********/32"
  dst-addr "**********/32"
  dst-addr "*********/32"
  service "SSH"
  name "WSJC-untrust-tust-7"
exit
rule id 69
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  src-addr "************/32"
  dst-addr "3.22.10.1/32"
  dst-addr "3.22.10.2/32"
  dst-addr "*********/32"
  service "Any"
  name "WSJC-untrust-tust-8"
exit
rule id 70
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "**********/28"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "**********/32"
  dst-addr "*********/32"
  service "tcp-25"
  name "WSJC-untrust-tust-9"
exit
rule id 71
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "**********/32"
  src-addr "**********/32"
  src-addr "3.50.10.61/32"
  src-addr "3.50.10.62/32"
  src-addr "3.50.10.63/32"
  src-addr "3.50.10.64/32"
  dst-addr "3.22.10.66/32"
  dst-addr "3.22.10.67/32"
  dst-addr "*********1/32"
  dst-addr "*********2/32"
  dst-addr "*********3/32"
  service "tcp-1521"
  name "WSJC-untrust-tust-10"
exit
rule id 72
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "*********/32"
  src-addr "*********/32"
  dst-addr "**********/32"
  dst-addr "*********2/32"
  name "WSJC-untrust-tust-11"
exit
rule id 73
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  dst-addr "*********/32"
  service "tcp-25"
  name "WSJC-untrust-tust-13"
exit
rule id 74
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.13.11.21/32"
  src-addr "**********/32"
  src-addr "4.13.11.30/32"
  src-addr "4.13.11.31/32"
  src-addr "4.13.11.32/32"
  src-addr "4.13.11.40/32"
  dst-addr "3.22.10.66/32"
  dst-addr "3.22.10.67/32"
  dst-addr "*********1/32"
  dst-addr "*********2/32"
  dst-addr "*********3/32"
  service "tcp-1521"
  service "tcp-22"
  name "WSJC-untrust-tust-14"
exit
rule id 75
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "*********/22"
  src-addr "3.28.20.0/24"
  dst-addr "*********/32"
  service "HTTP"
  service "tcp-443"
  name "WSJC-untrust-tust-15"
exit
rule id 76
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "3.21.15.28/32"
  src-addr "198.3.10.0/24"
  dst-addr "**********2/32"
  dst-addr "*********8/32"
  dst-addr "3.22.0.0/16"
  service "tcp-135"
  service "udp-135"
  service "tcp-136"
  service "udp-136"
  service "tcp-137"
  service "udp-137"
  service "tcp-138"
  service "udp-138"
  service "tcp-139"
  service "udp-139"
  service "tcp-445"
  service "udp-445"
  service "tcp-80"
  service "tcp-6001"
  name "WSJC-untrust-tust-16"
exit
rule id 77
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.50.10.11/32"
  src-addr "4.50.10.12/32"
  src-addr "4.50.10.13/32"
  src-addr "4.50.10.14/32"
  src-addr "4.50.10.21/32"
  src-addr "4.50.10.22/32"
  dst-addr "3.22.10.66/32"
  dst-addr "3.22.10.67/32"
  dst-addr "*********1/32"
  dst-addr "*********2/32"
  dst-addr "*********3/32"
  service "tcp-1521"
  name "WSJC-untrust-tust-17"
exit
rule id 78
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "***********/32"
  src-addr "4.14.100.22/32"
  src-addr "4.14.100.23/32"
  src-addr "4.14.100.24/32"
  src-addr "4.14.100.25/32"
  src-addr "4.14.100.26/32"
  src-addr "4.14.100.27/32"
  src-addr "4.14.100.28/32"
  src-addr "4.14.100.29/32"
  src-addr "4.14.100.30/32"
  src-addr "4.14.100.31/32"
  src-addr "4.14.100.32/32"
  src-addr "4.14.100.33/32"
  src-addr "4.14.100.34/32"
  src-addr "4.14.100.35/32"
  src-addr "4.14.100.36/32"
  src-addr "4.14.100.37/32"
  src-addr "4.14.100.38/32"
  src-addr "4.14.100.39/32"
  src-addr "***********/32"
  dst-addr "**********1/32"
  dst-addr "**********2/32"
  dst-addr "*********8/32"
  dst-addr "*********0/32"
  dst-addr "**********0/32"
  dst-addr "3.22.16.7/32"
  dst-addr "3.22.16.1/32"
  service "tcp-61894"
  service "tcp-8020"
  name "WSJC-untrust-tust-18"
exit
rule id 79
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "*********/22"
  src-addr "4.14.100.0/26"
  dst-addr "**********/32"
  dst-addr "**********/32"
  service "tcp-3306"
  name "WSJC-untrust-tust-19"
exit
rule id 80
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.102.12.11/32"
  src-addr "4.102.12.12/32"
  dst-addr "3.22.0.0/16"
  service "tcp-443"
  service "HTTP"
  name "WSJC-untrust-tust-20"
exit
rule id 81
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.102.12.11/32"
  src-addr "4.102.12.12/32"
  dst-addr "3.22.10.1/32"
  dst-addr "3.22.10.2/32"
  service "udp-53"
  name "WSJC-untrust-tust-21"
exit
rule id 82
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "**********/32"
  dst-addr "*********/32"
  service "tcp-35640"
  service "tcp-35641"
  name "WSJC-untrust-tust-22"
exit
rule id 83
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "*********4/32"
  dst-addr "**********/32"
  service "SSH"
  name "WSJC-untrust-tust-23"
exit
rule id 84
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "198.3.100.0/25"
  src-addr "4.14.100.0/26"
  dst-addr "*********/28"
  service "tcp-3306"
  name "WSJC-untrust-tust-26"
exit
rule id 85
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "***********/24"
  dst-addr "3.22.0.0/16"
  name "WSJC-untrust-tust-27"
exit
rule id 86
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "3.28.20.0/24"
  dst-addr "*********/28"
  service "tcp-3306"
  name "WSJC-untrust-tust-24"
exit
rule id 87
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.103.20.11/32"
  src-addr "4.103.20.12/32"
  dst-addr "*********/24"
  service "tcp-8999"
  service "tcp-7070"
  service "tcp-5222"
  name "WSJC-untrust-tust-28"
exit
rule id 88
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "3.28.20.0/24"
  dst-addr "*********/24"
  service "tcp-3306"
  service "HTTP"
  name "WSJC-untrust-tust-29"
exit
rule id 89
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "*********/24"
  dst-addr "*********/24"
  service "Any"
  name "WSJC-untrust-tust-30"
exit
rule id 90
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.28.10.0/24"
  src-addr "4.27.10.11/32"
  src-addr "4.27.10.12/32"
  dst-addr "*********/24"
  service "tcp-1521"
  name "WSJC-untrust-tust-31"
exit
rule id 91
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.103.20.11/32"
  src-addr "4.103.20.12/32"
  dst-addr "*********/24"
  service "tcp-80"
  name "WSJC-untrust-tust-33"
exit
rule id 92
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "4.103.15.10/32"
  src-addr "4.103.15.11/32"
  src-addr "4.103.15.12/32"
  src-addr "4.103.15.13/32"
  src-addr "***********/32"
  dst-addr "*********/24"
  service "tcp-80"
  name "WSJC-untrust-tust-32"
exit
rule id 93
  action permit
  src-zone "WSJC-untrust"
  dst-zone "WSJC-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  dst-addr "*********/32"
  dst-addr "*********/32"
  dst-addr "***********/32"
  service "tcp-6002"
  name "WSJC-untrust-tust-34"
exit
rule id 95
  action permit
  src-addr "***********/24"
  dst-addr "ip_public"
  service "TCP-10050"
  name "Zabbix_To_Any"
exit
rule id 96
  action permit
  src-zone "TCloud-untrust"
  dst-zone "TCloud-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  dst-addr "*************"
  service "tcp-30220"
  name "USAP-shujuzichanguanli"
exit
rule id 97
  action permit
  src-zone "TCloud-untrust"
  dst-zone "TCloud-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  dst-addr "*************"
  service "tcp-30223"
  name "USAP-yewuliuchengguanli"
exit
rule id 98
  action permit
  src-zone "TCloud-untrust"
  dst-zone "TCloud-trust"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********/32"
  src-addr "***********-134"
  dst-addr "*************"
  service "tcp-30221"
  name "USAP-yunshangbaobiaojishi"
exit
rule id 99
  action permit
  src-zone "TCloud-trust"
  dst-zone "TCloud-untrust"
  src-addr "************"
  dst-addr "4.103.19.10/32"
  service "tcp-19080"
  name "global-app-USAP"
exit
rule id 100
  action permit
  src-zone "TCloud-trust"
  dst-zone "TCloud-untrust"
  src-addr "************"
  dst-addr "***********"
  service "tcp-19080"
  name "yunshangbaobiaojishi-global-app-USAP"
exit
rule id 101
  action permit
  src-zone "TCloud-trust"
  dst-zone "TCloud-untrust"
  src-addr "************"
  dst-addr "4.35.10.10/32"
  service "TCP_8080"
  name "yunshangbaobiaojishi-global-app-SJZT"
exit
rule id 102
  action permit
  src-zone "TCloud-trust"
  dst-zone "TCloud-untrust"
  src-addr "************"
  dst-addr "4.9.5.1"
  service "tcp-34443"
  name "yunshangbaobiaojishi-global-app-p1-sftp"
exit
rule id 103
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "***********/32"
  dst-addr "********/24"
  service "SSH"
  name "ZDH_mifu"
exit
rule id 104
  action permit
  src-zone "WSJC-trust"
  dst-zone "WSJC-untrust"
  src-addr "*********/24"
  dst-addr "************"
  service "tcp-30000"
  name "WXGGH-TO-KFPT_SFTP"
exit
rule id 105
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "********-2"
  dst-addr "*********-42"
  service "tcp-11001"
  service "tcp-11002"
  service "HTTP"
  service "HTTPS"
  name "CA-TO-MIFU"
exit
rule id 106
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "**********-22"
  src-addr "**********-52"
  dst-addr "**********"
  service "tcp-8000"
  name "mimaji-to-mifu"
exit
rule id 107
  action permit
  src-zone "BlackBox-trust"
  dst-zone "BlackBox-untrust"
  src-addr "*********-12"
  dst-addr "********1"
  service "Any"
  name "BlackBox_TO_NAS"
exit
rule id 108
  action permit
  src-zone "BlackBox-trust"
  dst-zone "BlackBox-untrust"
  src-addr "*********-12"
  dst-addr "************"
  service "tcp-8083"
  name "BlackBox_TO_B_NG"
exit
rule id 109
  action permit
  src-zone "BlackBox-untrust"
  dst-zone "BlackBox-trust"
  src-addr "************/24"
  src-addr "************/24"
  dst-addr "*********"
  service "tcp-52200"
  name "TC_BlackBox"
exit
rule id 110
  action permit
  src-zone "BlackBox-untrust"
  dst-zone "BlackBox-trust"
  src-addr "**********"
  src-addr "***********"
  dst-addr "*********"
  service "tcp-52200"
  service "HTTP"
  service "tcp-11025"
  name "gongzhengPC_BlackBox"
exit
rule id 111
  action permit
  src-zone "BlackBox-untrust"
  dst-zone "BlackBox-trust"
  src-addr "net_***********"
  src-addr "***********/24"
  dst-addr "********/24"
  service "TCP_10051"
  service "TCP_10050"
  name "Zabbix_TO_Blockbox"
exit
rule id 112
  action permit
  src-zone "BlackBox-trust"
  dst-zone "BlackBox-untrust"
  src-addr "********/24"
  dst-addr "net_***********"
  dst-addr "***********/24"
  service "TCP_10051"
  service "TCP_10050"
  name "Blockbox _TO_ Zabbix"
exit
rule id 113
  action permit
  src-zone "BlackBox-untrust"
  dst-zone "BlackBox-trust"
  src-addr "************-35"
  dst-addr "********/24"
  service "SSH"
  name "Aops_TO_Blockbox"
exit
rule id 114
  action permit
  src-zone "BlackBox-trust"
  dst-zone "BlackBox-untrust"
  src-addr "********/24"
  dst-addr "3.100.220.200/32"
  service "HTTP"
  name "Blockbox _TO_ YUM"
exit
rule id 115
  action permit
  src-zone "BlackBox-trust"
  dst-zone "BlackBox-untrust"
  src-addr "********/24"
  dst-addr "*******/32"
  dst-addr "*******/32"
  service "NTP"
  name "Blockbox _TO_ NTP"
exit
rule id 116
  action permit
  src-zone "BlackBox-trust"
  dst-zone "BlackBox-untrust"
  src-addr "********/24"
  dst-addr "NAS_*********"
  service "TCP4045-4049"
  service "UDP4045-4049"
  service "tcp-111"
  service "udp-111"
  service "tcp-2049"
  service "udp-2049"
  service "tcp-635"
  service "udp-635"
  service "ICMP"
  name "Blockbox_TO_NAS205"
exit
rule id 117
  action permit
  src-zone "BlackBox-trust"
  dst-zone "BlackBox-untrust"
  src-addr "********/24"
  dst-addr "DNS_**********"
  service "DNS"
  service "ICMP"
  name "Blockbox_TO_DNS"
exit
rule id 118
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "**********-22"
  src-addr "**********-42"
  dst-addr "********/24"
  service "tcp-8000"
  service "tcp-8088"
  name "tance-to-mifu"
exit
rule id 119
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "***********/24"
  dst-addr "********/24"
  service "udp-161"
  name "mifujiankong"
exit
l2-nonip-action drop
no tcp-mss all
tcp-mss tunnel 1380
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
  icmp-unreachable-session-keep
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional vsys
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional vsys
exit      
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application vsys
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application vsys
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application vsys
exit
query-groups
exit
no sms disable
ha link interface HA0
ha link ip ******* *************
ha group 0
  monitor track "trackobj1"
exit
ha cluster 1 node 0

End

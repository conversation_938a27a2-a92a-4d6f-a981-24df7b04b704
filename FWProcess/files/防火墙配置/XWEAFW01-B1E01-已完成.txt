XWEAFW01-B<PERSON><PERSON><PERSON>(M)# show config
XWEAFW01-B1E01(M)# show configuration 

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "E-FW"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit      
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
interface vswitchif1
exit
interface MGT0 local
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit      
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit
interface aggregate1
exit
interface aggregate2
exit      
interface loopback1
exit
address "private_network"
exit
address "JG_HI_***********"
exit
address "JG_HI_***********"
exit
address "JG_HI_***********_NAT"
exit
address "JG_HI_***********_NAT"
exit
address "CIMS_*********/24"
exit
address "host_***********/32"
exit
address "range_*********-12"
exit
address "BJDC_**********"
exit
address "BJDC_**********"
exit
address "BJDC_**********"
exit      
address "BJDC_**********"
exit
address "BJDC_**********"
exit
address "***********"
exit
address "*************"
exit
address "*********"
exit
address "*********"
exit
address "**************"
exit
address "Range_SJJM-SSL"
exit
address "YYGS_********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit      
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "*********"
exit
address "*********"
exit
address "**********"
exit
address "************"
exit
address "***********"
exit
address "********-2"
exit
address "*********"
exit
address "range_**********-15"
exit
address "Range_Coding-NG"
exit      
address "*********"
exit
address "*********"
exit
address "************"
exit
address "**********"
exit
address "***********"
exit
address "**********"
exit
address "**********"
exit
address "**********-65"
exit
address "*********"
exit
address "IP_*************"
exit
address "IP_*********"
exit
address "************/24"
exit      
address "************/24"
exit
address "********"
exit
address "CZB_************/32"
exit
address "CZB_NAT_**********/32"
exit
address "CZB_***********/32"
exit
address "CZB_***********/32"
exit
address "CZB_***********/32"
exit
address "CZB_4.14.100.26/32"
exit
address "CZB_4.14.100.27/32"
exit
address "CZB_NAT_172.31.18.81/32"
exit
address "CZB_NAT_172.31.18.82/32"
exit
address "CZB_NAT_172.31.18.83/32"
exit      
address "CZB_NAT_172.31.18.84/32"
exit
address "CZB_NAT_172.31.18.85/32"
exit
address "**********"
exit
address "CZB_************/32"
exit
address "CZB_NAT_**********/32"
exit
address "book20211013"
exit
address "Range_Zabbix"
exit
address "VDI_**********"
exit
address "*********"
exit
address "Range_*********-34"
exit
address "**********"
exit
address "**********"
exit      
address "*************"
exit
address "*************"
exit
address "HaiNan_***********"
exit
address "HaiNan_**********"
exit
address "HaiNan_**********"
exit
address "HaiNan_***********"
exit
address "************"
exit
address "************"
exit
address "************"
exit
address "**********"
exit
address "**********"
exit
address "jiankong_***********/24"
exit      
address "GD_***********"
exit
address "************/24"
exit
address "**************"
exit
address "**************"
exit
address "*************"
exit
address "BJDC_***********"
exit
address "*********"
exit
address "**************"
exit
address "Range_***********-32"
exit
address "**********/24"
exit
address "*********"
exit
address "**********"
exit      
address "**********"
exit
address "DEVTEST-YYOS-VDI"
exit
address "************"
exit
address "************"
exit
address "************-36"
exit
address "nat_*********-29"
exit
address "***********-19"
exit
address "**********"
exit
address "**********"
exit
address "nat*********"
exit
address "nat*********"
exit
address "nat*********"
exit      
address "***********-14"
exit
address "**********/24"
exit
address "************"
exit
address "************"
exit
address "************"
exit
address "***********-22"
exit
aaa-server "local" type local
exit
track "JianCe"
exit
service "TCP-1521"
exit
service "udp-162"
  udp dst-port 162 
exit
service "udp-514"
  udp dst-port 514 
exit      
service "tcp-8890"
  tcp dst-port 8890 
exit
service "tcp-8891"
  tcp dst-port 8891 
exit
service "udp-161"
  udp dst-port 161 
exit
service "rdp-3389"
  tcp dst-port 3389 
exit
service "tcp-8999"
  tcp dst-port 8999 
exit
service "tcp-443"
  tcp dst-port 443 
exit
service "1"
  tcp dst-port 80 
exit
service "TCP_6666"
  tcp dst-port 6666 
exit      
service "AD-PORT"
  tcp dst-port 389 
  udp dst-port 389 
  tcp dst-port 636 
  tcp dst-port 3268 
  tcp dst-port 3269 
  tcp dst-port 88 
  udp dst-port 88 
  tcp dst-port 445 
  udp dst-port 445 
  tcp dst-port 25 
  tcp dst-port 135 
  tcp dst-port 5722 
  tcp dst-port 464 
  udp dst-port 464 
  udp dst-port 138 
  tcp dst-port 9389 
  udp dst-port 67 
  udp dst-port 2535 
  udp dst-port 137 
  tcp dst-port 139 
  tcp dst-port 49152 65535 
  udp dst-port 49152 65535 
  tcp dst-port 53 
  udp dst-port 53 
  udp dst-port 123 
exit
service "TCP-23000"
  tcp dst-port 23000 
exit
service "TCP-8022"
  tcp dst-port 8022 
exit
service "TCP-54102"
  tcp dst-port 54102 
exit
service "TCP-54103"
  tcp dst-port 54103 
exit
service "TCP-54104"
  tcp dst-port 54104 
exit
service "TCP-28080"
  tcp dst-port 28080 
exit
service "TCP-80"
  tcp dst-port 80 
exit      
service "TCP-8080"
  tcp dst-port 8080 
exit
service "TCP-8081"
  tcp dst-port 8081 
exit
service "tcp-4422"
  tcp dst-port 4422 
exit
service "TCP-30000"
  tcp dst-port 30000 
exit
service "tcp_9001"
  tcp dst-port 9001 
exit
service "TCP-3000"
  tcp dst-port 3000 
exit
service "TCP-10102"
  tcp dst-port 10102 
exit
service "tcp-389"
  tcp dst-port 389 
exit      
service "UDP-1234"
  udp dst-port 1234 
exit
service "TCP-6006"
  tcp dst-port 6006 
exit
service "TCP-8931"
  tcp dst-port 8931 
exit
service "TCP_34443"
  tcp dst-port 34443 
exit
service "TCP_18081"
  tcp dst-port 18081 
exit
service "tcp-29093"
  tcp dst-port 29093 
exit
service "SFTP22"
  tcp dst-port 22 
exit
service "tcp-8443"
  tcp dst-port 8443 
exit      
service "tcp-32600"
  tcp dst-port 32600 
exit
service "tcp-7443"
  tcp dst-port 7443 
exit
ips sigset "dns" template dns
  max-scan-bytes 30720
exit
ips sigset "ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "imap" template imap
  max-scan-bytes 30720
exit
ips sigset "finger" template finger
  max-scan-bytes 30720
exit
ips sigset "sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "voip" template voip
  max-scan-bytes 30720
exit
ips sigset "default_dns" template dns
  max-scan-bytes 30720
exit
ips sigset "default_ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "default_http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "default_pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "default_smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "default_telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "default_other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "default_other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "default_imap" template imap
  max-scan-bytes 30720
exit
ips sigset "default_finger" template finger
  max-scan-bytes 30720
exit
ips sigset "default_sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "default_nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "default_tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "default_snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "default_mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "default_mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "default_oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "default_msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "default_netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "default_dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "default_ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "default_voip" template voip
  max-scan-bytes 30720
exit
ips profile "no-ips"
exit      
ips profile "predef_default"
  sigset "default_dns"
  sigset "default_ftp"
  sigset "default_http"
  sigset "default_pop3"
  sigset "default_smtp"
  sigset "default_telnet"
  sigset "default_other-tcp"
  sigset "default_other-udp"
  sigset "default_imap"
  sigset "default_finger"
  sigset "default_sunrpc"
  sigset "default_nntp"
  sigset "default_tftp"
  sigset "default_snmp"
  sigset "default_mysql"
  sigset "default_mssql"
  sigset "default_oracle"
  sigset "default_msrpc"
  sigset "default_netbios"
  sigset "default_dhcp"
  sigset "default_ldap"
  sigset "default_voip"
  filter-class 1 
    severity "Low" 
    severity "Medium" 
    severity "High" 
    action reset
  exit
exit
url-category "custom1"
exit
url-category "custom2"
exit
url-category "custom3"
exit
contentfilter
exit
sandbox-profile "predef_low"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit      
sandbox-profile "predef_middle"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_high"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  file-type swf
  file-type rar
  file-type zip
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
sandbox-profile "predef_pe"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
url-profile "no-url"
exit
track "JianCe"
  interface aggregate1 
  interface aggregate2 
exit
admin user "hillstone"
  password TgSot4wzZEKoxgun5cxG/ubws+
        password-expiration 1577937177
  role "admin"
  access console
exit
admin user "netadmin"
  password HNaG0O/0RmQ9tDsNxAVf6LsgG/
        password-expiration 1577937189
  role "admin"
  access console
  access ssh
  access https
exit
logging event to syslog 
logging threat to buffer severity informational
logging threat to syslog custom-format  severity informational
logging network to syslog 
logging traffic session on
logging traffic nat on
logging traffic web-surfing on
logging traffic pbr on
logging configuration to syslog 
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog *********** vrouter "mgt-vr" udp 514 type event
logging syslog *********** vrouter "mgt-vr" udp 514 type config
logging syslog *********** vrouter "mgt-vr" udp 514 type network
logging syslog *********** vrouter "mgt-vr" udp 514 type nbc
logging syslog *********** vrouter "mgt-vr" udp 514 type threat
logging syslog *********** vrouter "mgt-vr" udp 514 type traffic session
logging syslog *********** vrouter "mgt-vr" udp 514 type traffic nat
logging syslog *********** vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog *********** vrouter "mgt-vr" udp 514 type debug
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog ************ vrouter "mgt-vr" udp 514 type config
logging syslog ************ vrouter "mgt-vr" udp 514 type network
logging syslog ************ vrouter "mgt-vr" udp 514 type nbc
logging syslog ************ vrouter "mgt-vr" udp 514 type threat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic session
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic nat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog ************ vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type nbc
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type config
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type network
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type nbc
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type threat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic session
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic nat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic pbr
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type sandbox
logging syslog 3.252.235.111 vrouter "mgt-vr" udp 514 type threat
logging syslog additional-information
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "JG_HI_***********"
  ip ***********/32
exit
address "JG_HI_***********"
  ip ***********/32
exit
address "JG_HI_***********_NAT"
  ip **********/32
exit
address "JG_HI_***********_NAT"
  ip **********/32
exit
address "CIMS_*********/24"
  ip *********/24
exit
address "host_***********/32"
  ip ***********/32
exit
address "range_*********-12"
  range ********* *********2
  range *********** *********34
  range *********61 *********72
exit
address "BJDC_**********"
  ip **********/32
exit
address "BJDC_**********"
  ip **********/32
exit
address "BJDC_**********"
  ip **********/32
exit
address "BJDC_**********"
  ip **********/32
exit
address "BJDC_**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "*************"
  ip *************/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "**************"
  ip **************/32
exit
address "Range_SJJM-SSL"
  range ********** **********
  range ********** **********
  range ********* *********
exit
address "YYGS_********"
  ip ********/24
exit      
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit      
address "**********"
  ip **********/32
exit
address "************"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "********-2"
  range ******** ********
exit
address "*********"
  ip *********/32
exit
address "range_**********-15"
  range ********** **********
exit
address "Range_Coding-NG"
  range *********** ***********
exit
address "*********"
  ip *********/32
exit      
address "*********"
  ip *********/32
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********-65"
  range ********** **********
exit
address "*********"
  ip *********/32
exit      
address "IP_*************"
  ip *************/32
exit
address "IP_*********"
  ip *********/32
exit
address "************/24"
  ip ************/24
exit
address "************/24"
  ip ************/24
exit
address "********"
  ip ********/24
exit
address "CZB_************/32"
  ip ************/32
exit
address "CZB_NAT_**********/32"
  ip **********/32
exit
address "CZB_***********/32"
  ip ***********/32
exit      
address "CZB_***********/32"
  ip ***********/32
exit
address "CZB_***********/32"
  ip ***********/32
exit
address "CZB_4.14.100.26/32"
  ip 4.14.100.26/32
exit
address "CZB_4.14.100.27/32"
  ip 4.14.100.27/32
exit
address "CZB_NAT_172.31.18.81/32"
  ip 172.31.18.81/32
exit
address "CZB_NAT_172.31.18.82/32"
  ip 172.31.18.82/32
exit
address "CZB_NAT_172.31.18.83/32"
  ip 172.31.18.83/32
exit
address "CZB_NAT_172.31.18.84/32"
  ip 172.31.18.84/32
exit      
address "CZB_NAT_172.31.18.85/32"
  ip 172.31.18.85/32
exit
address "**********"
  ip **********/32
exit
address "CZB_************/32"
  ip ************/32
exit
address "CZB_NAT_**********/32"
  ip **********/32
exit
address "Range_Zabbix"
  range ************ ************
exit
address "VDI_**********"
  ip **********/24
exit
address "*********"
  ip *********/32
exit
address "Range_*********-34"
  range ********* *********
exit      
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "*************"
  ip *************/32
exit
address "*************"
  ip *************/32
exit
address "HaiNan_***********"
  ip ***********/32
exit
address "HaiNan_**********"
  ip **********/32
exit
address "HaiNan_**********"
  ip **********/32
exit
address "HaiNan_***********"
  ip ***********/32
exit      
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "jiankong_***********/24"
  ip ***********/24
exit
address "GD_***********"
  ip ***********/32
exit
address "************/24"
  ip ************/24
exit      
address "**************"
  ip **************/32
exit
address "**************"
  ip **************/32
exit
address "*************"
  ip *************/32
exit
address "BJDC_***********"
  ip ***********/32
exit
address "*********"
  ip *********/32
exit
address "**************"
  ip **************/32
exit
address "Range_***********-32"
  range *********** ***********
exit
address "**********/24"
  ip **********/24
exit      
address "*********"
  ip *********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "DEVTEST-YYOS-VDI"
  ip ************/24
  ip ************/24
  ip ************/24
  ip ************/24
  ip ************/24
  ip ************/24
  ip ************/24
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit      
address "************-36"
  range ************ ************
exit
address "nat_*********-29"
  range ********* *********
exit
address "***********-19"
  range *********** ***********
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "nat*********"
  ip *********/32
exit
address "nat*********"
  ip *********/32
exit
address "nat*********"
  ip *********/32
exit      
address "***********-14"
  range *********** ***********
exit
address "**********/24"
  ip **********/24
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "***********-22"
  range *********** ***********
exit
zone "mgt"
  vrouter "mgt-vr"
exit
zone "trust"
  vrouter "E-FW"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "untrust"
  vrouter "E-FW"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
hostname "XWEAFW01-B1E01"
admin host any any
web same-account-login enable
no https client-auth match
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit

isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2 
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit
          
ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface MGT0 local
  zone  "mgt"
  ip address ************* *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit      
interface ethernet0/0
  zone  "mgt"
  manage ping
  manage ssh
  manage https
  manage telnet
  no reverse-route
exit
interface ethernet0/4
  aggregate aggregate2
exit
interface ethernet0/5
  aggregate aggregate2
exit
interface xethernet0/8
  aggregate aggregate1
exit
interface xethernet0/9
  aggregate aggregate1
exit
interface aggregate1
  zone  "trust"
  ip address *********** ***************
  manage ping
  lacp enable
  no reverse-route
exit
interface aggregate2
  zone  "untrust"
  ip address 4.255.191.5 ***************
  manage ping
  lacp enable
  no reverse-route
exit
interface loopback1
  zone  "trust"
  ip address 4.255.255.221 255.255.255.255
  manage ssh
  manage ping
  manage https
  no reverse-route
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 4.255.253.254
exit
ip vrouter "trust-vr"
  ip route 0.0.0.0/0 ***********
exit      
ip vrouter "E-FW"
  snatrule id 1 from address-book "range_*********-12" to address-book "Any" service "Any" trans-to ip 4.99.0.10/32 mode dynamicport log 
  snatrule id 2 from address-book "BJDC_**********" to address-book "Any" service "Any" trans-to ip 4.99.11.11/32 mode static log 
  snatrule id 3 from address-book "BJDC_**********" to address-book "Any" service "Any" trans-to ip 4.99.11.12/32 mode static log 
  snatrule id 4 from address-book "BJDC_**********" to address-book "Any" service "Any" trans-to ip 4.99.11.13/32 mode static log 
  snatrule id 5 from ip 4.9.11.30/32 to address-book "Any" service "Any" trans-to ip 4.99.10.30/32 mode static log 
  snatrule id 6 from ip 4.9.11.31/32 to address-book "Any" service "Any" trans-to ip 4.99.10.31/32 mode static log 
  snatrule id 7 from ip 4.9.11.32/32 to address-book "Any" service "Any" trans-to ip 4.99.10.32/32 mode static log 
  snatrule id 8 from ip 3.9.11.30/32 to address-book "Any" service "Any" trans-to ip 4.99.10.40/32 mode static log description "tmp" 
  snatrule id 9 from ip 3.9.11.31/32 to address-book "Any" service "Any" trans-to ip 4.99.10.41/32 mode static log description "tmp" 
  snatrule id 10 from ip 3.9.11.32/32 to address-book "Any" service "Any" trans-to ip 4.99.10.42/32 mode static log description "tmp" 
  snatrule id 1001 from ip ************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.11/32 mode static log 
  snatrule id 1002 from ip ************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.12/32 mode static log 
  snatrule id 1003 from ip *************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.13/32 mode static log 
  snatrule id 1004 from ip *************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.14/32 mode static log 
  snatrule id 1005 from ip ************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.15/32 mode static log 
  snatrule id 1006 from ip ************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.16/32 mode static log 
  snatrule id 1007 from ip ************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.17/32 mode static log 
  snatrule id 1008 from ip *************/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 1009 from ip *************/32 to address-book "Any" service "Any" trans-to ip 4.98.1.12/32 mode static log 
  snatrule id 12 from address-book "range_**********-15" to address-book "Any" service "Any" trans-to ip 4.99.11.151/32 mode dynamicport log 
  snatrule id 1010 from address-book "Range_Coding-NG" to address-book "Any" service "Any" trans-to address-book "*********" mode dynamicip log 
  snatrule id 13 from ip ************/24 to address-book "Any" service "Any" trans-to ip 4.99.11.14 mode dynamicport log 
  snatrule id 15 from ip ********/24 to address-book "Any" service "Any" trans-to ip 4.99.11.16 mode dynamicport log 
  snatrule id 14 from ip ************/24 to address-book "Any" service "Any" trans-to ip 4.99.11.15 mode dynamicport log 
  snatrule id 16 from address-book "CZB_***********/32" to address-book "Any" service "Any" trans-to address-book "CZB_NAT_172.31.18.81/32" mode static 
  snatrule id 17 from address-book "CZB_***********/32" to address-book "Any" service "Any" trans-to address-book "CZB_NAT_172.31.18.82/32" mode static 
  snatrule id 18 from address-book "CZB_***********/32" to address-book "Any" service "Any" trans-to address-book "CZB_NAT_172.31.18.83/32" mode static 
  snatrule id 19 from address-book "CZB_4.14.100.26/32" to address-book "Any" service "Any" trans-to address-book "CZB_NAT_172.31.18.84/32" mode static 
  snatrule id 20 from address-book "CZB_4.14.100.27/32" to address-book "Any" service "Any" trans-to address-book "CZB_NAT_172.31.18.85/32" mode static 
  snatrule id 21 from address-book "Range_Zabbix" to address-book "Any" service "Any" trans-to ip **********/32 mode dynamicport log 
  snatrule id 23 from address-book "VDI_**********" to address-book "Any" service "Any" trans-to address-book "*********" mode dynamicport log 
  snatrule id 30 from ip **********/32 to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 32 from ip **********/32 to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 34 from address-book "JG_HI_***********" to address-book "Any" service "Any" trans-to address-book "JG_HI_***********_NAT" mode static 
  snatrule id 35 from address-book "HaiNan_***********" to address-book "Any" service "Any" trans-to ip ***********/32 mode static log 
  snatrule id 36 from address-book "HaiNan_**********" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 37 from address-book "HaiNan_**********" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 40 from address-book "JG_HI_***********" to address-book "Any" service "Any" trans-to address-book "JG_HI_***********_NAT" mode static 
  snatrule id 45 from address-book "************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 46 from address-book "************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 47 from address-book "**********" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 48 from address-book "**********" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 51 from address-book "jiankong_***********/24" to address-book "Any" service "Any" trans-to address-book "GD_***********" mode dynamicport log 
  snatrule id 52 from address-book "************/24" to address-book "Any" service "Any" trans-to ip ***********/32 mode dynamicport log 
  snatrule id 53 from address-book "*************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 54 from address-book "**************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 55 from address-book "**************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 61 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 62 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 68 from ip **********/24 to address-book "Any" service "Any" trans-to ip ********* mode dynamicport log 
  snatrule id 63 from ip *********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 64 from ip *********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 65 from ip **********1/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 66 from ip **********2/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 70 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 11 from address-book "Range_SJJM-SSL" to address-book "Any" service "Any" trans-to address-book "*********" mode dynamicport log 
  snatrule id 71 from ip ************/24 to address-book "Any" service "Any" trans-to ip *********/32 mode dynamicport log 
  snatrule id 72 from ip ************/24 to address-book "Any" service "Any" trans-to ip *********/32 mode dynamicport log 
  snatrule id 73 from ip ************/24 to address-book "Any" service "Any" trans-to ip *********/32 mode dynamicport log 
  snatrule id 74 from ip ************/24 to address-book "Any" service "Any" trans-to ip *********/32 mode dynamicport log 
  snatrule id 75 from ip ************/24 to address-book "Any" service "Any" trans-to ip *********/32 mode dynamicport log 
  snatrule id 76 from ip ************/24 to address-book "Any" service "Any" trans-to ip *********/32 mode dynamicport log 
  snatrule id 77 from ip ************/24 to address-book "Any" service "Any" trans-to ip *********/32 mode dynamicport log 
  snatrule id 80 from address-book "************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 81 from address-book "************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 82 from ip **********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 83 from ip ************/32 to address-book "Any" service "Any" trans-to ip ***********/32 mode static log 
  snatrule id 84 from ip ************/32 to address-book "Any" service "Any" trans-to ip ***********/32 mode static log 
  snatrule id 85 from ip ************/32 to address-book "Any" service "Any" trans-to ip ***********/32 mode static log 
  snatrule id 86 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 87 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 88 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 89 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 90 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 91 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 92 from ip **********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 93 from ip **********/24 to address-book "Any" service "Any" trans-to ip *********** mode dynamicport log 
  snatrule id 100 from address-book "************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 102 from address-book "************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 101 from address-book "************" to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 22 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 24 from ip ***********/32 to address-book "Any" service "Any" trans-to ip *********/32 mode static log 
  snatrule id 94 from ip ************/32 to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 95 from ip ************/32 to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 96 from ip ************/32 to address-book "Any" service "Any" trans-to ip **********/32 mode static log 
  snatrule id 97 from ip ************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.26/32 mode static log 
  snatrule id 98 from ip ************/32 to address-book "Any" service "Any" trans-to ip 4.98.10.27/32 mode static log 
  dnatrule id 1001 from address-book "Any" to address-book "BJDC_**********" service "Any" trans-to ip 192.168.21.100/32 log 
  dnatrule id 1002 from address-book "Any" to address-book "BJDC_**********" service "Any" trans-to ip 192.168.21.20/32 log 
  dnatrule id 1 from address-book "Any" to address-book "***********" service "Any" trans-to address-book "*************" log 
  dnatrule id 2 from address-book "Any" to address-book "*********" service "Any" trans-to address-book "**************" log 
  dnatrule id 11 from address-book "Any" to ip **********/32 service "Any" trans-to ip 4.101.15.11/32 log 
  dnatrule id 12 from address-book "Any" to ip **********/32 service "Any" trans-to ip 4.101.15.12/32 log 
  dnatrule id 13 from address-book "Any" to ip **********/32 service "Any" trans-to ip 4.101.15.21/32 log 
  dnatrule id 14 from address-book "Any" to ip **********/32 service "Any" trans-to ip 4.101.15.22/32 log 
  dnatrule id 15 from address-book "Any" to ip *********/32 service "Any" trans-to ip 4.101.5.13/32 log 
  dnatrule id 1003 from address-book "Any" to ip **********/32 service "Any" trans-to ip 192.168.30.3/32 log 
  dnatrule id 1004 from address-book "Any" to ip **********/32 service "Any" trans-to ip 192.168.31.3/32 log 
  dnatrule id 1005 from address-book "Any" to address-book "*********" service "Any" trans-to address-book "************" log 
  dnatrule id 1006 from address-book "Any" to address-book "**********" service "Any" trans-to address-book "***********" log 
  dnatrule id 1007 from address-book "Any" to address-book "**********" service "Any" trans-to address-book "**********" log 
  dnatrule id 10 from address-book "Any" to address-book "IP_*********" service "Any" trans-to address-book "IP_*************" log 
  dnatrule id 3 from address-book "Any" to address-book "CZB_NAT_**********/32" service "Any" trans-to address-book "CZB_************/32" log 
  dnatrule id 4 from address-book "Any" to address-book "CZB_NAT_**********/32" service "Any" trans-to address-book "CZB_************/32" 
  dnatrule id 22 from address-book "Any" to ip 4.99.1.22/32 service "Any" trans-to ip 4.101.5.22/32 log 
  dnatrule id 24 from address-book "Any" to ip *********/32 service "Any" trans-to ip 4.101.5.24/32 log 
  dnatrule id 25 from address-book "Any" to ip *********/32 service "Any" trans-to ip 4.101.90.11/32 log 
  dnatrule id 26 from address-book "Any" to ip 4.99.1.32/32 service "Any" trans-to ip 4.101.90.12/32 log 
  dnatrule id 27 from address-book "Any" to ip 4.99.1.33/32 service "Any" trans-to ip 4.101.90.13/32 log 
  dnatrule id 28 from address-book "Any" to ip *********/32 service "Any" trans-to ip 4.101.90.14/32 log 
  dnatrule id 31 from address-book "Any" to ip **********/32 service "Any" trans-to ip *************/32 log 
  dnatrule id 33 from address-book "Any" to ip **********/32 service "Any" trans-to ip *************/32 log 
  dnatrule id 38 from address-book "Any" to address-book "JG_HI_***********_NAT" service "Any" trans-to address-book "JG_HI_***********" log 
  dnatrule id 39 from address-book "Any" to address-book "HaiNan_***********" service "Any" trans-to ip ***********/32 log 
  dnatrule id 41 from address-book "Any" to ip ***********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 42 from address-book "Any" to ip **********/32 service "Any" trans-to ip **********/32 log 
  dnatrule id 43 from address-book "Any" to ip **********/32 service "Any" trans-to ip **********/32 log 
  dnatrule id 44 from address-book "Any" to ip *********/32 service "Any" trans-to ip **********/32 log 
  dnatrule id 49 from address-book "Any" to ip **********/32 service "Any" trans-to ip **********/32 log 
  dnatrule id 50 from address-book "Any" to ip **********/32 service "Any" trans-to ip ************/32 log 
  dnatrule id 56 from address-book "Any" to ip ***********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 57 from address-book "Any" to address-book "BJDC_***********" service "Any" trans-to ip **************/32 log 
  dnatrule id 60 from address-book "Any" to address-book "*********" service "Any" trans-to address-book "**************" log 
  dnatrule id 67 from address-book "Any" to ip *********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 69 from address-book "Any" to ip *********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 68 from address-book "Any" to ip ********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 70 from address-book "Any" to ip *********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 71 from address-book "Any" to ip *********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 72 from address-book "Any" to ip *********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 73 from address-book "Any" to ip *********/32 service "Any" trans-to ip ***********/32 log 
  dnatrule id 74 from address-book "Any" to ip *********/32 service "Any" trans-to ip **********/32 log 
  dnatrule id 75 from address-book "Any" to ip *********/32 service "Any" trans-to ip **********/32 log 
  dnatrule id 76 from address-book "Any" to ip *********/32 service "Any" trans-to ip **********/32 log 
  ip route *********/16 ***********
  ip route *********/24 *********** description "XW_CIMS_OCS"
  ip route ***********/32 ***********
  ip route 0.0.0.0/0 ***********
  ip route *********/24 *********** description "XW_UMP"
  ip route ********/24 *********** description "XW_BJDC_Proxy"
  ip route ********/24 *********** description "XW_ZhengJuHeiXiang"
  ip route ********/24 *********** description "YZ_ZhengJuHeiXiang"
  ip route ********/8 ***********
  ip route ********/16 ***********
  ip route ********/16 ***********
  ip route ********/16 ***********
  ip route ********/24 *********** description "XW_UMP"
  ip route *********/24 ***********
  ip route ********/24 ***********
  ip route ************/24 ***********
  ip route ************/24 ***********
  ip route **********/24 ***********
  ip route *********/24 ***********
  ip route ***********/24 ***********
  ip route *********/24 ***********
  ip route ***********/24 ***********
  ip route ************/24 ***********
  ip route **********/24 ***********
  ip route ***********/24 ***********
  ip route ***********/24 ***********
  ip route **********/24 ***********
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp enable
ntp server ******* vrouter mgt-vr
clock zone china
rule id 1
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "ICMP"
exit
rule id 2
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "CIMS_*********/24"
  src-addr "host_***********/32"
  dst-addr "Any"
  service "SSH"
exit
rule id 8
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "BJDC_**********"
  src-addr "BJDC_**********"
  src-addr "BJDC_**********"
  src-addr "********"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "Range_Zabbix"
  dst-addr "BJDC_**********"
  service "HTTP"
  name "Proxy访问北京单场后台"
exit
rule id 9
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "range_*********-12"
  src-addr "************/24"
  dst-addr "BJDC_**********"
  service "HTTPS"
  name "UMP访问北京单场后台"
exit
rule id 3
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-range 4.9.11.30 4.9.11.32
  src-range 3.9.11.30 3.9.11.32
  dst-ip ***********/32
  service "TCP_6666"
  name "证据黑箱访问即开公证"
exit
rule id 4
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "Range_SJJM-SSL"
  dst-addr "*********"
  service "HTTPS"
  name "数据建模SSL访问实验室"
exit
rule id 10
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  service "TCP-23000"
  name "YYGS-TO-ZhiFU-1"
exit
rule id 11
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  dst-addr "*********"
  dst-addr "*********"
  dst-ip 4.99.1.22/32
  service "tcp-443"
  name "YYGS-TO-https"
exit
rule id 12
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  service "TCP-8022"
  name "YYGS-TO-juncai-1 4.1"
exit
rule id 13
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  service "TCP-54102"
  name "YYGS-TO-ZhiFU-2"
exit
rule id 14
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  service "TCP-54103"
  name "YYGS-TO-ZhiFU-3"
exit
rule id 15
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  service "TCP-54104"
  name "YYGS-TO-ZhiFU-4"
exit
rule id 16
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  service "TCP-28080"
  name "YYGS-TO-juncai-2 4.1"
exit
rule id 17
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "************"
  service "TCP-80"
  name "YYGS-TO-YUNWEI"
exit
rule id 18
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "***********"
  service "TCP-8080"
  name "YYGS-TO-JGXT"
exit
rule id 19
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "********-2"
  service "AD-PORT"
  name "YYGS-TO-AD"
exit
rule id 5
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**************"
  src-addr "**************"
  src-ip *************/32
  src-ip *************/32
  src-ip ************/32
  src-ip ************/32
  src-ip ************/32
  src-ip ************/32
  src-ip ************/32
  src-ip ************/32
  src-ip ************/32
  dst-ip **********/32
  dst-ip **********/32
  service "SSH"
  name "补号终端访问即开设奖数据管理生产文件提交服务"
exit
rule id 6
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*************"
  src-ip ************/32
  src-ip ************/32
  src-ip ************/32
  dst-ip **********/32
  dst-ip **********/32
  service "SSH"
  name "喷印服务器访问即开设奖数据管理印刷文件服务"
exit
rule id 7
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-ip *************/32
  src-ip *************/32
  dst-ip *********/32
  service "HTTPS"
  name "G3 Coding平台-测试环境制品库数据同步"
exit
rule id 20
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "*********"
  service "TCP-8080"
  service "TCP-8081"
  service "tcp-443"
  name "YYGS-TO-macfee"
exit
rule id 21
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "range_**********-15"
  src-addr "range_*********-12"
  src-addr "**********/24"
  dst-ip **********/32
  dst-ip **********/32
  service "HTTPS"
  name "UMP和奖期大屏访问丰台开奖"
exit
rule id 22
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "Range_Coding-NG"
  dst-addr "*********"
  dst-addr "IP_*********"
  service "tcp-4422"
  name "应用运维coding文件摆渡"
exit      
rule id 23
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********-65"
  dst-addr "**********"
  service "TCP-30000"
  name "海南电投访问开放平台SFTP"
exit
rule id 24
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********-65"
  src-addr "HaiNan_***********"
  src-addr "HaiNan_**********"
  src-addr "HaiNan_**********"
  dst-addr "**********"
  service "HTTPS"
  name "海南电投访问开放平台SSL"
exit
rule id 25
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "*********"
  service "tcp-443"
  service "TCP-80"
  service "TCP-8080"
  name "运营公司主机访问数据中心新mcafee主机"
exit
rule id 26
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "*********"
  dst-addr "YYGS_********"
  service "TCP-8081"
  name "数据中心新mcafee主机访问运营公司"
exit
rule id 27
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "CZB_4.14.100.27/32"
  src-addr "CZB_4.14.100.26/32"
  src-addr "CZB_***********/32"
  src-addr "CZB_***********/32"
  src-addr "CZB_***********/32"
  dst-addr "CZB_NAT_**********/32"
  dst-addr "CZB_NAT_**********/32"
  service "tcp_9001"
  name "访问财政部"
exit
rule id 28
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_********"
  dst-addr "**********"
  service "TCP-3000"
  name "YYGS_To_Grafana"
exit
rule id 29
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "VDI_**********"
  dst-ip *********/32
  service "HTTPS"
  service "TCP-10102"
  name "VDI_To_BaoLeiJi-1"
exit
rule id 30
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "VDI_**********"
  dst-addr "Range_*********-34"
  service "rdp-3389"
  service "tcp-389"
  name "VDI_To_BaoLeiJi-2"
exit
rule id 31
  action deny
  src-zone "trust"
  dst-zone "untrust"
  src-addr "**********"
  src-addr "**********"
  dst-ip **********/32
  dst-ip **********/32
  service "UDP-1234"
  name "KaiJiangShiPin_To_FengTi"
exit
rule id 32
  action deny
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*************"
  src-addr "*************"
  dst-ip **********/32
  dst-ip **********/32
  service "UDP-1234"
  name "FengTi_To_KaiJiangShiPin"
exit
rule id 33
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "HaiNan_***********"
  src-addr "HaiNan_**********"
  src-addr "HaiNan_**********"
  dst-addr "JG_HI_***********_NAT"
  service "tcp-443"
  name "HaiNan_To_JianGuan-1"
exit
rule id 34
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "JG_HI_***********"
  dst-addr "HaiNan_***********"
  dst-ip ***********/32
  service "TCP-6006"
  service "TCP-8931"
  name "JianGuan_To_HaiNan-1"
exit
rule id 35
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "HaiNan_***********"
  src-addr "HaiNan_**********"
  src-addr "HaiNan_**********"
  dst-addr "**********"
  service "TCP-30000"
  name "HaiNan_To_SFTP"
exit
rule id 36
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "JG_HI_***********"
  dst-ip ***********/32
  dst-ip **********/32
  dst-ip **********/32
  service "tcp-443"
  name "JianGuan_To_HaiNan-2"
exit
rule id 37
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "HaiNan_***********"
  src-addr "HaiNan_**********"
  src-addr "HaiNan_**********"
  src-addr "DEVTEST-YYOS-VDI"
  dst-ip *********/32
  service "TCP_34443"
  name "HaiNan_To_SFTP-1"
exit
rule id 38
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "************"
  src-addr "************"
  dst-ip **********/32
  service "TCP-8080"
  service "TCP-8081"
  name "CT-Cloud_To_CS-SSL"
exit
rule id 39
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "**********"
  src-addr "**********"
  dst-ip **********/32
  service "HTTPS"
  name "CS-b2csweb_To_CT-Cloud"
exit
rule id 40
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "jiankong_***********/24"
  dst-ip **********/32
  service "TCP-80"
  service "HTTPS"
  name "jiankong_To_gaode"
exit
rule id 41
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "HaiNan_***********"
  src-addr "HaiNan_**********"
  src-addr "HaiNan_**********"
  dst-ip ***********/32
  service "SSH"
  name "HaiNan_To_KFPT-SFTP"
exit
rule id 42
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "************/24"
  dst-addr "BJDC_***********"
  service "HTTPS"
  name "XiaoShouFuWu_To_BJDC"
exit
rule id 43
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "Range_***********-32"
  dst-addr "*********"
  service "TCP_18081"
  name "SJJM-NG_To_SSL"
exit
rule id 44
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "Range_SJJM-SSL"
  src-ip ***********/32
  src-ip ***********/32
  src-ip *********/32
  src-ip *********/32
  dst-ip *********/32
  service "HTTPS"
  name "USAP-SSL_To_XT-SSL"
exit
rule id 45
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********/24"
  dst-ip *********/32
  service "tcp-29093"
  name "XT-Know_To_Moniter"
exit
rule id 46
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-ip ***********/32
  dst-addr "**********"
  service "HTTPS"
  name "XT-GLGJ_To_KFPT-SSL"
exit
rule id 47
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********"
  dst-addr "**********"
  dst-addr "**********"
  service "FTP"
  name "北京单场新建"
exit
rule id 48
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "DEVTEST-YYOS-VDI"
  dst-ip ********/32
  service "SFTP22"
  name "YYOS-VDI_to_SFTP"
exit
rule id 49
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "************"
  src-addr "************"
  src-ip ***********/32
  dst-ip ***********/32
  service "SSH"
  name "KFSJFX_to_SFTP"
exit
rule id 50
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "************-36"
  dst-addr "nat_*********-29"
  dst-addr "nat*********"
  service "TCP-80"
  service "TCP-8080"
  service "tcp-32600"
  name "aopsapp-to-CSaopsapp"
exit
rule id 51
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "***********-14"
  src-addr "***********-19"
  src-addr "**********"
  src-addr "**********"
  src-addr "***********-22"
  dst-addr "nat*********"
  service "HTTPS"
  service "tcp-8443"
  service "tcp-7443"
  name "CSaops-to-SSL"
exit
rule id 52
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "************"
  src-addr "************"
  src-addr "************"
  dst-ip *********/32
  service "HTTPS"
  name "CT-Cloud_To_USAP-SSL"
exit
tcp-seq-check-disable
l2-nonip-action drop
no alg auto
no alg tftp
no alg msrpc
no alg sqlnetv2
no alg rsh
no alg rtsp
no alg http
no alg sunrpc
no alg ras
no alg q931
no alg sip
no alg pptp
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server vrouter "mgt-vr"
snmp-server engineID "hillstone"
snmp-server host *********** version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq ro
snmp-server host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg ro
snmp-server trap-host *********** version 2c community ckeI3SSNGQhyyPo3P+lMJmQ8mGku port 162
snmp-server trap-host ************ version 2c community DWbf06+a4P0FlgO+Yrtfp2YzSHMK port 162
snmp-server trap-host ************ version 2c community iYrqDOGOtTqwQ38yjPmPA3Sw3FEJ port 162
snmp-server trap-host ************ version 2c community IQKm6jOQzQw3266DzxLL5hlJXP8D port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional
exit
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application
exit
query-groups
  dashboard-query-group "netadmin-**********377-dashboard-query-group" user "netadmin"
    rule "license" create-time ********** id 1 query-string "%7B%22time%22%3A1700730763538%2C%22ignore%22%3Atrue%7D"
  exit
exit
longlife-sess-percent 8
no sms disable
ha link interface HA0
ha link data interface ethernet0/7
ha link ip ******* ***************
ha group 0
  monitor track "JianCe"
exit
ha cluster 1 node 0

End

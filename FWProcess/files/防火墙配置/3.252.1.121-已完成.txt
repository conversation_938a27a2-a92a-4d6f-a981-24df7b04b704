YZB5MFW01(M)# show config

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "ECC-vr"
exit
ip vrouter "yunwei"
exit
ip vrouter "YUNWEI"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit      
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
zone "ECC-trust"
exit
zone "ECC-untrust"
exit
zone "yunwei-trust"
exit
zone "yunwei-untrust"
exit
zone "YUNWEI-trust"
exit      
zone "YUNWEI-untrust"
exit
interface vswitchif1
exit
interface MGT0 local
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit      
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit
interface aggregate1
exit
interface aggregate1.201
exit
interface aggregate1.200
exit
interface aggregate1.202
exit
interface aggregate1.203
exit
address "private_network"
exit      
address "*********/24"
exit
address "*********/24"
exit
address "*********/24"
exit
address "*********/25"
exit
address "**********/25"
exit
address "************/26"
exit
address "*********"
exit
address "**********"
exit
address "*************"
exit
address "************"
exit
address "***********"
exit
address "***********"
exit      
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "***********"
exit
address "************"
exit      
address "***********"
exit
address "***********"
exit
address "**********"
exit
address "**********"
exit
address "***********"
exit
address "*********"
exit
address "*********"
exit
address "************"
exit
address "************"
exit
address "**********"
exit
address "*********"
exit
address "*************"
exit      
address "************"
exit
address "**********"
exit
address "***********"
exit
address "***********"
exit
address "************"
exit
address "************"
exit
address "*************"
exit
address "**********-33"
exit
address "*********-204"
exit
address "*********-214"
exit
address "***********-223"
exit
address "***********-12"
exit      
address "***********-22"
exit
address "********-2"
exit
address "************-12"
exit
address "************-14"
exit
address "*************-172"
exit
address "**********-2"
exit
address "************"
exit
address "************"
exit
address "***********/24"
exit
address "********/24"
exit
address "*************"
exit
address "*************"
exit      
address "************-112"
exit
address "*********"
exit
address "**********/24"
exit
address "CallCenter"
exit
address "**********/24"
exit
address "**********/24"
exit
address "********/24"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********/32"
exit
address "***********-24"
exit
address "************/32"
exit      
address "***********/32"
exit
address "**********/27"
exit
address "***********/32"
exit
address "***********/32"
exit
address "*********/32"
exit
address "*********"
exit
address "***********"
exit
address "***********-226"
exit
address "*************"
exit
address "ip_public"
exit
address "***********-61"
exit
aaa-server "local" type local
exit      
track "track-ha"
exit
service "389"
  tcp dst-port 389 
  udp dst-port 389 
exit
service "137"
  tcp dst-port 137 
  udp dst-port 137 
exit
service "udp-138"
  udp dst-port 138 
exit
service "tcp-135"
  tcp dst-port 135 
exit
service "tcp-139"
  tcp dst-port 139 
exit
service "88"
  tcp dst-port 88 
  udp dst-port 88 
exit
service "464"
  tcp dst-port 464 
  udp dst-port 464 
exit
service "tcp-3268"
  tcp dst-port 3268 
exit
service "tcp-3269"
  tcp dst-port 3269 
exit
service "tcp-445"
  tcp dst-port 445 
exit
service "TCP-1883"
  tcp dst-port 1883 
exit
service "tcp-6066"
  tcp dst-port 6066 
exit
service "TCP-389"
  tcp dst-port 389 
exit
service "tcp-3389"
  tcp dst-port 3389 
exit      
service "TCP-443"
  tcp dst-port 443 
exit
service "tcp-10102"
  tcp dst-port 10102 
exit
service "tcp-8080"
  tcp dst-port 8080 
exit
service "tcp-8000"
  tcp dst-port 8000 
exit
service "tcp-4433"
  tcp dst-port 4433 
exit
service "53"
  tcp dst-port 53 
  udp dst-port 53 
exit
service "tcp-636"
  tcp dst-port 636 
exit
service "tcp-1024-65535"
  tcp dst-port 1024 65535 
exit
service "tcp-16310-16316"
  tcp dst-port 16310 16316 
exit
service "tcp-16320-16323"
  tcp dst-port 16320 16323 
exit
service "tcp-9526"
  tcp dst-port 9526 
exit
service "111"
  tcp dst-port 111 
  udp dst-port 111 
exit
service "2049"
  tcp dst-port 2049 
  udp dst-port 2049 
exit
service "4046"
  tcp dst-port 4046 
  udp dst-port 4046 
exit
service "tcp-635"
  tcp dst-port 635 
exit
service "tcp-9243"
  tcp dst-port 9243 
exit
service "tcp-6008"
  tcp dst-port 6008 
exit
service "tcp-22"
  tcp dst-port 22 
exit
service "tcp-8090"
  tcp dst-port 8090 
exit
service "tcp-8091"
  tcp dst-port 8091 
exit
service "tcp-8089"
  tcp dst-port 8089 
exit
service "tcp-29091"
  tcp dst-port 29091 
exit
service "tcp-30000"
  tcp dst-port 30000 
exit
service "tcp-29411"
  tcp dst-port 29411 
exit
service "tcp-25601"
  tcp dst-port 25601 
exit
service "tcp-28080"
  tcp dst-port 28080 
exit
service "tcp-8022"
  tcp dst-port 8022 
exit
service "tcp-15000"
  tcp dst-port 15000 
exit
service "tcp-30900"
  tcp dst-port 30900 
exit
service "tcp-80"
  tcp dst-port 80 
exit
service "tcp-1521"
  tcp dst-port 1521 
exit
service "TCP-8111"
  tcp dst-port 8111 
exit
service "tcp-16311"
  tcp dst-port 16311 
exit
service "tcp-8900"
  tcp dst-port 8900 
exit
service "tcp-6044"
  tcp dst-port 6044 
exit
service "tcp-1918"
  tcp dst-port 1918 
exit
service "udp-51444"
  udp dst-port 51444 
exit
service "udp-514"
  udp dst-port 514 
exit
service "tcp-8081"
  tcp dst-port 8081 
exit
service "tcp-8082"
  tcp dst-port 8082 
exit
service "tcp-8443"
  tcp dst-port 8443 
exit
service "tcp-8444"
  tcp dst-port 8444 
exit
service "tcp-8801"
  tcp dst-port 8801 
exit
service "tcp-1433"
  tcp dst-port 1433 
exit
service "UDP-8010"
  udp dst-port 8010 
exit
service "TCP-8003"
  tcp dst-port 8003 
exit
service "tcp-30057"
  tcp dst-port 30057 
exit
service "TCP_30031-30049"
  tcp dst-port 30031 30049 
exit
service "TCP-8888"
  tcp dst-port 8888 
exit
sandbox-profile "predef_low"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_middle"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_high"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  file-type swf
  file-type rar
  file-type zip
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
sandbox-profile "predef_pe"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
url-profile "no-url"
exit
track "track-ha"
  interface aggregate1 
exit
admin user "hillstone"
  password MZNqwJC6VIag90OW7CanSCIQ8O
        password-expiration 1661903149
  role "admin"
  access console
exit
admin user "netadmin"
  password 2/1oCWNB3VBHpgt7iPxsf1zggI
        password-expiration 1661903101
  role "admin"
  access console
  access ssh
  access https
exit
no logging traffic session to buffer
no logging traffic nat to buffer
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog ************ vrouter "mgt-vr" udp 514 type config
logging syslog ************ vrouter "mgt-vr" udp 514 type network
logging syslog ************ vrouter "mgt-vr" udp 514 type nbc
logging syslog ************ vrouter "mgt-vr" udp 514 type threat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic session
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic nat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic pbr
logging syslog ************ vrouter "mgt-vr" udp 514 type debug
logging syslog ************ vrouter "mgt-vr" udp 514 type sandbox
logging syslog ************ vrouter "mgt-vr" tcp 9092 type event
logging syslog ************ vrouter "mgt-vr" tcp 9092 type config
logging syslog ************ vrouter "mgt-vr" tcp 9092 type network
logging syslog ************ vrouter "mgt-vr" tcp 9092 type nbc
logging syslog ************ vrouter "mgt-vr" tcp 9092 type threat
logging syslog ************ vrouter "mgt-vr" tcp 9092 type traffic session
logging syslog ************ vrouter "mgt-vr" tcp 9092 type traffic nat
logging syslog ************ vrouter "mgt-vr" tcp 9092 type traffic web-surf
logging syslog ************ vrouter "mgt-vr" tcp 9092 type traffic pbr
logging syslog ************ vrouter "mgt-vr" tcp 9092 type debug
logging syslog ************ vrouter "mgt-vr" tcp 9092 type sandbox
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "*********/25"
  ip *********/25
exit
address "**********/25"
  ip **********/25
exit
address "************/26"
  ip ************/26
exit
address "*********"
  ip *********/32
exit      
address "**********"
  ip **********/32
exit
address "*************"
  ip *************/32
exit
address "************"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit      
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit      
address "************"
  ip ************/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit      
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/32
exit
address "*********"
  ip *********/32
exit
address "*************"
  ip *************/32
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit      
address "***********"
  ip ***********/32
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "*************"
  ip *************/32
exit
address "**********-33"
  range ********** **********
exit
address "*********-204"
  range ********* *********
exit
address "*********-214"
  range ********* *********
exit
address "***********-223"
  range *********** 3.30.11.223
exit      
address "***********-12"
  range *********** 4.254.11.12
exit
address "***********-22"
  range *********** 4.254.11.22
exit
address "********-2"
  range ******** 4.20.1.2
exit
address "************-12"
  range ************ 4.103.100.12
exit
address "************-14"
  range ************ 4.190.163.14
exit
address "*************-172"
  range ************* 4.190.122.172
exit
address "**********-2"
  range ********** 4.190.83.2
exit
address "************"
  ip ************/32
exit      
address "************"
  ip ************/32
exit
address "***********/24"
  ip ***********/24
exit
address "********/24"
  ip ********/24
exit
address "*************"
  ip *************/32
exit
address "*************"
  ip *************/32
exit
address "************-112"
  range ************ 3.20.101.112
exit
address "*********"
  ip *********/32
  ip 4.254.0.2/32
  ip *********0/32
exit
address "**********/24"
  ip 4.192.0.0/10
exit
address "CallCenter"
  ip *********/24
  ip *********/24
  ip *********/24
  ip ***********/32
  ip 4.30.11.0/24
  ip 3.20.1.41/32
  ip 3.20.1.42/32
  ip 18.2.1.0/24
  ip 3.22.10.1/32
  ip 3.22.10.2/32
  ip 3.22.10.8/32
  ip 4.255.209.0/24
exit
address "**********/24"
  ip **********/24
exit
address "**********/24"
  ip **********/24
exit
address "********/24"
  ip ********/24
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "***********-24"
  range *********** 198.3.10.24
exit
address "************/32"
  ip ************/32
exit
address "***********/32"
  ip ***********/32
exit
address "**********/27"
  ip **********/27
exit
address "***********/32"
  ip ***********/32
exit
address "***********/32"
  ip ***********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "***********"
  ip ***********/32
exit
address "***********-226"
  range *********** 3.30.11.226
exit
address "*************"
  ip *************/32
exit
address "ip_public"
  ip 3.0.0.0/8
  ip 4.0.0.0/8
  ip 0.0.0.0/0
exit      
address "***********-61"
  range *********** 4.101.52.61
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
zone "ECC-trust"
  vrouter "ECC-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "ECC-untrust"
  vrouter "ECC-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "yunwei-trust"
  vrouter "yunwei"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "yunwei-untrust"
  vrouter "yunwei"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "YUNWEI-trust"
  vrouter "YUNWEI"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "YUNWEI-untrust"
  vrouter "YUNWEI"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
hostname "YZB5MFW01"
admin host any any
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit      

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit      

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit
          
isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit      

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface MGT0 local
  zone  "mgt"
  ip address 3.252.1.121 255.255.255.0
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet0/8
  aggregate aggregate1
exit      
interface xethernet0/9
  aggregate aggregate1
exit
interface aggregate1
  zone  "l2-trust"
  bandwidth downstream 20000000000
  bandwidth upstream 20000000000
  lacp enable
exit
interface aggregate1.201
  zone  "ECC-trust"
  ip address 3.255.249.6 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.200
  zone  "ECC-untrust"
  ip address 3.255.249.2 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.202
  zone  "yunwei-trust"
  ip address ***********0 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.203
  zone  "yunwei-untrust"
  ip address ***********4 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
ip vrouter "trust-vr"
  ip route 0.0.0.0/0 3.252.1.254
exit
ip vrouter "ECC-vr"
  ip route 0.0.0.0/0 ***********
  ip route *********/24 ***********
  ip route *********/24 ***********
  ip route *********/24 ***********
exit
ip vrouter "yunwei"
  ip route **********/24 ***********
  ip route 0.0.0.0/0 ***********3
  ip route **********/24 ***********
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp server ******* vrouter mgt-vr
clock zone china
rule id 36
  action permit
  src-zone "yunwei-trust"
  dst-zone "yunwei-untrust"
  src-addr "Any"
  dst-addr "ip_public"
  service "Any"
  name "yunwei-trust-untrust-100"
exit
rule id 1
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "*********"
  dst-addr "*********-204"
  dst-addr "*********-214"
  service "389"
  service "137"
  service "udp-138"
  service "tcp-135"
  service "tcp-139"
  service "88"
  service "464"
  service "tcp-3268"
  service "tcp-3269"
  service "tcp-445"
  service "ICMP"
  name "ECC-trust-untrust-1"
exit
rule id 2
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  src-addr "*********"
  dst-addr "*************"
  dst-addr "***********/32"
  service "TCP-1883"
  service "tcp-6066"
  name "ECC-trust-untrust-2"
exit
rule id 3
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "*********/25"
  dst-addr "**********/25"
  dst-addr "************/26"
  service "TCP-389"
  service "tcp-3389"
  service "TCP-443"
  service "tcp-10102"
  name "ECC-trust-untrust-3"
exit
rule id 4
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "************"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  service "TCP-443"
  service "tcp-8000"
  service "UDP-8010"
  service "TCP-8003"
  name "ECC-trust-untrust-4"
exit
rule id 5 
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********-223"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  service "tcp-4433"
  name "ECC-trust-untrust-5"
exit
rule id 6
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "***********-12"
  dst-addr "***********-22"
  service "tcp-8080"
  service "HTTP"
  name "ECC-trust-untrust-6"
exit
rule id 7
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "********-2"
  service "53"
  service "tcp-135"
  service "tcp-1024-65535"
  service "389"
  service "tcp-636"
  service "88"
  service "tcp-445"
  service "NTP"
  name "ECC-trust-untrust-7"
exit
rule id 8
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********-223"
  src-addr "***********-226"
  dst-addr "************-12"
  service "FTP"
  service "SSH"
  service "TCP-443"
  name "ECC-trust-untrust-8"
exit
rule id 9
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "***********"
  dst-addr "************"
  service "tcp-16310-16316"
  service "tcp-16320-16323"
  name "ECC-trust-untrust-9"
exit
rule id 10
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "***********"
  service "tcp-1521"
  name "ECC-trust-untrust-10"
exit
rule id 11
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********-33"
  service "tcp-8080"
  name "ECC-trust-untrust-11"
exit
rule id 12
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "***********"
  service "tcp-9526"
  name "ECC-trust-untrust-12"
exit
rule id 13
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "*********"
  service "111"
  service "2049"
  service "4046"
  service "tcp-635"
  name "ECC-trust-untrust-13"
exit
rule id 14
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "*********"
  service "TCP-389"
  name "ECC-trust-untrust-14"
exit
rule id 15
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "*********"
  service "TCP-389"
exit
rule id 16
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "************"
  service "tcp-9243"
  name "ECC-trust-untrust-15"
exit
rule id 17
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "************"
  service "HTTP"
  name "ECC-trust-untrust-16"
exit
rule id 18
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "**********"
  service "tcp-6008"
  name "ECC-trust-untrust-17"
exit
rule id 19
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********"
  dst-addr "*********"
  service "tcp-22"
  name "ECC-trust-untrust-18"
exit
rule id 20
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "************-14"
  dst-addr "*************-172"
  dst-addr "*************"
  service "TCP-443"
  name "ECC-trust-untrust-19"
exit
rule id 21
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "************"
  dst-addr "*************-172"
  dst-addr "**********"
  service "tcp-8090"
  name "ECC-trust-untrust-20"
exit
rule id 22
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "************"
  service "tcp-8091"
  service "tcp-8089"
  service "tcp-29091"
  service "tcp-30000"
  name "ECC-trust-untrust-21"
exit
rule id 23
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "************"
  service "tcp-8091"
  service "tcp-8089"
  service "tcp-29091"
  service "tcp-30000"
exit
rule id 24
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "***********"
  service "tcp-29411"
  name "ECC-trust-untrust-22"
exit
rule id 25
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "***********"
  service "tcp-25601"
  name "ECC-trust-untrust-23"
exit
rule id 26
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "************"
  service "tcp-28080"
  name "ECC-trust-untrust-24"
exit
rule id 27
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "************"
  service "tcp-8022"
  name "ECC-trust-untrust-25"
exit
rule id 28
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "*************-172"
  service "tcp-15000"
  name "ECC-trust-untrust-26"
exit
rule id 29
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "**********-2"
  service "tcp-30900"
  name "ECC-trust-untrust-27"
exit      
rule id 30
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "*************"
  service "tcp-80"
  name "ECC-trust-untrust-28"
exit
rule id 31
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "ICMP"
  name "ICMP"
exit
rule id 32
  action permit
  src-zone "yunwei-untrust"
  dst-zone "yunwei-trust"
  src-addr "Any"
  dst-addr "************"
  service "tcp-80"
  service "TCP-443"
  name "yunwei-untrust-trust22"
exit
rule id 33
  action permit
  src-zone "yunwei-untrust"
  dst-zone "yunwei-trust"
  src-addr "***********/24"
  dst-addr "************"
  dst-addr "************-112"
  dst-addr "************"
  service "tcp-8080"
  name "yunwei-untrust-trust45"
exit
rule id 34
  action permit
  src-zone "yunwei-untrust"
  dst-zone "yunwei-trust"
  src-addr "********/24"
  dst-addr "************"
  service "TCP-8111"
  name "yunwei-untrust-trust29"
exit      
rule id 54
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-range ************ ************
  service "TCP-443"
  service "tcp-8443"
  service "HTTP"
  service "TCP-8888"
  name "ECC-mipingSSL"
exit
rule id 35
  action permit
  src-zone "yunwei-untrust"
  dst-zone "yunwei-trust"
  src-addr "*************"
  src-addr "*************"
  dst-addr "************"
  service "tcp-80"
  service "tcp-8080"
  name "yunwei-untrust-trust38"
exit
rule id 37
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "**********/24"
  dst-addr "ip_public"
  service "Any"
  name "OCS-璁块棶"
exit
rule id 38
  action permit
  src-zone "yunwei-untrust"
  dst-zone "yunwei-trust"
  src-addr "CallCenter"
  dst-addr "ip_public"
  service "Any"
  name "yunwei-untrust-trust39"
exit
rule id 39
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "**********/24"
  dst-addr "**********/24"
  dst-addr "********/24"
  service "Any"
  name "ECC-trust-untrust-29"
exit
rule id 40
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "***********/32"
  dst-addr "***********/32"
  service "tcp-8080"
  service "udp-51444"
  name "ECC-trust-untrust-30"
exit
rule id 41
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "***********"
  service "tcp-16311"
  name "ECC-trust-untrust-31"
exit
rule id 42
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  service "tcp-8900"
  name "ECC-trust-untrust-32"
exit
rule id 43
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********/32"
  dst-addr "************/32"
  service "tcp-1521"
  name "ECC-trust-untrust-34"
exit
rule id 44
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********/32"
  dst-addr "***********-24"
  service "udp-514"
  name "ECC-trust-untrust-35"
exit
rule id 45
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "***********/32"
  dst-addr "**********/27"
  service "tcp-6044"
  service "tcp-1918"
  name "ECC-trust-untrust-36"
exit
rule id 46
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "***********/32"
  service "tcp-80"
  name "ECC-trust-untrust-37"
exit
rule id 47
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "*********/32"
  service "TCP-443"
  service "tcp-8080"
  service "tcp-8081"
  service "tcp-8082"
  service "tcp-8443"
  service "tcp-8444"
  service "tcp-8801"
  service "tcp-1433"
  service "tcp-80"
  name "ECC-trust-untrust-38"
exit
rule id 48
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "*********"
  service "Any"
  name "ECC-trust-untrust-39"
exit      
rule id 49
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "*************"
  service "tcp-30057"
  name "ECC_To_G3-DaPing"
exit
rule id 50
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-addr "***********-61"
  service "HTTP"
  name "ecc-to-aopsnginx"
exit
rule id 51
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-ip ***********/32
  service "tcp-8080"
  name "ecc-to-TianXunKanBan"
exit
rule id 52
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-ip **********/32
  service "TCP_30031-30049"
  name "ecc-to-TongYiJianKong"
exit
rule id 53
  action permit
  src-zone "ECC-trust"
  dst-zone "ECC-untrust"
  src-addr "*********/24"
  dst-ip ***********3/32
  service "TCP-443"
  name "ecc-to-ZhongDuanJianKong"
exit
l2-nonip-action drop
no tcp-mss all
tcp-mss tunnel 1380
snmp-server vrouter "mgt-vr"
snmp-server host ************ version 2c community DWbf06+a4P0FlgO+Yrtfp2YzSHMK ro
snmp-server host ************ version 2c community IQKm6jOQzQw3266DzxLL5hlJXP8D ro
snmp-server host *********** version 2c community fj7VB6vTZUNWzD1fJOwUEirolEo7 ro
snmp-server trap-host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg port 162
snmp-server trap-host ************ version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq port 162
snmp-server trap-host *********** version 2c community ckeI3SSNGQhyyPo3P+lMJmQ8mGku port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
  icmp-unreachable-session-keep
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional vsys
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional vsys
exit      
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application vsys
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application vsys
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application vsys
exit
query-groups
  dashboard-query-group "netadmin-**********200-dashboard-query-group" user "netadmin"
    rule "license" create-time ********** id 1 query-string "%7B%22time%22%3A1698939358343%2C%22ignore%22%3Atrue%7D"
  exit
exit
no sms disable
ha link interface HA0
ha link ip ******* ***************
ha group 0
  priority 50
  monitor track "track-ha"
exit      
ha cluster 1

End

<XWHPD-NE4DIL-FW01>dis curr
#
 version 7.1.064, Release 9141P38
#
 sysname XWHPD-NE4DIL-FW01
#
 clock timezone BeiJing add 08:00:00
#
context Admin id 1
#
failover group 1
 bind chassis 1 slot 6 cpu 1 primary
 bind chassis 2 slot 6 cpu 1 secondary
#
ip vpn-instance management
 route-distinguisher 1000000000:1
 vpn-target 1000000000:1 import-extcommunity
 vpn-target 1000000000:1 export-extcommunity
#
 irf domain 53
 irf mac-address persistent always
 irf auto-update enable
 irf auto-merge enable
 undo irf link-delay
 irf member 1 priority 32
 irf member 2 priority 1
#
track 1 interface Route-Aggregation1 physical
#
track 2 interface Route-Aggregation2 physical
#
track 3 interface Route-Aggregation3 physical
#
track 4 interface Route-Aggregation4 physical
#
track 101 interface Blade1/6/0/1 physical
#
track 102 interface Blade1/6/0/2 physical
#
track 201 interface Blade2/6/0/1 physical
#
track 202 interface Blade2/6/0/2 physical
#
ospf 2000 router-id **********
 non-stop-routing
 area 0.0.0.0
  network ********** 0.0.0.0
  network ********** 0.0.0.0
  network ************ 0.0.0.0
  network ************ 0.0.0.0
#
 ip ttl-expires enable
#
 nat static-load-balance enable
#
 lldp global enable
#
 system-working-mode standard
 password-recovery enable
#
vlan 1
#
vlan 3553
#
irf-port 1/2
 port group interface Ten-GigabitEthernet1/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet1/9/1/12 mode enhanced
#
irf-port 2/1
 port group interface Ten-GigabitEthernet2/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet2/9/1/12 mode enhanced
#
object-group ip address ********/24
 security-zone MGMT_Inside
 0 network subnet ******** *************
#
object-group ip address **********-213
 security-zone MGMT_Inside
 0 network subnet ********** ***************
#
object-group ip address ***********/32
 security-zone MGMT_Inside
 0 network host address ***********
#
object-group ip address **********-162
 security-zone MGMT_Outside
 0 network range ********** **********
#
object-group ip address **********
 security-zone MGMT_Inside
 0 network host address **********
#
object-group ip address *********
 security-zone MGMT_Inside
 0 network host address *********
#
object-group ip address *************-Solarwinds
 security-zone MGMT_Inside
 0 network host address *************
#
object-group ip address ********/16
 security-zone MGMT_Inside
 0 network subnet ******** ***********
#
object-group ip address *********/24
 security-zone MGMT_Inside
 0 network subnet ********* *************
#
object-group ip address *************/24
 description *************
 security-zone MGMT_Inside
 0 network subnet ************* *************
#
object-group ip address *************
 security-zone MGMT_Inside
 0 network subnet ************* *************
#              
object-group ip address *************
 0 network host address *************
#
object-group ip address *********/24
 0 network subnet ********* *************
#
object-group ip address *********/24
 0 network subnet ********* *************
#
object-group ip address *********/16
 0 network subnet ********* ***********
#
object-group ip address *********/16
 security-zone MGMT_Inside
 0 network subnet ********* ***********
#
object-group ip address *********-8
 0 network range ********* *********
#
object-group ip address *********-*********
 0 network range ********* *********
#
object-group ip address **********/24
 0 network subnet ********** *************
#
object-group ip address *********/16
 security-zone MGMT_Inside
 0 network subnet ********* ***********
#
object-group ip address *********/16
 0 network subnet ********* ***********
#
object-group ip address ***********/24
 security-zone MGMT_Outside
 0 network subnet *********** *************
#
object-group ip address *************
 security-zone MGMT_Outside
 0 network host address *************
#
object-group ip address *************
 security-zone MGMT_Outside
 0 network host address *************
#
object-group ip address ***********/24
 security-zone MGMT_Outside
 0 network subnet *********** *************
#
object-group ip address ***********
 security-zone MGMT_Outside
 0 network host address ***********
#
object-group ip address ***********/24
 security-zone MGMT_Outside
 0 network subnet *********** *************
#
object-group ip address ***********
 security-zone MGMT_Outside
 0 network host address ***********
#
object-group ip address ************
 security-zone MGMT_Outside
 0 network host address ************
#
object-group ip address *********/16
 0 network subnet ********* ***********
#
object-group ip address ***********
 0 network subnet *********** *************
#
object-group ip address ************-************
 0 network range ************ ************
#
object-group ip address *************
 0 network host address *************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address 4A-*********
 security-zone MGMT_Outside
 0 network subnet ********* *************
#
object-group ip address 4A-*********/24
 security-zone MGMT_Inside
 0 network subnet ********* *************
#
object-group ip address Ansbile-***********1
 security-zone MGMT_Outside
 0 network host address ***********1
#
object-group ip address Ansbile-***********2
 0 network host address ***********2
#
object-group ip address Backup-Server-***********/32
 security-zone MGMT_Inside
 0 network host address ***********
#
object-group ip address BOCC-*********/24
 security-zone MGMT_Outside
 0 network subnet ********* *************
#
object-group ip address BOCC_********/24
 security-zone MGMT_Outside
 0 network subnet ******** *************
 100 network subnet ********* *************
#
object-group ip address CAS_F5_4.190.162.3
 0 network host address 4.190.162.3
#
object-group ip address CASGW_F5_4.190.162.4
 0 network host address 4.190.162.4
#
object-group ip address CSLC-*********
 security-zone MGMT_Outside
 0 network subnet ********* *************
#
object-group ip address CSLC-baoleiji-**********
 security-zone MGMT_Outside
 0 network subnet ********** *************
#
object-group ip address CSLC_NTP_4.9.0.1/32
 security-zone MGMT_Outside
 0 network host address 4.9.0.1
#
object-group ip address DES-*********7-18
 security-zone MGMT_Inside
 0 network range *********7 *********8
#
object-group ip address DNS-***************
 security-zone MGMT_Inside
 0 network host address ***************
#
object-group ip address DNS-*******
 security-zone MGMT_Inside
 0 network host address *******
#
object-group ip address ECC-*********/24
 security-zone MGMT_Outside
 0 network subnet ********* *************
 20 network subnet 3.30.11.0 *************
#
object-group ip address F5-AMS-4.190.162.10
 security-zone MGMT_Outside
 0 network host address 4.190.162.10
#
object-group ip address FOC-*********
 0 network host address *********
#
object-group ip address FOC-***********
 0 network host address ***********
#
object-group ip address FOC-*********36-137
 0 network subnet *********36 ***************
#
object-group ip address G2-ESXI-**********/16
 security-zone MGMT_Outside
 0 network subnet ********** ***********
#
object-group ip address G2-Solarwinds-*************/32
 0 network host address *************
#
object-group ip address G2_cbgw_18.1.13.0
 security-zone MGMT_Inside
 0 network subnet 18.1.13.0 *************
#
object-group ip address G2_NTP
 security-zone MGMT_Outside
 0 network host address **********
 10 network host address ***********
#
object-group ip address G2_TRANSROUTE_18.1.13.81-84
 security-zone MGMT_Inside
 0 network range 18.1.13.81 18.1.13.84
#
object-group ip address G2_webdc_18..1.21.0
 security-zone MGMT_Inside
 0 network subnet 18.1.21.0 *************
#
object-group ip address G3-GW-4.190.79.0/24
 security-zone MGMT_Outside
 0 network subnet 4.190.79.0 *************
#
object-group ip address G3-Server-ILO-***********/24
 security-zone MGMT_Inside
 0 network subnet *********** *************
#
object-group ip address G3-TEST-*********/24
 security-zone MGMT_Inside
 0 network subnet ********* *************
#
object-group ip address G3_CORE_4.190.83.1-2
 security-zone MGMT_Outside
 0 network range 4.190.83.1 4.190.83.2
#
object-group ip address G3_DMZ_MAIL_4.190.0.1/32
 0 network host address 4.190.0.1
#
object-group ip address G3_MS_AIDB_***********21/32
 0 network host address ***********21
#
object-group ip address G3_MS_AIDB_***********22/32
 security-zone MGMT_Inside
 0 network host address ***********22
#
object-group ip address G3_MS_AIDB_***********23/32
 0 network host address ***********23
#
object-group ip address G3_Network_MGT_*********/24
 security-zone MGMT_Inside
 0 network subnet ********* *************
#
object-group ip address G3ECCSYS01-***********91
 description G3ECCSYS01-***********91
 security-zone MGMT_Outside
 0 network host address ***********91
#
object-group ip address G3ECCSYS02-***********92
 description G3ECCSYS02-***********92
 security-zone MGMT_Outside
 0 network host address ***********92
#
object-group ip address G3NTP-************-252
 security-zone MGMT_Outside
 0 network range ************ 4.190.80.252
#
object-group ip address GS_MS_***********/22
 0 network subnet *********** *************
#
object-group ip address GW-F5_4.190.161.0
 security-zone MGMT_Outside
 0 network subnet 4.190.161.0 *************
#
object-group ip address JCVSC-***********01
 security-zone MGMT_Outside
 0 network host address ***********01
#
object-group ip address JiGuan_192.168.182.130
 security-zone MGMT_Inside
 0 network host address 192.168.182.130
#
object-group ip address linShi_18.0.5.21-26
 security-zone MGMT_Inside
 0 network range 18.0.5.21 18.0.5.26
#
object-group ip address LinShi_18.5.81.2
 security-zone MGMT_Inside
 0 network host address 18.5.81.2
#
object-group ip address LinShi_18.5.95.151
 security-zone MGMT_Inside
 0 network host address 18.5.95.151
#
object-group ip address LinShi_18.5.95.156
 security-zone MGMT_Inside
 0 network host address 18.5.95.156
#
object-group ip address LinShi_18.5.95.36
 security-zone MGMT_Inside
 0 network host address 18.5.95.36
#
object-group ip address LinShi_********
 security-zone MGMT_Inside
 0 network subnet ******** *************
#
object-group ip address LinShi_*********
 security-zone MGMT_Inside
 0 network host address *********
#
object-group ip address LinShi_RMXDB_**********
 security-zone MGMT_Inside
 0 network host address **********
#              
object-group ip address LinShi_RTQ_*********
 security-zone MGMT_Inside
 0 network host address *********
#
object-group ip address Mail_************
 security-zone MGMT_Inside
 0 network host address ************
#
object-group ip address MS-***********/22
 security-zone MGMT_Outside
 0 network subnet *********** *************
#
object-group ip address NET-NTP-*********1
 0 network host address *********1
#
object-group ip address Network_Mgt_**********/24
 0 network subnet ********** *************
#
object-group ip address NetworkOOB-*********/23
 0 network subnet ********* *************
 1 network subnet ********* *************
#
object-group ip address NG-NeiWang-************
 security-zone MGMT_Outside
 0 network host address ************
#
object-group ip address Radius-**********
 0 network host address **********
#
object-group ip address SFTP-**********00
 security-zone MGMT_Inside
 0 network host address **********00
#
object-group ip address Solarwinds-*************
 security-zone MGMT_Outside
 0 network host address *************
#
object-group ip address Solarwinds-**********
 security-zone MGMT_Inside
 0 network subnet ********** *************
#
object-group ip address SR_NET_*********/24
 security-zone MGMT_Inside
 0 network subnet ********* *************
#
object-group ip address Storage_MGMT_***********/24
 0 network subnet *********** *************
#
object-group ip address T1-**********
 security-zone MGMT_Inside
 0 network host address **********
#
object-group ip address T1-**********
 security-zone MGMT_Inside
 0 network host address **********
#
object-group ip address T1_********/16
 0 network subnet ******** ***********
#
object-group ip address T1_********/24
 0 network subnet ******** *************
#
object-group ip address T1_********
 0 network host address ********
#
object-group ip address T1_********01
 0 network host address ********01
#
object-group ip address T1_**********/32
 0 network host address **********
#
object-group ip address T1_**********/32
 0 network host address **********
#
object-group ip address T1_*********
 security-zone MGMT_Inside
 0 network host address *********
#
object-group ip address T1_**********
 0 network subnet ********** *************
#
object-group ip address T1_********/24
 0 network subnet ******** *************
 100 network subnet ********* *************
#
object-group ip address T1_************/24
 0 network subnet ************ *************
#
object-group ip address T1_harbor_**********
 0 network host address **********
#
object-group ip address T1Ceshi-**********
 security-zone MGMT_Inside
 0 network host address **********
#
object-group ip address TENANT-CORE-************-173
 security-zone MGMT_Outside
 0 network range ************ ************
#
object-group ip address TENANT02-CORE-***********-16
 security-zone MGMT_Outside
 0 network range *********** ***********
#
object-group ip address TENANT02-CORE-***********/32
 security-zone MGMT_Outside
 0 network host address ***********
#
object-group ip address TENANT02_CORE_************/32
 security-zone MGMT_Outside
 0 network host address ************
#
object-group ip address TENANT02_MS_************-32
 security-zone MGMT_Outside
 0 network range ************ ************
#              
object-group ip address TENANT02_MS_************/32
 0 network host address ************
#
object-group ip address TEST_*********/16
 0 network subnet ********* ***********
#
object-group ip address Test_**********/22
 0 network subnet ********** *************
#
object-group ip address tiaobanji-***********
 security-zone MGMT_Outside
 0 network host address ***********
#
object-group ip address V3_CORE_K8SNODE_**********/24
 security-zone MGMT_Outside
 0 network subnet ********** *************
#
object-group ip address V3_CORE_Tidb_**********/32
 security-zone MGMT_Outside
 0 network host address **********
#
object-group ip address V3_CORE_WCSDB_***********/32
 security-zone MGMT_Outside
 0 network host address ***********
#
object-group ip address V3_ESXI_***********/24
 0 network subnet *********** *************
#
object-group ip address V3_GW_K8SNODE_**********/24
 security-zone MGMT_Outside
 0 network subnet ********** *************
#
object-group ip address V3_MS_************/32
 0 network host address ************
#
object-group ip address V3_MS_************/32
 0 network host address ************
#
object-group ip address V3_MS_*************/32
 0 network host address *************
#
object-group ip address V3_MS_***********/32
 0 network host address ***********
#
object-group ip address V3_MS_Harbor_***********/32
 0 network host address ***********
#
object-group ip address V3_MS_K8SNODE_***********/24
 security-zone MGMT_Inside
 0 network subnet *********** *************
#
object-group ip address V3_VC_***********
 0 network host address ***********
#
object-group ip address V3MNYY_CORE_**********/24
 0 network subnet ********** *************
#
object-group ip address VulnerabilityScan-************
 security-zone MGMT_Outside
 0 network host address ************
#
object-group ip address W5R-*********/24
 security-zone MGMT_Inside
 0 network subnet ********* *************
#
object-group ip address W5R-*********/24
 0 network subnet ********* *************
#
object-group ip address W5RBOCC
 0 network subnet ********* *************
 10 network subnet 4.128.1.0 *************
#
object-group ip address YJ-TS-T1_192.168.214.0/24
 security-zone MGMT_Inside
 0 network subnet 192.168.214.0 *************
#
object-group ip address YJ-TS-T1_192.168.215.0/24
 security-zone MGMT_Inside
 0 network subnet 192.168.215.0 *************
#
object-group ip address yunying-**********
 security-zone MGMT_Inside
 0 network subnet ********** *************
#
object-group ip address yunying-************
 security-zone MGMT_Inside
 0 network host address ************
#
object-group ip address YUNYINGCLIENT_10.0.103.0
 0 network subnet 10.0.103.0 *************
#
object-group ip address YZ_BOCC
 0 network subnet 9.66.1.0 *************
 10 network subnet 9.66.2.0 *************
 20 network subnet 9.66.3.0 *************
#
object-group ip address YZBOCC
 0 network subnet 4.128.10.0 *************
#
object-group ip address YZECC-*********/24
 security-zone MGMT_Inside
 0 network subnet ********* *************
#
object-group ip address Zabbix_Prosy-*************
 security-zone MGMT_Outside
 0 network host address *************
#
object-group ip address Zabbix_Proxy-*************
#
object-group service snmp
#
object-group service TCP-10250
 0 service tcp destination eq 10250
#
object-group service TCP-10255
 0 service tcp destination eq 10255
#
object-group service TCP-161-162
 0 service tcp destination range 161 162
#
object-group service TCP-17778
 0 service tcp destination eq 17778
#
object-group service TCP-2198
 0 service tcp destination eq 2198
#
object-group service TCP-2379
 0 service tcp destination eq 2379
#
object-group service TCP-30400
 0 service tcp destination eq 30400
#
object-group service TCP-31050-31051
#
object-group service TCP-33445
 0 service tcp destination eq 33445
#
object-group service TCP-3601
 0 service tcp destination eq 3601
#
object-group service TCP-3900
 0 service tcp destination eq 3900
#
object-group service TCP-5003
 0 service tcp destination eq 5003
#
object-group service TCP-514
 0 service tcp destination eq 514
#
object-group service TCP-5988-5989
 0 service tcp destination range 5988 5989
#
object-group service TCP-7001
 0 service tcp destination eq 7001
#
object-group service TCP-8000
 0 service tcp destination eq 8000
#
object-group service TCP-8013
 0 service tcp destination eq 8013
#              
object-group service TCP-8018
 0 service tcp destination eq 8018
#
object-group service TCP-8022
 0 service tcp destination eq 8022
#
object-group service TCP-8080
 0 service tcp destination eq 8080
#
object-group service TCP-8085
 0 service tcp destination eq 8085
#
object-group service TCP-8088
 0 service tcp destination eq 8088
#
object-group service TCP-8090
 0 service tcp destination eq 8090
#
object-group service TCP-8182
 0 service tcp destination eq 8182
#
object-group service TCP-8208
 0 service tcp destination eq 8208
#
object-group service TCP-8400
 0 service tcp destination eq 8400
#
object-group service TCP-8400-8900
 0 service tcp destination range 8400 8900
#
object-group service TCP-8888
 0 service tcp destination eq 8888
#
object-group service TCP-8889
 0 service tcp destination eq 8889
#
object-group service TCP_28070
 0 service tcp destination eq 28070
#
object-group service TCP_28080
 0 service tcp destination eq 28080
#
object-group service TCP_28081
 0 service tcp destination eq 28081
#
object-group service TCP_3000
 0 service tcp destination eq 3000
#
object-group service TCP_31050-31051
 0 service tcp destination range 31050 31051
#
object-group service TCP_31306
 0 service tcp destination eq 31306
#
object-group service TCP_3191
 0 service tcp destination eq 3191
#
object-group service TCP_3389
 0 service tcp destination eq 3389
#
object-group service TCP_3555
 0 service tcp destination eq 3555
#
object-group service TCP_3556
 0 service tcp destination eq 3556
#
object-group service TCP_3558
 0 service tcp destination eq 3558
#              
object-group service TCP_389
 0 service tcp destination eq 389
#
object-group service TCP_5000
 0 service tcp destination eq 5000
#
object-group service TCP_5480
 0 service tcp destination eq 5480
#
object-group service TCP_6370
 0 service tcp destination eq 6370
#
object-group service TCP_8086
 0 service tcp destination eq 8086
#
object-group service TCP_8088
 0 service tcp destination eq 8088
#
object-group service TCP_8112
 0 service tcp destination eq 8112
#
object-group service TCP_8400-8450
 0 service tcp destination range 8400 8450
#
object-group service TCP_902
 0 service tcp destination eq 902
#
object-group service TCP_9443
 0 service tcp destination eq 9443
#
object-group service TCP_UDP161
 0 service tcp destination eq 161
 5 service udp destination eq 161
#
object-group service UDP-1812
 0 service udp destination eq 1812
#
object-group service UDP-514
 0 service udp destination eq 514
#
object-group service UDP-8182
 0 service tcp destination eq 8182
#
object-group service UDP_123
 0 service udp destination eq 123
#              
object-group service UDP_161
 0 service udp destination eq 161
#
object-group service UDP_162
 0 service udp destination eq 162
#
object-group service UDP_902
 0 service udp destination eq 902
#
interface Reth1
 member interface Route-Aggregation1 priority 100
 member interface Route-Aggregation2 priority 50
#
interface Reth1.3030
 ip address ************ ***************
 vlan-type dot1q vid 3030
#
interface Reth2
 description To-NE4ACL
 member interface Route-Aggregation3 priority 100
 member interface Route-Aggregation4 priority 50
#
interface Reth2.3029
 ip address ************ ***************
 vlan-type dot1q vid 3029
#
interface Route-Aggregation1
 description "TO-XWHPD-NE4ACL-SW01 RAGG1"
 link-aggregation mode dynamic
#
interface Route-Aggregation2
 description "TO-XWHPD-NE4ACL-SW01 RAGG1"
 link-aggregation mode dynamic
#
interface Route-Aggregation3
 description "TO-XWHPD-NE3DIL-SW01 RAGG1"
 link-aggregation mode dynamic
#
interface Route-Aggregation4
 description "TO-XWHPD-NE3DIL-SW01 RAGG1"
 link-aggregation mode dynamic
#
interface Route-Aggregation100
 description IRF-BFD_MAD
 mad bfd enable
 mad ip address ************* *************** member 1
 mad ip address ************* *************** member 2
#
interface NULL0
#
interface LoopBack1
 ip address ********** ***************
#
interface LoopBack2
 ip address ********** ***************
#
interface GigabitEthernet1/0/1/1
 port link-mode route
 description "TO-XWHPD-NE4DIL-FW01 G2/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/0/1/2
 port link-mode route
#
interface GigabitEthernet1/0/1/3
 port link-mode route
#
interface GigabitEthernet1/0/1/4
 port link-mode route
#
interface GigabitEthernet1/9/1/1
 port link-mode route
 description "TO-XWHPD-NE4DIL-FW01 G2/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/9/1/2
 port link-mode route
#
interface GigabitEthernet1/9/1/3
 port link-mode route
#
interface GigabitEthernet1/9/1/4
 port link-mode route
#
interface GigabitEthernet2/0/1/1
 port link-mode route
 description "TO-XWHPD-NE4DIL-FW01 G1/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/0/1/2
 port link-mode route
#              
interface GigabitEthernet2/0/1/3
 port link-mode route
#
interface GigabitEthernet2/0/1/4
 port link-mode route
#
interface GigabitEthernet2/9/1/1
 port link-mode route
 description "TO-XWHPD-NE4DIL-FW01 G1/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/9/1/2
 port link-mode route
#
interface GigabitEthernet2/9/1/3
 port link-mode route
#
interface GigabitEthernet2/9/1/4
 port link-mode route
#
interface M-GigabitEthernet1/0/0/0
 ip binding vpn-instance management
 ip address ********** *************
#
interface Ten-GigabitEthernet1/0/1/5
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T1/2/0/5"
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/6
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T1/2/0/6"
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/7
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T1/2/0/7"
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/8
 port link-mode route
#
interface Ten-GigabitEthernet1/0/1/9
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T1/0/1"
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/10
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T1/0/2"
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/11
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T1/0/3"
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/5
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T1/3/0/5"
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/6
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T1/3/0/6"
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/7
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T1/3/0/7"
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/8
 port link-mode route
#
interface Ten-GigabitEthernet1/9/1/9
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T1/0/5"
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/10
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T1/0/6"
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/11
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T1/0/7"
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/5
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T2/2/0/5"
 port link-aggregation group 4
#
interface Ten-GigabitEthernet2/0/1/6
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T2/2/0/6"
 port link-aggregation group 4
#
interface Ten-GigabitEthernet2/0/1/7
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T2/2/0/7"
 port link-aggregation group 4
#
interface Ten-GigabitEthernet2/0/1/8
 port link-mode route
#
interface Ten-GigabitEthernet2/0/1/9
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T2/0/1"
 port link-aggregation group 2
#
interface Ten-GigabitEthernet2/0/1/10
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T2/0/2"
 port link-aggregation group 2
#
interface Ten-GigabitEthernet2/0/1/11
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T2/0/3"
 port link-aggregation group 2
#
interface Ten-GigabitEthernet2/9/1/5
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T2/3/0/5"
 port link-aggregation group 4
#
interface Ten-GigabitEthernet2/9/1/6
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T2/3/0/6"
 port link-aggregation group 4
#
interface Ten-GigabitEthernet2/9/1/7
 port link-mode route
 description "TO-XWHPD-NE3DIL-SW01 T2/3/0/7"
 port link-aggregation group 4
#              
interface Ten-GigabitEthernet2/9/1/8
 port link-mode route
#
interface Ten-GigabitEthernet2/9/1/9
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T2/0/5"
 port link-aggregation group 2
#
interface Ten-GigabitEthernet2/9/1/10
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T2/0/6"
 port link-aggregation group 2
#
interface Ten-GigabitEthernet2/9/1/11
 port link-mode route
 description "TO-XWHPD-NE4ACL-SW01 T2/0/7"
 port link-aggregation group 2
#
interface Ten-GigabitEthernet1/0/1/12
 description "TO-XWHPD-NE4DIL-FW01 T2/0/1/12 IRF"
#
interface Ten-GigabitEthernet1/9/1/12
 description "TO-XWHPD-NE4DIL-FW01 T2/9/1/12 IRF"
#
interface Ten-GigabitEthernet2/0/1/12
 description "TO-XWHPD-NE4DIL-FW01 T1/0/1/12 IRF"
#
interface Ten-GigabitEthernet2/9/1/12
 description "TO-XWHPD-NE4DIL-FW01 T1/9/1/12 IRF"
#
interface Blade1/6/0/1
#
interface Blade1/6/0/2
#
interface Blade2/6/0/1
#
interface Blade2/6/0/2
#
interface Blade-Aggregation1
 link-aggregation blade Blade4fw
#
interface Blade-Aggregation257
#
security-zone name Local
#
security-zone name Trust
 import interface Route-Aggregation100
#
security-zone name DMZ
#
security-zone name Untrust
#
security-zone name Management
 import interface M-GigabitEthernet1/0/0/0
#
security-zone name MGMT_Inside
 import interface Reth1
 import interface Reth1.3030
#
security-zone name MGMT_Outside
 import interface Reth2
 import interface Reth2.3029
#
zone-pair security source Local destination Trust
 packet-filter 2000
#
zone-pair security source Trust destination Local
 packet-filter 2000
#              
 scheduler logfile size 16
#
line class console
 user-role network-admin
#
line class vty
 user-role network-operator
#
line con 1/0 1/1
 authentication-mode scheme
 user-role network-admin
#
line con 2/0 2/1
 user-role network-admin
#
line con 1/6
 authentication-mode scheme
 user-role network-admin
#
line con 2/6
 user-role network-admin
#
line vty 0 63  
 authentication-mode scheme
 user-role network-admin
#
 ip route-static vpn-instance management 0.0.0.0 0 ***********
#
 info-center timestamp loghost iso
 info-center loghost source M-GigabitEthernet1/0/0/0
 info-center loghost vpn-instance MGMT ***********
 info-center loghost vpn-instance management **********
 info-center loghost vpn-instance MGMT **********3
 info-center loghost vpn-instance MGMT ************
 info-center loghost vpn-instance MGMT ************
 info-center loghost vpn-instance MGMT *************
#
 mad exclude interface M-GigabitEthernet1/0/0/0
#
 snmp-agent
 snmp-agent local-engineid 800063A280542BDE641FDB00000001
 snmp-agent community read cipher $c$3$ebe+6nMz0i+FIpGSTVqK/Ho4SsXR6+0w5YPxs/Bp acl name ACL-SNMP
 snmp-agent sys-info location fw01-w5r-D-11-3-20&D-12-3-20
 snmp-agent sys-info version v2c v3 
 snmp-agent target-host trap address udp-domain ************ vpn-instance MGMT params securityname cslc_snmp v2c
 snmp-agent target-host trap address udp-domain ************* vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain *********** params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain ********** vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent trap enable arp 
 snmp-agent trap enable radius 
 snmp-agent trap enable stp 
 snmp-agent trap enable syslog 
 snmp-agent trap source M-GigabitEthernet1/0/0/0
#
 ssh server enable
#
redundancy group 1
 member interface Reth1
 member interface Reth2
 member failover group 1
 node 1
  bind chassis 1
  priority 100
  track 1 interface Route-Aggregation1
  track 3 interface Route-Aggregation3
  track 101 interface Blade1/6/0/1
  track 102 interface Blade1/6/0/2
 node 2
  bind chassis 2
  priority 50
  track 2 interface Route-Aggregation2
  track 4 interface Route-Aggregation4
  track 201 interface Blade2/6/0/1
  track 202 interface Blade2/6/0/2
#
 ntp-service enable
 ntp-service unicast-server *********1 vpn-instance management source M-GigabitEthernet1/0/0/0
#
acl basic 2000
 rule 0 permit source *********** *********
#
acl basic name ACL-SNMP
 description "Network monitor system"
 rule 0 permit vpn-instance management source ********** *********
 rule 5 permit vpn-instance management source ************* 0
 rule 10 permit vpn-instance management source *********** *********
 rule 15 permit vpn-instance management source *********** 0
 rule 20 permit vpn-instance MGMT source ************ 0
 rule 1000 deny vpn-instance management
#
domain system
#              
 domain default enable system
#
role name level-0
 description Predefined level-0 role
#
role name level-1
 description Predefined level-1 role
#
role name level-2
 description Predefined level-2 role
#
role name level-3
 description Predefined level-3 role
#
role name level-4
 description Predefined level-4 role
#
role name level-5
 description Predefined level-5 role
#
role name level-6
 description Predefined level-6 role
#              
role name level-7
 description Predefined level-7 role
#
role name level-8
 description Predefined level-8 role
#
role name level-9
 description Predefined level-9 role
#
role name level-10
 description Predefined level-10 role
#
role name level-11
 description Predefined level-11 role
#
role name level-12
 description Predefined level-12 role
#
role name level-13
 description Predefined level-13 role
#
role name level-14
 description Predefined level-14 role
#
role name systemadmin
 rule 1 permit read web-menu m_monitor/m_atklog/m_blacklistlog
 rule 2 permit read web-menu m_monitor/m_atklog/m_singleatk
 rule 3 permit read web-menu m_monitor/m_atklog/m_scanatk
 rule 4 permit read web-menu m_monitor/m_atklog/m_floodatk
 rule 5 permit read web-menu m_monitor/m_atklog/m_threatlog
 rule 6 permit read web-menu m_monitor/m_atklog/m_urllog
 rule 7 permit read web-menu m_monitor/m_atklog/m_filefilterlog
 rule 8 permit read web-menu m_monitor/m_atklog/m_zonepairlog
 rule 9 permit read web-menu m_monitor/m_auditlogs/m_auditimchatlog
 rule 10 permit read web-menu m_monitor/m_auditlogs/m_auditcommunitylog
 rule 11 permit read web-menu m_monitor/m_auditlogs/m_auditsearchenginelog
 rule 12 permit read web-menu m_monitor/m_auditlogs/m_auditmaillog
 rule 13 permit read web-menu m_monitor/m_auditlogs/m_auditfiletransferlog
 rule 14 permit read web-menu m_monitor/m_auditlogs/m_auditrelaxstocklog
 rule 15 permit read web-menu m_monitor/m_auditlogs/m_auditotherapplog
 rule 16 permit read web-menu m_monitor/m_monitorlog/m_trafficlog
 rule 17 permit read web-menu m_monitor/m_rank/m_trafficrank
 rule 18 permit read web-menu m_monitor/m_rank/m_threadrank
 rule 19 permit read web-menu m_monitor/m_rank/m_urlfilterrank
 rule 20 permit read web-menu m_monitor/m_rank/m_ffilterrank
 rule 21 permit read web-menu m_monitor/m_rank/m_securityaudit
 rule 22 permit read web-menu m_monitor/m_rank/m_lb_serverreport
 rule 23 permit read web-menu m_monitor/m_rank/m_lb_linkreport
 rule 24 permit read web-menu m_monitor/m_rank/m_lb_dnsproxyreport
 rule 25 permit read web-menu m_monitor/m_trend/m_traffictrend
 rule 26 permit read web-menu m_monitor/m_trend/m_threadtrend
 rule 27 permit read web-menu m_monitor/m_trend/m_urlfiltertrend
 rule 28 permit read web-menu m_monitor/m_trend/m_ffiltertrend
 rule 29 permit read web-menu m_monitor/m_trend/m_lb_urltrend
 rule 30 permit read web-menu m_monitor/m_report
 rule 31 permit read web-menu m_monitor/m_session
 rule 32 permit read web-menu m_monitor/m_lb_dnscaches
 rule 33 permit read web-menu m_monitor/m_userinfocenter
 rule 34 permit read web-menu m_policy/m_firewall/m_secpolicy
 rule 35 permit read web-menu m_policy/m_firewall/m_redundancyrules
 rule 36 permit read web-menu m_policy/m_firewall/m_targetpolicy
 rule 37 permit read web-menu m_policy/m_attackdefense/m_atkpolicy
 rule 38 permit read web-menu m_policy/m_attackdefense/m_clientverifyprotectip
 rule 39 permit read web-menu m_policy/m_attackdefense/m_blacklistmanual
 rule 40 permit read web-menu m_policy/m_attackdefense/m_whitelistmanual
 rule 41 permit read web-menu m_policy/m_attackdefense/m_clientverifyzone
 rule 42 permit read web-menu m_policy/m_attackdefense/m_connlimitpolicies
 rule 43 permit read web-menu m_policy/m_attackdefense/m_urpf
 rule 44 permit read web-menu m_policy/m_nat/m_natoutboundconfig
 rule 45 permit read web-menu m_policy/m_nat/m_natserverconfig
 rule 46 permit read web-menu m_policy/m_nat/m_natstaticchange
 rule 47 permit read web-menu m_policy/m_nat/m_natoutbound444config
 rule 48 permit read web-menu m_policy/m_nat/m_natoutboundstatic444config
 rule 49 permit read web-menu m_policy/m_nat/m_natsettings
 rule 50 permit read web-menu m_policy/m_aft/m_aftaddrgrp
 rule 51 permit read web-menu m_policy/m_aft/m_aftnat64
 rule 52 permit read web-menu m_policy/m_aft/m_aftoutbound
 rule 53 permit read web-menu m_policy/m_aft/m_aftset
 rule 54 permit read web-menu m_policy/m_appaudit/m_auditpolicy
 rule 55 permit read web-menu m_policy/m_appaudit/m_keywordgroups
 rule 56 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthpolicy
 rule 57 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthchannel
 rule 58 permit read web-menu m_policy/m_bandwidthmanagement/m_interfacebandwidth
 rule 59 permit read web-menu m_policy/m_loadbalance/m_lb_globalconfig
 rule 60 permit read web-menu m_policy/m_loadbalance/m_lb_server
 rule 61 permit read web-menu m_policy/m_loadbalance/m_lb_link
 rule 62 permit read web-menu m_policy/m_netshare/m_netsharepolicy
 rule 63 permit read web-menu m_policy/m_netshare/m_netsharestatus
 rule 64 permit read web-menu m_policy/m_proxymanagement/m_proxypolicy
 rule 65 permit read web-menu m_policy/m_proxymanagement/m_whitelisthostname
 rule 66 permit read web-menu m_policy/m_proxymanagement/m_sslcertificate
 rule 67 permit read web-menu m_resource/m_healthmonitor
 rule 68 permit read web-menu m_resource/m_user/m_usercontrol
 rule 69 permit read web-menu m_resource/m_user/m_authentication
 rule 70 permit read web-menu m_resource/m_user/m_access
 rule 71 permit read web-menu m_resource/m_dpi/m_ipscfg
 rule 72 permit read web-menu m_resource/m_dpi/m_antiviruscfg
 rule 73 permit read web-menu m_resource/m_dpi/m_dfltcfg
 rule 74 permit read web-menu m_resource/m_dpi/m_ufltcfg
 rule 75 permit read web-menu m_resource/m_dpi/m_ffltcfg
 rule 76 permit read web-menu m_resource/m_dpi/m_apprecognition
 rule 77 permit read web-menu m_resource/m_dpi/m_securityaction
 rule 78 permit read web-menu m_resource/m_dpi/m_dpicfg
 rule 79 permit read web-menu m_resource/m_objectgroup/m_ipv4objectgroup
 rule 80 permit read web-menu m_resource/m_objectgroup/m_ipv6objectgroup
 rule 81 permit read web-menu m_resource/m_objectgroup/m_macobjectgroup
 rule 82 permit read web-menu m_resource/m_objectgroup/m_serviceobjectgroup
 rule 83 permit read web-menu m_resource/m_objectgroup/m_timerange
 rule 84 permit read web-menu m_resource/m_acl/m_ipv4acl
 rule 85 permit read web-menu m_resource/m_acl/m_ipv6acl
 rule 86 permit read web-menu m_resource/m_acl/m_macacl
 rule 87 permit read web-menu m_resource/m_ssl/m_sslserver
 rule 88 permit read web-menu m_resource/m_ssl/m_sslclient
 rule 89 permit read web-menu m_resource/m_ssl/m_ssladvancesettiing
 rule 90 permit read web-menu m_resource/m_publickey/m_publickeylocal
 rule 91 permit read web-menu m_resource/m_publickey/m_publickeypeer
 rule 92 permit read web-menu m_resource/m_pki_cert/m_pki
 rule 93 permit read web-menu m_resource/m_pki_cert/m_certificatepolicy
 rule 94 permit read web-menu m_resource/m_pki_cert/m_certificatesubject
 rule 95 permit read web-menu m_network/m_vrf
 rule 96 permit read web-menu m_network/m_if/m_interface
 rule 97 permit read web-menu m_network/m_if/m_inlineall
 rule 98 permit read web-menu m_network/m_if/m_lagg
 rule 99 permit read web-menu m_network/m_seczone
 rule 100 permit read web-menu m_network/m_link/m_vlan
 rule 101 permit read web-menu m_network/m_link/m_mac_sum
 rule 102 permit read web-menu m_network/m_dns_sum/m_dnshosts
 rule 103 permit read web-menu m_network/m_dns_sum/m_dns
 rule 104 permit read web-menu m_network/m_dns_sum/m_ddns
 rule 105 permit read web-menu m_network/m_dns_sum/m_dnsadvance
 rule 106 permit read web-menu m_network/m_ip_net/m_ip
 rule 107 permit read web-menu m_network/m_ip_net/m_arp
 rule 108 permit read web-menu m_network/m_ipv6_net/m_ipv6
 rule 109 permit read web-menu m_network/m_ipv6_net/m_nd
 rule 110 permit read web-menu m_network/m_vpn/m_gre
 rule 111 permit read web-menu m_network/m_vpn/m_ipsec
 rule 112 permit read web-menu m_network/m_vpn/m_advpn
 rule 113 permit read web-menu m_network/m_vpn/m_l2tp
 rule 114 permit read web-menu m_network/m_sslvpn/m_sslvpn_context
 rule 115 permit read web-menu m_network/m_sslvpn/m_sslvpn_gateway
 rule 116 permit read web-menu m_network/m_sslvpn/m_sslvpn_ipv4addrpool
 rule 117 permit read web-menu m_network/m_sslvpn/m_sslvpn_snatpool
 rule 118 permit read web-menu m_network/m_sslvpn/m_sslvpn_acif
 rule 119 permit read web-menu m_network/m_sslvpn/m_sslvpn_globalconfig
 rule 120 permit read web-menu m_network/m_sslvpn/m_sslvpn_tempmanagement
 rule 121 permit read web-menu m_network/m_sslvpn/m_sslvpn_statistics
 rule 122 permit read web-menu m_network/m_routing/m_routingtable
 rule 123 permit read web-menu m_network/m_routing/m_staticrouting
 rule 124 permit read web-menu m_network/m_routing/m_policyrouting
 rule 125 permit read web-menu m_network/m_routing/m_ospf
 rule 126 permit read web-menu m_network/m_routing/m_bgp
 rule 127 permit read web-menu m_network/m_routing/m_rip
 rule 128 permit read web-menu m_network/m_multicast/m_multicastrouting
 rule 129 permit read web-menu m_network/m_multicast/m_pim
 rule 130 permit read web-menu m_network/m_multicast/m_igmp
 rule 131 permit read web-menu m_network/m_dhcp/m_dhcpservice
 rule 132 permit read web-menu m_network/m_dhcp/m_dhcppool
 rule 133 permit read web-menu m_network/m_ipservice/m_ssh
 rule 134 permit read web-menu m_network/m_ipservice/m_ntp
 rule 135 permit read web-menu m_network/m_ipservice/m_ftp
 rule 136 permit read web-menu m_network/m_ipservice/m_telnet
 rule 137 permit read web-menu m_device/m_diagnosis/m_ping
 rule 138 permit read web-menu m_device/m_diagnosis/m_tracert
#
user-group system
#
local-user admin class manage
 password hash $h$6$2ARU60ipfILr44hR$v1M9D8yC+Jw18TvXvs4pj0h3ucejU1cZAW0nmhuAiK/IqeYar79IVVZsbdA5O3DXN79y/nP/J0H3kRZAqea2pg==
 service-type ssh terminal https
 authorization-attribute user-role level-3
 authorization-attribute user-role network-admin
 authorization-attribute user-role network-operator
#
local-user ftp class manage
 password hash $h$6$yXwASHYM2cyQBvVL$9t4HVVqKgSVhmRTbhyuUm4Wm7JtnB32YxYBSZuXPJVPKMsIDoW4MEhB3LI3zC7VPATEAPsrTFZa5+pK7DmVFrg==
 service-type ftp
 authorization-attribute user-role network-admin
#
local-user operator class manage
 password hash $h$6$z1ddmepcwOWRo8kH$JTWZ6AGGMEff4O3djRDDbDoqCZu0dkXfpsTArfzLN1YK/4XZE5jN3BwKBDSk+kSJxVKLwfP2rW4IR1qNzdmxNQ==
 service-type ssh terminal https
 authorization-attribute user-role level-1
 authorization-attribute user-role systemadmin
#              
 ftp server enable
#
 ip https enable
#
 inspect optimization no-acsignature disable
 inspect optimization raw disable
 inspect optimization uncompress disable
 inspect optimization url-normalization disable
 inspect optimization chunk disable
#
security-policy ip
 rule 116 name YZBOCC_Deny
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip YZBOCC
  source-ip ECC-*********/24
  service TCP_31306
  service TCP_3558
  service ssh
  service TCP_3555
  service TCP_3389
 rule 117 name YZBOCC_Deny_HTTPS
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip YZBOCC
  source-ip ECC-*********/24
  destination-ip SR_NET_*********/24
  destination-ip G3_Network_MGT_*********/24
  service https
  service http
 rule 107 name Deny-Radius
  disable
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip G3-TEST-*********/24
  destination-ip Radius-**********
  service UDP-1812
  service ping
 rule 26 name JiGuan_to_V3
  action pass
  counting enable
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip JiGuan_192.168.182.130
  destination-ip V3_MS_************/32
  destination-ip V3_MS_************/32
  service http
  service https
 rule 66 name Solarwinds_to_G3
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip TEST_*********/16
  destination-ip Solarwinds-*************
  service syslog
 rule 136 name TO-ECC
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip-host *********** 
  destination-ip TEST_*********/16
  service https
  service ssh
  service http
 rule 126 name SNMP-*********/24
  action pass
  source-zone MGMT_Outside
  source-zone MGMT_Inside
  destination-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip *********/24
  source-ip *********/16
  source-ip ************
  destination-ip *********-8
  destination-ip *********/16
  destination-ip ************
  service TCP_UDP161
  service UDP_161
  service UDP_162
  service UDP-514
 rule 63 name G3-Solarwinds
  action pass
  logging enable
  counting enable
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip Solarwinds-*************
  destination-ip TEST_*********/16
  service snmp-request
  service snmp-trap
  service TCP-161-162
  service ssh  
 rule 64 name BOCC_G3
  action pass
  logging enable
  counting enable
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip 4A-*********
  source-ip BOCC_********/24
  source-ip ECC-*********/24
  source-ip BOCC-*********/24
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip TEST_*********/16
  destination-ip Backup-Server-***********/32
  service ssh
  service http
  service https
  service TCP_3389
  service TCP-8080
  service TCP-3601
  service TCP-8090
 rule 62 name Zabbix_Proxy_to_DES
  action pass  
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip Zabbix_Prosy-*************
  destination-ip DES-*********7-18
  service TCP-8018
 rule 30 name Storage_Monitor
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip GS_MS_***********/22
  source-ip BOCC_********/24
  source-ip BOCC-*********/24
  source-ip ECC-*********/24
  source-ip *********/16
  source-ip *************
  destination-ip Storage_MGMT_***********/24
  destination-ip T1_********/16
  service TCP-8088
  service ssh
  service https
  service http
  service telnet
  service TCP-2198
  service TCP-8208
 rule 150 name Storage-MGT
  action pass
  source-zone MGMT_Inside
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Storage_MGMT_***********/24
  source-ip *********/24
  destination-ip Storage_MGMT_***********/24
  destination-ip *********/24
  service https
 rule 29 name Storage-Monitor
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Storage_MGMT_***********/24
  source-ip T1_********/16
  destination-ip GS_MS_***********/22
  destination-ip BOCC-*********/24
  destination-ip BOCC_********/24
  destination-ip ECC-*********/24
  service TCP-8088
  service ssh
  service http
  service https
  service telnet
 rule 60 name G2_TRANSROUTE_V3_30400
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip G2_TRANSROUTE_18.1.13.81-84
  destination-ip *********/16
  service TCP-30400
  service ssh
 rule 59 name V3_Network_Log
  action pass
  logging enable
  counting enable
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip TEST_*********/16
  destination-ip MS-***********/22
  destination-ip ***********/24
  service syslog
  service TCP-514
  service UDP-514
 rule 53 name V3_MGMT
  action pass
  counting enable
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip *************
  source-ip *************
  destination-ip TEST_*********/16
  destination-ip *********/16
 rule 8 name 1
  action pass
  counting enable
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip T1_********/24
  source-ip T1_**********/32
  source-ip YZECC-*********/24
  source-ip W5R-*********/24
  source-ip 4A-*********/24
  source-ip YUNYINGCLIENT_10.0.103.0
  source-ip YJ-TS-T1_192.168.214.0/24
  source-ip YJ-TS-T1_192.168.215.0/24
  source-ip yunying-**********
  destination-ip *********/16
  destination-ip *********/16
 rule 10 name T1_To_V3_VC_***********
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip T1_********/16
  destination-ip V3_VC_***********
  service TCP_5480
  service http
  service https
  service ssh
  service TCP_9443
 rule 11 name "V3_VC To V3_ESXI"
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip MS-***********/22
  destination-ip V3_ESXI_***********/24
  service UDP_902
  service TCP_902
  service https
  service http
 rule 13 name 3
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip V3_ESXI_***********/24
  destination-ip MS-***********/22
  service TCP_902
  service UDP_902
  service http
  service https
 rule 14 name 4
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip T1_********/16
  destination-ip GS_MS_***********/22
  service TCP_5000
  service ssh
  service http
  service TCP_3000
 rule 18 name "Luyu_Test CunChu_MGMT To V3_MS"
  action pass  
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Storage_MGMT_***********/24
  destination-ip V3_MS_***********/32
  service https
  service ssh
 rule 19 name "Luyu_Test_V3_MS To Storage"
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip V3_MS_***********/32
  destination-ip Storage_MGMT_***********/24
  service https
  service ssh
 rule 21 name "OSPF ICMP SNMP"
  action pass
  logging enable
  counting enable
  service ospf
  service ping
  service snmp-request
  service snmp-trap
  service syslog
  service ntp
 rule 22 name 5
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip Test_**********/22
  source-ip CAS_F5_4.190.162.3
  source-ip CASGW_F5_4.190.162.4
  destination-ip T1_********
  service TCP_389
 rule 35 name Backserer_to_V3_VC
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Backup-Server-***********/32
  destination-ip V3_VC_***********
  service TCP_902
  service https
 rule 34 name V3_VC_to_Backserver
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip V3_VC_***********
  destination-ip Backup-Server-***********/32
  service TCP_902
  service https
 rule 67 name BOCC_To_G3-Server-ILO
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip BOCC_********/24
  destination-ip G3-Server-ILO-***********/24
  service https
  service TCP-3900
  service TCP-2198
  service TCP-8208
 rule 57 name R2201_to_
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip yunying-************
  source-ip yunying-**********
  destination-ip *********/16
 rule 56 name V3_to_Mail
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip *********/16
  destination-ip Mail_************
  destination-ip DNS-*******
  destination-ip DNS-***************
  service pop3
  service smtp
  service dns-tcp
  service dns-udp
 rule 25 name V3_to_JiGuan
  action pass
  counting enable
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip V3_MS_************/32
  source-ip V3_MS_************/32
  destination-ip JiGuan_192.168.182.130
  service http
  service https
 rule 24 name _to_**********
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip tiaobanji-***********
  destination-ip T1Ceshi-**********
  service http
 rule 4 name _to_T1_AD
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip V3MNYY_CORE_**********/24
  destination-ip T1_********
 rule 1 name _to_T1_harbor
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip V3MNYY_CORE_**********/24
  destination-ip T1_harbor_**********
  destination-ip T1_********/24
  destination-ip ***********/32
  service http
  service ntp
 rule 72 name Backup_Server_***********
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip TENANT02_CORE_************/32
  source-ip TENANT02_MS_************/32
  source-ip TENANT02-CORE-***********/32
  source-ip TENANT02-CORE-***********-16
  source-ip TENANT-CORE-************-173
  source-ip TENANT02_MS_************-32
  destination-ip Backup-Server-***********/32
  service ssh
  service TCP-8400-8900
 rule 73 name Backup_Server_To_TENANT02
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Backup-Server-***********/32
  destination-ip TENANT02_CORE_************/32
  destination-ip TENANT02_MS_************/32
  destination-ip TENANT-CORE-************-173
  destination-ip TENANT02-CORE-***********-16
  destination-ip TENANT02-CORE-***********/32
  destination-ip TENANT02_MS_************-32
  service ssh
  service TCP-8400-8900
 rule 77 name G3_MS_To_Backup-Server
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip ***********/24
  source-ip G3_CORE_4.190.83.1-2
  destination-ip Backup-Server-***********/32
  service https
  service http
 rule 76 name G3-V1.3-20200429-01
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip *********/16
  destination-ip G2_NTP
  service ntp
  service UDP_123
 rule 83 name G3_OPERHOST_INSIDEZONE4.176
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip G3_CORE_4.190.83.1-2
  destination-ip *********/16
  service ssh  
  service http
  service TCP_6370
  service TCP-5003
  service TCP_3555
  service TCP_3558
  service TCP_3191
  service TCP-7001
  service TCP-2379
  service TCP-8080
 rule 78 name G3-V1.3-20200429-02
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip BOCC_********/24
  destination-ip *********/16
  service https
 rule 79 name CSLC-baoleiji
  action pass
  logging enable
  counting enable
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip CSLC-baoleiji-**********
  source-ip CSLC-*********
  source-ip 4A-*********
  source-ip BOCC_********/24
  source-ip *********/16
  source-ip ECC-*********/24
  destination-ip *********/16
  destination-ip *********/16
  destination-ip *********/16
  destination-ip *********/16
  service ssh
  service http
  service https
  service TCP_3389
  service TCP-8888
  service TCP-8889
  service TCP-8013
  service TCP-8090
  service TCP-8000
  service TCP_3555
  service TCP_3558
  service TCP_31306
  service TCP-3900
  service TCP-8208
  service TCP-2198
  service TCP-8088
  service TCP-8080
 rule 80 name anquanlousao
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip FOC-*********
  source-ip FOC-***********
 rule 81 name H3C_Shengji_20201001
  action pass
  counting enable
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip G3_Network_MGT_*********/24
  destination-ip BOCC_********/24
 rule 82 name H3C-Shengji-20201001_2
  action pass
  counting enable
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip BOCC_********/24
  destination-ip G3_Network_MGT_*********/24
 rule 84 name VMqianyi
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip V3_ESXI_***********/24
  destination-ip G2-ESXI-**********/16
 rule 85 name VMqianyi02
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip G2-ESXI-**********/16
  destination-ip V3_ESXI_***********/24
 rule 86 name G3_AIDB_To_Mail
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip G3_MS_AIDB_***********22/32
  source-ip G3_MS_AIDB_***********21/32
  source-ip G3_MS_AIDB_***********23/32
  source-ip Solarwinds-**********
  destination-ip G3_DMZ_MAIL_4.190.0.1/32
  service smtp
 rule 87 name G3_To_CSLC_NTP
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip *********/16
  source-ip *********/16
  source-ip *********/16
  destination-ip CSLC_NTP_4.9.0.1/32
  service ntp
 rule 88 name NTP
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip *********/16
  destination-ip G3NTP-************-252
  service ntp
 rule 89 name SSM-Ansbile
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip Ansbile-***********1
  destination-ip *********/16
  destination-ip *********/16
  destination-ip *********/16
  service ssh
 rule 90 name ECC02
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip G3ECCSYS01-***********91
  source-ip G3ECCSYS02-***********92
  destination-ip Storage_MGMT_***********/24
  destination-ip *********/16
  service snmp-request
  service snmp-trap
 rule 91 name Solarwinds-SSH
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Solarwinds-**********
  destination-ip *********/16
  destination-ip TEST_*********/16
  service ssh
  service http
  service https
  service TCP-8088
  service TCP-5988-5989
 rule 92 name JCVSC-TO-Solarwinds
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip JCVSC-***********01
  source-ip GS_MS_***********/22
  destination-ip Solarwinds-**********
  service TCP-17778
 rule 93 name SCAN
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip FOC-*********36-137
 rule 94 name TO-SFTP
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip *********/16
  destination-ip SFTP-**********00
  service ssh
 rule 95 name WangZha
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip SFTP-**********00
  destination-ip NG-NeiWang-************
  service TCP-33445
 rule 96 name BACK-NAS
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Backup-Server-***********/32
  destination-ip *********/16
 rule 97 name XWHBOCC-NTP
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip **********/24
  destination-ip NET-NTP-*********1
  service ntp
 rule 98 name XWHBOCC-Solarwinds
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Solarwinds-**********
  destination-ip **********/24
  service snmp
 rule 99 name XWHBOCC-syslog
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip **********/24
  destination-ip Solarwinds-**********
  service syslog
 rule 100 name XWHBOCC_G3
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip W5RBOCC
  source-ip YZ_BOCC
  destination-ip TEST_*********/16
  destination-ip Backup-Server-***********/32
  service ssh
  service http
  service https
  service TCP_3389
  service TCP-8080
  service TCP-3601
  service TCP-8090
 rule 101 name XWHBOCC_G3Storage_Monitor
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip W5RBOCC
  source-ip YZ_BOCC
  destination-ip Storage_MGMT_***********/24
  service TCP-8088
  service ssh
  service https
  service http
  service telnet
  service TCP-2198
  service TCP-8208
 rule 102 name XWHBOCC_G3Storage-Monitor
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Storage_MGMT_***********/24
  source-ip YZ_BOCC
  destination-ip W5RBOCC
  service TCP-8088
  service ssh  
  service http
  service https
  service telnet
 rule 103 name Zabbix_for_Windows01
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Network_Mgt_**********/24
  source-ip Backup-Server-***********/32
  source-ip W5R-*********/24
  destination-ip Zabbix_Prosy-*************
  service TCP-31050-31051
  service TCP_31050-31051
 rule 104 name Zabbix_for_Windows02
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip Zabbix_Prosy-*************
  destination-ip Network_Mgt_**********/24
  destination-ip Backup-Server-***********/32
  destination-ip W5R-*********/24
  service TCP_31050-31051
 rule 106 name Radius
  action pass
  counting enable
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip G3-TEST-*********/24
  destination-ip Radius-**********
  service UDP-1812
 rule 108 name Ansbile
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip Ansbile-***********2
  destination-ip *********/16
  service ssh
 rule 109 name YZBOCC-NTP
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip YZBOCC
  destination-ip NET-NTP-*********1
  service ntp
 rule 110 name YZBOCC_G3
  action pass  
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip YZBOCC
  destination-ip TEST_*********/16
  destination-ip Backup-Server-***********/32
  service ssh
  service http
  service https
  service TCP_3389
  service TCP-8080
  service TCP-3601
  service TCP-8090
 rule 111 name YZBOCCG3Storage_Monitor
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip YZBOCC
  destination-ip Storage_MGMT_***********/24
  service TCP-8088
  service ssh
  service https
  service http
  service telnet
  service TCP-2198
  service TCP-8208
 rule 112 name YZBOCC_G3Storage-Monitor
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Storage_MGMT_***********/24
  destination-ip YZBOCC
  service TCP-8088
  service ssh
  service http
  service https
  service telnet
 rule 114 name YZBOCC_G3Storage_Monitor
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip YZBOCC
  destination-ip Storage_MGMT_***********/24
  service TCP-8088
  service ssh
  service https
  service http 
  service telnet
  service TCP-2198
  service TCP-8208
 rule 115 name YZBOCC_G3storage-Monitor01
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip Storage_MGMT_***********/24
  destination-ip YZBOCC
  service TCP-8088
  service ssh
  service http
  service https
  service telnet
 rule 113 name YZBOCC_G301
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip YZBOCC
  destination-ip TEST_*********/16
  destination-ip Backup-Server-***********/32
  service ssh
  service http 
  service https
  service TCP_3389
  service TCP-8080
  service TCP-3601
  service TCP-8090
 rule 118 name VulnerabilityScan_Network
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip VulnerabilityScan-************
  destination-ip NetworkOOB-*********/23
  destination-ip *********/16
 rule 119 name monitor_prove
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip ************-************
  destination-ip *********-*********
  service https
  service ssh
 rule 120 name H3Cxunjian
  action pass
  source-zone MGMT_Outside
  destination-zone MGMT_Inside
  source-ip *************
  destination-ip TEST_*********/16
  service ssh
  service snmp-request
  service ftp
 rule 121 name H3Cxunjian2
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip TEST_*********/16
  destination-ip *************
  service ftp
 rule 125 name *********_************
  action pass
  source-zone MGMT_Inside
  destination-zone MGMT_Outside
  source-ip *********/16
  destination-ip ************
  service UDP_161
  service UDP_162
  service UDP-514
 rule 23 name deny
  logging enable
  counting enable
#
return

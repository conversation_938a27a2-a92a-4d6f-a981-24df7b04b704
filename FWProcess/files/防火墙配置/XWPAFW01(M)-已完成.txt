XWPAFW01(M)# sh config
XWPAFW01(M)# sh configuration ?
  <cr>                  
  |                     Output modifiers
  address               Address configuration
  auto-backup           Backup configurations
  backup                Backup configuration
  get-config-policy     Policy configuration
  get-config-policy-id  Policy configuration
  interface             Interface configuration
  policy                Policy configuration
  policy-global         Policy global configuration
  record                Record of configuration
  running               Running configuration
  startup               Startup configuration
  vrouter               Vrouter configuration
  xml                   Running configuration in xml
XWPAFW01(M)# sh configuration 

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit      
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
zone "MG"
exit
interface vswitchif1
exit
interface MGT0 local
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit      
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit
interface aggregate1
exit
interface aggregate2
exit      
address "private_network"
exit
address "monitor_address"
exit
address "Group_10.x.0.0"
exit
address "Group_9.x.0.0"
exit
address "GTM"
exit
address "net_**********"
exit
address "终端强管制"
exit
address "终端强管制应用"
exit
address "**********-59"
exit
address "***********-203"
exit
address "********-9"
exit
address "*********"
exit      
address "*********"
exit
address "UMP_Group"
exit
address "省市FTP更新服务器"
exit
address "host_************"
exit
address "host_**********"
exit
address "*********"
exit
address "************"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "*********"
exit      
address "*********"
exit
address "*********"
exit
address "Group_19.x.0.0"
exit
address "*********"
exit
address "*********"
exit
address "***********"
exit
address "**********-52"
exit
address "**********-22"
exit
address "**********-52"
exit
address "*********"
exit
address "**********"
exit
address "**********"
exit      
address "************-12"
exit
address "***********/24"
exit
address "骏彩OPCC"
exit
address "***********-12"
exit
address "ump_4.255.199.241"
exit
address "ump_4.255.199.242"
exit
address "umpDuplex_4.255.199.246"
exit
address "umpDuplex_4.255.199.247"
exit
address "**********-12"
exit
address "************-160"
exit
address "**********1-110"
exit
address "***********-22"
exit      
address "**********-22"
exit
address "********_*********_*********"
exit
address "***********-12"
exit
address "**********"
exit
address "**********"
exit
address "**********-213"
exit
address "************"
exit
address "**********-123"
exit
address "***********"
exit
address "*********-42"
exit
address "***********-62"
exit
address "***********-72"
exit      
address "***********-92"
exit
address "**********"
exit
address "骏彩支付_SFTP"
exit
address "*******"
exit
address "********/24"
exit
address "********/24"
exit
address "********/24"
exit
address "********/24"
exit
address "*********/24"
exit
address "*********/24"
exit
address "*********/24"
exit
address "***********/32"
exit      
address "********/24"
exit
address "**********/32"
exit
address "*********/32"
exit
address "*********-8"
exit
address "*********/24"
exit
address "***********"
exit
address "*********"
exit
address "*********/24"
exit
address "**********"
exit
address "********/24"
exit
address "********/16"
exit
address "********/16"
exit      
address "********/16"
exit
address "********/16"
exit
address "*********/24"
exit
address "********/24"
exit
address "*********/27"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "********/24"
exit
address "*********/24"
exit
address "**********"
exit
address "*********/24"
exit      
address "*********/24"
exit
address "***********-8"
exit
address "**********-123"
exit
address "***********/32"
exit
address "**********/24"
exit
address "************-247"
exit
address "*********"
exit
address "*********/32"
exit
address "*********/32"
exit
address "************/32"
exit
address "*********-47"
exit
address "************"
exit      
address "*********-152"
exit
address "************"
exit
address "************"
exit
address "***********-152"
exit
address "***********-162"
exit
address "**********"
exit
address "********/24"
exit
address "**********"
exit
address "***********-14"
exit
address "*********"
exit
address "*********-6"
exit
address "***********"
exit      
address "4.27.13.131-134"
exit
address "4.255.199.243-244"
exit
address "4.27.13.91-98"
exit
address "4.27.41.41-43"
exit
address "18.5.127.30"
exit
address "4.14.10.1"
exit
address "10.0.2.151"
exit
address "18.0.1.73-75"
exit
address "黑苏浙闽琼UMP工作站"
exit
address "4.11.9.31-33"
exit
address "9.11.1.21"
exit
address "4.28.20.31"
exit      
address "4.255.199.248/32"
exit
address "4.255.199.249/32"
exit
address "4.20.1.1-2"
exit
address "4.27.41.91-96"
exit
address "********/24"
exit
address "********/24"
exit
address "********/24"
exit
address "********/24"
exit
address "XXFB_4.27.41.50"
exit
address "***********"
exit
address "************~229"
exit
address "************"
exit      
address "************"
exit
address "**********"
exit
address "***********"
exit
address "***********1"
exit
address "18.0.2.34-35"
exit
address "**********-35"
exit
address "4.254.128.200"
exit
address "**********"
exit
address "9.15.1.20"
exit
address "18.0.2.94-95"
exit
address "4.17.10.11-14"
exit
address "18.0.12.94-96"
exit      
address "YYGS_4.98.9.0"
exit
address "18.4.21.20"
exit
address "18.1.16.14"
exit
address "18.0.2.101"
exit
address "*********"
exit
address "*********"
exit
address "************"
exit
address "XXFB-VPDN-1"
exit
address "XXFB-VPDN-2"
exit
address "XXFB-VPDN-3"
exit
address "XXFB-VPDN-4"
exit
address "XXFB-IPTV"
exit      
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "ump-normal_*********31-134"
exit
address "**********-214"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "CIMS_**********"
exit      
address "**********"
exit
address "**********/24"
exit
address "************/24"
exit
address "********"
exit
address "************"
exit
address "JS-DaoKu-PC"
exit
address "SJZT-kafka"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "198.3.10.0"
exit
address "3.22.99.0"
exit      
address "4.255.205.2"
exit
address "*******"
exit
address "3.13.10.140"
exit
address "***********"
exit
address "3.13.10.40"
exit
address "**********"
exit
address "**********"
exit
address "***********"
exit
address "TCloud-LG"
exit
address "G3_ump_group"
exit
address "SSL_4.100.5.0/24"
exit
address "************/24"
exit      
address "TC_APP_NAT_Address"
exit
address "********"
exit
address "**********"
exit
aaa-server "local" type local
exit
track "track-ha"
exit
service "tcp-4433"
  tcp dst-port 4433 
exit
service "SFTP"
  tcp dst-port 22 
exit
service "tcp-8080"
  tcp dst-port 8080 
exit
service "tcp-10080"
  tcp dst-port 10080 
exit
service "tcp-22"
  tcp dst-port 22 
exit
service "tcp-9876"
  tcp dst-port 9876 
exit
service "tcp-10911"
  tcp dst-port 10911 
exit
service "tcp-443"
  tcp dst-port 443 
exit
service "tcp-55382"
  tcp dst-port 55382 
exit
service "tcp-34443"
  tcp dst-port 34443 
exit
service "tcp-20417"
  tcp dst-port 20417 
exit
service "TCP-10912"
  tcp dst-port 10912 
exit
service "TCP-10909"
  tcp dst-port 10909 
exit
service "tcp-8081"
  tcp dst-port 8081 
exit
service "tcp-21"
  tcp dst-port 21 
exit
service "tcp-80"
  tcp dst-port 80 
exit
service "tcp-8330"
  tcp dst-port 8330 
exit
service "tcp-10050"
  tcp dst-port 10050 
exit
service "tcp-10051"
  tcp dst-port 10051 
exit
service "TCP-10102"
  tcp dst-port 10102 
exit
service "tcp-389"
  tcp dst-port 389 
exit
service "tcp-3389"
  tcp dst-port 3389 
exit
service "tcp-8000"
  tcp dst-port 8000 
exit
service "TCP-18080"
  tcp dst-port 18080 
exit
service "tcp-21406"
  tcp dst-port 21406 
exit
service "tcp-50094"
  tcp dst-port 50094 
exit
service "tcp-8091"
  tcp dst-port 8091 
exit
service "tcp-7001"
  tcp dst-port 7001 
exit
service "tcp-30000"
  tcp dst-port 30000 
exit
service "tcp-7061"
  tcp dst-port 7061 
exit
service "tcp-8061"
  tcp dst-port 8061 
exit
service "tcp-7051"
  tcp dst-port 7051 
exit
service "tcp-8051"
  tcp dst-port 8051 
exit
service "tcp-9021"
  tcp dst-port 9021 
exit
service "tcp-5533"
  tcp dst-port 5533 
exit
service "tcp-8443"
  tcp dst-port 8443 
exit
service "TCP-52701"
  tcp dst-port 52701 
exit
service "TCP-28080"
  tcp dst-port 28080 
exit
service "TCP-18081"
  tcp dst-port 18081 
exit
service "TCP-31306"
  tcp dst-port 31306 
exit
service "TCP-27003"
  tcp dst-port 27003 
exit
service "TCP-28081"
  tcp dst-port 28081 
exit
service "TCP-7002"
  tcp dst-port 7002 
exit
service "TCP-54102"
  tcp dst-port 54102 
exit
service "TCP-54013"
  tcp dst-port 54013 
exit
service "TCP-24102"
  tcp dst-port 24102 
exit
service "TCP-29092"
  tcp dst-port 29092 
exit
service "TCP-54103"
  tcp dst-port 54103 
exit
service "tcp-21514"
  tcp dst-port 21514 
exit
service "tcp-9000"
  tcp dst-port 9000 
exit
service "tcp-23000"
  tcp dst-port 23000 
exit
service "tcp-5080"
  tcp dst-port 5080 
exit
service "tcp-7004"
  tcp dst-port 7004 
exit
service "tcp-19080"
  tcp dst-port 19080 
exit
service "tcp-7005"
  tcp dst-port 7005 
exit
service "tcp-8001"
  tcp dst-port 8001 
exit
service "tcp-53"
  tcp dst-port 53 
exit
service "udp-53"
  udp dst-port 53 
exit
service "tcp-135"
  tcp dst-port 135 
exit
service "tcp-1024-65535"
  tcp dst-port 1024 65535 
exit
service "udp-389"
  udp dst-port 389 
exit
service "tcp-636"
  tcp dst-port 636 
exit
service "tcp-88"
  tcp dst-port 88 
exit
service "udp-88"
  udp dst-port 88 
exit
service "tcp-445"
  tcp dst-port 445 
exit
service "tcp-20001"
  tcp dst-port 20001 
exit
service "tcp-20002"
  tcp dst-port 20002 
exit
service "tcp-7777"
  tcp dst-port 7777 
exit
service "tcp-23215~23220"
  tcp dst-port 23215 23220 
exit
service "tcp-8000~9000"
  tcp dst-port 8000 9000 
exit
service "TCP-8022"
  tcp dst-port 8022 
exit
service "tcp-50011"
  tcp dst-port 50011 
exit
service "tcp-8090"
  tcp dst-port 8090 
exit
service "TCP-9092"
  tcp dst-port 9092 
exit
service "TCP-9526"
  tcp dst-port 9526 
exit
service "tcp-8003"
  tcp dst-port 8003 
exit
service "udp-8010"
  udp dst-port 8010 
exit
service "TCP-23"
  tcp dst-port 23 
exit
ips sigset "dns" template dns
  max-scan-bytes 30720
exit
ips sigset "ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "telnet" template telnet
  max-scan-bytes 30720
exit      
ips sigset "other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "imap" template imap
  max-scan-bytes 30720
exit
ips sigset "finger" template finger
  max-scan-bytes 30720
exit
ips sigset "sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "snmp" template snmp
  max-scan-bytes 30720
exit      
ips sigset "mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "voip" template voip
  max-scan-bytes 30720
exit      
ips sigset "default_dns" template dns
  max-scan-bytes 30720
exit
ips sigset "default_ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "default_http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "default_pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "default_smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "default_telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "default_other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "default_other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "default_imap" template imap
  max-scan-bytes 30720
exit
ips sigset "default_finger" template finger
  max-scan-bytes 30720
exit
ips sigset "default_sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "default_nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "default_tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "default_snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "default_mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "default_mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "default_oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "default_msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "default_netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "default_dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "default_ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "default_voip" template voip
  max-scan-bytes 30720
exit
ips profile "no-ips"
exit
ips profile "predef_default"
  sigset "default_dns"
  sigset "default_ftp"
  sigset "default_http"
  sigset "default_pop3"
  sigset "default_smtp"
  sigset "default_telnet"
  sigset "default_other-tcp"
  sigset "default_other-udp"
  sigset "default_imap"
  sigset "default_finger"
  sigset "default_sunrpc"
  sigset "default_nntp"
  sigset "default_tftp"
  sigset "default_snmp"
  sigset "default_mysql"
  sigset "default_mssql"
  sigset "default_oracle"
  sigset "default_msrpc"
  sigset "default_netbios"
  sigset "default_dhcp"
  sigset "default_ldap"
  sigset "default_voip"
  filter-class 1 
    severity "Low" 
    severity "Medium" 
    severity "High" 
    action reset
  exit
exit
url-category "custom1"
exit
url-category "custom2"
exit
url-category "custom3"
exit
contentfilter
exit
sandbox-profile "predef_low"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_middle"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_high"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  file-type swf
  file-type rar
  file-type zip
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
sandbox-profile "predef_pe"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
url-profile "no-url"
exit
track "track-ha"
  interface aggregate1 
  interface aggregate2 
exit
admin user "hillstone"
  password RSNssI/9WSAxWkc+dqfgryQQ+t
        password-expiration 1568718473
  role "admin"
  access console
exit
admin user "admin"
  password IzYCiFDjQgZgKQfHx/UGu60Qwt
        password-expiration 1631068499
  role "admin"
  access console
  access ssh
  access https
exit
admin user "cslcnet"
  password 86O239MWjz3J4CoBkXmkcfwQ6R
        password-expiration 1578998317
  access ssh
  access https
exit
logging event to syslog severity warnings
logging threat to buffer severity informational
logging threat to syslog custom-format  severity warnings
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.235.1 vrouter "trust-vr" udp 514 type event
logging syslog *********** vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type config
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type network
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type nbc
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type threat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic session
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic nat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic pbr
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type sandbox
logging syslog 3.252.235.111 vrouter "mgt-vr" udp 514 type threat
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "monitor_address"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "Group_10.x.0.0"
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip **********/16
  ip **********/16
  ip **********/16
  ip **********/16
  ip **********/16
  ip **********/16
exit
address "Group_9.x.0.0"
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
exit
address "GTM"
  ip ********/32
  ip ********/32
exit
address "net_**********"
  ip **********/16
exit
address "终端强管制"
  ip **********/32
exit
address "终端强管制应用"
  ip **********/32
exit
address "**********-59"
  range ********** **********
exit
address "***********-203"
  range *********** ***********
exit
address "********-9"
  range ******** ********
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "UMP_Group"
  range ********* *********2
  range *********31 *********34
  range *********61 *********72
exit
address "省市FTP更新服务器"
  ip **********/16
exit      
address "host_************"
  ip ************/32
exit
address "host_**********"
  ip **********/32
exit
address "*********"
  ip *********/32
exit
address "************"
  ip ************/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit      
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "Group_19.x.0.0"
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 1********/16
  ip 19.111.0.0/16
  ip 19.112.0.0/16
  ip 19.113.0.0/16
  ip 19.114.0.0/16
  ip 19.115.0.0/16
  ip 19.121.0.0/16
  ip 19.122.0.0/16
  ip 19.123.0.0/16
  ip 19.131.0.0/16
  ip 19.132.0.0/16
  ip 19.133.0.0/16
  ip 19.134.0.0/16
  ip 19.135.0.0/16
  ip 19.136.0.0/16
  ip 19.137.0.0/16
  ip 19.141.0.0/16
  ip 19.142.0.0/16
  ip 19.143.0.0/16
  ip 19.144.0.0/16
  ip 19.145.0.0/16
  ip 19.146.0.0/16
  ip 19.150.0.0/16
  ip 19.151.0.0/16
  ip 19.152.0.0/16
  ip 19.153.0.0/16
  ip 19.154.0.0/16
  ip 19.161.0.0/16
  ip 19.162.0.0/16
  ip 19.163.0.0/16
  ip 19.164.0.0/16
  ip 19.165.0.0/16
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "***********"
  ip ***********/32
exit
address "**********-52"
  range ********** 4.24.10.52
exit
address "**********-22"
  range ********** 4.50.10.22
exit
address "**********-52"
  range ********** 18.4.21.52
exit
address "*********"
  ip *********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "************-12"
  range ************ 4.103.100.12
exit
address "***********/24"
  ip ***********/24
exit
address "骏彩OPCC"
  ip ********/24
  ip *********/24
  ip *********/24
exit
address "***********-12"
  range *********** 4.255.10.12
exit
address "ump_4.255.199.241"
  ip 4.255.199.241/32
exit
address "ump_4.255.199.242"
  ip 4.255.199.242/32
exit      
address "umpDuplex_4.255.199.246"
  ip 4.255.199.246/32
exit
address "umpDuplex_4.255.199.247"
  ip 4.255.199.247/32
exit
address "**********-12"
  range ********** 4.20.10.12
exit
address "************-160"
  range ************ 4.255.10.160
exit
address "**********1-110"
  range **********1 4.20.10.110
exit
address "***********-22"
  range *********** 4.255.10.22
exit
address "**********-22"
  range ********** 4.20.10.22
exit
address "********_*********_*********"
  ip ********/24
  ip *********/24
  ip *********/24
exit
address "***********-12"
  range *********** 4.101.91.12
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********-213"
  range ********** 4.60.6.213
exit
address "************"
  ip ************/32
exit
address "**********-123"
  range ********** 4.60.6.123
exit
address "***********"
  ip ***********/32
exit
address "*********-42"
  range ********* 18.0.5.42
exit
address "***********-62"
  range *********** 18.5.127.62
exit
address "***********-72"
  range *********** 18.5.127.72
exit
address "***********-92"
  range *********** 18.5.127.92
exit
address "**********"
  ip **********/32
exit
address "骏彩支付_SFTP"
  ip 18.5.127.131/32
  ip 18.5.127.129/32
  range 18.5.127.141 ************
exit
address "*******"
  ip *******/32
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "***********/32"
  ip ***********/32
exit
address "********/24"
  ip ********/24
exit
address "**********/32"
  ip **********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********-8"
  range ********* 15.0.31.8
exit
address "*********/24"
  ip *********/24
exit
address "***********"
  ip ***********/32
exit
address "*********"
  ip *********/32
exit
address "*********/24"
  ip *********/24
exit
address "**********"
  ip **********/32
exit
address "********/24"
  ip ********/24
exit
address "********/16"
  ip ********/16
exit
address "********/16"
  ip ********/16
exit
address "********/16"
  ip ********/16
exit
address "********/16"
  ip ********/16
exit
address "*********/24"
  ip *********/24
exit
address "********/24"
  ip ********/24
exit
address "*********/27"
  ip *********/27
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "********/24"
  ip ********/24
exit
address "*********/24"
  ip *********/24
exit
address "**********"
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "***********-8"
  range *********** 18.255.15.8
exit
address "**********-123"
  range ********** **********
exit
address "***********/32"
  ip ***********/32
exit
address "**********/24"
  ip **********/24
exit
address "************-247"
  range ************ ************
exit
address "*********"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "************/32"
  ip ************/32
exit
address "*********-47"
  range ********* 4.60.8.47
exit
address "************"
  ip ************/32
exit
address "*********-152"
  range ********* 4.60.8.152
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "***********-152"
  range *********** 18.2.12.152
exit
address "***********-162"
  range *********** 18.2.12.162
exit
address "**********"
  ip **********/32
exit
address "********/24"
  ip ********/24
exit
address "**********"
  ip **********/32
exit
address "***********-14"
  range *********** 4.103.19.14
exit
address "*********"
  ip *********/32
exit
address "*********-6"
  range ********* 18.1.14.6
exit
address "***********"
  ip ***********/32
exit
address "4.27.13.131-134"
  range 4.27.13.131 4.27.13.134
exit
address "4.255.199.243-244"
  range 4.255.199.243 4.255.199.244
exit
address "4.27.13.91-98"
  range 4.27.13.91 4.27.13.98
exit
address "4.27.41.41-43"
  ip 4.27.41.41/32
  ip 4.27.41.42/32
  ip 4.27.41.43/32
exit
address "18.5.127.30"
  ip 18.5.127.30/32
exit
address "4.14.10.1"
  ip 4.14.10.1/32
exit
address "10.0.2.151"
  ip 10.0.2.151/32
exit
address "18.0.1.73-75"
  range 18.0.1.73 18.0.1.75
exit      
address "黑苏浙闽琼UMP工作站"
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
exit
address "4.11.9.31-33"
  range 4.11.9.31 4.11.9.33
exit
address "9.11.1.21"
  ip 9.11.1.21/32
exit
address "4.28.20.31"
  ip 4.28.20.31/32
exit
address "4.255.199.248/32"
  ip 4.255.199.248/32
exit
address "4.255.199.249/32"
  ip 4.255.199.249/32
exit
address "4.20.1.1-2"
  range 4.20.1.1 4.20.1.2
exit
address "4.27.41.91-96"
  range 4.27.41.91 4.27.41.96
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "********/24"
  ip ********/24
exit
address "XXFB_4.27.41.50"
  ip 4.27.41.50/32
exit
address "***********"
  ip ***********/32
exit
address "************~229"
  range ************ 18.5.127.229
exit
address "************"
  ip ************/32
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "***********1"
  ip ***********1/32
exit
address "18.0.2.34-35"
  range 18.0.2.34 18.0.2.35
exit
address "**********-35"
  range ********** 18.0.12.35
exit
address "4.254.128.200"
  ip 4.254.128.200/32
exit
address "**********"
  ip **********/32
exit
address "9.15.1.20"
  ip 9.15.1.20/32
exit
address "18.0.2.94-95"
  range 18.0.2.94 18.0.2.95
exit
address "4.17.10.11-14"
  range 4.17.10.11 4.17.10.14
exit
address "18.0.12.94-96"
  range 18.0.12.94 18.0.12.96
exit
address "YYGS_4.98.9.0"
  ip 4.98.9.0/24
exit
address "18.4.21.20"
  ip 18.4.21.20/32
exit
address "18.1.16.14"
  ip 18.1.16.14/32
exit
address "18.0.2.101"
  ip 18.0.2.101/32
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "************"
  ip ************/32
exit
address "XXFB-VPDN-1"
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip **********/16
  ip **********/16
  ip *********/16
exit
address "XXFB-VPDN-2"
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
exit
address "XXFB-VPDN-3"
  ip *********/16
  ip *********/16
  ip *********/16
  ip **********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
exit
address "XXFB-VPDN-4"
  ip *********/16
  ip *********/16
exit
address "XXFB-IPTV"
  ip **********/32
  ip **********/32
  ip ********/32
  ip ********/32
  ip ********/32
  ip ********/32
  ip ********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip **********/32
  ip **********/32
exit      
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "ump-normal_*********31-134"
  range *********31 *********34
exit
address "**********-214"
  range ********** **********
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit      
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "CIMS_**********"
  ip **********/24
exit
address "**********"
  ip **********/32
exit
address "**********/24"
  ip **********/24
exit
address "************/24"
  ip ************/24
exit
address "********"
  ip ********/32
exit      
address "************"
  ip ************/32
exit
address "JS-DaoKu-PC"
  ip **********/32
  ip **********/32
exit
address "SJZT-kafka"
  range ************ ************
  range ************ ************
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "198.3.10.0"
  ip 198.3.10.0/24
exit
address "3.22.99.0"
  ip 3.22.99.0/24
exit
address "4.255.205.2"
  ip 4.255.205.2/32
exit
address "*******"
  ip *******/8
exit
address "3.13.10.140"
  ip 3.13.10.140/32
exit
address "***********"
  ip ***********/32
exit
address "3.13.10.40"
  ip 3.13.10.40/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "TCloud-LG"
  ip 10.196.0.2/32
  ip 10.196.0.11/32
  ip 10.196.0.12/32
  ip 10.196.0.17/32
  ip 10.196.0.4/32
  ip 10.196.0.26/32
  ip 10.196.0.15/32
  ip 10.196.0.49/32
exit
address "G3_ump_group"
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
  ip ********/16
exit
address "SSL_4.100.5.0/24"
  ip 4.100.5.0/24
exit
address "************/24"
  ip ************/24
exit
address "TC_APP_NAT_Address"
  ip 4.8.1.1/32
exit
address "********"
  ip ********/32
exit
address "**********"
  ip **********/32
exit
zone "mgt"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad land-attack action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad port-scan
  ad port-scan action alarm
  ad syn-flood
  ad syn-flood destination-threshold 15000
  ad syn-flood destination ip-based
  ad icmp-flood
  ad ip-sweep
  ad ip-sweep action alarm
  ad ping-of-death
  ad udp-flood
  ad udp-flood destination-threshold 15000
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
zone "MG"
  vrouter "mgt-vr"
exit
hostname "XWPAFW01"
admin host 3.20.10.0/24 ssh
admin host 3.20.10.0/24 https
admin host 4.20.10.0/24 ssh
admin host 4.20.10.0/24 https
admin host *********/32 ssh
admin host **********/24 ssh
admin host **********/24 https
web same-account-login enable
no https client-auth match
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit

isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit      

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface MGT0 local
  zone  "MG"
  ip address 4.255.253.67 *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet4/0
  aggregate aggregate1
exit
interface xethernet4/1
  aggregate aggregate1
exit
interface xethernet4/2
  aggregate aggregate2
exit
interface xethernet4/3
  aggregate aggregate2
exit
interface aggregate1
  zone  "trust"
  ip address 4.255.199.17 255.255.255.252
  manage ping
  lacp enable
exit
interface aggregate2
  zone  "untrust"
  ip address 4.255.199.26 255.255.255.252
  manage ping
  lacp enable
  reverse-route prefer
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 4.255.253.254
exit
ip vrouter "trust-vr"
  snatrule id 1 from ip ********* to ip *********** service "Any" trans-to ip 18.255.15.5 mode static log 
  snatrule id 2 from address-book "************/24" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_Address" mode dynamicport log 
  dnatrule id 6 from address-book "Any" to ip ***********/32 service "Any" trans-to ip *********/32 log 
  dnatrule id 1 from address-book "Any" to ip 18.255.15.5/32 service "Any" trans-to address-book "*********/32" log 
  dnatrule id 7 from address-book "Any" to ip 18.255.15.6/32 service "Any" trans-to ip 15.0.31.6/32 log 
  dnatrule id 8 from address-book "Any" to ip 18.255.15.7/32 service "Any" trans-to ip 15.0.31.7/32 log 
  dnatrule id 9 from address-book "Any" to ip 18.255.15.8/32 service "Any" trans-to ip 15.0.31.8/32 log 
  ip route 4.0.0.0/8 4.255.199.18
  ip route 10.0.0.0/8 4.255.199.25
  ip route 4.255.199.32/30 4.255.199.25
  ip route 4.255.199.36/30 4.255.199.25
  ip route *******/8 4.255.199.25
  ip route ********/8 4.255.199.25
  ip route 1*******/8 4.255.199.25
  ip route 15.0.31.0/24 4.255.199.25
  ip route ************/24 4.255.199.18
  ip route 198.3.100.0/24 4.255.199.18
  ip route 3.22.99.0/24 4.255.199.18
  ip route 198.3.10.0/24 4.255.199.18
  ip route 198.3.11.0/24 4.255.199.18
  ip route 3.0.0.0/8 4.255.199.18
  ip route 10.196.0.0/24 4.255.199.18
  ip route ************/24 4.255.199.18
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp enable
ntp server 4.9.0.1
clock zone china
rule id 1
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-ip 4.255.199.0/24
  dst-ip 4.9.0.1/32
  service "ICMP"
  service "NTP"
  name "网络设备访问NTP"
exit
rule id 2
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "*********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-ip 4.11.2.50/32
  service "tcp-4433"
  service "ICMP"
  name "终端访问SSL网关"
exit
rule id 3
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  src-addr "Group_9.x.0.0"
  dst-ip 4.8.0.100/32
  service "DNS"
  service "ICMP"
  name "终端访问DNS"
exit
rule id 4
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  src-addr "Group_9.x.0.0"
  src-addr "省市FTP更新服务器"
  dst-ip 4.9.0.1/32
  dst-ip 4.9.0.10/32
  service "NTP"
  service "ICMP"
  name "NTP"
exit
rule id 5
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "GTM"
  src-addr "*********"
  dst-addr "net_**********"
  service "FTP"
  service "ICMP"
  service "HTTPS"
  name "GTM探测FTP状态"
exit
rule id 6
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  dst-addr "终端强管制"
  service "HTTP"
  service "ICMP"
  name "终端访问终端强管制"
exit
rule id 7
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  dst-addr "终端强管制应用"
  service "HTTP"
  service "tcp-8080"
  name "管理端访问终端强管制"
exit
rule id 8
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-ip *********/24
  src-ip *********/24
  src-ip ********/24
  dst-ip ************/32
  dst-ip ************/32
  dst-ip ************0/32
  service "HTTPS"
  name "云管平台访问G3模拟运营网络"
exit
rule id 9
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  dst-ip **********/32
  service "SFTP"
  service "HTTP"
  service "HTTPS"
  service "FTP"
  name "银行访问测试服务器"
exit
rule id 10
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-ip ***********/32
  service "HTTPS"
  name "彩银访问支付系统 4.1"
exit
rule id 11
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "**********"
  src-addr "**********-214"
  src-range ********** **********
  dst-addr "Group_9.x.0.0"
  service "Any"
  name "终端兑奖额度转账"
exit
rule id 12
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-ip *********/24
  src-ip *********/24
  src-ip ********/24
  dst-ip *************/32
  service "HTTP"
  name "运管平台访问G3模拟运营"
exit      
rule id 16
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-ip ***********/32
  dst-range ********** **********
  service "tcp-10080"
  name "骏彩开放平台访问营销中心 4.1"
exit
rule id 17
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********-42"
  src-ip ***********/32
  dst-ip *******/32
  service "tcp-34443"
  name "骏彩开放平台访问营销中心SFTP 4.1"
exit
rule id 18
  action permit
  disable 
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-range ************ ************
  service "tcp-9876"
  name "营销中心访问支付系统RocketMQ(nameserver) 4.1"
exit
rule id 19
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-range *********** ***********
  service "tcp-10911"
  service "TCP-10909"
  service "TCP-10912"
  name "营销中心访问支付系统RocketMQ(master) 4.1"
exit
rule id 20
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-range *********** ***********
  service "tcp-10911"
  service "TCP-10909"
  service "TCP-10912"
  name "营销中心访问支付系统RocketMQ(slave) 4.1"
exit
rule id 21
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-ip ***********/32
  service "tcp-8080"
  name "营销中心访问支付系统（前置网关F5） 4.1"
exit
rule id 22
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-ip **********/32
  service "tcp-443"
  name "营销中心访问骏彩支付宝（专线） 4.1"
exit
rule id 23
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-ip ************/32
  service "tcp-55382"
  name "营销中心访问骏彩联通（公网）4.1"
exit
rule id 24
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-range ************ ************
  service "tcp-443"
  name "营销中心访问骏彩支付宝（公网） 4.1"
exit
rule id 25
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-range ********** **********
  dst-ip ************/32
  service "tcp-20417"
  name "营销中心访问骏彩支付系统用户中心 4.1"
exit
rule id 28
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********-9"
  dst-addr "*********"
  service "HTTP"
  name "骏彩proxy访问 4.1"
exit
rule id 29
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "host_************"
  dst-addr "host_**********"
  dst-addr "**********"
  service "tcp-8081"
  name "骏彩开放平台访问即开第三方合作机构接入系统 4.1"
exit
rule id 30
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "UMP_Group"
  src-addr "************/24"
  dst-addr "省市FTP更新服务器"
  service "FTP"
  service "SSH"
  name "UMP访问各省FTP服务器"
exit
rule id 121
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-ip ********/32
  dst-addr "省市FTP更新服务器"
  service "SSH"
  name "终端机在线更新"
exit
rule id 31
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "UMP_Group"
  src-addr "*********"
  dst-addr "*********"
  service "HTTP"
  name "UMP访问骏彩AMS 4.1"
exit
rule id 32
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "UMP_Group"
  src-addr "**********-22"
  dst-addr "************"
  service "tcp-22"
  name "UMP推送开奖公告和销量公告报表 4.1"
exit      
rule id 33
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "UMP_Group"
  dst-addr "*********"
  service "HTTP"
  name "UMP访问北京数据同步服务器"
exit
rule id 34
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_19.x.0.0"
  src-addr "Group_9.x.0.0"
  dst-addr "*********"
  dst-addr "*********"
  service "HTTPS"
  name "省中心管理端访问UMP SSL网关"
exit
rule id 42
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  dst-addr "ump_4.255.199.241"
  dst-addr "ump_4.255.199.242"
  dst-addr "umpDuplex_4.255.199.246"
  dst-addr "umpDuplex_4.255.199.247"
  service "HTTPS"
  name "地市vpdn工作站访问UMP网关"
exit
rule id 35
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "***********"
  dst-addr "**********-52"
  service "HTTP"
  name "骏彩开放平台访问实体渠道nginx 4.1"
exit
rule id 36
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********-52"
  dst-addr "*********"
  service "tcp-4433"
  name "骏彩访问互联网SSL 4.1"
exit
rule id 37
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "host_************"
  dst-addr "**********"
  service "tcp-8330"
  service "tcp-8080"
  name "开放平台访问代销者服务接口 4.1"
exit
rule id 38
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "host_************"
  dst-addr "**********"
  service "tcp-8330"
  name "开放平台访问实名服务 4.1"
exit
rule id 39
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  dst-addr "************-12"
  service "FTP"
  name "管理端访问文件服务器"
exit
rule id 40
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********/24"
  dst-addr "省市FTP更新服务器"
  service "tcp-10050"
  service "tcp-443"
  service "tcp-22"
  name "监控平台访问省市中心更新服务器1"
exit
rule id 41
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "省市FTP更新服务器"
  dst-addr "***********/24"
  service "tcp-10051"
  name "监控平台访问省市中心更新服务器2"
exit
rule id 43
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "骏彩OPCC"
  dst-addr "***********-12"
  dst-addr "**********-12"
  service "TCP-10102"
  service "tcp-443"
  name "骏彩OPCC访问OCS代理机"
exit
rule id 44
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "骏彩OPCC"
  dst-addr "************-160"
  dst-addr "**********1-110"
  service "tcp-3389"
  service "tcp-389"
  name "骏彩OPCC访问OCS发布机"
exit
rule id 45
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "骏彩OPCC"
  dst-addr "***********-22"
  dst-addr "**********-22"
  service "tcp-3389"
  service "tcp-389"
  name "骏彩OPCC访问OCS域控"
exit
rule id 46
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********/24"
  dst-addr "*********"
  service "tcp-80"
  name "监控平台访问测试页监控项1 4.1"
exit
rule id 47
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********/24"
  dst-addr "************"
  service "tcp-22"
  name "监控平台访问测试页监控项2 4.1"
exit
rule id 48
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "********_*********_*********"
  dst-addr "***********-12"
  service "tcp-8000"
  service "tcp-8080"
  service "tcp-443"
  service "tcp-80"
  name "骏彩OPCC访问二中心云盘"
exit
rule id 49
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "host_************"
  dst-addr "**********"
  service "TCP-18080"
  name "骏彩开放平台访问UMP-CSS服务 4.1"
exit
rule id 50
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "**********-213"
  dst-addr "************"
  service "tcp-21406"
  name "电彩访问骏彩 4.1"
exit
rule id 51
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "**********-213"
  src-addr "**********-123"
  dst-addr "***********"
  service "tcp-50094"
  name "电彩访问骏彩G2 4.1"
exit
rule id 52
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "***********-62"
  src-addr "***********-72"
  src-addr "***********-92"
  dst-addr "**********"
  service "tcp-8091"
  name "骏彩支付访问电彩 4.1"
exit
rule id 53
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "骏彩支付_SFTP"
  dst-addr "*******"
  service "tcp-34443"
  name "骏彩支付访问电彩SFTP"
exit
rule id 54
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "********/24"
  dst-addr "*********/24"
  service "tcp-7001"
  service "tcp-443"
  name "骏彩访问支付"
exit
rule id 55
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "*********/27"
  dst-addr "***********/32"
  service "tcp-30000"
  service "tcp-22"
  name "骏彩访问支付SFTP 4.1"
exit
rule id 56
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "**********/32"
  dst-addr "*********/32"
  service "tcp-8080"
  name "支付访问骏彩-1"
exit
rule id 57
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "********/24"
  service "tcp-443"
  service "tcp-80"
  name "支付访问骏彩-2 4.1"
exit
rule id 58
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  src-addr "************/24"
  dst-addr "***********-8"
  service "tcp-5533"
  service "tcp-9021"
  service "tcp-8051"
  service "tcp-7051"
  service "tcp-8061"
  service "tcp-7061"
  name "ZF_TO_CCB"
exit
rule id 59
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "*********/24"
  service "SSH"
  service "FTP"
  service "tcp-8443"
  service "tcp-443"
  name "支付访问骏彩AB 4.1"
exit
rule id 60
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  name "支付访问骏彩3 4.1"
exit
rule id 61
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  src-addr "4.27.41.91-96"
  dst-addr "*********"
  service "TCP-52701"
  name "支付访问骏彩4 4.1"
exit
rule id 62
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "*********/24"
  service "TCP-31306"
  service "TCP-18081"
  service "TCP-28080"
  name "支付访问骏彩5 4.1"
exit
rule id 64
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "********/24"
  service "TCP-27003"
  service "TCP-31306"
  name "支付访问骏彩6 4.1"
exit
rule id 65
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "********/16"
  dst-addr "********/16"
  dst-addr "************-247"
  service "TCP-52701"
  service "SSH"
  service "TCP-31306"
  name "支付访问骏彩7 4.1"
exit
rule id 66
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "********/16"
  dst-addr "********/16"
  service "SSH"
  service "TCP-31306"
  service "tcp-80"
  service "tcp-443"
  name "支付访问骏彩8"
exit
rule id 67
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "*********/24"
  service "HTTP"
  service "TCP-31306"
  name "支付访问骏彩9 4.1"
exit
rule id 68
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "********/24"
  dst-addr "********/24"
  service "FTP"
  service "TCP-28081"
  service "TCP-28080"
  name "支付访问骏彩10 4.1"
exit
rule id 69
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********/27"
  dst-addr "**********"
  service "tcp-443"
  name "骏彩500W访问支付 4.1"
exit
rule id 70
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********/24"
  src-addr "********/24"
  dst-addr "**********"
  dst-addr "**********"
  service "TCP-7002"
  service "tcp-7001"
  name "骏彩访问支付1"
exit
rule id 71
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********/24"
  src-addr "********/24"
  dst-addr "*********/24"
  service "TCP-54013"
  service "TCP-54102"
  service "TCP-54103"
  name "运营ITO访问支付"
exit
rule id 72
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********"
  dst-addr "**********"
  service "tcp-443"
  name "代销者后台访问支付 4.1"
exit
rule id 73
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "********/24"
  dst-addr "********/24"
  service "TCP-54013"
  service "TCP-54102"
  service "TCP-24102"
  service "TCP-54103"
  name "骏彩访问支付2"
exit
rule id 74
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "********/24"
  src-addr "*********/24"
  src-addr "********/16"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "********/24"
  service "TCP-29092"
  name "骏彩访问支付3 4.1"
exit
rule id 75
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********/24"
  dst-addr "*********/24"
  dst-addr "********/24"
  service "TCP-54013"
  service "TCP-54102"
  service "TCP-24102"
  service "TCP-54103"
  name "骏彩访问支付4"
exit
rule id 76
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********/32"
  dst-addr "*********/27"
  service "SSH"
  name "SFTP访问500wan 4.1"
exit
rule id 77
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********/24"
  dst-addr "**********"
  service "tcp-8080"
  name "骏彩支付APP访问科技支付APP"
exit
rule id 78
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  dst-addr "*********"
  service "HTTPS"
  name "省市管理端银行访问彩银系统"
exit
rule id 79
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********/32"
  dst-addr "***********/32"
  service "tcp-30000"
  name "CCB访问支付SFTP"
exit
rule id 80
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "************/32"
  dst-addr "*********/32"
  service "tcp-21514"
  name "银联访问支付系统"
exit
rule id 81
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "*********-152"
  dst-addr "************"
  service "TCP-31306"
  name "4.1"
exit
rule id 82
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "*********-47"
  dst-addr "************"
  service "tcp-443"
  name "4.1-1"
exit
rule id 83
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "*********-47"
  dst-addr "************"
  service "tcp-9000"
  name "4.1-2"
exit
rule id 84
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "***********-152"
  src-addr "***********-162"
  dst-addr "**********"
  service "tcp-23000"
exit
rule id 85
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "********/24"
  dst-addr "**********"
  service "tcp-5080"
  name "山东彩银测试访问"
exit
rule id 86
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********-14"
  dst-addr "*********"
  service "tcp-7004"
  name "USAP访问骏彩Matserver 4.1"
exit
rule id 87
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********-6"
  dst-addr "***********"
  service "tcp-19080"
  name "骏彩Matserver访问USAP 4.1"
exit
rule id 88
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "4.27.13.131-134"
  dst-addr "*********"
  service "tcp-7004"
  name "USAP访问骏彩Matserver-1 4.1"
exit
rule id 89
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********/24"
  dst-addr "4.255.199.243-244"
  service "tcp-443"
  name "骏彩Matserver访问内网SSL"
exit
rule id 90
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "4.27.13.91-98"
  dst-addr "*********"
  service "tcp-7005"
  name "USAP访问骏彩Matserver-2 4.1"
exit
rule id 91
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "********/24"
  src-addr "Group_9.x.0.0"
  src-addr "Group_10.x.0.0"
  dst-addr "4.255.199.243-244"
  service "tcp-443"
  name "外部访问USAP"
exit
rule id 92
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "4.27.41.41-43"
  dst-addr "省市FTP更新服务器"
  service "SFTP"
  name "信息发布数据服务提供者访问更新服务器"
exit
rule id 93
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********-14"
  dst-addr "18.5.127.30"
  service "tcp-8001"
  name "USAP_nginx访问营销客服查询"
exit
rule id 94
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********"
  src-addr "9.11.1.21"
  dst-addr "*******"
  service "tcp-34443"
  name "北单游戏数据加载"
exit
rule id 95
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "省市FTP更新服务器"
  dst-addr "4.14.10.1"
  service "SFTP"
  name "省市更新服务器访问数据中台SFTP服务器"
exit
rule id 96
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "10.0.2.151"
  dst-addr "4.14.10.1"
  service "SFTP"
  name "竞彩网腾讯云环境访问数据中台SFTP服务器"
exit
rule id 97
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "18.0.1.73-75"
  dst-addr "4.14.10.1"
  dst-addr "**********"
  service "SFTP"
  name "竞彩日结数据访问数据中台SFTP服务器 4.1"
exit
rule id 98
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "黑苏浙闽琼UMP工作站"
  dst-addr "4.11.9.31-33"
  dst-addr "TCloud-LG"
  service "tcp-10080"
  service "SFTP"
  service "tcp-8080"
  name "黑苏浙闽琼UMP工作站访问导库服务"
exit
rule id 99
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********"
  dst-addr "4.28.20.31"
  service "tcp-34443"
  name "北单访问财务中心SFTP"
exit
rule id 100
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  src-addr "Group_10.x.0.0"
  dst-addr "4.255.199.248/32"
  dst-addr "4.255.199.249/32"
  service "HTTPS"
  name "终端访问国家中心更新服务器"
exit
rule id 101
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  dst-addr "4.20.1.1-2"
  service "NTP"
  service "tcp-445"
  service "udp-88"
  service "tcp-88"
  service "tcp-636"
  service "udp-389"
  service "tcp-389"
  service "tcp-1024-65535"
  service "tcp-135"
  service "udp-53"
  service "tcp-53"
  name "管理端工作站访问域控"
exit
rule id 102
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "********/24"
  src-addr "********/24"
  dst-addr "XXFB_4.27.41.50"
  service "tcp-8080"
  service "HTTP"
  name "IPTV和终端访问信息发布API"
exit
rule id 103
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********"
  dst-addr "************~229"
  service "tcp-7777"
  service "tcp-20002"
  service "tcp-20001"
  name "AB系统访问建行实名认证"
exit
rule id 104
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********"
  dst-addr "************"
  dst-addr "************"
  service "tcp-23215~23220"
  name "AB系统访问银联实名认证"
exit
rule id 105
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********"
  dst-addr "***********"
  service "tcp-8080"
  name "证据提取访问AB系统"
exit
rule id 106
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "***********1"
  dst-addr "***********"
  service "tcp-30000"
  service "tcp-8000~9000"
  name "建行访问AB系统"
exit
rule id 13
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********-35"
  src-addr "18.0.2.34-35"
  dst-addr "4.254.128.200"
  service "SQLNETv2"
  name "骏彩ECC大屏显示 4.1"
exit
rule id 14
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "**********"
  dst-addr "省市FTP更新服务器"
  service "SFTP"
  name "数据中台FTP服务访问省更新服务器"
exit
rule id 15
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "9.15.1.20"
  dst-addr "*******"
  service "tcp-34443"
  name "内蒙管理端访问SFTP服务器"
exit
rule id 26
  action permit
  disable
  src-zone "untrust"
  dst-zone "trust"
  src-addr "18.0.2.94-95"
  src-addr "18.0.12.94-96"
  dst-addr "*******"
  service "tcp-34443"
  name "骏彩RTQ访问SFTP服务 4.1"
exit
rule id 27
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "4.17.10.11-14"
  dst-addr "*********"
  service "TCP-52701"
  service "tcp-8080"
  name "SIH访问骏彩 4.1"
exit
rule id 63
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  dst-addr "*******"
  service "tcp-34443"
  name "省中心访问99999目录文件服务器"
exit
rule id 107
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "YYGS_4.98.9.0"
  dst-addr "18.4.21.20"
  service "tcp-443"
  name "YYGS-TO-juncai-1"
exit
rule id 108
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "YYGS_4.98.9.0"
  dst-addr "18.1.16.14"
  service "TCP-8022"
  name "YYGS-TO-juncai-2 4.1"
exit
rule id 109
  action permit
  disable
  src-zone "trust"
  dst-zone "untrust"
  src-addr "YYGS_4.98.9.0"
  dst-addr "18.0.2.101"
  service "TCP-28080"
  name "YYGS-TO-juncai-3 4.1"
exit
rule id 110
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********"
  dst-addr "*********"
  service "tcp-22"
  service "tcp-8081"
  service "tcp-8080"
  name "北京体彩中心访问数据中台服务"
exit
rule id 111
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  src-addr "Group_19.x.0.0"
  dst-addr "************"
  service "TCP-28081"
  service "TCP-28080"
  name "省中心访问骏彩CASGW"
exit
rule id 112
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_9.x.0.0"
  dst-ip *******/32
  service "SFTP"
  name "彩银银行文件对账SFTP服务"
exit
rule id 113
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "XXFB-VPDN-1"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  name "VPND访问信息发布API-1"
exit
rule id 114
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "XXFB-VPDN-2"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  name "VPND访问信息发布API-2"
exit
rule id 115
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "XXFB-VPDN-3"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  name "VPND访问信息发布API-3"
exit
rule id 116
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "XXFB-VPDN-4"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  name "VPND访问信息发布API-4"
exit
rule id 117
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "XXFB-IPTV"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  name "IPTV访问信息发布API"
exit
rule id 138
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "**********"
  dst-addr "********"
  service "tcp-443"
  name "HBbank-********"
exit
rule id 118
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-ip *********/32
  dst-ip **********/32
  service "tcp-50011"
  name "数据中台访问山东体彩即开销量数据"
exit
rule id 119
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-ip ********/16
  dst-ip ***********/32
  service "tcp-8090"
  name "天津体彩中心访问TLS终端数展示平台"
exit
rule id 120
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "ump-normal_*********31-134"
  src-addr "***********"
  dst-addr "省市FTP更新服务器"
  service "SFTP"
  name "UMP-NORMAL访问各省SFTP服务器"
exit
rule id 122
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  dst-ip **********/32
  dst-ip **********/32
  dst-ip **********/32
  dst-ip **********/32
  dst-ip **********/32
  service "tcp-443"
  name "终端访问信息发布SSL网关"
exit
rule id 123
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "CIMS_**********"
  dst-addr "省市FTP更新服务器"
  service "SSH"
  name "CIMS访问各省FTP服务器"
exit
rule id 124
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "**********/24"
  dst-addr "Group_9.x.0.0"
  service "tcp-3389"
  name "OCS访问省市管理端"
exit
rule id 125
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "************/24"
  src-addr "********"
  dst-addr "************"
  service "tcp-80"
  name "G3-ZF_To_CUP"
exit
rule id 126
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "JS-DaoKu-PC"
  dst-addr "SJZT-kafka"
  service "TCP-9092"
  name "JS-PC_To_SJZT-kafka"
exit
rule id 127
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "*********"
  dst-addr "net_**********"
  service "tcp-22"
  name "ZiDongHua_To_FTP-SEVER"
exit
rule id 128
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********"
  src-addr "***********"
  src-addr "3.22.99.0"
  dst-addr "Group_10.x.0.0"
  dst-addr "Group_9.x.0.0"
  service "SNMP"
  service "SYSLOG"
  service "ICMP"
  service "HTTPS"
  name "Solarwinds&PRTG-Trust"
exit
rule id 129
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "Group_10.x.0.0"
  src-addr "Group_9.x.0.0"
  dst-addr "***********"
  service "SNMP"
  service "SYSLOG"
  service "ICMP"
  service "tcp-8080"
  name "Solarwinds&PRTG-OUT"
exit
rule id 130
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "198.3.10.0"
  dst-addr "Group_10.x.0.0"
  dst-addr "Group_9.x.0.0"
  dst-addr "net_**********"
  service "ICMP"
  name "Monitor-OUT"
exit
rule id 131
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "4.255.205.2"
  src-addr "***********"
  dst-addr "Group_10.x.0.0"
  service "SFTP"
  name "Mserver_To_Terminal"
exit
rule id 132
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*******"
  dst-addr "3.13.10.40"
  dst-addr "***********"
  dst-addr "3.13.10.140"
  dst-addr "**********"
  dst-addr "**********"
  service "FTP"
  service "TFTP"
  service "SSH"
  service "TCP-9526"
  service "tcp-8000"
  service "tcp-8003"
  service "udp-8010"
  name "FTP&BQQ"
exit
rule id 133
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********"
  dst-addr "*******"
  service "tcp-8000"
  service "tcp-8003"
  name "BQQ_TO_SHENGZHONGXIN"
exit
rule id 134
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "***********"
  dst-addr "Group_10.x.0.0"
  dst-ip *************/32
  dst-ip *************/32
  service "TCP-23"
  service "SSH"
  name "YunWei_To_LNS"
exit
rule id 135
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "*********"
  dst-addr "**********"
  service "SFTP"
  name "BJDC_To_SFTP"
exit
rule id 136
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "G3_ump_group"
  dst-addr "TCloud-LG"
  service "SSH"
  name "G3_ump_22"
exit
rule id 137
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "G3_ump_group"
  dst-addr "SSL_4.100.5.0/24"
  service "HTTPS"
  name "G3_ump_443"
exit      
l2-nonip-action drop
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server vrouter "mgt-vr"
snmp-server engineID "cslc_snmp"
snmp-server host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg ro
snmp-server host ************ version 2c community ckeI3SSNGQhyyPo3P+lMJmQ8mGku ro
snmp-server host *********** version 2c community DWbf06+a4P0FlgO+Yrtfp2YzSHMK ro
snmp-server trap-host ************ version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq port 162
snmp-server trap-host ************ version 2c community iYrqDOGOtTqwQ38yjPmPA3Sw3FEJ port 162
snmp-server trap-host *********** version 2c community IQKm6jOQzQw3266DzxLL5hlJXP8D port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional
exit
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application
exit
no sms disable
ha link interface HA0
ha link interface ethernet0/0
ha link ip ******* *************
ha group 0
  monitor track "track-ha"
exit      
ha cluster 3

End

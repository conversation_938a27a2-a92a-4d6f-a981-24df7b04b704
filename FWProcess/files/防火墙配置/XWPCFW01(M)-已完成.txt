XWPCFW01(M)# show config

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "SSLtoSIE-vr"
exit
ip vrouter "juncai _vr"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit      
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
zone "SSLtoSIE-trust"
exit
zone "SSLtoSIE-untrust"
exit
zone "juncai _untrust"
exit
zone "juncai _trust"
exit
interface vswitchif1
exit
interface ethernet0/0 local
exit      
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit
interface xethernet3/0
exit
interface xethernet3/1
exit
interface xethernet3/2
exit
interface xethernet3/3
exit
interface xethernet3/4
exit
interface xethernet3/5
exit
interface xethernet3/6
exit
interface xethernet3/7
exit
interface xethernet5/0
exit      
interface xethernet5/1
exit
interface xethernet5/2
exit
interface xethernet5/3
exit
interface xethernet5/4
exit
interface xethernet5/5
exit
interface xethernet5/6
exit
interface xethernet5/7
exit
interface aggregate1
exit
interface aggregate1.101
exit
interface aggregate1.102
exit
interface aggregate1.105
exit
interface aggregate1.106
exit      
address "private_network"
exit
address "citrix"
exit
address "NTP"
exit
address "NFS_*********"
exit
address "NFS_*********"
exit
address "NFS_*********"
exit
address "NFS_*********"
exit
address "Group_10.x.0.0"
exit
address "**********"
exit
address "********-2"
exit
address "**********"
exit
address "********-3"
exit      
address "**********"
exit
address "********-2"
exit
address "**********"
exit
address "********-4"
exit
address "**********/24"
exit
address "*******"
exit
address "***********-***********"
exit
address "************-************"
exit
address "**********"
exit
address "*******"
exit
address "************-************"
exit
address "***********-***********"
exit      
address "***********-***********"
exit
address "**********"
exit
address "*******10"
exit
address "*********"
exit
address "************"
exit
address "************-15"
exit
address "********-9"
exit
address "************"
exit
address "**********-14"
exit
address "************-13"
exit
address "***********"
exit
address "**********-2"
exit      
address "*********/24"
exit
address "**********/21"
exit
address "**********/21"
exit
address "**********/21"
exit
address "***********/21"
exit
address "**********/24"
exit
address "**********"
exit
address "**********-2"
exit
address "*******/24"
exit
address "*********/16"
exit
address "**********2"
exit
address "**********"
exit      
address "***********/24"
exit
aaa-server "local" type local
exit
track "track-ha"
exit
logging session content hostname
logging session content username
service "tcp-1521"
  tcp dst-port 1521 
exit
service "TCP_1521"
  tcp dst-port 1521 timeout 3600
exit
service "tpc-1521"
  tcp dst-port 1521 timeout 300
exit
service "TCP-1415"
  tcp dst-port 1415 
exit
service "tcp-1521-7200"
  tcp dst-port 1521 timeout 7200
exit
service "tcp-1415-7200"
  tcp dst-port 1415 timeout 7200
exit
service "tcp-1521-300"
  tcp dst-port 1521 timeout 300
exit
service "tcp-1521-120"
  tcp dst-port 1521 timeout 120
exit
service "tcp-26379"
  tcp dst-port 26379 
exit
service "TCP-20050"
  tcp dst-port 20050 
exit
service "TCP-1417"
  tcp dst-port 1417 
exit
service "TCP_111"
  tcp dst-port 111 
exit
service "UDP_111"
  udp dst-port 111 
exit
service "TCP_2049"
  tcp dst-port 2049 
exit
service "UDP_2049"
  udp dst-port 2049 
exit
service "TCP_635"
  tcp dst-port 635 
exit
service "UDP_635"
  udp dst-port 635 
exit
service "TCP_4045"
  tcp dst-port 4045 
exit
service "UDP_4045"
  udp dst-port 4045 
exit
service "TCP_4046"
  tcp dst-port 4046 
exit
service "UDP_4046"
  udp dst-port 4046 
exit
service "TCP_4047"
  tcp dst-port 4047 
exit
service "UDP_4047"
  udp dst-port 4047 
exit
service "TCP_4048"
  tcp dst-port 4048 
exit
service "UDP_4048"
  udp dst-port 4048 
exit
service "TCP_4049"
  tcp dst-port 4049 
exit
service "UDP_4049"
  udp dst-port 4049 
exit
service "TCP_389"
  tcp dst-port 389 
exit
service "TCP_6006"
  tcp dst-port 6006 
exit
service "UDP_389"
  udp dst-port 389 
exit
service "tcp-4433"
  tcp dst-port 4433 
exit
service "TCP-14101"
  tcp dst-port 14101 
exit
service "TCP-15101"
  tcp dst-port 15101 
exit
service "tcp-60001-60100"
  tcp dst-port 60001 60100 
exit
service "tcp-6006-L"
  tcp dst-port 6006 timeout-day 1
exit
service "tcp-6006-s"
  tcp dst-port 6006 timeout 600
exit
service "TCP-10051"
  tcp dst-port 10051 
exit
service "TCP-10050"
  tcp dst-port 10050 
exit
service "tcp-34443"
  tcp dst-port 34443 
exit
service "tcp-1520"
  tcp dst-port 1520 
exit
service "TCP-139"
  tcp dst-port 139 
exit
service "TCP-3389"
  tcp dst-port 3389 
exit
service "TCP-80"
  tcp dst-port 80 
exit
service "TCP-443"
  tcp dst-port 443 
exit
service "TCP- 52701"
  tcp dst-port 52701 
exit
service "TCP-8080"
  tcp dst-port 8080 
exit
service "TCP- 21"
  tcp dst-port 21 
exit
service "UDP 1812"
  udp dst-port 1812 
exit
service "TCP_21050"
  tcp dst-port 21050 
exit
service "TCP-30000"
  tcp dst-port 30000 
exit
ips sigset "dns" template dns
  max-scan-bytes 30720
exit
ips sigset "ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "http" template http
  max-scan-bytes 30720
  web-server "default"
  exit    
exit
ips sigset "pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "imap" template imap
  max-scan-bytes 30720
exit
ips sigset "finger" template finger
  max-scan-bytes 30720
exit
ips sigset "sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "voip" template voip
  max-scan-bytes 30720
exit
ips sigset "default_dns" template dns
  max-scan-bytes 30720
exit
ips sigset "default_ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "default_http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "default_pop3" template pop3
  max-scan-bytes 30720
exit      
ips sigset "default_smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "default_telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "default_other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "default_other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "default_imap" template imap
  max-scan-bytes 30720
exit
ips sigset "default_finger" template finger
  max-scan-bytes 30720
exit
ips sigset "default_sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "default_nntp" template nntp
  max-scan-bytes 30720
exit      
ips sigset "default_tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "default_snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "default_mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "default_mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "default_oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "default_msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "default_netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "default_dhcp" template dhcp
  max-scan-bytes 30720
exit      
ips sigset "default_ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "default_voip" template voip
  max-scan-bytes 30720
exit
ips profile "no-ips"
exit
ips profile "predef_default"
  sigset "default_dns"
  sigset "default_ftp"
  sigset "default_http"
  sigset "default_pop3"
  sigset "default_smtp"
  sigset "default_telnet"
  sigset "default_other-tcp"
  sigset "default_other-udp"
  sigset "default_imap"
  sigset "default_finger"
  sigset "default_sunrpc"
  sigset "default_nntp"
  sigset "default_tftp"
  sigset "default_snmp"
  sigset "default_mysql"
  sigset "default_mssql"
  sigset "default_oracle"
  sigset "default_msrpc"
  sigset "default_netbios"
  sigset "default_dhcp"
  sigset "default_ldap"
  sigset "default_voip"
  filter-class 1 
    severity "Low" 
    severity "Medium" 
    severity "High" 
    action reset
  exit
exit
url-category "custom1"
exit
url-category "custom2"
exit
url-category "custom3"
exit
contentfilter
exit
url-profile "no-url"
exit      
track "track-ha"
  interface aggregate1 
exit
admin user "hillstone"
  password CDT7RjWRZRReD9GoTBIJYg6QYt
        password-expiration 1564688594
  role "admin"
  access console
  access ssh
  access https
exit
admin user "admin"
  password ztFlX39VM2o+D/5UhSqxnYRwOL
        password-expiration 1564688594
  role "admin"
  access console
  access ssh
  access https
exit
admin user "cslcnet"
  password Tzk2p3ItwmfRAT1i5FSXw0DQ4O
        password-expiration 1578971902
  access ssh
  access https
exit
logging event to syslog severity warnings
logging threat to buffer severity informational
logging threat to syslog custom-format  severity warnings
logging traffic session on
logging syslog ************ vrouter "trust-vr" udp 514 type event
logging syslog 4.255.235.1 vrouter "trust-vr" udp 514 type event
logging syslog *********** vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type config
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type network
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type threat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic session
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic nat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic pbr
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type sandbox
logging syslog additional-information
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "citrix"
  ip *********/24
exit
address "NTP"
  ip *******/32
exit
address "NFS_*********"
  ip *********/32
exit
address "NFS_*********"
  ip *********/32
exit
address "NFS_*********"
  ip *********/32
exit
address "NFS_*********"
  ip *********/32
exit
address "Group_10.x.0.0"
  ip 10.0.0.0/8
exit
address "**********"
  ip **********/32
exit
address "********-2"
  range ******** ********
exit
address "**********"
  ip **********/32
exit
address "********-3"
  range ******** ********
exit
address "**********"
  ip **********/32
exit
address "********-2"
  range ******** ********
exit
address "**********"
  ip **********/32
exit
address "********-4"
  range ******** ********
exit
address "**********/24"
  ip **********/24
exit
address "*******"
  ip *******/32
exit
address "***********-***********"
  range *********** ***********
exit
address "************-************"
  range ************ ************
exit
address "**********"
  ip **********/32
exit
address "*******"
  ip *******/32
exit
address "************-************"
  range ************ ************
exit
address "***********-***********"
  range *********** ***********
exit
address "***********-***********"
  range *********** ***********
exit
address "**********"
  ip **********/32
exit
address "*******10"
  ip *******10/32
exit
address "*********"
  ip *********/24
exit
address "************"
  ip ************/32
exit
address "************-15"
  range ************ ************
exit
address "********-9"
  range ******** ********
exit
address "************"
  ip ************/32
exit
address "**********-14"
  range ********** **********
exit
address "************-13"
  range ************ ************
exit
address "***********"
  ip ***********/32
exit
address "**********-2"
  ip **********/32
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "**********/21"
  ip **********/21
exit
address "**********/21"
  ip **********/21
exit
address "**********/21"
  ip **********/21
exit
address "***********/21"
  ip ***********/21
exit
address "**********/24"
  ip **********/24
exit
address "**********"
  ip **********/32
exit      
address "**********-2"
  range ********** 4.190.88.2
exit
address "*******/24"
  ip *******/24
exit
address "*********/16"
  ip *********/16
exit
address "**********2"
  ip **********2/32
exit
address "**********"
  ip **********/32
exit
address "***********/24"
  ip ***********/24
exit
zone "mgt"
  vrouter "mgt-vr"
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad land-attack action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad port-scan
  ad port-scan action alarm
  ad syn-flood
  ad syn-flood destination-threshold 50000
  ad syn-flood destination ip-based
  ad icmp-flood
  ad ip-sweep
  ad ip-sweep action alarm
  ad ping-of-death
  ad udp-flood
exit
zone "l2-untrust" l2
  type wan
exit      
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
zone "SSLtoSIE-trust"
  vrouter "SSLtoSIE-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "SSLtoSIE-untrust"
  vrouter "SSLtoSIE-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "juncai _untrust"
  vrouter "juncai _vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "juncai _trust"
  vrouter "juncai _vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
hostname "XWPCFW01"
admin host any any
web same-account-login enable
no https client-auth match
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit      

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit      

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit
          
isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit      

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface ethernet0/0 local
  zone  "mgt"
  ip address 4.255.253.61 *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet3/0
  aggregate aggregate1
exit      
interface xethernet3/1
  aggregate aggregate1
exit
interface xethernet5/0
  aggregate aggregate1
exit
interface xethernet5/1
  aggregate aggregate1
exit
interface aggregate1.101
  zone  "SSLtoSIE-untrust"
  ip address 4.255.198.2 255.255.255.252
  manage ping
  reverse-route prefer
exit
interface aggregate1.102
  zone  "SSLtoSIE-trust"
  ip address 4.255.198.6 255.255.255.252
  manage ping
  reverse-route prefer
exit
interface aggregate1.105
  zone  "juncai _untrust"
  ip address ***********0 255.255.255.252
  manage ping
  no reverse-route
exit
interface aggregate1.106
  zone  "juncai _trust"
  ip address ***********4 255.255.255.252
  manage ping
  no reverse-route
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 *************
exit
ip vrouter "SSLtoSIE-vr"
  ip route ********/24 ***********
  ip route *********/24 ***********
  ip route *********/24 ***********
  ip route ********/24 ***********
  ip route *********/24 ***********
  ip route *********/24 ***********
  ip route *********/24 ***********
  ip route ********/24 ***********
exit
ip vrouter "juncai _vr"
  ip route ***********/24 ***********3
  ip route *******/24 ***********3
  ip route *********/16 ***********
  ip route *********/16 ***********
  ip route *********/16 ***********
  ip route *********/16 ***********
  ip route *******/8 ***********3
  ip route *********/16 ***********
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp enable
ntp max-adjustment 0
ntp query-interval 1
ntp server ******* vrouter mgt-vr
clock zone china
rule id 28
  action permit
  src-zone "SSLtoSIE-untrust"
  dst-zone "SSLtoSIE-trust"
  src-addr "********-3"
  dst-addr "**********"
  service "ICMP"
  service "TCP-20050"
  name "第一组SSL访问SIE"
exit
rule id 29
  action permit
  src-zone "SSLtoSIE-untrust"
  dst-zone "SSLtoSIE-trust"
  src-addr "********-2"
  dst-addr "**********"
  service "ICMP"
  service "TCP-20050"
  name "第二组SSL访问SIE"
exit
rule id 30
  action permit
  src-zone "SSLtoSIE-untrust"
  dst-zone "SSLtoSIE-trust"
  src-addr "********-4"
  dst-addr "**********"
  service "ICMP"
  service "TCP-20050"
  name "第三组SSL访问SIE"
exit
rule id 31
  action permit
  src-zone "SSLtoSIE-untrust"
  dst-zone "SSLtoSIE-trust"
  src-addr "********-2"
  dst-addr "**********"
  service "ICMP"
  service "TCP-20050"
  name "第四组SSL访问SIE"
exit
rule id 25
  action permit
  src-zone "SSLtoSIE-untrust"
  dst-zone "SSLtoSIE-trust"
  src-range ******** ********
  dst-ip **********/32
  service "TCP-20050"
  service "ICMP"
  name "第五组SSL访问SIE"
exit
rule id 33
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "**********/24"
  src-addr "***********-***********"
  dst-addr "*******"
  service "tcp-34443"
  name "骏彩G3访问电彩SFTP"
exit
rule id 1
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "************-************"
  src-addr "***********-***********"
  dst-addr "**********"
  service "tcp-1520"
  service "tcp-1521"
  name "骏彩访问数据分析DB"
exit
rule id 2 
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "Any"
  dst-addr "*******"
  service "NTP"
  name "骏彩问NTP服务"
exit
rule id 5
  action permit
  src-zone "juncai _trust"
  dst-zone "juncai _untrust"
  src-addr "*********"
  src-addr "*******10"
  dst-addr "************"
  dst-addr "***********"
  service "TCP-80"
  name "QSCS&UMP访问骏彩UMP-AMS"
exit
rule id 4
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "***********-***********"
  src-addr "**********-2"
  src-addr "**********-2"
  dst-addr "**********"
  service "SSH"
  name "BASDB访问数据集成平台FTP服务器"
exit
rule id 6
  action permit
  src-zone "juncai _trust"
  dst-zone "juncai _untrust"
  src-addr "*********"
  dst-addr "************-15"
  service "TCP-443"
  service "HTTP"
  name "UMP访问骏彩MatServer"
exit
rule id 7
  action permit
  src-zone "juncai _trust"
  dst-zone "juncai _untrust"
  src-addr "********-9"
  dst-addr "************"
  service "TCP-80"
  name "SIE-proxy访问骏彩TRANSROUTER"
exit
rule id 8
  action permit
  src-zone "juncai _trust"
  dst-zone "juncai _untrust"
  src-addr "**********-14"
  dst-addr "************-13"
  service "TCP-8080"
  service "TCP- 52701"
  name "EX-Subscriber访问骏彩赛事推荐"
exit
rule id 9
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "***********-***********"
  dst-addr "*******"
  service "TCP- 21"
  name "骏彩RTQDB访问FTP服务器"
exit
rule id 3
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "************-************"
  dst-addr "Any"
  service "TCP-3389"
  service "TCP-139"
  service "TELNET"
  service "SSH"
  name "SOC"
exit
rule id 10
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "*********/24"
  src-addr "**********/21"
  src-addr "**********/21"
  src-addr "**********/21"
  src-addr "***********/21"
  src-addr "**********/24"
  dst-addr "**********"
  service "UDP 1812"
  name "骏彩访问Radius"
exit
rule id 11
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "*********/16"
  dst-addr "*******/24"
  service "Any"
  name "T-NFS"
exit
rule id 12
  action permit
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "**********"
  dst-addr "**********2"
  service "TCP_21050"
  name "juncai_impala"
exit
rule id 32
  action deny
  src-zone "juncai _untrust"
  dst-zone "juncai _trust"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "骏彩访问生产系统deny"
exit
rule id 13
  action permit
  src-zone "juncai _trust"
  dst-zone "juncai _untrust"
  src-addr "*********"
  dst-addr "***********/24"
  service "TCP-30000"
  service "ICMP"
  name "UMP-to-FG"
exit
rule id 19
  action deny
  log session-start
  log session-end
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "deny any"
exit
l2-nonip-action drop
no alg sqlnetv2
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server vrouter "mgt-vr"
snmp-server engineID "cslc_snmp"
snmp-server host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg ro
snmp-server host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg ro
snmp-server host *********** version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq ro
snmp-server trap-host ************ version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq port 162
snmp-server trap-host ************ version 2c community ckeI3SSNGQhyyPo3P+lMJmQ8mGku port 162
snmp-server trap-host *********** version 2c community DWbf06+a4P0FlgO+Yrtfp2YzSHMK port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
  icmp-unreachable-session-keep
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional
exit
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application
exit
query-groups
exit
longlife-sess-percent 8
no sms disable
ha link interface ethernet0/2
ha link interface ethernet0/1
ha link ip ******* *************
ha group 0
  monitor track "track-ha"
exit
ha cluster 2

End

XWPBFW03(M)# show config

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "WDGZ-vr"
exit
ip vrouter "TYDLPT_vr"
exit
ip vrouter "STQD_vr"
exit
ip vrouter "JGXT_vr"
exit
ip vrouter "AW_vr"
exit
ip vrouter "YXZX_vr"
exit
ip vrouter "QKL-vr"
exit
ip vrouter "ILMS_vr"
exit      
ip vrouter "XXFB_vrf"
exit
ip vrouter "YWJG_vrf"
exit
ip vrouter "PNUP_vrf"
exit
ip vrouter "CWZX_vrf"
exit
ip vrouter "CWYY_vrf"
exit
ip vrouter "SJZT_vrf"
exit
ip vrouter "SJJM_vrf"
exit
ip vrouter "CODING_vrf"
exit
ip vrouter "cs_vrf"
exit
ip vrouter "PWJC_vrf"
exit
ip vrouter "KFPT_vrf"
exit
ha group 0
exit      
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
zone "WDGZ-trust"
exit      
zone "WDGZ-untrust"
exit
zone "TYDLPT-trust"
exit
zone "TYDLPT-untrust"
exit
zone "STQD-untrust"
exit
zone "STQD-trust"
exit
zone "JGXT_trust"
exit
zone "JGXT_untrust"
exit
zone "AW_trust"
exit
zone "AW_untrust"
exit
zone "YXZX_trust"
exit
zone "YXZX_untrust"
exit
zone "QKL_trust"
exit      
zone "QKL_untrust"
exit
zone "ILMS-trust"
exit
zone "ILMS-untrust"
exit
zone "XXFB-trust"
exit
zone "XXFB-untrust"
exit
zone "YWJG-trust"
exit
zone "YWJG-untrust"
exit
zone "PNUP_trust"
exit
zone "PNUP_untrust"
exit
zone "CWZX_trust"
exit
zone "CWZX_untrust"
exit
zone "CWYY_trust"
exit      
zone "CWYY_untrust"
exit
zone "SJZT-trust"
exit
zone "SJZT-untrust"
exit
zone "SJJM-trust"
exit
zone "SJJM-untrust"
exit
zone "CODING-trust"
exit
zone "CODING-untrust"
exit
zone "cs-trust"
exit
zone "cs-untrust"
exit
zone "PWJC-trust"
exit
zone "PWJC-untrust"
exit
zone "KFPT-trust"
exit      
zone "KFPT-untrust"
exit
interface vswitchif1
exit
interface ethernet0/0 local
exit
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit
interface xethernet3/0
exit
interface xethernet3/1
exit
interface xethernet3/2
exit
interface xethernet3/3
exit
interface xethernet3/4
exit
interface xethernet3/5
exit      
interface xethernet3/6
exit
interface xethernet3/7
exit
interface xethernet5/0
exit
interface xethernet5/1
exit
interface xethernet5/2
exit
interface xethernet5/3
exit
interface xethernet5/4
exit
interface xethernet5/5
exit
interface xethernet5/6
exit
interface xethernet5/7
exit
interface aggregate1
exit
interface aggregate1.200
exit      
interface aggregate1.201
exit
interface aggregate1.202
exit
interface aggregate1.203
exit
interface aggregate1.204
exit
interface aggregate1.205
exit
interface aggregate1.206
exit
interface aggregate1.207
exit
interface aggregate1.208
exit
interface aggregate1.209
exit
interface aggregate1.210
exit
interface aggregate1.211
exit
interface aggregate1.212
exit      
interface aggregate1.213
exit
interface aggregate1.214
exit
interface aggregate1.215
exit
interface aggregate1.216
exit
interface aggregate1.217
exit
interface aggregate1.218
exit
interface aggregate1.219
exit
interface aggregate1.220
exit
interface aggregate1.221
exit
interface aggregate1.222
exit
interface aggregate1.223
exit
interface aggregate1.224
exit      
interface aggregate1.225
exit
interface aggregate1.226
exit
interface aggregate1.227
exit
interface aggregate1.228
exit
interface aggregate1.229
exit
interface aggregate1.230
exit
interface aggregate1.231
exit
interface aggregate1.232
exit
interface aggregate1.233
exit
interface aggregate1.234
exit
interface aggregate1.235
exit
interface aggregate1.236
exit      
interface aggregate1.237
exit
address "private_network"
exit
address "数据分析系统服务器1~15"
exit
address "网点感知APP"
exit
address "二中心zabbix监控"
exit
address "ctrix地址"
exit
address "安全接入集成平台"
exit
address "*********"
exit
address "**********-16"
exit
address "**********-74"
exit
address "**********"
exit
address "**********-87"
exit      
address "**********-43"
exit
address "**********-56"
exit
address "*******"
exit
address "***********-112"
exit
address "***********-122"
exit
address "*********/24"
exit
address "*********"
exit
address "**********"
exit
address "*********-*********"
exit
address "*********1"
exit
address "*********2-*********3"
exit
address "*********1"
exit      
address "**********-22"
exit
address "**********-32"
exit
address "**********"
exit
address "*********"
exit
address "*********/24"
exit
address "*********/24"
exit
address "*********-8"
exit
address "*********0- 12"
exit
address "**********-22"
exit
address "**********- 32"
exit
address "**********- 42"
exit
address "*********/24"
exit      
address "**********-14"
exit
address "***********-166"
exit
address "*********/24"
exit
address "*********/28"
exit
address "**********"
exit
address "*********15"
exit
address "*********12-113"
exit
address "*********22"
exit
address "*********41"
exit
address "*********32-133"
exit
address "*********52"
exit
address "*********01"
exit      
address "*********71"
exit
address "*********23"
exit
address "*********53"
exit
address "*********35"
exit
address "**********-62"
exit
address "*********31"
exit
address "**********"
exit
address "*********21"
exit
address "*********11"
exit
address "**********"
exit
address "*********12"
exit
address "**********"
exit      
address "*********51"
exit
address "*********0-12"
exit
address "**********-42"
exit
address "*******-4"
exit
address "*********14"
exit
address "*********34"
exit
address "*********61-164"
exit
address "***********"
exit
address "***********"
exit
address "**********-22"
exit
address "**********-98"
exit
address "**********"
exit      
address "**********"
exit
address "**********-13"
exit
address "**********-13"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "*********0"
exit
address "**********-65"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit      
address "**********"
exit
address "**********"
exit
address "***********"
exit
address "**********-13"
exit
address "**********"
exit
address "*********-2"
exit
address "*********-6"
exit
address "*********/24"
exit
address "***********-202"
exit
address "***********-212"
exit
address "*********"
exit
address "*********2"
exit      
address "************"
exit
address "*********/24"
exit
address "区块链应用服务器_4.27.31.2/32"
exit
address "区块链应用服务器_4.27.31.3/32"
exit
address "高频worm盘_3.9.10.90-94"
exit
address "区块链正向代理服务器_3.29.13.11-12"
exit
address "高频归集库_3.13.11.35"
exit
address "NET_*********"
exit
address "pcldb03_3.13.11.23"
exit
address "pcldb04_3.13.11.24"
exit
address "pcldb03-vip_3.13.11.33"
exit
address "pdldb04-vip_3.13.11.34"
exit      
address "二中心安全运维系统"
exit
address "***********"
exit
address "*********31"
exit
address "*********32"
exit
address "主中心STQD"
exit
address "*********01"
exit
address "*********02"
exit
address "*********/24"
exit
address "*********-38"
exit
address "************-94"
exit
address "营销中心DB"
exit
address "***********"
exit      
address "***********"
exit
address "ILMS_*********/24"
exit
address "ILMS_*********/24"
exit
address "即开citrix"
exit
address "报表集市-*********06"
exit
address "报表集市前置_3.29.8.11-12"
exit
address "*********10"
exit
address "*********11"
exit
address "*********12"
exit
address "4.24.10.x"
exit
address "***********"
exit
address "sleye_4.103.120.11-12"
exit      
address "**********-47"
exit
address "sleye_4.103.120.0"
exit
address "核心生产XCLCT"
exit
address "*********40-142"
exit
address "***********"
exit
address "**********-52"
exit
address "DIP贴源库"
exit
address "***********-102"
exit
address "*********-5"
exit
address "***********-130"
exit
address "************"
exit
address "**********"
exit      
address "************-112"
exit
address "**********"
exit
address "*********/24"
exit
address "**********1-16"
exit
address "4.12.70.81"
exit
address "4.26.10.20"
exit
address "4.12.70.50"
exit
address "4.26.10.81-82"
exit
address "3.12.41.50"
exit
address "18.5.127.222"
exit
address "**********"
exit
address "4.24.11.60"
exit      
address "4.26.10.21-22"
exit
address "***********"
exit
address "*********11"
exit
address "4.255.240.70"
exit
address "4.255.10.10-12"
exit
address "4.255.10.101-120"
exit
address "*********-6"
exit
address "**********1-202"
exit
address "4.20.1.101-102"
exit
address "4.28.10.201-202"
exit
address "4.28.10.111-113"
exit
address "4.28.10.116-118"
exit      
address "4.28.10.121-123"
exit
address "4.28.10.126-128"
exit
address "4.28.10.153-155"
exit
address "4.28.10.176-178"
exit
address "net_4.254.209.0"
exit
address "4.103.18.11-12"
exit
address "4.20.50.31-32"
exit
address "*********/24"
exit
address "*********/24"
exit
address "********/24"
exit
address "*********/32"
exit
address "***********/24"
exit      
address "*********-14"
exit
address "**********-34"
exit
address "************-12"
exit
address "**********-14"
exit
address "***********-162"
exit
address "**********"
exit
address "*********/24"
exit
address "************"
exit
address "**********-32"
exit
address "*************"
exit
address "*********-12"
exit
address "**********"
exit      
address "************-66"
exit
address "**********-69"
exit
address "**********"
exit
address "*********-6"
exit
address "*********30"
exit
address "*************-253"
exit
address "*********31-134"
exit
address "*********"
exit
address "***********-14"
exit
address "**********"
exit
address "***********-54"
exit
address "**********-13"
exit      
address "**********-24"
exit
address "***********-75"
exit
address "**********0"
exit
address "********-54"
exit
address "********-76"
exit
address "********-79"
exit
address "*********-206"
exit
address "*********-133"
exit
address "********-83"
exit
address "**********-62"
exit
address "***********-14"
exit
address "**********-12"
exit      
address "4.27.10.31-33"
exit
address "4.27.10.21-22"
exit
address "***********-14"
exit
address "3.27.13.91-96"
exit
address "********-79"
exit
address "4.27.41.71-73"
exit
address "**********-76"
exit
address "**********-83"
exit
address "***********-12"
exit
address "**********-43"
exit
address "*************"
exit
address "*********"
exit      
address "***********-192"
exit
address "***********-182"
exit
address "***********"
exit
address "*********/24"
exit
address "*********/24"
exit
address "**********"
exit
address "*********-2"
exit
address "*********-8"
exit
address "*********1-13"
exit
address "***********-2"
exit
address "**********-29"
exit
address "**********"
exit      
address "*********-2"
exit
address "*********"
exit
address "*********-8"
exit
address "**********-29"
exit
address "*********1-13"
exit
address "**********"
exit
address "***********-6"
exit
address "***********-4"
exit
address "实体渠道管理系统数据库隔离地址"
exit
address "**********0"
exit
address "**********"
exit
address "*********/24"
exit      
address "*********"
exit
address "*********"
exit
address "********-12"
exit
address "**********/24"
exit
address "**********-2"
exit
address "**********-2"
exit
address "*********"
exit
address "**********"
exit
address "********-93"
exit
address "********-62"
exit
address "***********"
exit
address "**********"
exit      
address "***********/24"
exit
address "**********"
exit
address "***********-22"
exit
address "***********-32"
exit
address "*********1-52"
exit
address "***********-2"
exit
address "*********/24"
exit
address "**********"
exit
address "**********-63"
exit
address "*********60"
exit
address "*********-47"
exit
address "***********-22"
exit      
address "***********-62"
exit
address "**********-28"
exit
address "***********-72"
exit
address "**********-38"
exit
address "***********-82"
exit
address "**********-88"
exit
address "***********-32"
exit
address "**********-22"
exit
address "**********-12"
exit
address "************-138"
exit
address "**********-42"
exit
address "XXFB_**********0-133"
exit      
address "**********"
exit
address "*******/8"
exit
address "10.0.0.0/8"
exit
address "*********01-202"
exit
address "*********11-212"
exit
address "**********"
exit
address "**********"
exit
address "*********-9"
exit
address "**********-61"
exit
address "**********"
exit
address "***********"
exit
address "*********/24"
exit      
address "**********0"
exit
address "**********"
exit
address "*********/24"
exit
address "**********-12"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "***********-102"
exit
address "**********0-112"
exit
address "**********1-122"
exit
address "***********-132"
exit      
address "***********"
exit
address "***********"
exit
address "**********-39"
exit
address "**********-48"
exit
address "*********01-106"
exit
address "************-54"
exit
address "**********-74"
exit
address "*******"
exit
address "*******"
exit
address "************-66"
exit
address "*********"
exit
address "************-202"
exit      
address "4.24.11.130"
exit
address "4.24.11.120"
exit
address "**********-22"
exit
address "**********-49"
exit
address "YYGS_4.98.9.0"
exit
address "4.9.1.23/32"
exit
address "4.9.1.25-27"
exit
address "4.24.12.101-102"
exit
address "4.24.12.201-202"
exit
address "4.24.12.181-182"
exit
address "4.24.12.161-162"
exit
address "4.24.12.221-222"
exit      
address "**********-24"
exit
address "***********-40"
exit
address "**********-54"
exit
address "************-************"
exit
address "*********12"
exit
address "**********/24"
exit
address "************-14"
exit
address "**********"
exit
address "***********-42"
exit
address "**********-42"
exit
address "***********"
exit
address "********"
exit      
address "*********"
exit
address "*********"
exit
address "*********"
exit
address "4.190.48.21-22"
exit
address "4.28.10.1-24"
exit
address "**********-42"
exit
address "**********-72"
exit
address "**********-82"
exit
address "**********-93"
exit
address "***********-113"
exit
address "***********-133"
exit
address "**********/32"
exit      
address "*********-135"
exit
address "***********-18"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "***********"
exit
address "*********"
exit
address "XXFB-VPDN-1"
exit
address "XXFB-VPDN-2"
exit
address "XXFB-VPDN-3"
exit
address "XXFB-VPDN-4"
exit
address "XXFB-IPTV"
exit      
address "**********"
exit
address "**********"
exit
address "************"
exit
address "**********/32"
exit
address "***********/32"
exit
address "**********/32"
exit
address "**********/32"
exit
address "************-************"
exit
address "***********-32"
exit
address "0415实体渠道"
exit
address "*********"
exit
address "**********-73"
exit      
address "***********-14"
exit
address "*********03-104"
exit
address "************/24"
exit
address "************/24"
exit
address "************/24"
exit
address "************/24"
exit
address "************/24"
exit
address "*********31-134"
exit
address "**********-16"
exit
address "***********-***********"
exit
address "**********/16"
exit
address "*********/24"
exit      
address "198.1.1.198-198.1.1.199"
exit
address "198.1.1.190-197"
exit
address "198.1.1.201-209"
exit
address "198.1.1.180"
exit
address "198.1.1.185"
exit
address "*********3"
exit
address "**********"
exit
address "**********-63"
exit
address "************/24"
exit
address "**********-247"
exit
address "**********"
exit
address "*********01-103"
exit      
address "**********"
exit
address "*********30"
exit
address "*********01-104"
exit
address "************/24"
exit
address "4.190.44.0/24"
exit
address "10.194.122.0/24"
exit
address "4.60.8.5"
exit
address "4.24.11.180"
exit
address "STQD_Addrss"
exit
address "4.255.240.119"
exit
address "4.255.240.120"
exit
address "***********0"
exit      
address "************"
exit
address "3.14.10.31-40"
exit
address "4.35.10.11-12"
exit
address "4.35.10.1-5"
exit
address "*********1-54"
exit
address "***********-54"
exit
address "4.35.21.0/24"
exit
address "4.35.21.10/32"
exit
address "***********-24"
exit
address "**********-38"
exit
address "3.22.16.101-102"
exit
address "3.22.16.111-112"
exit      
address "*********/24"
exit
address "*********/24"
exit
address "4.24.11.11-14"
exit
address "**********-55"
exit
address "4.24.11.171-174"
exit
address "4.103.12.21-22"
exit
address "4.24.11.171"
exit
address "************/24"
exit
address "*********61-172"
exit
address "4.20.50.50"
exit
address "4.13.20.40-4.13.20.42"
exit
address "4.190.124.11-14"
exit      
address "4.101.51.10"
exit
address "4.101.51.20"
exit
address "4.35.31.0/24"
exit
address "4.103.150.51-52"
exit
address "4.101.90.21-22"
exit
address "4.35.31.10"
exit
address "3.14.10.31-59"
exit
address "4.35.31.25"
exit
address "********/24"
exit
address "**********"
exit
address "************-66"
exit
address "************-98"
exit      
address "*********/24"
exit
address "3.252.101.2/32"
exit
address "**********-30"
exit
address "3.14.10.41-55"
exit
address "4.28.30.5"
exit
address "4.101.90.10-14"
exit
address "4.35.20.46-47"
exit
address "4.35.20.30"
exit
address "*********/24"
exit
address "************-192"
exit
address "10.194.135.7"
exit
address "4.28.10.30"
exit      
address "OMS"
exit
address "3.15.0.11-12"
exit
address "3.15.0.51-52"
exit
address "4.35.32.10"
exit
address "*********"
exit
address "css"
exit
address "css-api"
exit
address "4.35.31.21-24"
exit
address "4.35.33.10"
exit
address "4.35.33.0"
exit
address "4.24.12.111-112"
exit
address "4.103.16.90-94"
exit      
address "*********/24"
exit
address "4.10.10.41"
exit
address "4.28.10.181"
exit
address "*********/24"
exit
address "***********-13"
exit
address "***********/24"
exit
address "10.196.10.0/24"
exit
address "**********/19"
exit
address "4.190.0.43/32"
exit
address "*********-4"
exit
address "**********-63"
exit
address "4.190.48.61-62"
exit      
address "************"
exit
address "10.196.5.0/24"
exit
address "*************-212"
exit
address "**********"
exit
address "**********"
exit
address "***********/24"
exit
address "***********"
exit
address "***********/24"
exit
address "***********/24"
exit
address "***********/24"
exit
address "***********-16"
exit
address "*********-9"
exit      
aaa-server "local" type local
exit
track "track-ha"
exit
service "tcp-1521"
  tcp dst-port 1521 
exit
service "tcp-8080"
  tcp dst-port 8080 
exit
service "tcp-10050"
  tcp dst-port 10050 
exit
service "tcp-80"
  tcp dst-port 80 
exit
service "TCP-9876"
  tcp dst-port 9876 
exit
service "TCP-10911"
  tcp dst-port 10911 
exit
service "tcp-3306"
  tcp dst-port 3306 
exit
service "tcp-22"
  tcp dst-port 22 
exit
service "tcp-6379"
  tcp dst-port 6379 
exit
service "tcp-9000"
  tcp dst-port 9000 
exit
service "TCP-9091-9092"
  tcp dst-port 9091 9092 
exit
service "TCP-8075"
  tcp dst-port 8075 
exit
service "TCP-2181"
  tcp dst-port 2181 
exit
service "tcp-9080"
  tcp dst-port 9080 
exit
service "tcp-21"
  tcp dst-port 21 
exit
service "tcp7809"
  tcp dst-port 7809 
exit
service "TCP_7809-7820"
  tcp dst-port 7809 7820 
exit
service "tcp-23"
  tcp dst-port 23 
exit
service "tcp-161"
  tcp dst-port 161 
exit
service "tcp-3389"
  tcp dst-port 3389 
exit
service "tcp-8999"
  tcp dst-port 8999 
exit
service "tcp-443"
  tcp dst-port 443 
exit
service "tcp-162"
  tcp dst-port 162 
exit
service "tcp-514"
  tcp dst-port 514 
exit
service "tcp-8890"
  tcp dst-port 8890 
exit
service "tcp-8891"
  tcp dst-port 8891 
exit
service "tcp-10051"
  tcp dst-port 10051 
exit
service "UDP-161"
  udp dst-port 161 
exit
service "UDP-162"
  udp dst-port 162 
exit
service "UDP-514"
  udp dst-port 514 
exit
service "TCP-9090"
  tcp dst-port 9090 
exit
service "tcp-10080"
  tcp dst-port 10080 
exit
service "TCP-31306"
  tcp dst-port 31306 
exit
service "7809-7850"
  tcp dst-port 7809 7850 
exit
service "TCP_7809-7899"
  tcp dst-port 7809 7899 
exit
service "威胁感知"
  tcp dst-port 11000 
  tcp dst-port 11010 
  tcp dst-port 11020 
  tcp dst-port 11030 
  tcp dst-port 11040 
  tcp dst-port 11050 
  tcp dst-port 11060 
  tcp dst-port 11080 
exit
service "威胁感知端口"
  tcp dst-port 11000 
  tcp dst-port 11010 
  tcp dst-port 11020 
  tcp dst-port 11030 
  tcp dst-port 11040 
  tcp dst-port 11050 
  tcp dst-port 11060 
  tcp dst-port 11080 
exit
service "TCP-11090"
  tcp dst-port 11090 
exit
service "tcp-8330"
  tcp dst-port 8330 
exit
service "TCP-8010"
  tcp dst-port 8010 
exit
service "TCP-60200"
  tcp dst-port 60200 
exit
service "tcp-18086"
  tcp dst-port 18086 
exit      
service "tcp-28091"
  tcp dst-port 28091 
exit
service "tcp- 7901-7910"
  tcp dst-port 7901 7910 
exit
service "tcp-19080"
  tcp dst-port 19080 
exit
service "tcp-7004"
  tcp dst-port 7004 
exit
service "tcp-7005"
  tcp dst-port 7005 
exit
service "TCP-7070"
  tcp dst-port 7070 
exit
service "TCP-17070"
  tcp dst-port 17070 
exit
service "TCP-9333"
  tcp dst-port 9333 
exit      
service "TCP-19333"
  tcp dst-port 19333 
exit
service "tcp-26379"
  tcp dst-port 26379 
exit
service "tcp-61616"
  tcp dst-port 61616 
exit
service "tcp-2182"
  tcp dst-port 2182 
exit
service "tcp-6380-6389"
  tcp dst-port 6380 6389 
exit
service "tcp-8081"
  tcp dst-port 8081 
exit
service "tcp-34443"
  tcp dst-port 34443 
exit
service "tcp-18500"
  tcp dst-port 18500 
exit      
service "tcp-7001-7002"
  tcp dst-port 7001 7002 
exit
service "tcp-19876"
  tcp dst-port 19876 
exit
service "tcp-15001-15010"
  tcp dst-port 15001 15010 
exit
service "8090"
  tcp dst-port 8090 
exit
service "tcp-30035"
  tcp dst-port 30035 
exit
service "tcp-30036"
  tcp dst-port 30036 
exit
service "tcp-8000"
  tcp dst-port 8000 
exit
service "tcp-7000"
  tcp dst-port 7000 
exit      
service "TCP-3555"
  tcp dst-port 3555 
exit
service "tcp-8600"
  tcp dst-port 8600 
exit
service "tcp-20128"
  tcp dst-port 20128 
exit
service "TCP_2049"
  tcp dst-port 2049 
exit
service "TCP_111"
  tcp dst-port 111 
exit
service "UDP_4046"
  udp dst-port 4046 
exit
service "UDP_111"
  udp dst-port 111 
exit
service "TCP-19877"
  tcp dst-port 19877 
exit      
service "tcp-28080"
  tcp dst-port 28080 
exit
service "tcp-8090"
  tcp dst-port 8090 
exit
service "TCP-139"
  tcp dst-port 139 
exit
service "TCP_9200"
  tcp dst-port 9200 
exit
service "TCP- 6100-6200"
  tcp dst-port 6100 6200 
exit
service "TCP-27017"
  tcp dst-port 27017 
exit
service "tcp-8088"
  tcp dst-port 8088 
exit
service "tcp-30031-30034,tcp30037-30040"
  tcp dst-port 30031 30034 
  tcp dst-port 30037 30040 
exit
service "tcp-1556"
  tcp dst-port 1556 
exit
service "tcp-13724"
  tcp dst-port 13724 
exit
service "tcp-13782"
  tcp dst-port 13782 
exit
service "tcp-13720"
  tcp dst-port 13720 
exit
service "TCP_19092"
  tcp dst-port 19092 
exit
service "tcp-10081"
  tcp dst-port 10081 
exit
service "tcp-8080-8089"
  tcp dst-port 8080 8089 
exit
service "tcp-8850"
  tcp dst-port 8850 
exit
service "TCP-30040-30049"
  tcp dst-port 30040 30049 
exit
service "tcp-8089"
  tcp dst-port 8089 
exit
service "TCP_8040-8140"
  tcp dst-port 8040 8140 
exit
service "TCP-5601"
  tcp dst-port 5601 
exit
service "UDP_1812"
  udp dst-port 1812 
exit
service "tcp-10014"
  tcp dst-port 10014 
exit
service "TCP_30031-30049"
  tcp dst-port 30031 30049 
exit
service "TCP_21050"
  tcp dst-port 21050 
exit
service "tcp-10000"
  tcp dst-port 10000 
exit
ips sigset "dns" template dns
  max-scan-bytes 30720
exit
ips sigset "ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "telnet" template telnet
  max-scan-bytes 30720
exit      
ips sigset "other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "imap" template imap
  max-scan-bytes 30720
exit
ips sigset "finger" template finger
  max-scan-bytes 30720
exit
ips sigset "sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "snmp" template snmp
  max-scan-bytes 30720
exit      
ips sigset "mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "voip" template voip
  max-scan-bytes 30720
exit      
ips sigset "default_dns" template dns
  max-scan-bytes 30720
exit
ips sigset "default_ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "default_http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "default_pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "default_smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "default_telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "default_other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "default_other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "default_imap" template imap
  max-scan-bytes 30720
exit
ips sigset "default_finger" template finger
  max-scan-bytes 30720
exit
ips sigset "default_sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "default_nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "default_tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "default_snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "default_mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "default_mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "default_oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "default_msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "default_netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "default_dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "default_ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "default_voip" template voip
  max-scan-bytes 30720
exit
ips profile "no-ips"
exit
ips profile "predef_default"
  sigset "default_dns"
  sigset "default_ftp"
  sigset "default_http"
  sigset "default_pop3"
  sigset "default_smtp"
  sigset "default_telnet"
  sigset "default_other-tcp"
  sigset "default_other-udp"
  sigset "default_imap"
  sigset "default_finger"
  sigset "default_sunrpc"
  sigset "default_nntp"
  sigset "default_tftp"
  sigset "default_snmp"
  sigset "default_mysql"
  sigset "default_mssql"
  sigset "default_oracle"
  sigset "default_msrpc"
  sigset "default_netbios"
  sigset "default_dhcp"
  sigset "default_ldap"
  sigset "default_voip"
  filter-class 1 
    severity "Low" 
    severity "Medium" 
    severity "High" 
    action reset
  exit
exit
url-category "custom1"
exit
url-category "custom2"
exit
url-category "custom3"
exit
contentfilter
exit
url-profile "no-url"
exit
track "track-ha"
  interface aggregate1 
exit
admin user "hillstone"
  password rLVsR2Bkg5C2gATFN+0+1J7gE3
        password-expiration 1564690160
  role "admin"
  access console
  access ssh
  access https
exit
admin user "admin"
  password xlZtd2yH/yLuh1F25pADuYbgYV
        password-expiration 1564690160
  role "admin"
  access console
  access ssh
  access https
exit
admin user "cslcnet"
  password ArxvzXYCd6mBvvrAduPDtitQ6d
        password-expiration 1578972311
  access ssh
  access https
exit
no logging event to console
logging event to syslog severity warnings
logging threat to buffer severity informational
logging threat to syslog custom-format  severity warnings
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.235.1 vrouter "trust-vr" udp 514 type event
logging syslog *********** vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type config
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type network
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type threat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic session
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic nat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic pbr
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type sandbox
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "数据分析系统服务器1~15"
  range ************ ************
exit
address "网点感知APP"
  range ********** **********
exit
address "二中心zabbix监控"
  ip ***********/24
exit
address "ctrix地址"
  ip *********/24
exit
address "安全接入集成平台"
  ip *********/24
exit
address "*********"
  ip *********/16
exit      
address "**********-16"
  range ********** **********
exit
address "**********-74"
  range ********** **********
exit
address "**********"
  ip **********/32
exit
address "**********-87"
  range ********** **********
exit
address "**********-43"
  range ********** **********
exit
address "**********-56"
  range ********** **********
exit
address "*******"
  ip *******/32
exit
address "***********-112"
  range *********** *********12
exit      
address "***********-122"
  range *********** *********22
exit
address "*********/24"
  ip *********/24
exit
address "*********"
  ip *********/32
exit
address "**********"
  ip **********/32
exit
address "*********-*********"
  range ********* *********
exit
address "*********1"
  ip *********1/32
exit
address "*********2-*********3"
  range *********2 *********3
exit
address "*********1"
  ip *********1/32
exit      
address "**********-22"
  range ********** **********
exit
address "**********-32"
  range ********** **********
exit
address "**********"
  ip **********/32
exit
address "*********"
  ip *********/32
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "*********-8"
  range ********* *********
exit
address "*********0- 12"
  range *********0 *********2
exit      
address "**********-22"
  range ********** **********
exit
address "**********- 32"
  range ********** **********
exit
address "**********- 42"
  range ********** **********
exit
address "*********/24"
  ip *********/24
exit
address "**********-14"
  range ********** **********
exit
address "***********-166"
  range *********** ***********
exit
address "*********/24"
  ip *********/24
exit
address "*********/28"
  ip *********/28
exit      
address "**********"
  ip **********/32
exit
address "*********15"
  ip *********15/32
exit
address "*********12-113"
  range *********12 *********13
exit
address "*********22"
  ip *********22/32
exit
address "*********41"
  ip *********41/32
exit
address "*********32-133"
  range *********32 *********33
exit
address "*********52"
  ip *********52/32
exit
address "*********01"
  ip *********01/32
exit      
address "*********71"
  ip *********71/32
exit
address "*********23"
  ip *********23/32
exit
address "*********53"
  ip *********53/32
exit
address "*********35"
  ip *********35/32
exit
address "**********-62"
  range ********** 3.20.12.62
exit
address "*********31"
  ip *********31/32
exit
address "**********"
  ip **********/32
exit
address "*********21"
  ip *********21/32
exit      
address "*********11"
  ip *********11/32
exit
address "**********"
  ip **********/32
exit
address "*********12"
  ip *********12/32
exit
address "**********"
  ip **********/32
exit
address "*********51"
  ip *********51/32
exit
address "*********0-12"
  range *********0 *********2
exit
address "**********-42"
  range ********** **********
exit
address "*******-4"
  range ******* 4.9.6.4
exit      
address "*********14"
  ip *********14/32
exit
address "*********34"
  ip *********34/32
exit
address "*********61-164"
  range *********61 *********64
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "**********-22"
  range ********** 4.50.10.22
exit
address "**********-98"
  range ********** 4.27.13.98
exit
address "**********"
  ip **********/32
exit      
address "**********"
  ip **********/32
exit
address "**********-13"
  range ********** 3.52.37.13
exit
address "**********-13"
  range ********** 3.52.46.13
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "*********0"
  ip *********0/32
exit      
address "**********-65"
  range ********** 3.20.12.65
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "**********-13"
  range ********** 3.52.43.13
exit
address "**********"
  ip **********/32
exit      
address "*********-2"
  range ********* 4.20.26.2
exit
address "*********-6"
  range ********* 4.20.26.6
exit
address "*********/24"
  ip *********/24
exit
address "***********-202"
  ip ***********/32
  ip 4.20.25.202/32
exit
address "***********-212"
  ip ***********/32
  ip 4.20.25.211/32
  ip 4.20.25.212/32
exit
address "*********"
  ip *********/32
exit
address "*********2"
  ip *********2/32
exit      
address "************"
  ip ************/32
exit
address "*********/24"
  ip *********/24
exit
address "区块链应用服务器_4.27.31.2/32"
  ip 4.27.31.2/32
exit
address "区块链应用服务器_4.27.31.3/32"
  ip 4.27.31.3/32
exit
address "高频worm盘_3.9.10.90-94"
  range 3.9.10.90 3.9.10.94
exit
address "区块链正向代理服务器_3.29.13.11-12"
  range 3.29.13.11 3.29.13.12
exit
address "高频归集库_3.13.11.35"
  ip 3.13.11.35/32
exit
address "NET_*********"
  ip *********/24
exit      
address "pcldb03_3.13.11.23"
  ip 3.13.11.23/32
exit
address "pcldb04_3.13.11.24"
  ip 3.13.11.24/32
exit
address "pcldb03-vip_3.13.11.33"
  ip 3.13.11.33/32
exit
address "pdldb04-vip_3.13.11.34"
  ip 3.13.11.34/32
exit
address "二中心安全运维系统"
  ip 4.255.10.36/32
  range 4.20.10.10 4.20.10.12
  range 4.20.10.101 4.20.10.120
  range 4.255.10.10 4.255.10.12
  range 4.255.10.101 4.255.10.120
  range 4.255.10.131 4.255.10.138
  range 4.255.10.41 4.255.10.47
exit
address "***********"
  ip ***********/24
exit      
address "*********31"
  ip *********31/32
exit
address "*********32"
  ip *********32/32
exit
address "主中心STQD"
  ip 3.24.10.100/32
  ip ***********/32
  ip 3.24.10.102/32
  ip ***********/32
  range 3.24.10.71 3.24.10.76
exit
address "*********01"
  ip *********01/32
exit
address "*********02"
  ip *********02/32
exit
address "*********/24"
  ip *********/24
exit
address "*********-38"
  range ********* 3.29.2.38
exit
address "************-94"
  range ************ ************
exit
address "营销中心DB"
  range 4.28.10.111 ***********
  range 4.28.10.116 ***********
  range 4.28.10.121 ***********
  range 4.28.10.126 ***********
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "ILMS_*********/24"
  ip *********/24
exit
address "ILMS_*********/24"
  ip *********/24
exit
address "即开citrix"
  range 3.26.10.101 3.26.10.104
exit
address "报表集市-*********06"
  ip *********06/32
exit
address "报表集市前置_3.29.8.11-12"
  range 3.29.8.11 3.29.8.12
exit
address "*********11"
  ip *********11/32
exit
address "*********12"
  ip *********12/32
exit
address "4.24.10.x"
  range *********10 *********12
  range *********40 *********42
exit
address "***********"
  ip ***********/32
exit
address "sleye_4.103.120.11-12"
  range 4.103.120.11 4.103.120.12
exit
address "**********-47"
  range ********** *********7
exit
address "sleye_4.103.120.0"
  ip 4.103.120.0/24
exit
address "核心生产XCLCT"
  range ********** **********
  range ********** **********
exit
address "*********40-142"
  range *********40 *********42
exit
address "***********"
  ip ***********/32
exit
address "**********-52"
  range ********** 4.24.10.52
exit
address "DIP贴源库"
  ip 4.14.100.120/32
  range 4.14.100.101 ************
  range 4.14.100.110 4.14.100.112
exit
address "***********-102"
  range *********** 3.24.10.102
exit
address "*********-5"
  range ********* 4.28.10.5
exit
address "***********-130"
  range *********** **********0
exit
address "************"
  ip ************/32
exit
address "**********"
  ip **********/32
exit
address "************-112"
  range ************ 4.103.12.112
exit
address "**********"
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "**********1-16"
  range **********1 **********6
exit
address "4.12.70.81"
  ip 4.12.70.81/32
exit
address "4.26.10.20"
  ip 4.26.10.20/32
exit
address "4.12.70.50"
  ip 4.12.70.50/32
exit
address "4.26.10.81-82"
  range 4.26.10.81 4.26.10.82
exit
address "3.12.41.50"
  ip 3.12.41.50/32
exit
address "18.5.127.222"
  ip 18.5.127.222/32
exit
address "**********"
  ip **********/32
exit
address "4.24.11.60"
  ip 4.24.11.60/32
exit
address "4.26.10.21-22"
  range 4.26.10.21 4.26.10.22
exit
address "***********"
  ip ***********/32
exit
address "*********11"
  ip *********11/32
exit
address "4.255.240.70"
  ip 4.255.240.70/32
exit
address "4.255.10.10-12"
  range 4.255.10.10 4.255.10.12
exit
address "4.255.10.101-120"
  range 4.255.10.101 4.255.10.120
exit
address "*********-6"
  range ********* 4.20.26.6
exit
address "**********1-202"
  range **********1 **********2
exit
address "4.20.1.101-102"
  range 4.20.1.101 4.20.1.102
exit
address "4.28.10.201-202"
  range 4.28.10.201 4.28.10.202
exit
address "4.28.10.111-113"
  range 4.28.10.111 ***********
exit
address "4.28.10.116-118"
  range 4.28.10.116 ***********
exit
address "4.28.10.121-123"
  range 4.28.10.121 ***********
exit
address "4.28.10.126-128"
  range 4.28.10.126 ***********
exit
address "4.28.10.153-155"
  range 4.28.10.153 4.28.10.155
exit
address "4.28.10.176-178"
  range 4.28.10.176 4.28.10.178
exit
address "net_4.254.209.0"
  ip 4.254.209.0/24
exit
address "4.103.18.11-12"
  ip 4.103.18.11/32
  ip 4.103.18.12/32
  range 4.103.18.11 4.103.18.12
exit
address "4.20.50.31-32"
  ip 4.20.50.31/32
  ip 4.20.50.32/32
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "********/24"
  ip ********/24
exit
address "*********/32"
  ip *********/32
exit
address "***********/24"
  ip ***********/24
exit
address "*********-14"
  range ********* *********
exit
address "**********-34"
  range ********** **********
exit
address "************-12"
  range ************ ************
exit
address "**********-14"
  range ********** **********
exit
address "***********-162"
  range *********** ***********
exit
address "**********"
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "************"
  ip ************/32
exit
address "**********-32"
  range ********** **********
exit
address "*************"
  ip *************/32
exit
address "*********-12"
  range ********* *********2
exit
address "**********"
  ip **********/32
exit
address "************-66"
  range ************ ************
exit
address "**********-69"
  range ********** **********
exit
address "**********"
  ip **********/32
exit
address "*********-6"
  range ********* *********
exit
address "*********30"
  ip *********30/32
exit
address "*************-253"
  range ************* *************
exit
address "*********31-134"
  range *********31 *********34
exit
address "*********"
  ip *********/32
exit
address "***********-14"
  range *********** ***********
exit
address "**********"
  ip **********/32
exit
address "***********-54"
  range *********** ***********
exit
address "**********-13"
  range ********** **********
exit
address "**********-24"
  range ********** **********
exit
address "***********-75"
  range *********** ***********
exit
address "**********0"
  ip **********0/32
exit
address "********-54"
  range ******** ********
exit
address "********-76"
  range ******** ********
exit
address "********-79"
  range ******** ********
exit
address "*********-206"
  range ********* *********
exit
address "*********-133"
  range ********* *********
exit
address "********-83"
  range ******** ********
exit
address "**********-62"
  range ********** **********
exit
address "***********-14"
  range *********** ***********
exit
address "**********-12"
  range ********** **********
exit
address "4.27.10.31-33"
  range 4.27.10.31 4.27.10.33
exit
address "4.27.10.21-22"
  range 4.27.10.21 4.27.10.22
exit
address "***********-14"
  range *********** ***********
exit
address "3.27.13.91-96"
  range 3.27.13.91 3.27.13.96
exit
address "********-79"
  range ******** ********
exit
address "4.27.41.71-73"
  range 4.27.41.71 4.27.41.73
exit
address "**********-76"
  range ********** 4.27.41.76
exit
address "**********-83"
  range ********** 4.27.41.83
exit
address "***********-12"
  range *********** 4.103.21.12
exit
address "**********-43"
  range ********** 4.27.41.43
exit
address "*************"
  ip *************/32
exit
address "*********"
  ip *********/32
exit
address "***********-192"
  range *********** 4.27.41.192
exit
address "***********-182"
  range *********** ***********
exit
address "***********"
  ip ***********/32
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "**********"
  ip **********/32
exit
address "*********-2"
  range ********* 4.28.20.2
exit
address "*********-8"
  range ********* 4.28.20.8
exit
address "*********1-13"
  range *********1 *********3
exit
address "***********-2"
  range *********** ***********
exit
address "**********-29"
  range ********** 4.28.20.29
exit
address "**********"
  ip **********/32
exit
address "*********-2"
  range ********* 4.28.30.2
exit
address "*********"
  ip *********/32
exit
address "*********-8"
  range ********* 4.28.30.8
exit
address "**********-29"
  range ********** 4.28.30.29
exit
address "*********1-13"
  range *********1 *********3
exit
address "**********"
  ip **********/32
exit
address "***********-6"
  range *********** 198.3.100.6
exit
address "***********-4"
  range *********** 4.255.205.4
exit
address "实体渠道管理系统数据库隔离地址"
  ip *********12/32
  ip *********10/32
  range *********61 *********62
  range *********70 *********72
  range *********81 *********82
  range *********90 *********92
  range *********01 *********02
  range *********21 *********22
  range *********30 *********32
  range 4.24.12.101 4.24.12.102
  range 4.24.12.110 4.24.12.112
exit
address "**********0"
  ip **********0/32
exit
address "**********"
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "*********"
  ip *********/32
exit
address "*********"
  ip *********/32
exit
address "********-12"
  range ******** 4.9.5.12
exit      
address "**********/24"
  ip **********/24
exit
address "**********-2"
  range ********** 4.190.83.2
exit
address "**********-2"
  range ********** 4.101.52.2
exit
address "*********"
  ip *********/32
exit
address "**********"
  ip **********/32
exit
address "********-93"
  range ******** 3.9.4.93
exit
address "********-62"
  range ******** 3.9.4.62
exit
address "***********"
  ip ***********/32
exit      
address "**********"
  ip **********/32
exit
address "***********/24"
  ip ***********/24
exit
address "**********"
  ip **********/32
exit
address "***********-22"
  range *********** 4.103.17.22
exit
address "***********-32"
  range *********** 4.103.17.32
exit
address "*********1-52"
  range *********1 *********2
exit
address "***********-2"
  range *********** 4.255.205.2
exit
address "*********/24"
  ip *********/24
exit      
address "**********"
  ip **********/32
exit
address "**********-63"
  range ********** 3.98.13.63
exit
address "*********60"
  ip *********60/32
exit
address "*********-47"
  range ********* 4.60.8.47
exit
address "***********-22"
  range *********** 4.103.15.22
exit
address "***********-62"
  range *********** 4.103.12.62
exit
address "**********-28"
  range ********** **********
exit
address "***********-72"
  range *********** 4.103.12.72
exit      
address "**********-38"
  range ********** **********
exit
address "***********-82"
  range *********** 4.103.12.82
exit
address "**********-88"
  range ********** 4.24.11.88
exit
address "***********-32"
  range *********** 4.103.12.32
exit
address "**********-22"
  range ********** 4.24.13.22
exit
address "**********-12"
  range ********** 4.24.13.12
exit
address "************-138"
  range ************ 4.103.12.138
exit
address "**********-42"
  range ********** 4.24.13.42
exit      
address "**********"
  ip **********/32
exit
address "*******/8"
  ip *******/8
exit
address "10.0.0.0/8"
  ip 10.0.0.0/8
exit
address "*********01-202"
  range *********01 *********02
exit
address "*********11-212"
  range *********11 *********12
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "*********-9"
  range ********* 4.28.30.9
exit      
address "**********-61"
  range ********** 3.98.13.61
exit
address "**********"
  ip **********/32
exit
address "***********"
  ip ***********/32
exit
address "*********/24"
  ip *********/24
exit
address "**********0"
  ip **********0/32
exit
address "**********"
  ip **********/32
exit
address "*********/24"
  ip *********/24
exit
address "**********-12"
  range ********** 4.35.20.12
exit      
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********-102"
  range *********** 4.27.10.102
exit
address "**********0-112"
  range **********0 **********2
exit
address "**********1-122"
  range **********1 **********2
exit
address "***********-132"
  range *********** 4.27.10.132
exit      
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "**********-39"
  range ********** 4.28.20.39
exit
address "**********-48"
  range ********** 4.28.20.48
exit
address "*********01-106"
  range *********01 *********06
exit
address "************-54"
  range ************ ***********4
exit
address "**********-74"
  range ********** 4.13.70.74
exit
address "*******"
  ip *******/32
exit      
address "*******"
  ip *******/32
exit
address "************-66"
  range ************ ************
exit
address "*********"
  ip *********/32
exit
address "************-202"
  range ************ 4.103.12.202
exit
address "4.24.11.130"
  ip 4.24.11.130/32
exit
address "4.24.11.120"
  ip 4.24.11.120/32
exit
address "**********-22"
  range ********** 4.18.10.22
exit
address "**********-49"
  range ********** 4.18.10.49
exit      
address "YYGS_4.98.9.0"
  ip 4.98.9.0/24
exit
address "4.9.1.23/32"
  ip 4.9.1.23/32
exit
address "4.9.1.25-27"
  range 4.9.1.25 ********
exit
address "4.24.12.101-102"
  range 4.24.12.101 4.24.12.102
exit
address "4.24.12.201-202"
  range 4.24.12.201 4.24.12.202
exit
address "4.24.12.181-182"
  range 4.24.12.181 4.24.12.182
exit
address "4.24.12.161-162"
  range 4.24.12.161 4.24.12.162
exit
address "4.24.12.221-222"
  range 4.24.12.221 4.24.12.222
exit      
address "**********-24"
  range ********** 4.13.10.24
exit
address "***********-40"
  range *********** 4.14.100.40
exit
address "**********-54"
  range ********** **********
exit
address "************-************"
  range ************ ************
exit
address "*********12"
  ip *********12/32
exit
address "**********/24"
  ip **********/24
exit
address "************-14"
  range ************ 4.255.208.14
exit
address "**********"
  ip **********/32
exit      
address "***********-42"
  range *********** 4.190.88.42
exit
address "**********-42"
  range ********** 4.190.0.42
exit
address "***********"
  ip ***********/32
exit
address "********"
  ip ********/24
exit
address "*********"
  ip *********/24
exit
address "*********"
  ip *********/24
exit
address "*********"
  ip *********/32
exit
address "4.190.48.21-22"
  range 4.190.48.21 4.190.48.22
exit      
address "4.28.10.1-24"
  range 4.28.10.1 4.28.10.24
exit
address "**********-42"
  range ********** 4.20.27.42
exit
address "**********-72"
  range ********** 4.20.27.72
exit
address "**********-82"
  range ********** 4.20.27.82
exit
address "**********-93"
  range ********** 4.20.27.93
exit
address "***********-113"
  range *********** 4.20.27.113
exit
address "***********-133"
  range *********** 4.20.27.133
exit
address "**********/32"
  ip **********/32
exit      
address "*********-135"
  range ********* **********
exit
address "***********-18"
  range *********** ***********
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "***********"
  ip ***********/32
exit
address "*********"
  ip *********/32
exit
address "XXFB-VPDN-1"
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip **********/16
  ip **********/16
  ip *********/16
exit
address "XXFB-VPDN-2"
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
exit
address "XXFB-VPDN-3"
  ip *********/16
  ip *********/16
  ip *********/16
  ip **********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
  ip *********/16
exit
address "XXFB-VPDN-4"
  ip *********/16
  ip *********/16
exit
address "XXFB-IPTV"
  ip **********/32
  ip **********/32
  ip ********/32
  ip ********/32
  ip ********/32
  ip ********/32
  ip ********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip *********/32
  ip **********/32
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "************"
  ip ************/32
exit
address "**********/32"
  ip **********/32
exit
address "***********/32"
  ip ***********/32
exit
address "**********/32"
  ip **********/32
exit
address "**********/32"
  ip **********/32
exit
address "************-************"
  ip ************/32
  ip ************/32
  ip ************/32
exit
address "***********-32"
  range *********** ***********
exit
address "0415实体渠道"
  range ********** **********
  range ********** **********
  range ********** **********
exit
address "*********"
  ip *********/32
exit
address "**********-73"
  range ********** **********
exit      
address "***********-14"
  range *********** ***********
exit
address "*********03-104"
  range *********03 *********04
exit
address "************/24"
  ip ************/24
exit
address "************/24"
  ip ************/24
exit
address "************/24"
  ip ************/24
exit
address "************/24"
  ip ************/24
exit
address "************/24"
  ip ************/24
exit
address "*********31-134"
  range *********31 *********34
exit      
address "**********-16"
  range ********** **********
exit
address "***********-***********"
  ip ***********/32
  ip 4.24.11.177/32
  ip ***********/32
exit
address "**********/16"
  ip **********/16
exit
address "*********/24"
  ip *********/24
exit
address "198.1.1.198-198.1.1.199"
  ip 198.1.1.198/32
  ip 198.1.1.199/32
exit
address "198.1.1.190-197"
  range 198.1.1.190 198.1.1.197
exit
address "198.1.1.201-209"
  range 198.1.1.201 198.1.1.209
exit      
address "198.1.1.180"
  ip 198.1.1.180/32
exit
address "198.1.1.185"
  ip 198.1.1.185/32
exit
address "*********3"
  ip *********3/32
exit
address "**********"
  ip **********/16
exit
address "**********-63"
  range ********** 4.27.11.63
exit
address "************/24"
  ip ************/24
exit
address "**********-247"
  range ********** 3.9.20.247
exit
address "**********"
  ip **********/32
exit      
address "*********01-103"
  range *********01 *********03
exit
address "**********"
  ip **********/32
exit
address "*********30"
  ip *********30/32
exit
address "*********01-104"
  ip *********01/32
  ip *********02/32
  ip *********03/32
  ip *********04/32
exit
address "************/24"
  ip ************/24
exit
address "4.190.44.0/24"
  ip 4.190.44.0/24
exit
address "10.194.122.0/24"
  ip 10.194.122.0/24
exit      
address "4.60.8.5"
  ip 4.60.8.5/32
exit
address "4.24.11.180"
  ip 4.24.11.180/32
exit
address "STQD_Addrss"
  range ********** **********
  range ********** **********
  range ********** **********
  range *********** ***********
exit
address "4.255.240.119"
  ip 4.255.240.119/32
exit
address "4.255.240.120"
exit
address "***********0"
  ip ***********0/32
exit
address "************"
  ip ************/24
exit
address "3.14.10.31-40"
  range 3.14.10.31 3.14.10.40
exit
address "4.35.10.11-12"
  range 4.35.10.11 4.35.10.12
exit
address "4.35.10.1-5"
  range 4.35.10.1 *********
exit
address "*********1-54"
  range *********1 *********4
exit
address "***********-54"
  range *********** ***********
exit
address "4.35.21.0/24"
  ip 4.35.21.0/24
exit
address "4.35.21.10/32"
  ip 4.35.21.10/32
exit
address "***********-24"
  range *********** 4.14.100.24
exit
address "**********-38"
  range ********** 3.28.20.38
exit
address "3.22.16.101-102"
  range 3.22.16.101 3.22.16.102
exit
address "3.22.16.111-112"
  range 3.22.16.111 3.22.16.112
exit
address "*********/24"
  ip *********/24
exit
address "*********/24"
  ip *********/24
exit
address "4.24.11.11-14"
  range 4.24.11.11 4.24.11.14
exit
address "**********-55"
  range ********** 3.14.10.55
exit
address "4.24.11.171-174"
  range 4.24.11.171 4.24.11.174
exit
address "4.103.12.21-22"
  range 4.103.12.21 4.103.12.22
exit
address "4.24.11.171"
  ip 4.24.11.171/32
exit
address "************/24"
  ip ************/24
exit
address "*********61-172"
  range *********61 *********72
exit
address "4.20.50.50"
  ip 4.20.50.50/32
exit
address "4.13.20.40-4.13.20.42"
  range 4.13.20.40 4.13.20.42
exit
address "4.190.124.11-14"
  range 4.190.124.11 4.190.124.14
exit
address "4.101.51.10"
  ip 4.101.51.10/32
exit
address "4.101.51.20"
  ip 4.101.51.20/32
exit
address "4.35.31.0/24"
  ip 4.35.31.0/24
exit
address "4.103.150.51-52"
  range 4.103.150.51 4.103.150.52
exit
address "4.101.90.21-22"
  range 4.101.90.21 4.101.90.22
exit
address "4.35.31.10"
  ip 4.35.31.10/32
exit
address "3.14.10.31-59"
  range 3.14.10.31 3.14.10.59
exit
address "4.35.31.25"
  ip 4.35.31.25/32
exit
address "********/24"
  ip ********/24
exit
address "**********"
  ip **********/32
exit
address "************-66"
  range ************ ************
exit
address "************-98"
  range ************ 198.3.100.98
exit
address "*********/24"
  ip *********/24
exit
address "3.252.101.2/32"
  ip 3.252.101.2/32
exit
address "**********-30"
  range ********** 3.14.10.30
exit
address "3.14.10.41-55"
  range 3.14.10.41 3.14.10.55
exit
address "4.28.30.5"
  ip 4.28.30.5/32
exit
address "4.101.90.10-14"
  range 4.101.90.10 4.101.90.14
exit
address "4.35.20.46-47"
  range 4.35.20.46 4.35.20.47
exit
address "4.35.20.30"
  ip 4.35.20.30/32
exit
address "*********/24"
  ip *********/24
exit
address "************-192"
  range ************ 4.190.80.192
exit
address "10.194.135.7"
  ip 10.194.135.7/32
exit
address "4.28.10.30"
  ip 4.28.10.30/32
exit
address "OMS"
  ip 3.14.20.51/32
  ip 3.14.30.52/32
  ip 3.14.20.50/32
  ip 3.14.30.55/32
  ip 3.14.20.61/32
  ip 3.14.20.62/32
  ip 3.14.20.63/32
  ip 4.14.20.61/32
  ip 4.14.20.62/32
  ip 4.14.20.63/32
exit
address "3.15.0.11-12"
  range 3.15.0.11 3.15.0.12
exit
address "3.15.0.51-52"
  range 3.15.0.51 3.15.0.52
exit
address "4.35.32.10"
  ip 4.35.32.10/32
exit
address "*********"
  ip *********/24
exit
address "css"
  range *********86 *********88
exit
address "css-api"
  range *********81 *********83
exit
address "4.35.31.21-24"
  range 4.35.31.21 4.35.31.24
exit
address "4.35.33.10"
  ip 4.35.33.10/32
exit
address "4.35.33.0"
  ip 4.35.33.0/24
exit
address "4.24.12.111-112"
  range 4.24.12.111 4.24.12.112
exit
address "4.103.16.90-94"
  range 4.103.16.90 4.103.16.94
exit
address "*********/24"
  ip *********/24
exit
address "4.10.10.41"
  ip 4.10.10.41/32
exit
address "4.28.10.181"
  ip 4.28.10.181/32
exit
address "*********/24"
  ip *********/24
exit
address "***********-13"
  range *********** **********3
exit
address "***********/24"
  ip ***********/24
exit
address "10.196.10.0/24"
  ip 10.196.10.0/24
exit
address "**********/19"
  ip **********/19
exit
address "4.190.0.43/32"
  ip 4.190.0.43/32
exit
address "*********-4"
  ip *********/32
  ip 4.60.13.4/32
exit      
address "**********-63"
  ip **********/32
  ip 4.35.32.62/32
  ip 4.35.32.63/32
exit
address "4.190.48.61-62"
  range 4.190.48.61 4.190.48.62
exit
address "************"
  ip ************/32
exit
address "10.196.5.0/24"
  ip 10.196.5.0/24
exit
address "*************-212"
  range ************* 4.190.121.212
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "***********/24"
  ip ***********/24
exit
address "***********"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "***********/24"
  ip ***********/24
exit
address "***********-16"
  range *********** 4.103.17.16
exit
address "*********-9"
  range ********* 4.60.14.9
exit
zone "mgt"
  vrouter "mgt-vr"
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
zone "WDGZ-trust"
  vrouter "WDGZ-vr"
  description "网点感知"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "WDGZ-untrust"
  vrouter "WDGZ-vr"
  description "网点感知"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "TYDLPT-trust"
  vrouter "TYDLPT_vr"
  description "安全接入集成平台"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "TYDLPT-untrust"
  vrouter "TYDLPT_vr"
  description "安全接入集成平台"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "STQD-untrust"
  vrouter "STQD_vr"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "STQD-trust"
  vrouter "STQD_vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "JGXT_trust"
  vrouter "JGXT_vr"
  description "监管系统trust"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "JGXT_untrust"
  vrouter "JGXT_vr"
  description "监管系统untrust"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "AW_trust"
  vrouter "AW_vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "AW_untrust"
  vrouter "AW_vr"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "YXZX_trust"
  vrouter "YXZX_vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "YXZX_untrust"
  vrouter "YXZX_vr"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "QKL_trust"
  vrouter "QKL-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "QKL_untrust"
  vrouter "QKL-vr"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "ILMS-trust"
  vrouter "ILMS_vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "ILMS-untrust"
  vrouter "ILMS_vr"
  ad icmp-flood
  ad icmp-flood action alarm
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad udp-flood action alarm
  ad syn-flood
  ad syn-flood action alarm
  ad syn-flood destination ip-based
  ad ip-sweep
  ad ip-sweep action alarm
  ad port-scan
  ad port-scan action alarm
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-fragment action alarm
  ad ip-option
  ad ip-option action alarm
  ad ip-spoofing
  ad ip-directed-broadcast
  ad ip-directed-broadcast action alarm
  ad winnuke
  ad land-attack
  ad land-attack action alarm
exit
zone "XXFB-trust"
  vrouter "XXFB_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "XXFB-untrust"
  vrouter "XXFB_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "YWJG-trust"
  vrouter "YWJG_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "YWJG-untrust"
  vrouter "YWJG_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "PNUP_trust"
  vrouter "PNUP_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "PNUP_untrust"
  vrouter "PNUP_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CWZX_trust"
  vrouter "CWZX_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CWZX_untrust"
  vrouter "CWZX_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CWYY_trust"
  vrouter "CWYY_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CWYY_untrust"
  vrouter "CWYY_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "SJZT-trust"
  vrouter "SJZT_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "SJZT-untrust"
  vrouter "SJZT_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "SJJM-trust"
  vrouter "SJJM_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "SJJM-untrust"
  vrouter "SJJM_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CODING-trust"
  vrouter "CODING_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "CODING-untrust"
  vrouter "CODING_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "cs-trust"
  vrouter "cs_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "cs-untrust"
  vrouter "cs_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "PWJC-trust"
  vrouter "PWJC_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "PWJC-untrust"
  vrouter "PWJC_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "KFPT-trust"
  vrouter "KFPT_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "KFPT-untrust"
  vrouter "KFPT_vrf"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad udp-flood destination-threshold 1500
  ad udp-flood source-threshold 1500
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
hostname "XWPBFW03"
admin host any any
web same-account-login enable
no https client-auth match
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit      

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit      

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit
          
isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit      

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface ethernet0/0 local
  zone  "mgt"
  ip address ************ *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet3/0
  aggregate aggregate1
exit      
interface xethernet3/1
  aggregate aggregate1
exit
interface xethernet5/0
  aggregate aggregate1
exit
interface xethernet5/1
  aggregate aggregate1
exit
interface aggregate1
  zone  "trust"
  lacp enable
  no reverse-route
exit
interface aggregate1.200
  zone  "WDGZ-trust"
  ip address *********** ***************
  manage ping
  no reverse-route
exit
interface aggregate1.201
  zone  "WDGZ-untrust"
  ip address *********** ***************
  manage ping
  no reverse-route
exit
interface aggregate1.202
  zone  "TYDLPT-trust"
  ip address ***********0 ***************
  manage ping
  no reverse-route
exit
interface aggregate1.203
  zone  "TYDLPT-untrust"
  ip address ***********4 ***************
  manage ping
  no reverse-route
exit
interface aggregate1.204
  zone  "STQD-trust"
  ip address ***********8 ***************
  manage ping
  no reverse-route
exit
interface aggregate1.205
  zone  "STQD-untrust"
  ip address ***********2 ***************
  manage ping
  no reverse-route
exit
interface aggregate1.206
  zone  "JGXT_trust"
  ip address ***********6 ***************
  manage ping
  no reverse-route
exit
interface aggregate1.207
  zone  "JGXT_untrust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.208
  zone  "AW_trust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.209
  zone  "AW_untrust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.210
  zone  "YXZX_trust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.211
  zone  "YXZX_untrust"
  ip address ************ ***************
  manage ping
  no reverse-route
exit
interface aggregate1.212
  zone  "QKL_trust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.213
  zone  "QKL_untrust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.214
  zone  "ILMS-trust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.215
  zone  "ILMS-untrust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.216
  zone  "XXFB-trust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.217
  zone  "XXFB-untrust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.218
  zone  "YWJG-trust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.219
  zone  "YWJG-untrust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.220
  zone  "PNUP_trust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.221
  zone  "PNUP_untrust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.222
  zone  "CWZX_trust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.223
  zone  "CWZX_untrust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.224
  zone  "CWYY_trust"
  ip address ************ ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.225
  zone  "CWYY_untrust"
  ip address ***********02 ***************
  manage ping
  reverse-route prefer
exit
interface aggregate1.226
  zone  "SJZT-trust"
  ip address ************* ***************
  reverse-route prefer
exit
interface aggregate1.227
  zone  "SJZT-untrust"
  ip address ************* ***************
  reverse-route prefer
exit
interface aggregate1.228
  zone  "SJJM-trust"
  ip address ************* ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
interface aggregate1.229
  zone  "SJJM-untrust"
  ip address ************* ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
interface aggregate1.230
  zone  "CODING-trust"
  ip address ************* ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
interface aggregate1.231
  zone  "CODING-untrust"
  ip address ***********26 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  reverse-route prefer
exit
interface aggregate1.232
  zone  "cs-trust"
  ip address ************* ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.233
  zone  "cs-untrust"
  ip address ************4 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.234
  zone  "PWJC-trust"
  ip address ************* ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.235
  zone  "PWJC-untrust"
  ip address ***********50 ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.236
  zone  "KFPT-trust"
  ip address ************* ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
interface aggregate1.237
  zone  "KFPT-untrust"
  ip address ************* ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  manage ping
  no reverse-route
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 *************
exit
ip vrouter "WDGZ-vr"
  ip route 0.0.0.0/0 *********** description "网点感知缺省"
  ip route *********/24 *********** description "网点感知trust回程路由"
exit
ip vrouter "TYDLPT_vr"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ***********
exit
ip vrouter "STQD_vr"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ************
  ip route *********/24 ************
  ip route *********/24 ************
  ip route *********/24 ************
exit
ip vrouter "JGXT_vr"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ************
  ip route *********/24 ************
exit
ip vrouter "AW_vr"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ************
exit
ip vrouter "YXZX_vr"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ************
exit
ip vrouter "QKL-vr"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ************
exit
ip vrouter "ILMS_vr"
  ip route 0.0.0.0/0 ***********1
  ip route *********/24 ************
  ip route *********/24 ************
exit
ip vrouter "XXFB_vrf"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ************
exit
ip vrouter "YWJG_vrf"
  ip route 0.0.0.0/0 ************
  ip route *********/24 ************
exit
ip vrouter "PNUP_vrf"
  ip route 0.0.0.0/0 4.255.197.85
  ip route *********/24 4.255.197.81
exit      
ip vrouter "CWZX_vrf"
  ip route 0.0.0.0/0 ***********3
  ip route *********/24 4.255.197.89
exit
ip vrouter "CWYY_vrf"
  ip route 0.0.0.0/0 ***********01
  ip route *********/24 ***********7
exit
ip vrouter "SJZT_vrf"
  ip route 0.0.0.0/0 ***********09
  ip route *********/24 ***********05
exit
ip vrouter "SJJM_vrf"
  ip route 0.0.0.0/0 ***********17
  ip route *********/24 ***********13
  ip route 4.35.21.0/24 ***********13
exit
ip vrouter "CODING_vrf"
  ip route 0.0.0.0/0 ***********25
  ip route *********/24 ***********21
exit
ip vrouter "cs_vrf"
  ip route 0.0.0.0/0 ************3
  ip route 4.35.31.0/24 ***********29
exit
ip vrouter "PWJC_vrf"
  ip route 0.0.0.0/0 ***********49
  ip route *********/24 ***********45
exit
ip vrouter "KFPT_vrf"
  ip route 0.0.0.0/0 ***********57
  ip route 4.35.33.0/24 ***********53
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp enable
ntp server ******* vrouter mgt-vr
clock zone china
rule id 169
  action deny
  src-zone "STQD-trust"
  dst-zone "STQD-untrust"
  src-addr "*********32"
  src-addr "*********31"
  dst-addr "***********"
  service "Any"
  name "实体渠道管理系统数据分析库到贴源库隔离"
exit
rule id 166
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-ip ********/32
  src-ip 4.9.1.26/32
  src-ip 4.9.1.25/32
  src-ip 4.9.1.23/32
  dst-addr "**********"
  dst-addr "**********"
  dst-range *********** ***********
  dst-range **********1 **********2
  dst-range *********** 4.27.10.102
  dst-range *********31 *********32
  dst-range *********01 *********02
  dst-range ********** 4.50.10.22
  dst-range *********** 4.20.25.202
  dst-range ********* 4.20.26.6
  dst-range *********21 *********22
  dst-range *********61 *********62
  dst-range *********81 *********82
  dst-range *********01 *********02
  dst-range 4.24.12.101 4.24.12.102
  service "Any"
  name "数据库备份"
exit
rule id 68
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "二中心安全运维系统"
  src-ip 4.255.235.250/32
  dst-addr "Any"
  service "Any"
  name "二中心安全运维系统"
exit
rule id 62
  action permit
  disable 
  src-zone "Any"
  dst-zone "Any"
  src-addr "ctrix地址"
  dst-addr "Any"
  service "Any"
  description "8.26"
  name "citrix访问所有设备"
exit
rule id 4
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "ICMP"
exit
rule id 22
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-ip 198.3.8.102/32
  dst-addr "Any"
  service "tcp-22"
exit      
rule id 98
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "4.255.240.70"
  src-addr "*************"
  src-addr "4.255.240.120"
  src-addr "4.255.240.119"
  src-ip 4.255.240.50/32
  dst-addr "Any"
  service "Any"
  description "8.26"
  name "漏洞扫描"
exit
rule id 54
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***********/24"
  dst-addr "Any"
  service "tcp-443"
  service "tcp-1521"
  service "tcp-8999"
  service "tcp-3389"
  service "tcp-161"
  service "tcp-23"
  service "tcp-22"
  service "UDP-161"
  name "SOC平台访问生产服务器2区策略"
exit
rule id 55
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "***********/24"
  service "tcp-8891"
  service "tcp-8890"
  service "tcp-514"
  service "tcp-162"
  name "生产服务器2区访问SOC平台"
exit
rule id 58
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "二中心zabbix监控"
  service "tcp-10051"
  name "访问二中心zbbix监控"
exit
rule id 59
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "二中心zabbix监控"
  dst-addr "Any"
  service "tcp-10050"
  service "TCP-9333"
  service "TCP-7070"
  service "tcp-8081"
  service "tcp-8080"
  service "tcp-8090"
  name "二中心zabbix监控访问所有设备"
exit
rule id 60
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-ip 4.255.209.24/32
  dst-ip 4.255.209.23/32
  dst-ip ************/32
  service "UDP-514"
  service "UDP-162"
  service "UDP-161"
  name "二中心设备访问 ominibus probe"
exit
rule id 61
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "二中心zabbix监控"
  dst-addr "Any"
  service "ICMP"
exit
rule id 63
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-ip 4.255.100.0/24
  dst-addr "Any"
  service "Any"
  name "自动化装机访问所有"
exit
rule id 1 
  action permit
  disable
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-ip 3.13.11.22/32
  src-ip *********/24
  dst-ip *********/24
  service "Any"
  description "8.26"
  name "ctrix访问网点感知"
exit
rule id 6
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-ip 3.21.13.31/32
  dst-addr "网点感知APP"
  service "tcp-8080"
  name "网点感知nginx访问APP"
exit
rule id 5
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "数据分析系统服务器1~15"
  dst-ip *********/32
  service "tcp-1521"
  name "数据分析系统访问网点感知"
exit
rule id 7
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "二中心zabbix监控"
  dst-ip *********/24
  service "tcp-10050"
  name "zbbix监控访问网点感知"
exit
rule id 2
  action permit
  src-zone "WDGZ-trust"
  dst-zone "WDGZ-untrust"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "网点感知trust-untrust01"
exit
rule id 8 
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "*********"
  dst-addr "安全接入集成平台"
  service "Any"
  name "ctrix访问安全接入集成平台"
exit
rule id 9
  action permit
  src-zone "TYDLPT-trust"
  dst-zone "TYDLPT-untrust"
  src-addr "安全接入集成平台"
  dst-addr "Any"
  service "Any"
  name "安全接入管理平台trust-untrust01"
exit
rule id 10
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "**********-16"
  src-addr "***********-14"
  dst-addr "**********-74"
  service "tcp-80"
  service "tcp-9080"
  name "安全接入管理平台untrust-trust01"
exit
rule id 11
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "**********"
  src-addr "**********-87"
  dst-addr "**********-43"
  service "TCP-9876"
  name "安全接入管理平台untrust-trust02"
exit
rule id 12
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "**********"
  src-addr "**********-87"
  dst-addr "**********-56"
  service "TCP-10911"
  name "安全接入管理平台untrust-trust03"
exit      
rule id 13
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "*******"
  dst-addr "***********-112"
  dst-addr "***********-122"
  service "tcp-22"
  service "tcp-3306"
  name "安全接入管理平台untrust-trust04"
exit
rule id 14
  action permit
  src-zone "STQD-trust"
  dst-zone "STQD-untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "实体渠道trust-untrust01"
exit
rule id 16
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********"
  dst-addr "*********/24"
  dst-addr "*********/24"
  service "Any"
  name "ctrix访问安全接入集成平台1"
exit
rule id 17
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********"
  dst-addr "**********"
  service "tcp-6379"
  name "实体渠道应用服务器访问Redis"
exit
rule id 18
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********"
  dst-addr "*********-*********"
  service "tcp-3306"
  name "实体渠道应用服务器访问Mysql"
exit
rule id 19
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********"
  dst-addr "*********1"
  service "tcp-9000"
  name "实体渠道应用服务器访问数据分析服务1"
exit
rule id 20
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********"
  dst-addr "*********2-*********3"
  service "TCP-9091-9092"
  name "实体渠道应用服务器访问数据分析服务2-3"
exit
rule id 21
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********"
  dst-addr "*********1"
  service "TCP-2181"
  service "TCP-8075"
  service "tcp-8080"
  name "运营辅助服务器"
exit
rule id 26
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "**********-32"
  src-addr "**********-22"
  src-addr "**********"
  dst-addr "***********-112"
  dst-addr "***********-122"
  service "tcp-22"
  service "tcp-1521"
  name "安全接入管理平台untrust-trust05"
exit
rule id 27
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "**********-22"
  src-addr "**********-32"
  src-addr "**********"
  dst-addr "*********"
  service "tcp-22"
  service "tcp-1521"
  name "网点感知untrust-trust05"
exit
rule id 28
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "*********"
  dst-addr "*********/24"
  dst-addr "*********/24"
  service "Any"
  name "ctrix访问监管系统"
exit
rule id 29
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********-22"
  src-addr "**********-32"
  src-addr "**********"
  dst-addr "*********-8"
  dst-addr "*********0- 12"
  dst-addr "**********-22"
  dst-addr "**********- 32"
  dst-addr "**********- 42"
  service "tcp-22"
  service "tcp-1521"
  name "归集库访问监管系统数据库"
exit
rule id 30
  action permit
  src-zone "JGXT_trust"
  dst-zone "JGXT_untrust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "监管系统trust-untrust01"
exit
rule id 31
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "*********"
  dst-addr "*********/24"
  service "Any"
  name "ctrix访问AW系统"
exit
rule id 32
  action permit
  src-zone "AW_trust"
  dst-zone "AW_untrust"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "AW访问untrust"
exit
rule id 33
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "**********-16"
  dst-addr "**********-14"
  service "tcp-8080"
  name "统一接入平台访问AW"
exit      
rule id 34
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "***********-166"
  src-addr "*********/24"
  src-addr "*********/28"
  src-addr "**********"
  src-addr "**********"
  src-ip **********/32
  src-ip **********/32
  dst-addr "*********15"
  dst-addr "*********32-133"
  dst-addr "*********22"
  dst-addr "*********23"
  dst-addr "*********21"
  dst-addr "*********12"
  dst-addr "*********30"
  service "tcp-8080"
  name "JGXT-untrust-trust-01"
exit
rule id 35
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "*********/28"
  src-addr "*********/24"
  dst-addr "*********12-113"
  service "tcp-8080"
  name "JGXT-untrust-trust-02"
exit
rule id 36
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "*********/28"
  src-addr "*********/24"
  src-addr "**********/24"
  src-addr "********/24"
  src-addr "*********/24"
  dst-addr "*********22"
  dst-addr "*********41"
  dst-addr "*********32-133"
  dst-addr "*********52"
  dst-addr "*********01"
  dst-addr "*********71"
  service "tcp-8080"
  name "JGXT-untrust-trust-03"
exit
rule id 37
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "*********/24"
  src-addr "*********/28"
  dst-addr "*********12-113"
  service "tcp-21"
  name "JGXT-untrust-trust-04"
exit
rule id 38
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "*********/28"
  src-ip **********/32
  src-ip **********/32
  dst-addr "*********23"
  dst-addr "*********53"
  dst-addr "*********35"
  service "tcp-21"
  name "JGXT-untrust-trust-05"
exit      
rule id 39
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "Any"
  dst-addr "*********31"
  service "tcp-8080"
  name "JGXT-untrust-trust-06"
exit
rule id 40
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  dst-addr "*********21"
  service "tcp-8080"
  name "JGXT-untrust-trust-07"
exit
rule id 41
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  dst-addr "*********11"
  service "tcp-8080"
  name "JGXT-untrust-trust-08"
exit
rule id 42
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  dst-addr "*********12"
  dst-addr "*********01"
  service "tcp-8080"
  name "JGXT-untrust-trust-09"
exit
rule id 43
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "*********15"
  dst-addr "*********53"
  dst-addr "*********51"
  service "tcp-8080"
  name "JGXT-untrust-trust-10"
exit
rule id 44
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "*********0-12"
  service "tcp-1521"
  name "JGXT-untrust-trust-11"
exit
rule id 45
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********-62"
  dst-addr "**********-42"
  service "tcp-1521"
  name "JGXT-untrust-trust-12"
exit
rule id 46
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "*******-4"
  dst-addr "*********71"
  dst-addr "*********35"
  dst-addr "*********32-133"
  dst-addr "*********31"
  dst-addr "*********23"
  dst-addr "*********22"
  dst-addr "*********21"
  dst-addr "*********41"
  dst-addr "*********51"
  dst-addr "*********52"
  dst-addr "*********53"
  dst-addr "*********01"
  dst-addr "*********11"
  dst-addr "*********12"
  dst-addr "*********15"
  dst-addr "*********12-113"
  dst-addr "*********14"
  dst-addr "*********34"
  dst-addr "*********61-164"
  service "tcp-22"
  service "tcp-80"
  service "tcp-8080"
  service "tcp-1521"
  service "tcp-21"
  name "JGXT-untrust-trust-13"
exit
rule id 47
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "*******-4"
  dst-addr "**********-14"
  service "tcp-80"
  service "tcp-8080"
  service "tcp-22"
  service "tcp-1521"
  service "tcp-21"
  name "AW- untrust-trust02"
exit
rule id 48
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "***********"
  src-addr "***********"
  src-addr "**********-22"
  src-addr "**********-32"
  src-addr "************"
  src-addr "*********/24"
  dst-addr "**********-22"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  service "TCP_7809-7820"
  service "tcp-1521"
  name "AW- untrust-trust03"
exit
rule id 49
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "**********-98"
  src-addr "**********-16"
  src-addr "***********-14"
  src-addr "**********"
  src-addr "**********-247"
  src-addr "************/24"
  dst-addr "**********"
  service "tcp-8080"
  name "AW- untrust-trust04"
exit
rule id 15
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  dst-addr "*********14"
  service "tcp-8080"
  name "JGXT-untrust-trust-14"
exit
rule id 23
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  dst-addr "*********35"
  service "tcp-8080"
  name "JGXT-untrust-trust-15"
exit
rule id 24
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********-13"
  src-addr "**********-13"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "*********0"
  service "tcp-1521"
  name "JGXT-untrust-trust-16"
exit
rule id 25
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********-65"
  dst-addr "**********"
  service "tcp-1521"
  name "JGXT-untrust-trust-17"
exit
rule id 50
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********-65"
  src-addr "**********-13"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********-62"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "***********"
  src-addr "**********-13"
  src-addr "**********"
  src-addr "**********-13"
  src-addr "4.101.51.10"
  src-addr "4.101.51.20"
  dst-addr "**********- 32"
  dst-addr "*********0- 12"
  dst-addr "*********0-12"
  dst-addr "*********-2"
  dst-addr "*********-6"
  service "tcp-1521"
  name "JGXT-untrust-trust-18"
exit
rule id 52
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********-22"
  src-addr "**********-32"
  dst-addr "***********-202"
  dst-addr "***********-212"
  service "tcp-1521"
  name "JGXT-untrust-trust-19"
exit
rule id 53
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "*******-4"
  dst-addr "*********"
  dst-addr "*********2"
  service "tcp-1521"
  service "tcp-80"
  service "tcp-8080"
  service "tcp-22"
  service "tcp-21"
  name "安全接入管理平台untrust-trust06"
exit      
rule id 86
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "sleye_4.103.120.0"
  dst-addr "*********/24"
  service "HTTP"
  name "统一业务监管平台访问容器平台"
exit
rule id 56
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********"
  dst-addr "*********/24"
  service "Any"
  name "ctrix访问营销中心"
exit
rule id 57
  action permit
  src-zone "YXZX_trust"
  dst-zone "YXZX_untrust"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "营销中心trust-untrust"
exit
rule id 65
  action permit
  src-zone "QKL_trust"
  dst-zone "QKL_untrust"
  src-addr "区块链应用服务器_4.27.31.3/32"
  src-addr "区块链应用服务器_4.27.31.2/32"
  dst-addr "区块链正向代理服务器_3.29.13.11-12"
  service "tcp-443"
exit
rule id 144
  action permit
  src-zone "QKL_trust"
  dst-zone "QKL_untrust"
  src-addr "区块链应用服务器_4.27.31.3/32"
  src-addr "区块链应用服务器_4.27.31.2/32"
  dst-addr "***********-12"
  service "tcp-443"
exit
rule id 64
  action permit
  src-zone "QKL_trust"
  dst-zone "QKL_untrust"
  src-addr "区块链应用服务器_4.27.31.2/32"
  src-addr "区块链应用服务器_4.27.31.3/32"
  dst-addr "高频worm盘_3.9.10.90-94"
  service "Any"
exit
rule id 66
  action permit
  src-zone "QKL_trust"
  dst-zone "QKL_untrust"
  src-addr "区块链应用服务器_4.27.31.2/32"
  src-addr "区块链应用服务器_4.27.31.3/32"
  dst-addr "高频归集库_3.13.11.35"
  dst-addr "pcldb04_3.13.11.24"
  dst-addr "pdldb04-vip_3.13.11.34"
  dst-addr "pcldb03_3.13.11.23"
  dst-addr "pcldb03-vip_3.13.11.33"
  service "tcp-1521"
exit
rule id 67
  action permit
  src-zone "QKL_untrust"
  dst-zone "QKL_trust"
  src-addr "NET_*********"
  dst-addr "区块链应用服务器_4.27.31.2/32"
  dst-addr "区块链应用服务器_4.27.31.3/32"
  service "tcp-22"
exit
rule id 69
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "***********"
  dst-addr "*********/24"
  service "TCP-9090"
  name "监控访问营销中心"
exit
rule id 72
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "主中心STQD"
  dst-addr "*********31"
  dst-addr "*********32"
  dst-addr "*********01"
  dst-addr "*********02"
  service "tcp-1521"
  service "tcp7809"
  name "STQD2"
exit
rule id 70
  action permit
  log session-start
  log session-end
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********"
  src-addr "DIP贴源库"
  src-addr "***********-102"
  dst-addr "*********31"
  dst-addr "*********32"
  service "7809-7850"
  service "tcp-1521"
  service "FTP"
  service "SSH"
  name "STQD"
exit
rule id 73
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "*********01"
  dst-addr "*********02"
  dst-addr "4.24.10.x"
  service "tcp-1521"
  name "STQD3"
exit
rule id 85
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********"
  dst-addr "*********01"
  dst-addr "*********02"
  service "7809-7850"
  service "tcp-1521"
  name "STQD5"
exit
rule id 74
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********/24"
  src-addr "*********-38"
  src-addr "**********-55"
  dst-addr "*********31"
  dst-addr "*********32"
  service "tcp-1521"
  name "STQD4"
exit
rule id 75
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "********/24"
  src-ip ***********/32
  dst-range ********** **********
  service "tcp-10080"
  service "TCP-60200"
  name "骏彩开放平台访问营销中心"
exit
rule id 76
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "***********-14"
  src-range ********** **********
  dst-range ********** **********
  service "tcp-8080"
  service "tcp-9080"
  service "TCP-9090"
  name "统一登录平台访问营销中心"
exit
rule id 77
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "************-94"
  dst-addr "营销中心DB"
  service "TCP-31306"
  name "数据中台访问营销中心"
exit
rule id 78
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "***********"
  src-addr "***********"
  dst-addr "*********/24"
  service "SSH"
  name "运营公司访问营销中心"
exit      
rule id 80
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "************-94"
  src-addr "************"
  dst-addr "***********-212"
  dst-addr "*********0-12"
  dst-addr "**********-22"
  dst-addr "**********- 32"
  service "tcp-1521"
  name "数据中台访问监管系统DB"
exit
rule id 81
  action permit
  src-zone "ILMS-trust"
  dst-zone "ILMS-untrust"
  src-addr "ILMS_*********/24"
  src-addr "ILMS_*********/24"
  dst-addr "Any"
  service "Any"
  name "印务trust-untrust"
exit
rule id 82
  action permit
  src-zone "ILMS-untrust"
  dst-zone "ILMS-trust"
  src-addr "即开citrix"
  dst-addr "ILMS_*********/24"
  dst-addr "ILMS_*********/24"
  service "Any"
  name "即开citrix访问"
exit
rule id 83
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "**********-16"
  src-addr "***********-14"
  src-addr "*********31-134"
  dst-addr "报表集市-*********06"
  dst-addr "*********01-104"
  service "tcp-8080"
  name "统一登录平台访问报表集市"
exit
rule id 84
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "报表集市前置_3.29.8.11-12"
  dst-addr "报表集市-*********06"
  service "tcp-8080"
  name "报表集市前置访问报表集市"
exit
rule id 87
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "核心生产XCLCT"
  src-addr "OMS"
  dst-addr "*********31"
  dst-addr "*********32"
  dst-addr "*********01"
  dst-addr "*********02"
  dst-addr "*********10"
  dst-addr "*********11"
  dst-addr "*********12"
  dst-addr "*********40-142"
  service "SSH"
  service "tcp-1521"
  name "核心生产XCLCT访问实体渠道库"
exit      
rule id 88
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********"
  src-addr "********/24"
  src-addr "************"
  dst-addr "**********-52"
  service "HTTP"
  name "骏彩开放平台访问实体渠道NGINX"
exit
rule id 89
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "DIP贴源库"
  dst-addr "**********-22"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  service "tcp-1521"
  service "TCP_7809-7899"
  service "FTP"
  service "SSH"
  name "贴源库访问AWDB"
exit
rule id 90
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*******-4"
  dst-addr "*********-5"
  dst-addr "***********-130"
  service "TCP-31306"
  service "SSH"
  name "jenkins访问容器"
exit
rule id 91
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************"
  dst-addr "*********1"
  dst-addr "**********"
  service "威胁感知"
  service "TCP-11090"
  name "威胁感知"
exit      
rule id 92
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************-112"
  src-addr "***********-14"
  dst-addr "**********"
  service "tcp-8330"
  name "实体渠道地图服务"
exit
rule id 93
  action permit
  src-zone "ILMS-untrust"
  dst-zone "ILMS-trust"
  src-addr "**********1-16"
  src-addr "4.12.70.81"
  src-addr "**********0"
  src-addr "**********"
  dst-addr "4.26.10.20"
  dst-addr "4.26.10.21-22"
  service "tcp-6379"
  name "ETL服务器访问前置数据库"
exit
rule id 94
  action permit
  src-zone "ILMS-untrust"
  dst-zone "ILMS-trust"
  src-addr "4.12.70.50"
  src-addr "3.12.41.50"
  src-addr "*********-12"
  src-addr "4.24.11.11-14"
  src-addr "*********61-172"
  src-range *********31 *********34
  dst-addr "4.26.10.81-82"
  dst-addr "**********"
  service "tcp-8080"
  name "即开游戏导入工具客户端访问外部接口服务器"
exit
rule id 95
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "18.5.127.222"
  src-addr "********/24"
  src-addr "css"
  src-addr "css-api"
  dst-addr "**********"
  service "tcp-8330"
  service "tcp-8080"
  name "开放平台访问代销者系统接口服务"
exit
rule id 96
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "18.5.127.222"
  src-addr "********/24"
  src-addr "************/24"
  src-addr "************"
  src-addr "************/24"
  dst-addr "4.24.11.60"
  service "tcp-8330"
  name "开放平台访问实名服务"
exit
rule id 97
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "***********"
  dst-addr "*********11"
  service "tcp-3306"
  name "数据中台访问报表数据库"
exit
rule id 99
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "4.255.10.10-12"
  src-addr "4.255.10.101-120"
  dst-addr "**********-22"
  dst-addr "*********"
  dst-addr "*********-6"
  dst-addr "**********1-202"
  service "tcp-1521"
  name "OCS访问数据库1521"
exit
rule id 100
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "4.255.10.101-120"
  src-addr "4.255.10.10-12"
  src-addr "4.35.20.46-47"
  dst-addr "***********-122"
  dst-addr "***********-112"
  dst-addr "4.20.1.101-102"
  dst-addr "4.28.10.201-202"
  service "tcp-3306"
  name "OCS访问数据库3306"
exit
rule id 101
  action permit
  src-zone "Any"
  dst-zone "YXZX_trust"
  src-addr "4.255.10.101-120"
  src-addr "4.255.10.10-12"
  dst-addr "4.28.10.111-113"
  dst-addr "4.28.10.116-118"
  dst-addr "4.28.10.121-123"
  dst-addr "4.28.10.126-128"
  dst-addr "4.28.10.153-155"
  dst-addr "4.28.10.176-178"
  service "TCP-31306"
  name "OCS访问数据库31306"
exit
rule id 103
  action permit
  src-zone "ILMS-untrust"
  dst-zone "ILMS-trust"
  src-addr "4.103.18.11-12"
  src-addr "3.12.41.50"
  src-addr "4.12.70.50"
  src-range 4.103.18.21 4.103.18.22
  dst-addr "4.20.50.31-32"
  service "TCP-8010"
  name "流程子系统代理服务器访问流程子系统web服务器"
exit
rule id 104
  action permit
  src-zone "XXFB-trust"
  dst-zone "XXFB-untrust"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "XXFB -trust-to- untrust"
exit
rule id 105
  action permit
  src-zone "YWJG-trust"
  dst-zone "YWJG-untrust"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
  name "YWJG-trust-to-untrust"
exit
rule id 106
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问渠道管理"
exit
rule id 107
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问监管"
exit
rule id 108
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问网点感知"
exit
rule id 109
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问营销中心"
exit
rule id 110
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问实体渠道"
exit
rule id 111
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问机构用户"
exit
rule id 112
  action permit
  src-zone "ILMS-untrust"
  dst-zone "ILMS-trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问即开前置"
exit
rule id 113
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********-14"
  src-addr "**********-16"
  src-addr "**********-34"
  dst-addr "**********-47"
  service "tcp-80"
  name "拉新和电子投注单nginx、usap访问容器平台treafik"
exit
rule id 114
  action permit
  src-zone "AW_untrust"
  dst-zone "AW_trust"
  src-addr "************-12"
  dst-addr "**********-14"
  service "tcp-8080"
  name "应用运维访问AW"
exit
rule id 115
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "***********-162"
  src-addr "**********-22"
  dst-addr "**********"
  service "tcp-18086"
  name "MIPS访问拉新系统"
exit
rule id 117
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问信息发布"
exit
rule id 116
  action permit
  src-zone "QKL_untrust"
  dst-zone "QKL_trust"
  src-addr "*********/32"
  dst-addr "Any"
  service "tcp-22"
  name "自动化访问QKL"
exit
rule id 119
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "************"
  dst-addr "**********"
  service "tcp-28091"
  name "数据中台访问业务监管"
exit
rule id 118
  action permit
  src-zone "PNUP_trust"
  dst-zone "PNUP_untrust"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "PNUP-trust-to-untrust"
exit
rule id 122
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "**********-22"
  src-addr "**********-32"
  src-addr "***********-2"
  src-addr "***********-6"
  dst-addr "***********-112"
  service "tcp- 7901-7910"
  name "数据中台访问业务监管-1"
exit
rule id 125
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "************-66"
  dst-addr "**********"
  service "tcp-28091"
  name "数据中台访问业务监管-2"
exit
rule id 126
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "**********-69"
  src-addr "**********-49"
  src-addr "************/24"
  src-addr "************"
  dst-addr "**********"
  service "tcp-10080"
  name "数据中台访问业务监管-3"
exit
rule id 127
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "*********-6"
  src-addr "4.190.48.21-22"
  src-addr "**********"
  src-addr "**********-247"
  src-addr "************/24"
  src-addr "4.190.44.0/24"
  src-addr "10.194.122.0/24"
  src-addr "4.60.8.5"
  src-addr "************/24"
  src-addr "***********0"
  src-addr "************-12"
  src-addr "*********61-172"
  src-addr "4.190.124.11-14"
  src-addr "************/24"
  src-addr "***********/24"
  src-addr "10.196.10.0/24"
  src-addr "4.190.48.61-62"
  src-range *********01 *********02
  dst-addr "*********30"
  service "tcp-19080"
  name "骏彩Matserver访问USAP"
exit
rule id 128
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "*************-253"
  dst-addr "*********30"
  service "tcp-80"
  name "内联SSL网关访问USAP"
exit
rule id 129
  action permit
  src-zone "TYDLPT-trust"
  dst-zone "TYDLPT-untrust"
  src-addr "*********31-134"
  dst-addr "*********"
  service "tcp-7004"
  name "USAP访问骏彩Matserver"
exit
rule id 131
  action permit
  src-zone "TYDLPT-trust"
  dst-zone "TYDLPT-untrust"
  src-addr "**********-98"
  dst-addr "*********"
  service "tcp-7005"
  name "USAP访问骏彩Matserver-1"
exit      
rule id 132
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "***********-14"
  src-addr "***********-18"
  dst-addr "**********"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  service "tcp-8080"
  name "api反向代理访问数据服务"
exit
rule id 133
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "***********-54"
  dst-addr "**********-13"
  service "TCP-17070"
  service "TCP-7070"
  name "dmz区数据节点访问核心生产master"
exit      
rule id 134
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "***********-54"
  dst-addr "**********-24"
  service "TCP-19333"
  service "TCP-9333"
  name "dmz区数据节点访问核心生产数据节点"
exit
rule id 135
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "***********-75"
  dst-addr "**********0"
  service "tcp-8080"
  name "H5走势图访问数据服务"
exit
rule id 136
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "********-54"
  src-addr "********-76"
  src-addr "********-79"
  src-addr "*********-206"
  src-addr "*********-133"
  src-addr "********-83"
  src-addr "***********-54"
  src-addr "3.27.13.91-96"
  dst-addr "**********-62"
  service "tcp-26379"
  service "tcp-6379"
  name "API等数据服务访问redis"
exit
rule id 137
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "***********-14"
  src-addr "***********-14"
  src-addr "*********31-134"
  dst-addr "**********-47"
  service "tcp-80"
  name "拉新入口nginx访问容器平台treafik"
exit
rule id 138
  action permit
  src-zone "PNUP_untrust"
  dst-zone "PNUP_trust"
  src-addr "*********/24"
  dst-addr "**********-12"
  service "tcp-61616"
  name "容器平台访问pnup_mq"
exit
rule id 139
  action permit
  src-zone "PNUP_untrust"
  dst-zone "PNUP_trust"
  src-addr "*********/24"
  dst-addr "4.27.10.31-33"
  service "tcp-2182"
  name "容器平台访问zookeeper"
exit
rule id 140
  action permit
  src-zone "PNUP_untrust"
  dst-zone "PNUP_trust"
  src-addr "*********/24"
  dst-addr "4.27.10.21-22"
  service "tcp-6380-6389"
  name "容器平台访问pnup_redis"
exit
rule id 141
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "*********-133"
  src-addr "********-83"
  src-addr "3.27.13.91-96"
  src-addr "********-79"
  dst-addr "4.27.41.71-73"
  dst-addr "**********-43"
  service "tcp-8080"
  service "tcp-8081"
  name "主中心访问二中心table"
exit
rule id 142
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "3.27.13.91-96"
  src-addr "*********-133"
  src-addr "********-79"
  dst-addr "**********-76"
  service "tcp-8080"
  name "主中心访问二中心arthur"
exit
rule id 143
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "*********-133"
  dst-addr "**********-83"
  service "tcp-8080"
  name "主中心访问二中心flow"
exit
rule id 149
  action permit
  src-zone "CWZX_trust"
  dst-zone "CWZX_untrust"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "财务中心对外访问"
exit
rule id 150
  action permit
  src-zone "CWYY_trust"
  dst-zone "CWYY_untrust"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "财务运营对外访问"
exit
rule id 151
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/24"
  src-addr "********-12"
  src-addr "**********-2"
  src-addr "**********-2"
  src-addr "*********"
  src-addr "**********"
  src-addr "************-54"
  src-addr "**********-74"
  src-addr "***********"
  src-addr "**********-55"
  src-addr "************/24"
  dst-addr "**********"
  service "tcp-34443"
  name "财务中心SFTP"
exit
rule id 152
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********-2"
  dst-addr "**********-47"
  service "tcp-80"
  name "财务中心访问容器"
exit
rule id 153
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/24"
  dst-addr "*********-8"
  service "tcp-18500"
  name "容器访问财务中心注册中心"
exit
rule id 154
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/24"
  dst-addr "*********1-13"
  service "tcp-7001-7002"
  name "容器访问财务中心redis"
exit
rule id 155
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/24"
  dst-addr "**********-29"
  service "tcp-19876"
  service "TCP-19877"
  name "容器访问财务中心rocketMq"
exit
rule id 156
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/24"
  dst-addr "**********"
  service "tcp-15001-15010"
  name "容器访问财务中心tdsql-proxy"
exit
rule id 157
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********-2"
  dst-addr "**********-47"
  service "tcp-80"
  name "财务运营访问容器"
exit
rule id 183
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********/24"
  src-addr "*********/24"
  dst-addr "**********"
  dst-addr "*********/24"
  service "tcp-30036"
  service "tcp-30035"
  service "tcp-30031-30034,tcp30037-30040"
  service "TCP-30040-30049"
exit
rule id 158
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/24"
  dst-addr "*********"
  service "8090"
  name "容器访问财务运营"
exit
rule id 159
  action permit
  src-zone "CWYY_untrust"
  dst-zone "CWYY_trust"
  src-addr "*********/24"
  dst-addr "*********-8"
  service "tcp-18500"
  name "容器访问财务运营注册中心"
exit
rule id 160
  action permit
  src-zone "CWYY_untrust"
  dst-zone "CWYY_trust"
  src-addr "*********/24"
  dst-addr "**********-29"
  service "tcp-19876"
  name "容器访问财务运营rocketMq"
exit      
rule id 162
  action permit
  src-zone "CWYY_untrust"
  dst-zone "CWYY_trust"
  src-addr "*********/24"
  dst-addr "*********1-13"
  service "tcp-7001-7002"
  name "容器访问财务运营rocket"
exit
rule id 163
  action permit
  src-zone "CWYY_untrust"
  dst-zone "CWYY_trust"
  src-addr "*********/24"
  dst-addr "**********"
  service "tcp-15001-15010"
  name "容器访问财务运营tdsql-proxy"
exit
rule id 164
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "***********-4"
  dst-addr "Any"
  service "tcp-1521"
  service "TCP-31306"
  service "tcp-3306"
  service "tcp-34443"
  service "tcp-22"
  name "应用运维jenkins访问所有主机"
exit
rule id 170
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "核心生产XCLCT"
  dst-addr "***********-192"
  dst-addr "***********-182"
  service "Any"
  name "信息发布开通相关访问需求"
exit
rule id 171
  action permit
  src-zone "SJZT-trust"
  dst-zone "SJZT-untrust"
  src-addr "*********/24"
  dst-addr "Any"
  service "Any"
  name "数据中台出访策略"
exit
rule id 172
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "*********"
  src-addr "************"
  dst-addr "*********"
  service "tcp-22"
  name "数据中台二中心SFTP服务器访问数据集成平台虚拟机"
exit
rule id 173
  action permit
  src-zone "PNUP_untrust"
  dst-zone "PNUP_trust"
  src-addr "*********/32"
  dst-addr "*********/24"
  service "tcp-22"
  name "自动化访问拉新"
exit
rule id 174
  action permit
  src-zone "CWYY_untrust"
  dst-zone "CWYY_trust"
  src-addr "*********/32"
  dst-addr "*********/24"
  service "tcp-22"
  name "自动化访问财务运营"
exit
rule id 175
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/32"
  src-addr "10.194.135.7"
  dst-addr "**********"
  service "tcp-34443"
  name "自动化访问财务中心"
exit
rule id 176
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "********-93"
  src-addr "********-62"
  dst-addr "**********-62"
  dst-addr "**********-24"
  service "tcp-22"
  name "主中心信息发布数据迁移"
exit
rule id 284
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "**********-55"
  dst-addr "***********"
  service "tcp-1521"
  name "SJZT_to_***********"
exit
rule id 285
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "**********-55"
  dst-addr "4.28.10.201-202"
  service "tcp-3306"
  name "SJZT_to_4.28.10.201-202"
exit
rule id 177
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "********-83"
  src-addr "*********-133"
  src-addr "*********-206"
  src-addr "********-76"
  src-addr "********-54"
  dst-addr "***********-192"
  service "tcp-1521"
  name "主中心信息发布访问数据库"
exit
rule id 178
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "*********-133"
  src-addr "*********-206"
  src-addr "********-76"
  src-addr "********-54"
  dst-addr "**********"
  service "tcp-8080"
  name "主中心信息发布访问seaweed"
exit
rule id 179
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "***********/24"
  dst-addr "*********0"
  dst-addr "**********"
  service "tcp-1521"
  name "数据中台访问监管数据库"
exit
rule id 180
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "***********-22"
  dst-addr "报表集市-*********06"
  service "tcp-8080"
  name "二中心办公网前置nginx访问运营报表BI"
exit
rule id 181
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "***********-32"
  src-addr "198.1.1.198-198.1.1.199"
  src-addr "198.1.1.185"
  src-addr "198.1.1.180"
  src-addr "198.1.1.201-209"
  src-addr "198.1.1.190-197"
  dst-addr "*********1-52"
  dst-addr "*********3"
  service "tcp-8081"
  service "tcp-8080"
  service "HTTP"
  service "tcp-8080-8089"
  name "二中心互联网nginx访问数据中台代理"
exit
rule id 182
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "***********-2"
  dst-addr "*********/24"
  service "TCP-7070"
  service "tcp-8081"
  service "tcp-8080"
  name "jenkins访问信息发布系统"
exit
rule id 184
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "DIP贴源库"
  dst-addr "***********-112"
  service "SSH"
  service "tcp- 7901-7910"
  name "贴源库访问USADB"
exit
rule id 185
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********/24"
  dst-addr "**********"
  service "tcp-8330"
  name "数字化体育彩票app访问第三方接口"
exit
rule id 186
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********-47"
  dst-addr "*********60"
  dst-addr "**********"
  dst-addr "**********"
  service "tcp-8330"
  name "开放平台访问第三方接口"
exit
rule id 187
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********-22"
  dst-addr "*********60"
  dst-addr "**********"
  service "tcp-8330"
  name "拉新nginx访问第三方接口"
exit
rule id 188
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********-62"
  dst-addr "**********-28"
  service "tcp-8330"
  name "nginx访问实体渠道应用服务器"
exit
rule id 189
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********-72"
  dst-addr "**********-38"
  service "tcp-8330"
  name "nginx访问实体渠道API服务器"
exit
rule id 190
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********-82"
  dst-addr "**********-88"
  service "HTTP"
  service "tcp-10080"
  name "nginx访问实体渠道图片服务器"
exit
rule id 191
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********-32"
  dst-addr "**********-22"
  dst-addr "**********-12"
  service "tcp-8330"
  service "tcp-8000"
  name "nginx访问实体渠道认证服务器"
exit
rule id 193
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************-138"
  dst-addr "**********-42"
  service "tcp-7000"
  name "nginx访问实体渠道认证"
exit
rule id 194
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "***********-14"
  dst-addr "XXFB_**********0-133"
  service "tcp-8080"
  name "USAP访问信息发布管理端"
exit
rule id 195
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "*********/24"
  dst-addr "**********"
  service "tcp-8080"
  name "信息发布中心访问信息发布bifrost"
exit
rule id 196
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "*********-47"
  src-addr "*********-135"
  src-addr "************"
  dst-addr "**********"
  service "tcp-8080"
  name "开放平台访问信息发布bifrost"
exit
rule id 197
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "*******/8"
  src-addr "10.0.0.0/8"
  dst-addr "**********"
  service "tcp-8080"
  service "HTTP"
  name "IPTV和终端访问信息发布API"
exit
rule id 198
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********/24"
  dst-addr "*********01-202"
  dst-addr "*********11-212"
  service "7809-7850"
  service "tcp-1521"
  name "主中心访问三方查询库"
exit
rule id 200
  action permit
  src-zone "CWYY_untrust"
  dst-zone "CWYY_trust"
  src-addr "***********-14"
  src-addr "***********-32"
  dst-addr "*********-9"
  service "tcp-8080"
  name "USAP访问财务运营系统"
exit
rule id 201
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************"
  src-addr "***********"
  src-addr "**********-55"
  dst-addr "*********02"
  service "tcp-1521"
  name "数据中台访问实体渠道"
exit
rule id 51
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "***********-14"
  dst-addr "**********0"
  service "tcp-8080"
  name "USAP_nginx访问信息发布"
exit
rule id 71
  action permit
  src-zone "SJJM-trust"
  dst-zone "SJJM-untrust"
  src-addr "*********/24"
  src-addr "4.35.21.0/24"
  dst-addr "Any"
  service "Any"
  name "数据建模出向访问策略"
exit
rule id 79
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "**********-12"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  service "TCP-31306"
  name "数据建模访问营销中心数据库"
exit
rule id 102
  action permit
  src-zone "PNUP_untrust"
  dst-zone "PNUP_trust"
  src-addr "*********/24"
  src-addr "**********-32"
  src-addr "**********-22"
  src-addr "*********/24"
  dst-addr "***********-132"
  dst-addr "**********1-122"
  dst-addr "**********0-112"
  dst-addr "***********-102"
  dst-addr "***********"
  service "tcp-1521"
  name "容器平台访问拉新数据库"
exit
rule id 130
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "************-66"
  dst-addr "*********"
  service "TCP-3555"
  name "数据中台访问oracle服务器"
exit
rule id 145
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "**********-12"
  dst-addr "*********02"
  service "tcp-1521"
  name "数据建模DAM服务器访问实体渠道数据库"
exit
rule id 146
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************-202"
  dst-addr "4.24.11.130"
  service "tcp-8600"
  name "电子合同服务"
exit
rule id 147
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************-202"
  dst-addr "4.24.11.120"
  service "tcp-20128"
  name "电子合同服务FS"
exit
rule id 148
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/24"
  src-addr "************/24"
  dst-addr "**********"
  service "UDP_111"
  service "UDP_4046"
  service "TCP_111"
  service "TCP_2049"
  name "营销中心访问G3财务中心"
exit
rule id 165
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "YYGS_4.98.9.0"
  dst-addr "*********71"
  service "tcp-8080"
  name "YYGS-TO-JGXT"
exit      
rule id 161
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-ip *********/32
  dst-ip *********/32
  service "tcp-22"
  service "tcp-8081"
  service "tcp-8080"
  name "北京体彩中心访问数据中台服务"
exit
rule id 203
  action permit
  src-zone "CODING-trust"
  dst-zone "CODING-untrust"
  src-ip *********/24
  dst-addr "Any"
  service "Any"
  name "CODING出向访问策略"
exit
rule id 204
  action permit
  disable
  src-zone "CODING-untrust"
  dst-zone "CODING-trust"
  src-addr "************-14"
  dst-ip **********/32
  service "tcp-80"
  name "研发测试环境制品库数据同步"
exit
rule id 205
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********-12"
  src-addr "**********-24"
  src-addr "************"
  dst-addr "**********"
  service "tcp-8080"
  name "UMP访问实体渠道系统"
exit
rule id 206
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "***********-40"
  src-addr "***********/32"
  src-range *********** ***********
  dst-addr "**********-54"
  dst-addr "*********12"
  dst-addr "*********11"
  service "tcp-3306"
exit
rule id 207
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "************-************"
  dst-addr "Any"
  service "tcp-3389"
  service "TCP-139"
  service "TELNET"
  service "SSH"
  name "SOC"
exit
rule id 208
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********-2"
  src-addr "*********-2"
  dst-range 4.28.10.31 4.28.10.33
  service "TCP_9200"
  name "财务中心与财务运营访问es服务"
exit
rule id 209
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "*********-47"
  src-addr "*********31-134"
  src-addr "*********-12"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "**********-49"
  src-addr "***********-54"
  src-addr "**********-38"
  src-addr "*********/24"
  src-addr "*********/24"
  src-addr "************/24"
  src-addr "*********61-172"
  src-addr "************-192"
  src-addr "*************-212"
  src-addr "***********"
  src-ip ********/24
  dst-ip **********/32
  service "tcp-8080"
  name "开放平台访问数据中台NGINX"
exit
rule id 210
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "**********-2"
  dst-addr "**********"
  service "tcp-34443"
  name "骏彩运维虚机访问财务中心接口FTP服务器"
exit
rule id 211
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "*********"
  src-addr "*********"
  src-addr "********"
  src-addr "***********"
  src-addr "**********-42"
  src-addr "***********-42"
  dst-addr "*********"
  service "tcp-22"
  service "TCP- 6100-6200"
  name "骏彩访问Hive大数据"
exit
rule id 212
  action permit
  src-zone "YWJG-untrust"
  dst-zone "YWJG-trust"
  src-addr "*********/24"
  dst-addr "***********-113"
  dst-addr "**********-93"
  dst-addr "**********-82"
  dst-addr "**********-72"
  dst-addr "**********-42"
  dst-addr "***********-133"
  service "TCP-27017"
  service "tcp-26379"
  service "tcp-61616"
  service "tcp-6379"
  name "业务监控系统BMS连通至各中间件"
exit      
rule id 213
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "**********-72"
  src-addr "*********/24"
  dst-addr "***********-202"
  dst-addr "***********-212"
  service "tcp-1521"
  name "BMS访问监管DB"
exit
rule id 214
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "*********/24"
  src-addr "*********01-104"
  src-addr "**********-14"
  src-addr "***********-24"
  src-addr "***********-22"
  src-addr "**********"
  src-addr "**********-24"
  src-ip ************/32
  dst-addr "**********/32"
  service "tcp-8080"
  name "拉新访问数据集成平台"
exit
rule id 215
  action permit
  src-zone "YWJG-untrust"
  dst-zone "YWJG-trust"
  src-addr "************-66"
  dst-addr "**********-72"
  service "tcp-8088"
  name "数据中台访问监管系统"
exit
rule id 216
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********-47"
  src-addr "************"
  src-addr "*********31-134"
  dst-addr "*********"
  service "tcp-8080"
  name "支付中心实体渠道接口代理"
exit
rule id 217
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "XXFB-VPDN-1"
  src-addr "XXFB-VPDN-2"
  src-addr "XXFB-VPDN-3"
  src-addr "XXFB-VPDN-4"
  src-addr "XXFB-IPTV"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  dst-addr "***********"
  service "tcp-8080"
  service "HTTP"
  name "VPND&IPTV访问信息发布API"
exit
rule id 218
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************"
  src-range ************ ************
  dst-ip **********/32
  service "tcp-8330"
  name "体彩APP访问实体渠道"
exit
rule id 219
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-ip ********/32
  dst-ip **********/32
  service "tcp-13720"
  service "tcp-13782"
  service "tcp-13724"
  service "tcp-1556"
  name "备份服务器访问实体渠道服务器"
exit
rule id 220
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "***********/32"
  src-addr "**********/32"
  src-addr "***********-13"
  dst-addr "**********/32"
  dst-addr "**********/32"
  service "tcp-3306"
  name "tableau业务"
exit
rule id 221
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "************"
  src-addr "************/24"
  src-addr "css"
  src-addr "css-api"
  src-addr "**********/19"
  src-range ********** **********
  src-range ********** **********
  src-range ********** **********
  src-range *********** ***********
  src-range ********** **********
  src-range ********** **********
  dst-addr "**********/32"
  service "tcp-8080"
  name "实体渠道系统访问数据中台NG"
exit
rule id 222
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "************-************"
  dst-addr "**********-74"
  service "tcp-9080"
  name "USAP-DMZ区NG访问USAP服务"
exit
rule id 223
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "*********/32"
  dst-addr "*********/24"
  service "SSH"
  name "自动化服务器安全扫描"
exit
rule id 224
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "**********-43"
  src-addr "**********-24"
  src-addr "************/24"
  src-addr "4.35.20.46-47"
  src-addr "css"
  src-addr "css-api"
  dst-addr "*********11"
  dst-addr "*********01"
  dst-addr "*********12"
  service "tcp-1521"
  name "信息发布arthur至实体渠道DB"
exit
rule id 226
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "**********-24"
  src-addr "css"
  src-addr "css-api"
  dst-addr "**********-73"
  service "TCP_19092"
  name "UMP访问实体渠道同步数据"
exit
rule id 229
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "**********-24"
  src-addr "************/24"
  src-addr "css"
  src-addr "css-api"
  src-ip ************/32
  dst-ip **********/32
  dst-range *********** ***********
  service "tcp-8330"
  name "UMP访问实体渠道同步数据2"
exit
rule id 227
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "***********-14"
  dst-addr "*********03-104"
  service "tcp-8080"
  name "USAP访问报表集市"
exit
rule id 228
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "************/24"
  src-addr "************/24"
  src-ip ********/24
  dst-addr "**********-16"
  service "tcp-10081"
  name "实体渠道服务层访问营销中心"
exit
rule id 230
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-ip **********/32
  src-range ******** ********
  src-range ******** ********
  src-range ******** ********
  dst-ip ***********/32
  dst-ip ***********/32
  dst-ip ***********/32
  dst-ip ***********/32
  dst-ip **********/32
  service "tcp-8080"
  name "SSL访问信息发布后台"
exit
rule id 231
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "***********-14"
  dst-addr "**********"
  service "tcp-80"
  name "统一管理门户NG访问新型合规认证系统"
exit
rule id 232
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "*********/24"
  dst-addr "***********-***********"
  service "TCP-31306"
  name "新型合规认证系统访问系统数据库集群"
exit
rule id 233
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "**********/16"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********-247"
  dst-addr "*********/24"
  service "tcp-3306"
  name "腾讯云数据库同步"
exit
rule id 234
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "***********-22"
  src-addr "***********/32"
  dst-addr "**********-63"
  service "HTTP"
  service "tcp-8850"
  name "tableau业务互访"
exit
rule id 235
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "************/24"
  dst-addr "*********01-103"
  service "tcp-26379"
  name "腾讯云USAP访问"
exit
rule id 236
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "************/24"
  src-addr "************"
  dst-addr "**********"
  service "tcp-8080"
  name "腾讯云访问信息发布系统"
exit
rule id 237
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "10.194.122.0/24"
  src-addr "4.60.8.5"
  dst-addr "**********"
  service "tcp-8089"
  service "tcp-8080"
  name "G3-ZF_T0_XXFB-bifrost"
exit
rule id 238
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "***********-14"
  src-addr "*********31-134"
  src-addr "************/24"
  src-addr "************/24"
  dst-addr "4.24.11.180"
  service "tcp-8330"
  name "USAP_To_STQD"
exit
rule id 239
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "STQD_Addrss"
  src-addr "**********/19"
  dst-addr "*********30"
  service "tcp-19080"
  name "STQD_To_USAP"
exit
rule id 240
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "3.14.10.31-40"
  src-addr "**********-30"
  src-addr "3.14.10.41-55"
  dst-addr "*********1-54"
  dst-addr "4.35.10.1-5"
  dst-addr "4.35.10.11-12"
  service "tcp-22"
  name "主中心与二中心数据中台见SFTP访问"
exit
rule id 241
  action permit
  src-zone "CWZX_untrust"
  dst-zone "CWZX_trust"
  src-addr "************/24"
  src-addr "10.196.5.0/24"
  src-addr "***********/24"
  dst-addr "**********"
  service "tcp-22"
  service "tcp-34443"
  name "G3财务中心访问SFTP"
exit
rule id 242
  action permit
  src-zone "SJJM-untrust"
  dst-zone "SJJM-trust"
  src-addr "***********-14"
  src-addr "***********-22"
  dst-addr "4.35.21.10/32"
  service "tcp-8080"
  name "USAP_To_FanRuan"
exit
rule id 243
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "*********/32"
  dst-addr "*********/24"
  service "tcp-22"
  name "ZiDongHua_To_SJZT"
exit
rule id 244
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-range *********** ***********
  dst-addr "报表集市-*********06"
  dst-addr "*********01-104"
  service "tcp-22"
  name "YunYingFenXi_To_BaoBiaoJiShi"
exit
rule id 245
  action permit
  src-zone "PNUP_untrust"
  dst-zone "PNUP_trust"
  src-addr "3.22.16.101-102"
  src-addr "3.22.16.111-112"
  dst-addr "***********-102"
  service "tcp-22"
  service "tcp-1521"
  name "WS_to_PNUP-Datebase"
exit
rule id 246
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "4.103.12.21-22"
  dst-addr "4.24.11.171-174"
  service "tcp-26379"
  service "tcp-6379"
  name "message-nginx_to_Redis"
exit
rule id 247
  action permit
  src-zone "ILMS-untrust"
  dst-zone "ILMS-trust"
  src-addr "***********-14"
  dst-addr "4.20.50.50"
  service "TCP-8010"
  name "USAP-NG_To_JKDG"
exit
rule id 248
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "4.13.20.40-4.13.20.42"
  dst-addr "*********30"
  service "tcp-19080"
  name "QGZ_To_USAP"
exit
rule id 249
  action permit
  src-zone "ILMS-untrust"
  dst-zone "ILMS-trust"
  src-addr "4.103.18.11-12"
  src-range 4.103.18.21 4.103.18.22
  dst-addr "4.20.50.50"
  service "TCP-8010"
  name "USAP-NG_To_JKDG-1"
exit
rule id 250
  action permit
  src-zone "TYDLPT-trust"
  dst-zone "TYDLPT-untrust"
  src-addr "*********31-134"
  dst-addr "*********01-104"
  service "tcp-8080"
  name "USAP_To_BaoBiaoJiShi"
exit
rule id 251
  action permit
  src-zone "TYDLPT-untrust"
  dst-zone "TYDLPT-trust"
  src-addr "*********01-104"
  dst-addr "*********30"
  service "tcp-19080"
  name "BaoBiaoJiShi_To_USAP"
exit
rule id 252
  action permit
  src-zone "cs-trust"
  dst-zone "cs-untrust"
  src-addr "4.35.31.0/24"
  dst-addr "Any"
  service "Any"
  name "CS-trust-untrust"
exit
rule id 253
  action permit
  src-zone "cs-untrust"
  dst-zone "cs-trust"
  src-addr "4.103.150.51-52"
  src-addr "***********-14"
  dst-addr "4.35.31.10"
  service "tcp-8080"
  name "LC-CS_to_b2csweb"
exit
rule id 254
  action permit
  src-zone "cs-untrust"
  dst-zone "cs-trust"
  src-addr "4.101.90.21-22"
  dst-addr "4.35.31.10"
  service "tcp-8080"
  service "tcp-8081"
  name "WL-CS_to_b2csweb"
exit
rule id 255
  action permit
  src-zone "cs-untrust"
  dst-zone "cs-trust"
  src-addr "3.14.10.31-59"
  src-addr "************-94"
  dst-addr "4.35.31.25"
  dst-addr "4.35.31.21-24"
  service "TCP-31306"
  name "SJZT_to_CS-DB"
exit
rule id 256
  action permit
  src-zone "JGXT_untrust"
  dst-zone "JGXT_trust"
  src-addr "************/24"
  dst-addr "*********22"
  service "tcp-8080"
  name "G3-KFPT_TO_JGXT"
exit
rule id 257
  action permit
  src-zone "WDGZ-untrust"
  dst-zone "WDGZ-trust"
  src-addr "*********1-54"
  src-addr "4.35.10.1-5"
  src-addr "************-66"
  src-addr "************-98"
  src-addr "*********/24"
  dst-addr "**********/32"
  service "tcp-3306"
  name "SJZT_To_Sandbox-mysql"
exit
rule id 258
  action permit
  src-zone "SJJM-untrust"
  dst-zone "SJJM-trust"
  src-addr "*********/32"
  src-addr "3.252.101.2/32"
  dst-addr "4.35.21.0/24"
  service "tcp-22"
  name "ZiDongHua_To_SJJM"
exit
rule id 259
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "***********-22"
  dst-addr "*********1-54"
  dst-addr "4.35.10.1-5"
  service "TCP_8040-8140"
  name "SJZT-NG_To_TOMCAT"
exit
rule id 260
  action permit
  src-zone "CWYY_untrust"
  dst-zone "CWYY_trust"
  src-addr "************/24"
  dst-addr "**********"
  dst-addr "4.28.30.5"
  service "tcp-10080"
  name "G3-XSFW_TO_ZJGL"
exit
rule id 261
  action permit
  src-zone "SJJM-untrust"
  dst-zone "SJJM-trust"
  src-addr "4.101.90.10-14"
  src-addr "*********/24"
  dst-addr "4.35.20.30"
  service "tcp-80"
  name "BaoleiJi_To_SJJM-WEB-NG"
exit
rule id 262
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "4.101.90.10-14"
  dst-addr "4.28.10.30"
  service "TCP-5601"
  name "BaoleiJi_To_YX-kibana"
exit
rule id 263
  action permit
  src-zone "PWJC-trust"
  dst-zone "PWJC-untrust"
  src-addr "*********"
  dst-addr "Any"
  service "Any"
  name "mifu-trust-untrust"
exit
rule id 264
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "3.15.0.11-12"
  src-addr "3.15.0.51-52"
  dst-addr "4.35.32.10"
  service "tcp-8000"
  name "mifu-QianMing"
exit
rule id 265
  action permit
  src-zone "KFPT-trust"
  dst-zone "KFPT-untrust"
  src-addr "4.35.33.0"
  dst-addr "Any"
  service "Any"
  name "kfpt-trust-untrust"
exit
rule id 267
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "**********-55"
  dst-addr "4.24.12.101-102"
  dst-addr "4.24.12.111-112"
  service "tcp-1521"
  name "SJZT_To_STQD"
exit
rule id 266
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************/24"
  dst-addr "STQD_Addrss"
  service "tcp-8330"
  name "G3_To_G2-STQD"
exit
rule id 268
  action permit
  src-zone "KFPT-untrust"
  dst-zone "KFPT-trust"
  src-addr "4.103.16.90-94"
  dst-addr "4.35.33.10"
  service "tcp-8080"
  name "kfpt-untrust-trust-1"
exit
rule id 269
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-addr "************/24"
  dst-addr "**********0"
  service "tcp-8080"
  name "G3-KFPT_To_XXFB"
exit
rule id 270
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "4.10.10.41"
  service "UDP_1812"
  name "SC2Q_To_Radius"
exit
rule id 271
  action permit
  src-zone "KFPT-untrust"
  dst-zone "KFPT-trust"
  src-addr "*********/32"
  dst-addr "4.35.33.0"
  service "tcp-22"
  name "ZDH_KFPT"
exit
rule id 274
  action permit
  src-zone "SJJM-untrust"
  dst-zone "SJJM-trust"
  src-addr "*********/32"
  dst-addr "*********/24"
  service "tcp-22"
  name "ZiDongHua_To_SJJM1"
exit
rule id 273
  action permit
  src-zone "YWJG-untrust"
  dst-zone "YWJG-trust"
  src-addr "*********/32"
  dst-addr "*********/24"
  service "tcp-22"
  name "ZiDongHua_To_YWJG"
exit
rule id 272
  action permit
  src-zone "cs-untrust"
  dst-zone "cs-trust"
  src-addr "*********/32"
  dst-addr "4.35.31.0/24"
  service "tcp-22"
  name "ZiDongHua_To_Cs"
exit
rule id 275
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "**********-55"
  dst-addr "4.28.10.181"
  service "tcp-3306"
  name "SJZT-TO-YJGL"
exit
rule id 276
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "4.190.0.43/32"
  dst-addr "**********/32"
  service "tcp-8080"
  name "JC-JF"
exit
rule id 277
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "*********-4"
  dst-addr "**********-63"
  service "tcp-10014"
  name "QianMing-to-mifu"
exit
rule id 278
  action permit
  src-zone "PWJC-untrust"
  dst-zone "PWJC-trust"
  src-addr "二中心zabbix监控"
  dst-addr "*********"
  service "ICMP"
  name "zabbix-to-mifu"
exit
rule id 279
  action permit
  src-zone "YXZX_untrust"
  dst-zone "YXZX_trust"
  src-addr "*********/24"
  dst-ip **********/32
  service "TCP_30031-30049"
  name "ecc-to-TongYiJianKong"
exit
rule id 280
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "************"
  dst-addr "**********/32"
  service "TCP_21050"
  name "SJZT-to-NG"
exit
rule id 281
  action permit
  src-zone "XXFB-untrust"
  dst-zone "XXFB-trust"
  src-ip ***********/24
  dst-range *********** ***********
  service "tcp-1521"
  name ""
exit
rule id 282
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************/24"
  dst-addr "**********"
  service "HTTP"
  name "YN_STQD"
exit
rule id 283
  action permit
  src-zone "STQD-untrust"
  dst-zone "STQD-trust"
  src-addr "************/24"
  dst-addr "**********"
  service "tcp-8330"
  name "YN_STQD2"
exit
rule id 286
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "***********/24"
  src-addr "***********/24"
  src-addr "***********/24"
  src-addr "***********/24"
  dst-addr "**********/32"
  service "tcp-8080"
  name "G3chuanzu-to-zhongtai"
exit
rule id 287
  action permit
  src-zone "SJZT-untrust"
  dst-zone "SJZT-trust"
  src-addr "***********-16"
  src-addr "************-66"
  src-addr "************-98"
  src-addr "*********/24"
  src-addr "*********-9"
  src-addr "***********-40"
  src-addr "*********01-104"
  src-addr "************/24"
  dst-addr "**********/32"
  service "TCP_21050"
  service "tcp-10000"
  name "SHZT-youhua"
exit
rule id 3
  action deny
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit      
l2-nonip-action drop
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server vrouter "mgt-vr"
snmp-server engineID "cslc_snmp"
snmp-server host ************ version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq ro
snmp-server host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg ro
snmp-server host *********** version 2c community F4Sz9COrfWInjczI+vPw9DJ7I8Yq ro
snmp-server trap-host ************ version 2c community csl7NsdqZO5HMXcYJgR4BGuLRWcg port 162
snmp-server trap-host ************ version 2c community ckeI3SSNGQhyyPo3P+lMJmQ8mGku port 162
snmp-server trap-host *********** version 2c community DWbf06+a4P0FlgO+Yrtfp2YzSHMK port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
  icmp-unreachable-session-keep
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional
exit
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application
exit
query-groups
  dashboard-query-group "admin-**********271-dashboard-query-group" user "admin"
    rule "customwidget" create-time ********** id 1 query-string "flag_10,11,6,4,5,13,9"
    rule "license" create-time ********** id 2 query-string "%7B%22time%22%3A1699542568185%2C%22ignore%22%3Atrue%7D"
  exit
exit      
no sms disable
ha link interface ethernet0/2
ha link interface ethernet0/1
ha link ip ******* *************
ha group 0
  priority 150
  monitor track "track-ha"
exit
ha cluster 1

End

YZ-B1A03_2-6U-YY~(M)# show configuration
 
Building configuration.
Running configuration:
# PREVIOUS CONFIGERATION START
# END OF PREVIOUS CONFIGERATION
 
!
Version 5.5R8
subVersion 2.0
 
ip vrouter "mgt-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "TC_vr"
exit
ip vrouter "CSC_vr"
exit
ip vrouter "Public_vr"
exit
ip vrouter "Product_vr"
exit
ip vrouter "Extranet_vr"
exit
ip vrouter "Internet_vf"
exit
ip vrouter "MGMT_vr"
exit     
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "TC_Untrust"
exit     
zone "TC_Trust"
exit
zone "CSC_Untrust"
exit
zone "CSC_Trust"
exit
zone "Public_Untrust"
exit
zone "Public_Trust"
exit
zone "Product_Untrust"
exit
zone "Product_Trust"
exit
zone "Extranet_Untrust"
exit
zone "Extranet_Trust"
exit
zone "Internet_Untrust"
exit
zone "Internet_Trust"
exit
interface vswitchif1
exit     
interface ethernet0/0 local
exit
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit
interface xethernet3/0
exit
interface xethernet3/1
exit
interface xethernet3/2
exit
interface xethernet3/3
exit
interface xethernet3/4
exit
interface xethernet3/5
exit
interface xethernet3/6
exit
interface xethernet3/7
exit     
interface xethernet5/0
exit
interface xethernet5/1
exit
interface xethernet5/2
exit
interface xethernet5/3
exit
interface xethernet5/4
exit
interface xethernet5/5
exit
interface xethernet5/6
exit
interface xethernet5/7
exit
interface aggregate1
exit
interface aggregate1.100
exit
interface aggregate1.101
exit
interface aggregate1.102
exit     
interface aggregate1.103
exit
interface aggregate1.104
exit
interface aggregate1.105
exit
interface aggregate1.106
exit
interface aggregate1.107
exit
interface aggregate1.108
exit
interface aggregate1.109
exit
interface aggregate1.110
exit
interface aggregate1.111
exit
address "Any" predefined
exit
address "IPv6-any" ipv6 predefined
exit
address "private_network" predefined
exit     
address "VDI_YunZhuoMIan"
exit
address "VDI_TC"
exit
address "CSC_Client"
exit
address "SFTP-*********"
exit
address "TestVDI"
exit
address "VDI-172.100"
exit
address "**********"
exit
address "********"
exit
address "************"
exit
address "DHCP_**********-2"
exit
address "**********"
exit
address "**********"
exit     
address "**********"
exit
address "**********"
exit
address "172.100"
exit
address "***********"
exit
address "*************"
exit
address "VPN-10-208"
exit
address "***********"
exit
address "Print"
exit
address "LotteryVDI"
exit
address "**********"
exit
address "**********"
exit
address "CSC_VDI"
exit     
address "************/26"
exit
address "***********-15"
exit
address "************-56"
exit
address "**********"
exit
address "**********"
exit
address "**********"
exit
address "************"
exit
address "************-244"
exit
address "************-244"
exit
address "************-246"
exit
address "************"
exit
address "**********"
exit     
address "**********"
exit
address "*********"
exit
address "***********-12"
exit
aaa-server "local" type local
exit
service "TCP-34443"
exit
service "TCP-8443"
exit
service "TCP-3389"
exit
service "TCP-5480"
exit
service "TCP-8090"
exit
service "TCP-5222"
exit
service "389"
exit
service "TCP3000-3010"
exit     
service "TCP_9092"
exit
service "TCP-8088"
exit
service "TCP-34443"
  tcp dst-port 34443
exit
service "TCP-8443"
  tcp dst-port 8443
exit
service "TCP-3389"
  tcp dst-port 3389
exit
service "TCP-5480"
  tcp dst-port 5480
exit
service "TCP-8090"
  tcp dst-port 8090
exit
service "TCP-5222"
  tcp dst-port 5222
exit
service "389"
  tcp dst-port 389
  udp dst-port 389
exit
service "TCP3000-3010"
  tcp dst-port 3000 3010
exit
service "TCP_9092"
  tcp dst-port 9092
exit
service "TCP-8088"
  tcp dst-port 8088
exit
url-profile "no-url" global-predefined
exit
nbr-profile "no-nbr"
exit
epp-profile "no-epp"
exit
epp-profile "predef_epp"
status uninstall log-only
status unhealthy log-only
status infected block
status abnormal block
exit
no logging traffic session to buffer
no logging traffic nat to buffer
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_rsa_4096"
  keypair "RSA-Key-4096"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_ecc_256"
  keypair "ECC-Key-256"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_ecc_384"
  keypair "ECC-Key-384"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "Any" predefined
  ip 0.0.0.0/0
exit
address "IPv6-any" ipv6 predefined
  ip ::/0
exit
address "private_network" predefined
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "VDI_YunZhuoMIan"
  ip **********/24
exit
address "VDI_TC"
  ip **********/24
  ip **********/24
  ip **********/24
  ip **********/24
exit
address "CSC_Client"
  ip ************/25
exit
address "SFTP-*********"
  ip *********/32
exit
address "TestVDI"
  ip ***********/32
  ip *************/32
  ip *************/32
  ip *************/32
  ip **********/16
  ip **********/16
  range ************* *************
  range *********** ***********
exit
address "VDI-172.100"
  ip *************/32
  ip *************/32
  range ************* ***************
exit
address "**********"
  ip **********/16
exit
address "********"
  ip ********/16
exit
address "************"
  ip ************/24
exit
address "DHCP_**********-2"
  range ********** **********
exit
address "**********"
  ip **********/24
exit     
address "**********"
  ip **********/24
exit
address "**********"
  ip **********/24
exit
address "**********"
  ip **********/24
exit
address "172.100"
  ip ***********/16
exit
address "***********"
  ip ***********/32
exit
address "*************"
  ip *************/32
exit
address "VPN-10-208"
  ip ***********/24
  ip ***********/24
exit
address "***********"
  ip ***********/32
exit
address "Print"
  range ************ ************
  range ************ ************
  range ************ ************
exit
address "LotteryVDI"
  range ************ **************
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "CSC_VDI"
  ip **********/25
exit
address "************/26"
  ip ************/26
exit
address "***********-15"
  range *********** ***********
exit     
address "************-56"
  range ************ ************
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "**********"
  ip **********/32
exit
address "************"
  ip ************/32
exit
address "************-244"
  range ************ ************
exit
address "************-244"
  range ************ ************
exit
address "************-246"
  range ************ ************
exit     
address "************"
  ip ************/32
exit
address "**********"
  ip **********/16
exit
address "**********"
  ip **********/16
exit
address "*********"
  ip *********/22
exit
address "***********-12"
  range *********** ***********
exit
zone "mgt"
  vrouter "mgt-vr"
exit
zone "untrust"
  type wan
  ad icmp-flood
  ad ip-spoofing
  ad ip-sweep
  ad land-attack
  ad port-scan
  ad syn-flood
  ad tear-drop
  ad winnuke
  ad ping-of-death
  ad udp-flood
  ad ip-fragment
  ad ip-option
  ad ip-directed-broadcast
exit
zone "l2-untrust" l2
  type wan
exit
zone "TC_Untrust"
  vrouter "TC_vr"
exit
zone "TC_Trust"
  vrouter "TC_vr"
  ad disable
exit
zone "CSC_Untrust"
  vrouter "CSC_vr"
exit
zone "CSC_Trust"
  vrouter "CSC_vr"
  ad disable
exit
zone "Public_Untrust"
  vrouter "Public_vr"
exit
zone "Public_Trust"
  vrouter "Public_vr"
exit
zone "Product_Untrust"
  vrouter "Product_vr"
exit
zone "Product_Trust"
  vrouter "Product_vr"
exit
zone "Extranet_Untrust"
  vrouter "Extranet_vr"
exit
zone "Extranet_Trust"
  vrouter "Extranet_vr"
exit
zone "Internet_Untrust"
  vrouter "Internet_vf"
exit     
zone "Internet_Trust"
  vrouter "Internet_vf"
exit
hostname "YZ-B1A03_2-6U-YYVDI-FW01A"
admin host any ssh
admin host any https
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit
 
isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit
 
isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit
 
isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit     
 
isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit
 
isakmp proposal "psk-md5-3des-g2"
  hash md5
exit
 
isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit
 
isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit
 
isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit
 
isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit
 
isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit
 
isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit
 
isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit     
 
isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit
 
isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit
 
ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit
 
ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit
 
ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit
 
ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit
 
ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit
 
ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit
 
ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit     
 
ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit
 
ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit
 
ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit
 
ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit
 
ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit
 
strict-tunnel-check
flow
  icmp-unreachable-session-keep
exit
interface ethernet0/0 local
  zone  "mgt"
  ip address ************* ***************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface xethernet3/0
  aggregate aggregate1
exit
interface xethernet5/0
  aggregate aggregate1
exit
interface aggregate1
  zone  "l2-trust"
  lacp enable
exit
interface aggregate1.100
  zone  "Public_Untrust"
  ip address ********** ***************
  manage ping
exit
interface aggregate1.101
  zone  "Public_Trust"
  ip address ********** ***************
  manage ping
exit
interface aggregate1.102
  zone  "TC_Untrust"
  ip address ********** ***************
  manage ping
exit
interface aggregate1.103
  zone  "TC_Trust"
  ip address **********3 ***************
  manage ping
exit
interface aggregate1.104
  zone  "CSC_Untrust"
  ip address **********7 ***************
  manage ping
exit
interface aggregate1.105
  zone  "CSC_Trust"
  ip address *********** ***************
  manage ping
exit
interface aggregate1.106
  zone  "Product_Trust"
  ip address *********** ***************
  manage ping
exit
interface aggregate1.107
  zone  "Product_Untrust"
  ip address *********** ***************
  manage ping
exit
interface aggregate1.108
  zone  "Extranet_Trust"
  ip address *********** ***************
  manage ping
exit
interface aggregate1.109
  zone  "Extranet_Untrust"
  ip address *********** ***************
  manage ping
exit
interface aggregate1.110
  zone  "Internet_Trust"
  ip address *********** ***************
  manage ping
exit
interface aggregate1.111
  zone  "Internet_Untrust"
  ip address *********** ***************
  manage ping
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 *************
exit
ip vrouter "TC_vr"
  ip route 0.0.0.0/0 **********0
  ip route **********/24 **********4
  ip route **********/24 **********4
  ip route **********/24 **********4
  ip route **********/24 **********4
exit     
ip vrouter "CSC_vr"
  ip route 0.0.0.0/0 **********8
  ip route ************/25 ***********
exit
ip vrouter "Public_vr"
  ip route 0.0.0.0/0 **********
  ip route **********/24 **********
  ip route **********/24 **********
  ip route **********/24 **********
  ip route **********/24 **********
exit
ip vrouter "Product_vr"
  ip route **********/24 **********6
  ip route **********/24 **********6
  ip route **********/24 **********6
  ip route *********/24 ***********
  ip route **********/24 **********6
exit
ip vrouter "Extranet_vr"
  ip route **********/16 ***********
  ip route ************/24 ***********
  ip route ********/16 ***********
  ip route **********/19 ***********
  ip route ***********/24 ***********
  ip route ***********/24 ***********
  ip route **********/16 ***********
  ip route **********/16 ***********
  ip route ***********/24 ***********
  ip route **********/16 ***********
  ip route **********/16 ***********
  ip route *********/22 ***********
exit
ip vrouter "Internet_vf"
  ip route 0.0.0.0/0 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
  ip route **********/24 ***********
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit   
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
rule id 30
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "************"
  dst-addr "**********"
  service "Any"
  name "printer-test"
exit
rule id 10
  action permit
  src-addr "Any"
  dst-addr "Any"
  service "ICMP"
 name "ICMP"
exit
rule id 1
  action permit
  src-zone "Internet_Trust"
  dst-zone "Internet_Untrust"
  src-addr "VDI_TC"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "CSC_VDI"
  dst-addr "Any"
  service "Any"
  name "VDI_To_Internet"
exit
rule id 2
  action permit
  src-zone "Product_Trust"
  dst-zone "Product_Untrust"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "SFTP-*********"
  service "TCP-34443"
  name "VDI_To_Product"
exit
rule id 13
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "VDI_TC"
  dst-addr "DHCP_**********-2"
  service "DHCP-Relay"
  name "TC_To_DHCP"
exit
rule id 3
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "VDI_TC"
  dst-addr "**********"
  service "Any"
  name "TC_To_VDI"
exit
rule id 4
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "VDI_TC"
  dst-addr "TestVDI"
  service "Any"
  name "TC_To_TestVDI"
exit
rule id 5
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "VDI_TC"
  dst-addr "VDI-172.100"
  service "Any"
  name "TC_To_FuZhuYunYing-VDI"
exit
rule id 6
  action permit
  src-zone "CSC_Trust"
  dst-zone "CSC_Untrust"
  src-addr "CSC_Client"
  dst-addr "**********"
  service "Any"
  name "KeFu_To_VDI"
exit     
rule id 25
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "TestVDI"
  src-addr "CSC_VDI"
  dst-addr "**********"
  dst-addr "**********"
  service "HTTP"
  service "TCP3000-3010"
  name "To_Test-ShaDu"
exit
rule id 7
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "VDI_TC"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  service "Any"
  name "TC_To_VDI-ZhuoMian"
exit
rule id 8
  action permit
  src-zone "Public_Trust"
  dst-zone "Public_Untrust"
  src-addr "VDI_YunZhuoMIan"
  dst-addr "Any"
  service "Any"
  name "VDI-ZhuoMian_To_Internet"
exit
rule id 11
  action permit
  src-zone "Extranet_Untrust"
  dst-zone "Extranet_Trust"
  src-addr "**********"
  src-ip **********/16
  src-ip **********/16
  dst-addr "Any"
  service "SSH"
  service "HTTPS"
  service "TCP-8443"
  service "TCP-3389"
  service "TCP-5480"
  service "389"
  service "TCP-8088"
  name "Mange"
exit
rule id 12
  action permit
  src-zone "Extranet_Untrust"
  dst-zone "Extranet_Trust"
  src-addr "********"
  src-addr "************"
  dst-addr "Any"
  service "Any"
  name "Mange-1"
exit
rule id 14
  action permit
  src-zone "Internet_Untrust"
  dst-zone "Internet_Trust"
  src-addr "172.100"
  dst-addr "***********"
  dst-ip ***********/32
  dst-ip ***********/32
  service "Any"
  name "Old-TC_To_New-VDI"
exit
rule id 15
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "VDI_TC"
  dst-addr "TestVDI"
  service "Any"
  name "TC_To_TestVDI-1"
exit
rule id 16
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "VDI_YunZhuoMIan"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "*************"
  service "Any"
  name "VDI_To_Test-GongXiangPan"
exit
rule id 17
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-ip ***********/32
  service "TCP-8090"
  service "TCP-8443"
  service "TCP-5222"
  name "VDI_to_jiami"
exit
rule id 18
  action permit
  src-zone "TC_Untrust"
  dst-zone "TC_Trust"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "Print"
  service "Any"
  name "VDI-Print"
exit
rule id 19
  action permit
  src-zone "Extranet_Untrust"
  dst-zone "Extranet_Trust"
  src-addr "VPN-10-208"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  service "Any"
  name "Test-VPN_To_VDI"
exit
rule id 21
  action permit
  src-zone "Extranet_Untrust"
  dst-zone "Extranet_Trust"
  src-addr "LotteryVDI"
  dst-addr "Print"
  service "Any"
  name "LotteryVDI_To_YW-Print"
exit
rule id 22
  action permit
  src-zone "Extranet_Untrust"
  dst-zone "Extranet_Trust"
  src-addr "LotteryVDI"
  dst-addr "**********"
  service "Any"
  name "LotteryVDI_To_YW-GongXiangPan"
exit
rule id 24
  action permit
  src-zone "TC_Untrust"
  dst-zone "TC_Trust"
 src-addr "LotteryVDI"
  dst-addr "Print"
  service "Any"
  name "LotteryVDI_To_YW-Print-1"
exit
rule id 26
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "************/26"
  dst-addr "***********-15"
  service "SSH"
  name "VDI_To_ShuJuZhongTai"
exit
rule id 27
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "************/26"
  dst-addr "************-56"
  service "Any"
  name "VDI_To_ShuJuZhongTai-1"
exit
rule id 28
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "************/26"
  dst-addr "**********"
  service "HTTP"
  service "HTTPS"
  name "VDI_To_Coding"
exit
rule id 29
  action permit
  src-zone "Extranet_Trust"
  dst-zone "Extranet_Untrust"
  src-addr "**********"
  src-addr "**********"
  src-addr "**********"
  dst-addr "**********"
  dst-addr "**********"
  service "POP3"
  service "IMAP4"
  service "HTTPS"
  name "VDI_To_Mail"
exit
rule id 31
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "************-244"
  dst-addr "**********"
  service "Any"
  name "YJ-Printer_To_Share"
exit
rule id 32
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "************-244"
  dst-addr "**********"
  service "Any"
  name "XW-Printer_To_Share"
exit
rule id 33
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "************-246"
  dst-addr "**********"
  service "Any"
  name "YZ-Printer_To_Share"
exit
rule id 34
  action permit
  src-zone "TC_Trust"
  dst-zone "TC_Untrust"
  src-addr "************"
  src-addr "************"
  dst-addr "Any"
  service "Any"
  name "YJ-Printer_To_Internet"
exit     
rule id 36
  action permit
  src-zone "Extranet_Untrust"
  dst-zone "Extranet_Trust"
  src-addr "*********"
  dst-addr "***********"
  service "TCP-8443"
exit
rule id 35
  action permit
  src-zone "Extranet_Untrust"
  dst-zone "Extranet_Trust"
  src-addr "*********"
  dst-addr "***********"
  dst-addr "***********-12"
  service "HTTPS"
  service "TCP-8443"
  name "ZeroTrust_To_VDI"
exit
password-policy
  admin min-length 8
  admin complexity enable
  admin capital-letters 1
  admin small-letters 1
  admin numeric-characters 1
  admin non-alphanumeric-letters 1
  admin password-expiration 365
  admin original-time 1700498973
exit
admin user "hillstone"
  password rNOy32iiTOL4Wid20tsbS1rQGC
  password-expiration 1700499012
  role "admin"
  access console
  access ssh
  access https
key "20231026001356555"
value "eyJfZyI6eyJkZXN0aW5hdGlvbl9yb3V0ZS52aWV3LkRlc3RpbmF0aW9uUm91dGVHcmlkX2Rlc3RpbmF0aW9ucm91dGVncmlkX2Rlc3RpbmF0aW9uUm91dGVHcmlkX2Rlc3RpbmF0aW9uX3JvdXRlLnZpZXcuRGVzdGluYXRpb25Sb3V0ZVRhYl9kZXN0aW5hd
GlvbnJvdXRldGFiX3VuZGVmaW5lZF91bmRlZmluZWRfZGVzdGluYXRpb25fcm91dGUuc3RvcmUuRGVzdGluYXRpb25Sb3V0ZSI6W3siX3AiOlswXX0seyJfcCI6WzFdLCJfdyI6MTMxLCJ3T25DUiI6MX0seyJfcCI6WzJdfSx7Il9wIjpbM119LHsiX3AiOls0XX0se
yJfcCI6WzVdLCJfdyI6MTMxLCJ3T25DUiI6MX0seyJfcCI6WzZdfSx7Il9wIjpbN119LHsiX3AiOls4XX0seyJfcCI6WzldfSx7Il9wIjpbMTBdfSx7Il9wIjpbMTFdfSx7Il9wIjpbMTJdfSx7Il9wIjpbMTNdfV0sInBvbGljeS52aWV3LlBvbGljeUdyaWRfcG9sa
WN5Z3JpZF91bmRlZmluZWRfdW5kZWZpbmVkX3BvbGljeS5zdG9yZS5Qb2xpY3lMaXN0IjpbeyJfcCI6WzBdfSx7Il9wIjpbMV0sIl9oIjoxfSx7Il9wIjpbMl0sIl9jIjpbeyJfcCI6WzIsMF0sIl93IjoxNjEsIndPbkNSIjoxfSx7Il9wIjpbMiwxXX0seyJfcCI6W
zIsMl19XX0seyJfcCI6WzNdLCJfYyI6W3siX3AiOlszLDBdLCJfdyI6MTQ4LCJ3T25DUiI6MX0seyJfcCI6WzMsMV19XX0seyJfcCI6WzRdfSx7Il9wIjpbNV19LHsiX3AiOls2XX0seyJfcCI6WzddfSx7Il9wIjpbOF19LHsiX3AiOls5XSwiX2giOjF9LHsiX3AiO
lsxMF19LHsiX3AiOlsxMV19LHsiX3AiOlsxMl19LHsiX3AiOlsxM119LHsiX3AiOlsxNF19LHsiX3AiOlsxNV19LHsiX3AiOlsxNl19LHsiX3AiOlsxN10sIl9oIjoxfSx7Il9wIjpbMThdfV19LCJfcCI6e319"
exit
admin user "netadmin"
  password 5Yx0DFjrKzpBKM0xkcgPW/vgKm
  password-expiration 1701435042
  role "admin"
  access console
  access ssh
  access https
key "20231026001356555"
value "eyJfZyI6eyJwb2xpY3kudmlldy5Qb2xpY3lHcmlkX3BvbGljeWdyaWRfdW5kZWZpbmVkX3VuZGVmaW5lZF9wb2xpY3kuc3RvcmUuUG9saWN5TGlzdCI6W3siX3AiOlswXSwiX3ciOjgxLCJ3T25DUiI6MX0seyJfcCI6WzFdLCJfdyI6MjIzLCJ3T25DUiI6M
X0seyJfcCI6WzJdLCJfYyI6W3siX3AiOlsyLDBdLCJfdyI6MTc0LCJ3T25DUiI6MX0seyJfcCI6WzIsMV0sIl93IjozMDEsIndPbkNSIjoxfSx7Il9wIjpbMiwyXSwiX2giOjF9XX0seyJfcCI6WzNdLCJfYyI6W3siX3AiOlszLDBdLCJfdyI6MTkzLCJ3T25DUiI6M
X0seyJfcCI6WzMsMV0sIl93IjoyMjgsIndPbkNSIjoxfV19LHsiX3AiOls0XSwiX3ciOjE5Mywid09uQ1IiOjF9LHsiX3AiOls1XSwiX2giOjF9LHsiX3AiOls2XX0seyJfcCI6WzddLCJfaCI6MX0seyJfcCI6WzhdLCJfaCI6MX0seyJfcCI6WzldLCJfaCI6MX0se
yJfcCI6WzEwXX0seyJfcCI6WzExXSwiX2giOjF9LHsiX3AiOlsxMl0sIl9oIjoxfSx7Il9wIjpbMTNdLCJfaCI6MX0seyJfcCI6WzE0XSwiX2giOjF9LHsiX3AiOlsxNV0sIl9oIjoxfSx7Il9wIjpbMTZdfSx7Il9wIjpbMTddLCJfaCI6MX0seyJfcCI6WzE4XX1dL
CJkZXN0aW5hdGlvbl9yb3V0ZS52aWV3LkRlc3RpbmF0aW9uUm91dGVHcmlkX2Rlc3RpbmF0aW9ucm91dGVncmlkX2Rlc3RpbmF0aW9uUm91dGVHcmlkX2Rlc3RpbmF0aW9uX3JvdXRlLnZpZXcuRGVzdGluYXRpb25Sb3V0ZVRhYl9kZXN0aW5hdGlvbnJvdXRldGFiX
3VuZGVmaW5lZF91bmRlZmluZWRfZGVzdGluYXRpb25fcm91dGUuc3RvcmUuRGVzdGluYXRpb25Sb3V0ZSI6W3siX3AiOlswXX0seyJfcCI6WzFdfSx7Il9wIjpbMl19LHsiX3AiOlszXX0seyJfcCI6WzRdfSx7Il9wIjpbNV0sIl93IjoxNjQsIndPbkNSIjoxfSx7I
l9wIjpbNl19LHsiX3AiOls3XX0seyJfcCI6WzhdfSx7Il9wIjpbOV19LHsiX3AiOlsxMF19LHsiX3AiOlsxMV19LHsiX3AiOlsxMl19LHsiX3AiOlsxM119XX0sIl9wIjp7IkhTLnZpZXcuTWVudUxlZnRQYW5lbF9ocy1tZW51bGVmdC1wYW5lbF91bmRlZmluZWRfd
W5kZWZpbmVkIjp7Il9lIjp0cnVlfX19"
exit
user-sso server ad-scripting default
exit
user-sso server sso-radius default
exit
user-sso server radius-snooping default
exit
l2-nonip-action drop
no alg sqlnetv2
no tcp-mss all
tcp-mss tunnel 1380
ecmp-route-select by-src-and-dst
url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
sslproxy trust-domain "trust_domain_ssl_proxy_2048"
query-groups
  dashboard-query-group "netadmin-dashboard-query-group" user "netadmin"
    rule "license" create-time ********** query-string "%7B%22time%22%3A1719204762864%2C%22ignore%22%3Atrue%7D"
  exit
exit
no sms disable
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional vsys
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional vr vsys
exit
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application vsys
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application vsys
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application vsys
exit
statistics-set "predef_protocol_bw"
  target-data bandwidth id 5 record-history
  group-by protocol vsys
exit
statistics-filter address "Any"
ha link interface ethernet0/1
ha link interface ethernet0/2
ha link ip ******* ***************
ha group 0
  arp 30
exit
ha cluster 1 node 0
 
End

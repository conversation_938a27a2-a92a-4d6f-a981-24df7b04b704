XWEAFW03-B1E03(M)# sh config
XWEAFW03-B1E03(M)# sh configuration 

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ip vrouter "E-FW"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit      
zone "l2-untrust" l2
exit
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
interface vswitchif1
exit
interface MGT0 local
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit
interface ethernet0/2
exit
interface ethernet0/3
exit      
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit
interface aggregate1
exit
interface aggregate2
exit      
address "private_network"
exit
address "CIMS_*********/24"
exit
address "host_***********"
exit
address "payProxy_**********-214"
exit
address "payProxy_**********-64"
exit
address "weiXin_************"
exit
address "weiXin_************-6"
exit
address "host_***********"
exit
address "weixin_dnat_***********"
exit
address "sftp_dnat_************"
exit
address "payProxy_************-22"
exit
address "YYGS_FengKong"
exit      
address "JC_**********-2"
exit
address "TC_APP_address_********"
exit
address "TC_APP_address_********"
exit
address "TC_APP_address_*********"
exit
address "TC_APP_address_*********"
exit
address "TC_APP_address_**********"
exit
address "TC_APP_address_*********"
exit
address "TC_APP_address_*********"
exit
address "TC_APP_address_********"
exit
address "TC_APP_address_*********"
exit
address "TC_APP_address_********"
exit
address "TC_APP_address_********"
exit      
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "500W_address_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********0"
exit
address "TC_APP_NAT_***********"
exit
address "TC_APP_NAT_**********"
exit      
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_**********"
exit
address "TC_APP_NAT_***********"
exit
address "TC_APP_NAT_***********"
exit
address "TC_APP_**********"
exit
address "TC_APP_**********"
exit
address "TC_APP_**********"
exit
address "TC_APP_************"
exit
address "TC_APP_************"
exit
address "Juncai_************"
exit
address "Juncai_NAT_**********"
exit      
address "500W_Info_address_***********"
exit
address "SFTP_NAT_**********"
exit
address "SFTP_address_***********"
exit
address "500W_address_**********3"
exit
address "USAP_SSL_address_**********"
exit
address "USAP_SSL_address_**********"
exit
address "TC_APP_TO_USAP_address"
exit
address "psbc_address_************"
exit
address "psbc_NAT_**********"
exit
address "ELB_SSL_**********"
exit
address "ELB_SSL_NAT_**********"
exit
address "ELB_SFTP_***********"
exit      
address "ELB_SFTP_NAT_**********"
exit
address "payProxy_ng_************"
exit
address "payProxy_ng_************"
exit
address "payProxy_ng_************"
exit
address "payProxy_ng_************"
exit
address "payProxy_ng_NAT_**********"
exit
address "payProxy_ng_NAT_**********"
exit
address "payProxy_ng_NAT_**********"
exit
address "payProxy_ng_NAT_**********"
exit
address "JC_**********"
exit
address "weiXin_************"
exit
address "YYXNH_address_*************"
exit      
address "YYXNH_address_*************"
exit
address "YYXNH_address_*************"
exit
address "YYXNH_address_*************"
exit
address "TC_APP_address_********"
exit
address "TC_APP_address_*********"
exit
address "TC_APP_NAT_***********"
exit
address "TC_APP_NAT_***********"
exit
address "KFPT_SSL_NAT_***********"
exit
address "KFPT_SSL_**********"
exit
address "TCAPP_AL_*********/24"
exit
address "TCAPP_AL_*********/24"
exit
address "TCAPP_AL_NAT_***********"
exit      
address "TCAPP_AL_*********/24"
exit
address "TCAPP_AL_*********/24"
exit
address "TCAPP_AL_**********"
exit
address "TCAPP_AL_**********"
exit
address "TCAPP_AL_NAT_***********"
exit
address "TCAPP_AL_NAT_***********"
exit
address "TCAPP_AL_NAT_***********"
exit
address "TCAPP_AL_NAT_***********"
exit
address "TCAPP_AL_NAT_***********"
exit
address "TCAPP***********"
exit
address "TCAPP***********"
exit
address "TCAPP*********"
exit      
address "TCAPP*********"
exit
address "TCAPP*********"
exit
address "YYXNH_address_*************"
exit
address "YYXNH_address_*************"
exit
address "YYXNH_address_*************"
exit
address "TCAPP_AL_**********"
exit
address "TCAPP_AL_**********"
exit
address "TCAPP_AL_NAT_***********"
exit
address "TCAPP_AL_NAT_***********"
exit
address "TCAPP_AL_NAT_**********1"
exit
address "TCAPP_AL_NAT_**********2"
exit
address "TCAPP_TCloud_***********/24"
exit      
address "TCAPP_TCloud_***********/24"
exit
address "TCAPP_TCloud_***********/24"
exit
address "TCAPP_TCloud_***********/24"
exit
address "TCAPP_TCloud_*************"
exit
address "TCAPP_TCloud_***********"
exit
address "TCAPP_TCloud_NAT_**********5"
exit
address "TCAPP_TCloud_NAT_**********6"
exit
address "TCAPP_TCloud_NAT_**********7"
exit
address "TCAPP_TCloud_NAT_**********8"
exit
address "TCAPP_TCloud_NAT_**********9"
exit
address "TCAPP_TCloud_NAT_**********0"
exit
address "TCAPP_AL_NAT_NET-1"
exit      
address "TCAPP_AL_NAT_NET-2"
exit
address "TCAPP_TX_NAT_**********3"
exit
address "TCAPP_TCloud_***********/24"
exit
aaa-server "local" type local
exit
track "JianCe"
exit
service "SFTP22"
  tcp dst-port 22 
exit
service "TCP_52701"
  tcp dst-port 52701 
exit
service "TCP_8080"
  tcp dst-port 8080 
exit
service "TCP_30000"
  tcp dst-port 30000 
exit
service "TCP_28601"
  tcp dst-port 28601 
exit
service "TCP_20006"
  tcp dst-port 20006 
exit
service "TCP_20110"
  tcp dst-port 20110 
exit
ips sigset "dns" template dns
  max-scan-bytes 30720
exit
ips sigset "ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "smtp" template smtp
  max-scan-bytes 30720
exit      
ips sigset "telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "imap" template imap
  max-scan-bytes 30720
exit
ips sigset "finger" template finger
  max-scan-bytes 30720
exit
ips sigset "sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "tftp" template tftp
  max-scan-bytes 30720
exit      
ips sigset "snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "ldap" template ldap
  max-scan-bytes 30720
exit      
ips sigset "voip" template voip
  max-scan-bytes 30720
exit
ips sigset "default_dns" template dns
  max-scan-bytes 30720
exit
ips sigset "default_ftp" template ftp
  max-scan-bytes 30720
exit
ips sigset "default_http" template http
  max-scan-bytes 30720
  web-server "default"
  exit
exit
ips sigset "default_pop3" template pop3
  max-scan-bytes 30720
exit
ips sigset "default_smtp" template smtp
  max-scan-bytes 30720
exit
ips sigset "default_telnet" template telnet
  max-scan-bytes 30720
exit
ips sigset "default_other-tcp" template other-tcp
  max-scan-bytes 30720
exit
ips sigset "default_other-udp" template other-udp
  max-scan-bytes 30720
exit
ips sigset "default_imap" template imap
  max-scan-bytes 30720
exit
ips sigset "default_finger" template finger
  max-scan-bytes 30720
exit
ips sigset "default_sunrpc" template sunrpc
  max-scan-bytes 30720
exit
ips sigset "default_nntp" template nntp
  max-scan-bytes 30720
exit
ips sigset "default_tftp" template tftp
  max-scan-bytes 30720
exit
ips sigset "default_snmp" template snmp
  max-scan-bytes 30720
exit
ips sigset "default_mysql" template mysql
  max-scan-bytes 30720
exit
ips sigset "default_mssql" template mssql
  max-scan-bytes 30720
exit
ips sigset "default_oracle" template oracle
  max-scan-bytes 30720
exit
ips sigset "default_msrpc" template msrpc
  max-scan-bytes 30720
exit
ips sigset "default_netbios" template netbios
  max-scan-bytes 30720
exit
ips sigset "default_dhcp" template dhcp
  max-scan-bytes 30720
exit
ips sigset "default_ldap" template ldap
  max-scan-bytes 30720
exit
ips sigset "default_voip" template voip
  max-scan-bytes 30720
exit
ips profile "no-ips"
exit
ips profile "predef_default"
  sigset "default_dns"
  sigset "default_ftp"
  sigset "default_http"
  sigset "default_pop3"
  sigset "default_smtp"
  sigset "default_telnet"
  sigset "default_other-tcp"
  sigset "default_other-udp"
  sigset "default_imap"
  sigset "default_finger"
  sigset "default_sunrpc"
  sigset "default_nntp"
  sigset "default_tftp"
  sigset "default_snmp"
  sigset "default_mysql"
  sigset "default_mssql"
  sigset "default_oracle"
  sigset "default_msrpc"
  sigset "default_netbios"
  sigset "default_dhcp"
  sigset "default_ldap"
  sigset "default_voip"
  filter-class 1 
    severity "Low" 
    severity "Medium" 
    severity "High" 
    action reset
  exit
exit
url-category "custom1"
exit
url-category "custom2"
exit
url-category "custom3"
exit
contentfilter
exit
sandbox-profile "predef_low"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_middle"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_high"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  file-type swf
  file-type rar
  file-type zip
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
sandbox-profile "predef_pe"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
url-profile "no-url"
exit
track "JianCe"
  interface aggregate1 
  interface aggregate2 
exit
admin user "hillstone"
  password xyzx65kfrwlZZvAkGwLfQajQsu
        password-expiration 1577937458
  role "admin"
  access console
exit
admin user "netadmin"
  password GpjR3sbfIXECUSG/Rui6r+uA2O
        password-expiration 1577937410
  role "admin"
  access console
  access ssh
  access https
exit
logging event to syslog 
logging threat to buffer severity informational
logging threat to syslog custom-format  severity informational
logging network to syslog 
logging configuration to syslog 
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog ************ vrouter "mgt-vr" udp 514 type event
logging syslog ************ vrouter "mgt-vr" udp 514 type config
logging syslog ************ vrouter "mgt-vr" udp 514 type network
logging syslog ************ vrouter "mgt-vr" udp 514 type nbc
logging syslog ************ vrouter "mgt-vr" udp 514 type threat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic session
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic nat
logging syslog ************ vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog ************ vrouter "mgt-vr" udp 514 type debug
logging syslog *********** vrouter "mgt-vr" udp 514 type event
logging syslog *********** vrouter "mgt-vr" udp 514 type config
logging syslog *********** vrouter "mgt-vr" udp 514 type network
logging syslog *********** vrouter "mgt-vr" udp 514 type nbc
logging syslog *********** vrouter "mgt-vr" udp 514 type threat
logging syslog *********** vrouter "mgt-vr" udp 514 type traffic session
logging syslog *********** vrouter "mgt-vr" udp 514 type traffic nat
logging syslog *********** vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog *********** vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type nbc
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.235.2 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type event
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type config
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type network
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type nbc
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type threat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic session
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic nat
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type traffic pbr
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type debug
logging syslog 4.255.240.58 vrouter "trust-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" udp 514 type sandbox
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type event
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type config
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type network
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type nbc
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type threat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic session
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic nat
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic web-surf
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type traffic pbr
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type debug
logging syslog 4.255.240.88 vrouter "mgt-vr" tcp 9092 type sandbox
logging syslog 3.252.235.111 vrouter "mgt-vr" udp 514 type threat
logging syslog additional-information
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit      
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "CIMS_*********/24"
  ip *********/24
exit
address "host_***********"
  ip ***********/32
exit
address "payProxy_**********-214"
  range ********** **********
exit
address "payProxy_**********-64"
  range ********** **********
exit
address "weiXin_************"
  ip ************/32
exit
address "weiXin_************-6"
  range ************ ************
exit
address "host_***********"
  ip ***********/32
exit
address "weixin_dnat_***********"
  ip ***********/32
exit
address "sftp_dnat_************"
  ip ************/32
exit
address "payProxy_************-22"
  ip ************/32
  ip ************/32
exit
address "YYGS_FengKong"
  ip ***************/32
  ip ***************/32
  ip ***************/32
exit
address "JC_**********-2"
  ip **********/32
  ip **********/32
exit      
address "TC_APP_address_********"
  ip ********/24
exit
address "TC_APP_address_********"
  ip ********/24
exit
address "TC_APP_address_*********"
  ip *********/24
exit
address "TC_APP_address_*********"
  ip *********/24
exit
address "TC_APP_address_**********"
  ip **********/32
exit
address "TC_APP_address_*********"
  ip *********/32
exit
address "TC_APP_address_*********"
  ip *********/32
exit
address "TC_APP_address_********"
  ip ********/24
exit      
address "TC_APP_address_*********"
  ip *********/24
exit
address "TC_APP_address_********"
  ip ********/32
exit
address "TC_APP_address_********"
  ip ********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit      
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "500W_address_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********0"
  ip **********0/32
exit
address "TC_APP_NAT_***********"
  ip ***********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit      
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_**********"
  ip **********/32
exit
address "TC_APP_NAT_***********"
  ip ***********/32
exit
address "TC_APP_NAT_***********"
  ip ***********/32
exit
address "TC_APP_**********"
  ip **********/32
exit
address "TC_APP_**********"
  ip **********/32
exit
address "TC_APP_**********"
  ip **********/32
exit
address "TC_APP_************"
  ip ************/32
exit      
address "TC_APP_************"
  ip ************/32
exit
address "Juncai_************"
  ip ************/32
exit
address "Juncai_NAT_**********"
  ip **********/32
exit
address "500W_Info_address_***********"
  ip ***********/32
exit
address "SFTP_NAT_**********"
  ip **********/32
exit
address "SFTP_address_***********"
  ip ***********/32
exit
address "500W_address_**********3"
  ip **********3/32
exit
address "USAP_SSL_address_**********"
  ip **********/32
exit      
address "USAP_SSL_address_**********"
  ip **********/32
exit
address "TC_APP_TO_USAP_address"
  ip **********/32
  ip **********/32
  ip ***********/32
exit
address "psbc_address_************"
  ip ************/32
exit
address "psbc_NAT_**********"
  ip **********/32
exit
address "ELB_SSL_**********"
  ip **********/32
exit
address "ELB_SSL_NAT_**********"
  ip **********/32
exit
address "ELB_SFTP_***********"
  ip ***********/32
exit
address "ELB_SFTP_NAT_**********"
  ip **********/32
exit
address "payProxy_ng_************"
  ip ************/32
exit
address "payProxy_ng_************"
  ip ************/32
exit
address "payProxy_ng_************"
  ip ************/32
exit
address "payProxy_ng_************"
  ip ************/32
exit
address "payProxy_ng_NAT_**********"
  ip **********/32
exit
address "payProxy_ng_NAT_**********"
  ip **********/32
exit
address "payProxy_ng_NAT_**********"
  ip **********/32
exit
address "payProxy_ng_NAT_**********"
  ip **********/32
exit
address "JC_**********"
  ip **********/32
exit
address "weiXin_************"
  ip ************/32
exit
address "YYXNH_address_*************"
  ip *************/24
exit
address "YYXNH_address_*************"
  ip *************/24
exit
address "YYXNH_address_*************"
  ip *************/24
exit
address "YYXNH_address_*************"
  ip *************/24
exit
address "TC_APP_address_********"
  ip ********/24
exit
address "TC_APP_address_*********"
  ip *********/24
exit
address "TC_APP_NAT_***********"
  ip ***********/32
exit
address "TC_APP_NAT_***********"
  ip ***********/32
exit
address "KFPT_SSL_NAT_***********"
  ip ***********/32
exit
address "KFPT_SSL_**********"
  ip **********/32
exit
address "TCAPP_AL_*********/24"
  ip *********/24
exit
address "TCAPP_AL_*********/24"
  ip *********/24
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP_AL_*********/24"
  ip *********/24
exit
address "TCAPP_AL_*********/24"
  ip *********/24
exit
address "TCAPP_AL_**********"
  ip **********/32
exit
address "TCAPP_AL_**********"
  ip **********/32
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP***********"
  ip ***********/32
exit
address "TCAPP***********"
  ip ***********/32
exit
address "TCAPP*********"
  ip *********/32
exit
address "TCAPP*********"
  ip *********/32
exit
address "TCAPP*********"
  ip *********/32
exit
address "YYXNH_address_*************"
  ip *************/24
exit
address "YYXNH_address_*************"
  ip *************/24
exit
address "YYXNH_address_*************"
  ip *************/24
exit
address "TCAPP_AL_**********"
  ip **********/32
exit
address "TCAPP_AL_**********"
  ip **********/32
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP_AL_NAT_***********"
  ip ***********/32
exit
address "TCAPP_AL_NAT_**********1"
  ip **********1/32
exit
address "TCAPP_AL_NAT_**********2"
  ip **********2/32
exit
address "TCAPP_TCloud_***********/24"
  ip ***********/24
exit
address "TCAPP_TCloud_***********/24"
  ip ***********/24
exit
address "TCAPP_TCloud_***********/24"
  ip ***********/24
exit
address "TCAPP_TCloud_***********/24"
  ip ***********/24
exit
address "TCAPP_TCloud_*************"
  ip *************/32
exit
address "TCAPP_TCloud_***********"
  ip ***********/32
exit
address "TCAPP_TCloud_NAT_**********5"
  ip **********5/32
exit
address "TCAPP_TCloud_NAT_**********6"
  ip **********6/32
exit
address "TCAPP_TCloud_NAT_**********7"
  ip **********7/32
exit
address "TCAPP_TCloud_NAT_**********8"
  ip **********8/32
exit
address "TCAPP_TCloud_NAT_**********9"
  ip **********9/32
exit
address "TCAPP_TCloud_NAT_**********0"
  ip **********0/32
exit
address "TCAPP_AL_NAT_NET-1"
  range **********0 **********4
exit
address "TCAPP_AL_NAT_NET-2"
  range **********5 **********9
exit
address "TCAPP_TX_NAT_**********3"
  ip **********3/32
exit
address "TCAPP_TCloud_***********/24"
  ip ***********/24
exit
zone "mgt"
  vrouter "mgt-vr"
exit
zone "trust"
  vrouter "E-FW"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "untrust"
  vrouter "E-FW"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad syn-flood destination-threshold 15000
  ad syn-flood source-threshold 15000
  ad syn-flood destination ip-based
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
  ad udp-flood destination-threshold 15000
  ad udp-flood source-threshold 15000
  ad udp-flood action alarm
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
hostname "XWEAFW03-B1E03"
admin host any any
web same-account-login enable
no https client-auth match
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit

isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit

isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit

isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit
          
interface MGT0 local
  zone  "mgt"
  ip address ************* *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface ethernet0/4
  aggregate aggregate2
exit
interface ethernet0/5
  aggregate aggregate2
exit
interface xethernet0/8
  aggregate aggregate1
exit
interface xethernet0/9
  aggregate aggregate1
exit
interface aggregate1
  zone  "trust"
  ip address ************ ***************
  manage ping
  lacp enable
  no reverse-route
exit
interface aggregate2
  zone  "untrust"
  ip address ************ ***************
  manage ping
  lacp enable
  no reverse-route
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 *************
exit
ip vrouter "E-FW"
  snatrule id 1 from ip ************ to address-book "Any" service "Any" trans-to ip *********** mode static log 
  snatrule id 2 from ip ************ to address-book "Any" service "Any" trans-to ip *********** mode static log 
  snatrule id 3 from ip ********** to address-book "Any" service "Any" trans-to ip *********** mode static log disable 
  snatrule id 4 from ip ********** to address-book "Any" service "Any" trans-to ip *********** mode static log disable 
  snatrule id 5 from ip ********** to address-book "Any" service "Any" trans-to ip ***********1 mode static log 
  snatrule id 6 from ip ********** to address-book "Any" service "Any" trans-to ip ***********2 mode static log 
  snatrule id 7 from ip ********** to address-book "Any" service "Any" trans-to ip ***********3 mode static log 
  snatrule id 8 from ip ********** to address-book "Any" service "Any" trans-to ip ************ mode static log 
  snatrule id 1001 from ip ************ to address-book "Any" service "Any" trans-to ip ***********3 mode static log 
  snatrule id 1002 from ip ************ to address-book "Any" service "Any" trans-to ip ***********4 mode static log 
  snatrule id 1003 from ip ************ to address-book "Any" service "Any" trans-to ip ***********5 mode static log 
  snatrule id 1004 from ip ************ to address-book "Any" service "Any" trans-to ip ***********6 mode static log 
  snatrule id 1008 from address-book "TC_APP_address_********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_**********" mode dynamicport log 
  snatrule id 1009 from address-book "TC_APP_address_********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_**********0" mode static log 
  snatrule id 1010 from address-book "TC_APP_address_********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_***********" mode static log 
  snatrule id 1011 from address-book "TC_APP_address_********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_**********" mode dynamicport log 
  snatrule id 1012 from address-book "TC_APP_address_*********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_**********" mode dynamicport log 
  snatrule id 1013 from address-book "TC_APP_address_*********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_**********" mode dynamicport log 
  snatrule id 1015 from address-book "TC_APP_address_********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_**********" mode dynamicport log 
  snatrule id 1016 from address-book "TC_APP_address_*********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_**********" mode dynamicport log 
  snatrule id 9 from address-book "TC_APP_************" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_***********" mode static log 
  snatrule id 10 from address-book "TC_APP_************" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_***********" mode static log 
  snatrule id 100 from address-book "psbc_address_************" to address-book "Any" service "Any" trans-to address-book "psbc_NAT_**********" mode static log 
  snatrule id 101 from address-book "payProxy_ng_************" to address-book "Any" service "Any" trans-to address-book "payProxy_ng_NAT_**********" mode static log 
  snatrule id 102 from address-book "payProxy_ng_************" to address-book "Any" service "Any" trans-to address-book "payProxy_ng_NAT_**********" mode static log 
  snatrule id 103 from address-book "payProxy_ng_************" to address-book "Any" service "Any" trans-to address-book "payProxy_ng_NAT_**********" mode static log 
  snatrule id 104 from address-book "payProxy_ng_************" to address-book "Any" service "Any" trans-to address-book "payProxy_ng_NAT_**********" mode static log 
  snatrule id 1005 from ip *************** to address-book "Any" service "Any" trans-to ip ********** mode static log disable 
  snatrule id 1006 from ip *************** to address-book "Any" service "Any" trans-to ip ********** mode static log disable 
  snatrule id 1007 from ip *************** to address-book "Any" service "Any" trans-to ip ********** mode static log 
  snatrule id 1017 from ip ************ to address-book "Any" service "Any" trans-to ip ************ mode static log 
  snatrule id 11 from ip *********** to address-book "Any" service "Any" trans-to ip *********** mode static log 
  snatrule id 12 from ip *********** to address-book "Any" service "Any" trans-to ip *********** mode static log 
  snatrule id 1018 from ip *************** to address-book "Any" service "Any" trans-to ip ********** mode static log disable 
  snatrule id 13 from address-book "YYXNH_address_*************" to address-book "Any" service "Any" trans-to ip **********51 mode dynamicport log disable 
  snatrule id 14 from address-book "YYXNH_address_*************" to address-book "Any" service "Any" trans-to ip **********52 mode dynamicport log disable 
  snatrule id 15 from address-book "YYXNH_address_*************" to address-book "Any" service "Any" trans-to ip **********53 mode dynamicport log disable 
  snatrule id 16 from address-book "YYXNH_address_*************" to address-book "Any" service "Any" trans-to ip **********54 mode dynamicport log disable 
  snatrule id 17 from address-book "TC_APP_address_********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_***********" mode dynamicport log 
  snatrule id 18 from address-book "TC_APP_address_*********" to address-book "Any" service "Any" trans-to address-book "TC_APP_NAT_***********" mode dynamicport log 
  snatrule id 19 from address-book "TCAPP_AL_*********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_***********" mode dynamicport log 
  snatrule id 20 from address-book "TCAPP_AL_*********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_***********" mode dynamicport log 
  snatrule id 21 from address-book "TCAPP_AL_*********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_NET-1" mode dynamicport log 
  snatrule id 22 from address-book "TCAPP_AL_*********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_NET-2" mode dynamicport log 
  snatrule id 23 from address-book "TCAPP_AL_**********" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_***********" mode static log 
  snatrule id 24 from address-book "TCAPP_AL_**********" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_***********" mode static log 
  snatrule id 25 from address-book "YYXNH_address_*************" to address-book "Any" service "Any" trans-to ip **********55 mode dynamicport log disable 
  snatrule id 26 from address-book "YYXNH_address_*************" to address-book "Any" service "Any" trans-to ip **********56 mode dynamicport log disable 
  snatrule id 27 from address-book "YYXNH_address_*************" to address-book "Any" service "Any" trans-to ip **********99 mode dynamicport log 
  snatrule id 28 from address-book "TCAPP_AL_**********" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_**********1" mode static log 
  snatrule id 29 from address-book "TCAPP_AL_**********" to address-book "Any" service "Any" trans-to address-book "TCAPP_AL_NAT_**********2" mode static 
  snatrule id 30 from address-book "TCAPP_TCloud_***********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_TCloud_NAT_**********5" mode dynamicport log 
  snatrule id 31 from address-book "TCAPP_TCloud_***********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_TCloud_NAT_**********6" mode dynamicport log 
  snatrule id 32 from address-book "TCAPP_TCloud_***********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_TCloud_NAT_**********7" mode dynamicport log 
  snatrule id 33 from address-book "TCAPP_TCloud_***********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_TCloud_NAT_**********8" mode dynamicport 
  snatrule id 34 from address-book "TCAPP_TCloud_***********/24" to address-book "Any" service "Any" trans-to address-book "TCAPP_TX_NAT_**********3" mode dynamicport 
  dnatrule id 1001 from address-book "Any" to ip *********** service "Any" trans-to ip ************ log 
  dnatrule id 2 from address-book "Any" to ip ********** service "Any" trans-to ip *********** log 
  dnatrule id 3 from address-book "Any" to ip ********** service "Any" trans-to ip *********** log 
  dnatrule id 11 from address-book "Any" to address-book "TC_APP_NAT_**********" service "Any" trans-to address-book "TC_APP_**********" log 
  dnatrule id 12 from address-book "Any" to address-book "TC_APP_NAT_**********" service "Any" trans-to address-book "TC_APP_**********" log 
  dnatrule id 13 from address-book "Any" to address-book "TC_APP_NAT_**********" service "Any" trans-to address-book "TC_APP_**********" log 
  dnatrule id 14 from address-book "Any" to address-book "Juncai_NAT_**********" service "Any" trans-to address-book "Juncai_************" log 
  dnatrule id 15 from address-book "Any" to address-book "TC_APP_NAT_**********" service "Any" trans-to address-book "TC_APP_address_**********" log 
  dnatrule id 16 from address-book "Any" to address-book "TC_APP_NAT_**********" service "Any" trans-to address-book "TC_APP_address_*********" log 
  dnatrule id 4 from address-book "Any" to address-book "SFTP_NAT_**********" service "Any" trans-to address-book "SFTP_address_***********" log 
  dnatrule id 5 from address-book "Any" to address-book "USAP_SSL_address_**********" service "Any" trans-to address-book "USAP_SSL_address_**********" log 
  dnatrule id 20 from address-book "Any" to address-book "ELB_SSL_NAT_**********" service "Any" trans-to address-book "ELB_SSL_**********" log 
  dnatrule id 21 from address-book "Any" to address-book "ELB_SFTP_NAT_**********" service "Any" trans-to address-book "ELB_SFTP_***********" log 
  dnatrule id 6 from address-book "Any" to address-book "psbc_NAT_**********" service "Any" trans-to address-book "psbc_address_************" log 
  dnatrule id 7 from address-book "Any" to ip *********** service "Any" trans-to ip ********* log 
  dnatrule id 8 from address-book "Any" to ip *********** service "Any" trans-to ip ********* log 
  dnatrule id 9 from address-book "Any" to ip *********** service "Any" trans-to ip ********** log 
  dnatrule id 10 from address-book "Any" to ip ********** service "Any" trans-to ip *********** log 
  dnatrule id 17 from address-book "Any" to address-book "KFPT_SSL_NAT_***********" service "Any" trans-to address-book "KFPT_SSL_**********" log 
  dnatrule id 1 from address-book "Any" to ip ************ service "Any" trans-to ip ********** log 
  dnatrule id 22 from address-book "Any" to address-book "TCAPP_TCloud_NAT_**********9" trans-to address-book "TCAPP_TCloud_*************" 
  dnatrule id 23 from address-book "Any" to address-book "TCAPP_TCloud_NAT_**********0" trans-to address-book "TCAPP_TCloud_***********" 
  dnatrule id 18 from address-book "Any" to address-book "TCAPP***********" service "TCP_8080" trans-to ip ************ port 8080 log 
  dnatrule id 19 from address-book "Any" to address-book "TCAPP***********" service "TCP_20110" trans-to ip ************ port 20110 log 
  ip route *********/16 ************
  ip route ***********/32 ************
  ip route *********/24 ************
  ip route 0.0.0.0/0 ************
  ip route ********/24 ************
  ip route *********/24 ************ description "WeiXin_LinShiLuYou_ChenChaoA"
  ip route *********/24 ************ description "WeiXin_LinShiLuYou_ChenChaoA"
  ip route ***********/24 ************ description "JunCaiGongSi_CSLJC"
  ip route ***********/24 ************
  ip route ***********/24 ************
  ip route *********/24 ************
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp enable
ntp server ******* vrouter mgt-vr
clock zone china
rule id 1
  action permit
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "ICMP"
exit
rule id 2
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "CIMS_*********/24"
  src-addr "host_***********"
  dst-addr "Any"
  service "SSH"
exit
rule id 3
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "payProxy_**********-64"
  dst-addr "weixin_dnat_***********"
  service "HTTPS"
  name "WeiXinZhiFu"
exit
rule id 4
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "weiXin_************-6"
  src-addr "weiXin_************"
  dst-addr "sftp_dnat_************"
  service "SFTP22"
  name "WeiXinDuiZhang"
exit
rule id 5
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "payProxy_************-22"
  dst-addr "weixin_dnat_***********"
  service "HTTPS"
  name "微信支付"
exit
rule id 6
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYGS_FengKong"
  dst-addr "JC_**********"
  service "HTTPS"
  name "运营风控访问骏彩"
exit
rule id 7
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_*********"
  src-addr "TC_APP_address_*********"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  dst-addr "TC_APP_**********"
  dst-addr "TC_APP_NAT_**********"
  service "HTTPS"
  name "体彩APP访问实体渠道"
exit      
rule id 8
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_********"
  dst-addr "Juncai_************"
  dst-addr "Juncai_NAT_**********"
  service "TCP_52701"
  name "体彩AP访问骏彩"
exit
rule id 9
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_*********"
  src-addr "TC_APP_address_*********"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  dst-addr "TC_APP_**********"
  dst-addr "TC_APP_NAT_**********"
  service "HTTPS"
  name "体彩APP访问业务中台"
exit
rule id 10
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "TC_APP_************"
  src-addr "TC_APP_************"
  dst-addr "TC_APP_address_**********"
  dst-addr "TC_APP_address_*********"
  dst-addr "TC_APP_NAT_**********"
  dst-addr "TC_APP_NAT_**********"
  dst-addr "TCAPP***********"
  dst-addr "TCAPP_TCloud_NAT_**********9"
  dst-addr "TCAPP_TCloud_*************"
  service "TCP_8080"
  name "业务中台至体彩APP消息发送"
exit      
rule id 11
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "500W_address_**********"
  dst-addr "TC_APP_**********"
  dst-addr "TC_APP_NAT_**********"
  service "HTTPS"
  name "500W问业务中台"
exit
rule id 12
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "TC_APP_************"
  src-addr "TC_APP_************"
  dst-addr "500W_Info_address_***********"
  service "HTTPS"
  name "500W至体彩APP消息发送"
exit
rule id 13
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "500W_address_**********3"
  dst-addr "SFTP_NAT_**********"
  dst-addr "SFTP_address_***********"
  service "TCP_30000"
  name "500w访问SFTP"
exit
rule id 14
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "TC_APP_TO_USAP_address"
  src-addr "TCAPP_AL_**********"
  src-addr "TCAPP_AL_**********"
  src-addr "TCAPP_AL_**********"
  src-addr "TCAPP_AL_**********"
  src-addr "TCAPP_TCloud_***********"
  src-addr "TCAPP_TCloud_*************"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  dst-addr "USAP_SSL_address_**********"
  dst-addr "USAP_SSL_address_**********"
  service "HTTPS"
  name "数字化运营访问USAP"
exit
rule id 15
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "psbc_address_************"
  dst-addr "ELB_SSL_NAT_**********"
  dst-addr "ELB_SSL_**********"
  service "HTTPS"
  name "邮储银行访问彩银业务"
exit
rule id 16
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "psbc_address_************"
  dst-addr "ELB_SFTP_NAT_**********"
  dst-addr "ELB_SFTP_***********"
  service "SFTP22"
  name "邮储银行访问彩银SFTP"
exit
rule id 17
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-addr "payProxy_ng_************"
  src-addr "payProxy_ng_************"
  src-addr "payProxy_ng_************"
  src-addr "payProxy_ng_************"
  dst-addr "psbc_address_************"
  dst-addr "psbc_NAT_**********"
  service "TCP_28601"
  name "彩银出访邮储银行"
exit
rule id 18
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-ip ***********/32
  src-ip ***********/32
  dst-ip ***********/32
  dst-ip ***********/32
  service "TCP_20006"
  name "开放平台到运营公司业主端服务"
exit
rule id 19
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "TC_APP_address_*********"
  src-addr "TC_APP_address_********"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  dst-addr "ELB_SFTP_NAT_**********"
  dst-addr "ELB_SFTP_***********"
  service "SFTP22"
  name "数字化运营中心访问SFTP"
exit
rule id 20
  action permit
  src-zone "trust"
  dst-zone "untrust"
  src-ip ***********/32
  src-ip ***********/32
  dst-addr "TCAPP***********"
  dst-addr "TCAPP_TCloud_NAT_**********0"
  dst-addr "TCAPP_TCloud_***********"
  dst-ip ***********/32
  service "TCP_20110"
  name "开放平台NG访问竞彩大额预约二维码"
exit      
rule id 21
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-ip ***************/32
  dst-ip **********/32
  service "TCP_52701"
  name "综合运营中心虚拟化访问WEBDC"
exit
rule id 22
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "YYXNH_address_*************"
  src-addr "YYXNH_address_*************"
  src-addr "YYXNH_address_*************"
  src-addr "YYXNH_address_*************"
  src-addr "YYXNH_address_*************"
  src-addr "YYXNH_address_*************"
  src-addr "YYXNH_address_*************"
  dst-ip **********/32
  service "SFTP22"
  name "综合运营中心虚拟化访问SFTP"
exit      
rule id 23
  action permit
  src-zone "untrust"
  dst-zone "trust"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_********"
  src-addr "TC_APP_address_*********"
  src-addr "TC_APP_address_*********"
  src-addr "TC_APP_address_*********"
  src-addr "TC_APP_address_*********"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_AL_*********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  src-addr "TCAPP_TCloud_***********/24"
  dst-addr "KFPT_SSL_NAT_***********"
  service "HTTPS"
  name "体彩APP访问开放平台"
exit      
l2-nonip-action drop
no alg auto
no alg tftp
no alg msrpc
no alg sqlnetv2
no alg rsh
no alg rtsp
no alg http
no alg sunrpc
no alg ras
no alg q931
no alg sip
no alg pptp
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server vrouter "mgt-vr"
snmp-server engineID "hillstone"
snmp-server host *********** version 2c community IQKm6jOQzQw3266DzxLL5hlJXP8D ro
snmp-server host ************ version 2c community fj7VB6vTZUNWzD1fJOwUEirolEo7 ro
snmp-server trap-host *********** version 2c community ckeI3SSNGQhyyPo3P+lMJmQ8mGku port 162
snmp-server trap-host ************ version 2c community DWbf06+a4P0FlgO+Yrtfp2YzSHMK port 162
snmp-server trap-host ************ version 2c community iYrqDOGOtTqwQ38yjPmPA3Sw3FEJ port 162
snmp-server trap-host ************ version 2c community eW17D2EjwWzsWNIGeGY+emJVWOwr port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
  icmp-unreachable-session-keep
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional
exit
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application
exit
no sms disable
ha link interface HA0
ha link data interface ethernet0/7
ha link ip ******* ***************
ha group 0
  monitor track "JianCe"
exit
ha cluster 2 node 0

End

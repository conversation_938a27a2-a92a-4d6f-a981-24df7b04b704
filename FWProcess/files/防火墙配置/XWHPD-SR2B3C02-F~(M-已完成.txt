XWHPD-SR2B3C02-F~(M)# show config

Building configuration..
Running configuration:
!
Version 5.5R4

ip vrouter "mgt-vr"
exit
ip vrouter "twin-mode-vr"
exit
ip vrouter "trust-vr"
exit
ha group 0
exit
vswitch "vswitch1"
exit
zone "mgt"
exit
zone "trust"
exit
zone "untrust"
exit
zone "dmz"
exit
zone "l2-trust" l2
exit
zone "l2-untrust" l2
exit      
zone "l2-dmz" l2
exit
zone "VPNHub"
exit
zone "HA"
exit
zone "twin-mode"
exit
zone "DMZ"
exit
zone "Internet"
exit
zone "Inside"
exit
interface vswitchif1
exit
interface MGT0 local
exit
interface HA0
exit
interface ethernet0/0
exit
interface ethernet0/1
exit      
interface ethernet0/2
exit
interface ethernet0/3
exit
interface ethernet0/4
exit
interface ethernet0/5
exit
interface ethernet0/6
exit
interface ethernet0/7
exit
interface xethernet0/8
exit
interface xethernet0/9
exit
interface xethernet4/0
exit
interface xethernet4/1
exit
interface xethernet4/2
exit
interface xethernet4/3
exit      
interface loopback1
exit
interface loopback2
exit
interface aggregate1
exit
interface aggregate2
exit
interface aggregate2.3033
exit
interface aggregate2.3034
exit
interface aggregate2.3035
exit
address "private_network"
exit
address "V3_MS_*************/32"
exit
address "V3_DMZ_*********/24"
exit
address "BOCC-********/24"
exit
address "BOCC_*********"
exit      
address "BOCC-*********"
exit
address "*********/32"
exit
address "*********/32"
exit
address "Mail_************"
exit
address "tiaobanji-**********"
exit
address "DNS_*******"
exit
address "DNS_***************"
exit
address "DNS-***********"
exit
address "DNS-***********"
exit
address "*********/16"
exit
address "AlarManager-************-72"
exit
address "MS-***********/24"
exit      
address "TCP-***********"
exit
address "Zabbix-Proxy-*********51"
exit
address "ZabbixServer"
exit
address "NTP-**********"
exit
address "NTp-***********"
exit
address "4A-*********/24"
exit
address "Solarwinds-*************"
exit
address "Solarwinds-*************"
exit
address "G2-Mail-**********"
exit
address "************"
exit
address "MS-***********/24"
exit
address "************1"
exit      
address "*************"
exit
address "DNS-***********-52"
exit
address "DMZ_NAS_**********/22"
exit
address "NAS_Public_**********/24"
exit
address "Solarwinds-**********"
exit
address "G3-Test-***********/32"
exit
address "G3-Test-**********/24"
exit
address "G3-Test-*********/24"
exit
address "***********/24"
exit
address "G2OCS-**********/32"
exit
address "BOCC&4A"
exit
address "G3_*********/16"
exit      
address "G2_Mail_***********/32"
exit
address "**********"
exit
address "SSLF5-***********"
exit
address "DMZ-SSL-*********/24"
exit
address "WCS-F5-***********"
exit
address "*********"
exit
address "CSLC-baoleiji-*********/24"
exit
address "***********-6"
exit
address "MS-K8S-***********/24"
exit
address "*************-212"
exit
address "FOC-*********"
exit
address "FOC-***********"
exit      
address "G2-TAS-SSL-*********"
exit
address "OPSTOOL-***********81-182"
exit
address "G3_CORE_4.190.83.1"
exit
address "G3_CORE_4.190.83.2"
exit
address "G3WCSINFOS-*********1-22"
exit
address "DNS-************"
exit
address "DNS-*********"
exit
address "GWREDIS-**********/24"
exit
address "G3WCSINFOSFTP-***********-42"
exit
address "G3WCSINFOSFTP-F5-***********"
exit
address "G3TOHERAPROXY-*********1-12"
exit
address "G3GAIA-F5-************"
exit      
address "G3MONITORGAIA-*************-162"
exit
address "G3TOHERAPROXY-F5-***********"
exit
address "G3-MS-K8S-**********/24"
exit
address "G3MONITORPORTAL-F5-************"
exit
address "G3JCFAUTHCASSERVER-F5-************"
exit
address "OJobServer-**********-162"
exit
address "OCS-FTP-**********"
exit
address "ConfigCenter-F5-***********"
exit
address "ConfigCenter-************-143"
exit
address "SYSLOG-F5-************"
exit
address "G3_MS_AIDB_4.190.122.121-123"
exit
address "G3_Mail_*********/32"
exit      
address "G3_CORE_NTP_4.190.80.251-252"
exit
address "SBSG2BISRVA11-**********"
exit
address "SBSG2BISRVA12-**********"
exit
address "SBSG2BISRVA13-**********"
exit
address "BASDB-***********"
exit
address "BLIDB-************"
exit
address "Ansbile-************"
exit
address "SBSIRMBMO-**********-32"
exit
address "SBSG2IRMDBVIP-**********"
exit
address "IRM-F5-***********6"
exit
address "SBSG2OPSFTP01-************"
exit
address "ItoSchedule-*************-172"
exit      
address "SBSG2OTJob-***********"
exit
address "CSLC-Hive-*********/32"
exit
address "MS-***********/22"
exit
address "CSLC-KYLIN-************"
exit
address "CSLC-PHOENIX-************-57"
exit
address "RTQDBVIP-***********"
exit
address "CSLC-SSL4.103.211.0/24"
exit
address "SSL-*********"
exit
address "G2BASDB-*********"
exit
address "************"
exit
address "***********/24"
exit
address "**********"
exit      
address "G3-CORE-SDAS-************-192"
exit
address "**************"
exit
address "CSLC-baoleiji-**********/24"
exit
address "RMOAS-**********/24"
exit
address "FOC-***********-137"
exit
address "QiYeWeiXin-***********/16"
exit
address "QiYeWeiXin-*************/32"
exit
address "SFTP-**********00"
exit
address "W5RRMSFSC01"
exit
address "NAT-************-154"
exit
address "W5RRMSFSC01-F5"
exit
address "CSLRMSFSP01"
exit      
address "CSLCOPCC-*********"
exit
address "IRM-F5-*********"
exit
address "*********"
exit
address "W5RBOCC"
exit
address "Ansbile-************"
exit
address "YZBOCC"
exit
address "YZBOCC_4.128.10.3"
exit
address "G3TSPAPP01-*************"
exit
address "G3TSPAPP02-*************"
exit
address "***********-14"
exit
address "************-42"
exit
address "USAP-LB-***********"
exit      
address "SDAS-F5-4.190.166.2"
exit
address "SDAS-F5-4.190.166.0/24"
exit
address "USAP-***********-14"
exit
address "K8S-CORE-4.190.84.0/24"
exit
address "Sporttery-Mail-111.205.58.68"
exit
address "NAT-4.98.130.155-156"
exit
address "NAT-4.98.130.199"
exit
address "4.35.10.10"
exit
address "G3BISMONTORCOLLECT"
exit
address "VulnerabilityScan-************"
exit
address "4.98.1.18-25"
exit
address "4.35.10.10/32"
exit      
address "4.14.100.32"
exit
address "YJBOCC_9.66.32.0"
exit
aaa-server "local" type local
exit
track "HA"
exit
logging nat content hostname
service "TCP_31050"
  tcp dst-port 31050 
exit
service "TCP-31051"
  tcp dst-port 31051 
exit
service "TCP-31306"
  tcp dst-port 31306 
exit
service "TCP_3555"
  tcp dst-port 3555 
exit
service "TCP_3558"
  tcp dst-port 3558 
exit      
service "TCP_8080"
  tcp dst-port 8080 
exit
service "TCP_8000"
  tcp dst-port 8000 
exit
service "UDP_123"
  tcp dst-port 123 
exit
service "TCP-8086"
  tcp dst-port 8086 
exit
service "TCP-8090"
  tcp dst-port 8090 
exit
service "TCP_8013"
  tcp dst-port 8013 
exit
service "TCP_8888"
  tcp dst-port 8888 
exit
service "TCP_8889"
  tcp dst-port 8889 
exit      
service "TCP_3389"
  tcp dst-port 3389 
exit
service "TCP-29092"
  tcp dst-port 29092 
exit
service "TCP-25"
  tcp dst-port 25 
exit
service "TCP-6370"
  tcp dst-port 6370 
exit
service "TCP-5003"
  tcp dst-port 5003 
exit
service "TCP-3191"
  tcp dst-port 3191 
exit
service "TCP-7001"
  tcp dst-port 7001 
exit
service "TCP-2379"
  tcp dst-port 2379 
exit      
service "TCP-28080"
  tcp dst-port 28080 
exit
service "TCP-28083"
  tcp dst-port 28083 
exit
service "TCP-28081"
  tcp dst-port 28081 
exit
service "TCP-28082"
  tcp dst-port 28082 
exit
service "TCP-28070"
  tcp dst-port 28070 
exit
service "TCP-8443"
  tcp dst-port 8443 
exit
service "UDP-8472"
  udp dst-port 8472 
exit
service "TCP-6100-6200"
  tcp dst-port 6100 6200 
exit      
service "TCP-7070"
  tcp dst-port 7070 
exit
service "TCP-2181"
  tcp dst-port 2181 
exit
service "TCP-9443"
  tcp dst-port 9443 
exit
service "TCP-8023-8024"
  tcp dst-port 8023 8024 
exit
service "TCP-16000"
  tcp dst-port 16000 
exit
service "TCP-16010"
  tcp dst-port 16010 
exit
service "TCP-16020"
  tcp dst-port 16020 
exit
service "TCP-8021-8025"
  tcp dst-port 8021 8025 
exit      
service "TCP-8021"
  tcp dst-port 8021 
exit
service "TCP-8025"
  tcp dst-port 8025 
exit
service "TCP-8022"
  tcp dst-port 8022 
exit
service "TCP-8088"
  tcp dst-port 8088 
exit
service "TCP-19080"
  tcp dst-port 19080 
exit
service "TCP-10251-10252"
  tcp dst-port 10251 10252 
exit
service "TCP-4100-4130"
  tcp dst-port 4100 4130 
exit
service "TCP-5000-5030"
  tcp dst-port 5000 5030 
exit      
service "TCP-9100"
  tcp dst-port 9100 
exit
service "TCP-7100"
  tcp dst-port 7100 
exit
service "TCP_21050"
  tcp dst-port 21050 
exit
service "TCP-6677"
  tcp dst-port 6677 
exit
service "TCP-7788"
  tcp dst-port 7788 
exit
service "TCP-8001"
  tcp dst-port 8001 
exit
service "TCP-8002"
  tcp dst-port 8002 
exit
service "TCP-80"
  tcp dst-port 80 
exit      
sandbox-profile "predef_low"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit
sandbox-profile "predef_middle"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
  whitelist enable
  certificate-validation enable
exit      
sandbox-profile "predef_high"
  file-type pe
  file-type apk
  file-type jar
  file-type pdf
  file-type ms-office
  file-type swf
  file-type rar
  file-type zip
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
sandbox-profile "predef_pe"
  file-type pe
  protocol HTTP direction both
  protocol FTP direction both
  protocol SMTP direction upload
  protocol POP3 direction download
  protocol IMAP4 direction download
exit
url-profile "no-url"
exit
track "HA"
  interface xethernet4/0 
  interface xethernet4/1 
  interface xethernet4/2 
  interface xethernet4/3 
exit
admin user "hillstone"
  password Mf/LrMeXbQHckjO8uTxVWK+AIe
        password-expiration 1588124123
  role "admin"
  access console
  access telnet
  access ssh
  access http
  access https
exit
admin user "operator"
  password 1vtYt4b7EH2+Q0cvQ9XmHnyQ2s
        password-expiration 1604544901
  access console
  access telnet
  access ssh
  access http
  access https
  description "read only" 
exit
logging event to console severity debugging
logging event to syslog 
logging network to syslog 
logging traffic session on
logging traffic nat on
logging syslog ************ source-interface "MGT0" udp 514 type event
logging syslog ************ source-interface "MGT0" udp 514 type config
logging syslog ************ source-interface "MGT0" udp 514 type network
logging syslog ************ source-interface "MGT0" udp 514 type threat
logging syslog ************ source-interface "MGT0" udp 514 type traffic session
logging syslog ************ source-interface "MGT0" udp 514 type debug
logging syslog ********** source-interface "MGT0" udp 514 type event
logging syslog ********** source-interface "MGT0" udp 514 type config
logging syslog ********** source-interface "MGT0" udp 514 type network
logging syslog ********** source-interface "MGT0" udp 514 type threat
logging syslog ********** source-interface "MGT0" udp 514 type debug
pki trust-domain "trust_domain_default"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy"
  keypair "Default-Key"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "trust_domain_ssl_proxy_2048"
  keypair "Default-Key-2048"
  enrollment self
  subject commonName "SG-6000"
  subject organization "Hillstone Networks"
exit
pki trust-domain "network_manager_ca"
  enrollment terminal
exit
address "private_network"
  ip 10.0.0.0/8
  ip **********/12
  ip ***********/16
exit
address "V3_MS_*************/32"
  ip *************/32
exit      
address "V3_DMZ_*********/24"
  ip *********/24
exit
address "BOCC-********/24"
  ip ********/24
exit
address "BOCC_*********"
  ip *********/24
exit
address "BOCC-*********"
  ip *********/24
exit
address "*********/32"
  ip *********/32
exit
address "*********/32"
  ip *********/32
exit
address "Mail_************"
  ip ************/32
exit
address "tiaobanji-**********"
  ip **********/32
exit      
address "DNS_*******"
  ip *******/32
exit
address "DNS_***************"
  ip ***************/32
exit
address "DNS-***********"
  ip ***********/32
exit
address "DNS-***********"
  ip ***********/32
exit
address "*********/16"
  ip *********/16
exit
address "AlarManager-************-72"
  range ************ ************
exit
address "MS-***********/24"
  ip ***********/24
exit
address "TCP-***********"
  ip ***********/24
exit      
address "Zabbix-Proxy-*********51"
  ip *********51/32
exit
address "ZabbixServer"
  range ************ ************
exit
address "NTP-**********"
  ip **********/32
exit
address "NTp-***********"
  ip ***********/32
exit
address "4A-*********/24"
  ip *********/24
exit
address "Solarwinds-*************"
  ip *************/32
exit
address "Solarwinds-*************"
  ip *************/32
exit
address "G2-Mail-**********"
  ip **********/32
exit      
address "************"
  ip ************/32
exit
address "MS-***********/24"
  ip ***********/24
exit
address "************1"
  ip ************1/32
exit
address "*************"
  ip *************/32
exit
address "DNS-***********-52"
  range *********** ***********
exit
address "DMZ_NAS_**********/22"
  ip **********/22
exit
address "NAS_Public_**********/24"
  ip **********/24
exit
address "Solarwinds-**********"
  ip **********/32
exit      
address "G3-Test-***********/32"
  ip ***********/32
exit
address "G3-Test-**********/24"
  ip **********/24
exit
address "G3-Test-*********/24"
  ip *********/24
exit
address "***********/24"
  ip ***********/24
  ip ***********/24
  ip ***********/24
  ip ***********/24
exit
address "G2OCS-**********/32"
  ip **********/32
exit
address "BOCC&4A"
  ip ********/24
  ip *********/24
  ip *********/24
  ip *********/24
  ip *********/24
  ip *********/24
  ip *********/24
  ip ********/24
  ip ********/24
  ip ********/24
exit
address "G3_*********/16"
  ip *********/16
exit
address "G2_Mail_***********/32"
  ip ***********/32
exit
address "**********"
  ip **********/32
exit
address "SSLF5-***********"
  ip ***********/32
exit
address "DMZ-SSL-*********/24"
  ip *********/24
exit
address "WCS-F5-***********"
  ip ***********/32
exit      
address "*********"
  ip *********/24
exit
address "CSLC-baoleiji-*********/24"
  ip *********/24
exit
address "***********-6"
  range *********** 4.190.121.5
exit
address "MS-K8S-***********/24"
  ip ***********/24
exit
address "*************-212"
  range ************* 4.190.121.212
exit
address "FOC-*********"
  ip *********/32
exit
address "FOC-***********"
  ip ***********/32
exit
address "G2-TAS-SSL-*********"
  ip *********/32
exit      
address "OPSTOOL-***********81-182"
  range ***********81 ***********82
exit
address "G3_CORE_4.190.83.1"
  ip 4.190.83.1/32
exit
address "G3_CORE_4.190.83.2"
  ip 4.190.83.2/32
exit
address "G3WCSINFOS-*********1-22"
  range *********1 *********2
exit
address "DNS-************"
  ip ************/32
exit
address "DNS-*********"
  ip *********/32
exit
address "GWREDIS-**********/24"
  ip **********/24
exit
address "G3WCSINFOSFTP-***********-42"
  range *********** 4.190.40.42
exit      
address "G3WCSINFOSFTP-F5-***********"
  ip ***********/32
exit
address "G3TOHERAPROXY-*********1-12"
  range *********1 *********2
exit
address "G3GAIA-F5-************"
  ip ************/32
exit
address "G3MONITORGAIA-*************-162"
  range ************* 4.190.122.162
exit
address "G3TOHERAPROXY-F5-***********"
  ip ***********/32
exit
address "G3-MS-K8S-**********/24"
  ip **********/24
exit
address "G3MONITORPORTAL-F5-************"
  ip ************/32
exit
address "G3JCFAUTHCASSERVER-F5-************"
  ip ************/32
exit      
address "OJobServer-**********-162"
  range ********** 18.0.2.162
exit
address "OCS-FTP-**********"
  ip **********/32
exit
address "ConfigCenter-F5-***********"
  ip ***********/32
exit
address "ConfigCenter-************-143"
  range ************ 4.190.80.143
exit
address "SYSLOG-F5-************"
  ip ************/32
exit
address "G3_MS_AIDB_4.190.122.121-123"
  range 4.190.122.121 4.190.122.123
exit
address "G3_Mail_*********/32"
  ip *********/32
exit
address "G3_CORE_NTP_4.190.80.251-252"
  range 4.190.80.251 4.190.80.252
exit      
address "SBSG2BISRVA11-**********"
  ip **********/32
exit
address "SBSG2BISRVA12-**********"
  ip **********/32
exit
address "SBSG2BISRVA13-**********"
  ip **********/32
exit
address "BASDB-***********"
  ip ***********/32
exit
address "BLIDB-************"
  ip ************/32
exit
address "Ansbile-************"
  ip ************/32
exit
address "SBSIRMBMO-**********-32"
  range ********** 4.190.0.32
exit
address "SBSG2IRMDBVIP-**********"
  ip **********/32
exit      
address "IRM-F5-***********6"
  ip ***********6/32
exit
address "SBSG2OPSFTP01-************"
  ip ************/32
exit
address "ItoSchedule-*************-172"
  range ************* 4.190.122.172
exit
address "SBSG2OTJob-***********"
  ip ***********/32
exit
address "CSLC-Hive-*********/32"
  ip *********/32
exit
address "MS-***********/22"
  ip ***********/22
exit
address "CSLC-KYLIN-************"
  ip ************/32
exit
address "CSLC-PHOENIX-************-57"
  range ************ 198.3.100.57
exit      
address "RTQDBVIP-***********"
  ip ***********/32
exit
address "CSLC-SSL4.103.211.0/24"
  ip 4.103.211.0/24
exit
address "SSL-*********"
  ip *********/32
exit
address "G2BASDB-*********"
  ip *********/32
exit
address "************"
  ip ************/32
exit
address "***********/24"
  ip ***********/24
exit
address "**********"
  ip **********/24
exit
address "G3-CORE-SDAS-************-192"
  range ************ 4.190.80.192
exit      
address "**************"
  ip **************/32
exit
address "CSLC-baoleiji-**********/24"
  ip **********/24
exit
address "RMOAS-**********/24"
  ip **********/24
exit
address "FOC-***********-137"
  range *********** 4.176.1.137
exit
address "QiYeWeiXin-***********/16"
  ip ***********/16
exit
address "QiYeWeiXin-*************/32"
  host "*************"
exit
address "SFTP-**********00"
  ip **********00/32
exit
address "W5RRMSFSC01"
  ip 18.2.13.1/32
exit      
address "NAT-************-154"
  ip ************/32
  ip 4.98.130.152/32
  ip 4.98.130.153/32
  ip 4.98.130.154/32
exit
address "W5RRMSFSC01-F5"
  ip 4.190.160.5/32
exit
address "CSLRMSFSP01"
  ip 18.2.7.1/32
exit
address "CSLCOPCC-*********"
  ip *********/24
exit
address "IRM-F5-*********"
  ip *********/32
exit
address "*********"
  ip *********/16
exit
address "W5RBOCC"
  ip *********/24
  ip *********/24
exit
address "Ansbile-************"
  ip ************/32
exit
address "YZBOCC"
  ip 4.128.10.0/24
exit
address "YZBOCC_4.128.10.3"
  ip 4.128.10.3/32
exit
address "G3TSPAPP01-*************"
  ip *************/32
exit
address "G3TSPAPP02-*************"
  ip *************/32
exit
address "***********-14"
  ip ***********/32
  ip 4.103.19.12/32
  ip 4.103.19.13/32
  ip 4.103.19.14/32
exit
address "************-42"
  ip ************/32
  ip 4.103.120.42/32
exit
address "USAP-LB-***********"
  ip ***********/32
exit
address "SDAS-F5-4.190.166.2"
  ip 4.190.166.2/32
exit
address "SDAS-F5-4.190.166.0/24"
  ip 4.190.166.0/24
exit
address "USAP-***********-14"
  range *********** 4.103.19.14
exit
address "K8S-CORE-4.190.84.0/24"
  ip 4.190.84.0/24
exit
address "Sporttery-Mail-111.205.58.68"
  ip 111.205.58.68/32
exit
address "NAT-4.98.130.155-156"
  ip 4.98.130.155/32
  ip 4.98.130.156/32
exit      
address "NAT-4.98.130.199"
  ip 4.98.130.199/32
exit
address "4.35.10.10"
  ip 4.35.10.10/32
exit
address "G3BISMONTORCOLLECT"
  range 4.190.85.1 4.190.85.2
exit
address "VulnerabilityScan-************"
  ip ************/32
exit
address "4.98.1.18-25"
  range 4.98.1.18 4.98.1.25
exit
address "4.35.10.10/32"
  ip 4.35.10.10/32
exit
address "4.14.100.32"
  ip 4.14.100.32/32
exit
address "YJBOCC_9.66.32.0"
  ip 9.66.32.0/24
exit      
zone "mgt"
  vrouter "mgt-vr"
exit
zone "untrust"
  type wan
  ad tear-drop
  ad ip-spoofing
  ad land-attack
  ad ip-option
  ad ip-fragment
  ad ip-directed-broadcast
  ad winnuke
  ad port-scan
  ad syn-flood
  ad icmp-flood
  ad ip-sweep
  ad ping-of-death
  ad udp-flood
exit
zone "l2-untrust" l2
  type wan
exit
zone "twin-mode"
  vrouter "twin-mode-vr"
exit
zone "DMZ"
  vrouter "trust-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "Internet"
  vrouter "trust-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
zone "Inside"
  vrouter "trust-vr"
  ad disable
  ad icmp-flood
  ad udp-flood
  ad syn-flood
  ad syn-flood destination ip-based
  ad ip-sweep
  ad port-scan
  ad ping-of-death
  ad tear-drop
  ad ip-fragment
  ad ip-option
  ad ip-spoofing
  ad ip-directed-broadcast
  ad winnuke
  ad land-attack
exit
hostname "XWHPD-SR2B3C02-FW01"
snmp-server location "fw01-w5r-C-2-10-2"
admin host any any
isakmp proposal "psk-sha256-aes128-g2"
  hash sha256
  encryption aes
exit

isakmp proposal "psk-sha256-aes256-g2"
  hash sha256
  encryption aes-256
exit

isakmp proposal "psk-sha256-3des-g2"
  hash sha256
exit
          
isakmp proposal "psk-md5-aes128-g2"
  hash md5
  encryption aes
exit

isakmp proposal "psk-md5-aes256-g2"
  hash md5
  encryption aes-256
exit

isakmp proposal "psk-md5-3des-g2"
  hash md5
exit

isakmp proposal "rsa-sha256-aes128-g2"
  authentication rsa-sig
  hash sha256
  encryption aes
exit

isakmp proposal "rsa-sha256-aes256-g2"
  authentication rsa-sig
  hash sha256
  encryption aes-256
exit

isakmp proposal "rsa-sha256-3des-g2"
  authentication rsa-sig
  hash sha256
exit

isakmp proposal "rsa-md5-aes128-g2"
  authentication rsa-sig
  hash md5
  encryption aes
exit

isakmp proposal "rsa-md5-aes256-g2"
  authentication rsa-sig
  hash md5
  encryption aes-256
exit

isakmp proposal "rsa-md5-3des-g2"
  authentication rsa-sig
  hash md5
exit
          
isakmp proposal "dsa-sha-aes128-g2"
  authentication dsa-sig
  encryption aes
exit

isakmp proposal "dsa-sha-aes256-g2"
  authentication dsa-sig
  encryption aes-256
exit

isakmp proposal "dsa-sha-3des-g2"
  authentication dsa-sig
exit

ipsec proposal "esp-sha256-aes128-g2"
  hash sha256
  encryption aes
  group 2
exit

ipsec proposal "esp-sha256-aes128-g0"
  hash sha256
  encryption aes
exit      

ipsec proposal "esp-sha256-aes256-g2"
  hash sha256
  encryption aes-256
  group 2
exit

ipsec proposal "esp-sha256-aes256-g0"
  hash sha256
  encryption aes-256
exit

ipsec proposal "esp-sha256-3des-g2"
  hash sha256
  encryption 3des
  group 2
exit

ipsec proposal "esp-sha256-3des-g0"
  hash sha256
  encryption 3des
exit

ipsec proposal "esp-md5-aes128-g2"
  hash md5
  encryption aes
  group 2
exit

ipsec proposal "esp-md5-aes128-g0"
  hash md5
  encryption aes
exit

ipsec proposal "esp-md5-aes256-g2"
  hash md5
  encryption aes-256
  group 2
exit

ipsec proposal "esp-md5-aes256-g0"
  hash md5
  encryption aes-256
exit

ipsec proposal "esp-md5-3des-g2"
  hash md5
  encryption 3des
  group 2
exit

ipsec proposal "esp-md5-3des-g0"
  hash md5
  encryption 3des
exit

interface MGT0 local
  zone  "mgt"
  ip address ********* *************
  manage ssh
  manage ping
  manage snmp
  manage https
exit
interface ethernet0/0
  aggregate aggregate1
exit
interface ethernet0/1
  aggregate aggregate1
exit
interface xethernet4/0
  aggregate aggregate2
  bandwidth downstream 10000000000
  bandwidth upstream 10000000000
  description "TO-XWHPD-NE5DIL-SW01 T1/2/0/1"
exit
interface xethernet4/1
  aggregate aggregate2
  description "TO-XWHPD-NE5DIL-SW01 T1/2/0/2"
exit
interface xethernet4/2
  aggregate aggregate2
  description "TO-XWHPD-NE5DIL-SW01 T1/3/0/1"
exit
interface xethernet4/3
  aggregate aggregate2
  description "TO-XWHPD-NE5DIL-SW01 T1/3/0/2"
exit
interface loopback1
  zone  "untrust"
  ip address ********* ***************
exit
interface loopback2
  zone  "untrust"
  ip address ********* ***************
exit      
interface aggregate1
  description "HA"
exit
interface aggregate2
  zone  "l2-trust"
  bandwidth downstream 40000000000
  bandwidth upstream 40000000000
  description "To-XWHPD-NE5DIL-SW01 Agg5"
exit
interface aggregate2.3033
  zone  "Internet"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  description "To-XWHPD-NE5DIL-SW01 VRF-Internet_Vlan3033"
  manage ping
  reverse-route prefer
exit
interface aggregate2.3034
  zone  "DMZ"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  description "To-XWHPD-NE5DIL-SW01 VRF-DMZ_Vlan3034"
  manage ping
  reverse-route prefer
exit
interface aggregate2.3035
  zone  "Inside"
  ip address ************ ***************
  bandwidth downstream 1000000000
  bandwidth upstream 1000000000
  description "To-XWHPD-NE5DIL-SW01 Vlan-3035"
  manage ssh
  manage ping
  manage http
  manage https
  manage snmp
  reverse-route prefer
exit
ip vrouter "mgt-vr"
  ip route 0.0.0.0/0 ***********
exit
ip vrouter "trust-vr"
  snatrule id 1 from "SBSG2BISRVA12-**********" to "***********/24" service "Any" trans-to eif-ip mode dynamicport log disable 
  router ospf 2000
    router-id *********
    network *********/32 area 0.0.0.0
    network *********/32 area 0.0.0.0
    network ************/30 area 0.0.0.0
    network ************/30 area 0.0.0.0
    network ************/30 area 0.0.0.0
  exit
exit
qos-engine first
  root-pipe "default" id 1
    qos-mode "stat"
  exit
exit
qos-engine second
  disable
  root-pipe "default" id 2
    qos-mode "stat"
  exit
exit
ntp enable
ntp server **********
rule id 45
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3_MS_AIDB_4.190.122.121-123"
  dst-addr "G3_Mail_*********/32"
  service "SMTP"
  service "TCP-25"
exit
rule id 11
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "V3_MS_*************/32"
  dst-addr "Zabbix-Proxy-*********51"
  service "SSH"
  name "V3-11"
exit
rule id 4
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/32"
  src-addr "*********/32"
  dst-addr "Mail_************"
  dst-addr "DNS_***************"
  dst-addr "DNS_*******"
  service "SMTP"
  service "SMTPS"
  service "POP3"
  service "DNS"
  name "V3_DMZ_TO_Internet"
exit
rule id 6
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "DNS-***********"
  dst-addr "DNS-***********"
  service "DNS"
  name "V3-1"
exit
rule id 7
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "*********/16"
  src-addr "4A-*********/24"
  src-addr "BOCC_*********"
  src-addr "BOCC-*********"
  src-addr "BOCC-********/24"
  src-addr "*********"
  src-addr "YZBOCC"
  src-addr "YJBOCC_9.66.32.0"
  dst-addr "*********/16"
  dst-addr "DMZ_NAS_**********/22"
  service "SSH"
  service "HTTP"
  service "HTTPS"
  service "TCP-8443"
exit
rule id 8
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "AlarManager-************-72"
  src-addr "Solarwinds-**********"
  src-addr "OPSTOOL-***********81-182"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "SMTP"
  service "SMTPS"
  name "V3-2"
exit
rule id 9 
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "TCP-***********"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "TCP_31050"
  name "V3-3"
exit
rule id 10
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "Zabbix-Proxy-*********51"
  dst-addr "ZabbixServer"
  dst-addr "************"
  service "TCP-31051"
  service "TCP_31050"
  name "V3-4"
exit
rule id 90
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA11-**********"
  dst-addr "4.14.100.32"
  service "TCP_21050"
  name "juncai_impala"
exit
rule id 12
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "NTP-**********"
  dst-addr "NTp-***********"
  service "HTTP"
  service "NTP"
  name "V3-5"
exit
rule id 13
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "ZabbixServer"
  src-addr "MS-***********/24"
  dst-addr "Zabbix-Proxy-*********51"
  dst-addr "*********/16"
  service "TCP_31050"
  service "TCP-31051"
  service "TCP-31306"
  name "V3-3-6"
exit
rule id 14
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "Solarwinds-*************"
  dst-addr "*********/16"
  service "SNMP"
  service "SYSLOG"
exit
rule id 15
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "Solarwinds-*************"
  service "SNMP"
exit
rule id 16
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/32"
  src-addr "*********/32"
  dst-addr "G2-Mail-**********"
  dst-addr "G2_Mail_***********/32"
  service "SMTPS"
  service "SMTP"
  name "G3-to_G2-Mail"
exit
rule id 2
  action permit
  log session-start
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "OSPF"
  service "ICMP"
exit
rule id 17
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "*************"
  src-addr "************1"
  dst-addr "V3_DMZ_*********/24"
  service "Any"
  name "Jinkong"
exit
rule id 19
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "DMZ_NAS_**********/22"
  dst-addr "NAS_Public_**********/24"
  service "Any"
exit
rule id 20
  action permit
  src-zone "Internet"
  dst-zone "Inside"
  src-addr "G3-Test-***********/32"
  src-addr "G3-Test-*********/24"
  dst-addr "G3-Test-**********/24"
  dst-addr "***********/24"
  service "Any"
  name "G3-Test"
exit
rule id 3
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G2OCS-**********/32"
  dst-addr "*********/16"
  service "TCP_3558"
  service "TCP_3555"
  service "TCP-31306"
  service "SSH"
exit
rule id 18
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "BOCC&4A"
  src-addr "CSLC-baoleiji-*********/24"
  src-addr "CSLC-baoleiji-**********/24"
  src-addr "*********"
  src-addr "YZBOCC"
  dst-addr "G3_*********/16"
  service "TCP_8000"
  service "TCP_3555"
  service "TCP_3558"
  service "TCP-31306"
  service "TCP_8080"
  service "SSH"
  service "HTTP"
  service "HTTPS"
  service "TCP_8888"
  service "TCP-8021-8025"
  service "TCP-9443"
  service "SMTPS"
  service "SMTP"
  name "G3-V1.3-20200429-02"
exit
rule id 21
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "NTp-***********"
  dst-addr "NTP-**********"
  service "NTP"
  service "UDP_123"
exit      
rule id 23
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "**********"
  src-addr "YJBOCC_9.66.32.0"
  dst-addr "SSLF5-***********"
  service "HTTPS"
  name "G3-V131-TAS"
exit
rule id 75
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "DMZ-SSL-*********/24"
  dst-addr "WCS-F5-***********"
  service "TCP-8086"
  name "G3-V131-RMOAS"
exit
rule id 24
  action permit
  log session-start
  log session-end
  src-zone "dmz"
  dst-zone "Inside"
  src-addr "DMZ-SSL-*********/24"
  dst-addr "WCS-F5-***********"
  service "TCP-8086"
  description "TAS-赛果接入"
  name "G3-V131"
exit
rule id 25
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "CSLC-baoleiji-*********/24"
  src-addr "CSLC-baoleiji-**********/24"
  dst-addr "G3_*********/16"
  service "TCP_3389"
  service "TCP_8889"
  service "TCP_8888"
  service "TCP_8013"
  service "TCP-8090"
  service "TCP-31306"
  service "TCP_3558"
  service "TCP_3555"
  service "TCP_8000"
  service "SSH"
  service "HTTP"
  service "HTTPS"
  name "CSLC-baoleiji"
exit
rule id 26
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "V3_DMZ_*********/24"
  dst-addr "***********-6"
  service "TCP-29092"
  name "TO-ELK"
exit
rule id 27
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "*************-212"
  src-addr "MS-K8S-***********/24"
  src-addr "AlarManager-************-72"
  src-addr "*********/16"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "TCP-25"
exit
rule id 29
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "***********/24"
  dst-addr "G2-TAS-SSL-*********"
  service "HTTPS"
exit
rule id 28
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "FOC-***********"
  src-addr "FOC-*********"
  dst-addr "Any"
  service "Any"
  name "anquanlousao"
exit
rule id 30
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3_CORE_4.190.83.1"
  src-addr "G3_CORE_4.190.83.2"
  dst-addr "*********/16"
  service "TCP_8080"
  service "TCP-3191"
  service "TCP-5003"
  service "TCP-7001"
  service "TCP_3558"
  service "TCP_3555"
  service "TCP-6370"
  service "TCP-2379"
  service "HTTP"
  service "SSH"
  name "V3_OPERHOST_4.190"
exit
rule id 31
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Internet"
  src-addr "G3WCSINFOS-*********1-22"
  src-addr "SBSIRMBMO-**********-32"
  dst-addr "Any"
  service "HTTP"
  name "RMX1.1.0_01"
exit
rule id 32
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Internet"
  src-addr "G3WCSINFOS-*********1-22"
  src-addr "G3TOHERAPROXY-*********1-12"
  src-addr "SBSIRMBMO-**********-32"
  src-addr "V3_DMZ_*********/24"
  dst-addr "DNS_***************"
  dst-addr "DNS-************"
  dst-addr "DNS-*********"
  service "DNS"
  service "ICMP"
  service "PING"
  name "RMX1.1.0_02"
exit
rule id 33
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "G3WCSINFOS-*********1-22"
  dst-addr "GWREDIS-**********/24"
  service "TCP-7001"
  name "RMX1.1.0_03"
exit
rule id 34
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "G3WCSINFOS-*********1-22"
  dst-addr "G3WCSINFOSFTP-F5-***********"
  dst-addr "G3WCSINFOSFTP-***********-42"
  service "SSH"
  name "RMX1.1.0_04"
exit
rule id 35
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "G3TOHERAPROXY-*********1-12"
  dst-addr "G3GAIA-F5-************"
  service "TCP-28080"
  name "JCJK1.3.7_01"
exit
rule id 36
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3MONITORGAIA-*************-162"
  src-addr "MS-***********/22"
  dst-addr "G3TOHERAPROXY-F5-***********"
  service "TCP-28083"
  name "JCJK1.3.7_02"
exit
rule id 37
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3MONITORGAIA-*************-162"
  dst-addr "G3-MS-K8S-**********/24"
  service "HTTP"
  name "JCJK1.3.7_03"
exit
rule id 38
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Internet"
  src-addr "G3TOHERAPROXY-*********1-12"
  dst-addr "Any"
  service "HTTPS"
  name "JCJK1.3.7_04"
exit
rule id 39
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "G3TOHERAPROXY-*********1-12"
  dst-addr "G3JCFAUTHCASSERVER-F5-************"
  dst-addr "G3MONITORPORTAL-F5-************"
  service "HTTPS"
  service "TCP-28080"
  name "JCJK1.3.7_05"
exit
rule id 40
  action permit
  log policy-deny
  log session-start
  log session-end
  src-zone "Internet"
  dst-zone "DMZ"
  src-addr "Any"
  dst-addr "G3TOHERAPROXY-F5-***********"
  service "TCP-28080"
  service "TCP-28081"
  service "TCP-28082"
  name "JCJK1.3.7_06"
exit
rule id 41
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "OJobServer-**********-162"
  dst-addr "V3_DMZ_*********/24"
  service "SSH"
  name "OJobServer-TO-DMZ"
exit
rule id 42
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "V3_DMZ_*********/24"
  dst-addr "OCS-FTP-**********"
  service "FTP"
  name "OCS-FTP"
exit
rule id 43
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "V3_DMZ_*********/24"
  dst-addr "ConfigCenter-F5-***********"
  dst-addr "ConfigCenter-************-143"
  service "TCP-28081"
  service "TCP-28070"
  service "HTTP"
  name "TO-ConfigCenter"
exit
rule id 44
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "SYSLOG-F5-************"
  service "SYSLOG"
  name "TO-SYSLOG"
exit
rule id 46
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "G3_CORE_NTP_4.190.80.251-252"
  service "NTP"
exit
rule id 47
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA12-**********"
  src-addr "SBSG2BISRVA11-**********"
  src-addr "SBSG2BISRVA13-**********"
  dst-addr "BASDB-***********"
  dst-addr "BLIDB-************"
  dst-addr "4.35.10.10/32"
  service "TCP_3555"
  service "TCP-31306"
  service "TCP_8080"
  name "SDAS-V2.15.1_01"
exit
rule id 48
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA11-**********"
  src-addr "SBSG2BISRVA12-**********"
  dst-addr "BLIDB-************"
  service "TCP-31306"
  name "SDAS-V2.15.1_02"
exit
rule id 59
  action permit
  log session-start
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA12-**********"
  src-addr "SBSG2BISRVA11-**********"
  src-addr "**********"
  dst-addr "CSLC-KYLIN-************"
  dst-addr "CSLC-PHOENIX-************-57"
  dst-addr "***********/24"
  service "TCP-2181"
  service "TCP-7070"
  service "TCP-16020"
  service "TCP-16010"
  service "TCP-16000"
  name "SDAS-V2.15.1_03"
exit      
rule id 60
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA11-**********"
  src-addr "SBSG2BISRVA12-**********"
  dst-addr "RTQDBVIP-***********"
  service "TCP_3555"
  name "SDAS-V2.15.1_04"
exit
rule id 49
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "Ansbile-************"
  dst-addr "*********/16"
  service "SSH"
exit
rule id 50
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSIRMBMO-**********-32"
  dst-addr "SBSG2IRMDBVIP-**********"
  service "TCP_3555"
  name "IRM1.15.0_01"
exit
rule id 51
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "DMZ-SSL-*********/24"
  dst-addr "IRM-F5-***********6"
  service "TCP-8443"
  name "IRM1.15.0_02"
exit
rule id 52
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "SBSG2OPSFTP01-************"
  service "FTP"
  service "SSH"
  name "IRM1.15.0_03"
exit
rule id 54
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3_CORE_4.190.83.2"
  src-addr "G3_CORE_4.190.83.1"
  dst-addr "G3_*********/16"
  service "UDP-8472"
  service "TCP-5003"
  service "TCP-3191"
  service "TCP_8080"
  service "TCP-7001"
  service "TCP_3555"
  service "TCP_3558"
  service "TCP-2379"
  service "TCP-6370"
  service "HTTP"
  service "SSH"
  name "IRM1.15.0_04"
exit
rule id 55
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "ItoSchedule-*************-172"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "TCP-25"
  name "ItoSchedule"
exit
rule id 58
  action permit
  src-zone "DMZ"
  dst-zone "Internet"
  src-addr "*********/32"
  src-addr "*********/32"
  dst-addr "Mail_************"
  service "SMTP"
  service "TCP-25"
exit
rule id 56
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "SBSG2OTJob-***********"
  dst-addr "*********/16"
  service "SSH"
  name "G3-R160_01"
exit
rule id 57
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA12-**********"
  src-addr "SBSG2BISRVA11-**********"
  dst-addr "CSLC-Hive-*********/32"
  service "TCP-6100-6200"
exit
rule id 61
  action permit
  log session-start
  src-zone "Internet"
  dst-zone "DMZ"
  src-addr "CSLC-SSL4.103.211.0/24"
  dst-addr "SSL-*********"
  service "TCP-8023-8024"
  service "HTTPS"
  service "TCP-8021"
  service "TCP-8022"
  service "TCP-8025"
  service "TCP-9443"
  service "TCP-8443"
  service "PING"
  name "LinShiCeShi-20201208"
exit
rule id 62
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA11-**********"
  src-addr "SBSG2BISRVA12-**********"
  dst-addr "G2BASDB-*********"
  service "TCP_3555"
exit
rule id 63
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3-CORE-SDAS-************-192"
  dst-addr "SBSG2BISRVA11-**********"
  service "TCP-8023-8024"
exit
rule id 64
  action permit
  src-zone "Internet"
  dst-zone "DMZ"
  src-addr "Any"
  dst-addr "SSL-*********"
  service "TCP-8021-8025"
  service "TCP-8443"
  service "TCP-9443"
  service "HTTPS"
  name "SDAS-V2.15.1_05"
exit
rule id 65
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "RMOAS-**********/24"
  dst-addr "SSLF5-***********"
  service "HTTPS"
  name "RMOAS"
exit
rule id 66
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "FOC-***********-137"
  dst-addr "V3_DMZ_*********/24"
  service "Any"
  name "SCAN"
exit      
rule id 68
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "*********/16"
  dst-addr "SFTP-**********00"
  service "SSH"
  name "TO-SFTP"
exit
rule id 69
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "MS-K8S-***********/24"
  dst-addr "G3TOHERAPROXY-F5-***********"
  service "TCP-28083"
  name "G3_JianCe2.0_01"
exit
rule id 71
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "***********/24"
  src-addr "4.98.1.18-25"
  dst-addr "W5RRMSFSC01"
  dst-addr "CSLRMSFSP01"
  service "SSH"
exit
rule id 72
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "NAT-************-154"
  src-addr "NAT-4.98.130.155-156"
  src-addr "NAT-4.98.130.199"
  src-addr "4.98.1.18-25"
  dst-addr "W5RRMSFSC01-F5"
  service "SSH"
exit
rule id 73
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "CSLCOPCC-*********"
  src-addr "BOCC&4A"
  dst-addr "IRM-F5-*********"
  service "TCP-8443"
exit      
rule id 76
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "W5RBOCC"
  src-addr "YZBOCC"
  dst-addr "*********/16"
  service "SSH"
  service "HTTP"
  service "HTTPS"
  service "TCP-8443"
exit
rule id 77
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "Ansbile-************"
  dst-addr "*********/16"
  service "SSH"
exit
rule id 78
  action deny
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "YZBOCC"
  dst-addr "Any"
  service "TCP_3555"
  service "TCP_3558"
  service "SSH"
  service "TCP-31306"
  service "TCP_3389"
exit
rule id 79
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "YZBOCC_4.128.10.3"
  dst-addr "*********/32"
  dst-addr "G3_Mail_*********/32"
  service "TCP-25"
exit
rule id 80
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3TSPAPP01-*************"
  src-addr "G3TSPAPP02-*************"
  dst-addr "G3_*********/16"
  service "SSH"
  service "HTTP"
  service "TCP_8080"
  service "UDP-8472"
exit
rule id 81
  action permit
  src-zone "Internet"
  dst-zone "Inside"
  src-addr "************-42"
  src-addr "***********-14"
  dst-addr "WCS-F5-***********"
  service "TCP-8088"
exit
rule id 82
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SDAS-F5-4.190.166.0/24"
  dst-addr "USAP-LB-***********"
  service "TCP-19080"
exit
rule id 83
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "USAP-***********-14"
  dst-addr "SDAS-F5-4.190.166.2"
  service "TCP-8021"
exit
rule id 84
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "K8S-CORE-4.190.84.0/24"
  dst-addr "*********/32"
  dst-addr "*********/32"
  service "SMTP"
  service "TCP-25"
exit
rule id 85
  action permit
  src-zone "DMZ"
  dst-zone "Internet"
  src-addr "*********/32"
  src-addr "*********/32"
  dst-addr "Sporttery-Mail-111.205.58.68"
  service "SMTP"
  service "TCP-25"
exit
rule id 86
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA11-**********"
  dst-addr "4.35.10.10"
  service "TCP_8080"
  name "SDAS-V22.05.03"
exit
rule id 87
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "G3BISMONTORCOLLECT"
  dst-addr "*********/16"
  service "TCP-31306"
  service "TCP_3558"
  service "TCP_3555"
  service "TCP-3191"
  service "TCP-7001"
  service "TCP-6370"
  service "TCP-10251-10252"
  service "TCP-2379"
  service "TCP_8080"
  service "TCP-4100-4130"
  service "TCP-5000-5030"
  service "TCP-9100"
  service "SSH"
  service "HTTP"
  service "HTTPS"
  service "TCP-7100"
  name "G3BISMONTORCOLLECT_R340"
exit
rule id 88
  action permit
  src-zone "Inside"
  dst-zone "DMZ"
  src-addr "VulnerabilityScan-************"
  dst-addr "*********/16"
  service "Any"
  name "VulnerabilityScan_NetworkDMZZone"
exit
rule id 89
  action permit
  src-zone "Inside"
  dst-zone "Internet"
  src-addr "VulnerabilityScan-************"
  dst-addr "*********/16"
  service "Any"
  name "VulnerabilityScan_NetworkInternetZone"
exit
rule id 91
  action permit
  src-zone "DMZ"
  dst-zone "Inside"
  src-addr "SBSG2BISRVA11-**********"
  src-addr "SBSG2BISRVA12-**********"
  src-addr "SBSG2BISRVA13-**********"
  dst-ip ************/32
  service "TCP-8443"
  service "TCP-6677"
  service "TCP-7788"
  service "TCP-8001"
  service "TCP-8002"
  service "TCP-80"
  name "to_EDRagent"
exit
rule id 1
  action deny
  log session-start
  src-zone "Any"
  dst-zone "Any"
  src-addr "Any"
  dst-addr "Any"
  service "Any"
exit
tcp-seq-check-disable
l2-nonip-action drop
no tcp-mss all
tcp-mss tunnel 1380
snmp-server manager
snmp-server port 161
snmp-server vrouter "mgt-vr"
snmp-server host ************* version 2c community b7QovmJreebXrHEOlL44eXSw3FE/ ro
snmp-server host **********/24 version 2c community YUE6WbDr8Ludce1AzxI9cRlJXP8Q ro
snmp-server host *********** version 2c community NtYcBbvD5P2FNsacPkrl3COOHykQ ro
snmp-server host ************1 version 2c community f9p0KSNOYylJvoKXesbptD0bWLow ro
snmp-server trap-host ************* version 2c community hLMrgcAauKyI9YISO73qyirolEoN port 162
snmp-server trap-host ********** version 2c community QRMcb7fAWLCauh30x/jQvmJVWOwM port 162
snmp-server trap-host *********** version 2c community +0pR8uIgNwB1pgL7haZuG0bofM05 port 162
snmp-server trap-host ************1 version 2c community g3IyM32XKOJuNDAsvyZAaFB+16sc port 162
ecmp-route-select by-src-and-dst
  url-db-query server1 "url1.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server1 enable
  url-db-query server2 "url2.hillstonenet.com" port 8866 vrouter trust-vr
  url-db-query server2 enable
flow
  icmp-unreachable-session-keep
exit
strict-tunnel-check
statistics-set "predef_if_bw"
  target-data bandwidth id 0 record-history
  group-by interface directional vsys
exit
statistics-set "predef_user_bw"
  target-data bandwidth id 1 record-history
  group-by user directional vsys
exit
statistics-set "predef_app_bw"
  target-data bandwidth id 2 record-history
  group-by application vsys
exit
statistics-set "predef_user_app_bw"
  target-data bandwidth id 3
  group-by user directional interface zone application vsys
exit
statistics-set "predef_zone_if_app_bw"
  target-data bandwidth id 4
  group-by interface zone directional application vsys
exit
query-groups
  dashboard-query-group "hillstone-**********248-dashboard-query-group" user "hillstone"
    rule "license" create-time ********** id 1 query-string "%7B%22time%22%3A1697671073772%2C%22ignore%22%3Atrue%7D"
  exit
exit
no longlife-sess-percent
no sms disable
lan-addr private_network
ha link interface aggregate1
ha link ip ************* ***************
ha group 0
  monitor track "HA"
exit
ha cluster 1

End

HRP_M<XW-B1B02-ALI-C-FW1>dis cur
2024-01-17 09:47:01.626 +08:00
!Software Version V600R022C00SPC100
!Last configuration was updated at 2024-01-11 11:34:29+08:00 by netadmin
!Last configuration was saved at 2024-01-17 04:15:01+08:00
!kms_feature --
!said VRPV800R006C00B016D0127-0.0.1
#
pki realm default
#
clock timezone Beijing add 08:00:00
#
sysname XW-B1B02-ALI-C-FW1
#
undo ftp server source all-interface
undo ftp ipv6 server source all-interface
#
firewall defend action alert
#
firewall detect ftp
firewall detect sqlnet
firewall detect dns
#
log type policy enable
#
log type traffic enable
#
log type traffic-report enable
#
undo update schedule ips-sdb enable
update schedule ips-sdb daily 00:00
undo update schedule av-sdb enable
update schedule av-sdb daily 00:00
undo update schedule sa-sdb enable
update schedule sa-sdb daily 00:00
#
firewall log session half-connection enable
firewall log encrypt-algorithm aes256-gcm
#
hrp enable
undo hrp preempt enable
hrp device active
hrp standby config enable
hrp auto-sync config static-route
hrp auto-sync config policy-based-route
hrp mirror config enable
hrp mirror session enable
hrp interface Eth-Trunk63 remote *******
hrp track interface Eth-Trunk11
hrp authentication-key %+%##!!!!!!!!!"!!!!"!!!!*!!!!1O'{A*P2NCZl9vX/dyOI`*00*}5Vt%an_5Q!!!!!2jp5!!!!!!<!!!!"8(#XH_X(B7EFwQD8NqFi1pi+>DW26A3=OU!!!!!%+%#
undo hrp track trunk-member enable
#
firewall feature-bypass bandwidth-policy enable level 2 priority 1
firewall feature-bypass ddos enable level 2 priority 2
#
ntp server disable
ntp ipv6 server disable
ntp server source-interface all disable
ntp ipv6 server source-interface all disable
ntp unicast-server ******* vpn-instance _management_vpn_ source-interface MEth0/0/0
#
cpu-defend policy defendAll
 car packet-type default pps 10000
 car all-packets pps 100000
#
vlan batch 1 11 to 13
#
arp learning on-different-segment disable
#
configuration file auto-save interval 600 delay 6 cpu-limit 60
#
telnet server disable
telnet ipv6 server disable
undo telnet server-source all-interface
undo telnet ipv6 server-source all-interface
#
cpu-defend-policy defendAll
#
diffserv domain default
#
ip vpn-instance _management_vpn_
 ipv4-family
#
vlan 1
#
time-range worktime 08:00 to 18:00 working-day
#
aaa
 authentication-scheme default
  authentication-mode local
 authorization-scheme default
  authorization-mode local
 accounting-scheme default
  accounting-mode none
 local-aaa-user password policy administrator
  password history record number 5
  password min-length 9
  password alert before-expire 45
  password expire 180
  password complexity three-of-kinds
  password similar-to-name-check disable
 domain default
  authentication-scheme default
  accounting-scheme default
 local-aaa-user wrong-password retry-interval 5 retry-time 10 block-time 5
 local-aaa-user user-name complexity-check disable
 local-user netadmin password irreversible-cipher $1d$e$z//#~^{;[~CK9t$0L&^4}U.p<x!qQCk:{IP.*"n~N<S:7S*s`I]lXb'$
 local-user netadmin password-force-change disable
 local-user netadmin privilege level 3
 local-user netadmin service-type terminal ssh http
 local-user netadmin user-group manage-ug
#
interface MEth0/0/0
 ip binding vpn-instance _management_vpn_
 ip address ************* *************
#
interface Eth-Trunk11
 mode lacp-static
#
interface Eth-Trunk11.11
 ip address *********** ***************
 dot1q termination vid 11
 service-manage ping permit
#
interface Eth-Trunk11.12
 ip address *********** ***************
 dot1q termination vid 12
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
#
interface Eth-Trunk11.13
 ip address *********** ***************
 dot1q termination vid 13
 service-manage ping permit
#
interface Eth-Trunk63
 description HRP
 ip address ******* ***************
 service-manage ping permit
 service-manage ssh permit
#
interface GE0/0/0
 eth-trunk 63
 combo enable fiber
 device transceiver 1000BASE-X
#
interface GE0/0/1
 eth-trunk 63
 combo enable fiber
 device transceiver 1000BASE-X
#
interface GE0/0/2
#
interface GE0/0/3
#
interface GE0/0/4
#
interface GE0/0/5
#
interface GE0/0/6
#
interface GE0/0/7
#               
interface GE0/0/8
#
interface GE0/0/9
#
interface GE0/0/10
#
interface GE0/0/11
#
interface 10GE0/0/0
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/1
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/2
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/3
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/4
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/5
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/6
#
interface 10GE0/0/7
#
interface 10GE0/0/8
#
interface 10GE0/0/9
#
interface Virtual-if0
#
interface NULL0
#
ip route-static 0.0.0.0 0.0.0.0 Eth-Trunk11.12 *********** description to_untrust
ip route-static ********* *********** Eth-Trunk11.11 *********** description to_Aliyun_underlay
ip route-static ********** ************* Eth-Trunk11.13 *********** description to_dmz_VPC
ip route-static *********** ************* Eth-Trunk11.11 *********** description to_PublicService_VPC
ip route-static ********* *********** Eth-Trunk11.11 *********** description to_YeWuZhongTai_VPC
ip route-static ********* ************* Eth-Trunk11.11 *********** description to_LeTouVPC_VPC
ip route-static ********** ************* Eth-Trunk11.11 *********** description to_ChuanZu_VPC
ip route-static ********** ************* Eth-Trunk11.11 *********** description to_JiKai_VPC
ip route-static ********** ************* Eth-Trunk11.11 *********** description to_JingCai_VPC
ip route-static vpn-instance _management_vpn_ 0.0.0.0 0.0.0.0 *************
#
snmp-agent
snmp-agent local-engineid 800007DB03C4D4389B7BCF
snmp-agent community read cipher %@%##!!!!!!!!!"!!!!"!!!!*!!!!1O'{A*P2NCu*(XY6D;`ARES^X:#/R@xiIFX!!!!!2jp5!!!!!!U!!!!h@LP#(zAH~zVwjJ86,@U!NJy3#yoYPz1;pW*,D81C5#d#Ye$]1w<&$Bo#EECdDwQ&!!!!!!!!!!!!!!!%@%# alias __CommunityAliasName_02_38420
snmp-agent community write cipher %@%##!!!!!!!!!"!!!!"!!!!*!!!!1O'{A*P2NCH#"r=55b^J]A8RRO(0<7^z!+.!!!!!2jp5!!!!!!U!!!!>=ZP$c#yoIxv;4R.|#NBAAsl!}*Nd9if"u9;<.t*x(u#T"/j8+j&Zx3!w{kU-w]_>!!!!!!!!!!!!!!!%@%# alias __CommunityAliasName_02_56853
#
snmp-agent sys-info version v2c v3
#
snmp-agent protocol source-interface MEth0/0/0
undo snmp-agent protocol source-status all-interface
undo snmp-agent protocol source-status ipv6 all-interface
#
undo snmp-agent proxy protocol source-status all-interface
undo snmp-agent proxy protocol source-status ipv6 all-interface
#
snmp-agent trap enable
#               
lldp enable
#
stelnet server enable
sftp server enable
ssh server-source -i Eth-Trunk63
ssh server-source -i MEth0/0/0
ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh authorization-type default aaa
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256 ecdh_sha2_nistp256 dh_group16_sha512 curve25519_sha256
#
ssh server publickey dsa ecc rsa x509v3-ssh-rsa rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 2048
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha256
#
header shell information "
****************************************************************
*                     Copyright (C) 2012-2022                  *
*                 Huawei Technologies Co., Ltd.                *
*                       All rights reserved.                   *
*           Without the owner's prior written consent,         *
*    no decompiling or reverse-engineering shall be allowed.   *
****************************************************************
"
#
timestamp enable
#
user-interface con 0
 authentication-mode aaa
#
user-interface vty 0 20
 authentication-mode aaa
 protocol inbound ssh
#
ike proposal default
 encryption-algorithm aes-gcm-256 aes-gcm-192 aes-gcm-128 
 dh group14     
 authentication-algorithm sha2-512 sha2-384 sha2-256 
 authentication-method pre-share
 integrity-algorithm hmac-sha2-256 
 prf hmac-sha2-256 
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk11.11
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk11.12
#
firewall zone dmz
 set priority 50
 add interface Eth-Trunk11.13
#
firewall zone name HA id 4
 add interface Eth-Trunk63
#               
sa
#
profile type url-filter name redis.rds.ops.aliyun.tc
 add whitelist host *.redis.rds.ops.aliyun.tc
#
slb
#
ip service-set tcp_10006 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10006
#
ip service-set tcp_10080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10080
#
ip service-set tcp_18080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 18080
#
ip service-set tcp_19080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 19080
#
ip service-set tcp_28080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 28080
#
ip service-set tcp_30000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30000
#
ip service-set tcp_30001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30001
#
ip service-set tcp_30011 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30011
#
ip service-set tcp_30033 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30033
#
ip service-set tcp_30040 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30040
#
ip service-set tcp_30200 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30200
#
ip service-set tcp_30201 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30201
#
ip service-set tcp_30202 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30202
#               
ip service-set tcp_30203 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30203
#
ip service-set tcp_30204 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30204
#
ip service-set tcp_30897 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30897
#
ip service-set tcp_31000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31000
#
ip service-set tcp_32020 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 32020
#
ip service-set tcp_32201 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 32201
#
ip service-set tcp_32202 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 32202
#
ip service-set tcp_389 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 389
#
ip service-set tcp_5900 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 5900
#
ip service-set tcp_60021 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 60021
#
ip service-set tcp_60022 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 60022
#
ip service-set tcp_63306 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 63306
#
ip service-set tcp_63389 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 63389
#
ip service-set tcp_636 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 636
#
ip service-set tcp_6379 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6379
#
ip service-set tcp_80 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 80
#
ip service-set tcp_8080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set tcp_8081 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8081
#
ip service-set tcp_8082 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8082
#
ip service-set tcp_8083 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8083
#
ip service-set tcp_8085 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8085
#
ip service-set tcp_8087 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8087
#
ip service-set tcp_8330 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8330
#               
ip service-set tcp_8989 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8989
#
ip service-set tcp_9897 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9897
#
nat static-mapping
#
security-policy
 default policy logging
 default session logging
 default packet-filter intrazone enable
 rule name HA_local_check
  source-zone HA
  source-zone local
  destination-zone HA
  destination-zone local
  service icmp
  service protocol udp destination-port 18514
  action permit
 rule name permit_icmp
  service icmp
  action permit 
 rule name permit_ntp
  source-zone dmz
  source-zone local
  source-zone trust
  destination-zone untrust
  destination-address ******* mask ***************
  service ntp
  action permit
 rule name permit_dns
  source-zone dmz
  source-zone local
  source-zone trust
  destination-zone untrust
  destination-address ********** mask ***************
  service dns
  service dns-tcp
  session logging
  action permit
 rule name permit_gtmToDns
  source-zone untrust
  destination-zone trust
  destination-address range *********** ***********
  service dns   
  service dns-tcp
  session logging
  action permit
 rule name 20231130_ocsToAliyunAso
  description 申请人：高大会
  source-zone untrust
  destination-zone trust
  source-address range *********** ************
  destination-address *********** mask ***************
  service https
  action permit
 rule name 20231130_ocsToAliyunAscm
  description 申请人：高大会
  source-zone untrust
  destination-zone trust
  source-address range *********** ************
  destination-address *********** mask ***************
  service https
  action permit
 rule name 20231130_ocsToAliyunTianji
  description 申请人：高大会
  source-zone untrust
  destination-zone trust
  source-address range *********** ************
  destination-address *********** mask ***************
  destination-address ************ mask ***************
  service https
  action permit
 rule name 20231130_ocsToAliyunEdas
  description 申请人：高大会
  source-zone untrust
  destination-zone trust
  source-address range *********** ************
  destination-address ************* mask ***************
  service https
  action permit
 rule name 20231130_ocsToAliyunManage
  description 申请人：高大会
  source-zone untrust
  destination-zone trust
  source-address range *********** ************
  destination-address ********* mask ***********
  service ftp
  service rdp-tcp
  service ssh
  action permit 
 rule name 20231130_EccToBaoLeiJi
  description 申请人：高大会
  source-zone untrust
  destination-zone trust
  source-address ******** mask *************
  source-address ********* mask *************
  source-address ********* mask *************
  source-address ********* mask *************
  destination-address range ************ ************
  service https
  service tcp_5900
  service tcp_60021
  service tcp_60022
  service tcp_63306
  service tcp_63389
  session logging
  action permit
 rule name 20231130_scannerToAliyun
  description 申请人：高大会
  source-zone untrust
  destination-zone dmz
  destination-zone local
  destination-zone trust
  source-address ************ mask ***************
  action permit
 rule name 20231130_aliyunToSOC
  description 申请人：高大会 张润苗
  source-zone dmz
  source-zone local
  source-zone trust
  destination-zone trust
  destination-address *********** mask *************
  service syslog
  action permit
 rule name 20231130_baoLeiJiToYuKong
  description 申请人：高大会
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  destination-address range ******** ********
  service tcp_389
  service tcp_636
  action permit
 rule name 20231130_baoLeiJiToShuangYinSu
  description 申请人：高大会
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  destination-address range ********** **********
  service http
  service radius
  traffic logging enable
  action permit
 rule name 20231130_AOPSToAliyun
  description 申请人：孙凯
  source-zone untrust
  destination-zone trust
  source-address range ************ ************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  service http
  service https
  action permit
 rule name 20231130_monitorToAliyun
  description 申请人：张永帅
  source-zone untrust
  destination-zone trust
  source-address *********** mask *************
  source-address range ************ ************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  service http
  service https
  action permit
 rule name 20231130_monitorToKafka
  description 申请人：张永帅
  source-zone untrust
  destination-zone trust
  source-address *********** mask *************
  destination-address range ************* *************
  service tcp_8080
  action permit
 rule name 20231130_monitorToRedis
  description 申请人：张永帅
  source-zone untrust
  destination-zone trust
  source-address *********** mask *************
  service tcp_6379
  profile url-filter redis.rds.ops.aliyun.tc
  action permit
 rule name 20231130_monitorToKFPT
  description 申请人：张永帅
  source-zone untrust
  destination-zone trust
  source-address *********** mask *************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service http
  action permit
 rule name 20231130_aliyunToMonitor
  description 申请人：张永帅
  source-zone trust
  destination-zone untrust
  destination-address *********** mask *************
  service http
  service tcp_30033
  service tcp_30040
  service tcp_30897
  service tcp_9897
  action permit
 rule name 20231207_nginxToKFTP
  description 申请人：韩志成
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  source-address range *********** ***********
  source-address range *********** ***********
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service http
  action permit
 rule name 20231207_usapToKFTP
  description 申请人：韩志成
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  source-address range *********** ***********
  destination-address 10.90.4.100 mask ***************
  destination-address 10.90.4.109 mask ***************
  service http
  action permit
 rule name 20231207_KFTPToUSAP
  description 申请人：韩志成
  source-zone trust
  destination-zone untrust
  source-address 10.90.4.0 mask *************
  destination-address 4.103.19.10 mask ***************
  service tcp_19080
  action permit
 rule name 20231207_KFTPToSec
  description 申请人：韩志成
  source-zone trust
  destination-zone untrust
  source-address 10.90.4.0 mask *************
  destination-address 3.15.0.30 mask ***************
  service tcp_10006
  action permit
 rule name 20231207_KFTPToDmp
  description 申请人：韩志成
  source-zone trust
  destination-zone untrust
  source-address 10.90.4.0 mask *************
  destination-address ********** mask ***************
  service tcp_8080
  action permit
 rule name 20231225_KFPTtoFUWU
  source-zone trust
  destination-zone untrust
  source-address 10.90.4.0 mask *************
  destination-address 10.194.119.2 mask ***************
  destination-address 10.194.120.51 mask ***************
  destination-address 10.194.120.57 mask ***************
  destination-address ************* mask ***************
  destination-address ************* mask ***************
  destination-address ************* mask ***************
  destination-address ************* mask ***************
  destination-address ************* mask ***************
  destination-address ************ mask ***************
  destination-address ********** mask ***************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  destination-address ************ mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  destination-address ******** mask ***************
  destination-address ********** mask ***************
  destination-address *********** mask ***************
  destination-address ********** mask ***************
  destination-address *********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  destination-address *********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  destination-address *********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  service tcp_10080
  service tcp_18080
  service tcp_28080
  service tcp_30000
  service tcp_30001
  service tcp_30011
  service tcp_30200
  service tcp_30201
  service tcp_30202
  service tcp_30203
  service tcp_30204
  service tcp_31000
  service tcp_32020
  service tcp_32201
  service tcp_32202
  service tcp_80
  service tcp_8080
  service tcp_8081
  service tcp_8082
  service tcp_8083
  service tcp_8085
  service tcp_8087
  service tcp_8330
  service tcp_8989
  policy logging
  session logging
  action permit
 rule name 20231130_monitorToBaoLeiJi
  description 申请人：高大会
  source-zone untrust
  destination-zone trust
  source-address *********** mask *************
  destination-address range ************ ************
  service https
  action permit
#               
bandwidth-policy
#
policy-based-route
#
nat-policy
#
dns-transparent-policy
 mode based-on-multi-interface
#
multi-interface
#
web-manager enable port 8443
web-manager http forward enable
web-manager server-source all-interface
undo web-manager captcha enable
#
warranty
#
return
HRP_M<XW-B1B02-ALI-C-FW1>                   dis version
2024-01-17 09:47:11.978 +08:00
Huawei YunShan OS
Version ******** (USG6600F V600R022C00SPC100)
Copyright (C) 2021-2022 Huawei Technologies Co., Ltd.
HUAWEI USG6635F uptime is 33 days, 23 hours, 27 minutes
Patch Version: V600R022SPH180


MPU(Master) 0 : uptime is  33 days, 23 hours, 26 minutes
        StartupTime 2023/12/14   10:20:17
Memory      Size    : 32768 M bytes
Flash       Size    : 4096 M bytes
MPU version information:
1.PCB       Version : SGB5MPUA VER B
2.MAB       Version : 1
3.Board     Type    : MPUA-USG6635F
4.BIOS      Version : 791
5.CPLD1     Version : 275
  CPLD2     Version : 275

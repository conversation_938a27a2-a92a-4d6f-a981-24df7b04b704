HRP_M<XWLCFW01-B1F05>dis cu 
2024-02-28 18:49:00.342 +08:00
!Software Version V500R005C00SPC200
#
sysname XWLCFW01-B1F05
#
 l2tp domain suffix-separator @
#
info-center source default channel 2 trap level informational
info-center loghost source GigabitEthernet0/0/0
info-center loghost *********** vpn-instance mgt_vrf
info-center loghost ***********
info-center loghost ************ vpn-instance mgt_vrf facility local6
info-center loghost ************ vpn-instance mgt_vrf
info-center loghost ************ vpn-instance mgt_vrf
info-center loghost ************* vpn-instance mgt_vrf
info-center logbuffer size 1024
#
authentication-profile name portal_authen_default
#
 ipsec sha2 compatible enable 
#
 undo factory-configuration prohibit
#
undo telnet server enable
undo telnet ipv6 server enable
#
clock timezone Beijing add 08:00:00
#
 hrp enable
 hrp mirror config enable
 hrp interface Eth-Trunk0 remote *******
 hrp mirror session enable
 undo hrp preempt
 undo hrp track trunk-member enable
 hrp track interface Eth-Trunk1
#
 update schedule location-sdb weekly Sun 23:34
#
 firewall defend action discard
#
 log type traffic enable
 log type syslog enable
 log type policy enable
#
 undo dataflow enable
#
 undo sa force-detection enable
#
 lldp enable
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
 banner enable
#
 user-manage web-authentication security port 8887
page-setting
 user-manage security version tlsv1.1 tlsv1.2
password-policy
 level high
user-manage single-sign-on ad
user-manage single-sign-on tsm
user-manage single-sign-on radius
user-manage auto-sync online-user
#
 firewall ids authentication type aes256
#
 web-manager security version tlsv1.1 tlsv1.2
 web-manager enable
 web-manager security enable
 undo web-manager config-guide enable
#
firewall dataplane to manageplane application-apperceive default-action drop
#
 update schedule ips-sdb daily 23:11
 update schedule av-sdb daily 23:11
 update schedule sa-sdb daily 23:11
 update schedule ip-reputation daily 23:11
 update schedule cnc daily 23:11
 update schedule file-reputation daily 23:11
#
 set disk-scan parameter attach on
 set disk-scan parameter cycle 15
 set disk-scan parameter iostat 80
 set disk-scan parameter speed 10
 set disk-scan parameter switch on
 set disk-scan parameter parallel 50
 disk-usage alarm threshold 95 
#
vsys enable 
resource-class r0
resource-class core
resource-class normal
resource-class azzd
resource-class stqd
resource-class sgw
resource-class others
resource-class sjzt
resource-class yzd
resource-class yhlx
resource-class tyxxfb
resource-class kfpt
resource-class cslp
resource-class usap
resource-class wsjc
resource-class qkl
resource-class csljc
resource-class ticaiapp
#
#
vsys name Core 1
 assign interface Eth-Trunk1.301
 assign interface Eth-Trunk1.302
 assign interface LoopBack0
 assign interface Eth-Trunk1.399
 assign resource-class core
#
vsys name Normal 2
 assign interface Eth-Trunk1.303
 assign interface Eth-Trunk1.304
 assign resource-class normal
#
vsys name Others 3
 assign interface Eth-Trunk1.305
 assign interface Eth-Trunk1.306
 assign resource-class others
#
vsys name AZZD 4
 assign interface Eth-Trunk1.307
 assign interface Eth-Trunk1.308
 assign resource-class azzd
#
vsys name SGW 5
 assign interface Eth-Trunk1.397
 assign interface Eth-Trunk1.398
 assign resource-class sgw
#
vsys name STQD 6
 assign interface Eth-Trunk1.309
 assign interface Eth-Trunk1.310
 assign resource-class stqd
#
vsys name SJZT 7
 assign interface Eth-Trunk1.319
 assign interface Eth-Trunk1.320
 assign resource-class sjzt
#
vsys name YZD 8
 assign interface Eth-Trunk1.313
 assign interface Eth-Trunk1.314
 assign resource-class yzd
#
vsys name YHLX 9
 assign interface Eth-Trunk1.315
 assign interface Eth-Trunk1.316
 assign resource-class yhlx
#
vsys name TYXXFB 10
 assign interface Eth-Trunk1.317
 assign interface Eth-Trunk1.318
 assign resource-class tyxxfb
#
vsys name KFPT 11
 assign interface Eth-Trunk1.311
 assign interface Eth-Trunk1.312
 assign resource-class kfpt
#
vsys name CSLP 12
 assign interface Eth-Trunk1.321
 assign interface Eth-Trunk1.322
 assign resource-class cslp
#
vsys name USAP 13
 assign interface Eth-Trunk1.323
 assign interface Eth-Trunk1.324
 assign resource-class usap
#
vsys name WSJC 14
 assign interface Eth-Trunk1.325
 assign interface Eth-Trunk1.326
 assign resource-class wsjc
#
vsys name QKL 15
 assign interface Eth-Trunk1.327
 assign interface Eth-Trunk1.328
 assign resource-class qkl
#
vsys name CSLJC 16
 assign interface Eth-Trunk1.329
 assign interface Eth-Trunk1.330
 assign resource-class csljc
#
vsys name TICAIAPP 17
 assign interface Eth-Trunk1.331
 assign interface Eth-Trunk1.332
 assign resource-class ticaiapp
#
vsys name YJVPN 18
 assign interface Eth-Trunk1.401
 assign interface Eth-Trunk1.402
#
ip vpn-instance AZZD
 ipv4-family
#
ip vpn-instance CSLJC
 ipv4-family
#
ip vpn-instance CSLP
 ipv4-family
#
ip vpn-instance Core
 ipv4-family
#
ip vpn-instance KFPT
 ipv4-family
#
ip vpn-instance Normal
 ipv4-family
#
ip vpn-instance Others
 ipv4-family
#
ip vpn-instance QKL
 ipv4-family
#
ip vpn-instance SGW
 ipv4-family
#
ip vpn-instance SJZT
 ipv4-family
#
ip vpn-instance STQD
 ipv4-family
#
ip vpn-instance TICAIAPP
 ipv4-family
#
ip vpn-instance TYXXFB
 ipv4-family
#
ip vpn-instance USAP
 ipv4-family
#
ip vpn-instance VPN
 ipv4-family
#
ip vpn-instance WSJC
 ipv4-family
#
ip vpn-instance YHLX
 ipv4-family
#
ip vpn-instance YJVPN
 ipv4-family
#
ip vpn-instance YZD
 ipv4-family
#
ip vpn-instance default
 ipv4-family
#
ip vpn-instance mgt_vrf
 ipv4-family
#
radius-server template radius_server
 radius-server shared-key cipher %^%#&5c),tq^21&q=v4Ld&aXhk/>,A}4#!r:uo~#!&]D%^%#
 radius-server authentication ********** 1812 vpn-instance mgt_vrf source ip-address ************* weight 80
 radius-server group-filter class
#
hwtacacs-server template cslc
 hwtacacs-server authentication ********** vpn-instance mgt_vrf
 hwtacacs-server authentication ********** vpn-instance mgt_vrf secondary
 hwtacacs-server authorization ********** vpn-instance mgt_vrf
 hwtacacs-server authorization ********** vpn-instance mgt_vrf secondary
 hwtacacs-server accounting ********** vpn-instance mgt_vrf
 hwtacacs-server accounting ********** vpn-instance mgt_vrf secondary
 hwtacacs-server shared-key cipher %^%#eC^#4JFaVAS[|[)#)DJ&qKl7I|S|n#Xt4<TAsD)Z[(iN;PI3A4_(;~Ix|T46%^%#
 undo hwtacacs-server user-name domain-included
#
ip address-set ntp_******* type object
 address 0 ******* mask 32
#
ip address-set Jiankong type object
 address 0 *********** mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
ike proposal default
 encryption-algorithm aes-256 aes-192 aes-128 
 dh group14 
 authentication-algorithm sha2-512 sha2-384 sha2-256 
 authentication-method pre-share
 integrity-algorithm hmac-sha2-256 
 prf hmac-sha2-256 
#
web-auth-server default
 port 50100
#
portal-access-profile name default
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authentication-scheme cslc
  authentication-mode hwtacacs local
 authentication-scheme radius
  authentication-mode radius local
 authorization-scheme default
 authorization-scheme cslc
  authorization-mode hwtacacs local
  authorization-cmd 3 hwtacacs local
 accounting-scheme default
 accounting-scheme cslc
  accounting-mode hwtacacs
 domain default
  authentication-scheme cslc
  accounting-scheme cslc
  authorization-scheme cslc
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain default_admin
  authentication-scheme cslc
  accounting-scheme cslc
  authorization-scheme cslc
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain cslc
  authentication-scheme cslc
  accounting-scheme cslc
  authorization-scheme cslc
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain ztc
  authentication-scheme radius
  radius-server radius_server
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 manager-user audit-admin 
  password cipher @%@%s@_vO4)o,V1$vk9G+lj%K*,iM@Y40AkEk"N|DQ38P<MM*,lK@%@%
  service-type web terminal 
  level 15 

 manager-user api-admin 
  password cipher @%@%ZO%oS^~]F*uG@G2p"A-#)6AhJ@@l;M3r^=JR{;*an9"$6Ak)@%@%
  service-type api 
  level 15 

 manager-user admin 
  password cipher @%@%&vlhOJ)}VTasD<@7jDXJo<E];=+wG:^N`4=XxLURJDu-<E`o@%@%
  service-type web terminal ssh 
  level 15 
  authentication-scheme admin_local 

 manager-user netadmin 
  password cipher @%@%U}O:Vf=KK<R-<M~k_~%TNpi8[+PN3/EBr>7Eop)qdKW/pi;N@%@%
  service-type web terminal ssh 
  level 15 
  authentication-scheme admin_local 

 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
 bind manager-user audit-admin role audit-admin
 bind manager-user admin role system-admin
 bind manager-user netadmin role system-admin
#
ntp-service server disable
ntp-service ipv6 server disable
ntp-service unicast-server ******* vpn-instance mgt_vrf source-interface GigabitEthernet0/0/0
#
interface Eth-Trunk0
 description TO XinTiao
 ip address ******* ***************
#
interface Eth-Trunk1
 description TO XWLCSW01-B1F05-Eth-Trunk1-2
 mode lacp-static
#
interface Eth-Trunk1.301
 vlan-type dot1q 301
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
interface Eth-Trunk1.302
 vlan-type dot1q 302
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
interface Eth-Trunk1.303
 vlan-type dot1q 303
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.304
 vlan-type dot1q 304
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.305
 vlan-type dot1q 305
 ip binding vpn-instance Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.306
 vlan-type dot1q 306
 ip binding vpn-instance Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.307
 vlan-type dot1q 307
 ip binding vpn-instance AZZD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.308
 vlan-type dot1q 308
 ip binding vpn-instance AZZD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.309
 vlan-type dot1q 309
 ip binding vpn-instance STQD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.310
 vlan-type dot1q 310
 ip binding vpn-instance STQD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.311
 vlan-type dot1q 311
 ip binding vpn-instance KFPT
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.312
 vlan-type dot1q 312
 ip binding vpn-instance KFPT
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.313
 vlan-type dot1q 313
 ip binding vpn-instance YZD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.314
 vlan-type dot1q 314
 ip binding vpn-instance YZD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.315
 vlan-type dot1q 315
 ip binding vpn-instance YHLX
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.316
 vlan-type dot1q 316
 ip binding vpn-instance YHLX
 ip address *********2 ***************
 service-manage ping permit
#
interface Eth-Trunk1.317
 vlan-type dot1q 317
 ip binding vpn-instance TYXXFB
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.318
 vlan-type dot1q 318
 ip binding vpn-instance TYXXFB
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.319
 vlan-type dot1q 319
 ip binding vpn-instance SJZT
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.320
 vlan-type dot1q 320
 ip binding vpn-instance SJZT
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.321
 vlan-type dot1q 321
 ip binding vpn-instance CSLP
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.322
 vlan-type dot1q 322
 ip binding vpn-instance CSLP
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.323
 vlan-type dot1q 323
 ip binding vpn-instance USAP
 ip address *********0 ***************
 service-manage ping permit
#
interface Eth-Trunk1.324
 vlan-type dot1q 324
 ip binding vpn-instance USAP
 ip address *********4 ***************
 service-manage ping permit
#
interface Eth-Trunk1.325
 vlan-type dot1q 325
 ip binding vpn-instance WSJC
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.326
 vlan-type dot1q 326
 ip binding vpn-instance WSJC
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.327
 vlan-type dot1q 327
 ip binding vpn-instance QKL
 ip address **********6 ***************
 service-manage ping permit
#
interface Eth-Trunk1.328
 vlan-type dot1q 328
 ip binding vpn-instance QKL
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.329
 vlan-type dot1q 329
 ip binding vpn-instance CSLJC
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.330
 vlan-type dot1q 330
 ip binding vpn-instance CSLJC
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.331
 vlan-type dot1q 331
 ip binding vpn-instance TICAIAPP
 ip address *********22 ***************
 service-manage ping permit
#
interface Eth-Trunk1.332
 vlan-type dot1q 332
 ip binding vpn-instance TICAIAPP
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.397
 vlan-type dot1q 397
 ip binding vpn-instance SGW
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.398
 vlan-type dot1q 398
 ip binding vpn-instance SGW
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.399
 vlan-type dot1q 399
 ip binding vpn-instance Core
 ip address ************* *************
 alias Eth-Trunk1.399
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
#
interface Eth-Trunk1.401
 vlan-type dot1q 401
 ip binding vpn-instance YJVPN
 ip address *********14 ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
interface Eth-Trunk1.402
 vlan-type dot1q 402
 ip binding vpn-instance YJVPN
 ip address **********2 ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
l2tp-group default-lns
#
interface GigabitEthernet0/0/0
 undo shutdown
 ip binding vpn-instance mgt_vrf
 ip address ************* *************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage snmp permit
 service-manage telnet permit
 service-manage netconf permit
#
interface GigabitEthernet1/0/0
 undo shutdown
#
interface GigabitEthernet1/0/1
 undo shutdown
#
interface GigabitEthernet1/0/2
 undo shutdown
#
interface GigabitEthernet1/0/3
 undo shutdown
#
interface GigabitEthernet1/0/4
 undo shutdown
#
interface GigabitEthernet1/0/5
 undo shutdown
#
interface GigabitEthernet1/0/6
 undo shutdown
#
interface GigabitEthernet1/0/7
 undo shutdown
#
interface GigabitEthernet1/0/8
 undo shutdown
 eth-trunk 0
#
interface GigabitEthernet1/0/9
 undo shutdown
 eth-trunk 1
#
interface GigabitEthernet2/0/0
 undo shutdown
#
interface GigabitEthernet2/0/1
 undo shutdown
#
interface GigabitEthernet2/0/2
 undo shutdown
#
interface GigabitEthernet2/0/3
 undo shutdown
#
interface GigabitEthernet2/0/4
 undo shutdown
#
interface GigabitEthernet2/0/5
 undo shutdown
#
interface GigabitEthernet2/0/6
 undo shutdown
#
interface GigabitEthernet2/0/7
 undo shutdown
#
interface GigabitEthernet3/0/0
 undo shutdown
#
interface GigabitEthernet3/0/1
 undo shutdown
#
interface GigabitEthernet3/0/2
 undo shutdown
#
interface GigabitEthernet3/0/3
 undo shutdown
#
interface GigabitEthernet3/0/4
 undo shutdown
#
interface GigabitEthernet3/0/5
 undo shutdown
#
interface GigabitEthernet3/0/6
 undo shutdown
#
interface GigabitEthernet3/0/7
 undo shutdown
#
interface GigabitEthernet3/0/8
 undo shutdown
 eth-trunk 0
#
interface GigabitEthernet3/0/9
 undo shutdown
 eth-trunk 1
#
interface GigabitEthernet4/0/0
 undo shutdown
#
interface GigabitEthernet4/0/1
 undo shutdown
#
interface GigabitEthernet4/0/2
 undo shutdown
#
interface GigabitEthernet4/0/3
 undo shutdown
#
interface GigabitEthernet4/0/4
 undo shutdown
#
interface GigabitEthernet4/0/5
 undo shutdown
#
interface GigabitEthernet4/0/6
 undo shutdown
#
interface GigabitEthernet4/0/7
 undo shutdown
#
interface GigabitEthernet4/0/8
 undo shutdown
#
interface GigabitEthernet4/0/9
 undo shutdown
#
interface Virtual-if0
#
interface Virtual-if1
#
interface Virtual-if2
#
interface Virtual-if3
#
interface Virtual-if4
#
interface Virtual-if5
#
interface Virtual-if6
#
interface Virtual-if7
#
interface Virtual-if8
#
interface Virtual-if9
#
interface Virtual-if10
#
interface Virtual-if11
#
interface Virtual-if12
#
interface Virtual-if13
#
interface Virtual-if14
#
interface Virtual-if15
#
interface Virtual-if16
#
interface Virtual-if17
#
interface Virtual-if18
#
interface NULL0
#
interface LoopBack0
 ip binding vpn-instance Core
 ip address ************* ***************
 alias LoopBack0
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface GigabitEthernet0/0/0
#
firewall zone untrust
 set priority 5
#
firewall zone dmz
 set priority 50
 add interface Eth-Trunk0
#
firewall interzone trust untrust
detect ftp
#
api
#
ip route-static vpn-instance mgt_vrf 0.0.0.0 0.0.0.0 GigabitEthernet0/0/0 ************* description Out-Of-Band-Management
#
snmp-agent
snmp-agent local-engineid 800007DB0324166DB211BB
snmp-agent community read cipher %^%#;1p/%WbNDL&5,52aE&9U4136"Px1n#!%4FA!8hC5LE$M8#Gb@#)&|JDM(vB>1r[5!1\2e75'7z~8rIR(%^%#
snmp-agent sys-info version v2c
undo snmp-agent sys-info version v3
snmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#EF']-pDWiD8kQ$2%{WcXRt5RB<D{uRcU,"D*lPUM%^%# v2c
snmp-agent target-host trap address udp-domain *********** vpn-instance mgt_vrf params securityname cipher %^%#=9X&#((mFUuO5l2a^>rR)y6+27vwyVHgtBJ0[8>8%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#N=J)-*KS#B<#t1H<{hM/Af~qP4N,;#BGxq-=21<3%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#{a>jOeL5;DgrAgKOCp,0XU.}J|H:J6T;%H4hB+I~%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#(Nj>B3['zRPLfP0Ng#r$$pO4W['I"Gxv&0K@zAvF%^%# v2c
snmp-agent mib-view included View_ALL iso
snmp-agent trap enable
#
undo ssh server compatible-ssh1x enable
stelnet ipv4 server enable
ssh user admin
ssh user admin authentication-type password
ssh user admin service-type all
ssh user admin sftp-directory hda1:
ssh user cslc
ssh user cslc authentication-type password
ssh user cslc service-type all
ssh user netadmin
ssh user netadmin authentication-type password
ssh user netadmin service-type all
ssh user netadmin sftp-directory hda1:
ssh client first-time enable
ssh server cipher aes256_ctr aes128_ctr 3des_cbc
#
firewall detect ftp
#
user-interface con 0
 authentication-mode aaa
user-interface vty 0 4
 authentication-mode aaa
 protocol inbound ssh
user-interface vty 16 20
#
pki realm default
#
sa
#
location
#
multi-interface
 mode proportion-of-weight
#
right-manager server-group
#
agile-network 
#
sandbox cloud
 linkage enable
 file-set EXE max-size 2048
 file-set GZIP max-size 2048
 file-set OFFICE max-size 2048
 file-set PDF max-size 2048
#
device-classification
 device-group pc
 device-group mobile-terminal
 device-group undefined-group
#
user-manage server-sync tsm
#
security-policy
 default policy logging
 default session logging
 rule name ha
  source-zone dmz
  source-zone local
  destination-zone dmz
  destination-zone local
  action permit
 rule name ntp
  source-zone local
  source-zone trust
  destination-zone trust
  destination-address address-set ntp_*******
  service ntp
  action permit
 rule name jianKong
  destination-address address-set Jiankong
  service snmptrap
  service syslog
  action permit
 rule name snmp
  source-address address-set Jiankong
  service snmp
  action permit
 rule name radius
  source-zone local
  destination-zone trust
  destination-address address-set **********
  service radius
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
dns-transparent-policy
 mode based-on-multi-interface
#
rightm-policy
#
decryption-policy
#
mac-access-profile name mac_access_profile
#
 sms
#
return
#
switch vsys Core 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set Core_***********/24 type object
 address 0 *********** mask 24
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set NAS_********-12 type object
 address 0 ******** mask 32
 address 1 ******** mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_*********** type object
 address 0 *********** mask 24
#
ip address-set download_vip_************ type object
 address 0 ************ mask 32
#
ip address-set "UMP servers_*********-12" type object
 address 0 range ********* *********2
#
ip address-set "download servers_************-12" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set XXFB_********-76 type object
 address 0 range ******** ********
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set MG_******* type object
 address 0 ******* mask 8
#
ip address-set FTP_******** type object
 address 0 ******** mask 32
#
ip address-set FTP_******** type object
 address 0 ******** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-22 type object
 address 0 range ************ ************
#
ip address-set pcitupd_ng_************ type object
 address 0 ************ mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set log_server_********** type object
 address 0 ********** mask 32
#
ip address-set XXFB_**********-43 type object
 address 0 range ********** **********
#
ip address-set ********-76 type object
 address 0 range ******** ********
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set YZECCPJ_***********-223 type object
 address 0 range *********** ***********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ***********-226 type object
 address 0 range *********** ***********
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set NEW_down_ng type object
 address 0 range ************ ************
#
ip address-set XXFB_server type object
 address 0 range ********** **********
#
ip address-set UMP_server type object
 address 0 range ********* *********2
 address 1 range *********31 *********34
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1024
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1025
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1035
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
 service 8 protocol tcp source-port 0 to 65535 destination-port 1234
 service 9 protocol udp source-port 0 to 65535 destination-port 1234
#
ip service-set TCP_4505 type object 1040
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1041
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set tcp-20 type object 1089
 service 0 protocol tcp source-port 0 to 65535 destination-port 20
#
ip service-set TCP-8890 type object 1115
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1116
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1117
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_8510 type object 1200
 service 0 protocol tcp destination-port 8510
#
ip service-set TCP_34443 type object 1241
 service 0 protocol tcp destination-port 34443
#
ip service-set tcp-139 type object 1246
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1247
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_8080 type object 1313
 service 0 protocol tcp destination-port 8080
#
ip service-set TCP_6677 type object 1470
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1471
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1472
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1473
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1474
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.301
 vlan-type dot1q 301
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
interface Eth-Trunk1.302
 vlan-type dot1q 302
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
interface Eth-Trunk1.399
 vlan-type dot1q 399
 ip binding vpn-instance Core
 ip address ************* *************
 alias Eth-Trunk1.399
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
#
l2tp-group default-lns
#
interface Virtual-if1
#
interface LoopBack0
 ip binding vpn-instance Core
 ip address ************* ***************
 alias LoopBack0
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.302
 add interface Eth-Trunk1.399
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.301
#
firewall zone dmz
 set priority 50
#
firewall interzone trust untrust
detect ftp
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name XXFB&UMP_2_NEW_dowload
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set UMP_server
  source-address address-set XXFB_server
  destination-address address-set NEW_down_ng
  service ssh
  action permit
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_********-12
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_********-12
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "SGW to download server vip"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address address-set download_vip_************
  service TCP_8443
  service http
  action permit
 rule name "UMP to download servers"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address ************ mask *************
  source-address address-set "UMP servers_*********-12"
  source-address range *********61 *********72
  destination-address address-set "download servers_************-12"
  service ftp
  service ssh
  action permit
 rule name "XXFB to download servers"
  description XinXiFaBu HouTai
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set FTP_********
  source-address address-set FTP_********
  source-address address-set XXFB_********-76
  destination-address address-set "download servers_************-12"
  service ftp
  service ssh
  action permit
 rule name JianKong
  source-zone untrust
  destination-zone trust
  source-address address-set host_***********
  destination-address address-set "download servers_************-12"
  service ftp
  action permit
 rule name "MG to download server"
  source-zone untrust
  destination-zone trust
  source-address address-set MG_*******
  destination-address address-set "download servers_************-12"
  destination-address address-set download_vip_************
  service ftp
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name SCP-1
  source-zone untrust
  destination-zone trust
  source-address *********** mask ***************
  destination-address address-set ************-22
  service ssh
  action permit
 rule name SCP-2
  source-zone trust
  destination-zone untrust
  source-address address-set ************-22
  destination-address *********** mask ***************
  service ssh
  action permit
 rule name "SGW to pcitupd_ng"
  source-zone untrust
  destination-zone trust
  destination-address address-set pcitupd_ng_************
  service http
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service TCP_34443
  service ssh
  action permit
 rule name get_TLSterminal_log
  source-zone untrust
  destination-zone trust
  source-address address-set log_server_**********
  destination-address address-set "download servers_************-12"
  service ssh
  action permit
 rule name XXFB_arthur-TO-download
  source-zone untrust
  destination-zone trust
  source-address address-set XXFB_**********-43
  destination-address address-set "download servers_************-12"
  service ssh
  action permit
 rule name "SJZT-FTP to download servers"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set "download servers_************-12"
  service ssh
  action permit
 rule name "YZECCdownload se"
  source-zone untrust
  destination-zone trust
  source-address address-set YZECCPJ_***********-223
  destination-address address-set "download servers_************-12"
  service ftp
  service https
  service ssh
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name ump-normal_to_download
  policy logging
  source-zone untrust
  destination-zone trust
  source-address range *********31 *********34
  destination-address range ************ ************
  service ssh
  action permit
 rule name SGW_to_pcitinst
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address ************ mask ***************
  service TCP_8080
  action permit
 rule name install_Dis_to_download
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ******** mask ***************
  destination-address range ************ ************
  service ssh
  action permit
 rule name "YZECCdownload se"
  source-address address-set ***********-226
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Core_***********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static *********** ************* ********* description TO-Core
#
 firewall session aging-time service-set tcp-20 1200
#
 sms
#
return
#
switch vsys Normal 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set Normal_***********/24 type object
 address 0 *********** mask 24
#
ip address-set host_********* type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set sleye_nginx type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set container-platform-ingress type object
 description RongQiPingTai
 address 0 range ********** **********
#
ip address-set SGW_*********** type object
 address 0 *********** mask 24
#
ip address-set sleye_Nginx_VIP type object
 address 0 ************ mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set sleye_msgw type object
 description message gateway
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
#
ip address-set net_*********/24 type object
 address 0 ********* mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set *************** type object
 description ***************
 address 0 *************** mask 32
#
ip address-set ************** type object
 description **************
 address 0 ************** mask 32
#
ip address-set Range_***********-54 type object
 address 0 range *********** ***********
#
ip address-set Range_***********-24 type object
 address 0 range *********** ***********
#
ip address-set net_**********/24 type object
 address 0 ********** mask 24
#
ip address-set Range_************-42 type object
 address 0 range ************ ************
#
ip address-set ************-202 type object
 address 0 range ************ ************
#
ip address-set net_**********/24 type object
 address 0 ********** mask 24
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set Range_************-52 type object
 address 0 range ************ ************
#
ip address-set Range_**********-220 type object
 address 0 range ********** **********
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set Range_**********-2 type object
 address 0 range ********** **********
#
ip address-set Range_************-42 type object
 address 0 range ************ ************
#
ip address-set Node_************ type object
 address 0 ************ mask 24
#
ip address-set CFZX type object
 address 0 ************** mask 32
#
ip address-set SZRMB_SSL type object
 address 0 ************ mask 32
#
ip address-set BAJQKHZX type object
 address 0 *********** mask 32
 address 1 ********** mask 32
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set Range_************-62 type object
 address 0 range ************ ************
#
ip address-set Normal_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set CFZX_SERVER type object
 address 0 ************ mask 24
#
ip address-set SZQB_SERVER type object
 address 0 ************ mask 24
#
ip address-set SZRMB_SERVER type object
 address 0 range ************ ************
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set test type group
 address 0 range *********** ***********
#
ip service-set TCP_10050 type object 1026
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1027
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1037
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1042
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1043
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8080 type object 1060
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP-8890 type object 1118
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1119
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1120
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-139 type object 1248
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1249
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_30201 type object 1315
 service 0 protocol tcp destination-port 30201
#
ip service-set TCP_8082 type object 1351
 service 0 protocol tcp source-port 0 to 65535 destination-port 8082
#
ip service-set TCP_8088 type object 1352
 service 0 protocol tcp source-port 0 to 65535 destination-port 8088
#
ip service-set NAS type object 1353
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 635
 service 5 protocol udp source-port 0 to 65535 destination-port 635
 service 6 protocol tcp source-port 0 to 65535 destination-port 4045 to 4049
 service 7 protocol udp source-port 0 to 65535 destination-port 4045 to 4049
#
ip service-set TCP_6677 type object 1475
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1476
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1477
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1478
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1479
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP_8081 type object 1491
 service 0 protocol tcp source-port 0 to 65535 destination-port 8081
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.303
 vlan-type dot1q 303
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.304
 vlan-type dot1q 304
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if2
#
profile type url-filter name sleye_message
 add whitelist url www.dh3t.com
 add whitelist url qyapi.weixin.qq.com
 category pre-defined control-level low
 https-filter enable
 whitelist-only enable
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.304
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.303
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain qyapi.weixin.qq.com 
  add domain www.dh3t.com 
 domain-set name JCURL 
  add domain fusion.txodds.com 
  add domain fusion2prx.txodds.com 
 domain-set name SBURL 
  add domain staticfcc.ccbft.com 
  add domain mapp.dcep.ccb.com 
  add domain ch5.dcep.ccb.com 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name CFZX-SZQB_To_SZRMB
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CFZX_SERVER
  source-address address-set SZQB_SERVER
  destination-address address-set SZRMB_SERVER
  service TCP_8080
  service TCP_8081
  service https
  action permit
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "SGW to sleyeNginx"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address address-set sleye_Nginx_VIP
  service http
  action permit
 rule name sleyeNginx-to-container-ingress
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set sleye_nginx
  destination-address address-set container-platform-ingress
  service http
  action permit
 rule name "sleye to internet qyapi"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set sleye_msgw
  destination-address address-set ************
  destination-address address-set ***************
  destination-address address-set **************
  destination-address domain-set URL
  service dns
  service http
  service https
  action permit
 rule name "sleye to internet"
  disable
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set sleye_msgw
  service dns
  service http
  service https
  profile url-filter sleye_message
  action permit
 rule name "container to sleye_message"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set net_*********/24
  destination-address address-set sleye_msgw
  service TCP_8080
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name "YXZX-K8S to G3-KFPT-NG"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address ********* mask *************
  destination-address ************* mask ***************
  service TCP_30201
  action permit
 rule name ShuJuChanPin_To_msgw
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set Node_************
  source-address address-set Range_***********-24
  source-address address-set Range_***********-54
  destination-address address-set sleye_msgw
  service TCP_8080
  action permit
 rule name SGW_to_CSLJC-BOS
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address ************ mask ***************
  service TCP_8082
  action permit
 rule name CSLJC-CORE-K8S_TO_BOSROUTE-F5
  source-zone untrust
  destination-zone trust
  source-address address-set net_**********/24
  destination-address ************ mask ***************
  service TCP_8082
  action permit
 rule name G3BOSROUTER_TO_DMZ-NAS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-42
  destination-address address-set ************-202
  service NAS
  service icmp
  action permit
 rule name JC-BOSROUTER_TO_BOS-WCSROUTE
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-42
  destination-address *********** mask ***************
  service TCP_8088
  action permit
 rule name JC-K8S_TO_BOSRouter-F5
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set net_**********/24
  destination-address address-set host_************
  service TCP_8082
  action permit
 rule name BOSRouter_To_Internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-42
  destination-address domain-set JCURL
  service dns
  service https
  action permit
 rule name BOSRouter_To_DNS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-42
  service dns
  action permit
 rule name BOSRouter_To_txodds
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-42
  destination-address ************** mask ***************
  destination-address ************** mask ***************
  destination-address ************* mask ***************
  destination-address ************ mask ***************
  destination-address *********** mask ***************
  service https
  action permit
 rule name SZRMB-NG_To_DNS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-52
  service dns
  action permit
 rule name SZRMB-NG_To_Internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-52
  destination-address domain-set SBURL
  service https
  action permit
 rule name CY_To_SZRMB-NG
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set CFZX
  source-address address-set Range_**********-220
  destination-address address-set host_************
  service https
  action permit
 rule name YWZD_to_CSLJC-BOS
  source-zone untrust
  destination-zone trust
  source-address address-set Range_************-42
  source-address address-set Range_**********-2
  destination-address ************ mask ***************
  destination-address address-set Range_************-42
  service TCP_8082
  service ssh
  action permit
 rule name SZRMB_To_BAJQKHZX
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_************-62
  destination-address address-set BAJQKHZX
  service TCP_8080
  action permit
 rule name SZRMB_SSL_To_SZRMB_NG
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  source-address address-set SZRMB_SSL
  destination-address address-set host_************
  service TCP_8080
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Normal_***********/24
  source-address address-set Normal_***********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static *********** ************* ********** description TO-Normal
ip route-static *********** ************* ********** description G3_XinXiFaBu
#
 sms
#
return
#
switch vsys Others 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ************
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set YJZH_************ type object
 description YingJiZhiHui_MaYue
 address 0 ************ mask 32
#
ip address-set JYJK_************ type object
 description JiaoYiJianKong_MaYue
 address 0 ************ mask 32
#
ip address-set ***********-12 type object
 description JiaoYiJianKong_YingJiZhiHui
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set Monitor-NG type object
 address 0 range ************ ************
#
ip address-set G3-Monitor type object
 address 0 range *********** ***********
#
ip address-set Monitor_************-22 type object
 address 0 range ************ ************
#
ip address-set **********-12 type object
 address 0 range ********** **********
#
ip address-set ************-12 type object
 address 0 range ************ ************
#
ip address-set SFTP_***********00 type object
 address 0 ***********00 mask 32
#
ip address-set ************-52 type object
 address 0 range ************ ************
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set ************-57 type object
 address 0 range ************ ************
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set **********-12 type object
 address 0 range ********** **********
#
ip address-set Normal_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set TIP-IP type object
 address 0 ************ mask 32
 address 1 ************** mask 32
 address 2 ************* mask 32
 address 3 ************* mask 32
 address 4 ************ mask 32
 address 5 ************6 mask 32
 address 6 ************* mask 32
 address 7 ************** mask 32
 address 8 ************ mask 32
 address 9 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set ***********-54 type object
 address 0 range *********** ***********
#
ip service-set TCP_10050 type object 1030
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1031
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1036
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1044
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1045
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1121
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1122
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1123
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-9883 type object 1182
 service 0 protocol tcp source-port 0 to 65535 destination-port 9883
#
ip service-set tcp-9995 type object 1183
 service 0 protocol tcp source-port 0 to 65535 destination-port 9995
#
ip service-set tcp-9994 type object 1184
 service 0 protocol tcp source-port 0 to 65535 destination-port 9994
#
ip service-set TCP-8090 type object 1185
 service 0 protocol tcp source-port 0 to 65535 destination-port 8090
#
ip service-set TCP_8080 type object 1225
 service 0 protocol tcp destination-port 8080
#
ip service-set TCP_8444 type object 1242
 service 0 protocol tcp destination-port 8444
#
ip service-set TCP_8066 type object 1243
 service 0 protocol tcp destination-port 8066
#
ip service-set TCP_9200 type object 1244
 service 0 protocol tcp destination-port 9200
#
ip service-set tcp-139 type object 1250
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1251
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_38081 type object 1321
 service 0 protocol tcp destination-port 38081
#
ip service-set TCP_38082 type object 1338
 service 0 protocol tcp destination-port 38082
#
ip service-set TCP_6280 type object 1339
 service 0 protocol tcp destination-port 6280
#
ip service-set TCP_6279 type object 1346
 service 0 protocol tcp destination-port 6279
#
ip service-set TCP_38083 type object 1349
 service 0 protocol tcp destination-port 38083
#
ip service-set TCP_30000 type object 1367
 service 0 protocol tcp destination-port 30000
#
ip service-set TCP_25 type object 1373
 service 0 protocol tcp destination-port 25
#
ip service-set TCP_6677 type object 1480
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1481
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1482
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1483
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1484
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.305
 vlan-type dot1q 305
 ip binding vpn-instance Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.306
 vlan-type dot1q 306
 ip binding vpn-instance Others
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if3
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.306
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.305
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain qyapi.weixin.qq.com 
  add domain mail.sporttery.cn 
 domain-set name TIPURL 
  add domain static.threatbook.cn 
  add domain static-css.threatbook.cn 
  add domain api.threatbook.cn 
  add domain static-js.threatbook.cn 
  add domain s.threatbook.cn 
  add domain x.threatbook.com 
  add domain z.threatbook.cn 
  add domain static-img.threatbook.cn 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name k01
  source-zone local
  source-zone trust
  destination-zone untrust
  source-address *********** mask ***************
  source-address *********** mask ***************
  service TCP-8090
  service dns
  service icmp
  service tcp-9883
  service tcp-9994
  service tcp-9995
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name SGW_to_YingJiZhiHui
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set YJZH_************
  service http
  action permit
 rule name SGW_to_JiaoYiJianKong
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set JYJK_************
  service http
  action permit
 rule name JYJK_nginx_to_DNS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set JYJK_************
  source-address address-set YJZH_************
  service dns
  action permit
 rule name JYJK_nginx_to_internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set JYJK_************
  source-address address-set YJZH_************
  destination-address domain-set URL
  service https
  action permit
 rule name JYJK_nginx_to_APP
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set JYJK_************
  source-address address-set YJZH_************
  destination-address ************ mask ***************
  destination-address address-set ***********-12
  service TCP_6279
  service TCP_6280
  service TCP_8080
  action permit
 rule name Security_to_TaiShiGanZhi
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  source-address range ************ ************
  destination-address ************ mask ***************
  service TCP_8444
  service https
  action permit
 rule name TaiShiGanZhi_to_Security
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address range ************ ************
  service TCP_8066
  service TCP_9200
  service https
  action permit
 rule name Security_to_duplex
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  destination-address range ************ ************
  action permit
 rule name TaiShiGanZhi_to_duplex
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  destination-address range *********** ***********
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name nginx_to_monitorAPP
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  destination-address ********** mask ***************
  destination-address address-set **********-12
  destination-address address-set ************-12
  service TCP_38081
  service TCP_38083
  action permit
 rule name SGW_to_monitorNG
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address ************ mask ***************
  service http
  action permit
 rule name G3-Monitor-NG_TO_internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  destination-address domain-set URL
  service https
  action permit
 rule name G3-Monitor-NG_to_internet-DNS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  service dns
  action permit
 rule name G3-Monitor_TO_Monitor-NG
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set G3-Monitor
  source-address address-set Monitor_************-22
  destination-address address-set Monitor-NG
  service TCP_38082
  action permit
 rule name JuHeZhiFu-SFTP
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SFTP_***********00
  service TCP_30000
  service ssh
  action permit
 rule name SGW_to_CS
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set host_************
  service TCP_8080
  action permit
 rule name CS_to_b2csweb
  source-zone trust
  destination-zone untrust
  source-address address-set ************-52
  destination-address address-set host_**********
  service TCP_8080
  action permit
 rule name b2csweb_to_mail
  source-zone untrust
  destination-zone trust
  source-address address-set **********-12
  destination-address address-set host_************
  service TCP_25
  action permit
 rule name mail_to_Internet
  source-zone trust
  destination-zone untrust
  source-address address-set ************-57
  service TCP_25
  service dns
  service icmp
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Normal_***********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name SOAS_to_TIP
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  destination-address address-set host_************
  service TCP-8090
  service https
  service syslog
  action permit
 rule name TIP_to_DNS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_************
  service dns
  service icmp
  action permit
 rule name TIP_to_Internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_************
  destination-address domain-set TIPURL
  service https
  action permit
 rule name TIP_to_Internet-1
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_************
  destination-address address-set TIP-IP
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
 rule name SecOCS-To-TIP
  source-zone untrust
  destination-zone trust
  source-address address-set ***********-54
  destination-address address-set host_************
  service https
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static *********** ************* *********1 description TO-Others
#
 sms
#
return
#
switch vsys AZZD 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ************
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set QRAS type object
 address 0 range ***********1 ***********2
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_******* type object
 address 0 ******* mask 32
#
ip address-set update_server type object
 address 0 range ***********1 ***********2
#
ip address-set SGW_*********** type object
 address 0 *********** mask 24
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set ********** type object
 address 0 ********** mask ***************
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set *********/24 type object
 address 0 ********* mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip service-set TCP_10050 type object 1032
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1033
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1038
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP-8890 type object 1124
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1125
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1126
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_81 type object 1198
 service 0 protocol tcp source-port 0 to 65535 destination-port 81
#
ip service-set TCP_8080 type object 1229
 service 0 protocol tcp destination-port 8080
#
ip service-set TCP_5080 type object 1230
 service 0 protocol tcp destination-port 5080
#
ip service-set TCP_8000 type object 1231
 service 0 protocol tcp destination-port 8000
#
ip service-set TCP_34443 type object 1232
 service 0 protocol tcp destination-port 34443
#
ip service-set tcp-139 type object 1252
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1253
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_6677 type object 1383
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1384
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1385
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1386
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1387
 service 0 protocol tcp destination-port 8443
#
ip service-set TCP-8090 type object 1490
 service 0 protocol tcp source-port 0 to 65535 destination-port 8090
#
ip service-set tcp-26389 type object 1519
 service 0 protocol tcp source-port 0 to 65535 destination-port 26389
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.307
 vlan-type dot1q 307
 ip binding vpn-instance AZZD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.308
 vlan-type dot1q 308
 ip binding vpn-instance AZZD
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if4
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.308
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.307
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain api.weixin.qq.com 
  add domain file.api.weixin.qq.com 
  add domain openapi.alipay.com 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name "SGW to pcittscs"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address address-set QRAS
  service TCP_81
  action permit
 rule name "SGW to pcittscsup"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address address-set update_server
  service http
  action permit
 rule name QRAS-to-qianguanzhi
  source-zone trust
  destination-zone untrust
  source-address address-set QRAS
  destination-address address-set host_**********
  service http
  action permit
 rule name zidongbushu_update_server
  source-zone untrust
  destination-zone trust
  source-address address-set host_*******
  destination-address address-set update_server
  service ssh
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service TCP_34443
  service ssh
  action permit
 rule name SGW_to_AZZD-js-epb
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address *********** mask ***************
  service TCP_8080
  action permit
 rule name SGW_to_AZZD-infohub
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address *********** mask ***************
  service http
  action permit
 rule name SGW_to_AZZD-downld
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address *********** mask ***************
  service http
  action permit
 rule name auth-nginx_to_internetDNS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  service dns
  action permit
 rule name auth-nginx_to_internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address domain-set URL
  service http
  service https
  action permit
 rule name API_to_ELB-proxy
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ********** mask ***************
  destination-address ******** mask ***************
  service TCP_5080
  service http
  action permit
 rule name infohub_to_SIH
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address range ********** **********
  service TCP_8000
  action permit
 rule name ump_to_download
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ************ mask *************
  source-address range ********* *********2
  destination-address range *********** ***********
  service TCP_34443
  service ftp
  service http
  service ssh
  action permit
 rule name AZZD-manage
  policy logging
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  destination-address ********** mask *************
  service http
  service ssh
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name ump-normal_to_azzd-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address range *********31 *********34
  source-address range *********61 *********72
  destination-address range *********** ***********
  service TCP_34443
  action permit
 rule name ZiDongHua_To_AZZD-NG
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  source-address address-set host_***********
  source-address address-set host_*********
  destination-address range *********** ***********
  service TCP_34443
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name tc-cloud_to_bp-pmc
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ************ mask *************
  source-address ************ mask *************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service TCP-8090
  service TCP_8080
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
 rule name jk-26389
  source-zone untrust
  destination-zone trust
  source-address address-set *********/24
  source-address address-set ***********/24
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  service tcp-26389
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* ********** description TO-azzd
#
 sms
#
return
#
switch vsys SGW 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ************
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set SSL_External_IP type object
 address 0 *********** mask 24
 address 1 *********** mask 24
 address 2 *********** mask 24
 address 3 *********** mask 24
 address 4 *********** mask 24
 address 5 *********** mask 24
#
ip address-set sleye_nginx_vip type object
 address 0 ************ mask 32
#
ip address-set LDAP_********* type object
 address 0 ********* mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set appauth_nginx_************ type object
 address 0 ************ mask 32
#
ip address-set STQD_SSL_************ type object
 address 0 ************ mask 32
#
ip address-set STQD_tc-map_************ type object
 address 0 ************ mask 32
#
ip address-set singdownload_ssl_************ type object
 address 0 ************ mask 32
#
ip address-set doubdownload_ssl_************ type object
 address 0 ************ mask 32
#
ip address-set sleye_ssl_************ type object
 address 0 ************ mask 32
#
ip address-set download_server_************ type object
 address 0 ************ mask 32
#
ip address-set ump_************ type object
 address 0 ************ mask 32
#
ip address-set umpDuplex_************ type object
 address 0 ************ mask 32
#
ip address-set webseal_********** type object
 address 0 ********** mask 32
#
ip address-set stqd_web_*********** type object
 address 0 *********** mask 32
#
ip address-set stqd_api_*********** type object
 address 0 *********** mask 32
#
ip address-set stqd_pic_*********** type object
 address 0 *********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set cslp_ildg_*********** type object
 address 0 *********** mask 32
#
ip address-set ildg_ssl_************ type object
 address 0 ************ mask 32
#
ip address-set famcache_********** type object
 address 0 ********** mask 32
#
ip address-set famsdk_********** type object
 address 0 ********** mask 32
#
ip address-set famtrade_********** type object
 address 0 ********** mask 32
#
ip address-set famuser_********** type object
 address 0 ********** mask 32
#
ip address-set openapi_********** type object
 address 0 ********** mask 32
#
ip address-set ticket_********** type object
 address 0 ********** mask 32
#
ip address-set pcittscs_3.29.1.31-32 type object
 address 0 3.29.1.31 mask 32
 address 1 3.29.1.32 mask 32
#
ip address-set pcittscsup_3.29.1.41-42 type object
 address 0 3.29.1.41 mask 32
 address 1 3.29.1.42 mask 32
#
ip address-set download_3.29.1.10 type object
 address 0 3.29.1.10 mask 32
#
ip address-set infohub_3.17.10.10 type object
 address 0 3.17.10.10 mask 32
#
ip address-set js-epb_3.29.1.50 type object
 address 0 3.29.1.50 mask 32
#
ip address-set sjzt_3.29.8.10 type object
 address 0 3.29.8.10 mask 32
#
ip address-set sjzt_3.29.9.10 type object
 address 0 3.29.9.10 mask 32
#
ip address-set usap_3.29.11.10 type object
 address 0 3.29.11.10 mask 32
#
ip address-set xxfb_3.29.4.10 type object
 address 0 3.29.4.10 mask 32
#
ip address-set xxfb_3.29.4.30 type object
 address 0 3.29.4.30 mask 32
#
ip address-set xxfb_3.29.4.70 type object
 address 0 3.29.4.70 mask 32
#
ip address-set yxyf_3.29.7.11 type object
 address 0 3.29.7.11 mask 32
#
ip address-set yxyf_3.29.7.12 type object
 address 0 3.29.7.12 mask 32
#
ip address-set wsjc_3.29.12.10 type object
 address 0 3.29.12.10 mask 32
#
ip address-set STQD_3.29.2.125 type object
 address 0 3.29.2.125 mask 32
#
ip address-set STQD_3.29.2.130 type object
 address 0 3.29.2.130 mask 32
#
ip address-set LX_3.29.3.10 type object
 address 0 3.29.3.10 mask 32
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set pcittscs_***********1-102 type object
 address 0 ***********1 mask 32
 address 1 ***********2 mask 32
#
ip address-set pcittscsup_***********1-112 type object
 address 0 ***********1 mask 32
 address 1 ***********2 mask 32
#
ip address-set xxfb_4.103.16.10 type object
 address 0 4.103.16.10 mask 32
#
ip address-set xxfb_*********** type object
 address 0 *********** mask 32
#
ip address-set xxfb_4.103.16.70 type object
 address 0 4.103.16.70 mask 32
#
ip address-set USAP_jcqd_*********** type object
 address 0 *********** mask 32
#
ip address-set ***********-14 type object
 address 0 range *********** ***********
#
ip address-set ***********1-102 type object
 address 0 ***********1 mask 32
 address 1 ***********2 mask 32
#
ip address-set pcitupd_ng_************ type object
 address 0 ************ mask 32
#
ip address-set net_***********-22 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set net_***********-16 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 *********** mask 32
 address 5 *********** mask 32
 address 6 *********** mask 32
#
ip address-set net_***********-32 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set STQD_4.103.12.30-32 type object
 address 0 range 4.103.12.30 4.103.12.32
#
ip address-set YJZH_************ type object
 description YingJiZhiHui_MaYue
 address 0 ************ mask 32
#
ip address-set JYJK_************ type object
 description JiaoYiJianKong_MaYue
 address 0 ************ mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip service-set TCP_10050 type object 1028
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1029
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set TCP_389 type object 1034
 service 0 protocol tcp source-port 0 to 65535 destination-port 389
#
ip service-set "NAS service port" type object 1039
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1046
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1047
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8501 type object 1066
 service 0 protocol tcp source-port 0 to 65535 destination-port 8501
#
ip service-set TCP_8502 type object 1067
 service 0 protocol tcp source-port 0 to 65535 destination-port 8502
#
ip service-set TCP_8503 type object 1068
 service 0 protocol tcp source-port 0 to 65535 destination-port 8503
#
ip service-set TCP_8510 type object 1069
 service 0 protocol tcp source-port 0 to 65535 destination-port 8510
#
ip service-set TCP_8010 type object 1092
 service 0 protocol tcp destination-port 8010
#
ip service-set TCP_8520 type object 1093
 service 0 protocol tcp destination-port 8520
#
ip service-set TCP_8080 type object 1105
 service 0 protocol tcp destination-port 8080
#
ip service-set TCP_8443 type object 1106
 service 0 protocol tcp destination-port 8443
#
ip service-set TCP_7001 type object 1107
 service 0 protocol tcp destination-port 7001
#
ip service-set TCP-8890 type object 1127
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1128
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1129
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_81 type object 1151
 service 0 protocol tcp destination-port 81
#
ip service-set TCP_10088 type object 1152
 service 0 protocol tcp destination-port 10088
#
ip service-set TCP_10080 type object 1153
 service 0 protocol tcp destination-port 10080
#
ip service-set TCP_8000 type object 1154
 service 0 protocol tcp destination-port 8000
#
ip service-set TCP_18080 type object 1155
 service 0 protocol tcp destination-port 18080
#
ip service-set TCP_8181 type object 1156
 service 0 protocol tcp destination-port 8181
#
ip service-set tcp-139 type object 1254
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1255
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_8023 type object 1280
 service 0 protocol tcp destination-port 8023
#
ip service-set TCP_8024 type object 1281
 service 0 protocol tcp destination-port 8024
#
ip service-set TCP_9443 type object 1282
 service 0 protocol tcp destination-port 9443
#
ip service-set TCP_8081 type object 1300
 service 0 protocol tcp destination-port 8081
#
ip service-set TCP_8082 type object 1301
 service 0 protocol tcp destination-port 8082
#
ip service-set TCP_8083 type object 1302
 service 0 protocol tcp destination-port 8083
#
ip service-set TCP_8084 type object 1303
 service 0 protocol tcp destination-port 8084
#
ip service-set TCP_7443 type object 1370
 service 0 protocol tcp destination-port 7443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.397
 vlan-type dot1q 397
 ip binding vpn-instance SGW
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.398
 vlan-type dot1q 398
 ip binding vpn-instance SGW
 ip address *********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if5
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.398
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.397
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  policy logging
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Zabbix_JianKong
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "internet to sleye ssl"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set sleye_ssl_************
  service https
  action permit
 rule name "internet to singdownload ssl"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set singdownload_ssl_************
  service https
  action permit
 rule name "internet to doubDownload ssl"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set doubdownload_ssl_************
  service https
  action permit
 rule name "internet to SQTD"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set STQD_SSL_************
  service TCP_8010
  service TCP_8501
  service TCP_8502
  service TCP_8503
  service TCP_8510
  service TCP_8520
  service https
  action permit
 rule name "internet to UMP"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set ump_************
  service https
  action permit
 rule name "internet to UMP_Duplex"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set umpDuplex_************
  service https
  action permit
 rule name "internet to ildg"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set ildg_ssl_************
  service https
  action permit
 rule name "internet to SGW"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SGW_***********/24
  service TCP_10088
  service TCP_7001
  service TCP_7443
  service TCP_8023
  service TCP_8024
  service TCP_8443
  service TCP_8510
  service TCP_8520
  service TCP_9443
  service https
  action permit
 rule name "SGW to sleye"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set sleye_nginx_vip
  service http
  action permit
 rule name "SGW to LDAP"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set *********
  destination-address address-set LDAP_*********
  service TCP_389
  action permit
 rule name "SGW to STQD_appauth"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set appauth_nginx_************
  service TCP_8501
  service TCP_8502
  service TCP_8503
  action permit
 rule name "SGW to STQD_tc-map"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set STQD_tc-map_************
  service http
  action permit
 rule name "SGW to download server"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set download_server_************
  service TCP_8443
  service http
  action permit
 rule name "SGW to stqd_web"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set stqd_web_***********
  service http
  action permit
 rule name "SGW to stqd_api"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set stqd_api_***********
  service http
  action permit
 rule name "SGW to stqd_pic"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set stqd_pic_***********
  service http
  action permit
 rule name "SGW to webseal"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set webseal_**********
  service http
  action permit
 rule name "SGW to cslp_ildg"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  destination-address address-set cslp_ildg_***********
  service TCP_8010
  action permit
 rule name "SGW to famcache"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service TCP_8084
  action permit
 rule name "SGW to famsdk"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service TCP_8081
  action permit
 rule name "SGW to famtrade"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service TCP_8082
  action permit
 rule name "SGW to famuser"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service TCP_8083
  action permit
 rule name "SGW to openapi"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  destination-address address-set openapi_**********
  service TCP_7001
  action permit
 rule name "SGW to ticket"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name "SGW to pcittscs"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set pcittscs_3.29.1.31-32
  destination-address address-set pcittscs_***********1-102
  service TCP_81
  action permit
 rule name "SGW to pcittscsup"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set pcittscsup_3.29.1.41-42
  destination-address address-set pcittscsup_***********1-112
  service http
  action permit
 rule name "SGW to Android_download"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set download_3.29.1.10
  service TCP_10080
  action permit
 rule name "SGW to Android_infohub"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set infohub_3.17.10.10
  service TCP_8000
  action permit
 rule name "SGW to Android_js-epb"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set js-epb_3.29.1.50
  service TCP_8080
  action permit
 rule name "SGW to sjzt_api"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set sjzt_3.29.8.10
  service http
  action permit
 rule name "SGW to sjzt_receive"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set sjzt_3.29.9.10
  service TCP_8080
  action permit
 rule name "SGW to usap"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set usap_3.29.11.10
  service http
  action permit
 rule name "SGW to XXFB_tapi"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set xxfb_3.29.4.10
  destination-address address-set xxfb_4.103.16.10
  service TCP_8080
  service http
  action permit
 rule name "SGW to XXFB_report"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set xxfb_3.29.4.30
  destination-address address-set xxfb_***********
  service http
  action permit
 rule name "SGW to XXFB_yoda"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set xxfb_3.29.4.70
  destination-address address-set xxfb_4.103.16.70
  service TCP_8080
  service http
  action permit
 rule name "SGW to YXYF_yjzh"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set yxyf_3.29.7.11
  service http
  action permit
 rule name "SGW to YXYF_jyjk"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set yxyf_3.29.7.12
  service http
  action permit
 rule name "SGW to WSJC"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  destination-address address-set wsjc_3.29.12.10
  service http
  action permit
 rule name "SGW to STQD_tc-auth"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set STQD_3.29.2.125
  destination-address address-set STQD_4.103.12.30-32
  service http
  action permit
 rule name "SGW to STQD_bi"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set STQD_3.29.2.130
  service TCP_8181
  action permit
 rule name "SGW to LX_pnup-ha"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set ***********-14
  destination-address address-set LX_3.29.3.10
  service TCP_18080
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name "SGW to USAP_jcqd"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set USAP_jcqd_***********
  service http
  action permit
 rule name "SGW to pcitupd_ng"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set pcitupd_ng_************
  service http
  action permit
 rule name "SGW to DATA_NG"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set net_***********-22
  service http
  action permit
 rule name "SGW to Receive_APP_NG"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set net_***********-16
  service TCP_8080
  action permit
 rule name "SGW to BMS_NG"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set net_***********-32
  service http
  action permit
 rule name SGW_to_YingJiZhiHui
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set YJZH_************
  service http
  action permit
 rule name SGW_to_JiaoYiJianKong
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set JYJK_************
  service http
  action permit
 rule name SGW_to_AZZD-js-epb
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service TCP_8080
  action permit
 rule name SGW_to_AZZD-infohub
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name SGW_to_AZZD-downld
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name SGW_to_STQD-ecnginx
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address ************ mask ***************
  service http
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name SGW_to_CSLJC
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address ********* mask ***************
  action permit
 rule name SGW_to_pcitinst
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address ************ mask ***************
  service TCP_8080
  action permit
 rule name SGW_to_fnc
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service TCP_8080
  action permit
 rule name SGW_to_monitorNG
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address ************ mask ***************
  service http
  action permit
 rule name SGW_to_XXFB-CDN
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name SGW_to_CSLJC-BOS
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address ************ mask ***************
  service TCP_8082
  action permit
 rule name SGW_to_CS
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address ************ mask ***************
  service TCP_8080
  action permit
 rule name SGW-To-SZRMB_NG
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set host_************
  service TCP_8080
  action permit
 rule name SSL_to_YODA_F5
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set ***********
  service http
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 ***********
ip route-static *********** ************* *********** description TO-SGW
ip route-static *********** ************* *********** description TO-SGW
ip route-static *********** ************* *********** description TO-SGW
ip route-static *********** ************* *********** description TO-SGW
ip route-static *********** ************* *********** description TO-SGW
ip route-static *********** ************* *********** description TO-SGW
ip route-static *********** ************* *********** description TO-SGW
ip route-static *********** ************* *********** description TO-SGW
#
 sms
#
return
#
switch vsys STQD 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set tc-map-vip_************ type object
 address 0 ************ mask 32
#
ip address-set tc-map_nginx type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set appauth_nginx_************ type object
 address 0 ************ mask 32
#
ip address-set appauth-app_********** type object
 address 0 ********** mask 32
#
ip address-set appauth-app_********** type object
 address 0 ********** mask 32
#
ip address-set ShiMingFuWu_API type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set ShiMingYanZhen_group type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set BaiDu_DNS_************ type object
 address 0 ************ mask 32
#
ip address-set stqd_web_*********** type object
 address 0 *********** mask 32
#
ip address-set stqd_api_*********** type object
 address 0 *********** mask 32
#
ip address-set stqd_pic_*********** type object
 address 0 *********** mask 32
#
ip address-set stqd_web_nginx type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set stqd_api_nginx type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set stqd_pic_nginx type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set stqd_web_*********** type object
 address 0 *********** mask 32
#
ip address-set stqd_api_********** type object
 address 0 ********** mask 32
#
ip address-set stqd_pic_********** type object
 address 0 ********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set api_client type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
 address 3 3.24.10.67 mask 32
 address 4 3.24.10.68 mask 32
 address 5 3.24.10.69 mask 32
#
ip address-set api_nginx type object
 address 0 4.103.12.141 mask 32
 address 1 4.103.12.142 mask 32
 address 2 4.103.12.140 mask 32
#
ip address-set stqs_web_3.24.10.231-238 type object
 address 0 range 3.24.10.231 3.24.10.238
#
ip address-set stqd_api_3.24.10.50-58 type object
 address 0 range 3.24.10.50 3.24.10.58
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set STQD_4.24.11.20-28 type object
 address 0 range 4.24.11.20 **********
#
ip address-set STQD_4.24.11.30-38 type object
 address 0 range 4.24.11.30 **********
#
ip address-set STQD_4.24.11.80-88 type object
 address 0 range 4.24.11.80 4.24.11.88
#
ip address-set STQD_**********-94 type object
 address 0 range ********** 4.24.11.94
#
ip address-set STQD_4.103.12.20-22 type object
 address 0 range 4.103.12.20 4.103.12.22
#
ip address-set STQD_***********-82 type object
 address 0 range *********** ***********
#
ip address-set STQD_4.103.12.130-138 type object
 address 0 range 4.103.12.130 4.103.12.138
#
ip address-set STQD_**********/24 type object
 address 0 ********** mask 24
#
ip address-set STQD_4.24.13.166/32 type object
 address 0 4.24.13.166 mask 32
#
ip address-set STQD_4.103.12.140-142 type object
 address 0 range 4.103.12.140 4.103.12.142
#
ip address-set STQD_4.103.12.30-32 type object
 address 0 range 4.103.12.30 4.103.12.32
#
ip address-set STQD_4.24.13.10-12 type object
 address 0 range 4.24.13.10 4.24.13.12
#
ip address-set STQD_4.24.13.20-22 type object
 address 0 range 4.24.13.20 4.24.13.22
#
ip address-set STQD_4.24.13.40-42 type object
 address 0 range 4.24.13.40 4.24.13.42
#
ip address-set STQD_4.24.11.40-44 type object
 address 0 range 4.24.11.40 **********
#
ip address-set STQD_**********-74 type object
 address 0 range ********** **********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set STQD_**********-78 type object
 address 0 range ********** **********
#
ip address-set ************** type object
 description **************
 address 0 ************** mask 32
#
ip address-set *************** type object
 description ***************
 address 0 *************** mask 32
#
ip address-set DNS_tuijian type object
 address 0 223.5.5.5 mask 32
 address 1 ************** mask 32
 address 2 *************** mask 32
#
ip address-set STQD_***********-184 type object
 address 0 range *********** ***********
#
ip address-set host_10.194.119.2 type object
 address 0 10.194.119.2 mask 32
#
ip address-set STQD_4.103.12.21-22 type object
 address 0 range 4.103.12.21 4.103.12.22
#
ip address-set STQD_4.24.11.171-174 type object
 address 0 range 4.24.11.171 4.24.11.174
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set STQD_4.24.11.141-144 type object
 address 0 range 4.24.11.141 4.24.11.144
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1048
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1049
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1050
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1051
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1052
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8330 type object 1059
 service 0 protocol tcp source-port 0 to 65535 destination-port 8330
#
ip service-set TCP_8501 type object 1061
 service 0 protocol tcp source-port 0 to 65535 destination-port 8501
#
ip service-set TCP_8502 type object 1062
 service 0 protocol tcp source-port 0 to 65535 destination-port 8502
#
ip service-set TCP_8503 type object 1063
 service 0 protocol tcp source-port 0 to 65535 destination-port 8503
#
ip service-set appauth-app type object 1064
 service 0 protocol tcp source-port 0 to 65535 destination-port 11000
 service 1 protocol tcp source-port 0 to 65535 destination-port 11010
 service 2 protocol tcp source-port 0 to 65535 destination-port 11020
 service 3 protocol tcp source-port 0 to 65535 destination-port 11030
 service 4 protocol tcp source-port 0 to 65535 destination-port 11040
 service 5 protocol tcp source-port 0 to 65535 destination-port 11050
 service 6 protocol tcp source-port 0 to 65535 destination-port 11060
 service 7 protocol tcp source-port 0 to 65535 destination-port 11080
 service 8 protocol tcp source-port 0 to 65535 destination-port 11090
#
ip service-set TCP_2195 type object 1065
 service 0 protocol tcp source-port 0 to 65535 destination-port 2195
#
ip service-set TCP_8181 type object 1077
 service 0 protocol tcp source-port 0 to 65535 destination-port 8181
#
ip service-set TCP_3128 type object 1078
 service 0 protocol tcp source-port 0 to 65535 destination-port 3128
#
ip service-set TCP-8890 type object 1130
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1131
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1132
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_8000 type object 1221
 service 0 protocol tcp destination-port 8000
#
ip service-set TCP_7000 type object 1222
 service 0 protocol tcp destination-port 7000
#
ip service-set TCP_12131 type object 1223
 service 0 protocol tcp destination-port 12131
#
ip service-set TCP_10080 type object 1224
 service 0 protocol tcp source-port 0 to 65535 destination-port 10080
#
ip service-set TCP_8600 type object 1236
 service 0 protocol tcp destination-port 8600
#
ip service-set TCP_20128 type object 1237
 service 0 protocol tcp destination-port 20128
#
ip service-set tcp-139 type object 1256
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1257
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_110 type object 1305
 service 0 protocol tcp destination-port 110
#
ip service-set TCP_43 type object 1306
 service 0 protocol tcp destination-port 43
#
ip service-set TCP_143 type object 1307
 service 0 protocol tcp destination-port 143
#
ip service-set TCP_995 type object 1308
 service 0 protocol tcp destination-port 995
#
ip service-set TCP_993 type object 1309
 service 0 protocol tcp destination-port 993
#
ip service-set TCP_1995 type object 1310
 service 0 protocol tcp destination-port 1995
#
ip service-set udp-1812 type object 1327
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_26379 type object 1358
 service 0 protocol tcp destination-port 26379
#
ip service-set tcp-6379 type object 1361
 service 0 protocol tcp source-port 0 to 65535 destination-port 6379
#
ip service-set TCP_6677 type object 1388
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1389
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1390
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1391
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1392
 service 0 protocol tcp destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
acl number 3001
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip source ************ 0 
 rule 1 permit ip destination ************ 0 
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.309
 vlan-type dot1q 309
 ip binding vpn-instance STQD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.310
 vlan-type dot1q 310
 ip binding vpn-instance STQD
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if6
#
profile type url-filter name "appauth to internet"
 add whitelist url gateway.push.apple.com
 add whitelist url gateway.sandbox.push.apple.com
 category pre-defined control-level low
 https-filter enable
 whitelist-only enable
profile type url-filter name ShiMingYanZhen
 add whitelist host iv.unitid.*
 add whitelist host iv1.unitid.*
 category pre-defined control-level low
 https-filter enable
 whitelist-only enable
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.310
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.309
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain iv.unitid.cn 
  add domain iv1.unitid.cn 
  add domain restapi.amap.com 
  add domain api.map.baidu.com 
 domain-set name URL_message 
  add domain api.netease.im 
  add domain api.jpush.cn 
  add domain device.jpush.cn 
  add domain report.jpush.cn 
  add domain inforequest.sporttery.cn 
  add domain cncapi.getui.com 
  add domain telapi.getui.com 
  add domain api.getui.com 
 domain-set name URL_auth-true 
  add domain fs.unitid.cn 
  add domain iv.unitid.cn 
  add domain fs1.unitid.cn 
  add domain iv1.unitid.cn 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "SGW to tc-map"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set tc-map-vip_************
  service http
  action permit
 rule name "SGW to appauth-nginx"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set appauth_nginx_************
  service TCP_8501
  service TCP_8502
  service TCP_8503
  action permit
 rule name "SGW to stqd_web"
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set stqd_web_***********
  service http
  action permit
 rule name "SGW to stqd_api"
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set stqd_api_***********
  service http
  action permit
 rule name "SGW to stqd_pic"
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set stqd_pic_***********
  service http
  action permit
 rule name "tc-map-nginx to app"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set tc-map_nginx
  destination-address address-set host_**********
  service TCP_8330
  action permit
 rule name "appauth-nginx to app"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set appauth_nginx_************
  destination-address address-set appauth-app_**********
  destination-address address-set appauth-app_**********
  service appauth-app
  action permit
 rule name "appauth-nginx to internet"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set appauth_nginx_************
  service TCP_2195
  service dns
  profile url-filter "appauth to internet"
  action permit
 rule name "appauth-app to nginx"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set appauth-app_**********
  source-address address-set appauth-app_**********
  destination-address address-set appauth_nginx_************
  service TCP_3128
  action permit
 rule name ShiMingFuWu_API
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set ShiMingFuWu_API
  destination-address address-set ShiMingYanZhen_group
  destination-address address-set host_************
  service TCP_8181
  action permit
 rule name "ShiMingYanZhen to Internet"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ShiMingYanZhen_group
  destination-address address-set ***************
  destination-address address-set **************
  destination-address address-set BaiDu_DNS_************
  destination-address domain-set URL
  service dns
  service http
  service https
  action permit
 rule name "web-nginx to app"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set stqd_web_***********
  source-address address-set stqd_web_nginx
  destination-address address-set STQD_4.24.11.20-28
  destination-address address-set stqd_web_***********
  destination-address address-set stqs_web_3.24.10.231-238
  service TCP_8330
  action permit
 rule name "api-nginx to app"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address *********** mask ***************
  source-address address-set stqd_api_nginx
  destination-address address-set STQD_4.24.11.30-38
  destination-address address-set stqd_api_3.24.10.50-58
  destination-address address-set stqd_api_**********
  service TCP_8330
  action permit
 rule name "pic-nginx to app"
  source-zone trust
  destination-zone untrust
  source-address address-set stqd_pic_nginx
  destination-address address-set stqd_pic_**********
  service http
  action permit
 rule name "api_client to api_nginx"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set api_client
  destination-address address-set api_nginx
  service TCP_8181
  action permit
 rule name "api_nginx to Internet"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set api_nginx
  destination-address address-set BaiDu_DNS_************
  destination-address domain-set URL
  service dns
  service http
  service https
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name app_to_message-nginx
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set STQD_4.24.11.141-144
  source-address address-set STQD_***********-184
  source-address address-set STQD_4.24.11.20-28
  source-address address-set STQD_4.24.11.30-38
  source-address address-set STQD_4.24.11.40-44
  source-address address-set STQD_**********-74
  source-address address-set STQD_**********-78
  destination-address address-set STQD_4.103.12.20-22
  service TCP_12131
  action permit
 rule name message-nginx_to_internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_4.103.12.20-22
  destination-address address-set BaiDu_DNS_************
  destination-address address-set DNS_tuijian
  destination-address domain-set URL_message
  service TCP_12131
  service dns
  service https
  action permit
 rule name auth-nginx_to_internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_4.103.12.130-138
  destination-address address-set BaiDu_DNS_************
  destination-address domain-set URL_auth-true
  service dns
  service https
  action permit
 rule name pic-nginx_to_pic-app
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_***********-82
  destination-address address-set STQD_4.24.11.80-88
  service TCP_10080
  service http
  action permit
 rule name app_to_api-nginx
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set STQD_**********-94
  destination-address address-set STQD_4.103.12.140-142
  service TCP_8181
  service http
  action permit
 rule name "SGW to stqd_net"
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set STQD_**********/24
  service http
  action permit
 rule name nginx_to_auth
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_4.103.12.30-32
  destination-address address-set STQD_4.24.13.10-12
  destination-address address-set STQD_4.24.13.20-22
  service TCP_8000
  service TCP_8330
  action permit
 rule name nginx_to_auth-EFS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_4.103.12.130-138
  destination-address address-set STQD_4.24.13.40-42
  service TCP_7000
  action permit
 rule name app_TO_msg
  source-zone untrust
  destination-zone trust
  source-address address-set ************/24
  source-address address-set STQD_4.24.13.10-12
  destination-address address-set STQD_4.103.12.20-22
  service TCP_12131
  action permit
 rule name app_TO_auth-true
  source-zone untrust
  destination-zone trust
  source-address address-set STQD_4.24.13.10-12
  source-address address-set STQD_4.24.13.20-22
  destination-address address-set STQD_4.103.12.130-138
  service TCP_8181
  action permit
 rule name ops_TO_STQD
  source-zone untrust
  destination-zone trust
  source-address address-set STQD_4.24.13.166/32
  destination-address address-set STQD_**********/24
  service TCP_12131
  service TCP_8330
  service http
  action permit
 rule name SGW_to_STQD-ecnginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address ************ mask ***************
  service http
  action permit
 rule name STQD-ecnginx_to_app
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  destination-address *********** mask ***************
  service TCP_8600
  action permit
 rule name STQD-ecnginx_to_FS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  destination-address *********** mask ***************
  service TCP_20128
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name ECAPP_to_STQD-mail
  policy logging
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  destination-address range ************ ************
  service smtp
  action permit
 rule name STQD-mail_to_internet
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range ************ ************
  service TCP_110
  service TCP_143
  service TCP_1995
  service TCP_43
  service TCP_993
  service TCP_995
  service dns
  service smtp
  action permit
 rule name radius
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_**********/24
  destination-address ********** mask ***************
  service udp-1812
  action permit
 rule name Message-NG_to_SMS-GW
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_4.103.12.20-22
  destination-address address-set host_10.194.119.2
  service http
  action permit
 rule name message-nginx_to_Redis
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_4.103.12.21-22
  destination-address address-set STQD_4.24.11.171-174
  service TCP_26379
  service tcp-6379
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_**********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_**********/24
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* ********** description ShiTiQuDao
#
 sms
#
return
#
switch vsys SJZT 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set net_***********-22 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set net_***********-16 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 *********** mask 32
 address 5 *********** mask 32
 address 6 *********** mask 32
#
ip address-set net_***********-32 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set yybi_*********** type object
 address 0 *********** mask 32
#
ip address-set yyableau_********** type object
 address 0 ********** mask 32
#
ip address-set proxy_**********-31 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set proxy_**********-52 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set net_************ type object
 address 0 ************ mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set net_************ type object
 address 0 ************ mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set net_***********-22 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set net_**********-2 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set net_**********1 type object
 address 0 **********1 mask 32
#
ip address-set **********-63 type object
 address 0 range ********** **********
#
ip address-set net_**********-40 type object
 address 0 range ********** **********
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set SJZT-TOMCAT type object
 address 0 range ********** **********
 address 1 range ********* *********
#
ip address-set G3_************* type object
 address 0 ************* mask 32
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set HUE type object
 address 0 range ********** **********
#
ip address-set net_**********2-13 type object
 address 0 **********2 mask 32
 address 1 **********3 mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set Range_************-57 type group
 address 0 range ************ ************
#
ip address-set Range_************-93 type group
 address 0 range ************ ************
#
ip address-set ***********-14 type group
 address 0 *********** mask 32
 address 1 4.103.19.12 mask 32
 address 2 4.103.19.13 mask 32
 address 3 *********** mask 32
#
ip address-set Range_************-66 type group
 address 0 range ************ 198.3.100.66
#
ip service-set TCP_10050 type object 1094
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1095
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1096
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1097
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1098
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1133
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1134
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1135
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_8080 type object 1216
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_8081 type object 1217
 service 0 protocol tcp source-port 0 to 65535 destination-port 8081
#
ip service-set TCP_9092 type object 1218
 service 0 protocol tcp source-port 0 to 65535 destination-port 9092
#
ip service-set "YZProxy service port" type object 1219
 service 0 protocol tcp source-port 0 to 65535 destination-port 9000 to 9001
 service 1 protocol tcp source-port 0 to 65535 destination-port 9009
 service 2 protocol tcp source-port 0 to 65535 destination-port 3000
 service 3 protocol tcp source-port 0 to 65535 destination-port 1818
 service 4 protocol tcp source-port 0 to 65535 destination-port 8080 to 8083
 service 5 protocol tcp source-port 0 to 65535 destination-port 8888
#
ip service-set tcp-3389 type object 1258
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set tcp-139 type object 1259
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP_1812 type object 1350
 service 0 protocol tcp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_19080 type object 1365
 service 0 protocol tcp destination-port 19080
#
ip service-set TCP_8040-8140 type object 1377
 service 0 protocol tcp source-port 0 to 65535 destination-port 8040 to 8140
#
ip service-set TCP_6677 type object 1412
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1413
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1414
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1415
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1416
 service 0 protocol tcp destination-port 8443
#
ip service-set TCP_30051 type object 1447
 service 0 protocol tcp destination-port 30051
#
ip service-set TCP_8888 type object 1453
 service 0 protocol tcp source-port 0 to 65535 destination-port 8888
#
ip service-set udp-1812 type object 1458
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP-18088 type object 1469
 service 0 protocol tcp destination-port 18088
#
ip service-set TCP_5601 type object 1485
 service 0 protocol tcp destination-port 5601
#
ip service-set TCP_30002 type object 1486
 service 0 protocol tcp destination-port 30002
#
ip service-set TCP-21050 type object 1493
 service 0 protocol tcp destination-port 21050
#
ip service-set TCP-10000 type object 1494
 service 0 protocol tcp destination-port 10000
#
ip service-set TCP-2181 type object 1495
 service 0 protocol tcp destination-port 2181
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.319
 vlan-type dot1q 319
 ip binding vpn-instance SJZT
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.320
 vlan-type dot1q 320
 ip binding vpn-instance SJZT
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if7
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.320
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.319
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set **********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set **********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name "SGW to DATA_NG"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set net_***********-22
  service http
  action permit
 rule name "SGW to SGW to Receive_NG"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set net_***********-16
  service TCP_8080
  action permit
 rule name "SGW to BMS_NG"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set net_***********-32
  service http
  action permit
 rule name "SJZT_BG_NG to YYBI"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set yybi_***********
  service TCP_8080
  action permit
 rule name "SJZT_BG_NG to YYtableau"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set yyableau_**********
  service http
  action permit
 rule name "SJZT_BG_NG to yzproxy"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set proxy_**********-31
  service "YZProxy service port"
  service http
  action permit
 rule name "SJZT_Internet_NG to xwproxy"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-32
  destination-address address-set proxy_**********-52
  service TCP_8080
  service TCP_8081
  service http
  action permit
 rule name "YZ_LOG to xw_log"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set net_************
  source-address address-set net_************
  destination-address address-set net_***********-16
  service ssh
  action permit
 rule name "logcollection to logcache"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-16
  destination-address address-set Range_************-57
  destination-address address-set Range_************-93
  service TCP-18088
  service TCP_9092
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name "SJZT_BG_NG to yzsanbox"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set net_**********-2
  service "YZProxy service port"
  service http
  action permit
 rule name "SJZT_BG_NG to YY_tableau"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set **********-63
  destination-address address-set net_**********1
  service http
  action permit
 rule name YZ_TO_XW_SJZT-SFTP
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set net_**********-40
  destination-address address-set net_***********-16
  service ssh
  action permit
 rule name SJZT_To_Radius
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set host_**********
  service TCP_1812
  service udp-1812
  action permit
 rule name SJZT-FR_To_USAP-NG
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set host_***********
  service TCP_19080
  action permit
 rule name SJZT-FR_To_SJZT-NG
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set host_**********
  service TCP_8080
  action permit
 rule name USAP-NG_To_SJZT
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set ***********-14
  destination-address address-set net_***********-22
  service TCP_8080
  action permit
 rule name SJZT-NG_To_FanRuan
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set host_**********
  service TCP_8080
  action permit
 rule name SJZT-NG_To_TOMCAT
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set SJZT-TOMCAT
  service TCP_8040-8140
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name SJZT_NG-To-G3_HGJG
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set G3_*************
  service TCP_30051
  action permit
 rule name SJZT-NG_To_Tableau
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set host_************
  service TCP_8080
  service http
  service https
  action permit
 rule name SJZT-NG_To_HUE
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set HUE
  service TCP_8888
  action permit
 rule name SJZT_BG_NG_To_tableau
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address address-set net_**********2-13
  service TCP_8080
  service http
  service https
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name SJZT-NG_To_kibana
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  service TCP_5601
  action permit
 rule name SJZT-NG_To_Grafana
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-22
  destination-address ************ mask ***************
  service TCP_30002
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address ********** mask ***************
  service dns
  action permit
 rule name USAP_to_AOMSCLB
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set net_***********-16
  destination-address address-set Range_************-66
  destination-address address-set host_**********
  service TCP-10000
  service TCP-21050
  service TCP-2181
  service TCP_9092
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* ********** description ShuJuZhongTai
#
 sms
#
return
#
switch vsys YZD 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set DaiXiaoZhe_nginx type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set Range_***********-12 type object
 address 0 range *********** ***********
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set Miguan_*********** type object
 address 0 *********** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1070
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1071
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1072
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1073
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1074
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8080 type object 1075
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_8330 type object 1076
 service 0 protocol tcp source-port 0 to 65535 destination-port 8330
#
ip service-set TCP-8890 type object 1136
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1137
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1138
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-139 type object 1260
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1261
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_6677 type object 1397
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1398
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1399
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1400
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1401
 service 0 protocol tcp destination-port 8443
#
ip service-set UDP_514 type object 1489
 service 0 protocol udp source-port 0 to 65535 destination-port 514
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.313
 vlan-type dot1q 313
 ip binding vpn-instance YZD
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.314
 vlan-type dot1q 314
 ip binding vpn-instance YZD
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if8
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.314
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.313
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  disable
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  disable
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service ssh
  action permit
 rule name soc
  disable
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  disable
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  disable
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  disable
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  disable
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  disable
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  disable
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  disable
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  disable
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name DaiXiaoZhe_outside
  disable
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_************
  destination-address address-set host_************
  service TCP_8080
  action permit
 rule name DaiXiaoZhe_API
  disable
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set DaiXiaoZhe_nginx
  destination-address address-set host_**********
  service TCP_8330
  action permit
 rule name NVS
  disable
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name ump
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set host_***********
  service TCP_8080
  action permit
 rule name sso
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set host_***********
  service TCP_8080
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name MiGuan_To_Internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_***********-12
  destination-address ************** mask ***************
  service https
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name Miguan_To_*************
  source-zone trust
  destination-zone untrust
  source-address address-set Miguan_***********
  destination-address address-set *************
  service UDP_514
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* ********** description YeZhuDuan_MiGuan
#
 sms
#
return
#
switch vsys YHLX 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set LX_nginx_IN type object
 address 0 range *********** ***********
#
ip address-set LX_nginx_OUT type object
 address 0 range *********** ***********
#
ip address-set docker_********* type object
 address 0 ********* mask 24
#
ip address-set docker_treafik type object
 address 0 range ********** **********
#
ip address-set host_******* type object
 address 0 ******* mask 32
#
ip address-set ***********-14 type object
 address 0 range *********** ***********
#
ip address-set ***********-4 type object
 address 0 range *********** ***********
#
ip address-set map_********** type object
 address 0 ********** mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set STQD_*********** type object
 address 0 *********** mask 32
#
ip address-set **********-15 type object
 address 0 range ********** **********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set docker_********* type object
 address 0 ********* mask 24
#
ip address-set treafik_**********-24 type object
 address 0 range ********** **********
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1079
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1080
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1081
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1082
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1083
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1139
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1140
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1141
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_18080 type object 1186
 service 0 protocol tcp destination-port 18080
#
ip service-set TCP_28081-28090 type object 1187
 service 0 protocol tcp destination-port 28081 to 28090
#
ip service-set TCP_8330 type object 1214
 service 0 protocol tcp destination-port 8330
#
ip service-set tcp-3389 type object 1262
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set tcp-139 type object 1263
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP_6677 type object 1402
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1403
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1404
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1405
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1406
 service 0 protocol tcp destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.315
 vlan-type dot1q 315
 ip binding vpn-instance YHLX
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.316
 vlan-type dot1q 316
 ip binding vpn-instance YHLX
 ip address *********2 ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if9
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.316
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.315
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain api.weixin.qq.com 
  add domain file.api.weixin.qq.com 
  add domain mp.weixin.qq.com 
  add domain api.mch.weixin.qq.com 
  add domain open.weixin.qq.com 
  add domain www.dh3t.com 
  add domain apis.map.qq.com 
  add domain pnup-hd.lottery-dev.com 
  add domain pnup-hd.tcssyw.com 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name SSL-TO-LX_nginx_IN
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address address-set LX_nginx_IN
  service TCP_18080
  action permit
 rule name LX_nginx_OUT-to-URL
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set LX_nginx_OUT
  destination-address domain-set URL
  service http
  service https
  action permit
 rule name LX_nginx_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set LX_nginx_OUT
  service dns
  action permit
 rule name docker_*********-TO-LX_nginx_OUT
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set docker_*********
  destination-address address-set LX_nginx_OUT
  service TCP_28081-28090
  action permit
 rule name LX_nginx_IN-TO-docker_treafik
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set LX_nginx_IN
  destination-address address-set docker_treafik
  destination-address address-set treafik_**********-24
  service http
  action permit
 rule name *******-TO-LX_nginx_OUT
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*******
  destination-address address-set ***********-14
  service ssh
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ***********-4
  source-address address-set Jenkis
  service ssh
  action permit
 rule name LX_nginx-TO-map
  source-zone trust
  destination-zone untrust
  source-address address-set LX_nginx_OUT
  destination-address address-set map_**********
  service TCP_8330
  action permit
 rule name LX_nginx-TO-STQD
  source-zone trust
  destination-zone untrust
  source-address address-set LX_nginx_OUT
  destination-address ********** mask ***************
  destination-address address-set STQD_***********
  service TCP_8330
  action permit
 rule name Ӫinx
  source-zone untrust
  destination-zone trust
  source-address address-set **********-15
  destination-address address-set LX_nginx_IN
  service TCP_18080
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name Docker-Net_To_LX-Nginx-OUT
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set docker_*********
  destination-address address-set LX_nginx_OUT
  service TCP_28081-28090
  action permit
 rule name Docker-201_TO_LX-Nginx-OUT
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_***********
  destination-address address-set ***********-14
  service ssh
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name G3_to_LX_nginx
  source-zone untrust
  destination-zone trust
  source-address address-set G3_************
  destination-address address-set LX_nginx_IN
  service TCP_18080
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* *********1 description YongHuLaXin
#
 sms
#
return
#
switch vsys TYXXFB 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set XXFB_API_***********-14 type object
 address 0 range *********** ***********
#
ip address-set XXFB_API_********** type object
 address 0 ********** mask 32
#
ip address-set XXFB_seaweed_***********-54 type object
 address 0 range *********** ***********
#
ip address-set XXFB_seaweed_master_**********-13 type object
 address 0 range ********** **********
#
ip address-set XXFB_seaweed_data_**********-24 type object
 address 0 range ********** **********
#
ip address-set XXFB_H5_***********-75 type object
 address 0 range *********** ***********
#
ip address-set XXFB_H5_**********0 type object
 address 0 **********0 mask 32
#
ip address-set SGW_*********** type object
 address 0 *********** mask 24
#
ip address-set XXFB_********** type object
 address 0 ********** mask 24
#
ip address-set XXFB_YODA_**********1-216 type object
 address 0 range **********1 **********6
#
ip address-set XXFB_LOG_***********-32 type object
 address 0 range *********** ***********
#
ip address-set XXFB_bifrost_**********-96 type object
 address 0 range ********** **********
#
ip address-set XXFB_LOG_*********** type object
 address 0 *********** mask 32
#
ip address-set XXFB_*********-22 type object
 address 0 range ********* *********
#
ip address-set XXFB_**********-62 type object
 address 0 range ********** **********
#
ip address-set XXFB_seaweed_*********** type object
 address 0 *********** mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set relay_***********-42 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set live_**********-202 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set Zabbix_***********/24 type object
 address 0 *********** mask 24
#
ip address-set XXFB_*********** type object
 address 0 *********** mask 32
#
ip address-set XXFB_*********** type object
 address 0 *********** mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ***********-18 type object
 address 0 range *********** ***********
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set **********0 type object
 address 0 **********0 mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set nginx_XXFB type object
 address 0 range *********** ***********
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set DaPing_Address type object
 address 0 *********** mask 32
 address 1 range *********** ***********
#
ip address-set Range_************-55 type object
 address 0 range ************ ************
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set ***********-94 type object
 address 0 range *********** ***********
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set **********-14 type group
 address 0 range ********** **********
#
ip service-set TCP_10050 type object 1084
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1085
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1086
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1087
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1088
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1142
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1143
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1144
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP-7070 type object 1188
 service 0 protocol tcp source-port 0 to 65535 destination-port 7070
#
ip service-set TCP_7070 type object 1189
 service 0 protocol tcp source-port 0 to 65535 destination-port 7070
#
ip service-set TCP_17070 type object 1190
 service 0 protocol tcp source-port 0 to 65535 destination-port 17070
#
ip service-set TCP_8080 type object 1191
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_9333 type object 1192
 service 0 protocol tcp source-port 0 to 65535 destination-port 9333
#
ip service-set TCP_19333 type object 1193
 service 0 protocol tcp source-port 0 to 65535 destination-port 19333
#
ip service-set TCP_26379 type object 1213
 service 0 protocol tcp destination-port 26379
#
ip service-set TCP_6379 type object 1215
 service 0 protocol tcp source-port 0 to 65535 destination-port 6379
#
ip service-set TCP_1935 type object 1220
 service 0 protocol tcp destination-port 1935
#
ip service-set tcp-139 type object 1264
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1265
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set ********** type object 1328
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_5671 type object 1329
 service 0 protocol tcp destination-port 5671
#
ip service-set TCP_11666 type object 1330
 service 0 protocol tcp destination-port 11666
#
ip service-set TCP_30201 type object 1331
 service 0 protocol tcp destination-port 30201
#
ip service-set TCP-11671 type object 1332
 service 0 protocol tcp source-port 0 to 65535 destination-port 11671
#
ip service-set TCP_30202 type object 1347
 service 0 protocol tcp destination-port 30202
#
ip service-set TCP_30203 type object 1366
 service 0 protocol tcp destination-port 30203
#
ip service-set TCP_6677 type object 1407
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1408
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1409
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1410
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1411
 service 0 protocol tcp destination-port 8443
#
ip service-set TCP_30204 type object 1466
 service 0 protocol tcp destination-port 30204
#
ip service-set TCP-30205 type object 1487
 service 0 protocol tcp destination-port 30205
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.317
 vlan-type dot1q 317
 ip binding vpn-instance TYXXFB
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.318
 vlan-type dot1q 318
 ip binding vpn-instance TYXXFB
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if10
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.318
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.317
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain cdnapi.cu-cdn.com 
  add domain cdn.aliyuncs.com 
 domain-set name XXFBURL 
  add domain api.performfeeds.com 
  add domain api.betradar.com 
  add domain api.ap-northeast-1.betradar.com 
  add domain api.stats.com 
  add domain mq.betradar.com 
  add domain mq.ap-northeast-1.betradar.com 
  add domain football.api.press.net 
  add domain feed.datafactory.la 
  add domain global.api.betradar.com 
  add domain global.mq.betradar.com 
  add domain xml.donbest.com 
  add domain footballteamnews.com 
  add domain www.footballteamnews.com 
 domain-set name RTMPURL 
  add domain rtmp-cloud.lottery-it.com 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_***********/24
  service TCP-7070
  service TCP_10050
  service http
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name "SGW to XXFB"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address address-set XXFB_**********
  service http
  action permit
 rule name XXFB_API
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-18
  source-address address-set XXFB_API_***********-14
  destination-address address-set ***********
  destination-address address-set **********0
  destination-address address-set ***********
  destination-address address-set ***********
  destination-address address-set XXFB_API_**********
  service TCP_8080
  action permit
 rule name XXFB_seaweed_dmz-to-core-master
  source-zone trust
  destination-zone untrust
  source-address address-set XXFB_seaweed_***********-54
  destination-address address-set XXFB_seaweed_master_**********-13
  service TCP_17070
  service TCP_7070
  action permit
 rule name XXFB_seaweed_dmz-to-core-data
  source-zone trust
  destination-zone untrust
  source-address address-set XXFB_seaweed_***********-54
  destination-address address-set XXFB_seaweed_data_**********-24
  service TCP_19333
  service TCP_9333
  action permit
 rule name XXFB_seaweed_core-data-to-dmz
  source-zone untrust
  destination-zone trust
  source-address address-set **********-14
  source-address address-set XXFB_seaweed_data_**********-24
  destination-address address-set XXFB_seaweed_***********-54
  service TCP_17070
  service TCP_7070
  action permit
 rule name XXFB_H5
  source-zone trust
  destination-zone untrust
  source-address address-set XXFB_H5_***********-75
  destination-address address-set XXFB_H5_**********0
  service TCP_8080
  action permit
 rule name XXFB_YODA-TO-XXFB_LOG
  source-zone untrust
  destination-zone trust
  source-address address-set XXFB_YODA_**********1-216
  source-address address-set XXFB_bifrost_**********-96
  destination-address address-set XXFB_LOG_***********
  destination-address address-set XXFB_LOG_***********-32
  service http
  action permit
 rule name XXFB_LOG-TO-DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set XXFB_LOG_***********-32
  service dns
  action permit
 rule name XXFB_LOG-TO-CDN
  source-zone trust
  destination-zone untrust
  source-address address-set XXFB_LOG_***********-32
  destination-address domain-set URL
  service http
  service https
  action permit
 rule name XXFB_seaweed-TO-redis
  source-zone trust
  destination-zone untrust
  source-address address-set XXFB_seaweed_***********-54
  destination-address address-set XXFB_**********-62
  service TCP_26379
  service TCP_6379
  action permit
 rule name XXFB_DMZ-TO-seaweed
  source-zone untrust
  destination-zone trust
  source-address address-set XXFB_*********-22
  destination-address address-set XXFB_seaweed_***********
  destination-address address-set XXFB_seaweed_***********-54
  service TCP_8080
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name XXFB_relay-TO-live
  source-zone trust
  destination-zone untrust
  source-address address-set relay_***********-42
  destination-address address-set live_**********-202
  service TCP_1935
  action permit
 rule name CDN-TO-rtmp
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set XXFB_***********
  service TCP_1935
  action permit
 rule name CDN-TO-tstatic
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set ***********
  destination-address address-set XXFB_***********
  service http
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name radius  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set XXFB_**********
  destination-address ********** mask ***************
  service radius
  action permit
 rule name nginx_XXFB-to-XXFBURL
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set nginx_XXFB
  destination-address domain-set XXFBURL
  service TCP_5671
  service http
  service https
  action permit
 rule name nginx_XXFB-to-DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set nginx_XXFB
  service dns
  action permit
 rule name G3_to_XXFB
  source-zone untrust
  destination-zone trust
  source-address address-set G3_************
  destination-address address-set ***********
  service TCP-11671
  service TCP-30205
  service TCP_11666
  service TCP_30201
  service TCP_30202
  service TCP_30203
  service TCP_30204
  service TCP_5671
  action permit
 rule name XXFB_relay-TO-DNS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set relay_***********-42
  service dns
  action permit
 rule name XXFB_relay-TO-rtmp
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set relay_***********-42
  destination-address domain-set RTMPURL
  service http
  action permit
 rule name DaPing-TO-rtmp
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set DaPing_Address
  destination-address address-set XXFB_***********
  service http
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name Monitor_to_XXFB
  source-zone untrust
  destination-zone trust
  source-address address-set Range_************-55
  destination-address address-set ***********
  service TCP_30203
  action permit
 rule name kfpt_to_hulianwang
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-94
  destination-address address-set **********
  service TCP_8080
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********5
ip route-static ********** ************* *********9 description TongYi_XinXiFaBu
#
 sms
#
return
#
switch vsys KFPT 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ************
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 *********** mask 24
 address 3 ************8 mask 32
 address 4 range ************ ************
 address 5 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set net_*********/24 type object
 description G3_YingXiaoZhongXin
 address 0 ********* mask 24
#
ip address-set host_***********0 type object
 description G3_KaiFangPingTai_Nginx
 address 0 ***********0 mask 32
#
ip address-set G3_KaiFangPingTai_Nginx type object
 address 0 ***********1 mask 32
 address 1 ***********2 mask 32
 address 2 ***********0 mask 32
#
ip address-set group_**********-74 type object
 description TiCaiGuanJia
 address 0 range ********** **********
#
ip address-set nginx_***********1-202 type object
 address 0 ***********1 mask 32
 address 1 ***********2 mask 32
#
ip address-set _************* type object
 address 0 ************* mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set SFTP_************ type object
 address 0 ************ mask 32
#
ip address-set ********/24 type object
 address 0 ******** mask 24
#
ip address-set ************/32 type object
 address 0 ************ mask 32
#
ip address-set ************/32 type object
 address 0 ************ mask 32
#
ip address-set ************/32 type object
 address 0 ************ mask 32
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set XXFB_bifrost_**********-96 type object
 address 0 range ********** **********
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set *********-152 type object
 address 0 range ********* **********
#
ip address-set *********-47 type object
 address 0 range ********* *********
#
ip address-set **********-88 type object
 address 0 range ********** **********
#
ip address-set **********-38 type object
 address 0 range ********** **********
#
ip address-set **********-44 type object
 address 0 range ********** **********
#
ip address-set ***********0-202 type object
 address 0 range ***********0 ***********2
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set **********-24 type object
 address 0 range ********** **********
#
ip address-set nginx_**********-15 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 3 ********** mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set group_**********-78 type object
 description STQD_DXZJK
 address 0 range ********** **********
#
ip address-set **********-28 type object
 address 0 range ********** **********
#
ip address-set net_**********/16 type object
 address 0 ********** mask 16
#
ip address-set ***********-184 type object
 address 0 range *********** ***********
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set KFPT_***********-22 type object
 address 0 range *********** ***********
#
ip address-set G3_************ type object
 address 0 ************ mask 32
#
ip address-set Range_************-55 type object
 address 0 range ************ ************
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set css type object
 address 0 range *********86 *********88
#
ip address-set css_api type object
 address 0 range *********81 *********83
#
ip address-set ************** type object
 address 0 ************** mask 32
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set ************-98 type object
 address 0 range ************ ************
#
ip address-set **********-50 type object
 address 0 range ********** **********
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set *********-12 type object
 address 0 range ********* *********
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set *********/24 type object
 address 0 ********* mask 24
#
ip service-set TCP_10050 type object 1053
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1054
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1055
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1056
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1057
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_11666-11670 type object 1058
 service 0 protocol tcp source-port 0 to 65535 destination-port 11666
 service 1 protocol tcp source-port 0 to 65535 destination-port 11667
 service 2 protocol tcp source-port 0 to 65535 destination-port 11668
 service 3 protocol tcp source-port 0 to 65535 destination-port 11669
 service 4 protocol tcp source-port 0 to 65535 destination-port 11670
#
ip service-set TCP_12001 type object 1090
 service 0 protocol tcp source-port 0 to 65535 destination-port 12001
#
ip service-set TCP-4321 type object 1091
 service 0 protocol tcp source-port 0 to 65535 destination-port 4321
#
ip service-set TCP_30000 type object 1108
 service 0 protocol tcp destination-port 30000
#
ip service-set tcp-9999 type object 1109
 service 0 protocol tcp source-port 0 to 65535 destination-port 9999
#
ip service-set tcp-55382 type object 1110
 service 0 protocol tcp source-port 0 to 65535 destination-port 55382
#
ip service-set tcp-8443 type object 1111
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set tcp-8090 type object 1112
 service 0 protocol tcp source-port 0 to 65535 destination-port 8090
#
ip service-set tcp-30000 type object 1113
 service 0 protocol tcp source-port 0 to 65535 destination-port 30000
#
ip service-set tcp-1001-1005 type object 1114
 service 0 protocol tcp source-port 0 to 65535 destination-port 1001 to 1005
#
ip service-set TCP-8890 type object 1145
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1146
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1147
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP-11671 type object 1157
 service 0 protocol tcp source-port 0 to 65535 destination-port 11671
#
ip service-set tcp-30101 type object 1199
 service 0 protocol tcp source-port 0 to 65535 destination-port 30101
#
ip service-set tcp-30103 type object 1201
 service 0 protocol tcp source-port 0 to 65535 destination-port 30103
#
ip service-set tcp-30102 type object 1211
 service 0 protocol tcp source-port 0 to 65535 destination-port 30102
#
ip service-set tcp-30104 type object 1212
 service 0 protocol tcp source-port 0 to 65535 destination-port 30104
#
ip service-set tcp-31002 type object 1227
 service 0 protocol tcp source-port 0 to 65535 destination-port 31002
#
ip service-set TCP_7001 type object 1228
 service 0 protocol tcp destination-port 7001
#
ip service-set TCP_31003 type object 1238
 service 0 protocol tcp destination-port 31003
#
ip service-set tcp-30105 type object 1240
 service 0 protocol tcp source-port 0 to 65535 destination-port 30105
#
ip service-set tcp-3389 type object 1266
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set tcp-139 type object 1267
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP_31002 type object 1278
 service 0 protocol tcp destination-port 31002
#
ip service-set TCP_31101 type object 1289
 service 0 protocol tcp destination-port 31101
#
ip service-set tcp-31004 type object 1316
 service 0 protocol tcp source-port 0 to 65535 destination-port 31004
#
ip service-set tcp-31005 type object 1317
 service 0 protocol tcp source-port 0 to 65535 destination-port 31005
#
ip service-set tcp-8743 type object 1318
 service 0 protocol tcp source-port 0 to 65535 destination-port 8743
#
ip service-set TCP_30201 type object 1319
 service 0 protocol tcp destination-port 30201
#
ip service-set TCP_30000-31000 type object 1320
 service 0 protocol tcp source-port 0 to 65535 destination-port 30000 to 31000
#
ip service-set TCP_30108 type object 1322
 service 0 protocol tcp destination-port 30108
#
ip service-set TCP_30109 type object 1325
 service 0 protocol tcp destination-port 30109
#
ip service-set TCP_5017 type object 1326
 service 0 protocol tcp destination-port 5017
#
ip service-set TCP_31201 type object 1334
 service 0 protocol tcp destination-port 31201
#
ip service-set TCP_10031 type object 1341
 service 0 protocol tcp source-port 0 to 65535 destination-port 10031
#
ip service-set TCP_33000 type object 1348
 service 0 protocol tcp destination-port 33000
#
ip service-set TCP_11669-11670 type object 1356
 service 0 protocol tcp source-port 0 to 65535 destination-port 11669 to 11670
#
ip service-set TCP_10032 type object 1357
 service 0 protocol tcp source-port 0 to 65535 destination-port 10032
#
ip service-set TCP_31202 type object 1362
 service 0 protocol tcp destination-port 31202
#
ip service-set TCP_31301 type object 1372
 service 0 protocol tcp destination-port 31301
#
ip service-set TCP_10024 type object 1379
 service 0 protocol tcp destination-port 10024
#
ip service-set TCP_10029 type object 1380
 service 0 protocol tcp destination-port 10029
#
ip service-set TCP_10030 type object 1381
 service 0 protocol tcp destination-port 10030
#
ip service-set TCP_32000 type object 1382
 service 0 protocol tcp destination-port 32000
#
ip service-set TCP_6677 type object 1393
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1394
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1395
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1396
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_11668 type object 1441
 service 0 protocol tcp destination-port 11668
#
ip service-set TCP_30203 type object 1442
 service 0 protocol tcp destination-port 30203
#
ip service-set TCP_10033 type object 1445
 service 0 protocol tcp destination-port 10033
#
ip service-set TCP_8083 type object 1449
 service 0 protocol tcp destination-port 8083
#
ip service-set TCP_32201 type object 1450
 service 0 protocol tcp destination-port 32201
#
ip service-set TCP_8084 type object 1451
 service 0 protocol tcp destination-port 8084
#
ip service-set TCP_32202 type object 1452
 service 0 protocol tcp destination-port 32202
#
ip service-set udp-1812 type object 1456
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_31302 type object 1461
 service 0 protocol tcp source-port 0 to 65535 destination-port 31302
#
ip service-set TCP-18081 type object 1462
 service 0 protocol tcp source-port 0 to 65535 destination-port 18081
#
ip service-set TCP_10034 type object 1464
 service 0 protocol tcp destination-port 10034
#
ip service-set TCP_8802 type object 1465
 service 0 protocol tcp destination-port 8802
#
ip service-set TCP_10035 type object 1492
 service 0 protocol tcp destination-port 10035
#
ip service-set TCP_20001 type object 1515
 service 0 protocol tcp destination-port 20001
#
ip service-set TCP_10036 type object 1517
 service 0 protocol tcp destination-port 10036
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
acl number 3000
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip source ***********1 0 
 rule 1 permit ip destination ***********1 0 
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.311
 vlan-type dot1q 311
 ip binding vpn-instance KFPT
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.312
 vlan-type dot1q 312
 ip binding vpn-instance KFPT
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if11
#
profile type url-filter name G3_KaiFangPingTai
 add whitelist url api.performfeeds.com
 add whitelist url inforequest.sporttery.cn
 category pre-defined control-level low
 https-filter enable
 whitelist-only enable
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.312
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.311
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain api.performfeeds.com 
  add domain inforequest.sporttery.cn 
  add domain api.performfeeds.com.cn 
  add domain open.100x100w.com 
  add domain openapi.caiyang.com.cn 
  add domain feiyu.lianhaikeji.com 
  add domain api.betradar.com 
  add domain api.ap-southeast-1.betradar.com 
  add domain api.ap-northeast-1.betradar.com 
  add domain api.unicompayment.com 
  add domain cpcouponapi.hljtyy.com 
  add domain open.suning.com 
  add domain activity-openapi.agtech-develop.cn 
  add domain act.gdlottery.cn 
  add domain sdk.oss.smartcert.cn 
  add domain iv.unitid.cn 
  add domain bizapi.jd.com 
  add domain gl.tyj.zj.gov.cn 
  add domain device.api.ct10649.com 
  add domain api.map.baidu.com 
  add domain api.stats.com 
  add domain user.chinayanghe.com 
  add domain www.dh3t.com 
  add domain yzd.learning.lottery.gov.cn 
  add domain api.smsxy.com 
  add domain qyapi.weixin.qq.com 
  add domain pmallapi.dui1dui.com 
  add domain gw.quancangyun.cn 
  add domain api.huigongkezhi.cn 
  add domain cslottery.hui10.com 
  add domain www.umyun.com 
  add domain ticai.bjgzc.com 
  add domain ticaihx.bjgzc.com 
  add domain www.jumdata.com 
  add domain cp.gw.96rg.cn 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 default session logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "permit DNS"
  source-zone trust
  destination-zone untrust
  source-address address-set ************/32
  source-address address-set ************/32
  source-address address-set G3_KaiFangPingTai_Nginx
  service dns
  service https
  action permit
 rule name "G3_YingXiao to G3_KaiFang_Nginx"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set net_*********/24
  destination-address address-set host_***********0
  service TCP-11671
  service TCP_10031
  service TCP_11666-11670
  service TCP_30201
  service tcp-30101
  service tcp-30103
  service tcp-30104
  action permit
 rule name "Nginx to Internet"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set G3_KaiFangPingTai_Nginx
  destination-address ************** mask ***************
  destination-address address-set _*************
  destination-address domain-set URL
  service TCP-18081
  service TCP-4321
  service TCP_5017
  service http
  service https
  service tcp-8743
  action permit
 rule name TiCaiGuanJia
  source-zone untrust
  destination-zone trust
  source-address address-set group_**********-74
  destination-address address-set host_***********0
  destination-address address-set nginx_***********1-202
  service TCP_12001
  action permit
 rule name "internet to SFTP"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SFTP_************
  service TCP_30000
  action permit
 rule name "ZFK8S to nginx"
  source-zone untrust
  destination-zone trust
  source-address address-set ************/24
  source-address address-set ********/24
  destination-address address-set ************/32
  service http
  service https
  service tcp-1001-1005
  service tcp-30000
  service tcp-55382
  service tcp-8090
  service tcp-8443
  service tcp-9999
  action permit
 rule name "ZF Nginx to Internet"
  source-zone trust
  destination-zone untrust
  source-address address-set ************/32
  source-address address-set ************/32
  service http
  service https
  service tcp-30000
  service tcp-55382
  service tcp-8090
  service tcp-8443
  service tcp-9999
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name XXFB-TO-KFPT_SFTP
  source-zone untrust
  destination-zone trust
  source-address address-set **********-24
  source-address address-set XXFB_bifrost_**********-96
  destination-address address-set SFTP_************
  service tcp-30000
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name ƽ̨

               source-zone untrust
  destination-zone trust
  source-address address-set *********-47
  destination-address ***********0 mask ***************
  service tcp-30102
  action permit
 rule name ʵ̨nginx
  source-zone untrust
  destination-zone trust
  source-address ********** mask ***************
  source-address address-set **********-88
  source-address address-set **********-28
  source-address address-set **********-38
  source-address address-set **********-44
  destination-address address-set ***********0-202
  service tcp-31002
  action permit
 rule name SGW_to_KFPT-openapi
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Range_************-55
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service TCP_7001
  action permit
 rule name nginx_to_apiserver
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ********** mask ***************
  service TCP_7001
  action permit
 rule name STQD-GM_to_KFPT-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  destination-address address-set ***********0-202
  service TCP_31003
  action permit
 rule name YXZX-NG_TO_KFPT-NG
  source-zone untrust
  destination-zone trust
  source-address address-set nginx_**********-15
  destination-address address-set ***********0-202
  service TCP_10024
  service TCP_10029
  service TCP_10030
  service TCP_10032
  service TCP_10033
  service TCP_30108
  service TCP_30109
  service tcp-30102
  service tcp-30105
  service tcp-31004
  service tcp-31005
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name STQD-DXZJK_to_KFPT-nginx
  source-zone untrust
  destination-zone trust
  source-address address-set group_**********-78
  source-address range ***********1 ***********2
  destination-address address-set ***********0-202
  service TCP_12001
  service TCP_31002
  action permit
 rule name SJZT_to_KFPT-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  destination-address address-set ***********0-202
  service TCP_31101
  action permit
 rule name G3YPT_to_KFPT-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ************-98
  source-address address-set net_**********/16
  destination-address address-set host_***********0
  service TCP_20001
  service TCP_30000-31000
  service TCP_31002
  service TCP_31201
  service TCP_31202
  service TCP_31301
  service TCP_31302
  service TCP_32000
  action permit
 rule name STQD_to_KFPT-NG
  source-zone untrust
  destination-zone trust
  source-address address-set ***********-184
  destination-address address-set ***********0-202
  service TCP_31002
  action permit
 rule name KFPT-NG_to_clb-pt-oap
  source-zone trust
  destination-zone untrust
  source-address address-set KFPT_***********-22
  destination-address address-set G3_************
  service http
  action permit
 rule name UMP_to_KFPT-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********-24
  source-address address-set css
  source-address address-set css_api
  destination-address address-set host_***********0
  service TCP_33000
  action permit
 rule name Monitor_to_KFPT-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Range_************-55
  destination-address address-set ***********0-202
  destination-address address-set host_***********0
  service TCP_11668
  service TCP_11669-11670
  service TCP_12001
  service TCP_30108
  service TCP_30203
  service tcp-30101
  service tcp-30102
  service tcp-30103
  service tcp-30104
  service tcp-31004
  service tcp-31005
  action permit
 rule name G3-KFPT-NG_To_CCRS
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set G3_KaiFangPingTai_Nginx
  destination-address ************ mask ***************
  service TCP-8890
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service http
  service tcp-8443
  action permit
 rule name G3_to_KFPT
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set G3_************
  destination-address address-set host_***********0
  service TCP_10024
  service TCP_10029
  service TCP_10030
  service TCP_10031
  service TCP_10032
  service TCP_10033
  service TCP_10034
  service TCP_10035
  service TCP_10036
  service TCP_11666-11670
  service TCP_30108
  service TCP_30109
  service tcp-30101
  service tcp-30102
  service tcp-30103
  service tcp-30104
  service tcp-30105
  action permit
 rule name kfpt_to_nginx
  source-zone untrust
  destination-zone trust
  source-address address-set *********/24
  source-address address-set G3_************
  destination-address address-set G3_KaiFangPingTai_Nginx
  service TCP_32201
  service TCP_32202
  action permit
 rule name Nginx_to_JuncaiInternet
  source-zone trust
  destination-zone untrust
  source-address address-set G3_KaiFangPingTai_Nginx
  destination-address ************** mask ***************
  service TCP_32201
  service TCP_8083
  service TCP_8084
  action permit
 rule name radius
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set **********
  service udp-1812
  action permit
 rule name G3-KFPT-NG_To_QYHG
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set G3_KaiFangPingTai_Nginx
  destination-address ************** mask ***************
  service TCP_8802
  action permit
 rule name WXGGH-TO-KFPT_SFTP
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  source-address address-set ***********
  source-address address-set **********-50
  destination-address address-set SFTP_************
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name BlackBox_TO-NG
  source-zone untrust
  destination-zone trust
  source-address address-set *********-12
  destination-address address-set ***********0-202
  service TCP_8083
  action permit
 rule name boce_to_KFPT-nginx
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  destination-address address-set nginx_***********1-202
  service TCP_11668
  service TCP_30108
  service TCP_31003
  service tcp-30102
  service tcp-30104
  action permit
 rule name 20231207_nginx_to_kfpt
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service http
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* ********** description G3_KaiFangPingTai
#
 sms
#
return
#
switch vsys CSLP 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ildg_vip_*********** type object
 address 0 *********** mask 32
#
ip address-set ildg_***********-12 type object
 address 0 *********** mask 32
 address 1 4.103.18.11 mask 32
 address 2 4.103.18.12 mask 32
#
ip address-set ildg_web_4.20.50.31-32 type object
 address 0 4.20.50.31 mask 32
 address 1 4.20.50.32 mask 32
#
ip address-set cslp_ctrix_3.26.10.101-104 type object
 address 0 3.26.10.101 mask 32
 address 1 3.26.10.102 mask 32
 address 2 3.26.10.103 mask 32
 address 3 3.26.10.104 mask 32
#
ip address-set cslp_tool_********** type object
 address 0 ********** mask 32
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1099
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1100
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1101
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1102
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1103
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8010 type object 1104
 service 0 protocol tcp destination-port 8010
#
ip service-set TCP-8890 type object 1148
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1149
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1150
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-139 type object 1268
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1269
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_6677 type object 1417
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1418
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1419
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1420
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1421
 service 0 protocol tcp destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.321
 vlan-type dot1q 321
 ip binding vpn-instance CSLP
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.322
 vlan-type dot1q 322
 ip binding vpn-instance CSLP
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if12
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.322
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.321
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set **********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set **********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "SGW to cslp_ildg"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  destination-address address-set ildg_vip_***********
  service TCP_8010
  action permit
 rule name "cslp_tool to cslp_ildg"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set cslp_tool_**********
  destination-address address-set ildg_***********-12
  destination-address range *********** ***********
  service TCP_8010
  action permit
 rule name "cslp_ctrix to cslp_ildg"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set cslp_ctrix_3.26.10.101-104
  destination-address address-set ildg_***********-12
  destination-address range *********** ***********
  service ssh
  action permit
 rule name "ildg to web"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ildg_***********-12
  source-address range *********** ***********
  destination-address address-set host_**********
  destination-address address-set ildg_web_4.20.50.31-32
  service TCP_8010
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* ********** description YinWuGongSi
#
 sms
#
return
#
switch vsys USAP 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set soc_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set USAP_nginx_WX type object
 address 0 range *********** ***********
#
ip address-set USAP_nginx_Internet type object
 address 0 range *********** ***********
#
ip address-set USAP_Service type object
 address 0 range ********** **********
#
ip address-set USAP_nginx_Internet_VIP type object
 address 0 *********** mask 32
#
ip address-set **********-74 type object
 address 0 range ********** **********
#
ip address-set SGW_*********** type object
 address 0 *********** mask 24
#
ip address-set USAP_********** type object
 address 0 ********** mask 24
#
ip address-set **********-47 type object
 address 0 range ********** **********
#
ip address-set *********-9 type object
 address 0 range ********* *********
#
ip address-set "inside systems" type object
 address 0 range ********* *********
 address 1 range ********** **********
 address 2 range ********** **********
 address 3 range **********1 **********3
 address 4 range ********** **********
 address 5 *********** mask 32
 address 6 range *********** ***********
 address 7 range ************ ************
 address 8 ********** mask 32
 address 9 ********** mask 32
 address 10 range ********* *********
 address 11 range ********** **********
 address 12 range ***********1 ***********2
 address 13 range *********** ***********
 address 14 **********1 mask 32
 address 15 range *********** ***********
 address 16 *********** mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set XXFB_**********0-133 type object
 address 0 range **********0 **********3
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set nginx_**********-15 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 3 ********** mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set **********-63 type object
 address 0 range ********** **********
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set G3_************* type object
 address 0 ************* mask 32
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set G3_************* type object
 address 0 ************* mask 32
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set Host_4.60.8.5 type object
 address 0 4.60.8.5 mask 32
#
ip address-set host_4.14.100.53 type object
 address 0 4.14.100.53 mask 32
#
ip address-set host_4.24.11.180 type object
 address 0 4.24.11.180 mask 32
#
ip address-set STQD_***********-184 type object
 address 0 range *********** ***********
#
ip address-set STQD_**********-28 type object
 address 0 range ********** **********
#
ip address-set STQD_**********-38 type object
 address 0 range ********** **********
#
ip address-set STQD_**********-44 type object
 address 0 range ********** **********
#
ip address-set G3_10.194.120.76 type object
 address 0 10.194.120.76 mask 32
#
ip address-set G3_10.194.119.11 type object
 address 0 10.194.119.11 mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set net_**********/24 type object
 address 0 ********** mask 24
#
ip address-set CSLJC_*********** type object
 address 0 *********** mask 32
#
ip address-set STQD_4.24.11.11-14 type object
 address 0 range 4.24.11.11 4.24.11.14
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set ZGWS_*********-4 type object
 address 0 range ********* 3.22.10.4
#
ip address-set host_4.190.166.2 type object
 address 0 4.190.166.2 mask 32
#
ip address-set host_10.194.123.17 type object
 address 0 10.194.123.17 mask 32
#
ip address-set host_10.194.123.14 type object
 address 0 10.194.123.14 mask 32
#
ip address-set Node_************ type object
 address 0 ************ mask 24
#
ip address-set net_***********-22 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set JKED_F5_VIP type object
 address 0 range 4.13.70.111 4.13.70.113
#
ip address-set JKED_Internet type object
 address 0 range 4.13.70.41 4.13.70.43
 address 1 range 4.13.70.81 4.13.70.82
 address 2 range 4.20.70.101 4.20.70.102
 address 3 range 4.13.70.101 4.13.70.102
#
ip address-set host_4.13.70.32 type object
 address 0 4.13.70.32 mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set **********-52 type object
 address 0 range ********** 4.20.50.52
#
ip address-set 4.13.20.40-42 type object
 address 0 range 4.13.20.40 4.13.20.42
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_10.194.123.48 type object
 address 0 10.194.123.48 mask 32
#
ip address-set G3_10.194.123.22 type object
 address 0 10.194.123.22 mask 32
#
ip address-set G3_10.194.123.30 type object
 address 0 10.194.123.30 mask 32
#
ip address-set G3_10.194.123.35 type object
 address 0 10.194.123.35 mask 32
#
ip address-set ************-55 type object
 address 0 range ************ ************
#
ip address-set G3_10.194.123.26 type object
 address 0 10.194.123.26 mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set **********-12 type object
 address 0 range ********** **********
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set tableau type object
 address 0 range **********4 **********5
 address 1 **********6 mask 32
#
ip address-set 10.196.71.0/24 type object
 address 0 10.196.71.0 mask 24
#
ip address-set ***********-14 type group
 address 0 *********** mask 32
 address 1 4.103.19.12 mask 32
 address 2 4.103.19.13 mask 32
 address 3 *********** mask 32
#
ip address-set ***********-14 type group
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 4.103.19.12 mask 32
 address 3 4.103.19.13 mask 32
 address 4 *********** mask 32
#
ip address-set ***********-104 type group
 address 0 range *********** ***********
#
ip address-set *************-253 type group
 address 0 range ************* 4.190.166.253
#
ip service-set TCP_10050 type object 1158
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1159
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1160
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1161
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1162
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8890 type object 1163
 service 0 protocol tcp destination-port 8890
#
ip service-set TCP_8891 type object 1164
 service 0 protocol tcp destination-port 8891
#
ip service-set TCP_8999 type object 1165
 service 0 protocol tcp destination-port 8999
#
ip service-set tcp-7004 type object 1194
 service 0 protocol tcp source-port 0 to 65535 destination-port 7004
#
ip service-set TCP_10080 type object 1195
 service 0 protocol tcp destination-port 10080
#
ip service-set TCP_9080 type object 1196
 service 0 protocol tcp destination-port 9080
#
ip service-set TCP_10081 type object 1197
 service 0 protocol tcp destination-port 10081
#
ip service-set tcp-18080 type object 1202
 service 0 protocol tcp source-port 0 to 65535 destination-port 18080
#
ip service-set tcp-8081 type object 1203
 service 0 protocol tcp source-port 0 to 65535 destination-port 8081
#
ip service-set tcp-8089 type object 1204
 service 0 protocol tcp source-port 0 to 65535 destination-port 8089
#
ip service-set tcp-8080 type object 1205
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set tcp-54106 type object 1206
 service 0 protocol tcp source-port 0 to 65535 destination-port 54106
#
ip service-set tcp-9090 type object 1207
 service 0 protocol tcp source-port 0 to 65535 destination-port 9090
#
ip service-set tcp-8087 type object 1208
 service 0 protocol tcp source-port 0 to 65535 destination-port 8087
#
ip service-set tcp-8001 type object 1209
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set tcp-19080 type object 1210
 service 0 protocol tcp source-port 0 to 65535 destination-port 19080
#
ip service-set TCP_10082 type object 1226
 service 0 protocol tcp source-port 0 to 65535 destination-port 10082
#
ip service-set TCP_7001 type object 1239
 service 0 protocol tcp source-port 0 to 65535 destination-port 7001
#
ip service-set tcp-139 type object 1270
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1271
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP-7004 type object 1279
 service 0 protocol tcp source-port 0 to 65535 destination-port 7004
#
ip service-set TCP_8800 type object 1311
 service 0 protocol tcp destination-port 8800
#
ip service-set TCP_19080 type object 1312
 service 0 protocol tcp destination-port 19080
#
ip service-set TCP_8080 type object 1314
 service 0 protocol tcp destination-port 8080
#
ip service-set TCP_30001 type object 1323
 service 0 protocol tcp destination-port 30001
#
ip service-set TCP_30010 type object 1324
 service 0 protocol tcp destination-port 30010
#
ip service-set TCP_30080 type object 1333
 service 0 protocol tcp destination-port 30080
#
ip service-set TCP_8330 type object 1340
 service 0 protocol tcp source-port 0 to 65535 destination-port 8330
#
ip service-set TCP_8088 type object 1354
 service 0 protocol tcp source-port 0 to 65535 destination-port 8088
#
ip service-set TCP_6002 type object 1359
 service 0 protocol tcp destination-port 6002
#
ip service-set TCP_8021 type object 1360
 service 0 protocol tcp destination-port 8021
#
ip service-set TCP_30000 type object 1363
 service 0 protocol tcp destination-port 30000
#
ip service-set TCP_30500 type object 1364
 service 0 protocol tcp destination-port 30500
#
ip service-set TCP_8010 type object 1368
 service 0 protocol tcp destination-port 8010
#
ip service-set TCP-8090 type object 1369
 service 0 protocol tcp destination-port 8090
#
ip service-set TCP_30501 type object 1371
 service 0 protocol tcp destination-port 30501
#
ip service-set TCP_30220 type object 1374
 service 0 protocol tcp destination-port 30220
#
ip service-set TCP_30011 type object 1375
 service 0 protocol tcp destination-port 30011
#
ip service-set TCP_30221 type object 1376
 service 0 protocol tcp destination-port 30221
#
ip service-set TCP_30222 type object 1378
 service 0 protocol tcp destination-port 30222
#
ip service-set TCP_6677 type object 1422
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1423
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8002 type object 1424
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1425
 service 0 protocol tcp destination-port 8443
#
ip service-set TCP_30017 type object 1443
 service 0 protocol tcp destination-port 30017
#
ip service-set TCP_30223 type object 1444
 service 0 protocol tcp destination-port 30223
#
ip service-set TCP_30058 type object 1446
 service 0 protocol tcp destination-port 30058
#
ip service-set TCP_30224 type object 1448
 service 0 protocol tcp destination-port 30224
#
ip service-set TCP_30225 type object 1454
 service 0 protocol tcp source-port 0 to 65535 destination-port 30225
#
ip service-set TCP_30226 type object 1455
 service 0 protocol tcp source-port 0 to 65535 destination-port 30226
#
ip service-set udp-1812 type object 1457
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_30023 type object 1459
 service 0 protocol tcp source-port 0 to 65535 destination-port 30023
#
ip service-set TCP-30021 type object 1460
 service 0 protocol tcp destination-port 30021
#
ip service-set TCP_30024 type object 1463
 service 0 protocol tcp source-port 0 to 65535 destination-port 30024
#
ip service-set TCP_30101 type object 1467
 service 0 protocol tcp source-port 0 to 65535 destination-port 30101
#
ip service-set TCP_30227 type object 1468
 service 0 protocol tcp source-port 0 to 65535 destination-port 30227
#
ip service-set TCP_30510 type object 1488
 service 0 protocol tcp source-port 0 to 65535 destination-port 30510
#
ip service-set TCP_31001 type object 1516
 service 0 protocol tcp destination-port 31001
#
ip service-set TCP_31011 type object 1518
 service 0 protocol tcp destination-port 31011
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.323
 vlan-type dot1q 323
 ip binding vpn-instance USAP
 ip address *********0 ***************
 service-manage ping permit
#
interface Eth-Trunk1.324
 vlan-type dot1q 324
 ip binding vpn-instance USAP
 ip address *********4 ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if13
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.324
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.323
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain api.weixin.qq.com 
  add domain open.weixin.qq.com 
  add domain api.netease.im 
  add domain qyapi.weixin.qq.com 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name soc-to-trust
  policy logging
  source-address address-set soc_***********/24
  service TCP_8890
  service TCP_8891
  service https
  service icmp
  service snmp
  service ssh
  action permit
 rule name trust-to-soc
  policy logging
  destination-address address-set soc_***********/24
  service TCP_8999
  service rdp-tcp
  service rdp-udp
  service snmptrap
  service syslog
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name "SGW to nginx"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address address-set USAP_**********
  service http
  action permit
 rule name USAPMatserver
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ********* mask ***************
  service tcp-7004
  action permit
 rule name nginx_WX-to-out
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_WX
  destination-address *************** mask ***************
  destination-address ************ mask ***************
  destination-address ************** mask ***************
  destination-address ************ mask ***************
  service https
  action permit
 rule name nginx_WX-to-URL
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_WX
  destination-address domain-set URL
  service https
  action permit
 rule name nginx_WX-to-DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_WX
  service dns
  action permit
 rule name service-to-nginx_vip
  policy logging
  source-zone trust
  source-zone untrust
  destination-zone trust
  destination-zone untrust
  source-address address-set USAP_Service
  source-address address-set USAP_nginx_Internet
  destination-address address-set USAP_Service
  destination-address address-set USAP_nginx_Internet_VIP
  service TCP_10080
  service TCP_10081
  service TCP_9080
  service http
  action permit
 rule name ͳһnginx
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set **********-74
  service TCP_9080
  service http
  action permit
 rule name USAP_nginxӦάϵͳ
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ***********0 mask ***************
  service tcp-18080
  action permit
 rule name USAP_nginxƽ̨
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ********** mask ***************
  service tcp-8081
  action permit
 rule name USAP_nginxϵ
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ************ mask ***************
  service tcp-8089
  action permit
 rule name USAP_nginxAW&YY&JK&SJZT&XXFB
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ********** mask ***************
  destination-address ********* mask ***************
  destination-address *********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  service tcp-8080
  action permit
 rule name USAP_nginx֧  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ********** mask ***************
  service tcp-54106
  action permit
 rule name USAP_nginx

                        source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ********** mask ***************
  service TCP_9080
  service tcp-9090
  action permit
 rule name USAP_nginxͳһϵͳ
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set **********-47
  service http
  action permit
 rule name USAP_nginxϵͳ
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ************ mask ***************
  service tcp-8087
  action permit
 rule name USAP_nginxӪѯ
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address *********** mask ***************
  service tcp-8001
  action permit
 rule name USAP_nginx

                        source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ********** mask ***************
  destination-address address-set *********-9
  service tcp-8080
  action permit
 rule name ϵͳUSAP_nginx
  source-zone untrust
  destination-zone trust
  source-address address-set "inside systems"
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name USAP_nginx-TO-XXFB
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set XXFB_**********0-133
  service tcp-8080
  action permit
 rule name USAP_to_nginx
  source-zone untrust
  destination-zone trust
  source-address address-set USAP_Service
  destination-address address-set USAP_nginx_Internet_VIP
  service TCP_10082
  action permit
 rule name USAP_nginx

                        source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address **********0 mask ***************
  service tcp-8080
  action permit
 rule name USAP-NG_TO_KFPT
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set **********
  service TCP_7001
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name USAPG2MATServer
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ***********5 mask ***************
  service TCP-7004
  action permit
 rule name USAPTableuau  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address **********1 mask ***************
  destination-address address-set **********-63
  service tcp-8080
  action permit
 rule name ump-normal_to_usap-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address range *********31 *********34
  source-address range ********** **********
  destination-address *********** mask ***************
  service TCP_19080
  action permit
 rule name usap-nginx_to_ump-normal
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address range *********30 *********34
  service TCP_8800
  action permit
 rule name SGW_to_fnc
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********
  destination-address *********** mask ***************
  service TCP_8080
  action permit
 rule name fncNg_to_fncApp
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ********* mask ***************
  destination-address range ********* *********
  service TCP_8080
  action permit
 rule name USAP_to_Baobiao
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set ***********-104
  destination-address range *********** ***********
  destination-address range *********** ***********
  service tcp-8080
  action permit
 rule name Baobiao_to_USAP
  source-zone untrust
  destination-zone trust
  source-address address-set ***********-104
  source-address range *********** ***********
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  action permit
 rule name USAP-NG_TO_Docker-NG
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ********** mask ***************
  service http
  action permit
 rule name Docker_TO_USAP-NG
  source-zone untrust
  destination-zone trust
  source-address range ********** **********
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  action permit
 rule name G3_to_USAP
  source-zone untrust
  destination-zone trust
  source-address address-set ************-55
  source-address address-set G3_************
  destination-address address-set USAP_nginx_Internet_VIP
  service TCP_10080
  service TCP_10081
  service TCP_10082
  service tcp-19080
  action permit
 rule name USAP_to_G3
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set G3_*************
  destination-address address-set G3_10.194.120.76
  service TCP_30001
  service TCP_30010
  action permit
 rule name USAPTableuau
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address **********1 mask ***************
  service http
  action permit
 rule name G3_to_USAPVIP
  source-zone untrust
  destination-zone trust
  source-address address-set G3_************
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  action permit
 rule name USAPNG_to_clb-bp-ipc-01
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set G3_*************
  service TCP_30080
  action permit
 rule name USAP_to_YZDIP
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address **********1 mask ***************
  service http
  action permit
 rule name USAP_to_XWDIP
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address range *********** ***********
  service tcp-8087
  service tcp-8089
  action permit
 rule name YZDIP_to_USAPVIP
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address **********1 mask ***************
  source-address address-set ************/24
  source-address address-set 10.196.71.0/24
  source-address address-set Host_4.60.8.5
  source-address address-set Node_************
  source-address address-set host_4.14.100.53
  destination-address *********** mask ***************
  service tcp-19080
  action permit
 rule name XWDIP_to_USAP
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  destination-address *********** mask ***************
  service tcp-19080
  action permit
 rule name Service_To_USAP-nginx-Internet
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set 4.13.20.40-42
  destination-address address-set host_4.14.100.53
  service tcp-8080
  action permit
 rule name USAP-nginx-Internet_To_STQD
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set host_**********
  destination-address address-set host_4.24.11.180
  service TCP_8330
  action permit
 rule name STQD_to_USAPVIP
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set STQD_4.24.11.11-14
  source-address address-set STQD_***********-184
  source-address address-set STQD_**********-28
  source-address address-set STQD_**********-38
  source-address address-set STQD_**********-44
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  action permit
 rule name USAPNG_to_clb-pt-oap-mgr
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set G3_10.194.119.11
  service http
  action permit
 rule name USAP-NG_To_FanRuan
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set host_**********
  destination-address address-set net_***********-22
  service tcp-8080
  action permit
 rule name FanRuan_to_USAP
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_**********
  source-address address-set net_***********-22
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  service tcp-8080
  action permit
 rule name CSLJC-GW-K8S_to_USAP
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set net_**********/24
  destination-address *********** mask ***************
  service tcp-19080
  action permit
 rule name USAPNG_to_CSLJC-BOS-WCSROUTE-F5
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set CSLJC_***********
  service TCP_8088
  action permit
 rule name ZGWS_To_USAP-NG
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ZGWS_*********-4
  source-address address-set host_***********
  destination-address address-set USAP_nginx_Internet
  destination-address address-set USAP_nginx_Internet_VIP
  service TCP_19080
  action permit
 rule name USAP-NG_To_ZGWS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set ZGWS_*********-4
  destination-address address-set host_***********
  service TCP_6002
  action permit
 rule name USAP-nginx-Internet_To_JC-DAS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set host_4.190.166.2
  service TCP_8021
  action permit
 rule name JC-DAS_to_USAPVIP
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set *************-253
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  action permit
 rule name USAP-NG_To_STQD-CLB
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address ************ mask ***************
  destination-address address-set Node_************
  destination-address address-set host_10.194.123.17
  service TCP_30000
  service TCP_30001
  service TCP_31001
  service TCP_31011
  action permit
 rule name USAP-NG_To_JK-CLB
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set host_10.194.123.14
  service TCP_30500
  action permit
 rule name JK-Node_To_USAP-NG
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Node_************
  destination-address address-set USAP_nginx_Internet_VIP
  service http
  service tcp-19080
  action permit
 rule name USAP_NG_To_JKED
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set JKED_F5_VIP
  service TCP-8090
  action permit
 rule name JKED_To_USAP_NG
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********-52
  source-address address-set JKED_Internet
  source-address address-set host_4.13.70.32
  destination-address address-set USAP_nginx_Internet_VIP
  service TCP_19080
  action permit
 rule name USAP-NG_To_JKDG
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set host_**********
  service TCP_8010
  action permit
 rule name JK-Node_To_USAP-NG-1
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Node_************
  destination-address address-set USAP_nginx_Internet_VIP
  service tcp-19080
  action permit
 rule name USAP-NG_To_basmpa
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set host_10.194.123.48
  service TCP_30222
  service TCP_30224
  service TCP_30227
  service TCP_30501
  action permit
 rule name USAP-NG_To_SOM-CLB
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set G3_10.194.123.22
  service TCP_30011
  service TCP_30017
  service tcp-8080
  action permit
 rule name USAP-NG_To_dgsdam
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set G3_10.194.123.30
  service TCP_30220
  action permit
 rule name USAP-NG_To_rpt
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set G3_10.194.123.35
  service TCP_30221
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8002
  service TCP_8443
  service http
  service tcp-8001
  action permit
 rule name USAP_To_bpmca-CLB
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ************* mask ***************
  service TCP_30223
  service TCP_30225
  service TCP_30226
  action permit
 rule name USAP-NG_To_clb-sf-csm
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set G3_10.194.123.26
  service TCP_30058
  action permit
 rule name radius
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_**********
  destination-address address-set **********
  service udp-1812
  action permit
 rule name USAP_nginx_to_kfgd
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set **********
  service tcp-8080
  action permit
 rule name kfgd_to_usap
  source-zone untrust
  destination-zone trust
  source-address address-set **********-12
  destination-address address-set USAP_nginx_Internet_VIP
  service TCP_19080
  action permit
 rule name USAP-NG_To_of-fns-clb
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  source-address range *********** ***********
  destination-address ************* mask ***************
  service TCP_30023
  action permit
 rule name USAP_to_AOMSCLB
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set *************
  service TCP-30021
  action permit
 rule name USAP-NG_To_clb-of-ids
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ************* mask ***************
  service TCP_30024
  action permit
 rule name USAP-NG_To_clb-cf-pms-01
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address ************* mask ***************
  service TCP_30101
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name USAP-Fengxianfangkong
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-14
  destination-address address-set *************
  service TCP_30510
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name 20231207_nginx_to_kfptMG
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address *********** mask ***************
  service http
  action permit
 rule name 20231207_aliKFPT_to_usap-nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ********* mask *************
  destination-address *********** mask ***************
  service TCP_19080
  action permit
 rule name tableau-to-usap
  source-zone untrust
  destination-zone trust
  source-address address-set tableau
  destination-address address-set USAP_**********
  service TCP_19080
  service icmp
  action permit
 rule name usap-to-tableau
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_nginx_Internet
  destination-address address-set tableau
  service http
  service icmp
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* *********3 description TongYiAnQuanJieRuPingTai
#
 sms
#
return
#
switch vsys WSJC 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set soc_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set *********/24 type object
 address 0 ********* mask 24
#
ip address-set host_********* type object
 address 0 ********* mask 32
#
ip address-set ***********-119 type object
 address 0 range *********** ***********
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set ***********-*********** type group
 address 0 range *********** ***********
#
ip address-set **********-********** type group
 address 0 range ********** **********
#
ip address-set **********-********** type group
 address 0 range ********** **********
#
ip service-set TCP_10050 type object 1166
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1167
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1168
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1169
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1170
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8890 type object 1171
 service 0 protocol tcp destination-port 8890
#
ip service-set TCP_8891 type object 1172
 service 0 protocol tcp destination-port 8891
#
ip service-set TCP_8999 type object 1173
 service 0 protocol tcp destination-port 8999
#
ip service-set tcp-139 type object 1272
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1273
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP-10005 type object 1290
 service 0 protocol tcp source-port 0 to 65535 destination-port 10005
#
ip service-set TCP-10009 type object 1291
 service 0 protocol tcp source-port 0 to 65535 destination-port 10009
#
ip service-set TCP_5222 type object 1335
 service 0 protocol tcp source-port 0 to 65535 destination-port 5222
#
ip service-set TCP_7070 type object 1336
 service 0 protocol tcp source-port 0 to 65535 destination-port 7070
#
ip service-set TCP-10010 type object 1337
 service 0 protocol tcp source-port 0 to 65535 destination-port 10010
#
ip service-set TCP-10020 type object 1342
 service 0 protocol tcp source-port 0 to 65535 destination-port 10020
#
ip service-set TCP-10021 type object 1343
 service 0 protocol tcp source-port 0 to 65535 destination-port 10021
#
ip service-set TCP-10022 type object 1344
 service 0 protocol tcp source-port 0 to 65535 destination-port 10022
#
ip service-set TCP-10011 type object 1345
 service 0 protocol tcp source-port 0 to 65535 destination-port 10011
#
ip service-set TCP_6677 type object 1426
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1427
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1428
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1429
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1430
 service 0 protocol tcp destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.325
 vlan-type dot1q 325
 ip binding vpn-instance WSJC
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.326
 vlan-type dot1q 326
 ip binding vpn-instance WSJC
 ip address *********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if14
#
url-filter category user-defined name WSURL
 add url ocr.tencentcloudapi.com
 add host www.dh3t.com
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.326
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.325
#
firewall zone dmz
 set priority 50
#
 domain-set name WSURL 
  add domain ocr.tencentcloudapi.com 
  add domain www.dh3t.com 
  add domain yapi.weixin.qq.com 
  add domain qyapi.weixin.qq.com 
  add domain e-cloudstore.com 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name soc-to-trust
  policy logging
  source-address address-set soc_***********/24
  service TCP_8890
  service TCP_8891
  service https
  service icmp
  service snmp
  service ssh
  action permit
 rule name trust-to-soc
  policy logging
  destination-address address-set soc_***********/24
  service TCP_8999
  service rdp-tcp
  service rdp-udp
  service snmptrap
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name nginx_to_k8s
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ********* mask *************
  destination-address address-set *********/24
  service http
  action permit
 rule name SGW_to_WSJC-NGINX
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_***********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name Docker_to_WS-NG
  source-zone untrust
  destination-zone trust
  source-address ********* mask *************
  source-address address-set *********/24
  source-address address-set **********-**********
  source-address address-set **********-**********
  source-address range ********** **********
  destination-address address-set ***********-***********
  service TCP-10005
  service TCP-10009
  service TCP-10010
  service TCP-10011
  service TCP-10020
  service TCP-10021
  service TCP-10022
  action permit
 rule name WS-NG_to_URL
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-***********
  destination-address domain-set WSURL
  service https
  action permit
 rule name WS-NG_to_DNS
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  service dns
  action permit
 rule name nginx_to_WS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ********* mask *************
  destination-address range ********** **********
  service TCP_5222
  service TCP_7070
  service TCP_8999
  service http
  action permit
 rule name Nginx_to_JueCe
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address address-set ***********-119
  destination-address address-set host_***********
  destination-address address-set host_*********
  service http
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********7
ip route-static ********** ************* **********1 description WeiShengJueCe3.0
#
 sms
#
return
#
switch vsys QKL 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
#
ip address-set soc_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set QKL_*********-3 type object
 address 0 ********* mask 32
 address 1 ********* mask 32
#
ip address-set QKL_***********-12 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set TEST_************** type object
 address 0 ************** mask 32
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1174
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1175
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1176
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1177
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1178
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8890 type object 1179
 service 0 protocol tcp destination-port 8890
#
ip service-set TCP_8891 type object 1180
 service 0 protocol tcp destination-port 8891
#
ip service-set TCP_8999 type object 1181
 service 0 protocol tcp destination-port 8999
#
ip service-set TCP_8444 type object 1245
 service 0 protocol tcp destination-port 8444
#
ip service-set tcp-139 type object 1274
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1275
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_6677 type object 1431
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1432
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1433
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1434
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1435
 service 0 protocol tcp destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.327
 vlan-type dot1q 327
 ip binding vpn-instance QKL
 ip address **********6 ***************
 service-manage ping permit
#
interface Eth-Trunk1.328
 vlan-type dot1q 328
 ip binding vpn-instance QKL
 ip address *********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if15
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.328
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.327
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name soc-to-trust
  policy logging
  source-address address-set soc_***********/24
  service TCP_8890
  service TCP_8891
  service https
  service icmp
  service snmp
  service ssh
  action permit
 rule name trust-to-soc
  policy logging
  destination-address address-set soc_***********/24
  service TCP_8999
  service rdp-tcp
  service rdp-udp
  service snmptrap
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name QKL_PROX-TO-DMZ
  source-zone untrust
  destination-zone trust
  source-address address-set QKL_*********-3
  destination-address address-set QKL_***********-12
  service https
  action permit
 rule name QKL_DMZ-TO-TEST
  source-zone trust
  destination-zone untrust
  source-address address-set QKL_***********-12
  destination-address address-set TEST_**************
  service https
  action permit
 rule name Jenkis
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  service ssh
  action permit
 rule name Security_to_TaiShiGanZhi
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ************ mask ***************
  service TCP_8444
  action permit
 rule name Security_to_duplex
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address range ************ ************
  action permit
 rule name TaiShiGanZhi_to_duplex
  source-zone untrust
  destination-zone trust
  source-address range ************ ************
  destination-address range *********** ***********
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********5
ip route-static ********** ************* **********9 description QuKuaiLian_BlockChain
#
 sms
#
return
#
switch vsys CSLJC 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set CSLJC_*********** type object
 address 0 *********** mask 32
#
ip address-set ***********-14 type object
 address 0 range *********** ***********
#
ip address-set Range_************-42 type object
 address 0 range ************ ************
#
ip address-set 4.190 type object
 address 0 ********* mask 16
#
ip address-set ************_42 type object
 address 0 range ************ ************
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_28080 type object 1233
 service 0 protocol tcp destination-port 28080
#
ip service-set TCP_28081 type object 1234
 service 0 protocol tcp destination-port 28081
#
ip service-set TCP_28082 type object 1235
 service 0 protocol tcp destination-port 28082
#
ip service-set tcp-139 type object 1276
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1277
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_8023 type object 1283
 service 0 protocol tcp destination-port 8023
#
ip service-set TCP_8024 type object 1284
 service 0 protocol tcp destination-port 8024
#
ip service-set TCP_9443 type object 1285
 service 0 protocol tcp destination-port 9443
#
ip service-set TCP_8443 type object 1286
 service 0 protocol tcp destination-port 8443
#
ip service-set TCP_7070 type object 1287
 service 0 protocol tcp destination-port 7070
#
ip service-set TCP_2181 type object 1288
 service 0 protocol tcp destination-port 2181
#
ip service-set TCP_8088 type object 1355
 service 0 protocol tcp source-port 0 to 65535 destination-port 8088
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
acl number 3002
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip destination *********** 0 
 rule 1 permit ip source *********** 0 
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.329
 vlan-type dot1q 329
 ip binding vpn-instance CSLJC
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.330
 vlan-type dot1q 330
 ip binding vpn-instance CSLJC
 ip address *********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if16
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.330
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.329
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain ahchk2.txodds.com 
  add domain ahceur2.txodds.com 
  add domain xml.donbest.com 
  add domain qyapi.weixin.qq.com 
  add domain mail.sporttery.cn 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name juncai_to_office_mail
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address range ********* *********
  destination-address ************ mask ***************
  destination-address ************* mask ***************
  service smtp
  action permit
 rule name internet_to_juncai
  policy logging
  source-zone untrust
  destination-zone trust
  destination-address *********** mask ***************
  service TCP_28080
  service TCP_28081
  service TCP_28082
  action permit
 rule name juncai_to_internetDNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range ********* *********
  source-address range *********1 *********2
  source-address range *********1 *********2
  source-address range ********** **********
  service dns
  action permit
 rule name juncai_to_internet
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address range *********1 *********2
  source-address range *********1 *********2
  source-address range ********** **********
  destination-address domain-set URL
  service http
  service https
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name internet_to_sdas
  policy logging
  source-zone untrust
  destination-zone trust
  destination-address ********* mask ***************
  service TCP_8023
  service TCP_8024
  service TCP_8443
  service TCP_9443
  service https
  action permit
 rule name juncai_to_getSaleData
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range ********** **********
  destination-address range ************ ************
  service TCP_2181
  service TCP_7070
  action permit
 rule name USAPNG_to_CSLJC-BOS-WCSROUTE-F5
  source-zone untrust
  destination-zone trust
  source-address address-set ***********-14
  destination-address address-set CSLJC_***********
  service TCP_8088
  action permit
 rule name JC-BOSROUTER_TO_BOS-WCSROUTE
  source-zone untrust
  destination-zone trust
  source-address address-set Range_************-42
  destination-address *********** mask ***************
  service TCP_8088
  action permit
 rule name juncai_to_office_mail-1
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address range ********* *********
  destination-address domain-set URL
  service smtp
  action permit
 rule name jc_************_42
  source-zone trust
  destination-zone untrust
  source-address address-set 4.190
  destination-address address-set ************_42
  service ssh
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********13
ip route-static ********* ************* *********17 description JunCaiGongSi
ip route-static ********* ************* *********17 description JunCaiGongSi
ip route-static *********** ************* *********17 description JunCaiGongSi
#
 sms
#
return
#
switch vsys TICAIAPP 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ************
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set soc_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1292
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1293
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1294
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1295
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1296
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8890 type object 1297
 service 0 protocol tcp destination-port 8890
#
ip service-set TCP_8891 type object 1298
 service 0 protocol tcp destination-port 8891
#
ip service-set TCP_8999 type object 1299
 service 0 protocol tcp destination-port 8999
#
ip service-set TCP_8080 type object 1304
 service 0 protocol tcp destination-port 8080
#
ip service-set TCP_6677 type object 1436
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1437
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1438
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1439
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1440
 service 0 protocol tcp destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.331
 vlan-type dot1q 331
 ip binding vpn-instance TICAIAPP
 ip address *********22 ***************
 service-manage ping permit
#
interface Eth-Trunk1.332
 vlan-type dot1q 332
 ip binding vpn-instance TICAIAPP
 ip address *********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if17
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.332
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.331
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name soc_to_trust
  policy logging
  source-address address-set soc_***********/24
  service TCP_8890
  service TCP_8891
  service https
  service icmp
  service snmp
  service ssh
  action permit
 rule name trust_to_soc
  policy logging
  destination-address address-set soc_***********/24
  service TCP_8999
  service rdp-tcp
  service snmptrap
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SGW_to_Nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address *********** mask *************
  destination-address ********** mask *************
  action permit
 rule name Nginx_to_ticket
  source-zone trust
  destination-zone untrust
  source-address *********** mask ***************
  source-address *********** mask ***************
  destination-address ********** mask ***************
  service http
  action permit
 rule name Nginx_to_appServer
  source-zone trust
  destination-zone untrust
  source-address *********** mask ***************
  source-address *********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  service TCP_8080
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name EDR_DNS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  destination-address ********** mask ***************
  service dns
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********21
ip route-static ********** ************* *********25 description TicaiAPP_XiangGuan
#
 sms
#
return
#
switch vsys YJVPN 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ************
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set VPN_VS type object
 address 0 ************* mask 32
#
ip address-set ************/28 type object
 address 0 ************ mask 28
#
ip address-set ***********-3 type object
 address 0 range *********** ***********
#
ip address-set **********-42 type object
 address 0 range ********** **********
#
ip address-set ***********2/28 type object
 address 0 ***********2 mask 28
#
ip address-set ************/32 type object
 address 0 ************ mask 32
#
ip address-set **********/32 type object
 address 0 ********** mask 32
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set ***********-12 type object
 address 0 range *********** ***********
#
ip address-set OCS_fabuji type object
 address 0 range ***********1 ************
 address 1 range ************ ************
 address 2 range ************ ************
#
ip address-set OCS_yukong type object
 address 0 range *********** ***********
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip service-set UDP-8778 type object 1496
 service 0 protocol udp source-port 0 to 65535 destination-port 8778
#
ip service-set TCP-8777 type object 1497
 service 0 protocol tcp source-port 0 to 65535 destination-port 8777
#
ip service-set TCP-8891 type object 1498
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8890 type object 1499
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP_10051 type object 1500
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set TCP-8999 type object 1501
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP-1812 type object 1502
 service 0 protocol tcp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP-1813 type object 1503
 service 0 protocol tcp source-port 0 to 65535 destination-port 1813
#
ip service-set UDP-1812 type object 1504
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set UDP-1813 type object 1505
 service 0 protocol udp source-port 0 to 65535 destination-port 1813
#
ip service-set TCP_6677 type object 1506
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1507
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8443 type object 1508
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP_8001 type object 1509
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1510
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_10102 type object 1511
 service 0 protocol tcp source-port 0 to 65535 destination-port 10102
#
ip service-set 389 type object 1512
 service 0 protocol udp source-port 0 to 65535 destination-port 389
 service 1 protocol tcp source-port 0 to 65535 destination-port 389
#
ip service-set TCP-3389 type object 1513
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP-8889 type object 1514
 service 0 protocol tcp source-port 0 to 65535 destination-port 8889
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.401
 vlan-type dot1q 401
 ip binding vpn-instance YJVPN
 ip address *********14 ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
interface Eth-Trunk1.402
 vlan-type dot1q 402
 ip binding vpn-instance YJVPN
 ip address **********2 ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
l2tp-group default-lns
#
interface Virtual-if18
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.402
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.401
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name Any-VPN_VS
  source-zone untrust
  destination-zone trust
  destination-address address-set VPN_VS
  service TCP-8777
  service UDP-8778
  action permit
 rule name VPN_To_SYS
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-3
  source-address address-set ************/28
  destination-address address-set **********-42
  service TCP-1812
  service TCP-1813
  service UDP-1812
  service UDP-1813
  action permit
 rule name EDR
  source-zone trust
  destination-zone untrust
  source-address address-set ***********2/28
  destination-address address-set ************/32
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  action permit
 rule name GTM
  source-zone trust
  destination-zone untrust
  source-address address-set ***********2/28
  destination-address address-set **********/32
  service dns
  service dns-tcp
  action permit
 rule name OCS_out
  source-zone trust
  destination-zone untrust
  source-address address-set ***********2/28
  service TCP_10102
  service https
  action permit
 rule name OCS_in
  source-zone untrust
  destination-zone trust
  source-address address-set **********/24
  action permit
 rule name OCS_out2
  source-zone trust
  destination-zone untrust
  source-address address-set ***********2/28
  destination-address address-set OCS_fabuji
  destination-address address-set OCS_yukong
  service 389
  service TCP-3389
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********13
ip route-static *********** ************* **********1 description TO-VPN
#
 sms
#
return

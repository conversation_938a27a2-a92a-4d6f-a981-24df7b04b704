HRP_M<XW-B1B02-ALI-TEST-FW1>dis cur
2024-01-17 09:51:18.421 +08:00
!Software Version V600R021C00SPC200
!Last configuration was updated at 2024-01-09 11:06:36+08:00 by netadmin
!Last configuration was saved at 2024-01-16 21:44:45+08:00
!kms_feature --
#
pki realm default
#
clock timezone Beijing add 08:00:00
#
sysname XW-B1B02-ALI-TEST-FW1
#
undo ftp server source all-interface
undo ftp ipv6 server source all-interface
#
firewall defend action alert
#
firewall detect ftp
firewall detect sqlnet
firewall detect dns
#
firewall log encrypt-algorithm aes256-gcm
#
hrp enable
hrp standby config enable
hrp auto-sync config static-route
hrp auto-sync config policy-based-route
hrp mirror config enable
hrp mirror session enable
hrp interface Eth-Trunk63 remote *******
undo hrp track trunk-member enable
hrp track interface Eth-Trunk11
hrp authentication-key %+%##!!!!!!!!!"!!!!"!!!!*!!!!sgYXSb=fnHFMq5.#p0l93`a3JX0Ec3nj:l0!!!!!2jp5!!!!!!<!!!!ydAmAYE'yRm3fVA.IG8!Alu@,z<bhPoGL1"!!!!!%+%#
#
ddos-mode detect-only
#
firewall feature-bypass bandwidth-policy enable level 2 priority 1
firewall feature-bypass ddos enable level 2 priority 2
#
undo update schedule ips-sdb enable
update schedule ips-sdb daily 00:00
undo update schedule av-sdb enable
update schedule av-sdb daily 00:00
undo update schedule sa-sdb enable
update schedule sa-sdb daily 00:00
#
ntp server source-interface all disable
ntp ipv6 server source-interface all disable
#
arp learning on-different-segment disable
#
configuration file auto-save interval 600 delay 6 cpu-limit 60
#
telnet server disable
telnet ipv6 server disable
undo telnet server-source all-interface
undo telnet ipv6 server-source all-interface
#
diffserv domain default
#
ip vpn-instance _management_vpn_
 ipv4-family
#
time-range worktime 08:00 to 18:00 working-day
#
aaa
 authentication-scheme default
  authentication-mode local
 authorization-scheme default
  authorization-mode local
 accounting-scheme default
  accounting-mode none
 local-user policy security-enhance
 local-aaa-user password policy administrator
  password expire 900
 domain default
  authentication-scheme default
  accounting-scheme default
 local-user netadmin password irreversible-cipher $1d$u3E4~QEqNJ,9h!%N$S}"sN#3U_PEBdv*SMXE;6PD'F<~pg>yM1^,v-P=D$
 local-user netadmin privilege level 3
 local-user netadmin service-type terminal ssh http
 local-user sysadmin password irreversible-cipher $1d$lB4`HW^DfOh7.>'M$3,]<TlS;YIFO|%$t,k*C6Sz]FJdbA~#\hs3EWq(#$
 local-user sysadmin privilege level 3
 local-user sysadmin service-type terminal ssh http
#
interface MEth0/0/0
 ip binding vpn-instance _management_vpn_
 ip address ************* *************
#
interface Eth-Trunk11
 mode lacp-static
 anti-ddos flow-statistic enable
#
interface Eth-Trunk11.11
 ip address *********** ***************
 dot1q termination vid 11
 service-manage ping permit
 service-manage ssh permit
 anti-ddos flow-statistic enable
#
interface Eth-Trunk11.12
 ip address *********** ***************
 dot1q termination vid 12
 service-manage ping permit
 anti-ddos flow-statistic enable
#
interface Eth-Trunk11.13
 ip address *********** ***************
 dot1q termination vid 13
 service-manage ping permit
 anti-ddos flow-statistic enable
#
interface Eth-Trunk63
 description 心跳接口
 ip address ******* ***************
 service-manage ping permit
 service-manage ssh permit
#
interface GE0/0/0
 eth-trunk 63
 combo enable fiber
 device transceiver 1000BASE-X
#
interface GE0/0/1
 eth-trunk 63
 combo enable fiber
 device transceiver 1000BASE-X
#
interface GE0/0/2
#
interface GE0/0/3
#
interface GE0/0/4
#
interface GE0/0/5
#
interface GE0/0/6
#
interface GE0/0/7
#               
interface GE0/0/8
#
interface GE0/0/9
#
interface GE0/0/10
#
interface GE0/0/11
#
interface 10GE0/0/0
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/1
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/2
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/3
 eth-trunk 11
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/4
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/5
 device transceiver 10GBASE-FIBER
#
interface 10GE0/0/6
#
interface 10GE0/0/7
#
interface 10GE0/0/8
#
interface 10GE0/0/9
#
interface Virtual-if0
#
interface NULL0
#
ip route-static ********* *********** Eth-Trunk11.11 ********** description underlay
ip route-static ********** ************* Eth-Trunk11.11 ********** description dns
ip route-static ********* ************* Eth-Trunk11.11 ********** description 功能测试VPC
ip route-static ********** ************* Eth-Trunk11.11 ********** description 性能测试VPC
ip route-static *********** ************* Eth-Trunk11.11 ********** description 协同工作VPC
ip route-static ********** ************* Eth-Trunk11.11 ********** description 公共资源VPC
ip route-static ********* *********** Eth-Trunk11.11 ********** description 生产测试VPC
ip route-static ********** *********** Eth-Trunk11.12 *********** description test-VDI
ip route-static ********** *********** Eth-Trunk11.12 *********** description test-jiankong
ip route-static ********** *********** Eth-Trunk11.12 *********** description tce-cloud
ip route-static ********** *********** Eth-Trunk11.12 *********** description 腾讯云测试网段
ip route-static ********** *********** Eth-Trunk11.12 *********** description test-coding
ip route-static ********** *********** Eth-Trunk11.12 *********** description test
ip route-static ********** *********** Eth-Trunk11.12 *********** description 测试环境数据中台
ip route-static ******** ********* Eth-Trunk11.12 *********** description 骏彩网段
ip route-static ********* ********* Eth-Trunk11.12 *********** description 亦庄测试网段
ip route-static ********* ********* Eth-Trunk11.12 *********** description 西五环测试网段
ip route-static vpn-instance _management_vpn_ 0.0.0.0 0.0.0.0 *************
#
snmp-agent local-engineid 800007DB03C4D4389B7E53
#
snmp-agent sys-info version v3
#
undo snmp-agent protocol source-status all-interface
undo snmp-agent protocol source-status ipv6 all-interface
#
undo snmp-agent proxy protocol source-status all-interface
undo snmp-agent proxy protocol source-status ipv6 all-interface
#
lldp enable
#
stelnet server enable
sftp server enable
undo ssh server authentication-type keyboard-interactive enable
ssh user netadmin
ssh user netadmin authentication-type password
ssh user netadmin service-type all
ssh user netadmin sftp-directory flash:
ssh user sysadmin
ssh user sysadmin authentication-type password
ssh user sysadmin service-type all
ssh user sysadmin sftp-directory flash:
ssh server-source -i Eth-Trunk63
ssh server-source -i MEth0/0/0
undo ssh server-source all-interface
undo ssh ipv6 server-source all-interface
ssh authorization-type default aaa
#
ssh server cipher aes256_gcm aes128_gcm aes256_ctr aes256_cbc aes128_cbc
ssh server hmac sha2_512 sha2_256
ssh server key-exchange dh_group_exchange_sha256 ecdh_sha2_nistp256 ecdh_sha2_nistp384 ecdh_sha2_nistp521 sm2_kep dh_group16_sha512 curve25519_sha256
#
ssh server publickey rsa rsa_sha2_256 rsa_sha2_512
#
ssh server dh-exchange min-len 2048
#
ssh client publickey rsa_sha2_256 rsa_sha2_512
#
ssh client cipher aes256_gcm aes128_gcm aes256_ctr aes192_ctr aes128_ctr
ssh client hmac sha2_512 sha2_256
ssh client key-exchange dh_group_exchange_sha1 dh_group14_sha1 dh_group1_sha1
#
header shell information "
****************************************************************
*                     Copyright (C) 2012-2022                  *
*                 Huawei Technologies Co., Ltd.                *
*                       All rights reserved.                   *
*           Without the owner's prior written consent,         *
*    no decompiling or reverse-engineering shall be allowed.   *
****************************************************************
"
#
timestamp enable
#
user-interface con 0
 authentication-mode password
 set authentication password cipher $1d$|4EXSo:n}T{]\HCH$RdfZ.oX/&0MiOSRzS",Pw"Q:<HY#O.-BS!$oN3p-$
#
user-interface vty 0 20
 authentication-mode aaa
 protocol inbound ssh
#
warranty
#
web-manager enable port 8443
web-manager http forward enable
undo web-manager captcha enable
#
ike proposal default
 encryption-algorithm aes-gcm-256 aes-gcm-192 aes-gcm-128 
 dh group14 
 authentication-algorithm sha2-512 sha2-384 sha2-256 
 authentication-method pre-share
 integrity-algorithm hmac-sha2-256 
 prf hmac-sha2-256 
#               
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk11.11
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk11.12
#
firewall zone dmz
 set priority 50
 add interface Eth-Trunk11.13
#
firewall zone name DAD id 4
 set priority 60
 add interface Eth-Trunk63
#
sa
#
ip address-set **********/16 type object
 address 0 ********** mask 16
#
ip address-set ************-103 type object
 address 0 range ************ ************
#
ip address-set **********/24 type object
 description coding
 address 0 ********** mask 24
#
ip address-set **********/24 type object
 description aops
 address 0 ********** mask 24
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set *********/16 type object
 address 0 ********* mask 16
#
ip address-set *********/16 type object
 address 0 ********* mask 16
#
ip address-set *********/18 type object
 address 0 ********* mask 18
#
ip address-set *************-227 type object
 description 信创OA
 address 0 range ************* *************
#
ip address-set *********/16 type object
 address 0 ********* mask 16
#
ip address-set *************/32 type object
 address 0 ************* mask 32
#
ip address-set ************-95 type object
 address 0 range ************ ************
#
ip address-set *************-12 type object
 address 0 range ************* *************2
#
ip address-set *************** type object
 address 0 *************** mask 32
#
ip address-set ************** type object
 address 0 ************** mask 32
#               
ip address-set ************-54 type object
 address 0 range ************ 104.21.54.54
#
ip address-set ********/8 type object
 address 0 ******** mask 8
#
ip address-set ***********-40 type object
 address 0 range *********** ***********
#
ip address-set ********/16 type object
 address 0 ******** mask 16
#
ip service-set 2883 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 2883
 service 1 protocol udp source-port 0 to 65535 destination-port 2883
#
ip service-set tcp_10006 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10006
#
ip service-set tcp_1812 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1812
#
ip service-set tcp_1821 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1821
#
ip service-set tcp_2883 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 2883
#
ip service-set tcp_30010 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30010
#
ip service-set tcp_30030-30040 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30030 to 30040
#
ip service-set tcp_389 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 389
#
ip service-set tcp_6006 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6006
#
ip service-set tcp_636 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 636
#
ip service-set tcp_8080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#               
ip service-set tcp_8081 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8081
#
ip service-set tcp_8089 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8089
#
ip service-set tcp_8888 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8888
#
ip service-set tcp_9080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9080
#
ip service-set tcp_9090 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9090
#
ip service-set udp_1812 type object
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
domain-set name oss-cn-beijing-acloud-d01-a.ops
 add domain *.oss-cn-beijing-acloud-d01-a.ops.aliyun.tc
#
nat static-mapping
#               
security-policy
 default policy logging
 default session logging
 default packet-filter intrazone enable
 rule name 主备心跳
  source-zone DAD
  source-zone local
  destination-zone DAD
  destination-zone local
  service protocol udp source-port 0 to 65535 destination-port 18514
  action permit
 rule name icmp
  service icmp
  action permit
 rule name tmp-vdi-to-manage1
  source-zone untrust
  destination-zone trust
  source-address address-set **********/16
  destination-address address-set *********/16
  policy logging
  action permit
 rule name tmp-juncai-to-manage1
  source-zone untrust
  destination-zone trust
  source-address address-set ********/8
  destination-address address-set *********/16
  policy logging
  action permit
 rule name tmp-to-testVPC
  source-zone untrust
  destination-zone trust
  source-address address-set **********/16
  source-address address-set ********/8
  destination-address address-set *********/16
  destination-address address-set *********/16
  policy logging
  action permit
 rule name Coding_to_AliCloud
  description 申请人架构部孙凯
  source-zone untrust
  destination-zone trust
  source-address address-set **********/24
  source-address address-set **********/24
  policy logging
  action permit
 rule name g3_jiankong01
  description 申请人：张永帅
  source-zone untrust
  destination-zone trust
  source-address address-set ************-103
  source-address address-set *************-12
  source-address address-set ***********-40
  destination-address address-set *********/16
  service http
  policy logging
  action permit
 rule name g3_jiankong02
  description 申请人：张永帅
  source-zone untrust
  destination-zone trust
  source-address address-set ************-95
  policy logging
  action permit
 rule name g3_jiankong03
  description 申请人：张永帅
  source-zone trust
  destination-zone untrust
  destination-address address-set **************
  service http  
  service tcp_30030-30040
  policy logging
  action permit
 rule name 信创OA测试
  description 申请人:付超霖
  source-zone trust
  destination-zone untrust
  source-address address-set *************-227
  destination-address address-set *************
  service tcp_2883
  policy logging
  action permit
 rule name 信创OA漏洞扫描
  description 申请人:付超霖
  source-zone untrust
  destination-zone trust
  source-address ************ mask ***************
  destination-address address-set *************-227
  policy logging
  action permit
 rule name 信创OA测试01
  description 申请人:付超霖
  source-zone untrust
  destination-zone trust
  source-address *********** mask ***************
  destination-address ************* mask ***************
  service http
  service https
  service tcp_8888
  policy logging
  action permit
 rule name 灾备云到测试OB
  description 罗利宇（灾备云到测试OB）
  source-zone trust
  destination-zone untrust
  source-address address-set *********/18
  destination-address *************** mask ***************
  destination-address *************** mask ***************
  destination-address address-set ***************
  destination-address range *************** ***************
  service 2883
  action permit
 rule name 灾备云生产到竞猜
  description 刘鹏（灾备云生产到测试竞猜）
  source-zone trust
  destination-zone untrust
  source-address address-set *********/16
  destination-address *********** mask ***************
  service http
  policy logging
  action permit
 rule name 灾备云生产到LDAP
  description 刘鹏（灾备云生产到LDAP）
  source-zone trust
  destination-zone untrust
  source-address address-set *********/16
  destination-address ************** mask ***************
  destination-address ************** mask ***************
  destination-address range ************* *************
  service tcp_389
  service tcp_6006
  policy logging
  action permit
 rule name 等保测评
  description 安全沈天阳
  source-zone untrust
  destination-zone trust
  source-address address-set *************/32
  source-address address-set ************-54
  destination-address address-set *********/16
  policy logging
  action permit
 rule name 云上DNS到云下DNS
  description 申请人：王琳
  source-zone trust
  destination-zone untrust
  destination-address *************** mask ***************
  destination-address ************** mask ***************
  service dns
  service dns-tcp
  policy logging
  action permit
 rule name 云上DNS到云下DNStmp
  description 申请人：王琳
  source-zone untrust
  destination-zone trust
  source-address ************** mask ***************
  service dns
  service dns-tcp
  policy logging
  action permit
 rule name 20231103_cloudToAD
  description 申请人：安全高大会
  source-zone trust
  destination-zone untrust
  source-address ************* mask ***************
  source-address *********** mask ***************
  destination-address range ************* *************
  service tcp_389
  service tcp_636
  action permit
 rule name 20231103_cloudToDoubleFactor
  description 申请人：安全高大会
  source-zone trust
  destination-zone untrust
  source-address ************* mask ***************
  source-address *********** mask ***************
  destination-address ************* mask ***************
  service tcp_1821
  service udp_1812
  action permit
 rule name 20231103_cloudToMonitor
  description 申请人：安全高大会
  source-zone trust
  destination-zone untrust
  source-address ********** mask ***************
  destination-address ************ mask ***************
  service ssh
  service syslog
  action permit
 rule name 20240108_cloudToMonitor
  description 申请人：安全李嘉星
  source-zone trust
  destination-zone untrust
  destination-address ************ mask *************
  service syslog
  action permit
 rule name 20231103_usap-nginx-to-cloud
  description 申请人：白鹏飞
  source-zone untrust
  destination-zone trust
  source-address *********** mask ***************
  destination-address ********* mask ***************
  service http
  policy logging
  action permit
 rule name 20231103_cloud-to-usap-nginx
  description 申请人：白鹏飞
  source-zone trust
  destination-zone untrust
  source-address address-set *********/18
  destination-address *********** mask ***************
  service tcp_9080
  policy logging
  action permit
 rule name 20231103_cloud-to-passwordservice
  description 申请人：白鹏飞
  source-zone trust
  destination-zone untrust
  source-address address-set *********/18
  destination-address ************* mask ***************
  destination-address ************** mask ***************
  service tcp_10006
  policy logging
  action permit
 rule name 20231103_cloud-to-shujuzhongtai
  description 申请人：白鹏飞
  source-zone trust
  destination-zone untrust
  source-address address-set *********/18
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  destination-address ************ mask ***************
  service tcp_8080
  service tcp_8089
  policy logging
  action permit
 rule name 20231103_usap-to-nginx-cloud
  description 申请人：白鹏飞
  source-zone untrust
  destination-zone trust
  source-address *********** mask ***************
  destination-address ********* mask ***************
  destination-address ********* mask ***************
  service http
  policy logging
  action permit
 rule name 20231103_cloud-to-usap-nginx2
  description 申请热：白鹏飞
  source-zone trust
  destination-zone untrust
  source-address address-set *********/18
  destination-address *********** mask ***************
  service tcp_30010
  service tcp_8081
  policy logging
  action permit
 rule name 20231128_开放平台压测tmp01
  description 申请人：白鹏飞
  source-zone untrust
  destination-zone trust
  source-address 10.213.0.84 mask ***************
  source-address *********** mask ***************
  source-address range *********** ***********
  destination-address ********* mask ***************
  destination-address ********* mask ***************
  service http
  action permit
 rule name 20231128_开放平台压测tmp02
  description 申请人：白鹏飞
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address *************** mask ***************
  destination-address ************ mask ***************
  service tcp_2883
  service tcp_9090
  action permit
 rule name 20231128_开放平台测试
  description 申请人：白鹏飞
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  action permit
#
bandwidth-policy
#
policy-based-route
#
nat-policy
#
multi-interface
#
return
HRP_M<XW-B1B02-ALI-TEST-FW1>                   dis version
2024-01-17 09:51:25.773 +08:00
Huawei Versatile Routing Platform Software
VRP (R) software, Version ******** (USG6600F V600R021C00SPC200)
Copyright (C) 2012-2022 Huawei Technologies Co., Ltd.
HUAWEI USG6635F uptime is 239 days, 20 hours, 26 minutes
Patch Version: V600R021SPH180

MPUA-USG6635F 0 : uptime is  239 days, 20 hours, 25 minutes
        StartupTime 2023/05/22   21:27:23
Memory      Size    : 32768 M bytes
Flash       Size    : 4096 M bytes
MPUA-USG6635F version information:
1.PCB       Version : SGB5MPUA VER B
2.MAB       Version : 1
3.Board     Type    : MPUA-USG6635F
4.BIOS      Version : 290
5.CPLD1     Version : 272
  CPLD2     Version : 272

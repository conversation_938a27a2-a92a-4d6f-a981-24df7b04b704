HRP_M<XWECFW01-B1E03>dis cu 
2024-02-28 18:52:53.329 +08:00
!Software Version V500R005C00SPC200
#
sysname XWECFW01-B1E03
#
 l2tp domain suffix-separator @
#
info-center source default channel 2 trap level informational
info-center loghost source GigabitEthernet0/0/0
info-center loghost *********** vpn-instance mgt_vrf
info-center loghost ***********
info-center loghost ************ vpn-instance mgt_vrf facility local6
info-center loghost ************ vpn-instance mgt_vrf
info-center loghost ************ vpn-instance mgt_vrf
info-center logbuffer size 1024
#
authentication-profile name portal_authen_default
#
 ipsec sha2 compatible enable 
#
 undo factory-configuration prohibit
#
undo telnet server enable
undo telnet ipv6 server enable
#
clock timezone Beijing add 08:00:00
#
 hrp enable
 hrp mirror config enable
 hrp interface Eth-Trunk0 remote *******
 hrp mirror session enable
 undo hrp preempt
 undo hrp track trunk-member enable
 hrp track interface Eth-Trunk1
#
 update schedule location-sdb weekly Sun 05:48
#
 firewall defend action discard
#
 log type traffic enable
 log type syslog enable
 log type policy enable
#
 undo dataflow enable
#
 undo sa force-detection enable
#
 lldp enable
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
 banner enable
#
 user-manage web-authentication security port 8887
page-setting
 user-manage security version tlsv1.1 tlsv1.2
password-policy
 level high
user-manage single-sign-on ad
user-manage single-sign-on tsm
user-manage single-sign-on radius
user-manage auto-sync online-user
#
 firewall ids authentication type aes256
#
 web-manager security version tlsv1.1 tlsv1.2
 web-manager enable
 web-manager security enable
 undo web-manager config-guide enable
#
firewall dataplane to manageplane application-apperceive default-action drop
#
 update schedule ips-sdb daily 03:55
 update schedule av-sdb daily 03:55
 update schedule sa-sdb daily 03:55
 update schedule ip-reputation daily 03:55
 update schedule cnc daily 03:55
 update schedule file-reputation daily 03:55
#
 set disk-scan parameter attach on
 set disk-scan parameter cycle 15
 set disk-scan parameter iostat 80
 set disk-scan parameter speed 10
 set disk-scan parameter switch on
 set disk-scan parameter parallel 50
 disk-usage alarm threshold 95 
#
vsys enable 
resource-class r0
resource-class a_core
resource-class a_normal
resource-class a_others
resource-class b_core
resource-class b_normal
resource-class b_others
resource-class b_sgw
resource-class csljc
resource-class b_elb
#
#
vsys name A_Core 1
 assign interface Eth-Trunk1.101
 assign interface Eth-Trunk1.102
 assign resource-class a_core
#
vsys name A_Normal 2
 assign interface Eth-Trunk1.103
 assign interface Eth-Trunk1.104
 assign resource-class a_normal
#
vsys name A_Others 3
 assign interface Eth-Trunk1.105
 assign interface Eth-Trunk1.106
 assign resource-class a_others
#
vsys name B_Core 4
 assign interface Eth-Trunk1.151
 assign interface Eth-Trunk1.152
 assign resource-class b_core
#
vsys name B_Normal 5
 assign interface Eth-Trunk1.153
 assign interface Eth-Trunk1.154
 assign resource-class b_normal
#
vsys name B_Others 6
 assign interface Eth-Trunk1.155
 assign interface Eth-Trunk1.156
 assign resource-class b_others
#
vsys name B_SGW 7
 assign interface Eth-Trunk1.197
 assign interface Eth-Trunk1.198
 assign resource-class b_sgw
#
vsys name CSLJC 8
 assign interface Eth-Trunk1.107
 assign interface Eth-Trunk1.108
 assign resource-class csljc
#
vsys name B_ELB 9
 assign interface Eth-Trunk1.157
 assign interface Eth-Trunk1.158
 assign resource-class b_elb
#
vsys name SGW 10
#
ip vpn-instance A_Core
 ipv4-family
#
ip vpn-instance A_Normal
 ipv4-family
#
ip vpn-instance A_Others
 ipv4-family
#
ip vpn-instance B_Core
 ipv4-family
#
ip vpn-instance B_ELB
 ipv4-family
#
ip vpn-instance B_Normal
 ipv4-family
#
ip vpn-instance B_Others
 ipv4-family
#
ip vpn-instance B_SGW
 ipv4-family
#
ip vpn-instance CSLJC
 ipv4-family
#
ip vpn-instance SGW
 ipv4-family
#
ip vpn-instance default
 ipv4-family
#
ip vpn-instance mgt_vrf
 ipv4-family
#
radius-server template radius_server
 radius-server shared-key cipher %^%#Xgx$)Gt')4}IcfG_S}F&9k'6XY;6n1cTC`1Pe,6Z%^%#
 radius-server authentication ********** 1812 vpn-instance mgt_vrf source ip-address ************* weight 80
 radius-server group-filter class
#
hwtacacs-server template cslc
 hwtacacs-server authentication ********** vpn-instance mgt_vrf
 hwtacacs-server authentication ********** vpn-instance mgt_vrf secondary
 hwtacacs-server authorization ********** vpn-instance mgt_vrf
 hwtacacs-server authorization ********** vpn-instance mgt_vrf secondary
 hwtacacs-server accounting ********** vpn-instance mgt_vrf
 hwtacacs-server accounting ********** vpn-instance mgt_vrf secondary
 hwtacacs-server shared-key cipher %^%#BdIjX{}tMA:$g&5_x;r6s.o9Wk/*YQ|6Z~4TO-!.Kw*H/^TM,O:sx*G"oTqG%^%#
 undo hwtacacs-server user-name domain-included
#
ip address-set CIMS_*********/24 type object
 address 0 ********* mask 24
 address 1 ********* mask 24
#
ip address-set ******* type object
 address 0 ******* mask 32
#
ip address-set jiankong type object
 address 0 *********** mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
acl number 3000
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip 
#
ike proposal default
 encryption-algorithm aes-256 aes-192 aes-128 
 dh group14 
 authentication-algorithm sha2-512 sha2-384 sha2-256 
 authentication-method pre-share
 integrity-algorithm hmac-sha2-256 
 prf hmac-sha2-256 
#
web-auth-server default
 port 50100
#
portal-access-profile name default
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authentication-scheme cslc
  authentication-mode hwtacacs local
 authentication-scheme radius
  authentication-mode radius local
 authorization-scheme default
 authorization-scheme cslc
  authorization-mode hwtacacs local
  authorization-cmd 3 hwtacacs local
 accounting-scheme default
 accounting-scheme cslc
  accounting-mode hwtacacs
 domain default
  authentication-scheme cslc
  accounting-scheme cslc
  authorization-scheme cslc
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain default_admin
  authentication-scheme radius
  accounting-scheme cslc
  authorization-scheme cslc
  radius-server radius_server
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain cslc
  authentication-scheme radius
  accounting-scheme cslc
  authorization-scheme cslc
  radius-server radius_server
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain ztc
  authentication-scheme radius
  radius-server radius_server
  service-type administrator-access
  internet-access mode password
  reference user current-domain
 manager-user audit-admin 
  password cipher @%@%N2K.*8lz;CqMru7,ASL4bk2@\(t'G+qn|,OHQbWfr`P9k2Cb@%@%
  service-type web terminal 
  level 15 

 manager-user api-admin 
  password cipher @%@%z5*mHBi|~7Ub{FRbJgB1lBAPoD<\R[rocYdXx~)1^1pCBASl@%@%
  service-type api 
  level 15 

 manager-user admin 
  password cipher @%@%;FEv,w`5BQPC-,R:*rJH`7231mSZSFSf.Wb=AtQj*YZ!726`@%@%
  service-type web terminal ssh 
  level 15 
  authentication-scheme admin_local 

 manager-user netadmin 
  password cipher @%@%{eKuESSqD=$\H"IM:e1)-":[x~Pi-#yGEM.6iS:l[IK8":^-@%@%
  service-type web terminal ssh 
  level 15 
  authentication-scheme admin_local 

 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
 bind manager-user audit-admin role audit-admin
 bind manager-user admin role system-admin
 bind manager-user netadmin role system-admin
#
ntp-service server disable
ntp-service ipv6 server disable
ntp-service unicast-server ******* vpn-instance mgt_vrf source-interface GigabitEthernet0/0/0
#
interface Eth-Trunk0
 description TO XinTiao
 ip address ******* ***************
#
interface Eth-Trunk1
 description TO XWECSW01-B1E03-04-Eth-Trunk1-2
 mode lacp-static
#
interface Eth-Trunk1.101
 vlan-type dot1q 101
 ip binding vpn-instance A_Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.102
 vlan-type dot1q 102
 ip binding vpn-instance A_Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.103
 vlan-type dot1q 103
 ip binding vpn-instance A_Normal
 ip address *********0 ***************
 service-manage ping permit
#
interface Eth-Trunk1.104
 vlan-type dot1q 104
 ip binding vpn-instance A_Normal
 ip address *********4 ***************
 service-manage ping permit
#
interface Eth-Trunk1.105
 vlan-type dot1q 105
 ip binding vpn-instance A_Others
 ip address *********8 ***************
 service-manage ping permit
#
interface Eth-Trunk1.106
 vlan-type dot1q 106
 ip binding vpn-instance A_Others
 ip address *********2 ***************
 service-manage ping permit
#
interface Eth-Trunk1.107
 vlan-type dot1q 107
 ip binding vpn-instance CSLJC
 ip address *********6 ***************
 service-manage ping permit
#
interface Eth-Trunk1.108
 vlan-type dot1q 108
 ip binding vpn-instance CSLJC
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.151
 vlan-type dot1q 151
 ip binding vpn-instance B_Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.152
 vlan-type dot1q 152
 ip binding vpn-instance B_Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.153
 vlan-type dot1q 153
 ip binding vpn-instance B_Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.154
 vlan-type dot1q 154
 ip binding vpn-instance B_Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.155
 vlan-type dot1q 155
 ip binding vpn-instance B_Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.156
 vlan-type dot1q 156
 ip binding vpn-instance B_Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.157
 vlan-type dot1q 157
 ip binding vpn-instance B_ELB
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.158
 vlan-type dot1q 158
 ip binding vpn-instance B_ELB
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.197
 vlan-type dot1q 197
 ip binding vpn-instance B_SGW
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.198
 vlan-type dot1q 198
 ip binding vpn-instance B_SGW
 ip address *********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface GigabitEthernet0/0/0
 undo shutdown
 ip binding vpn-instance mgt_vrf
 ip address ************* *************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage snmp permit
 service-manage telnet permit
 service-manage netconf permit
#
interface GigabitEthernet1/0/0
 undo shutdown
#
interface GigabitEthernet1/0/1
 undo shutdown
#
interface GigabitEthernet1/0/2
 undo shutdown
#
interface GigabitEthernet1/0/3
 undo shutdown
#
interface GigabitEthernet1/0/4
 undo shutdown
#
interface GigabitEthernet1/0/5
 undo shutdown
#
interface GigabitEthernet1/0/6
 undo shutdown
#
interface GigabitEthernet1/0/7
 undo shutdown
#
interface GigabitEthernet1/0/8
 undo shutdown
 eth-trunk 0
#
interface GigabitEthernet1/0/9
 undo shutdown
 eth-trunk 1
#
interface GigabitEthernet2/0/0
 undo shutdown
#
interface GigabitEthernet2/0/1
 undo shutdown
#
interface GigabitEthernet2/0/2
 undo shutdown
#
interface GigabitEthernet2/0/3
 undo shutdown
#
interface GigabitEthernet2/0/4
 undo shutdown
#
interface GigabitEthernet2/0/5
 undo shutdown
#
interface GigabitEthernet2/0/6
 undo shutdown
#
interface GigabitEthernet2/0/7
 undo shutdown
#
interface GigabitEthernet3/0/0
 undo shutdown
#
interface GigabitEthernet3/0/1
 undo shutdown
#
interface GigabitEthernet3/0/2
 undo shutdown
#
interface GigabitEthernet3/0/3
 undo shutdown
#
interface GigabitEthernet3/0/4
 undo shutdown
#
interface GigabitEthernet3/0/5
 undo shutdown
#
interface GigabitEthernet3/0/6
 undo shutdown
#
interface GigabitEthernet3/0/7
 undo shutdown
#
interface GigabitEthernet3/0/8
 undo shutdown
 eth-trunk 0
#
interface GigabitEthernet3/0/9
 undo shutdown
 eth-trunk 1
#
interface GigabitEthernet4/0/0
 undo shutdown
#
interface GigabitEthernet4/0/1
 undo shutdown
#
interface GigabitEthernet4/0/2
 undo shutdown
#
interface GigabitEthernet4/0/3
 undo shutdown
#
interface GigabitEthernet4/0/4
 undo shutdown
#
interface GigabitEthernet4/0/5
 undo shutdown
#
interface GigabitEthernet4/0/6
 undo shutdown
#
interface GigabitEthernet4/0/7
 undo shutdown
#
interface GigabitEthernet4/0/8
 undo shutdown
#
interface GigabitEthernet4/0/9
 undo shutdown
#
interface Virtual-if0
#
interface Virtual-if1
#
interface Virtual-if2
#
interface Virtual-if3
#
interface Virtual-if4
#
interface Virtual-if5
#
interface Virtual-if6
#
interface Virtual-if7
#
interface Virtual-if8
#
interface Virtual-if9
#
interface Virtual-if10
#
interface NULL0
#
interface LoopBack0
 ip address ************* ***************
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface GigabitEthernet0/0/0
#
firewall zone untrust
 set priority 5
#
firewall zone dmz
 set priority 50
 add interface Eth-Trunk0
#
api
#
ip route-static vpn-instance mgt_vrf 0.0.0.0 0.0.0.0 GigabitEthernet0/0/0 ************* description Out-Of-Band-Management
#
snmp-agent
snmp-agent local-engineid 800007DB037CC385A557BB
snmp-agent community read cipher %^%#v15>"q$n85vECo9:j't1{&YN5NE-UW704H.d9A<OT)Qf!~win:\&\A;h<DqKwX\y*w^]*=+|9x:\xI2#%^%#
snmp-agent sys-info version v2c
undo snmp-agent sys-info version v3
snmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#pa!X82*"]/L2!O.=wczF+`$RVBaPH7zn'<S4\]>B%^%# v2c
snmp-agent target-host trap address udp-domain *********** vpn-instance mgt_vrf params securityname cipher %^%#5Jb^UW,2yP6%,MAQOmv5=JQ/8pRuoTy~pB4oxQ@-%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#WD59L+P>s5%k/zV9:PeY[Nqy5y=jXJ@qAoB[_WpO%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#2@a'YdDzL9*bKv/e%b0R6>/Q>XEeF*siPU4H&h;(%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#!#f(=Rq"[$WbQ9KJy*2%LL$>RtQa+XB&`IDpmQ)Q%^%# v2c
snmp-agent mib-view included View_ALL iso
snmp-agent trap source GigabitEthernet0/0/0
snmp-agent trap enable
#
undo ssh server compatible-ssh1x enable
stelnet ipv4 server enable
ssh user admin
ssh user admin authentication-type password
ssh user admin service-type all
ssh user admin sftp-directory hda1:
ssh user cslc
ssh user cslc authentication-type password
ssh user cslc service-type all
ssh user netadmin
ssh user netadmin authentication-type password
ssh user netadmin service-type all
ssh user netadmin sftp-directory hda1:
ssh client first-time enable
ssh server cipher aes256_ctr aes128_ctr 3des_cbc
#
firewall detect ftp
#
user-interface con 0
 authentication-mode aaa
user-interface vty 0 4
 authentication-mode aaa
 user privilege level 3
 protocol inbound ssh
user-interface vty 16 20
#
pki realm default
#
sa
#
location
#
multi-interface
 mode proportion-of-weight
#
right-manager server-group
#
agile-network 
#
sandbox cloud
 linkage enable
 file-set EXE max-size 2048
 file-set GZIP max-size 2048
 file-set OFFICE max-size 2048
 file-set PDF max-size 2048
#
device-classification
 device-group pc
 device-group mobile-terminal
 device-group undefined-group
#
user-manage server-sync tsm
#
security-policy
 rule name ha
  source-zone dmz
  source-zone local
  destination-zone dmz
  destination-zone local
  action permit
 rule name ntp
  destination-address address-set *******
  service ntp
  action permit
 rule name "trap syslog"
  destination-address address-set jiankong
  service snmptrap
  service syslog
  action permit
 rule name snmp
  source-address address-set jiankong
  service snmp
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name radius
  source-zone local
  destination-zone trust
  destination-address address-set **********
  service icmp
  service radius
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
dns-transparent-policy
 mode based-on-multi-interface
#
rightm-policy
#
decryption-policy
#
mac-access-profile name mac_access_profile
#
 sms
#
return
#
switch vsys A_Core 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range ************ ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set *********-22 type object
 address 0 range ********* *********
#
ip address-set *********-3 type object
 address 0 range ********* *********
#
ip address-set **********-3 type object
 address 0 range ********** **********
#
ip address-set *********-22 type object
 address 0 range ********* *********
#
ip address-set ***********-12 type object
 address 0 range *********** ***********
#
ip address-set ***********-22 type object
 address 0 range *********** ***********
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set **********-44 type object
 address 0 range ********** **********
#
ip address-set **********-47 type object
 address 0 range ********** **********
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ************-202 type object
 address 0 range ************ ************
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set **********-22 type object
 address 0 range ********** **********
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1024
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1025
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1026
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1045
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1046
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1060
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1061
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1062
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-8088 type object 1105
 service 0 protocol tcp source-port 0 to 65535 destination-port 8088
#
ip service-set TCP-139 type object 1112
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1113
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set NAS type object 1134
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 635
 service 5 protocol udp source-port 0 to 65535 destination-port 635
 service 6 protocol tcp source-port 0 to 65535 destination-port 4045 to 4049
 service 7 protocol udp source-port 0 to 65535 destination-port 4045 to 4049
#
ip service-set udp-1812 type object 1170
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_6677 type object 1175
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1176
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1177
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1178
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1179
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.101
 vlan-type dot1q 101
 ip binding vpn-instance A_Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.102
 vlan-type dot1q 102
 ip binding vpn-instance A_Core
 ip address ********* ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if1
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.102
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.101
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  service snmptrap
  service syslog
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name B1  source-zone untrust
  destination-zone trust
  source-address address-set **********-3
  source-address address-set *********-3
  source-address address-set *********-22
  source-address address-set *********-22
  destination-address address-set ***********-12
  destination-address address-set ***********-22
  service ssh
  service tcp-8088
  action permit
 rule name CSLP-PY-TO-SFTP
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  source-address address-set **********
  destination-address address-set ***********-12
  service ssh
  action permit
 rule name CSLP-BH-TO-SFTP
  source-zone untrust
  destination-zone trust
  source-address address-set **********-44
  source-address address-set **********-47
  destination-address address-set ***********-22
  service ssh
  action permit
 rule name BuHaoZhongDuan_to_SFTP
  source-zone untrust
  destination-zone trust
  source-address address-set **********-22
  source-address range ********** **********
  source-address range ********** **********
  source-address range ********** **********
  destination-address address-set ***********-22
  service ssh
  action permit
 rule name PenYinServer_to_SFTP
  source-zone untrust
  destination-zone trust
  source-address ********** mask ***************
  source-address ********** mask ***************
  source-address ********** mask ***************
  source-address address-set **********
  destination-address address-set ***********-12
  service ssh
  action permit
 rule name YY-OCS_TO_SFTP
  source-zone untrust
  destination-zone trust
  source-address address-set ************
  source-address address-set ***********
  destination-address address-set ***********-12
  destination-address address-set ***********-22
  service ssh
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-12
  destination-address address-set ************-202
  service NAS
  service icmp
  action permit
 rule name NAS-

                  source-zone untrust
  destination-zone trust
  source-address address-set ************-202
  destination-address address-set ***********-12
  service NAS
  service icmp
  action permit
 rule name radius
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set **********
  service udp-1812
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static ********** ************* ********* description TO-A_Core
ip route-static ********** ************* ********* description TO-A_Core
#
 sms
#
return
#
switch vsys A_Normal 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range ************ ***********0
 address 4 range ************ ************
 address 5 ********* mask 24
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 *********** mask 24
 address 3 ************8 mask 32
 address 4 range ************ ************
 address 5 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set *********/27 type object
 address 0 ********* mask 27
#
ip address-set ********/24 type object
 address 0 ******** mask 24
#
ip address-set *********/24 type object
 address 0 ********* mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set host_********* type object
 address 0 ********* 0
#
ip address-set host_********** type object
 address 0 ********** 0
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set Jenkis type object
 address 0 range *********** ***********
#
ip address-set **********-2 type object
 address 0 range ********** **********
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set **********-12 type object
 address 0 range ********** **********
#
ip address-set Host_*********** type object
 address 0 *********** mask 32
#
ip address-set SGW_*********/24 type object
 address 0 ********* mask 24
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set **********-65 type object
 address 0 range ********** **********
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set ************-202 type object
 address 0 range ************ ************
#
ip address-set ***********-12 type object
 address 0 range *********** ***********
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set KFPT_***********-22 type object
 address 0 range *********** ***********
#
ip address-set G3_************ type object
 address 0 ************ mask 32
#
ip address-set net_10.194.101-105 type object
 address 0 ************ mask 24
 address 1 ************ mask 24
 address 2 ************ mask 24
 address 3 ************ mask 24
 address 4 ************ mask 24
#
ip address-set Range_************-42 type object
 address 0 range ************ ************
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_4.98.46.200 type object
 address 0 4.98.46.200 mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_4.20.26.122 type object
 address 0 4.20.26.122 mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set **********-12 type object
 address 0 range ********** 4.20.26.12
#
ip address-set host_4.20.26.1 type object
 address 0 4.20.26.1 mask 32
#
ip address-set **********-55 type object
 address 0 range ********** **********
#
ip address-set **********-34 type object
 address 0 range ********** 4.13.10.34
#
ip address-set Range_4.254.127.51-55 type object
 address 0 range 4.254.127.51 4.254.127.55
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set **********/24 type object
 address 0 ********** mask 24
#
ip address-set ************-38 type object
 address 0 range ************ 4.255.205.38
#
ip address-set *********/24 type object
 address 0 ********* mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set DNS_********** type object
 address 0 ********** mask 32
#
ip address-set *********-25 type object
 address 0 range ********* *********
#
ip address-set YZBOCC type object
 address 0 9.66.1.0 mask 24
#
ip address-set XWHBOCC type object
 address 0 4.128.0.0 mask 16
 address 1 18.2.13.0 mask 24
 address 2 18.2.15.0 mask 24
#
ip address-set YJBOCC type object
 address 0 18.2.0.0 mask 16
 address 1 9.66.32.0 mask 24
#
ip address-set **********-72 type object
 address 0 range ********** 4.98.10.72
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1030
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1031
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1032
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1047
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1048
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set tcp-30000 type object 1059
 service 0 protocol tcp source-port 0 to 65535 destination-port 30000
#
ip service-set TCP-8890 type object 1063
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1064
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1065
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_34443 type object 1083
 service 0 protocol tcp source-port 0 to 65535 destination-port 34443
#
ip service-set TCP_18081 type object 1107
 service 0 protocol tcp source-port 0 to 65535 destination-port 18081
#
ip service-set TCP_7001 type object 1110
 service 0 protocol tcp destination-port 7001
#
ip service-set TCP-139 type object 1114
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1115
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP-4422 type object 1128
 service 0 protocol tcp source-port 0 to 65535 destination-port 4422
#
ip service-set NAS type object 1135
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 635
 service 5 protocol udp source-port 0 to 65535 destination-port 635
 service 6 protocol tcp source-port 0 to 65535 destination-port 4045 to 4049
 service 7 protocol udp source-port 0 to 65535 destination-port 4045 to 4049
#
ip service-set TCP_30202 type object 1148
 service 0 protocol tcp destination-port 30202
#
ip service-set TCP_20006 type object 1149
 service 0 protocol tcp destination-port 20006
#
ip service-set TCP_20110 type object 1150
 service 0 protocol tcp destination-port 20110
#
ip service-set TCP_30601 type object 1151
 service 0 protocol tcp destination-port 30601
#
ip service-set TCP_443 type object 1159
 service 0 protocol tcp destination-port 443
#
ip service-set TCP_6006 type object 1160
 service 0 protocol tcp destination-port 6006
#
ip service-set TCP_8931 type object 1161
 service 0 protocol tcp destination-port 8931
#
ip service-set TCP_1521 type object 1162
 service 0 protocol tcp destination-port 1521
#
ip service-set TCP_30010 type object 1166
 service 0 protocol tcp destination-port 30010
#
ip service-set udp-1812 type object 1171
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_8080 type object 1172
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_32600 type object 1173
 service 0 protocol tcp source-port 0 to 65535 destination-port 32600
#
ip service-set TCP_6677 type object 1180
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1181
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1182
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1183
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1184
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP_8989 type object 1213
 service 0 protocol tcp destination-port 8989
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.103
 vlan-type dot1q 103
 ip binding vpn-instance A_Normal
 ip address *********0 ***************
 service-manage ping permit
#
interface Eth-Trunk1.104
 vlan-type dot1q 104
 ip binding vpn-instance A_Normal
 ip address *********4 ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if2
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.104
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.103
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  service snmptrap
  service syslog
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name "ZFK8S to SFTP"
  source-zone untrust
  destination-zone trust
  source-address 10.0.0.0 mask *********
  source-address ******** mask *********
  source-address ********** mask ***************
  source-address ********** mask ***************
  source-address ********** mask ***************
  source-address ********** mask ***************
  source-address address-set ************/24
  source-address address-set *********/24
  source-address address-set ********/24
  source-address address-set host_*********
  destination-address *********** mask ***************
  service tcp-30000
  action permit
 rule name SFTP500WAN
  source-zone trust
  destination-zone untrust
  source-address *********** mask ***************
  destination-address address-set *********/27
  service ssh
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address ************ mask ***************
  source-address address-set NVS
  action permit
 rule name ShuJuJiaZai
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  source-address address-set host_*********
  destination-address address-set host_**********
  service TCP_34443
  action permit
 rule name JenkisTo1050
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  destination-address address-set **********/24
  service TCP_34443
  service ssh
  action permit
 rule name JenkisTo1051
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Jenkis
  destination-address address-set **********/24
  service ssh
  action permit
 rule name SFTP
  source-zone untrust
  destination-zone trust
  source-address ********** mask ***************
  destination-address ********** mask ***************
  service TCP_34443
  action permit
 rule name nginxSFTP
  source-zone trust
  destination-zone untrust
  source-address address-set **********-2
  destination-address ********** mask ***************
  service TCP_34443
  action permit
 rule name SJJM-Nginx
  source-zone untrust
  destination-zone trust
  source-address address-set **********-12
  destination-address address-set ***********
  service http
  action permit
 rule name SJJM_NG_T0_SGW
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********
  destination-address address-set Host_***********
  service TCP_18081
  action permit
 rule name SGW_to_KFPT-openapi
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Range_4.254.127.51-55
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service TCP_7001
  action permit
 rule name nginx_to_apiserver
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ********** mask ***************
  service TCP_7001
  action permit
 rule name SGW_to_CodingNG
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name CodingNG_to_app
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ************ mask ***************
  service http
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name YYYW_to_CodingNG
  source-zone untrust
  destination-zone trust
  source-address range *********** ***********
  destination-address *********** mask ***************
  service TCP-4422
  action permit
 rule name CodingNG_to_TEST-Coding
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ********* mask ***************
  destination-address ********* mask ***************
  service TCP-4422
  action permit
 rule name "HainanDiantou to SFTP"
  source-zone untrust
  destination-zone trust
  source-address address-set **********-65
  source-address address-set host_***********
  source-address address-set host_**********
  source-address address-set host_**********
  destination-address address-set ***********
  service tcp-30000
  action permit
 rule name 500wSFTP
  source-zone untrust
  destination-zone trust
  source-address *********** mask ***************
  destination-address address-set ***********
  service tcp-30000
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ************-202
  destination-address address-set ***********-12
  service NAS
  service icmp
  action permit
 rule name NAS-

                  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ***********-12
  destination-address address-set ************-202
  service NAS
  service icmp
  action permit
 rule name XXFB_to_KFPTng
  source-zone untrust
  destination-zone trust
  source-address range ********** **********
  destination-address range *********** ***********
  service TCP_30202
  action permit
 rule name KFPTng_to_YeZhuDuan
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service TCP_20006
  action permit
 rule name KFPT-NG_to_YeZhuDuan
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service TCP_20110
  action permit
 rule name DZTZ_TO_KFPT-NG
  source-zone untrust
  destination-zone trust
  source-address ************ mask *************
  source-address address-set net_10.194.101-105
  destination-address range *********** ***********
  service TCP_30601
  action permit
 rule name KFPT-NG_to_clb-pt-oap
  source-zone trust
  destination-zone untrust
  source-address address-set KFPT_***********-22
  destination-address address-set G3_************
  service http
  action permit
 rule name G3BOSROUTER_TO_NAS
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Range_************-42
  destination-address address-set ************-202
  service NAS
  service icmp
  action permit
 rule name HaiNai_To_JianGuan-1
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_4.20.26.122
  source-address address-set host_***********
  source-address address-set host_**********
  source-address address-set host_**********
  destination-address address-set host_***********
  service TCP_443
  action permit
 rule name JianGuan_To_HaiNai-1
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_***********
  destination-address address-set host_4.98.46.200
  service TCP_6006
  service TCP_8931
  action permit
 rule name JianGuan_To_Iner-1
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_***********
  destination-address address-set host_***********
  service TCP_6006
  service TCP_8931
  action permit
 rule name JianGuan_To_Iner-2
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_***********
  source-address address-set host_***********
  destination-address address-set host_4.98.46.200
  service TCP_6006
  service TCP_8931
  action permit
 rule name JianGuan_To_Iner-3
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_***********
  source-address address-set host_***********
  destination-address address-set **********-12
  destination-address address-set host_4.20.26.1
  service TCP_1521
  action permit
 rule name JianGuan_To_HaiNai-2
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_***********
  destination-address address-set host_***********
  destination-address address-set host_**********
  destination-address address-set host_**********
  service TCP_443
  action permit
 rule name HaiNai_To_SFTP
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********-55
  source-address address-set **********-72
  source-address address-set host_***********
  source-address address-set host_***********
  source-address address-set host_**********
  source-address address-set host_**********
  destination-address address-set host_**********
  service TCP_34443
  action permit
 rule name UMP_To_JianGuan
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********-34
  destination-address address-set host_***********
  service TCP_30010
  action permit
 rule name JianGuan_TO_Nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_4.20.26.122
  source-address address-set host_***********
  destination-address address-set host_***********
  destination-address address-set host_***********
  service TCP_30010
  action permit
 rule name radius
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set **********
  service udp-1812
  action permit
 rule name aopsnginx_to_aops
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address address-set ************-38
  service TCP_32600
  service TCP_8080
  service http
  action permit
 rule name SGW_to_aopsnginx
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service TCP_32600
  service http
  action permit
 rule name ecc_to_aopsnginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set *********/24
  destination-address range *********** ***********
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  source-address ********** mask *************
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name "YunYing to SFTP"
 rule name aops_to_aops_nginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ************-38
  destination-address range *********** ***********
  service TCP_32600
  service TCP_8080
  service http
  action permit
 rule name **********_to_DNS
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set DNS_**********
  service dns
  action permit
 rule name VDI_to_BOCCSFTP
  source-zone untrust
  destination-zone trust
  source-address address-set *********-25
  source-address address-set XWHBOCC
  source-address address-set YJBOCC
  source-address address-set YZBOCC
  destination-address address-set host_**********
  service TCP_34443
  action permit
 rule name 20231207_nginx_to_kfpt
  policy logging
  source-zone trust
  destination-zone untrust
  source-address range *********** ***********
  destination-address ************ mask ***************
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service http
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
 rule name ALTX_to_KFPTnginx
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ************ mask *************
  source-address ********* mask *************
  destination-address *********** mask ***************
  destination-address range *********** ***********
  service TCP_8989
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static ********** ************* *********3 description TO_A_Normal
ip route-static ********** ************* *********3 description TO-A_Normal
ip route-static ********** ************* *********3 description TO_A_Normal
#
 sms
#
return
#
switch vsys A_Others 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range ************ ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set Terminal type object
 address 0 198.1.1.0 mask 24
 address 1 3.30.12.0 mask 24
 address 2 4.30.11.0 mask 24
 address 3 ********* mask 24
 address 4 18.2.1.0 mask 24
 address 5 18.2.12.0 mask 24
 address 6 18.2.22.0 mask 24
 address 7 9.66.32.0 mask 24
#
ip address-set host_4.101.91.11 type object
 address 0 4.101.91.11 mask 32
#
ip address-set host_4.101.91.21 type object
 address 0 4.101.91.21 mask 32
#
ip address-set host_4.101.91.31 type object
 address 0 4.101.91.31 mask 32
#
ip address-set host_4.255.10.11 type object
 address 0 4.255.10.11 mask 32
#
ip address-set host_4.255.10.21 type object
 address 0 4.255.10.21 mask 32
#
ip address-set host_4.255.10.22 type object
 address 0 4.255.10.22 mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set Range_4.101.90.11-14 type object
 address 0 range 4.101.90.11 4.101.90.14
#
ip address-set host_198.3.100.240 type object
 address 0 198.3.100.240 mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_4.98.1.20 type object
 address 0 4.98.1.20 mask 32
#
ip address-set Range_4.190.121.81-82 type object
 address 0 range 4.190.121.81 4.190.121.82
#
ip address-set Range_**********-2 type object
 address 0 range ********** **********
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set SGW_*********/24 type object
 address 0 ********* mask 24
#
ip address-set ***********-22 type object
 address 0 range *********** ***********
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set Range_**********-47 type object
 address 0 range ********** **********
#
ip address-set Range_***********-32 type object
 address 0 range *********** ***********
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_********* type object
 address 0 ********* mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set ******** type object
 address 0 ******** mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set host*********** type object
 address 0 *********** mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set ***********-54 type object
 address 0 range *********** ***********
#
ip address-set host_************8 type object
 address 0 ************8 mask 32
#
ip address-set Sec_Monitor type object
 address 0 ************* mask 32
 address 1 ************ mask 32
 address 2 ************0 mask 32
 address 3 ************2 mask 32
 address 4 ************9 mask 32
 address 5 ************ mask 32
 address 6 ************1 mask 32
 address 7 ************0 mask 32
 address 8 ************* mask 32
 address 9 ************* mask 32
 address 10 ************ mask 32
 address 11 ************ mask 32
 address 12 ************ mask 32
 address 13 ************ mask 32
 address 14 ************ mask 32
 address 15 ************ mask 32
 address 16 ************* mask 32
 address 17 ************ mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set ***********-52 type object
 address 0 range *********** ***********
#
ip service-set TCP_10050 type object 1036
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1037
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1038
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1049
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1050
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1066
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1067
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1068
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_8000 type object 1084
 service 0 protocol tcp source-port 0 to 65535 destination-port 8000
#
ip service-set TCP_8080 type object 1085
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_389 type object 1086
 service 0 protocol tcp source-port 0 to 65535 destination-port 389
#
ip service-set udp-389 type object 1087
 service 0 protocol udp source-port 0 to 65535 destination-port 389
#
ip service-set tcp-636 type object 1088
 service 0 protocol tcp source-port 0 to 65535 destination-port 636
#
ip service-set tcp-3268 type object 1089
 service 0 protocol tcp source-port 0 to 65535 destination-port 3268
#
ip service-set tcp-3269 type object 1090
 service 0 protocol tcp source-port 0 to 65535 destination-port 3269
#
ip service-set tcp-5722 type object 1091
 service 0 protocol tcp source-port 0 to 65535 destination-port 5722
#
ip service-set 464 type object 1092
 service 0 protocol tcp source-port 0 to 65535 destination-port 464
 service 1 protocol udp source-port 0 to 65535 destination-port 464
#
ip service-set tcp-9389 type object 1093
 service 0 protocol tcp source-port 0 to 65535 destination-port 9389
#
ip service-set tcp-2535 type object 1094
 service 0 protocol tcp source-port 0 to 65535 destination-port 2535
#
ip service-set udp-2535 type object 1095
 service 0 protocol udp source-port 0 to 65535 destination-port 2535
#
ip service-set 49152-65535 type object 1096
 service 0 protocol tcp source-port 0 to 65535 destination-port 49152 to 65535
 service 1 protocol udp source-port 0 to 65535 destination-port 49152 to 65535
#
ip service-set TCP-139 type object 1116
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1117
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP-10102 type object 1154
 service 0 protocol tcp source-port 0 to 65535 destination-port 10102
#
ip service-set TCP-5601 type object 1155
 service 0 protocol tcp source-port 0 to 65535 destination-port 5601
#
ip service-set TCP-4451 type object 1156
 service 0 protocol tcp source-port 0 to 65535 destination-port 4451
#
ip service-set TCP_25601 type object 1157
 service 0 protocol tcp source-port 0 to 65535 destination-port 25601
#
ip service-set TCP_30900 type object 1158
 service 0 protocol tcp source-port 0 to 65535 destination-port 30900
#
ip service-set TCP_3000 type object 1163
 service 0 protocol tcp destination-port 3000
#
ip service-set TCP_8081 type object 1164
 service 0 protocol tcp destination-port 8081
#
ip service-set TCP_18081 type object 1167
 service 0 protocol tcp destination-port 18081
#
ip service-set TCP_6677 type object 1185
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1186
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1187
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1188
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1189
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP_8880 type object 1206
 service 0 protocol tcp destination-port 8880
#
ip service-set TCP_8889 type object 1208
 service 0 protocol tcp source-port 0 to 65535 destination-port 8889
#
ip service-set TCP_8400 type object 1209
 service 0 protocol tcp destination-port 8400
#
ip service-set TCP_30038 type object 1210
 service 0 protocol tcp destination-port 30038
#
ip service-set TCP_30039 type object 1211
 service 0 protocol tcp destination-port 30039
#
ip service-set tcp_19090 type object 1212
 service 0 protocol tcp source-port 0 to 65535 destination-port 19090
#
ip service-set TCP_10000 type object 1225
 service 0 protocol tcp destination-port 10000
#
ip service-set SecOCS-PORT type object 1226
 service 6 protocol tcp source-port 0 to 65535 destination-port 6000 to 8000
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.105
 vlan-type dot1q 105
 ip binding vpn-instance A_Others
 ip address *********8 ***************
 service-manage ping permit
#
interface Eth-Trunk1.106
 vlan-type dot1q 106
 ip binding vpn-instance A_Others
 ip address *********2 ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if3
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.106
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.105
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  service snmptrap
  service syslog
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name TerminalToNetworkDisk
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Terminal
  destination-address address-set host_4.101.91.11
  service TCP_8000
  service TCP_8080
  service http
  service https
  action permit
 rule name CIMSProxyToNetworkDisk
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_4.255.10.11
  destination-address address-set host_4.101.91.11
  service http
  action permit
 rule name NetworkDiskToCIMS-AD
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_4.101.91.11
  source-address address-set host_4.101.91.21
  source-address address-set host_4.101.91.31
  destination-address address-set host_4.255.10.21
  destination-address address-set host_4.255.10.22
  service 464
  service 49152-65535
  service TCP_389
  service bootps
  service dns
  service dns-tcp
  service kerberos-tcp
  service kerberos-udp
  service netbios-datagram
  service netbios-name
  service netbios-session
  service netbios-ssn
  service rpc
  service smb
  service smtp
  service tcp-3268
  service tcp-3269
  service tcp-5722
  service tcp-636
  service tcp-9389
  service udp-2535
  service udp-389
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name BaoLeiJi-To-G3Monitor
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set host_198.3.100.240
  destination-address address-set host_**********
  service TCP-5601
  action permit
 rule name BaoLeiJi-To-ShuangYinSu
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_***********
  destination-address address-set host_**********
  service TCP-4451
  action permit
 rule name VDI-To-BaoLeiJi_1
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set host_***********
  service TCP-10102
  service http
  service https
  action permit
 rule name VDI-To-BaoLeiJi_2
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_4.98.1.20
  destination-address address-set Range_4.101.90.11-14
  service TCP-3389
  service TCP_389
  action permit
 rule name BaoLeiJi-To-JC_KIBANA
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set Range_4.190.121.81-82
  destination-address address-set host_***********
  service TCP_25601
  action permit
 rule name BaoLeiJi-To-JC_Debug
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set Range_**********-2
  service TCP_30900
  action permit
 rule name BaoLeiJi-To-G3Monitor-1
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set host_**********
  service TCP_3000
  action permit
 rule name SGW_to_CS
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service TCP_8080
  service TCP_8081
  action permit
 rule name CS-NG_To_b2csweb
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-22
  destination-address address-set host_**********
  service TCP_8080
  service TCP_8081
  action permit
 rule name BaoLeiJi_To_SJJM-WEB-NG
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set host_**********
  service http
  action permit
 rule name DAM-Server_To_NG
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Range_**********-47
  destination-address address-set host_***********
  service TCP_18081
  action permit
 rule name SJJM-WEB-NG_To_SSL
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_***********-32
  destination-address address-set host_*********
  service TCP_18081
  action permit
 rule name BaoLeiJi-To-SJFX
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set ********
  service ssh
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ********** mask *************
  source-address ********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name BaoLeiJi-To-YeWu
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  service TCP_8080
  service TCP_8880
  action permit
 rule name BaoLeiJi-To-SJZT
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set **********
  destination-address address-set **********
  destination-address address-set **********
  service TCP_8080
  service TCP_8889
  action permit
 rule name BaoLeiJi-To-SOClogging
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set host_***********
  destination-address ************1 mask ***************
  destination-address ************2 mask ***************
  service TCP_8400
  service syslog
  action permit
 rule name 20231116_BaoLeiJi-To-dns
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address ********** mask ***************
  service dns
  service dns-tcp
  action permit
 rule name 20231116_BaoLeiJi-To-jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address *********** mask ***************
  destination-address ********** mask ***************
  destination-address ************* mask ***************
  destination-address ************ mask ***************
  service TCP_30038
  service TCP_30039
  service http
  action permit
 rule name Baoleiji-To-ZhongTai
  source-zone trust
  destination-zone untrust
  source-address address-set Range_4.101.90.11-14
  source-address address-set host_***********
  destination-address address-set host***********
  service icmp
  service tcp_19090
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
 rule name SecOCS-To-Monitor-1
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-54
  destination-address address-set Sec_Monitor
  service https
  action permit
 rule name SecOCS-To-Monitor-2
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-54
  destination-address address-set host_************8
  service TCP_8081
  action permit
 rule name VDI-To-SecOCS_1
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set host_***********
  service http
  action permit
 rule name VDI-To-SecOCS_2
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_4.98.1.20
  destination-address address-set ***********-52
  destination-address address-set host_***********
  service TCP-10102
  service TCP_10000
  service https
  action permit
 rule name VDI-To-SecOCS_3
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_4.98.1.20
  destination-address address-set ***********-54
  service SecOCS-PORT
  service TCP-3389
  service TCP_389
  action permit
 rule name SecOCS-To-ShuangYinSu
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********-52
  source-address address-set ***********-54
  destination-address address-set host_**********
  service TCP-4451
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********7
ip route-static ********** ************* *********1 description TO-A_Others
ip route-static ********** ************* *********1 description TO-A_Others
#
 sms
#
return
#
switch vsys B_Core 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range ************ ***********0
 address 4 range ************ ************
 address 5 ********* mask 24
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_*********/24 type object
 address 0 ********* mask 24
#
ip address-set elb_************ type object
 address 0 ************ mask 32
#
ip address-set elb_************-14 type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32
#
ip address-set elb_******** type object
 address 0 ******** mask 32
#
ip address-set elb_*******-2 type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set payProxy_**********-214 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
 address 3 ********** mask 32
#
ip address-set payProxyNG type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
#
ip address-set weixin_dnat_*********** type object
 address 0 *********** mask 32
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set STQD_NG type object
 address 0 range ************ ************
#
ip address-set STQD_********** type object
 address 0 ********** mask 32
#
ip address-set YWZT_NG type object
 address 0 range ************ ************
#
ip address-set YWZT_********** type object
 address 0 ********** mask 32
#
ip address-set YWZT_NG_500W type object
 address 0 range ************ ************
#
ip address-set K8S-******** type object
 address 0 ******** mask 24
#
ip address-set K8S-NG type object
 address 0 range ************ ************
#
ip address-set TC_APP_XIAOXI type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set 500W_XIAOXI type object
 address 0 *********** mask 32
#
ip address-set USAP_NG type object
 address 0 range ************ ************
#
ip address-set USAP_Service type object
 address 0 range ********** **********
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set G3_************* type object
 address 0 ************* mask 32
#
ip address-set USAP_************-62 type object
 address 0 range ************ ************
#
ip address-set GTM_3.9.20.X type object
 address 0 ********** mask 32
 address 1 range ********** **********
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set G3_************ type object
 address 0 ************ mask 24
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set SJZT-NG type object
 address 0 4.101.129.71 mask 32
 address 1 4.101.129.72 mask 32
 address 2 ************ mask 32
#
ip address-set SJZT-VS type object
 address 0 *********** mask 32
#
ip address-set *********** type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 10.196.80.0 mask 24
#
ip service-set TCP_10050 type object 1027
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1028
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1029
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1051
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1052
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1069
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1070
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1071
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_5080 type object 1082
 service 0 protocol tcp destination-port 5080
#
ip service-set TCP-139 type object 1118
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1119
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_8330 type object 1131
 service 0 protocol tcp source-port 0 to 65535 destination-port 8330
#
ip service-set TCP_8080 type object 1132
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_19080 type object 1136
 service 0 protocol tcp source-port 0 to 65535 destination-port 19080
#
ip service-set TCP_9080 type object 1137
 service 0 protocol tcp source-port 0 to 65535 destination-port 9080
#
ip service-set TCP_30010 type object 1152
 service 0 protocol tcp destination-port 30010
#
ip service-set TCP_54102 type object 1153
 service 0 protocol tcp destination-port 54102
#
ip service-set udp-1812 type object 1169
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
ip service-set TCP_6677 type object 1190
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1191
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1192
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1193
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1194
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP_19091 type object 1214
 service 0 protocol tcp source-port 0 to 65535 destination-port 19091
#
ip service-set TCP_19092 type object 1215
 service 0 protocol tcp source-port 0 to 65535 destination-port 19092
#
ip service-set TCP_19093 type object 1216
 service 0 protocol tcp source-port 0 to 65535 destination-port 19093
#
ip service-set TCP_19094 type object 1217
 service 0 protocol tcp source-port 0 to 65535 destination-port 19094
#
ip service-set TCP_19095 type object 1218
 service 0 protocol tcp source-port 0 to 65535 destination-port 19095
#
ip service-set TCP_31001 type object 1224
 service 0 protocol tcp destination-port 31001
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.151
 vlan-type dot1q 151
 ip binding vpn-instance B_Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.152
 vlan-type dot1q 152
 ip binding vpn-instance B_Core
 ip address ********* ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if4
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.152
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.151
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  service snmptrap
  service syslog
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name "SGW to ELB"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set elb_************
  service TCP_5080
  action permit
 rule name "nginx to ELB"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set elb_************-14
  destination-address address-set elb_********
  service http
  action permit
 rule name "internal to ELB_NG"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set elb_*******-2
  destination-address address-set elb_************
  destination-address address-set elb_************-14
  service TCP_5080
  action permit
 rule name "payProxy to nginx"
  source-zone untrust
  destination-zone trust
  source-address address-set payProxy_**********-214
  destination-address address-set payProxyNG
  service https
  action permit
 rule name "payProxyNG to weixin"
  source-zone trust
  destination-zone untrust
  source-address address-set payProxyNG
  destination-address address-set weixin_dnat_***********
  service https
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name "SGW to STQD_NG"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set STQD_NG
  service TCP_8330
  action permit
 rule name "STQD_NG to STQD"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set STQD_NG
  destination-address address-set STQD_**********
  service TCP_8330
  action permit
 rule name "SGW to YWZT_NG"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set YWZT_NG
  service TCP_8080
  action permit
 rule name "YWZT_NG to YWZT_**********"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set YWZT_NG
  destination-address address-set YWZT_**********
  service TCP_8080
  action permit
 rule name "K8S-******** to K8S-NG"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ************/24
  source-address address-set K8S-********
  destination-address address-set K8S-NG
  service TCP_8080
  service https
  action permit
 rule name "K8S-NG to TC_APP_XIAOXI"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set K8S-NG
  destination-address *********** mask ***************
  destination-address *********** mask ***************
  destination-address address-set TC_APP_XIAOXI
  service TCP_8080
  action permit
 rule name "SGW to YWZT_NG_500W"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set YWZT_NG_500W
  service TCP_8080
  action permit
 rule name "YWZT_NG_500W to YWZT_**********"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set YWZT_NG_500W
  destination-address address-set YWZT_**********
  service TCP_8080
  action permit
 rule name "K8S-NG to 500W_XIAOXI"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set K8S-NG
  destination-address address-set 500W_XIAOXI
  service https
  action permit
 rule name "SGW to USAP_NG"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set USAP_NG
  service TCP_19080
  service http
  action permit
 rule name "USAP_NG to USAP_Service"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_NG
  destination-address address-set USAP_Service
  service TCP_9080
  action permit
 rule name G3_to_USAP
  source-zone untrust
  destination-zone trust
  source-address address-set ***********
  source-address address-set G3_************
  source-address address-set GTM_3.9.20.X
  destination-address address-set USAP_NG
  service TCP_19080
  action permit
 rule name USAP_to_G3
  source-zone trust
  destination-zone untrust
  source-address address-set USAP_************-62
  destination-address ********** mask ***************
  destination-address address-set ************
  destination-address address-set G3_*************
  service TCP_30010
  service TCP_31001
  service TCP_54102
  action permit
 rule name G3_to_K8S-NG
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set G3_************
  destination-address address-set K8S-NG
  service TCP_8080
  action permit
 rule name radius
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********/24
  destination-address address-set **********
  service udp-1812
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address *********** mask *************
  source-address *********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name SJZT-NG_to_SJZT-VS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SJZT-NG
  destination-address address-set SJZT-VS
  service TCP_19091
  service TCP_19092
  service TCP_19093
  service TCP_19094
  service TCP_19095
  action permit
 rule name SGW_to_SJZT-NG
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set SJZT-NG
  service TCP_19091
  service TCP_19092
  service TCP_19093
  service TCP_19094
  service TCP_19095
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static *********** ************* ********* description TO-B_Core
ip route-static *********** ************* ********* description TO-B_Core
#
 sms
#
return
#
switch vsys B_Normal 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range ************ ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set SFPT_*********** type object
 address 0 *********** mask 32
#
ip address-set elb_*********-59 type object
 address 0 range ********* *********
#
ip address-set psbc_nat_********** type object
 address 0 ********** mask 32
#
ip address-set YunYing_**********-9 type object
 address 0 range ********** **********
#
ip address-set sjzt_********** type object
 address 0 ********** mask 32
#
ip address-set **********-35 type object
 address 0 range ********** **********
#
ip address-set *********** type object
 address 0 *********** mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set host_*********** type object
 address 0 *********** mask 32
#
ip address-set **********-75 type object
 address 0 range ********** **********
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set **********-55 type object
 address 0 range ********** **********
#
ip address-set ***********-49 type object
 address 0 range *********** ***********
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip address-set ************ type object
 address 0 ************ mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1033
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1034
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1035
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1053
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1054
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1072
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1073
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1074
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_34443 type object 1111
 service 0 protocol tcp destination-port 34443
#
ip service-set TCP-139 type object 1120
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1121
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set tcp-30000 type object 1168
 service 0 protocol tcp source-port 0 to 65535 destination-port 30000
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.153
 vlan-type dot1q 153
 ip binding vpn-instance B_Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.154
 vlan-type dot1q 154
 ip binding vpn-instance B_Normal
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if5
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.154
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.153
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service TCP_34443
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  service snmptrap
  service syslog
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name "psbc to SFTP"
  source-zone untrust
  destination-zone trust
  source-address address-set psbc_nat_**********
  destination-address address-set SFPT_***********
  service ssh
  action permit
 rule name "ELB to SFTP"
  source-zone untrust
  destination-zone trust
  source-address address-set elb_*********-59
  destination-address address-set SFPT_***********
  service ssh
  action permit
 rule name "YunYing to SFTP"
  source-zone untrust
  destination-zone trust
  source-address *********** mask ***************
  source-address *********** mask ***************
  source-address address-set ***********-49
  source-address address-set YunYing_**********-9
  source-address address-set host_***********
  source-address address-set host_***********
  destination-address address-set SFPT_***********
  service ssh
  action permit
 rule name "ShuJuZhongTai to SFTP"
  source-zone untrust
  destination-zone trust
  source-address address-set sjzt_**********
  destination-address address-set SFPT_***********
  service ssh
  action permit
 rule name JGXT_TO_KFPT-SFTP
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********-35
  source-address address-set **********-75
  source-address address-set ***********
  source-address address-set host_***********
  source-address address-set host_**********
  source-address address-set host_**********
  destination-address address-set SFPT_***********
  service ssh
  action permit
 rule name KFSJFX_TO_KFPT-SFTP
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set **********-55
  source-address address-set host_**********
  source-address address-set host_**********
  destination-address address-set SFPT_***********
  service ssh
  action permit
 rule name SJSJ_TO_XQTC
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ************
  source-address address-set ************
  source-address address-set ************
  source-address address-set ************
  source-address address-set *********
  destination-address address-set SFPT_***********
  service ssh
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static *********** ************* ********** description TO-B_Normal
#
 sms
#
return
#
switch vsys B_Others 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range ************ ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1039
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1040
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1041
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1055
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1056
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1075
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1076
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1077
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP-139 type object 1122
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1123
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_6677 type object 1195
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1196
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1197
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1198
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1199
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.155
 vlan-type dot1q 155
 ip binding vpn-instance B_Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.156
 vlan-type dot1q 156
 ip binding vpn-instance B_Others
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if6
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.156
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.155
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  service snmptrap
  service syslog
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address *********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static *********** ************* ********** description TO-B_Others
#
 sms
#
return
#
switch vsys B_SGW 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range ************ ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_*********/24 type object
 address 0 ********* mask 24
#
ip address-set SGW_*********/24 type object
 address 0 ********* mask 24
#
ip address-set elb_************ type object
 address 0 ************ mask 32
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set Host_*********** type object
 address 0 *********** mask 32
#
ip address-set Host_*********** type object
 address 0 *********** mask 32
#
ip address-set Host_********* type object
 address 0 ********* mask 32
#
ip address-set **********-14 type object
 address 0 range ********** **********
#
ip address-set **********-24 type object
 address 0 range ********** **********
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set STQD_NG type object
 address 0 range ************ ************
#
ip address-set YWZT_NG type object
 address 0 range ************ ************
#
ip address-set YWZT_NG_500W type object
 address 0 range ************ ************
#
ip address-set USAP_NG type object
 address 0 range ************ ************
#
ip address-set ************/24 type object
 address 0 ************ mask 24
#
ip address-set SGW_*********** type object
 address 0 *********** mask 32
#
ip address-set SGW_********* type object
 address 0 ********* mask 32
#
ip address-set SGW_********* type object
 address 0 ********* mask 32
#
ip address-set ********* type object
 description SSL
 address 0 ********* mask 24
#
ip address-set ********* type object
 description SSL-CRL
 address 0 ********* mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set SJZT-NG-VS type object
 address 0 ************ mask 32
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip service-set TCP_10050 type object 1042
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1043
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1044
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1057
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1058
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1078
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1079
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1080
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_5080 type object 1081
 service 0 protocol tcp destination-port 5080
#
ip service-set TCP_18081 type object 1106
 service 0 protocol tcp source-port 0 to 65535 destination-port 18081
#
ip service-set TCP_8443 type object 1108
 service 0 protocol tcp destination-port 8443
#
ip service-set TCP_7001 type object 1109
 service 0 protocol tcp destination-port 7001
#
ip service-set TCP-139 type object 1124
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1125
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_8330 type object 1129
 service 0 protocol tcp source-port 0 to 65535 destination-port 8330
#
ip service-set TCP_8080 type object 1130
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_19080 type object 1147
 service 0 protocol tcp source-port 0 to 65535 destination-port 19080
#
ip service-set TCP_8081 type object 1165
 service 0 protocol tcp destination-port 8081
#
ip service-set TCP_7443 type object 1174
 service 0 protocol tcp source-port 0 to 65535 destination-port 7443
#
ip service-set TCP_32600 type object 1205
 service 0 protocol tcp source-port 0 to 65535 destination-port 32600
#
ip service-set TCP_UDP_389 type object 1207
 service 0 protocol tcp source-port 0 to 65535 destination-port 389
 service 1 protocol udp source-port 0 to 65535 destination-port 389
#
ip service-set TCP_19091 type object 1219
 service 0 protocol tcp source-port 0 to 65535 destination-port 19091
#
ip service-set TCP_19092 type object 1220
 service 0 protocol tcp source-port 0 to 65535 destination-port 19092
#
ip service-set TCP_19093 type object 1221
 service 0 protocol tcp source-port 0 to 65535 destination-port 19093
#
ip service-set TCP_19094 type object 1222
 service 0 protocol tcp source-port 0 to 65535 destination-port 19094
#
ip service-set TCP_19095 type object 1223
 service 0 protocol tcp source-port 0 to 65535 destination-port 19095
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.197
 vlan-type dot1q 197
 ip binding vpn-instance B_SGW
 ip address *********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.198
 vlan-type dot1q 198
 ip binding vpn-instance B_SGW
 ip address *********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if7
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.198
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.197
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  service snmptrap
  service syslog
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name "external to SGW"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SGW_*********/24
  service TCP_19091
  service TCP_19092
  service TCP_19093
  service TCP_19094
  service TCP_19095
  service TCP_7443
  service TCP_8443
  service https
  action permit
 rule name "SGW to ELB"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address address-set elb_************
  service TCP_5080
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name SJJM_NG_T0_SGW
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Host_***********
  destination-address address-set Host_***********
  service TCP_18081
  action permit
 rule name SJJM_SGW-In_To_SGW-Out
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Host_*********
  action permit
 rule name SGW_to_KFPT-openapi
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service TCP_7001
  action permit
 rule name SGW_to_CodingNG
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name SOC-1
  source-address address-set ************-************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name "SGW to STQD_NG"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address address-set STQD_NG
  service TCP_8330
  action permit
 rule name "SGW to YWZT_NG"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address address-set YWZT_NG
  service TCP_8080
  action permit
 rule name "SGW to YWZT_NG_500W"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address address-set YWZT_NG_500W
  service TCP_8080
  action permit
 rule name "SGW to USAP_NG"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address address-set USAP_NG
  service TCP_19080
  service http
  action permit
 rule name "SGW to youchu_NG"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address ************ mask ***************
  service http
  action permit
 rule name CT-Cloud_to_SGW
  policy logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SGW_*********/24
  service TCP_8080
  service TCP_8081
  action permit
 rule name SGW_to_CS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service TCP_8080
  service TCP_8081
  action permit
 rule name USAP_To_SGW
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set ************/24
  destination-address address-set SGW_***********
  service http
  action permit
 rule name SGW_to_XT-SGW
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********
  source-address address-set SGW_*********
  source-address address-set SGW_*********/24
  destination-address ********* mask ***************
  service https
  action permit
 rule name "SGW to AOPS_NG"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address range *********** ***********
  service TCP_32600
  service http
  action permit
 rule name SSL_CRL
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *********
  destination-address address-set *********
  service TCP_UDP_389
  action permit
 rule name "SGW to VDI_OCS"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
 rule name SGW_to_SecOCS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address *********** mask ***************
  service http
  action permit
 rule name SGW_to_SJZT-NG-VS
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_*********/24
  destination-address address-set SJZT-NG-VS
  service TCP_19091
  service TCP_19092
  service TCP_19093
  service TCP_19094
  service TCP_19095
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 ***********
ip route-static ********* ************* *********** description TO-B_SGW
ip route-static ********* ************* *********** description TO-B_SGW
ip route-static ********* ************* *********** description TO-B_SGW
ip route-static ********* ************* *********** description TO-B_SGW
ip route-static ********* ************* *********** description TO-B_SGW
#
 sms
#
return
#
switch vsys CSLJC 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ************ ***********0
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set soc_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
 address 1 ********* mask 32
 address 2 ********* mask 32
 address 3 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set YunYingGongSi type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
#
ip address-set JunCai type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set TC_APP_address type object
 address 0 range *********** ***********
#
ip address-set Juncai_address_************ type object
 address 0 ************ mask 32
#
ip address-set DEVTEST_YYOS_VDI type object
 address 0 range ********* *********
 address 1 range ********* *********
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1097
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1098
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1099
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1100
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1101
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8890 type object 1102
 service 0 protocol tcp destination-port 8890
#
ip service-set TCP_8891 type object 1103
 service 0 protocol tcp destination-port 8891
#
ip service-set TCP_8999 type object 1104
 service 0 protocol tcp destination-port 8999
#
ip service-set TCP-139 type object 1126
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set TCP-3389 type object 1127
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_52701 type object 1133
 service 0 protocol tcp source-port 0 to 65535 destination-port 52701
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.107
 vlan-type dot1q 107
 ip binding vpn-instance CSLJC
 ip address *********6 ***************
 service-manage ping permit
#
interface Eth-Trunk1.108
 vlan-type dot1q 108
 ip binding vpn-instance CSLJC
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if8
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.108
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.107
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name soc_to_trust
  policy logging
  source-address address-set soc_***********/24
  service TCP_8890
  service TCP_8891
  service https
  service icmp
  service snmp
  service ssh
  action permit
 rule name trust_to_soc
  policy logging
  destination-address address-set soc_***********/24
  service TCP_8999
  service rdp-tcp
  service snmptrap
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "YunYingFengKong to JunCai"
  source-zone untrust
  destination-zone trust
  source-address address-set YunYingGongSi
  destination-address address-set JunCai
  service https
  action permit
 rule name SOC-1
  source-address range ************ ************
  service TCP-139
  service TCP-3389
  service ssh
  service telnet
  action permit
 rule name "SGW to TC-APP_NG"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set TC_APP_address
  destination-address address-set Juncai_address_************
  service TCP_52701
  action permit
 rule name "YYXNH to WEBDC"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ********** mask ***************
  destination-address ************ mask ***************
  service TCP_52701
  action permit
 rule name "YYXNH to SFTP"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address **********51 mask ***************
  source-address **********52 mask ***************
  source-address **********53 mask ***************
  source-address **********54 mask ***************
  source-address **********55 mask ***************
  source-address **********56 mask ***************
  source-address **********99 mask ***************
  source-address address-set DEVTEST_YYOS_VDI
  destination-address *********** mask ***************
  service ssh
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********5
ip route-static *********** ************* *********9 description CSLJC
ip route-static *********** ************* *********9 description CSLJC
#
 sms
#
return
#
switch vsys B_ELB 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range *********** ***********
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ************ ***********0
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set soc_***********/24 type object
 address 0 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
 address 1 ********* mask 32
 address 2 ********* mask 32
 address 3 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set SGW_*********/24 type object
 address 0 ********* mask 24
#
ip address-set elb_************ type object
 address 0 ************ mask 32
#
ip address-set elb_************-14 type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32
#
ip address-set payProxy_**********-214 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
 address 3 ********** mask 32
#
ip address-set payProxy_**********-202 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set payProxyNG type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32
 address 4 ************ mask 32
#
ip address-set psbc_nat_********** type object
 address 0 ********** mask 32
#
ip address-set elb_******** type object
 address 0 ******** mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1138
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1139
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1140
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1141
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1142
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_28601 type object 1143
 service 0 protocol tcp source-port 0 to 65535 destination-port 28601
#
ip service-set TCP_8890 type object 1144
 service 0 protocol tcp destination-port 8890
#
ip service-set TCP_8891 type object 1145
 service 0 protocol tcp destination-port 8891
#
ip service-set TCP_8999 type object 1146
 service 0 protocol tcp destination-port 8999
#
ip service-set TCP_6677 type object 1200
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1201
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1202
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1203
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1204
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.157
 vlan-type dot1q 157
 ip binding vpn-instance B_ELB
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.158
 vlan-type dot1q 158
 ip binding vpn-instance B_ELB
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if9
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.158
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.157
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name soc_to_trust
  policy logging
  source-address address-set soc_***********/24
  service TCP_8890
  service TCP_8891
  service https
  service icmp
  service snmp
  service ssh
  action permit
 rule name trust_to_soc
  policy logging
  destination-address address-set soc_***********/24
  service TCP_8999
  service rdp-tcp
  service snmptrap
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "SGW to ELB"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_*********/24
  destination-address address-set elb_************
  service http
  action permit
 rule name "nginx to ELB"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set elb_************-14
  destination-address address-set elb_********
  service http
  action permit
 rule name "payProxy to nginx"
  source-zone untrust
  destination-zone trust
  source-address address-set payProxy_**********-202
  destination-address address-set payProxyNG
  service TCP_28601
  action permit
 rule name "payProxyNG to psbc"
  source-zone trust
  destination-zone untrust
  source-address address-set payProxyNG
  destination-address address-set psbc_nat_**********
  service TCP_28601
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address *********** mask *************
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static *********** ************* ********** description ELB
#
 sms
#
return
#
switch vsys SGW 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
l2tp-group default-lns
#
interface Virtual-if10
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
#
firewall zone untrust
 set priority 5
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
 sms
#
return

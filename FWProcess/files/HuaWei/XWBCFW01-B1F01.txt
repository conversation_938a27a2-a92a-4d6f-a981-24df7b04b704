HRP_M<XWBCFW01-B1F01>dis cu 
2024-02-28 18:47:28.217 +08:00
!Software Version V500R005C00SPC200
#
sysname XWBCFW01-B1F01
#
 l2tp domain suffix-separator @
#
info-center source default channel 2 trap level informational
info-center loghost source GigabitEthernet0/0/0
info-center loghost *********** vpn-instance mgt_vrf
info-center loghost ***********
info-center loghost ************ vpn-instance mgt_vrf facility local6
info-center loghost ************ vpn-instance mgt_vrf
info-center loghost ************ vpn-instance mgt_vrf
info-center logbuffer size 1024
#
authentication-profile name portal_authen_default
#
 ipsec sha2 compatible enable 
#
 undo factory-configuration prohibit
#
undo telnet server enable
undo telnet ipv6 server enable
#
clock timezone Beijing add 08:00:00
#
 hrp enable
 hrp mirror config enable
 hrp interface Eth-Trunk0 remote *******
 hrp mirror session enable
 undo hrp preempt
 undo hrp track trunk-member enable
 hrp track interface Eth-Trunk1
#
 update schedule location-sdb weekly Sun 06:59
#
 firewall defend action discard
#
 log type traffic enable
 log type syslog enable
 log type policy enable
#
 undo dataflow enable
#
 undo sa force-detection enable
#
 lldp enable
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
 banner enable
#
 user-manage web-authentication security port 8887
page-setting
 user-manage security version tlsv1.1 tlsv1.2
password-policy
 level high
user-manage single-sign-on ad
user-manage single-sign-on tsm
user-manage single-sign-on radius
user-manage auto-sync online-user
#
 firewall ids authentication type aes256
#
 web-manager security version tlsv1.1 tlsv1.2
 web-manager enable
 web-manager security enable
 undo web-manager config-guide enable
#
firewall dataplane to manageplane application-apperceive default-action drop
#
 update schedule ips-sdb daily 05:07
 update schedule av-sdb daily 05:07
 update schedule sa-sdb daily 05:07
 update schedule ip-reputation daily 05:07
 update schedule cnc daily 05:07
 update schedule file-reputation daily 05:07
#
 set disk-scan parameter attach on
 set disk-scan parameter cycle 15
 set disk-scan parameter iostat 80
 set disk-scan parameter speed 10
 set disk-scan parameter switch on
 set disk-scan parameter parallel 50
 disk-usage alarm threshold 95 
#
vsys enable 
resource-class r0
resource-class core
resource-class normal
resource-class others
resource-class ilms
resource-class sgw
resource-class wsjc
#
#
vsys name Core 1
 assign interface Eth-Trunk1.201
 assign interface Eth-Trunk1.202
 assign resource-class core
#
vsys name Normal 2
 assign interface Eth-Trunk1.203
 assign interface Eth-Trunk1.204
 assign resource-class normal
#
vsys name Others 3
 assign interface Eth-Trunk1.205
 assign interface Eth-Trunk1.206
 assign resource-class others
#
vsys name ILMS 4
 assign interface Eth-Trunk1.207
 assign interface Eth-Trunk1.208
 assign resource-class ilms
#
vsys name SGW 5
 assign interface Eth-Trunk1.297
 assign interface Eth-Trunk1.298
 assign resource-class sgw
#
vsys name WSJC 6
 assign interface Eth-Trunk1.209
 assign interface Eth-Trunk1.210
 assign resource-class wsjc
#
ip vpn-instance Core
 ipv4-family
 ipv6-family
#
ip vpn-instance ILMS
 ipv4-family
#
ip vpn-instance Normal
 ipv4-family
#
ip vpn-instance Others
 ipv4-family
#
ip vpn-instance SGW
 ipv4-family
#
ip vpn-instance WSJC
 ipv4-family
#
ip vpn-instance default
 ipv4-family
#
ip vpn-instance mgt_vrf
 ipv4-family
#
radius-server template radius_server
 radius-server shared-key cipher %^%#nQh[Qv=a&$3M~nF:N`CGN]MG"'aWV=@2PJ8~!luT%^%#
 radius-server authentication ********** 1812 vpn-instance mgt_vrf source ip-address ************* weight 80
 radius-server group-filter class
#
hwtacacs-server template cslc
 hwtacacs-server authentication ********** vpn-instance mgt_vrf
 hwtacacs-server authentication ********** vpn-instance mgt_vrf secondary
 hwtacacs-server authorization ********** vpn-instance mgt_vrf
 hwtacacs-server authorization ********** vpn-instance mgt_vrf secondary
 hwtacacs-server accounting ********** vpn-instance mgt_vrf
 hwtacacs-server accounting ********** vpn-instance mgt_vrf secondary
 hwtacacs-server shared-key cipher %^%#lf@`+92t$YYY8X>p6t}E_O08448NX"4kJ7SK7r16mO76=rW_r*(<dR#6tx26%^%#
 undo hwtacacs-server user-name domain-included
#
ip address-set ntp_******* type object
 address 0 ******* mask 32
#
ip address-set jiankong type object
 address 0 *********** mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
#
ip address-set ********** type object
 address 0 ********** mask 32
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
ike proposal default
 encryption-algorithm aes-256 aes-192 aes-128 
 dh group14 
 authentication-algorithm sha2-512 sha2-384 sha2-256 
 authentication-method pre-share
 integrity-algorithm hmac-sha2-256 
 prf hmac-sha2-256 
#
web-auth-server default
 port 50100
#
portal-access-profile name default
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authentication-scheme cslc
  authentication-mode hwtacacs local
 authentication-scheme radius
  authentication-mode radius local
 authorization-scheme default
 authorization-scheme cslc
  authorization-mode hwtacacs local
  authorization-cmd 3 hwtacacs local
 accounting-scheme default
 accounting-scheme cslc
  accounting-mode hwtacacs
 domain default
  authentication-scheme cslc
  accounting-scheme cslc
  authorization-scheme cslc
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain default_admin
  authentication-scheme cslc
  accounting-scheme cslc
  authorization-scheme cslc
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain cslc
  authentication-scheme cslc
  accounting-scheme cslc
  authorization-scheme cslc
  hwtacacs-server cslc
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 domain ztc
  authentication-scheme radius
  radius-server radius_server
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 manager-user audit-admin 
  password cipher @%@%|jW7NU\&E;*D:V6)HZ3Enk0q\8[ZU#PSc=]b41NaIP-Ak0tn@%@%
  service-type web terminal 
  level 15 

 manager-user api-admin 
  password cipher @%@%eGOsN97t.!B]ha8Jh9uMSQ'GQG8;FE_G*!fqHX~8%vg7Q'JS@%@%
  service-type api 
  level 15 

 manager-user admin 
  password cipher @%@%Q0tGD],3(6%L,I;;YS#(l=0xkwCGW=HhY4hBZo//z#l5=0{l@%@%
  service-type web terminal ssh 
  level 15 
  authentication-scheme admin_local 

 manager-user netadmin 
  password cipher @%@%/S}CO!+-YUHgd$~KY0lA1#QZ(>75'|,@V6j-CF0mfw;8#Q]1@%@%
  service-type web terminal ssh 
  level 15 
  authentication-scheme admin_local 

 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
 bind manager-user audit-admin role audit-admin
 bind manager-user admin role system-admin
 bind manager-user netadmin role system-admin
#
ntp-service server disable
ntp-service ipv6 server disable
ntp-service unicast-server ******* vpn-instance mgt_vrf source-interface GigabitEthernet0/0/0
#
interface Eth-Trunk0
 description XinTiao
 ip address ******* ***************
#
interface Eth-Trunk1
 description TO XWBCSW01-B1F01-02-Eth-Trunk5-6
 mode lacp-static
#
interface Eth-Trunk1.201
 vlan-type dot1q 201
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.202
 vlan-type dot1q 202
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.203
 vlan-type dot1q 203
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.204
 vlan-type dot1q 204
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.205
 vlan-type dot1q 205
 ip binding vpn-instance Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.206
 vlan-type dot1q 206
 ip binding vpn-instance Others
 ip address *********2 ***************
 service-manage ping permit
#
interface Eth-Trunk1.207
 vlan-type dot1q 207
 ip binding vpn-instance ILMS
 ip address *********6 ***************
 service-manage ping permit
#
interface Eth-Trunk1.208
 vlan-type dot1q 208
 ip binding vpn-instance ILMS
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.209
 vlan-type dot1q 209
 ip binding vpn-instance WSJC
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.210
 vlan-type dot1q 210
 ip binding vpn-instance WSJC
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.297
 vlan-type dot1q 297
 ip binding vpn-instance SGW
 ip address *********02 ***************
 service-manage ping permit
#
interface Eth-Trunk1.298
 vlan-type dot1q 298
 ip binding vpn-instance SGW
 ip address *********06 ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface GigabitEthernet0/0/0
 undo shutdown
 ip binding vpn-instance mgt_vrf
 ip address ************* *************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage snmp permit
 service-manage telnet permit
 service-manage netconf permit
#
interface GigabitEthernet1/0/0
 undo shutdown
#
interface GigabitEthernet1/0/1
 undo shutdown
#
interface GigabitEthernet1/0/2
 undo shutdown
#
interface GigabitEthernet1/0/3
 undo shutdown
#
interface GigabitEthernet1/0/4
 undo shutdown
#
interface GigabitEthernet1/0/5
 undo shutdown
#
interface GigabitEthernet1/0/6
 undo shutdown
#
interface GigabitEthernet1/0/7
 undo shutdown
#
interface GigabitEthernet1/0/8
 undo shutdown
 eth-trunk 0
#
interface GigabitEthernet1/0/9
 undo shutdown
 eth-trunk 1
#
interface GigabitEthernet2/0/0
 undo shutdown
#
interface GigabitEthernet2/0/1
 undo shutdown
#
interface GigabitEthernet2/0/2
 undo shutdown
#
interface GigabitEthernet2/0/3
 undo shutdown
#
interface GigabitEthernet2/0/4
 undo shutdown
#
interface GigabitEthernet2/0/5
 undo shutdown
#
interface GigabitEthernet2/0/6
 undo shutdown
#
interface GigabitEthernet2/0/7
 undo shutdown
#
interface GigabitEthernet3/0/0
 undo shutdown
#
interface GigabitEthernet3/0/1
 undo shutdown
#
interface GigabitEthernet3/0/2
 undo shutdown
#
interface GigabitEthernet3/0/3
 undo shutdown
#
interface GigabitEthernet3/0/4
 undo shutdown
#
interface GigabitEthernet3/0/5
 undo shutdown
#
interface GigabitEthernet3/0/6
 undo shutdown
#
interface GigabitEthernet3/0/7
 undo shutdown
#
interface GigabitEthernet3/0/8
 undo shutdown
 eth-trunk 0
#
interface GigabitEthernet3/0/9
 undo shutdown
 eth-trunk 1
#
interface GigabitEthernet4/0/0
 undo shutdown
#
interface GigabitEthernet4/0/1
 undo shutdown
#
interface GigabitEthernet4/0/2
 undo shutdown
#
interface GigabitEthernet4/0/3
 undo shutdown
#
interface GigabitEthernet4/0/4
 undo shutdown
#
interface GigabitEthernet4/0/5
 undo shutdown
#
interface GigabitEthernet4/0/6
 undo shutdown
#
interface GigabitEthernet4/0/7
 undo shutdown
#
interface GigabitEthernet4/0/8
 undo shutdown
#
interface GigabitEthernet4/0/9
 undo shutdown
#
interface Virtual-if0
#
interface Virtual-if1
#
interface Virtual-if2
#
interface Virtual-if3
#
interface Virtual-if4
#
interface Virtual-if5
#
interface Virtual-if6
#
interface NULL0
#
interface LoopBack0
 ip address ************* ***************
 alias LoopBack0
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface GigabitEthernet0/0/0
#
firewall zone untrust
 set priority 5
#
firewall zone dmz
 set priority 50
 add interface Eth-Trunk0
#
api
#
ip route-static vpn-instance mgt_vrf 0.0.0.0 0.0.0.0 GigabitEthernet0/0/0 ************* description Out-Of-Band-Management
#
snmp-agent
snmp-agent local-engineid 800007DB0324166DB211ED
snmp-agent community read cipher %^%#)pGjK@r"H;-zeJ8lRp01V/xeN@M}=O|;do)}y"E.g^C|;74GDRtW1,,nrFW0gA4k#z=|hEy}ma>K[m<W%^%#
snmp-agent sys-info version v2c
undo snmp-agent sys-info version v3
snmp-agent target-host trap address udp-domain ************ params securityname cipher %^%#L&R|L*x+_TR3:6QN030:,wx6R}*Na~'7]FNG_3r&%^%# v2c
snmp-agent target-host trap address udp-domain *********** vpn-instance mgt_vrf params securityname cipher %^%#~ZxtM'p@M6Rs,!S^"ib,~Exy=~;l*6&&)9Vt5R(5%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#N{<qPEw_LYgcg'~3Rz)4)#qWWXt+GPv:8H10zb4P%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#SfgFTs5dD=Z7mf-Y0G/H7|>RHPtt6YQ1U|(g)f*8%^%# v2c
snmp-agent target-host trap address udp-domain ************ vpn-instance mgt_vrf params securityname cipher %^%#{4qVSc^akWb`}EMosumS>}5A%BF(|#$Q>sQ3sv"<%^%# v2c
snmp-agent mib-view included View_ALL iso
snmp-agent trap source GigabitEthernet0/0/0
snmp-agent trap enable
#
undo ssh server compatible-ssh1x enable
stelnet server enable
ssh user admin
ssh user admin authentication-type password
ssh user admin service-type all
ssh user admin sftp-directory hda1:
ssh user cslc
ssh user cslc authentication-type password
ssh user cslc service-type all
ssh user netadmin
ssh user netadmin authentication-type password
ssh user netadmin service-type all
ssh user netadmin sftp-directory hda1:
ssh client first-time enable
ssh server cipher aes256_ctr aes128_ctr 3des_cbc
#
firewall detect ftp
#
user-interface con 0
 authentication-mode aaa
user-interface vty 0 4
 authentication-mode aaa
user-interface vty 16 20
#
pki realm default
#
sa
#
location
#
multi-interface
 mode proportion-of-weight
#
right-manager server-group
#
agile-network 
#
sandbox cloud
 linkage enable
 file-set EXE max-size 2048
 file-set GZIP max-size 2048
 file-set OFFICE max-size 2048
 file-set PDF max-size 2048
#
device-classification
 device-group pc
 device-group mobile-terminal
 device-group undefined-group
#
user-manage server-sync tsm
#
security-policy
 rule name ha
  source-zone dmz
  source-zone local
  destination-zone dmz
  destination-zone local
  action permit
 rule name ntp
  destination-address address-set ntp_*******
  service icmp
  service ntp
  action permit
 rule name jiankong
  destination-address address-set jiankong
  service snmptrap
  service syslog
  action permit
 rule name snmp
  source-address address-set jiankong
  service snmp
  action permit
 rule name radius
  source-zone local
  destination-zone trust
  destination-address address-set **********
  service radius
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
dns-transparent-policy
 mode based-on-multi-interface
#
rightm-policy
#
decryption-policy
#
mac-access-profile name mac_access_profile
#
 sms
#
return
#
switch vsys Core 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ***********0
 address 4 range ************ ************
 address 5 ********* mask 24
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1026
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1027
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1036
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1040
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1041
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1069
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1070
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1071
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-139 type object 1089
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1090
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_6677 type object 1119
 service 0 protocol tcp source-port 0 to 65535 destination-port 6677
#
ip service-set TCP_7788 type object 1120
 service 0 protocol tcp source-port 0 to 65535 destination-port 7788
#
ip service-set TCP_8001 type object 1121
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_8002 type object 1122
 service 0 protocol tcp source-port 0 to 65535 destination-port 8002
#
ip service-set TCP_8443 type object 1123
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.201
 vlan-type dot1q 201
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage ping permit
#
interface Eth-Trunk1.202
 vlan-type dot1q 202
 ip binding vpn-instance Core
 ip address ********* ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if1
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.202
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.201
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name ntp_service
  source-zone untrust
  destination-zone trust
  destination-address ************ mask ***************
  service ntp
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ***********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static *********** ************* ********* description TO-Core
#
 sms
#
return
#
switch vsys Normal 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1028
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1029
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1037
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1042
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1043
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1072
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1073
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1074
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-139 type object 1091
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1092
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.203
 vlan-type dot1q 203
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.204
 vlan-type dot1q 204
 ip binding vpn-instance Normal
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if2
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.204
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.203
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********
ip route-static *********** ************* *********3 description TO-Normal
#
 sms
#
return
#
switch vsys Others 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set host_************ type object
 address 0 ************ mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1030
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1031
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1038
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1044
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1045
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP-8890 type object 1075
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1076
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1077
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_8080 type object 1088
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set tcp-139 type object 1093
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1094
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.205
 vlan-type dot1q 205
 ip binding vpn-instance Others
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.206
 vlan-type dot1q 206
 ip binding vpn-instance Others
 ip address *********2 ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if3
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.206
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.205
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  disable
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  disable
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  disable
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  disable
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  disable
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  disable
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  disable
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  disable
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "saltstack master"
  disable
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  disable
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name SOC
  disable
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  disable
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name NVS
  disable
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name ump
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set host_************
  service TCP_8080
  action permit
 rule name sso
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set host_************
  service http
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********7
ip route-static *********** ************* *********1 description TO-Others
#
 sms
#
return
#
switch vsys ILMS 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set ILMS_**********/24 type object
 address 0 ********** mask 24
#
ip address-set ȫϵͳ type object
 address 0 range ********** **********
 address 1 range **********1 **********0
#
ip address-set Monitor_ZabbixServers type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************1 mask 32
#
ip address-set Monitor_ZabbixProxy type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************2 mask 32
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
 address 1 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set host_********** type object
 description YouXiDaoRuGongJu
 address 0 ********** mask 32
#
ip address-set ILMS_QianZhi_group type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 *********** mask 32
#
ip address-set ILMS_QianZhi_C_group type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set CAS_********** type object
 address 0 ********** mask 32
#
ip address-set CAS_********** type object
 address 0 ********** mask 32
#
ip address-set ILMS_GengXin_group type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set ILMS_QianZhi_VIP type object
 address 0 *********** mask 32
#
ip address-set QianZhiDB_**********-22 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
#
ip address-set SGW_*********** type object
 address 0 *********** mask 24
#
ip address-set ILMS_C_vip_*********** type object
 address 0 *********** mask 32
#
ip address-set ILMS_UPDATE_vip_*********** type object
 address 0 *********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ********** type object
 address 0 ********** mask 32
#
ip address-set **********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set Monitor_Zabbix type group
 address 0 address-set Monitor_ZabbixServers
 address 1 address-set Monitor_ZabbixProxy
#
ip service-set TCP_10051 type object 1024
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set TCP_10050 type object 1025
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set "NAS service port" type object 1039
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1046
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1047
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8443 type object 1050
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP_20050 type object 1060
 service 0 protocol tcp source-port 0 to 65535 destination-port 20050
#
ip service-set TCP_6379 type object 1061
 service 0 protocol tcp source-port 0 to 65535 destination-port 6379
#
ip service-set TCP-8890 type object 1078
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1079
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1080
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-139 type object 1095
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1096
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_6677 type object 1109
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1110
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1111
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1112
 service 0 protocol tcp destination-port 8002
#
ip service-set udp-1812 type object 1118
 service 0 protocol udp source-port 0 to 65535 destination-port 1812
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.207
 vlan-type dot1q 207
 ip binding vpn-instance ILMS
 ip address *********6 ***************
 service-manage ping permit
#
interface Eth-Trunk1.208
 vlan-type dot1q 208
 ip binding vpn-instance ILMS
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if4
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.208
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.207
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "Internet to ILMS"
  source-zone untrust
  destination-zone trust
  destination-address address-set ILMS_C_vip_***********
  destination-address address-set ILMS_QianZhi_VIP
  destination-address address-set ILMS_UPDATE_vip_***********
  service TCP_8443
  action permit
 rule name DaoRuGongJu_Client
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_**********
  source-address address-set host_**********
  destination-address address-set ILMS_GengXin_group
  destination-address address-set ILMS_QianZhi_C_group
  destination-address address-set ILMS_QianZhi_VIP
  destination-address address-set ILMS_QianZhi_group
  service TCP_8443
  action permit
 rule name "to CAS"
  source-zone trust
  destination-zone untrust
  source-address address-set ILMS_QianZhi_C_group
  source-address address-set ILMS_QianZhi_group
  destination-address address-set CAS_**********
  destination-address address-set CAS_**********
  service TCP_20050
  action permit
 rule name "to QianZhiDB"
  source-zone trust
  destination-zone untrust
  source-address address-set ILMS_QianZhi_C_group
  source-address address-set ILMS_QianZhi_group
  destination-address address-set QianZhiDB_**********-22
  service TCP_6379
  action permit
 rule name "CAS to QianZhi"
  source-zone untrust
  destination-zone trust
  source-address address-set CAS_**********
  destination-address address-set ILMS_QianZhi_C_group
  destination-address address-set ILMS_QianZhi_group
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name radius
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set ILMS_**********/24
  destination-address address-set **********
  service udp-1812
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********5
ip route-static ********** ************* *********9 description TO-ILMS
#
 sms
#
return
#
switch vsys SGW 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 range *********** ***********
 address 3 range ***********1 ***********0
 address 4 range ************ ************
 address 5 range *********** ***********
 address 6 *********** mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set SGW_***********/24 type object
 address 0 *********** mask 24
#
ip address-set LDAP_********* type object
 address 0 ********* mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_********* type object
 address 0 ********* mask 32
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set "SIE Group5_**********" type object
 address 0 ********** mask 32
#
ip address-set QH_IDP_********** type object
 address 0 ********** mask 32
#
ip address-set "SIE Group1_**********" type object
 address 0 ********** mask 32
#
ip address-set "SIE Group2_**********" type object
 address 0 ********** mask 32
#
ip address-set "SIE Group3_**********" type object
 address 0 ********** mask 32
#
ip address-set "SIE Group4_**********" type object
 address 0 ********** mask 32
#
ip address-set IDP_Group1 type object
 description SX,NM,LN,HL,SD,HA
 address 0 *********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
 address 3 ********** mask 32
#
ip address-set IDP_Group2 type object
 description FJ,GD,GX,SC,XZ,NX
 address 0 *********** mask 32
 address 1 ********** mask 32
#
ip address-set IDP_Group3 type object
 description SH,JS,ZJ,AH,HB
 address 0 *********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
#
ip address-set IDP_Group4 type object
 description BJ,TJ,JX,CQ,GZ,YN
 address 0 *********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
#
ip address-set IDP_Group6 type object
 description JL,QH
 address 0 *********** mask 32
 address 1 ********** mask 32
#
ip address-set IDP_Group7 type object
 description HE,HN,HI,SN,GS,XJ
 address 0 *********** mask 32
 address 1 ********** mask 32
 address 2 *********** mask 32
 address 3 ********** mask 32
 address 4 ********** mask 32
#
ip address-set ILFS_************* type object
 address 0 ************* mask 32
#
ip address-set ILFS_C_************* type object
 address 0 ************* mask 32
#
ip address-set ILMS_UPDATE_************* type object
 address 0 ************* mask 32
#
ip address-set ILMS_*********** type object
 address 0 *********** mask 32
#
ip address-set ILMS_*********** type object
 address 0 *********** mask 32
#
ip address-set ILMS_*********** type object
 address 0 *********** mask 32
#
ip address-set WSJC_************* type object
 address 0 ************* mask 32
#
ip address-set WSJC_************* type object
 address 0 ************* mask 32
#
ip address-set WSJC_Nginx_VIP type object
 address 0 *********** mask 32
#
ip address-set WSJC_mailServer type object
 address 0 *********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip address-set ********* type object
 address 0 ********* mask 32
#
ip service-set TCP_10050 type object 1032
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1033
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set TCP_389 type object 1034
 service 0 protocol tcp source-port 0 to 65535 destination-port 389
#
ip service-set "NAS service port" type object 1035
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1048
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1049
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_8500 type object 1051
 service 0 protocol tcp source-port 0 to 65535 destination-port 8500
#
ip service-set TCP_8600 type object 1052
 service 0 protocol tcp source-port 0 to 65535 destination-port 8600
#
ip service-set TCP_20050 type object 1053
 service 0 protocol tcp source-port 0 to 65535 destination-port 20050
#
ip service-set TCP_8091 type object 1054
 service 0 protocol tcp source-port 0 to 65535 destination-port 8091
#
ip service-set TCP_8443 type object 1062
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP-8890 type object 1081
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1082
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1083
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set tcp-139 type object 1097
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1098
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_30000 type object 1101
 service 0 protocol tcp destination-port 30000
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
acl number 3000
 description "Acl for Quintuple Packet Capture"
 rule 0 permit tcp source *********** ********* 
 rule 1 permit tcp destination *********** ********* 
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.297
 vlan-type dot1q 297
 ip binding vpn-instance SGW
 ip address *********02 ***************
 service-manage ping permit
#
interface Eth-Trunk1.298
 vlan-type dot1q 298
 ip binding vpn-instance SGW
 ip address *********06 ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if5
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.298
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.297
#
firewall zone dmz
 set priority 50
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_*********
  service "NAS service port"
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set CIMS_Servers
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "internet to TLS ShouPiao"
  policy logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SGW_***********/24
  service TCP_8443
  service TCP_8500
  action permit
 rule name "internet to TLS IDP"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SGW_***********/24
  service TCP_8600
  action permit
 rule name "internet to ILFS"
  source-zone untrust
  destination-zone trust
  destination-address address-set ILFS_*************
  service TCP_8443
  action permit
 rule name "internet to ILFS_C"
  source-zone untrust
  destination-zone trust
  destination-address address-set ILFS_C_*************
  service TCP_8443
  action permit
 rule name "internet to ILMS_UPDATE"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set ILMS_UPDATE_*************
  service TCP_8443
  action permit
 rule name "internet to WSJC"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set WSJC_*************
  destination-address address-set WSJC_*************
  service https
  action permit
 rule name "internet to  SSL"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set SGW_***********/24
  service https
  action permit
 rule name "SGW to LDAP"
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set *********
  destination-address address-set LDAP_*********
  service TCP_389
  action permit
 rule name "SGW to SIE Group1"
  description BJ,TJ,SX,NM,LN,JL,HL,SD,HA,GS,NX,XJ
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set "SIE Group1_**********"
  service TCP_20050
  action permit
 rule name "SGW to SIE Group2"
  description SH,JS,ZJ,AH,HB,GX,CQ,XZ
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set "SIE Group2_**********"
  service TCP_20050
  action permit
 rule name "SGW to SIE Group3"
  description HE,HN,HI
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set "SIE Group3_**********"
  service TCP_20050
  action permit
 rule name "SGW to SIE Group4"
  description FJ,JX,GD,SC,GZ,YN,SN
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set "SIE Group4_**********"
  service TCP_20050
  action permit
 rule name "SGW to SIE Group5"
  description QH
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set "SIE Group5_**********"
  service TCP_20050
  action permit
 rule name "SGW to IDP Group1"
  description SX,NM,LN,HL,SD,HA
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set IDP_Group1
  service TCP_8091
  action permit
 rule name "SGW to IDP Group2"
  description FJ,GD,GX,SC,XZ,NX
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set IDP_Group2
  service TCP_8091
  action permit
 rule name "SGW to IDP Group3"
  description SH,JS,ZJ,AH,HB
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set IDP_Group3
  service TCP_8091
  action permit
 rule name "SGW to IDP Group4"
  description BJ,TJ,JX,CQ,GZ,YN
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set IDP_Group4
  service TCP_8091
  action permit
 rule name "SGW to IDP Group6"
  description JL,QH
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set IDP_Group6
  service TCP_8091
  action permit
 rule name "SGW to IDP Group7"
  description HE,HN,HI,SN,GS,XJ
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set IDP_Group7
  service TCP_8091
  action permit
 rule name "SGW to ILMS"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set ILMS_***********
  destination-address address-set ILMS_***********
  destination-address address-set ILMS_***********
  service TCP_8443
  action permit
 rule name "SGW to WSJC_Nginx"
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set WSJC_Nginx_VIP
  service http
  action permit
 rule name "SGW to WSJC_mail"
  disable
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address address-set WSJC_mailServer
  service http
  service https
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name SGW_to_G3
  description G3_gateway
  source-zone trust
  destination-zone untrust
  source-address address-set SGW_***********/24
  destination-address ********** mask ***************
  service TCP_30000
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 *********01
ip route-static *********** ************* *********05 description TO-SGW
ip route-static *********** ************* *********05 description TO-SGW
ip route-static *********** ************* *********05 description TO-SGW
ip route-static *********** ************* *********05 description TO-SGW
ip route-static *********** ************* *********05 description TO-SGW
ip route-static *********** ************* *********05 description TO-SGW
ip route-static *********** ************* *********05 description TO-SGW
ip route-static *********** ************* *********05 description TO-SGW
#
 sms
#
return
#
switch vsys WSJC 
#
 l2tp domain suffix-separator @
#
 firewall defend action discard
#
 isp name "china mobile" set filename china-mobile.csv 
 isp name "china unicom" set filename china-unicom.csv 
 isp name "china telecom" set filename china-telecom.csv 
 isp name "china educationnet" set filename china-educationnet.csv 
#
page-setting
password-policy
 level high
#
ip address-set host_********* type object
 description sytem ops
 address 0 ********* mask 32
#
ip address-set CIMS_Servers type object
 address 0 range ********** **********
 address 1 range **********1 **********0
 address 2 ********* mask 24
 address 3 range *********** ***********
 address 4 range ***********1 ***********0
 address 5 range ************ ************
 address 6 range *********** ***********
 address 7 *********** mask 32
#
ip address-set NTP_Server type object
 address 0 ******* mask 32
 address 1 ******* mask 32
#
ip address-set "YUM Server" type object
 address 0 ************* mask 32
 address 1 *********** mask 32
#
ip address-set NAS_group type object
 address 0 ********* mask 32
#
ip address-set Zabbix_JianKong type object
 address 0 range ************ ************
 address 1 range ************1 ************2
 address 2 ************8 mask 32
 address 3 range ************ ************
 address 4 *********** mask 24
#
ip address-set "Saltstack Master" type object
 address 0 ************ mask 32
 address 1 ************ mask 32
#
ip address-set net_***********/24 type object
 address 0 *********** mask 24
#
ip address-set WSJC_Nginx type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set WSJC_Nginx_VIP type object
 address 0 *********** mask 32
#
ip address-set WSJC_Nginx_OUT type object
 address 0 *********** mask 32
#
ip address-set WSJC_mail type object
 address 0 *********** mask 32
#
ip address-set jueCe_******** type object
 address 0 ******** mask 16
#
ip address-set jueCe_sendMail type object
 address 0 *********** mask 32
 address 1 ********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 *********** mask 32
#
ip address-set jueCe_Edge type object
 address 0 ********** mask 32
#
ip address-set baiDuDNS type object
 address 0 ************ mask 32
#
ip address-set aliDNS type object
 address 0 ********* mask 32
#
ip address-set SGW_4.103.211.0/24 type object
 address 0 *********** mask 24
#
ip address-set docker_**********-38 type object
 address 0 range ********** **********
#
ip address-set mailManage type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 ********** mask 32
 address 3 ********** mask 32
 address 4 ********** mask 32
 address 5 *********** mask 32
#
ip address-set ***********/24 type object
 address 0 *********** mask 24
#
ip address-set ZJYC_************** type object
 address 0 ************** mask 32
#
ip address-set NVS type object
 address 0 ************* mask 32
#
ip address-set ************-************ type object
 address 0 range ************ ************
#
ip address-set docker_********* type object
 address 0 ********* mask 24
#
ip address-set CXweb type object
 address 0 *********** mask 32
#
ip address-set host_********** type object
 address 0 ********** mask 32
#
ip address-set **********/24 type object
 address 0 *********** mask 24
#
ip address-set ************ type object
 address 0 ************ mask 32
#
ip address-set ************* type object
 address 0 ************* mask 32
#
ip address-set ********* type object
 address 0 ********* mask 16
#
ip address-set ********** type object
 address 0 ********** mask 24
#
ip service-set TCP_10050 type object 1055
 description Zabbix
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_10051 type object 1056
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#
ip service-set "NAS service port" type object 1057
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
 service 2 protocol tcp source-port 0 to 65535 destination-port 2049
 service 3 protocol udp source-port 0 to 65535 destination-port 2049
 service 4 protocol tcp source-port 0 to 65535 destination-port 4046
 service 5 protocol udp source-port 0 to 65535 destination-port 4046
 service 6 protocol tcp source-port 0 to 65535 destination-port 635
 service 7 protocol udp source-port 0 to 65535 destination-port 635
#
ip service-set TCP_4505 type object 1058
 service 0 protocol tcp source-port 0 to 65535 destination-port 4505
#
ip service-set TCP_4506 type object 1059
 service 0 protocol tcp source-port 0 to 65535 destination-port 4506
#
ip service-set TCP_10001 type object 1063
 service 0 protocol tcp source-port 0 to 65535 destination-port 10001
#
ip service-set TCP_10002 type object 1064
 service 0 protocol tcp source-port 0 to 65535 destination-port 10002
#
ip service-set TCP_10003 type object 1065
 service 0 protocol tcp source-port 0 to 65535 destination-port 10003
#
ip service-set TCP_10004 type object 1066
 service 0 protocol tcp source-port 0 to 65535 destination-port 10004
#
ip service-set TCP_10005 type object 1067
 service 0 protocol tcp source-port 0 to 65535 destination-port 10005
#
ip service-set TCP_8080 type object 1068
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP-8890 type object 1084
 service 0 protocol tcp source-port 0 to 65535 destination-port 8890
#
ip service-set TCP-8891 type object 1085
 service 0 protocol tcp source-port 0 to 65535 destination-port 8891
#
ip service-set TCP-8999 type object 1086
 service 0 protocol tcp source-port 0 to 65535 destination-port 8999
#
ip service-set TCP_10006 type object 1087
 service 0 protocol tcp source-port 0 to 65535 destination-port 10006
#
ip service-set tcp-139 type object 1099
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
#
ip service-set tcp-3389 type object 1100
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set TCP_10009 type object 1102
 service 0 protocol tcp source-port 0 to 65535 destination-port 10009
#
ip service-set TCP_10007 type object 1103
 service 0 protocol tcp source-port 0 to 65535 destination-port 10007
#
ip service-set TCP_10008 type object 1104
 service 0 protocol tcp source-port 0 to 65535 destination-port 10008
#
ip service-set TCP_18081 type object 1105
 service 0 protocol tcp source-port 0 to 65535 destination-port 18081
#
ip service-set TCP_10010 type object 1106
 service 0 protocol tcp source-port 0 to 65535 destination-port 10010
#
ip service-set TCP_10012 type object 1107
 service 0 protocol tcp source-port 0 to 65535 destination-port 10012
#
ip service-set TCP_10013 type object 1108
 service 0 protocol tcp source-port 0 to 65535 destination-port 10013
#
ip service-set TCP_6677 type object 1113
 service 0 protocol tcp destination-port 6677
#
ip service-set TCP_7788 type object 1114
 service 0 protocol tcp destination-port 7788
#
ip service-set TCP_8001 type object 1115
 service 0 protocol tcp destination-port 8001
#
ip service-set TCP_8002 type object 1116
 service 0 protocol tcp destination-port 8002
#
ip service-set TCP_8443 type object 1117
 service 0 protocol tcp destination-port 8443
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day   
#
acl number 3001
 description "Acl for Quintuple Packet Capture"
 rule 0 permit ip source 4.102.211.254 0 
 rule 1 permit ip destination 4.102.211.254 0 
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authorization-scheme default
 accounting-scheme default
 domain default
  service-type internetaccess ssl-vpn l2tp ike
  internet-access mode password
  reference user current-domain
 role system-admin
 role device-admin
 role device-admin(monitor)
 role audit-admin
#
interface Eth-Trunk1.209
 vlan-type dot1q 209
 ip binding vpn-instance WSJC
 ip address ********** ***************
 service-manage ping permit
#
interface Eth-Trunk1.210
 vlan-type dot1q 210
 ip binding vpn-instance WSJC
 ip address ********** ***************
 service-manage ping permit
#
l2tp-group default-lns
#
interface Virtual-if6
#
sa
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
 add interface Eth-Trunk1.210
#
firewall zone untrust
 set priority 5
 add interface Eth-Trunk1.209
#
firewall zone dmz
 set priority 50
#
 domain-set name URL 
  add domain sdk.entinfo.cn 
  add domain www.dh3t.com 
  add domain api.netease.im 
  add domain www.cwl.gov.cn 
  add domain www.zhcw.com 
  add domain ocr.tencentcloudapi.com 
  add domain www.henanfucai.com 
  add domain www.gxcaipiao.com.cn 
  add domain sjcj.tyj.zj.gov.cn 
  add domain bes.caitong.sina.com.cn 
#
location
#
multi-interface
 mode proportion-of-weight
#
security-policy
 default policy logging
 rule name icmp
  description permit icmp
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  service icmp
  action permit
 rule name SOC
  source-zone local
  source-zone trust
  source-zone untrust
  destination-zone trust
  source-address address-set ***********/24
  service TCP-8890
  service TCP-8891
  service https
  service icmp
  service snmptrap
  service ssh
  action permit
 rule name soc
  source-zone trust
  destination-zone local
  destination-zone trust
  destination-zone untrust
  destination-address address-set ***********/24
  service TCP-8999
  service rdp-tcp
  service rdp-udp
  service snmp
  service syslog
  action permit
 rule name CIMS-Management
  description permit Citrix management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address ***********50 mask ***************
  source-address address-set CIMS_Servers
  action permit
 rule name Sysops-Management
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set host_*********
  service ssh
  action permit
 rule name Zabbix_JianKong
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set Zabbix_JianKong
  service TCP_10050
  action permit
 rule name Zabbix_Jiankong
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set Zabbix_JianKong
  service TCP_10051
  service snmptrap
  service syslog
  action permit
 rule name ntp
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NTP_Server
  service ntp
  action permit
 rule name yum
  source-zone trust
  destination-zone untrust
  destination-address address-set "YUM Server"
  service http
  action permit
 rule name NAS
  policy logging
  source-zone trust
  destination-zone untrust
  destination-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "NAS duplexing"
  policy logging
  source-zone untrust
  destination-zone trust
  source-address address-set NAS_group
  service "NAS service port"
  action permit
 rule name "saltstack master"
  source-zone trust
  destination-zone untrust
  destination-address address-set "Saltstack Master"
  service TCP_4505
  service TCP_4506
  action permit
 rule name "snmp get"
  source-zone untrust
  destination-zone trust
  source-address address-set net_***********/24
  service snmp
  action permit
 rule name "SGW to Nginx"
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_4.103.211.0/24
  destination-address address-set WSJC_Nginx_VIP
  service http
  action permit
 rule name "SGW to mailServer"
  disable
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set SGW_4.103.211.0/24
  destination-address address-set WSJC_mail
  service http
  service https
  action permit
 rule name "Internet to mailServer"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  destination-address address-set WSJC_mail
  service imap
  service pop3
  service smtp
  action permit
 rule name "nginx to jueCe"
  source-zone trust
  destination-zone untrust
  source-address address-set WSJC_Nginx
  destination-address address-set docker_**********-38
  destination-address address-set jueCe_********
  service http
  service https
  action permit
 rule name "jueCe to Nginx_OUT"
  source-zone untrust
  destination-zone trust
  source-address address-set jueCe_Edge
  destination-address address-set WSJC_Nginx_OUT
  service TCP_10001
  service TCP_10002
  service TCP_10003
  service TCP_10004
  service TCP_10005
  service TCP_10006
  service TCP_10007
  service TCP_10008
  service TCP_10009
  service TCP_10012
  service http
  service https
  action permit
 rule name "jueCe to mailServer"
  source-zone untrust
  destination-zone trust
  source-address address-set docker_**********-38
  source-address address-set jueCe_sendMail
  destination-address address-set WSJC_mail
  service imap
  service pop3
  service smtp
  action permit
 rule name "Nginx_OUT to internet"
  source-zone trust
  destination-zone untrust
  source-address address-set WSJC_Nginx_OUT
  destination-address domain-set URL
  service dns
  service http
  service https
  action permit
 rule name "Nginx_OUT to internetMail"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set WSJC_mail
  service smtp
  action permit
 rule name "Nginx_OUT to DNS"
  source-zone trust
  destination-zone untrust
  source-address address-set WSJC_Nginx_OUT
  source-address address-set WSJC_mail
  destination-address address-set aliDNS
  destination-address address-set baiDuDNS
  service dns
  action permit
 rule name "mailManage to mailServer"
  policy logging
  session logging
  source-zone untrust
  destination-zone trust
  source-address address-set mailManage
  destination-address address-set WSJC_mail
  service TCP_8080
  service http
  service smtp
  action permit
 rule name "Nginx_OUT to ZJ_YC"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set WSJC_Nginx_OUT
  destination-address address-set ZJYC_**************
  service http
  action permit
 rule name NVS
  source-zone untrust
  destination-zone trust
  source-address address-set NVS
  action permit
 rule name soc-1
  source-address address-set ************-************
  service ssh
  service tcp-139
  service tcp-3389
  service telnet
  action permit
 rule name Docker_to_WS-NG-OUT
  source-zone untrust
  destination-zone trust
  source-address address-set docker_*********
  destination-address address-set WSJC_Nginx_OUT
  service TCP_10009
  action permit
 rule name "Nginx_OUT to CXweb"
  policy logging
  session logging
  source-zone trust
  destination-zone untrust
  source-address address-set WSJC_Nginx_OUT
  destination-address address-set CXweb
  service TCP_18081
  action permit
 rule name Edge-server_to_WS-NG-OUT
  source-zone untrust
  destination-zone trust
  source-address address-set host_**********
  destination-address address-set WSJC_Nginx_OUT
  service TCP_10010
  service TCP_10013
  action permit
 rule name EDR_to_Agent
  policy logging
  source-zone trust
  destination-zone untrust
  source-address ************ mask ***************
  destination-address ***********50 mask ***************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  action permit
 rule name zhuji_to_EDR
  policy logging
  source-zone trust
  destination-zone untrust
  source-address address-set **********/24
  destination-address address-set ************
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service TCP_8443
  service http
  service https
  action permit
 rule name OCS_to_*********
  source-zone untrust
  destination-zone trust
  source-address address-set **********
  destination-address address-set *********
  service ssh
  action permit
 rule name *********_to_syslog
  source-zone trust
  destination-zone untrust
  source-address address-set *********
  destination-address address-set *************
  service syslog
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
#
audit-policy
#
proxy-policy
#
quota-policy
#
pcp-policy
#
decryption-policy
#
ip route-static 0.0.0.0 0.0.0.0 **********
ip route-static ********** ************* ********** description WeiShengJueCe
#
 sms
#
return

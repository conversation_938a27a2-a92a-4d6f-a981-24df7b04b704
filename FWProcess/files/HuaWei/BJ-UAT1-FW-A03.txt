<BJ-UAT1-FW-A03>
<BJ-UAT1-FW-A03>
<BJ-UAT1-FW-A03>
<BJ-UAT1-FW-A03>dis cu 
09:37:46  2023/11/21
#
 l2tp domain suffix-separator @
#
 info-center source AUDIT channel 0 log state off
 info-center source DLP channel 0 log state off
 info-center source URL channel 0 log state off
 info-center source AV channel 0 log state off
 info-center source APPCTL channel 0 log state off
 info-center source RBL channel 0 log state off
 info-center source IPS channel 0 log state off
 info-center source DDOS channel 0 log state off
 info-center source MAILFLT channel 0 log state off
 info-center source UM channel 0 log state off
 info-center source AUDIT channel 1 log state off
 info-center source DLP channel 1 log state off
 info-center source URL channel 1 log state off
 info-center source AV channel 1 log state off
 info-center source APPCTL channel 1 log state off
 info-center source RBL channel 1 log state off
 info-center source IPS channel 1 log state off
 info-center source DDOS channel 1 log state off
 info-center source MAILFLT channel 1 log state off
 info-center source UM channel 1 log state off
 info-center source AUDIT channel 2 log state off
 info-center source DLP channel 2 log state off
 info-center source URL channel 2 log state off
 info-center source AV channel 2 log state off
 info-center source APPCTL channel 2 log state off
 info-center source RBL channel 2 log state off
 info-center source IPS channel 2 log state off
 info-center source DDOS channel 2 log state off
 info-center source MAILFLT channel 2 log state off
 info-center source AUDIT channel 4 log state off
 info-center source DLP channel 4 log state off
 info-center source URL channel 4 log state off
 info-center source AV channel 4 log state off
 info-center source APPCTL channel 4 log state off
 info-center source RBL channel 4 log state off
 info-center source IPS channel 4 log state off
 info-center source DDOS channel 4 log state off
 info-center source MAILFLT channel 4 log state off
 info-center source UM channel 4 log state off
 info-center source AUDIT channel 6 log state off
 info-center source DLP channel 6 log state off
 info-center source URL channel 6 log state off
 info-center source AV channel 6 log state off
 info-center source APPCTL channel 6 log state off
 info-center source RBL channel 6 log state off
 info-center source IPS channel 6 log state off
 info-center source DDOS channel 6 log state off
 info-center source MAILFLT channel 6 log state off
 info-center source UM channel 6 log state off
 info-center source AUDIT channel 7 log state off
 info-center source DLP channel 7 log state off
 info-center source URL channel 7 log state off
 info-center source AV channel 7 log state off
 info-center source APPCTL channel 7 log state off
 info-center source RBL channel 7 log state off
 info-center source IPS channel 7 log state off
 info-center source DDOS channel 7 log state off
 info-center source MAILFLT channel 7 log state off
 info-center source UM channel 7 log state off
 info-center source AUDIT channel 8 log state off
 info-center source DLP channel 8 log state off
 info-center source URL channel 8 log state off
 info-center source AV channel 8 log state off
 info-center source APPCTL channel 8 log state off
 info-center source RBL channel 8 log state off
 info-center source IPS channel 8 log state off
 info-center source DDOS channel 8 log state off
 info-center source MAILFLT channel 8 log state off
 info-center source UM channel 8 log state off
 info-center source AUDIT channel 9 log state off
 info-center source DLP channel 9 log state off
 info-center source URL channel 9 log state off
 info-center source AV channel 9 log state off
 info-center source APPCTL channel 9 log state off
 info-center source RBL channel 9 log state off
 info-center source IPS channel 9 log state off
 info-center source DDOS channel 9 log state off
 info-center source MAILFLT channel 9 log state off
 info-center source UM channel 9 log state off
 info-center loghost source GigabitEthernet0/0/0
 undo info-center enable
#
 cpu-usage-mgmtplane alarm threshold enable
 cpu-usage-dataplane alarm threshold enable
#
 undo hrp ospfv3-cost adjust-enable
#
 nat server ************ global ************ inside ************ no-reverse
 nat server ************ global ************ inside ************ no-reverse
 nat server SDN zone egress global ******** ********** inside ******** ********** no-reverse
 nat server ********** zone egress global ********** inside ********** no-reverse
 nat server ********* zone egress global ********** inside ********* no-reverse
 nat server ********* zone egress global ********** inside ********* no-reverse
 nat server ********* zone egress global ********** inside ********* no-reverse
 nat server ********* zone egress global ********** inside ********* no-reverse
 nat server ********* zone egress global ********** inside ********* no-reverse
 nat server ************ zone egress global ********** inside ************ no-reverse
 nat server ********* global *********8 inside ********* no-reverse
 nat server ********** global *********9 inside ********** no-reverse
 nat server "T5 38.0.160.39" global *********5 inside 38.0.160.39 no-reverse
 nat server "G3 VDI要开通到骏彩T5环境BOS" global *********6 inside 38.0.160.59 no-reverse
 nat server 48.0.147.2 global *********2 inside 48.0.147.2 no-reverse
 nat server T5********* global *********3 inside ********* no-reverse
 nat server T5*********** global *********4 inside *********** no-reverse
 nat server 18.0.185.12 protocol tcp global *********7 8086 inside 18.0.185.12 8086 no-reverse
 nat server 18.0.185.39 protocol tcp global *********8 8088 inside 18.0.185.39 8088 no-reverse
 nat server ********29_8080 protocol tcp global *********7 8080 inside ********29 8080 no-reverse
 nat server NAT********** protocol tcp global *********9 28080 inside ********** 28080 no-reverse
 nat server *********0_18.0.95.94 protocol tcp global *********0 3388 inside 18.0.95.94 3389 no-reverse
 nat server *********0_18.0.95.95 protocol tcp global *********0 3389 inside 18.0.95.95 3389 no-reverse
 nat server *********0_18.0.95.97 protocol tcp global *********0 22 inside 18.0.95.97 22 no-reverse
 nat server *********0_18.0.95.98 protocol tcp global *********0 telnet inside 18.0.95.98 22 no-reverse
 nat server *********0_18.0.95.99 protocol tcp global *********0 24 inside 18.0.95.99 22 no-reverse
 nat server *********0_18.0.95.100 protocol tcp global *********0 smtp inside 18.0.95.100 22 no-reverse
 nat server *********0_18.0.95.100:443 protocol tcp global *********0 443 inside 18.0.95.100 443 no-reverse
 nat server *********0_18.0.95.99:443 protocol tcp global *********0 444 inside 18.0.95.99 443 no-reverse
 nat server *********0_18.0.95.99:5601 protocol tcp global *********0 5601 inside 18.0.95.99 5601 no-reverse
 nat server *********0:5602_18.0.95.100:5601 protocol tcp global *********0 5602 inside 18.0.95.100 5601 no-reverse
 nat server *********0_514 protocol tcp global *********0 cmd inside 18.0.95.100 cmd no-reverse
 nat server *********1 protocol tcp global *********1 30024 inside 18.0.95.97 30024 no-reverse
 nat server *********2 global *********2 inside ********** no-reverse
 nat server *********3 global *********3 inside 18.4.255.11 no-reverse
 nat server 18.0.80.64 protocol tcp global *********9 7001 inside 18.0.80.64 7001 no-reverse
 nat server 18.0.80.12 protocol tcp global *********9 8086 inside 18.0.80.12 8086 no-reverse
 nat server 18.0.80.39 protocol tcp global *********9 3558 inside 18.0.80.39 3558 no-reverse
 nat server 18.0.80.36 protocol tcp global *********9 3555 inside 18.0.80.36 3555 no-reverse
 nat server 18.0.80.96 protocol tcp global *********9 3556 inside 18.0.80.96 3555 no-reverse
 nat server 18.0.80.40 protocol tcp global *********9 31306 inside 18.0.80.40 31306 no-reverse
 nat server 18.0.80.2 protocol tcp global *********9 30001 inside 18.0.80.2 30001 no-reverse
 nat server 18.0.80.10 protocol tcp global *********9 22 inside 18.0.80.10 22 no-reverse
 nat server 18.0.80.65 protocol tcp global *********9 7002 inside 18.0.80.65 7001 no-reverse
 nat server 18.0.80.66 protocol tcp global *********9 7003 inside 18.0.80.66 7001 no-reverse
 nat server 18.0.80.11:8082 protocol tcp global *********9 8082 inside 18.0.80.11 8082 no-reverse
 nat server 18.0.80.11:8092 protocol tcp global *********9 8092 inside 18.0.80.11 8092 no-reverse
 nat server ***********_20230630删除 global *********4 inside *********** no-reverse
 nat server *********** global *********5 inside *********** no-reverse
 nat server 18.0.92.50_58 global *********6 *********4 inside 18.0.92.50 18.0.92.58 no-reverse
 nat server ********** protocol tcp global *********5 28080 inside ********** 28080 no-reverse
 nat server ******** protocol tcp global *********5 389 inside ******** 389 no-reverse
 nat server 18.0.92.61 protocol tcp global *********5 7001 inside 18.0.92.61 7001 no-reverse
 nat server 18.0.92.62 protocol tcp global *********5 7002 inside 18.0.92.62 7001 no-reverse
 nat server 18.0.92.63 protocol tcp global *********5 7003 inside 18.0.92.63 7001 no-reverse
 nat server ********5 protocol tcp global *********5 22 inside ********5 22 no-reverse
 nat server 18.0.92.78 global *********6 inside 18.0.92.78 no-reverse
 nat server 18.0.92.39 protocol tcp global *********5 3558 inside 18.0.92.39 3558 no-reverse
 nat server T5*********** protocol tcp global ********** 7001 inside *********** 7001 no-reverse
 nat server T538.0.160.65 protocol tcp global ********** 7002 inside 38.0.160.65 7001 no-reverse
 nat server T538.0.160.66 protocol tcp global ********** 7003 inside 38.0.160.66 7001 no-reverse
 nat server T538.0.160.36 protocol tcp global ********** 3555 inside 38.0.160.36 3555 no-reverse
 nat server T5*********** protocol tcp global ********** 3556 inside *********** 3555 no-reverse
 nat server T5*********** protocol tcp global ********** 31306 inside *********** 31306 no-reverse
 nat server T5********** protocol tcp global ********** 30001 inside ********** 30001 no-reverse
 nat server T5*********** protocol tcp global ********** 8082 inside *********** 8082 no-reverse
 nat server T5***********:8092 protocol tcp global ********** 8092 inside *********** 8092 no-reverse
 nat server T5测试 global ********** inside *********** no-reverse
#
ip vpn-instance mgmt
#
 ip ttl-expires enable                    
 ip df-unreachables enable
#
 dhcp enable
#
 undo firewall ipv6 statistic system enable
#
 dns resolve
 dns transparent-proxy enable
#
 vlan batch 1
#
 undo firewall session link-state check tcp
 undo firewall session link-state check icmp
#
#
 undo firewall statistic system enable
#
pki certificate access-control-policy default permit
#
 undo dns proxy
#
 license-server domain sdplsp.huawei.com
#                                         
 lldp enable
#
 user-manage web-authentication security port 8887
 user-manage single-sign-on ad mode plug-in
 user-manage portal-template portal
password-policy
 level high
page-setting
 user-manage security version tlsv1 tlsv1.1 tlsv1.2
#
 firewall detect ftp
 firewall detect sqlnet
#
 firewall packet-filter basic-protocol enable
#
 sysname BJ-UAT1-FW-A03
#
 web-manager security version tlsv1 tlsv1.1 tlsv1.2
 web-manager enable
 web-manager security enable port 8443
 undo web-manager config-guide enable
#
 undo update schedule ips-sdb enable      
 undo update schedule av-sdb enable
 undo update schedule sa-sdb enable
 update schedule weekly Sun 04:46
#
 policy accelerate standby enable
#
 undo factory-configuration prohibit
#
 engine bypass
#
 undo rbl-filter enable
#
 time-range worktime
  period-range 08:00:00 to 18:00:00 working-day
#
acl number 3000
 rule 0 permit ip source ************* 0
 rule 1 permit ip destination ************* 0
acl number 3001
 rule 0 permit ip source ************** 0 destination ********** 0
#
interface Eth-Trunk0
 alias Eth-Trunk0                         
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage telnet permit
#
interface Eth-Trunk0.1
 vlan-type dot1q 452
 alias egress.1
 ip address ********** ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage telnet permit
#
interface Eth-Trunk1
 alias Eth-Trunk1
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage telnet permit
#
interface Eth-Trunk1.1
 vlan-type dot1q 455                      
 alias egw.1
 ip address ********** ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage telnet permit
#
interface Eth-Trunk2
 alias Eth-trunk2
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage telnet permit
#
interface Eth-Trunk2.1
 vlan-type dot1q 453
 alias core.1
 ip address ********** ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage snmp permit               
 service-manage telnet permit
#
interface Eth-Trunk3
 alias Eth-Trunk3
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage telnet permit
#
interface Eth-Trunk3.1
 vlan-type dot1q 454
 alias fez.1
 ip address ********** ***************
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage snmp permit
 service-manage telnet permit
#
interface Eth-Trunk3.2
 vlan-type dot1q 888
 alias webserver
 ip address ********** ***************    
 service-manage ping permit
 service-manage ssh permit
 service-manage telnet permit
#
interface Eth-Trunk4
#
interface GigabitEthernet0/0/0
 alias GE0/MGMT
 ip binding vpn-instance mgmt
 ip address ********* *************
 lldp enable
 lldp tlv-enable basic-tlv all
 service-manage http permit
 service-manage https permit
 service-manage ping permit
 service-manage ssh permit
 service-manage snmp permit
 service-manage telnet permit
 anti-ddos flow-statistic enable
 anti-ddos syn-flood source-detect alert-rate 100
#
interface GigabitEthernet1/0/0
 eth-trunk 0                              
 lldp enable
 lldp tlv-enable basic-tlv all
#
interface GigabitEthernet1/0/1
 eth-trunk 0
 lldp enable
 lldp tlv-enable basic-tlv all
#
interface GigabitEthernet1/0/2
 eth-trunk 1
 lldp enable
 lldp tlv-enable basic-tlv all
#
interface GigabitEthernet1/0/3
 eth-trunk 1
 lldp enable
 lldp tlv-enable basic-tlv all
#
interface GigabitEthernet1/0/4
 eth-trunk 2
 lldp enable
 lldp tlv-enable basic-tlv all
#                                         
interface GigabitEthernet1/0/5
 eth-trunk 2
 lldp enable
 lldp tlv-enable basic-tlv all
#
interface GigabitEthernet1/0/6
 eth-trunk 3
#
interface GigabitEthernet1/0/7
 eth-trunk 3
#
interface GigabitEthernet1/0/8
#
interface GigabitEthernet1/0/9
#
interface GigabitEthernet1/0/10
#
interface GigabitEthernet1/0/11
#
interface NULL0
#
interface LoopBack1
 ip address ********** *************      
#
firewall zone local
 set priority 100
#
firewall zone trust
 set priority 85
#
firewall zone untrust
 set priority 5
#
firewall zone dmz
 set priority 50
#
firewall zone name egress
 set priority 45
 add interface Eth-Trunk0
 add interface Eth-Trunk0.1
#
firewall zone name egw
 set priority 46
 add interface Eth-Trunk1
 add interface Eth-Trunk1.1
#                                         
firewall zone name fez
 set priority 47
 add interface Eth-Trunk3
 add interface Eth-Trunk3.1
#
firewall zone name core
 set priority 48
 add interface Eth-Trunk2
 add interface Eth-Trunk2.1
#
firewall zone name MGMT
 set priority 1
 add interface GigabitEthernet0/0/0
#
firewall zone name webserver
 set priority 30
 add interface Eth-Trunk3.2
#
aaa
 authentication-scheme default
 authentication-scheme admin_local
 authentication-scheme admin_radius_local
 authentication-scheme admin_hwtacacs_local
 authentication-scheme admin_ad_local
 authentication-scheme admin_ldap_local
 authentication-scheme admin_radius
 authentication-scheme admin_hwtacacs
 authentication-scheme admin_ad
 authentication-scheme admin_ldap
 authentication-scheme admin_securid
 authentication-scheme admin_securid_local
 #
 authorization-scheme default
 #
 accounting-scheme default
 #
 manager-user password-modify enable
 manager-user audit-admin
  password cipher %@%@~"wC>c#XX2n:Hs/vi/%./i`WZYM!3xS\@,u^9#C1"x.7Kofa%@%@
  service-type web terminal telnet
  level 15
  ssh authentication-type password
  ssh service-type stelnet
 #
 manager-user admin
  password cipher %@%@Llq\2F*9cU=^zq1g(|d>umd[EJ9c*[)4[0Co{:A27#bH0sje%@%@
  service-type web terminal telnet ssh
  level 15
  ftp-directory hda1:
  ssh authentication-type password
  ssh service-type stelnet
  authentication-scheme admin_local
 #
 manager-user jack
  password cipher %@%@9lVCHfT#YF@|$2+I\a32VSJAw@G:R;h/Z:JHb!TQ"}NP`YPK%@%@
  service-type web ssh
  level 15
  ftp-directory hda1:
  ssh authentication-type password
  ssh service-type stelnet
  authentication-scheme admin_local
 #
 manager-user netadmin
  password cipher %@%@|AhtGkEu@2tJ=uSq\*p3fcZQ{HAH=l~Tt/)~+zFs:~5WOi`[%@%@
  service-type web telnet ssh
  level 15
  ftp-directory hda1:
  ssh authentication-type password
  ssh service-type stelnet                
  authentication-scheme admin_local
 #
 manager-user asuser
  password cipher %@%@)l.oWF~1/YM+`oOl&2~OcPG>&G4S%1CLU>KD"C',EUSA`VMH%@%@
  service-type web ssh
  level 15
  ftp-directory hda1:
  ssh authentication-type password
  ssh service-type stelnet
  authentication-scheme admin_local
 #
 domain default
  service-type access internet-access
  reference user current-domain
  new-user deny-authentication
 #
role system-admin
  description system-admin
role device-admin
  description device-admin
role device-admin(monitor)
  description device-admin(monitor)
role audit-admin                          
  description audit-admin
role readonly
  description readonly
 dashboard read-only
 monitor
  read-only report session statistic statistic-acl
  none diagnose
 policy read-only
 object
  read-only address service application user-manage time-range url-category keyword-group signature av ips data-filter file-filter app-control mail-filter url-filter authen-server geo-location device-classification certificate mail-address-group healthcheck
  none security-group
 network
  read-only interface pair-interface zone dns dhcp route ipsec l2tp gre dsvpn sslvpn tsm
  none standby
 system
  read-only configuration high-reliability license update-center agile-network
  none manager vsys log-configuration system-update configuration-manage
 bind manager-user audit-admin role audit-admin
 bind manager-user asuser role readonly
 bind manager-user jack role system-admin
 bind manager-user netadmin role system-admin
#
ospf 1 router-id **********               
 area 0.0.0.0
  network ********* *******
  network ********** *******
  network ********** *******
  network ********** *******
  network ********** 0.0.0.0
  network ********** *******
#
nqa-jitter tag-version 1

#
 ip route-static 0.0.0.0 0.0.0.0 Eth-Trunk0.1 *********
 ip route-static *********** *************** **********
 ip route-static ************* ************* Eth-Trunk0.1 *********
 ip route-static vpn-instance mgmt 0.0.0.0 0.0.0.0 **********
#
 snmp-agent
 snmp-agent local-engineid 000007DB7F000001094291D9
 snmp-agent community read  %$%$ymv3J;*(^8:+-5K53ap>I|sj%$%$
 snmp-agent community write  %$%$3,Rz7IebO!k+r*R^EX,KRH~6%$%$
 snmp-agent community read  %$%$4kI{E8uBBF3ZCj@,/X]C;qh_%$%$
 snmp-agent sys-info contact R&D Huawei Technologies Co.,Ltd.
 snmp-agent sys-info version v2c          
 undo snmp-agent sys-info version v3
 snmp-agent trap enable bfd
 snmp-agent trap enable ospf 1
 snmp-agent trap enable bgp
 snmp-agent trap enable lsp
 snmp-agent trap enable ldp
 snmp-agent trap enable ipsec
 snmp-agent trap enable l2tp
 snmp-agent trap enable configuration
 snmp-agent trap enable system
 snmp-agent trap enable standard
 snmp-agent trap enable vrrp
 snmp-agent trap enable ssh
 snmp-agent trap enable srm
#
 telnet server enable
#
 sftp server enable
 stelnet server enable
#
 banner enable
#
 undo dns proxy ipv6 enable               
#
 isp name "china mobile"
 isp name "china mobile" set filename china-mobile.csv
 isp name "china unicom"
 isp name "china unicom" set filename china-unicom.csv
 isp name "china telecom"
 isp name "china telecom" set filename china-telecom.csv
 isp name "china educationnet"
 isp name "china educationnet" set filename china-educationnet.csv
#
user-interface con 0
 authentication-mode aaa
user-interface vty 0 4
 authentication-mode aaa
#
ip address-set egress type group
 address 0 address-set **********/24.
 address 1 address-set *************/32.
 address 2 address-set **********/32.
 address 3 address-set **********/24.
 address 4 address-set **********/32.
 address 5 address-set **********/24.
 address 6 address-set *************/32.  
 address 7 address-set 500wan**************/32
 address 8 address-set abc
 address 9 address-set ali*************
 address 10 address-set bj_prod_fw_a01
 address 11 address-set bj_prod_fw_a02
 address 12 address-set bj_prod_rt_a01
 address 13 address-set bj_prod_rt_a02
 address 14 address-set bj_prod_sw_a01
 address 15 address-set ca_server(outline)
 address 16 address-set call_center
 address 17 address-set ccb
 address 18 address-set cslc_**********
 address 19 address-set cslc_***********
 address 20 address-set cslc_***********
 address 21 address-set cslcump*********
 address 22 address-set cslcump*********
 address 23 address-set cslvsts
 address 24 address-set icbc
 address 25 address-set lianlian**************
 address 26 address-set mail_system
 address 27 address-set message_system
 address 28 address-set sporttery
 address 29 address-set tencent***********/24
 address 30 address-set uuzz********
 address 31 address-set uuzz*************
 address 32 address-set uuzz*************
 address 33 address-set uuzz*************
 address 34 address-set uuzz************
 address 35 address-set uuzz*************
 address 36 address-set uuzz*************
 address 37 address-set uuzz*************
 address 38 address-set uuzz*************
#
ip address-set egress_aaa_client type group
 address 0 address-set bj_prod_fw_a01
 address 1 address-set bj_prod_fw_a02
 address 2 address-set bj_prod_rt_a01
 address 3 address-set bj_prod_rt_a02
 address 4 address-set bj_prod_sw_a01
#
ip address-set egress_snmp_client type group
 address 0 address-set bj_prod_rt_a01
 address 1 address-set bj_prod_rt_a02
 address 2 address-set bj_prod_sw_a01
#
ip address-set message&mail_system type group
 address 0 address-set mail_system
 address 1 address-set message_system
#
ip address-set bcs_aa type group
 address 0 address-set absbcsaaa01
 address 1 address-set absbcsaaa02
 address 2 address-set absbcsaaa03
 address 3 address-set absbcsaaa04
 address 4 address-set absbcsaaa05
 address 5 address-set absbcsaaa06
 address 6 address-set absbcsaaa07
 address 7 address-set absbcsaaa08
 address 8 address-set absbcsaaa09
 address 9 address-set absbcsaaa10
 address 10 address-set absbcsaaa11
 address 11 address-set bcs_aa_aa_group_virtual_ip
 address 12 address-set bcs_aa_aaauditsql_virtual_ip
 address 13 address-set bcs_aa_aamsdtc_virtual_ip
 address 14 address-set bcs_aa_aasql_virtual_ip
 address 15 address-set bcs_aa_cluster_virtual_ip
 address 16 address-set bcs_aa_iaaserver_virtual_ip
 address 17 address-set bcs_aa_matserver_virtual_ip
 address 18 address-set bcs_aa_oltpserver1_virtual_ip
 address 19 address-set bcs_aa_aaaudit_group_virtual_ip
#
ip address-set bis_ab type group
 address 0 address-set absbisaba01
 address 1 address-set absbisaba02
 address 2 address-set absbisaba03
 address 3 address-set absbisaba04
 address 4 address-set absbisaba05
 address 5 address-set absbisaba06
 address 6 address-set absbisaba07
 address 7 address-set absbisaba08
 address 8 address-set absbisaba09
 address 9 address-set absbisaba10
#
ip address-set cb_info_hub type group
 address 0 address-set cb_info_hub1
 address 1 address-set cb_info_hub2
 address 2 address-set cb_info_hub3
 address 3 address-set cb_info_hub4
#
ip address-set hp_openview type group
 address 0 address-set hp_openview01
 address 1 address-set hp_openview02      
 address 2 address-set hp_openview03
#
ip address-set igw_out type group
 address 0 address-set absigw_lb1_out
 address 1 address-set absigwa01_out
 address 2 address-set absigwa02_out
#
ip address-set monsvc_in type group
 address 0 address-set absmonsvca01_in
 address 1 address-set absmonsvca02_in
 address 2 address-set absmonsvca03_in
 address 3 address-set absmonsvca04_in
 address 4 address-set absmonsvca05_in
 address 5 address-set absmonsvca06_in
 address 6 address-set absmonsvca07_in
 address 7 address-set absmonsvca08_in
 address 8 address-set absmonsvca09_in
 address 9 address-set mon_svc_cluster_virtual_ip_in
#
ip address-set monsvc_out type group
 address 0 address-set absmonsvca01_out
 address 1 address-set absmonsvca02_out
 address 2 address-set absmonsvca03_out   
 address 3 address-set absmonsvca04_out
 address 4 address-set absmonsvca05_out
 address 5 address-set absmonsvca06_out
 address 6 address-set absmonsvca07_out
 address 7 address-set absmonsvca08_out
 address 8 address-set absmonsvca09_out
 address 9 address-set mon_svc_cluster_virtual_ip_out
#
ip address-set oltp_ab type group
 address 0 address-set oltp_ab_vmsa01
 address 1 address-set oltp_ab_vmsa02
 address 2 address-set oltp_ab_vmsa03
 address 3 address-set oltp_ab_vmsa04
 address 4 address-set oltp_ab_vmsa05
 address 5 address-set oltp_ab_vmsa06
 address 6 address-set oltp_ab_vmsa07
 address 7 address-set oltp_ab_vmsa08
 address 8 address-set oltp_ab_vmsabclu
 address 9 address-set oltp_ab_vmsoltpab1
#
ip address-set stm_svc_out type group
 address 0 address-set absstmtsvc_lb_out
#                                         
ip address-set aaa_fez_client type group
 address 0 address-set bj_prod_sw_a03
#
ip address-set as_in type group
 address 0 address-set absasa01_in
 address 1 address-set absasa02_in
 address 2 address-set absasa03_in
 address 3 address-set absasa04_in
 address 4 address-set absasa05_in
 address 5 address-set absasa06_in
 address 6 address-set absasa07_in
 address 7 address-set absasa08_in
 address 8 address-set absasa09_in
 address 9 address-set absasa10_in
 address 10 address-set absasa11_in
 address 11 address-set absasa12_in
 address 12 address-set absasa13_in
 address 13 address-set absasa14_in
 address 14 address-set absasa15_in
 address 15 address-set absasa16_in
 address 16 address-set absasa17_in
 address 17 address-set absasa18_in
 address 18 address-set absasftp01_in     
 address 19 address-set absasftp02_in
 address 20 address-set absasftp_cluster_virture_ip
#
ip address-set as_out type group
 address 0 address-set absas_lb1_out
 address 1 address-set absasa01_out
 address 2 address-set absasa02_out
 address 3 address-set absasa03_out
 address 4 address-set absasa04_out
 address 5 address-set absasa05_out
 address 6 address-set absasa06_out
 address 7 address-set absasa07_out
 address 8 address-set absasa08_out
 address 9 address-set absasa09_out
 address 10 address-set absasa10_out
 address 11 address-set absasa11_out
 address 12 address-set absasa12_out
 address 13 address-set absasa13_out
 address 14 address-set absasa14_out
 address 15 address-set absasa15_out
 address 16 address-set absasa16_out
 address 17 address-set absasa17_out
 address 18 address-set absasftp01_out    
 address 19 address-set absasftp02_out
 address 20 address-set absasftp_cluster_virture_ip
#
ip address-set asdb type group
 address 0 address-set absasdba01
 address 1 address-set absasdba02
 address 2 address-set absasdba03
 address 3 address-set absasdba04
 address 4 address-set absasdba05
 address 5 address-set absasdba06
 address 6 address-set absasdba07
 address 7 address-set asdb_cluster_virtual_ip
 address 8 address-set asdb_msdtc
 address 9 address-set asdb_sql
#
ip address-set bj_prod_lb_a01&a02 type group
 address 0 address-set bj_prod_lb_a01
 address 1 address-set bj_prod_lb_a01&a02_virture_ip
 address 2 address-set bj_prod_lb_a02
#
ip address-set snmp_fez_client type group
 address 0 address-set bj_prod_sw_a03
#                                         
ip address-set eft_gw_in type group
 address 0 address-set abseftgwa01_in
 address 1 address-set abseftgwa02_in
 address 2 address-set abseftgwa03_in
 address 3 address-set abseftgwa04_in
 address 4 address-set abseftgwa05_in
 address 5 address-set abseftgwa06_in
 address 6 address-set abseftgwa07_in
 address 7 address-set abseftgwa08_in
 address 8 address-set abseftgwa09_in
 address 9 address-set eft_gw_in_cluster_virtual_ip
#
ip address-set eft_gw_out type group
 address 0 address-set abseftgwa01_out
 address 1 address-set abseftgwa02_out
 address 2 address-set abseftgwa03_out
 address 3 address-set abseftgwa04_out
 address 4 address-set abseftgwa05_out
 address 5 address-set abseftgwa06_out
 address 6 address-set abseftgwa07_out
 address 7 address-set abseftgwa08_out
 address 8 address-set abseftgwa09_out
 address 9 address-set eft_gw_out_cluster_virtual_ip
#
ip address-set egw_aaa_client type group
 address 0 address-set bj_prod_sw_a02
#
ip address-set egw_snmp_client type group
 address 0 address-set bj_prod_sw_a02
#
ip address-set ca_gateway type group
 address 0 address-set ca_gateway_1
 address 1 address-set ca_gateway_2
 address 2 address-set ca_gateway_cluster_virtual_ip
#
ip address-set emial_gw_in type group
 address 0 address-set absemailgwa01_in
 address 1 address-set absemailgwa02_in
 address 2 address-set email_gw_cluster_virtual_ip_in
 address 3 address-set email_gw_ftp_virtual_ip_email_in
#
ip address-set emial_gw_out type group
 address 0 address-set absemailgwa01_out
 address 1 address-set absemailgwa02_out
 address 2 address-set email_gw_cluster_virtual_ip_out
 address 3 address-set email_gw_ftp_vir_ip_email_out
#
ip address-set sms/mail_gw_in type group
 address 0 address-set absemailgwa01_in
 address 1 address-set absemailgwa02_in
 address 2 address-set absmsggwa08_in
 address 3 address-set absmsggwa09_in
 address 4 address-set abssmsgwa01_in
 address 5 address-set abssmsgwa02_in
 address 6 address-set email_gw_cluster_virtual_ip_in
 address 7 address-set email_gw_ftp_virtual_ip_email_in
 address 8 address-set sms_gw_cluster_virtual_ip_in
 address 9 address-set sms_gw_ftp_virtual_ip_sms_in
#
ip address-set sms/mail_gw_out type group
 address 0 address-set absmsggwa01_out
 address 1 address-set absmsggwa02_out
 address 2 address-set absmsggwa03_out
 address 3 address-set absmsggwa04_out
 address 4 address-set absmsggwa05_out
 address 5 address-set absmsggwa06_out
 address 6 address-set absmsggwa07_out
 address 7 address-set absmsggwa08_out
 address 8 address-set absmsggwa09_out    
 address 9 address-set sms/mail_gw_cluster_vir_ip_out
#
ip address-set sms_gw_in type group
 address 0 address-set abssmsgwa01_in
 address 1 address-set abssmsgwa02_in
 address 2 address-set sms_gw_cluster_virtual_ip_in
 address 3 address-set sms_gw_ftp_virtual_ip_sms_in
#
ip address-set sms_gw_out type group
 address 0 address-set abssmsgwa01_out
 address 1 address-set abssmsgwa02_out
 address 2 address-set sms_gw_cluster_virtual_ip_out
 address 3 address-set sms_gw_ftp_virtual_ip_sms_out
#
ip address-set ABSBISOnlinecore type group
 address 0 address-set ABSBISOnlinecore**********
 address 1 address-set ABSBISOnlinecore**********
 address 2 address-set ABSBISOnlinecore**********
 address 3 address-set ABSBISOnlinecore**********
 address 4 address-set ABSBISOnlinecore_**********
#
ip address-set ABSBISOnline_IN type group
 address 0 address-set ABSASFTP_ClusterIP_IN
 address 1 address-set ABSBISOnlineA01_IN
 address 2 address-set ABSBISOnlineA02_IN
 address 3 address-set ABSBISOnlineA03_IN
 address 4 address-set ABSBISOnlineA04_IN
 address 5 address-set ABSBISOnlineA05_IN
 address 6 address-set ABSBISOnlineA06_IN
 address 7 address-set ABSBISOnlineA07_IN
 address 8 address-set ABSBISOnlineA08_IN
 address 9 address-set ABSBISOnlineA09_IN
#
ip address-set ABSBISOnline_OUT type group
 address 0 address-set ABSBISOnlineA01_OUT
 address 1 address-set ABSBISOnlineA02_OUT
 address 2 address-set ABSBISOnlineA03_OUT_
 address 3 address-set ABSBISOnlineA04_OUT_
 address 4 address-set ABSBISOnlineA05_OUT_
 address 5 address-set ABSBISOnlineA06_OUT_
 address 6 address-set ABSBISOnlineA07_OUT_
 address 7 address-set ABSBISOnlineA08_OUT_
 address 8 address-set ABSBISOnlineA09_OUT_
 address 9 address-set ABSBISOnline_Cluster_OUT
 address 10 address-set ABSBISOnline_Cluster_OUT_Group
#                                         
ip address-set PBS type group
 address 0 address-set pasdb_************.
 address 1 address-set ************.
 address 2 address-set ************.
 address 3 address-set ************.
 address 4 address-set pas_************/32.
 address 5 address-set ************.
 address 6 address-set ************.
 address 7 address-set ************.
#
ip address-set ***********/24. type group
 address 0 *********** mask 24
#
ip address-set "SCP VIP" type group
 address 0 ********** mask 24
#
ip address-set ELP type group
 address 0 ********* mask 32
 address 1 ********* mask 32
 address 2 ********* mask 32
 address 3 ********* mask 32
 address 4 ******** mask 24
#                                         
ip address-set Tidb type group
 address 0 ********* mask 32
 address 1 ********* mask 32
 address 2 ********* mask 32
 address 3 ********* mask 32
 address 4 ********* mask 32
 address 5 ********* mask 32
 address 6 ********* mask 32
 address 7 ********* mask 32
 address 8 ********* mask 32
 address 9 *********0 mask 32
 address 10 *********1 mask 32
 address 11 *********2 mask 32
#
ip address-set TESTYUNPINGTAI type group
 address 0 ******** mask 32
 address 1 ******** mask 32
 address 2 ******** mask 32
 address 3 ********* mask 32
 address 4 ********* mask 32
#
ip address-set ***********_25 type group
 address 0 range *********** ***********  
#
ip address-set ***********_228 type group
 address 0 range *********** ***********
#
ip address-set ***********_125 type group
 address 0 range *********** ***********
#
ip address-set LBSNAT type group
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32
 address 4 ************ mask 32
 address 5 ************ mask 32
#
ip address-set **********_36_0516 type group
 address 0 range ********** **********
#
ip address-set *********_73_0516 type group
 address 0 range ********* *********
#
ip address-set ************~160_数据采集机 type group
 address 0 range ************ ************
#
ip address-set *********. type group
 address 0 ********* mask *************
#
ip address-set 付中豪测试 type group
 address 0 ********** mask 32
 address 1 ********* mask 32
#
ip address-set **********/24. type object
 description Egress
 address 0 ********** mask 24
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/32. type object
 address 0 ********** mask 32             
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set 500wan**************/32 type object
 address 0 ************** mask 32
#
ip address-set abc type object
 address 0 ********** mask 27
#
ip address-set ali************* type object
 address 0 ************* mask 32
#
ip address-set bj_prod_fw_a01 type object
 address 0 ********* mask 32
#
ip address-set bj_prod_fw_a02 type object
 address 0 ********* mask 32
#
ip address-set bj_prod_rt_a01 type object 
 address 0 ********** mask 32
#
ip address-set bj_prod_rt_a02 type object
 address 0 ********** mask 32
#
ip address-set bj_prod_sw_a01 type object
 address 0 ********** mask 32
#
ip address-set ca_server(outline) type object
 address 0 ********** mask 27
#
ip address-set call_center type object
 address 0 ********** mask 24
#
ip address-set ccb type object
 address 0 *********** mask 27
#
ip address-set cslcump********* type object
 address 0 ********* mask 24
#
ip address-set cslcump********* type object
 address 0 ********* mask 24
#                                         
ip address-set cslc_********** type object
 address 0 ********** mask 24
#
ip address-set cslc_*********** type object
 address 0 *********** mask 24
#
ip address-set cslc_*********** type object
 address 0 *********** mask 24
#
ip address-set cslvsts type object
 address 0 *********** mask 24
#
ip address-set icbc type object
 address 0 *********** mask 27
#
ip address-set lianlian************** type object
 address 0 ************** mask 32
#
ip address-set mail_system type object
 address 0 ************ mask 25
#
ip address-set message_system type object
 address 0 ********** mask 25             
#
ip address-set sporttery type object
 address 0 ******** mask 27
#
ip address-set tencent***********/24 type object
 address 0 *********** mask 24
#
ip address-set uuzz******** type object
 address 0 ******** mask 24
#
ip address-set uuzz************* type object
 address 0 ************* mask 32
#
ip address-set uuzz************* type object
 address 0 ************* mask 32
#
ip address-set uuzz************* type object
 address 0 ************* mask 32
#
ip address-set uuzz************ type object
 address 0 ************ mask 32
#
ip address-set uuzz************* type object
 address 0 ************* mask 32
#
ip address-set uuzz************* type object
 address 0 ************* mask 32
#
ip address-set uuzz************* type object
 address 0 ************* mask 32
#
ip address-set uuzz************* type object
 address 0 ************* mask 32
#
ip address-set ************/23. type object
 address 0 ************ mask 23
#
ip address-set ********28/32. type object
 address 0 ********28 mask 32
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set *********7/32. type object
 address 0 *********7 mask 32
#                                         
ip address-set *********6/32. type object
 address 0 *********6 mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set ********/28. type object
 address 0 ******** mask 28
#
ip address-set *********/28. type object
 address 0 ********* mask 28
#
ip address-set *********/24. type object
 address 0 ********* mask 24
 address 1 ******** mask 32
 address 2 ******** mask 32
 address 3 ********** mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32              
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set absbcsaaa01 type object
 address 0 18.0.4.21 mask 32
#
ip address-set absbcsaaa02 type object
 address 0 18.0.4.22 mask 32
#
ip address-set absbcsaaa03 type object
 address 0 18.0.4.32 mask 32
#
ip address-set absbcsaaa04 type object
 address 0 18.0.4.33 mask 32
#
ip address-set absbcsaaa05 type object
 address 0 18.0.4.34 mask 32
#
ip address-set absbcsaaa06 type object    
 address 0 18.0.4.35 mask 32
#
ip address-set absbcsaaa07 type object
 address 0 18.0.4.36 mask 32
#
ip address-set absbcsaaa08 type object
 address 0 18.0.4.37 mask 32
#
ip address-set absbcsaaa09 type object
 address 0 18.0.4.38 mask 32
#
ip address-set absmonsvca07_in type object
 address 0 18.0.4.88 mask 32
#
ip address-set absmonsvca07_out type object
 address 0 18.4.21.38 mask 32
#
ip address-set absmonsvca08_in type object
 address 0 18.0.4.89 mask 32
#
ip address-set absmonsvca08_out type object
 address 0 18.4.21.39 mask 32
#                                         
ip address-set absmonsvca09_in type object
 address 0 18.0.4.90 mask 32
#
ip address-set absmonsvca09_out type object
 address 0 18.4.21.40 mask 32
#
ip address-set absstmtsvc_lb_out type object
 address 0 18.4.21.30 mask 32
#
ip address-set absbcsaaa10 type object
 address 0 18.0.4.39 mask 32
#
ip address-set absbcsaaa11 type object
 address 0 18.0.4.40 mask 32
#
ip address-set absbisaba01 type object
 address 0 ********* mask 32
#
ip address-set absbisaba02 type object
 address 0 18.0.4.42 mask 32
#
ip address-set absbisaba03 type object
 address 0 18.0.4.43 mask 32              
#
ip address-set absbisaba04 type object
 address 0 18.0.4.44 mask 32
#
ip address-set absbisaba05 type object
 address 0 ********* mask 32
#
ip address-set absbisaba06 type object
 address 0 18.0.4.46 mask 32
#
ip address-set absbisaba07 type object
 address 0 18.0.4.47 mask 32
#
ip address-set absbisaba08 type object
 address 0 ********* mask 32
#
ip address-set absbisaba09 type object
 address 0 ********* mask 32
#
ip address-set absbisaba10 type object
 address 0 ********* mask 32
#
ip address-set absbisonline_msdtc type object
 address 0 ********** mask 32
#
ip address-set absbisonline_sql type object
 address 0 ********** mask 32
#
ip address-set absbisonline_vip type object
 address 0 ********** mask 32
#
ip address-set absbisonlinea01 type object
 address 0 ********** mask 32
#
ip address-set absbisonlinea02 type object
 address 0 ********** mask 32
#
ip address-set absigw_lb1_out type object
 address 0 ********** mask 32
#
ip address-set absigwa01_out type object
 address 0 ********* mask 32
#
ip address-set absigwa02_out type object
 address 0 ********* mask 32
#                                         
ip address-set absmgw_lb1_out type object
 address 0 ********* mask 24
#
ip address-set absmonsvca01_in type object
 address 0 ********* mask 32
#
ip address-set absmonsvca01_out type object
 address 0 ********** mask 32
#
ip address-set absmonsvca02_in type object
 address 0 ********* mask 32
#
ip address-set absmonsvca02_out type object
 address 0 ********** mask 32
#
ip address-set absmonsvca03_in type object
 address 0 ********* mask 32
#
ip address-set absmonsvca03_out type object
 address 0 ********** mask 32
#
ip address-set absmonsvca04_in type object
 address 0 ********* mask 32              
#
ip address-set absmonsvca04_out type object
 address 0 ********** mask 32
#
ip address-set absmonsvca05_in type object
 address 0 ********* mask 32
#
ip address-set absmonsvca05_out type object
 address 0 ********** mask 32
#
ip address-set absmonsvca06_in type object
 address 0 ********* mask 32
#
ip address-set absmonsvca06_out type object
 address 0 ********** mask 32
#
ip address-set bcs_aa_aaauditsql_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_aamsdtc_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_aasql_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_cluster_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_iaaserver_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_matserver_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_oltpserver1_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set betgw01_********** type object
 address 0 ********** mask 32
#
ip address-set betgw02_********** type object
 address 0 ********** mask 32
#
ip address-set cb_info_hub1 type object
 address 0 ********* mask 32
#                                         
ip address-set cb_info_hub2 type object
 address 0 ********* mask 32
#
ip address-set cb_info_hub3 type object
 address 0 ********* mask 32
#
ip address-set cb_info_hub4 type object
 address 0 ********* mask 32
#
ip address-set control_m type object
 address 0 ********** mask 28
#
ip address-set csl_ab_mat03_********** type object
 address 0 ********** mask 32
#
ip address-set csl_ab_mat05_********** type object
 address 0 ********** mask 32
#
ip address-set csl_ab_opcc01_********** type object
 address 0 ********** mask 32
#
ip address-set csl_ab_opcc02_********** type object
 address 0 ********** mask 32             
#
ip address-set csl_ab_opcc03_********** type object
 address 0 ********** mask 32
#
ip address-set csl_ab_opcc04_********** type object
 address 0 ********** mask 32
#
ip address-set csl_ab_opcc05_********** type object
 address 0 ********** mask 32
#
ip address-set customerserivcegw_out type object
 address 0 ********** mask 32
#
ip address-set eccom_neteagle type object
 address 0 ********** mask 32
#
ip address-set g2cbgw_********* type object
 address 0 ********* mask 32
#
ip address-set g2matserver_********* type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_aa_group_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set bcs_aa_aaaudit_group_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set hp_openview01 type object
 address 0 ********** mask 32
#
ip address-set hp_openview02 type object
 address 0 ********** mask 32
#
ip address-set hp_openview03 type object
 address 0 ********** mask 32
#
ip address-set ise_server type object
 address 0 ********** mask 32
#
ip address-set matserver_out type object
 address 0 ********* mask 23
#
ip address-set mon_svc_cluster_virtual_ip_in type object
 address 0 ********* mask 32
#                                         
ip address-set mon_svc_cluster_virtual_ip_out type object
 address 0 ********** mask 32
#
ip address-set nbu_server type object
 address 0 ********** mask 32
#
ip address-set odc_as_server type object
 address 0 ************* mask 32
#
ip address-set odc_betslip type object
 address 0 ************* mask 32
#
ip address-set oltp_ab_vmsa01 type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsa02 type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsa03 type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsa04 type object
 address 0 ********* mask 32              
#
ip address-set oltp_ab_vmsa05 type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsa06 type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsa07 type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsa08 type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsabclu type object
 address 0 ********* mask 32
#
ip address-set oltp_ab_vmsoltpab1 type object
 address 0 ********* mask 32
#
ip address-set patrol_server type object
 address 0 ********60 mask 29
#
ip address-set rsa_syslog_server type object
 address 0 ********* mask 32
#
ip address-set snmp_server(lms) type object
 address 0 *********1 mask 32
#
ip address-set trendmicro_server type object
 address 0 ********39 mask 32
#
ip address-set uat_f6_testpc type object
 address 0 ********* mask 32
#
ip address-set uat_f6_testpc(*********/24) type object
 address 0 ********* mask 24
#
ip address-set vsts_test_pc type object
 address 0 ********** mask 32
#
ip address-set zabbix_********** type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#                                         
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set absas_lb1_out type object
 address 0 ********** mask 32
#
ip address-set absasa01_in type object
 address 0 ********** mask 32
#
ip address-set absasa01_out type object
 address 0 ********** mask 32
#
ip address-set absasa02_in type object
 address 0 ********** mask 32
#
ip address-set absasa02_out type object
 address 0 ********** mask 32
#
ip address-set absasa03_in type object
 address 0 ********** mask 32             
#
ip address-set absasa03_out type object
 address 0 ********** mask 32
#
ip address-set absasa04_in type object
 address 0 ********** mask 32
#
ip address-set absasa04_out type object
 address 0 ********** mask 32
#
ip address-set absasa05_in type object
 address 0 ********** mask 32
#
ip address-set absasa05_out type object
 address 0 ********** mask 32
#
ip address-set absasa06_in type object
 address 0 ********** mask 32
#
ip address-set absasa06_out type object
 address 0 ********** mask 32
#
ip address-set absasa07_in type object    
 address 0 ********** mask 32
#
ip address-set absasa07_out type object
 address 0 18.4.64.17 mask 32
#
ip address-set absasa08_in type object
 address 0 18.4.11.18 mask 32
#
ip address-set absasa08_out type object
 address 0 18.4.64.18 mask 32
#
ip address-set absasa09_in type object
 address 0 18.4.11.19 mask 32
#
ip address-set absasa09_out type object
 address 0 18.4.64.19 mask 32
#
ip address-set absasa10_in type object
 address 0 18.4.11.20 mask 32
#
ip address-set absasa10_out type object
 address 0 18.4.64.20 mask 32
#                                         
ip address-set absasa11_in type object
 address 0 18.4.11.21 mask 32
#
ip address-set absasa11_out type object
 address 0 18.4.64.21 mask 32
#
ip address-set absasa12_in type object
 address 0 18.4.11.22 mask 32
#
ip address-set absasa12_out type object
 address 0 18.4.64.22 mask 32
#
ip address-set absasa13_in type object
 address 0 18.4.11.23 mask 32
#
ip address-set absasa13_out type object
 address 0 18.4.64.23 mask 32
#
ip address-set absasa14_in type object
 address 0 18.4.11.24 mask 32
#
ip address-set absasa14_out type object
 address 0 18.4.64.24 mask 32             
#
ip address-set absasa15_in type object
 address 0 18.4.11.25 mask 32
#
ip address-set absasa15_out type object
 address 0 18.4.64.25 mask 32
#
ip address-set absasa16_in type object
 address 0 18.4.11.26 mask 32
#
ip address-set absasa16_out type object
 address 0 18.4.64.26 mask 32
#
ip address-set absasa17_in type object
 address 0 18.4.11.27 mask 32
#
ip address-set absasa17_out type object
 address 0 18.4.64.27 mask 32
#
ip address-set absasa18_in type object
 address 0 18.4.11.30 mask 32
#
ip address-set absasdba01 type object     
 address 0 ********** mask 32
#
ip address-set absasdba02 type object
 address 0 ********** mask 32
#
ip address-set absasdba03 type object
 address 0 ********** mask 32
#
ip address-set absasdba04 type object
 address 0 18.4.11.37 mask 32
#
ip address-set absasdba05 type object
 address 0 18.4.11.38 mask 32
#
ip address-set absasdba06 type object
 address 0 18.4.11.39 mask 32
#
ip address-set absasdba07 type object
 address 0 18.4.11.40 mask 32
#
ip address-set absasftp01_in type object
 address 0 18.4.11.28 mask 32
#                                         
ip address-set absasftp01_out type object
 address 0 18.4.64.28 mask 32
#
ip address-set absasftp02_in type object
 address 0 18.4.11.29 mask 32
#
ip address-set absasftp02_out type object
 address 0 18.4.64.29 mask 32
#
ip address-set absasftp_cluster_virture_ip type object
 address 0 18.4.64.41 mask 32
#
ip address-set as&asdb(*********/24) type object
 address 0 ********* mask 24
#
ip address-set asdb_cluster_virtual_ip type object
 address 0 18.4.11.33 mask 32
#
ip address-set asdb_msdtc type object
 address 0 ********** mask 32
#
ip address-set asdb_sql type object
 address 0 ********** mask 32             
#
ip address-set bj_prod_lb_a01 type object
 address 0 ********* mask 32
#
ip address-set bj_prod_lb_a01&a02_virture_ip type object
 address 0 ********* mask 32
#
ip address-set bj_prod_lb_a02 type object
 address 0 ********* mask 32
#
ip address-set bj_prod_sw_a03 type object
 address 0 ********** mask 32
#
ip address-set ca_gateway_1 type object
 address 0 ********* mask 32
#
ip address-set ca_gateway_2 type object
 address 0 ********* mask 32
#
ip address-set ca_gateway_3 type object
 address 0 ********* mask 32
#
ip address-set ca_gateway_cluster_virtual_ip type object
 address 0 ********** mask 32
#
ip address-set ca_server(all) type object
 address 0 ********* mask 24
#
ip address-set ca_dsvs01 type object
 address 0 ********** mask 32
#
ip address-set ca_dsvs02 type object
 address 0 ********** mask 32
#
ip address-set fez_syslog_client type object
 address 0 ********* mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#                                         
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set absbisonline_msdtc_in type object
 address 0 ********* mask 32
#
ip address-set absbisonline_sql_in type object
 address 0 ********* mask 32
#
ip address-set absbisonline_web01_in type object
 address 0 ********* mask 32
#
ip address-set absbisonline_web02_in type object
 address 0 ********* mask 32
#
ip address-set absbisonline_web_vip_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa01_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa01_out type object
 address 0 ********* mask 32              
#
ip address-set abseftgwa02_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa02_out type object
 address 0 ********* mask 32
#
ip address-set abseftgwa03_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa03_out type object
 address 0 ********* mask 32
#
ip address-set abseftgwa04_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa04_out type object
 address 0 ********* mask 32
#
ip address-set abseftgwa05_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa05_out type object
 address 0 ********** mask 32
#
ip address-set abseftgwa06_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa06_out type object
 address 0 ********* mask 32
#
ip address-set abseftgwa07_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa07_out type object
 address 0 ********* mask 32
#
ip address-set abseftgwa08_in type object
 address 0 ********* mask 32
#
ip address-set abseftgwa08_out type object
 address 0 ********* mask 32
#
ip address-set abseftgwa09_in type object
 address 0 ********* mask 32
#                                         
ip address-set abseftgwa09_out type object
 address 0 ********* mask 32
#
ip address-set absemailgwa01_in type object
 address 0 ********* mask 32
#
ip address-set absemailgwa01_out type object
 address 0 *********9 mask 32
#
ip address-set absemailgwa02_in type object
 address 0 ********* mask 32
#
ip address-set absemailgwa02_out type object
 address 0 ********** mask 32
#
ip address-set absgwzdca01 type object
 address 0 ******** mask 32
#
ip address-set absmsggwa01_out type object
 address 0 ********* mask 32
#
ip address-set absmsggwa02_out type object
 address 0 ********* mask 32              
#
ip address-set absmsggwa03_out type object
 address 0 ********* mask 32
#
ip address-set absmsggwa04_out type object
 address 0 ********* mask 32
#
ip address-set absmsggwa05_out type object
 address 0 ********* mask 32
#
ip address-set absmsggwa06_out type object
 address 0 ********* mask 32
#
ip address-set absmsggwa07_out type object
 address 0 ********* mask 32
#
ip address-set absmsggwa08_in type object
 address 0 ********* mask 32
#
ip address-set absmsggwa08_out type object
 address 0 ********* mask 32
#
ip address-set absmsggwa09_in type object 
 address 0 ********* mask 32
#
ip address-set absmsggwa09_out type object
 address 0 ********* mask 32
#
ip address-set abssmsgwa01_in type object
 address 0 ********* mask 32
#
ip address-set abssmsgwa01_out type object
 address 0 ********* mask 32
#
ip address-set abssmsgwa02_in type object
 address 0 ********* mask 32
#
ip address-set abssmsgwa02_out type object
 address 0 ********* mask 32
#
ip address-set bisonline1_********* type object
 address 0 ********* mask 32
#
ip address-set bisonline2_********* type object
 address 0 ********* mask 32
#                                         
ip address-set bisonline3_********* type object
 address 0 ********* mask 32
#
ip address-set bj_prod_sw_a02 type object
 address 0 ********** mask 32
#
ip address-set boactchk_********* type object
 address 0 ********* mask 32
#
ip address-set eft&emai&sms(********/24) type object
 address 0 ******** mask 24
#
ip address-set eft_gw_in_cluster_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set eft_gw_out_cluster_virtual_ip type object
 address 0 ********* mask 32
#
ip address-set egw_ad type object
 address 0 ******** mask 24
#
ip address-set egw_syslog_client type object
 address 0 ******** mask 24               
#
ip address-set email_gw_cluster_virtual_ip_in type object
 address 0 ********* mask 32
#
ip address-set email_gw_cluster_virtual_ip_out type object
 address 0 *********1 mask 32
#
ip address-set email_gw_ftp_virtual_ip_email_in type object
 address 0 ********* mask 32
#
ip address-set email_gw_ftp_vir_ip_email_out type object
 address 0 *********0 mask 32
#
ip address-set sms/mail_gw_cluster_vir_ip_out type object
 address 0 ********* mask 32
#
ip address-set sms_gw_cluster_virtual_ip_in type object
 address 0 ********* mask 32
#
ip address-set sms_gw_cluster_virtual_ip_out type object
 address 0 ********* mask 32
#
ip address-set sms_gw_ftp_virtual_ip_sms_in type object
 address 0 ********* mask 32
#
ip address-set sms_gw_ftp_virtual_ip_sms_out type object
 address 0 *********6 mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set "Ali *************" type object
 address 0 ************* mask 32
#
ip address-set CSLCyanpiaoserver1 type object
 address 0 ************* mask ***************
#
ip address-set CSLCyanpiaoserver2 type object
 address 0 ************* mask ***************
#
ip address-set lottreasure************/24 type object
 address 0 ************ mask *************
#                                         
ip address-set **********. type object
 address 0 ********** mask ***************
#
ip address-set *********4. type object
 address 0 *********4 mask ***************
#
ip address-set **********. type object
 address 0 ********** mask ***************
#
ip address-set **********. type object
 address 0 ********** mask *************
#
ip address-set yanpiaoserver1 type object
 address 0 ********** mask ***************
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32             
#
ip address-set ************/24. type object
 address 0 ********** mask 24
#
ip address-set ************/27. type object
 address 0 ************ mask 27
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set 18.4.252.240/27. type object
 address 0 ************ mask 27
#
ip address-set 500wan************** type object
 address 0 ************** mask 32
#
ip address-set 500wan_18.4.2.32/27 type object
 address 0 18.4.2.32 mask 27
#
ip address-set "500WAN_INFO *********/27" type object
 address 0 ********* mask 27
#
ip address-set CSLC************* type object
 address 0 ************* mask 32
#
ip address-set Lottery_18.4.252.96/27 type object
 address 0 18.4.252.96 mask 27
#
ip address-set TaoBao_18.4.1.160/27 type object
 address 0 18.4.1.160 mask 27
#
ip address-set TaoBao_18.4.2.160/27 type object
 address 0 18.4.2.160 mask 27
#
ip address-set UMP*********/22 type object
 address 0 ********* mask 22
#
ip address-set UMP********* type object
 address 0 ********* mask 24
#
ip address-set UMP********* type object
 address 0 ********* mask 32
#
ip address-set "Webserver *********/24" type object
 address 0 ********* mask 24
#                                         
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ********39/32. type object
 address 0 ********39 mask 32
#
ip address-set *********1. type object
 address 0 *********1 mask 32
#
ip address-set *********0/32. type object
 address 0 *********0 mask 32
#
ip address-set *********4/32. type object
 address 0 *********4 mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32              
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set *************/24. type object
 address 0 ************* mask 24
#
ip address-set ************/24. type object
 address 0 ************ mask 24
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set 500wan**************. type object
 address 0 ************** mask 32
#
ip address-set ABSBISOnlinecore********** type object
 address 0 ********** mask 32
#
ip address-set ABSBISOnlinecore********** type object
 address 0 ********** mask 32
#
ip address-set ABSBISOnlinecore_********** type object
 address 0 ********** mask 32
#
ip address-set ABSBISOnlinecore********** type object
 address 0 ********** mask 32
#
ip address-set ABSBISOnlinecore_********** type object
 address 0 ********** mask 32
#
ip address-set ABSBISOnlinecore********** type object
 address 0 ********** mask 32
#
ip address-set "ABSSANA01 *********5" type object
 address 0 *********5 mask 32
#
ip address-set BetGW01********** type object
 address 0 ********** mask 32
#
ip address-set BetGW01********** type object
 address 0 ********** mask 32
#                                         
ip address-set BetGW03********** type object
 address 0 ********** mask 32
#
ip address-set BetGW04********** type object
 address 0 ********** mask 32
#
ip address-set Betslip101 type object
 address 0 *********** mask 32
#
ip address-set Betslip105 type object
 address 0 *********** mask 32
#
ip address-set Betslip106 type object
 address 0 *********** mask 32
#
ip address-set Betslip205 type object
 address 0 *********** mask 32
#
ip address-set CB_Info_HUB5 type object
 address 0 ********* mask 32
#
ip address-set CB_Info_HUB6 type object
 address 0 ********* mask 32              
#
ip address-set CB_Info_HUB7 type object
 address 0 ********* mask 32
#
ip address-set CSLC_PASS01_************ type object
 address 0 ************ mask 32
#
ip address-set *************. type object
 address 0 ************* mask 32
#
ip address-set CSLC_PASS03_************ type object
 address 0 ************ mask 32
#
ip address-set CSLC_PASS04_************ type object
 address 0 ************ mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********. type object    
 address 0 ********** mask 32
#
ip address-set **********/ type object
 address 0 ********** mask 32
#
ip address-set TAS*********1 type object
 address 0 *********1 mask 32
#
ip address-set TAS*********0 type object
 address 0 *********0 mask 32
#
ip address-set TTS*********0 type object
 address 0 *********0 mask 32
#
ip address-set (*********/24 type object
 address 0 ********* mask 24
#
ip address-set uuzz********** type object
 address 0 ********** mask 32
#
ip address-set *************. type object
 address 0 ************* mask 32
#                                         
ip address-set *************. type object
 address 0 ************* mask 32
#
ip address-set WSUS type object
 address 0 ********88 mask 32
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set ABSASFTP_ClusterIP_IN type object
 address 0 ********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set pas_************/32. type object
 address 0 ************ mask 32
#
ip address-set ABSBISOnline_Cluster_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnline_Cluster_OUT type object
 address 0 ********* mask 32              
#
ip address-set ABSBISOnline_Cluster_OUT_Group type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA01_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA01_OUT type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA02_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA02_OUT type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA03_OUT_ type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA04_OUT_ type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA05_OUT_ type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA06_OUT_ type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA07_OUT_ type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA08_OUT_ type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA09_OUT_ type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA03_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA04_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA05_IN type object
 address 0 ********* mask 32
#                                         
ip address-set ABSBISOnlineA06_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA07_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA08_IN type object
 address 0 ********* mask 32
#
ip address-set ABSBISOnlineA09_IN type object
 address 0 ********* mask 32
#
ip address-set PAS************ type object
 address 0 ************ mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set pasdb_************. type object
 address 0 ************ mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32           
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set CSLC_PASS02_************* type object
 address 0 ************* mask 32
#
ip address-set 500wan_************* type object
 address 0 ************* mask 32
#
ip address-set 500wan_************* type object
 address 0 ************* mask 32
#
ip address-set yunying_************ type object
 address 0 ************ mask 24
#
ip address-set pasdb_************. type object
 address 0 ************ mask 32
#
ip address-set TAS*********2 type object
 address 0 *********2 mask 32
#
ip address-set DUANXINGW type object
 address 0 *************** mask 32
#
ip address-set UUZZ2_******** type object
 address 0 ******** mask 24
#
ip address-set uuzz_**********/28 type object
 address 0 ********** mask 28
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#                                         
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *********1. type object
 address 0 *********1 mask 32
#
ip address-set *********2. type object
 address 0 *********2 mask 32
#
ip address-set TLS_*********3. type object
 address 0 *********3 mask 32
#
ip address-set *************. type object
 address 0 ************* mask 24
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32             
#
ip address-set *************. type object
 address 0 ************* mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set *********3. type object
 address 0 *********3 mask 32
 address 1 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********1. type object
 address 0 **********1 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#                                         
ip address-set **********2. type object
 address 0 **********2 mask 32
#
ip address-set pas_18.4.130.142/32. type object
 address 0 18.4.130.142 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set CSLC_103.11.1.12 type object
 address 0 103.11.1.12 mask 32
#
ip address-set CSLC_172.26.22.108 type object
 address 0 172.26.22.108 mask 32
#
ip address-set CSLC_172.26.22.200 type object
 address 0 172.26.22.200 mask 32          
#
ip address-set jingcaiwangsoureceip type object
 address 0 18.4.1.195 mask 32
#
ip address-set 安全扫描 type object
 address 0 ********** mask 32
#
ip address-set FSS01 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set OLTPAB type object
 address 0 ********* mask 32
 address 1 ********* mask 32
 address 2 ********* mask 32
#
ip address-set AS_********** type object
 address 0 ********** mask 32
#
ip address-set ********/24. type object   
 address 0 ******** mask 24
#
ip address-set **********. type object
 address 0 ******** mask 24
#
ip address-set group********* type object
 address 0 ********* mask 24
#
ip address-set **********/23. type object
 address 0 18.4.200.0 mask 23
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ********/32. type object
 address 0 ******** mask 32
#
ip address-set ********/16. type object
 address 0 ******** mask 16
#
ip address-set "SCP **********" type object
 address 0 ********** mask 24
#                                         
ip address-set "SCPAPIGW VIP" type object
 address 0 ********** mask 24
#
ip address-set yinwu_192.168.8.0 type object
 address 0 192.168.8.0 mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set SCPvip_*********01 type object
 address 0 *********01 mask 32
#
ip address-set zhifubao_172.16.2.0/24 type object
 address 0 172.16.2.0 mask 24
#
ip address-set SCPAPIGW_vip type object
 address 0 18.4.64.201 mask 32
#
ip address-set ********/24/ type object
 address 0 ******** mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24               
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set *********. type object     
 address 0 ********* mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set 18.4.3.1_50 type object
 address 0 range 18.4.3.1 18.4.3.50
#
ip address-set 18.4.4.1_18.4.4.50 type object
 address 0 range 18.4.4.1 18.4.4.50
#
ip address-set *********_24 type object
 address 0 range ********* **********
#
ip address-set 18.4.129_18.4.130 type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32
#                                         
ip address-set ***********/T1 type object
 address 0 *********** mask 32
#
ip address-set "New sporttery_**************" type object
 address 0 ************** mask 32
#
ip address-set "YunYing_Pay sys_**************" type object
 address 0 ************** mask 32
#
ip address-set ccbgw_********91 type object
 address 0 ********91 mask 32
#
ip address-set ccb_************* type object
 address 0 ************* mask 32
#
ip address-set ccb_************* type object
 address 0 ************* mask 32
#
ip address-set FAMBKGWA01_********1 type object
 address 0 ********1 mask 32
#
ip address-set FAMBKGMGA01_********* type object
 address 0 ********* mask 32              
#
ip address-set FAMELPGWA01_******** type object
 address 0 ******** mask 32
#
ip address-set FAMELPGWB02_******** type object
 address 0 ******** mask 32
#
ip address-set CSLC_************ type object
 address 0 ************ mask 32
#
ip address-set 新竟彩网 type object
 address 0 *********** mask 32
#
ip address-set webdc********* type object
 address 0 ********* mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 24
#
ip address-set 18..5.0.0. type object
 address 0 ******** mask 16
#
ip address-set *********/24. type object  
 address 0 ********* mask 24
#
ip address-set ********./27 type object
 address 0 ******** mask 27
#
ip address-set APIGWF5 type object
 address 0 ********** mask 32
#
ip address-set *********. type object
 address 0 ********* mask 24
#
ip address-set *********/22. type object
 address 0 ********* mask 22
#
ip address-set "********* /24" type object
 address 0 ********* mask 24
#
ip address-set tencent_***********/24 type object
 address 0 *********** mask 24
#
ip address-set **********(C043) type object
 address 0 ********** mask 32
 address 1 ******** mask 32               
 address 2 ******** mask 32
 address 3 ********** mask 32
 address 4 ********** mask 32
 address 5 ******** mask 32
 address 6 ******** mask 32
#
ip address-set liantongjingcai type object
 address 0 ************** mask 32
 address 1 ************** mask 32
#
ip address-set ***********; type object
 address 0 *********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set CSLC_************ type object
 address 0 ************ mask 32
#
ip address-set jimiji type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#                                         
ip address-set jiamiji type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
#
ip address-set To_T2&T3_************* type object
 address 0 ************* mask 32
#
ip address-set zhifu_********/24_********/24 type object
 address 0 ******** mask 24
 address 1 ******** mask 24
#
ip address-set zabbix_***********&232 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set Corenat********/8 type object
 address 0 ******** mask 8
#
ip address-set Tencent_Coudnet_**********/16 type object
 address 0 ********** mask 16
#                                         
ip address-set OMS_*********/24 type object
 address 0 ********* mask 24
#
ip address-set TencentCloud type object
 address 0 10.0.0.0 mask 8
#
ip address-set WechatNum_*********** type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 ********10 mask 32
#
ip address-set ********. type object
 address 0 ******** mask 32
#
ip address-set ********. type object
 address 0 ******** mask 32
#
ip address-set ***********_ type object
 address 0 *********** mask 32
#
ip address-set *********_ type object
 address 0 ********* mask 32
#                                         
ip address-set ELP_F5 type object
 address 0 ************ mask 32
#
ip address-set ********/8_ type object
 address 0 ******** mask 8
#
ip address-set ceshiClient_********/24 type object
 address 0 ******** mask 24
#
ip address-set APIGW type object
 address 0 ******** mask 32
 address 1 ******** mask 32
#
ip address-set YunYingzhifu type object
 address 0 *************** mask 32
#
ip address-set F5_PreGW type object
 address 0 *********** mask 32
#
ip address-set PREGW type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32            
 address 3 *********** mask 32
 address 4 *********** mask 32
 address 5 *********** mask 32
#
ip address-set new_elp type object
 address 0 ************ mask 32
#
ip address-set ab_ip type object
 address 0 *********** mask 24
#
ip address-set New_Sit type object
 address 0 ********** mask 24
 address 1 ********* mask 21
#
ip address-set Core type object
 address 0 ********* mask 21
 address 1 ********** mask 24
#
ip address-set zhifu_core type object
 address 0 ********** mask 32
 address 1 *********** mask 32
#
ip address-set keji type object           
 address 0 ************ mask 32
 address 1 ************* mask 32
#
ip address-set EFTGW type object
 address 0 ********* mask 32
 address 1 ********* mask 32
 address 2 ********* mask 32
#
ip address-set ********_ type object
 address 0 ******** mask 32
#
ip address-set zabbix_********* type object
 address 0 ********* mask 32
#
ip address-set zabbix_client type object
 address 0 ********** mask 32
#
ip address-set zhifu1.1 type object
 address 0 *********** mask 32
 address 1 18.5.128.12 mask 32
 address 2 *********** mask 32
#
ip address-set yunyingzhifu type object   
 address 0 20.0.101.45 mask 32
 address 1 20.0.101.46 mask 32
 address 2 20.0.101.47 mask 32
 address 3 20.0.101.48 mask 32
 address 4 20.0.101.49 mask 32
#
ip address-set 500wan__ type object
 address 0 10.0.1.210 mask 31
 address 1 10.0.1.212 mask 31
#
ip address-set keji_elpgw type object
 address 0 172.26.14.102 mask 32
#
ip address-set *********_ type object
 address 0 ********* mask 32
#
ip address-set **********_ type object
 address 0 ********** mask 32
#
ip address-set ********_ type object
 address 0 ******** mask 32
#
ip address-set ********01_ type object    
 address 0 ********01 mask 32
#
ip address-set duanxingw type object
 address 0 18.5.32.221 mask 32
 address 1 18.5.32.222 mask 32
 address 2 ********10 mask 32
#
ip address-set Ansible_Server type object
 address 0 18.0.10.100 mask 32
#
ip address-set zhifu1.1_5 type object
 address 0 ********21 mask 32
 address 1 ********22 mask 32
 address 2 ********12 mask 32
 address 3 18.5.40.82 mask 31
 address 4 18.5.40.84 mask 32
 address 5 18.5.40.42 mask 31
 address 6 18.5.40.44 mask 32
 address 7 18.5.40.1 mask 32
 address 8 18.5.40.2 mask 31
 address 9 18.5.40.4 mask 32
 address 10 18.5.40.81 mask 32
 address 11 18.5.40.41 mask 32            
 address 12 18.2.1.100 mask 32
 address 13 ********** mask 32
 address 14 ******** mask 32
 address 15 *********** mask 32
 address 16 18.5.32.81 mask 32
 address 17 18.5.32.82 mask 32
 address 18 18.5.32.83 mask 32
 address 19 18.5.32.84 mask 32
 address 20 18.2.1.121 mask 32
 address 21 18.5.128.10 mask 31
 address 22 18.5.128.12 mask 31
 address 23 ********31 mask 32
 address 24 ********32 mask 32
 address 25 18.5.34.8 mask 32
 address 26 ********* mask 29
 address 27 18.0.10.123 mask 32
 address 28 18.0.10.121 mask 32
 address 29 18.0.10.122 mask 32
 address 30 18.0.10.124 mask 32
#
ip address-set zhifu_ip.port=21514 type object
 address 0 ********31 mask 32
#                                         
ip address-set zhifu1.1_webserver type object
 address 0 18.5.40.82 mask 31
 address 1 18.5.40.84 mask 32
 address 2 18.5.40.42 mask 31
 address 3 18.5.40.44 mask 32
 address 4 18.5.40.1 mask 32
 address 5 18.5.40.2 mask 31
 address 6 18.5.40.4 mask 32
 address 7 18.5.40.81 mask 32
 address 8 18.5.40.41 mask 32
#
ip address-set To_Yinlian type object
 address 0 ********21 mask 32
 address 1 ********22 mask 32
 address 2 ********12 mask 32
#
ip address-set Core_*********/24 type object
 address 0 ********* mask 24
#
ip address-set To_Docker_7001 type object
 address 0 18.5.65.120 mask 32
 address 1 18.5.65.121 mask 32
 address 2 18.5.65.122 mask 32            
 address 3 18.5.65.123 mask 32
#
ip address-set Core_net type object
 address 0 *********** mask 32
 address 1 ********* mask 32
 address 2 ********* mask 29
 address 3 18.5.34.8 mask 32
 address 4 *********** mask 32
#
ip address-set 500Wan type object
 address 0 10.0.1.211 mask 32
 address 1 113.98.237.181 mask 32
#
ip address-set openstack type object
 address 0 18.0.10.121 mask 32
 address 1 18.0.10.122 mask 32
 address 2 18.0.10.123 mask 32
#
ip address-set Dsvs_18.2.1.245 type object
 address 0 18.2.1.245 mask 32
#
ip address-set PPTP type object
 address 0 18.0.10.123 mask 32            
 address 1 18.0.10.121 mask 32
#
ip address-set ********/32_ type object
 address 0 ******** mask 32
#
ip address-set Yunying20180206 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 172.16.3.49 mask 32
 address 5 172.16.3.44 mask 32
#
ip address-set From_yunying_to_T1 type object
 address 0 18.5.128.10 mask 32
 address 1 18.5.128.11 mask 32
 address 2 18.5.128.12 mask 32
 address 3 18.5.33.81 mask 32
 address 4 18.5.33.82 mask 32
 address 5 ********* mask 32
 address 6 ********** mask 32
 address 7 range 18.5.34.1 18.5.34.8
 address 8 18.5.32.81 mask 32             
 address 9 *********** mask 32
 address 10 *********** mask 32
 address 11 *********** mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set "********** /24" type object
 address 0 ********** mask 24
#
ip address-set test18.2.17.102 type object
 address 0 18.2.17.102 mask 32
#
ip address-set F5VIP********** type object
 address 0 ********** mask 32
#
ip address-set "********* /24" type object
 address 0 ********* mask 24
#
ip address-set "********* /24" type object
 address 0 ********* mask 24
#
ip address-set "********* /24" type object
 address 0 ********* mask 24
#
ip address-set "********* /24" type object
 address 0 ********* mask 24
#
ip address-set "******** /32" type object
 address 0 ******** mask 32
 address 1 *********** mask 32
#
ip address-set "********* /24" type object
 address 0 ********* mask 24
#
ip address-set "********* /24" type object
 address 0 ********* mask 24
#
ip address-set "********** /24" type object
 address 0 ********** mask 24             
#
ip address-set "********** /24" type object
 address 0 ********** mask 24
#
ip address-set yygg type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
#
ip address-set yy type object
 address 0 ******** mask 32
#
ip address-set **********_ type object
 address 0 ********** mask 32
#
ip address-set ***********_ type object
 address 0 *********** mask 32
#
ip address-set TIDB_RS type object
 address 0 ********* mask 32
 address 1 ********* mask 32
 address 2 ********* mask 32              
 address 3 ********* mask 32
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ********73_ type object
 address 0 ********73 mask 32
#
ip address-set G3 type object
 address 0 ******** mask 16
#
ip address-set 18_5_252_0 type object
 address 0 ********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#                                         
ip address-set *************/24. type object
 address 0 ************* mask 24
#
ip address-set elpgw_*********/72 type object
 address 0 ********* mask 32
 address 1 ********* mask 32
#
ip address-set **************_16 type object
 address 0 ************** mask 32
 address 1 ************** mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#                                         
ip address-set **************_236 type object
 address 0 ************** mask 32
 address 1 ************** mask 32
 address 2 ************** mask 32
#
ip address-set ***********/24_ type object
 address 0 *********** mask 24
#
ip address-set **********/24_ type object
 address 0 ********** mask 24
#
ip address-set ************_ type object
 address 0 ************ mask 32
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set 18.5.252/253 type object
 address 0 range ************ ************
 address 1 range *********** ***********
 address 2 range *********** ***********
 address 3 range *********** ***********
 address 4 range *********** ***********  
 address 5 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********/68 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set **********71. type object
 address 0 **********71 mask 32
#
ip address-set **********72. type object
 address 0 **********72 mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32           
 address 1 ************ mask 32
#
ip address-set ***********_63 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 *********** mask 32
 address 5 *********** mask 32
#
ip address-set **********_63 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 24
#
ip address-set TICai_APP type object
 address 0 10.20.0.0 mask 16
#
ip address-set 10.50.5.0. type object
 address 0 10.50.5.0 mask 24              
#
ip address-set T1Cai_APP type object
 address 0 10.20.0.0 mask 16
#
ip address-set 172.30.*.*. type object
 address 0 172.30.10.12 mask 32
 address 1 172.30.10.7 mask 32
 address 2 172.30.20.142 mask 32
 address 3 172.30.40.15 mask 32
 address 4 172.30.10.10 mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set 18.5.128.* type object
 address 0 18.5.128.10 mask 32
 address 1 18.5.128.11 mask 32            
 address 2 18.5.128.12 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set 10.50.5.81. type object
 address 0 10.50.5.81 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set *************. type object
 address 0 ************* mask 24
#                                         
ip address-set 18.5.6.13_TO_18 type object
 address 0 range 18.5.6.13 18.5.6.18
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********_211_212_213 type object
 address 0 ********** mask 32
 address 1 ***********1 mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set *********/22. type object
 address 0 ********* mask 22
#
ip address-set ************/32. type object
 address 0 ************ mask 32           
#
ip address-set ***********. type object
 address 0 *********** mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/24_ type object
 address 0 ********* mask 24
#
ip address-set ********37/32_ type object 
 address 0 ********37 mask 32
#
ip address-set ***********/32_ type object
 address 0 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set T1_VC type object
 address 0 ********** mask 255.255.254.0
#
ip address-set Mips_VC type object
 address 0 ************ mask 24
#
ip address-set XWH_T3 type object
 address 0 172.16.12.0 mask 22
 address 1 172.16.8.0 mask 22
 address 2 *********** mask 22
#
ip address-set T1_vlan10 type object      
 address 0 ******** mask 16
#
ip address-set 192_168_32_15 type object
 address 0 ************* mask 32
#
ip address-set *************/************** type object
 address 0 ************* mask 32
#
ip address-set 172.16.11.0_24 type object
 address 0 172.16.11.0 mask 24
#
ip address-set 18.0.204.1. type object
 address 0 18.0.204.1 mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set 18.7.12.16. type object
 address 0 18.7.12.16 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
 address 1 *********** mask 32            
#
ip address-set 172.16.110.0_24 type object
 address 0 172.16.110.0 mask 24
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set 18.0.99.99_100 type object
 address 0 range 18.0.99.99 18.0.99.100
#
ip address-set **********_16 type object
 address 0 ********** mask 16
#
ip address-set 18.1.2.241_32 type object
 address 0 18.1.2.241 mask 32
#
ip address-set 500wan_10.0.1.211/32 type object
 address 0 10.0.1.211 mask 32
#
ip address-set T3_38.5.80.0/21 type object
 address 0 38.5.80.0 mask 21
#
ip address-set T3_38.5.72.0/21 type object
 address 0 38.5.72.0 mask 21
#
ip address-set XWHT3_tiaobanji_********/24 type object
 address 0 ******** mask 24
#
ip address-set 38.5.34.0_24 type object
 address 0 38.5.34.0 mask 24
#
ip address-set 28.4.246.0_24 type object
 address 0 28.4.246.0 mask 24
#
ip address-set ********/22. type object
 address 0 ******** mask 22
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#                                         
ip address-set 192.168.66.150_32 type object
 address 0 192.168.66.150 mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set *********4. type object
 address 0 *********4 mask 32
#
ip address-set YunGuanPingTai_172.16.16.1 type object
 address 0 172.16.16.1 mask 32
#
ip address-set ********/32_ type object
 address 0 ******** mask 32
#
ip address-set ***************/32_ type object
 address 0 *************** mask 32
#
ip address-set T3_38.2.1.0/24 type object
 address 0 38.2.1.0 mask 24               
#
ip address-set XWH_172.16.7.0/24 type object
 address 0 172.16.7.0 mask 24
#
ip address-set *************/24. type object
 address 0 ************* mask 24
#
ip address-set ********/32. type object
 address 0 ******** mask 32
#
ip address-set XWH_T3_********30 type object
 address 0 ********30 mask 32
#
ip address-set T1_JMJ_18.2.1.244 type object
 address 0 18.2.1.244 mask 32
#
ip address-set XWH_T4NAT_********/24 type object
 address 0 ******** mask 24
#
ip address-set XWH_T3_********31 type object
 address 0 ********31 mask 32
#
ip address-set XWH_G3_********/24 type object
 address 0 ******** mask 24
#
ip address-set XWH_T4_NAT_********** type object
 address 0 ********** mask 32
#
ip address-set XWH_T4_tiaobanji_********* type object
 address 0 ********* mask 32
#
ip address-set XWH_T4_tiaobanji_********* type object
 address 0 ********* mask 32
#
ip address-set XWH_T4_NAT_********* type object
 address 0 ********* mask 32
#
ip address-set XWH_T4_NAT_********* type object
 address 0 ********* mask 32
#
ip address-set BG_************** type object
 address 0 ************** mask 32
#
ip address-set BG_************** type object
 address 0 ************** mask 32
#                                         
ip address-set XWH_T4_*********_9 type object
 address 0 range ********* *********
#
ip address-set XWH_T4_NAT_*********_31 type object
 address 0 range ********* *********
#
ip address-set T1_*********** type object
 address 0 *********** mask 32
#
ip address-set **********_ type object
 address 0 ********** mask 32
#
ip address-set XWHG3_*********_NAT_********** type object
 address 0 ********** mask 32
#
ip address-set XWHG3_*********/24 type object
 address 0 ********* mask 24
#
ip address-set XWHG3_*********/21 type object
 address 0 ********* mask 21
#
ip address-set XWHG3_*********/24 type object
 address 0 ********* mask 24              
#
ip address-set CSLC_***********/24 type object
 address 0 *********** mask 24
#
ip address-set XWH_T3_******** type object
 address 0 ******** mask 32
#
ip address-set ********. type object
 address 0 ******** mask 24
#
ip address-set **********_16_17 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 ********** mask 32
#
ip address-set *********/21_ type object
 address 0 ********* mask 21
#
ip address-set **********/32_ type object
 address 0 ********** mask 32
#
ip address-set *********/24_ type object
 address 0 ********* mask 24              
#
ip address-set CSLC_*************/24 type object
 address 0 ************* mask 24
#
ip address-set XWH_**********_8 type object
 address 0 range ********** **********
#
ip address-set BG_************* type object
 address 0 ************* mask 32
#
ip address-set ************_103 type object
 address 0 range ************ ************
#
ip address-set BG_************** type object
 address 0 ************** mask 32
#
ip address-set XWH_**********01_103 type object
 address 0 range **********01 **********03
#
ip address-set XWHG3_**********_37 type object
 address 0 range ********** **********
#
ip address-set *********. type object     
 address 0 ********* mask 24
#
ip address-set *********_9 type object
 address 0 range ********* *********
#
ip address-set T1_********/32 type object
 address 0 ******** mask 32
#
ip address-set XWH_T4MS_*********/32 type object
 address 0 ********* mask 32
#
ip address-set ********33. type object
 address 0 ********33 mask 32
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set *************. type object
 address 0 ************* mask 32
#
ip address-set **********/24_ type object
 address 0 ********** mask *************
#                                         
ip address-set CSLC_TiaoBanJi_********** type object
 address 0 ********** mask 32
#
ip address-set CSLC_TiaoBanJi_********** type object
 address 0 ********** mask 32
#
ip address-set CSLC_********** type object
 address 0 ********** mask 32
#
ip address-set CSLC_********** type object
 address 0 ********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ********0. type object
 address 0 ********0 mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ********35/136 type object
 address 0 ********35 mask 32             
 address 1 ********36 mask 32
#
ip address-set ***************/224 type object
 address 0 *************** mask 32
 address 1 *************** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ********01. type object
 address 0 ********01 mask 32
#
ip address-set *************. type object
 address 0 ************* mask 24
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set G3_********* type object
 address 0 ********* mask 16
#
ip address-set T3_********* type object
 address 0 ********* mask 32              
#
ip address-set ************/ type object
 address 0 ************ mask 32
#
ip address-set T3_NAT_******** type object
 address 0 ******** mask 24
#
ip address-set **********_84 type object
 address 0 range ********** **********
#
ip address-set ***********//28 type object
 address 0 *********** mask 28
#
ip address-set *********//27 type object
 address 0 ********* mask 24
#
ip address-set *********./24 type object
 address 0 ********* mask 24
#
ip address-set "G3_*********/16 to T1" type object
 address 0 ********* mask 16
#
ip address-set ********32. type object    
 address 0 ********32 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 24
#
ip address-set XWH_G3_*******/8 type object
 address 0 ******* mask 8
#
ip address-set ********. type object
 address 0 ******** mask 32
#
ip address-set *******./8 type object
 address 0 ******* mask 8
#
ip address-set *************. type object
 address 0 ************* mask 32
#
ip address-set ops_********11 type object
 address 0 ********11 mask 32
#
ip address-set OPS_********12 type object
 address 0 ********12 mask 32
#                                         
ip address-set T4MS_18.5.99.11 type object
 address 0 18.5.99.11 mask 32
#
ip address-set OPS_********13 type object
 address 0 ********13 mask 32
#
ip address-set T4MS_18.6.4.36 type object
 address 0 18.6.4.36 mask 32
#
ip address-set G3_MS_4.190.120.0/22 type object
 address 0 4.190.120.0 mask 22
#
ip address-set GWAMSF5_18.5.98.15 type object
 address 0 18.5.98.15 mask 32
#
ip address-set ********42. type object
 address 0 ********42 mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set qc_************* type object
 address 0 ************* mask 32          
#
ip address-set 18.5.48.101/.32 type object
 address 0 18.5.48.101 mask 32
#
ip address-set ********44/.32 type object
 address 0 ********44 mask 32
#
ip address-set ********43. type object
 address 0 ********43 mask 32
#
ip address-set ***********./24 type object
 address 0 *********** mask 24
#
ip address-set CSLC_104.21.2.0/24 type object
 address 0 104.21.2.0 mask 24
#
ip address-set ********. type object
 address 0 ******** mask 24
#
ip address-set G3AMS_18.5.84.0./24 type object
 address 0 18.5.84.0 mask 24
#
ip address-set ********37. type object    
 address 0 ********37 mask 32
#
ip address-set **********./24 type object
 address 0 ********** mask 24
#
ip address-set ***********./24 type object
 address 0 *********** mask 24
#
ip address-set ***********./24 type object
 address 0 18.0.11.0 mask 24
#
ip address-set ***********./32 type object
 address 0 *********** mask 32
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#
ip address-set T1_18.0.254.242/32 type object
 address 0 18.0.254.242 mask 32
#                                         
ip address-set XWH_G3_4.176.0.0/24 type object
 address 0 4.176.0.0 mask 24
#
ip address-set ***********/32_ type object
 address 0 *********** mask 32
#
ip address-set *************/32_ type object
 address 0 ************* mask 32
#
ip address-set CSLC_104.21.2.51/32 type object
 address 0 104.21.2.51 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set *********14. type object
 address 0 *********14 mask 32
#
ip address-set ********46/32. type object
 address 0 ********46 mask 32
#
ip address-set *************&************ type object
 address 0 ************* mask 32          
 address 1 ************ mask 32
#
ip address-set T1_4.190.121.231/32 type object
 address 0 4.190.121.231 mask 32
#
ip address-set G3_MS_4.190.121.231/32 type object
 address 0 4.190.121.231 mask 32
#
ip address-set XWH_G3_4.176.1.0/24 type object
 address 0 4.176.1.0 mask 24
#
ip address-set G3_ILO_4.191.249.0/24 type object
 address 0 4.191.249.0 mask 24
#
ip address-set G3_MS_4.190.122.0/24 type object
 address 0 4.190.122.0 mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ********47/32. type object
 address 0 ********47 mask 32
#                                         
ip address-set ********48/32. type object
 address 0 ********48 mask 32
#
ip address-set ***********/98 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set ********. type object
 address 0 ******** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 24
#
ip address-set 172.20.16.0. type object
 address 0 172.20.16.0 mask 24
#
ip address-set ********49/32. type object
 address 0 ********49 mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set T3_NAT_18.6.3.72 type object
 address 0 18.6.3.72 mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set XWH_T4_NAT_18.6.4.134 type object
 address 0 18.6.4.134 mask 32
#
ip address-set ********./24 type object
 address 0 ******** mask 24
#
ip address-set XWH_磁带机_18.5.85.1 type object
 address 0 18.5.85.1 mask 32
#
ip address-set XWH_磁带机_18.5.85.2 type object
 address 0 18.5.85.2 mask 32
#                                         
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *********11/32. type object
 address 0 *********11 mask 32
#
ip address-set T4_48.0.100.0/24 type object
 address 0 48.0.100.0 mask 24
#
ip address-set *********1/32. type object
 address 0 *********1 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32            
#
ip address-set T4_********* type object
 address 0 ********* mask 24
#
ip address-set **************/22/************** type object
 address 0 ************** mask 32
 address 1 ************** mask 32
 address 2 ************** mask 32
#
ip address-set *********_44 type object
 address 0 range ********* *********
#
ip address-set ********/32. type object
 address 0 ******** mask 32
#
ip address-set **********_80 type object
 address 0 range ********** **********
#
ip address-set T3_********/24 type object
 address 0 ******** mask 24
#
ip address-set T3_Harbor_********** type object
 address 0 ********** mask 32             
#
ip address-set 500WAN_************* type object
 address 0 ************* mask 32
#
ip address-set 500WAN_************ type object
 address 0 ************ mask 32
#
ip address-set ********/8. type object
 address 0 ******** mask 8
#
ip address-set ***********_ type object
 address 0 *********** mask 32
#
ip address-set ***********/32_ type object
 address 0 *********** mask 32
#
ip address-set T3_***********/32 type object
 address 0 *********** mask 32
#
ip address-set T3_*********/32 type object
 address 0 ********* mask 32
#
ip address-set T3_*********/32 type object
 address 0 ********* mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ********34/32. type object
 address 0 ********34 mask 32
#
ip address-set ********88/32. type object
 address 0 ********88 mask 32
#
ip address-set WSUS_Internet type object
 address 0 ************ mask 32
 address 1 ************* mask 32
 address 2 ************ mask 32
 address 3 ************* mask 32
 address 4 ************ mask 32
#
ip address-set ********39/32. type object
 address 0 ********39 mask 32
#
ip address-set FangBingDu_Internet type object
 address 0 ************** mask 32         
 address 1 ************* mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/22. type object
 address 0 *********** mask 22
#
ip address-set ************/24. type object
 address 0 ************ mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set T2N1_********** type object
 address 0 ********** mask 32
#
ip address-set 500wan_********/24 type object
 address 0 ******** mask 24
#                                         
ip address-set JCW_*********** type object
 address 0 *********** mask 32
#
ip address-set T1_********/32 type object
 address 0 ******** mask 32
#
ip address-set XWH_**********_9 type object
 address 0 range ********** **********
#
ip address-set T1_********04/32 type object
 address 0 ********04 mask 32
#
ip address-set T2_T3_********** type object
 address 0 ********** mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32            
#
ip address-set T4_********** type object
 address 0 ********** mask 32
#
ip address-set T4_********** type object
 address 0 ********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set CSLC_TiaoBanJi_********** type object
 address 0 ********** mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set *********4/32. type object
 address 0 *********4 mask 32
#
ip address-set **********/32. type object 
 address 0 ********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ********/32. type object
 address 0 ******** mask 32
#                                         
ip address-set ********/32. type object
 address 0 ******** mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set CSLC_***********/24 type object
 address 0 *********** mask 24
#
ip address-set CSLC_***********/24 type object
 address 0 *********** mask 24
#
ip address-set ********. type object
 address 0 ******** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32             
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *********5. type object
 address 0 *********5 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 24
#
ip address-set XWH_T1_4.191.80.0/24 type object
 address 0 4.191.80.0 mask 24
#
ip address-set CSLC_172.20.23.0/24 type object
 address 0 172.20.23.0 mask 24
#
ip address-set CSLC_172.20.30.0/24 type object
 address 0 172.20.30.0 mask 24
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set **********/16. type object
 address 0 ********** mask 16
#
ip address-set **********./24 type object
 address 0 ********** mask 16
#
ip address-set NAT_27.16.18.10. type object
 address 0 27.16.18.10 mask 32
#
ip address-set JiGuan_192.168.182.130 type object
 address 0 192.168.182.130 mask 32
#
ip address-set ********/16. type object
 address 0 ******** mask 16
#
ip address-set T4_172.16.8.0/24 type object
 address 0 172.16.8.0 mask 24
#
ip address-set **********. type object
 address 0 ********** mask 32
#                                         
ip address-set *********. type object
 address 0 ********* mask 24
#
ip address-set CSLC_104.23.0.10 type object
 address 0 104.23.0.10 mask 32
#
ip address-set CSLC_104.23.0.11 type object
 address 0 104.23.0.11 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set V3_CORE_4.190.80.0/21 type object
 address 0 4.190.80.0 mask 21
#
ip address-set 18.0.2.37. type object
 address 0 18.0.2.37 mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32              
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set *********/21. type object
 address 0 ********* mask 21
#
ip address-set *********. type object
 address 0 ********* mask 24
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ********/8. type object
 address 0 ******** mask 8
#
ip address-set T1_******** type object
 address 0 ******** mask 32
#
ip address-set *********12_214 type object
 address 0 range *********12 *********14
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ********./16 type object
 address 0 ******** mask 16
#
ip address-set BG_192.168.249.0/24 type object
 address 0 192.168.249.0 mask 24
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set V3_GW_4.190.40.0/21 type object
 address 0 4.190.40.0 mask 21
#                                         
ip address-set ***********./32 type object
 address 0 *********** mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set T1_18.0.2.133/32 type object
 address 0 18.0.2.133 mask 32
#
ip address-set V3_4.190.80.73/32 type object
 address 0 4.190.80.73 mask 32
#
ip address-set *************/24. type object
 address 0 ************* mask 24
#
ip address-set ***********./32 type object
 address 0 *********** mask 32
#
ip address-set ***********./32 type object
 address 0 *********** mask 32
#
ip address-set ************./32 type object
 address 0 ************ mask 32           
#
ip address-set YYGSXNZM_192.168.245.0 type object
 address 0 192.168.245.0 mask 24
#
ip address-set YunYing_192.168.215.50 type object
 address 0 192.168.215.50 mask 32
#
ip address-set YunYing_192.168.215.51 type object
 address 0 192.168.215.51 mask 32
#
ip address-set 4F_18.2.1.120_123 type object
 address 0 range 18.2.1.120 **********
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **************/26. type object
 address 0 ************** mask 26
#
ip address-set 18.5.32.131_147 type object
 address 0 range 18.5.32.131 18.5.32.147
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ********_8 type object
 address 0 range ******** ********
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#                                         
ip address-set *********./24 type object
 address 0 ********* mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set CSLC_104.23.0.12_14 type object
 address 0 range 104.23.0.12 104.23.0.14
#
ip address-set CSLC_**********/24 type object
 address 0 ********** mask 24
#
ip address-set T19_18.6.19.0 type object
 address 0 18.6.19.0 mask 24
#
ip address-set **********8_99 type object
 address 0 range **********8 104.23.0.99
#
ip address-set CSLC_104.21.1.0/24 type object
 address 0 104.21.1.0 mask 24
#
ip address-set **********/16. type object
 address 0 ********** mask 16             
#
ip address-set ***************/32. type object
 address 0 *************** mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********./24 type object
 address 0 *********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set T19_18.6.19.4 type object
 address 0 ********* mask 32
#
ip address-set ********7_18 type object
 address 0 range ********7 ********8
#
ip address-set XWH_T4_***********/32 type object
 address 0 *********** mask 32
#
ip address-set ********7. type object
 address 0 ********7 mask 32
#
ip address-set ********8. type object
 address 0 ********8 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ********. type object
 address 0 ******** mask 32
#                                         
ip address-set ********. type object
 address 0 ******** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set XWH_T4_***********/32 type object
 address 0 *********** mask 32
#
ip address-set T4_*********/24 type object
 address 0 ********* mask 24
#
ip address-set T1_**********/32 type object
 address 0 ********** mask 32
#
ip address-set T4_**********/32 type object
 address 0 ********** mask 32
#
ip address-set T1_**********_213 type object
 address 0 range ********** **********    
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set ***********./32 type object
 address 0 *********** mask 32
#
ip address-set ***********./24 type object
 address 0 *********** mask 24
#
ip address-set uazz_********/24 type object
 address 0 ******** mask 24
#
ip address-set ************./32 type object
 address 0 ************ mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set XWHT4_********** type object
 address 0 ********** mask 32
#
ip address-set XWH_T4_DMZ_**********_12 type object
 address 0 range ********** **********
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#                                         
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set ********27./32 type object
 address 0 ********27 mask 32
#
ip address-set *************./32 type object
 address 0 ************* mask 32
#
ip address-set XWT_T4_*********/32 type object
 address 0 ********* mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set *************/24. type object
 address 0 ************* mask 24          
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set *************/24. type object
 address 0 ************* mask 24
#
ip address-set ***************/32. type object
 address 0 *************** mask 32
#
ip address-set ***************/32. type object
 address 0 *************** mask 32
#
ip address-set 28.0.7.12./32 type object
 address 0 28.0.7.12 mask 32
#
ip address-set ***********./32 type object
 address 0 *********** mask 32
#
ip address-set ************/24. type object
 address 0 ************ mask 24
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set T4_48.1.14.0 type object
 address 0 48.1.14.0 mask 24
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#                                         
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set T4_68.0.0.0/8 type object
 address 0 68.0.0.0 mask 8
#
ip address-set *********./24 type object
 address 0 ********* mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set SDN_172.16.21.1/32 type object
 address 0 172.16.21.1 mask 32            
#
ip address-set *********85/32. type object
 address 0 *********85 mask 32
#
ip address-set *********86/32. type object
 address 0 *********86 mask 32
#
ip address-set *********. type object
 address 0 ********* mask 24
#
ip address-set ************./32 type object
 address 0 ************ mask 32
#
ip address-set T1_18.2.1.34/32 type object
 address 0 18.2.1.34 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set T4_18.5.97.0 type object
 address 0 18.5.97.0 mask 24
#
ip address-set *********/32. type object  
 address 0 ********* mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set NTP_********83 type object
 address 0 ********83 mask 32
#
ip address-set SDAS_18.5.80.161_162 type object
 address 0 range 18.5.80.161 18.5.80.162
#
ip address-set RTQ_18.5.84.81_82 type object
 address 0 range 18.5.84.81 18.5.84.82
#
ip address-set NTP_18.0.216.78 type object
 description 测试手动调快12小时
 address 0 18.0.216.78 mask 32
#
ip address-set kaifaceshitools_********0 type object
 address 0 ********0 mask 32
#
ip address-set UMP_18.0.117.3 type object
 address 0 18.0.117.3 mask 32             
#
ip address-set BASDB_********5 type object
 address 0 ********5 mask 32
#
ip address-set *********6_239 type object
 address 0 range *********6 *********9
#
ip address-set **********_93 type object
 address 0 range ********** **********
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set *********6. type object
 address 0 *********7 mask 32
#
ip address-set *********6/32. type object 
 address 0 *********6 mask 32
#
ip address-set *********7/32. type object
 address 0 *********7 mask 32
#
ip address-set *********8/32. type object
 address 0 *********8 mask 32
#
ip address-set *********9/32. type object
 address 0 *********9 mask 32
#
ip address-set 18.5.85.11_12 type object
 address 0 range 18.5.85.11 18.5.85.12
#
ip address-set NTP_********** type object
 address 0 ********** mask 32
#
ip address-set ********./32 type object
 address 0 ******** mask 32
#
ip address-set T1_AD_F5_18.0.10.200 type object
 address 0 18.0.10.200 mask 32
#                                         
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set 104.23.11.10/32. type object
 address 0 104.23.11.10 mask 32
#
ip address-set ***********1. type object
 address 0 ***********1 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********./24 type object
 address 0 ********* mask 24
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32            
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set *********. type object
 address 0 ********* mask 24
#
ip address-set 18.5.88.216./32 type object
 address 0 18.5.88.216 mask 32
#
ip address-set MGMT_18.4.0.0/24 type object
 address 0 18.4.0.0 mask 24
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set *************. type object
 address 0 ************* mask 32
#
ip address-set *********. type object     
 address 0 ********* mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#                                         
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set XWHT3_18.5.81.5/32 type object
 address 0 18.5.81.5 mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ************/32. type object
 address 0 ************ mask 32           
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set *********01/32. type object
 address 0 *********01 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set T5_************ type object
 address 0 ************ mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set XWH_********28/32 type object
 address 0 ********28 mask 32
#
ip address-set CSLC************** type object
 address 0 ************** mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#                                         
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32             
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ************./32 type object
 address 0 ************ mask 32
#
ip address-set ************./32 type object
 address 0 ************ mask 32
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#
ip address-set zhiqi_************ type object
 address 0 ************ mask 32
#
ip address-set *********./32 type object
 address 0 ********* mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set *************/24. type object
 address 0 ************* mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ********/32. type object
 address 0 ******** mask 32
#
ip address-set ********0/32. type object
 address 0 ********0 mask 32
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#                                         
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set ***********/32. type object
 address 0 ********* mask 24
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set .**********/24 type object
 address 0 ********** mask 24
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32            
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set **********/16. type object
 address 0 ********** mask 16
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ********20/32. type object
 address 0 ********20 mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set NFS_18.6.4.48/32 type object
 address 0 18.6.4.48 mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#                                         
ip address-set 192.168.116.254/32. type object
 address 0 192.168.116.254 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********0. type object
 address 0 **********0 mask 32
#
ip address-set ***********../24 type object
 address 0 *********** mask 24
#
ip address-set ***************. type object
 address 0 *************** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32            
#
ip address-set ************/24. type object
 address 0 ************ mask 24
#
ip address-set zabbix_*********01 type object
 address 0 *********01 mask 32
#
ip address-set Solarwinds*********00 type object
 address 0 *********00 mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set 104.21.20.26. type object  
 address 0 104.21.20.26 mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ********/32. type object
 address 0 ******** mask 32
#                                         
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set solarwinds_*********00 type object
 address 0 *********00 mask 32
#
ip address-set TXY_10.215.41.178 type object
 address 0 10.215.41.178 mask 32
#
ip address-set T1_*********/24 type object
 address 0 ********* mask 24
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#
ip address-set *************./32 type object
 address 0 ************* mask 32          
#
ip address-set **************./32 type object
 address 0 ************** mask 32
#
ip address-set ***********./32 type object
 address 0 *********** mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set 18.0.71.0_18.0.75.0 type object
 address 0 18.0.71.0 mask 24
 address 1 18.0.72.0 mask 24
 address 2 18.0.73.0 mask 24
 address 3 18.0.74.0 mask 24
 address 4 18.0.75.0 mask 24
#                                         
ip address-set ************/24. type object
 address 0 ************ mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********./24 type object
 address 0 ********** mask 24
#
ip address-set 192.168.65.140/32. type object
 address 0 192.168.65.140 mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set ***************/32. type object
 address 0 *************** mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32            
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set SDN_4.189.0.0/16 type object
 address 0 4.189.0.0 mask 16
#
ip address-set T1_NAT_78.0.0.0/16 type object
 address 0 78.0.0.0 mask 16
#
ip address-set T1_18.2.1.9 type object
 address 0 18.2.1.9 mask 32
#
ip address-set T1_18.2.1.224 type object
 address 0 18.2.1.224 mask 32
#
ip address-set T1_18.2.1.249 type object
 address 0 18.2.1.249 mask 32
#
ip address-set T4_18.6.4.51 type object
 address 0 18.6.4.51 mask 32
#
ip address-set **********. type object    
 address 0 ********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set *************/24. type object
 address 0 ************* mask 24
#                                         
ip address-set 10.216.23.0/24. type object
 address 0 10.216.23.0 mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set *********6/32. type object
 address 0 *********6 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set *************./32 type object
 address 0 ************* mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24             
#
ip address-set 28.0.122.194./32 type object
 address 0 28.0.122.194 mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#
ip address-set **********./32 type object
 address 0 ********** mask 32
#
ip address-set 192.168.140/141.0 type object
 address 0 ************* mask 24
 address 1 ************* mask 24
#
ip address-set ********/16. type object
 address 0 ******** mask 16
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set *********7. type object
 address 0 *********7 mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ***********_65 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32            
 address 4 *********** mask 32
 address 5 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set **********4. type object
 address 0 **********4 mask 32
#
ip address-set ***********_33 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
#
ip address-set ***********_42 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32            
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set ************./32 type object
 address 0 *********** mask 32
#
ip address-set *************. type object
 address 0 ************* mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set *********7. type object
 address 0 *********7 mask 32
#
ip address-set T4:*********14 type object
 address 0 *********14 mask 32
#
ip address-set USAP:*********** type object
 address 0 *********** mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set **********_32 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#
ip address-set *************/24. type object
 address 0 ************* mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32          
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set **********/12/120 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
 address 2 **********0 mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set ************_56 type object
 address 0 ************ mask 32
 address 1 ************ mask 32
 address 2 ************ mask 32
 address 3 ************ mask 32           
 address 4 ************ mask 32
 address 5 ************ mask 32
#
ip address-set ***********_8 type object
 address 0 *********** mask 32
 address 1 *********** mask 32
 address 2 *********** mask 32
 address 3 *********** mask 32
 address 4 *********** mask 32
 address 5 *********** mask 32
 address 6 *********** mask 32
 address 7 *********** mask 32
#
ip address-set vpn_bangong type object
 address 0 ************* mask 24
 address 1 ************* mask 24
 address 2 ************* mask 24
 address 3 192.168.237.0 mask 24
 address 4 192.168.238.0 mask 24
 address 5 192.168.239.0 mask 24
#
ip address-set 18.6.4.53. type object
 address 0 18.6.4.53 mask 32              
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set 192.168.70.58. type object
 address 0 192.168.70.58 mask 32
#
ip address-set 10.0.0.0/8. type object
 address 0 10.0.0.0 mask 8
#
ip address-set **********/16. type object
 address 0 ********** mask 16
#
ip address-set ***************. type object
 address 0 *************** mask 32
#
ip address-set 18.5.70.0/24. type object
 address 0 18.5.70.0 mask 24
#
ip address-set 18.5.84.0/24. type object  
 address 0 18.5.84.0 mask 24
#
ip address-set ************/24. type object
 address 0 ************ mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set guoce_104/8and10./8 type object
 address 0 ********* mask 8
 address 1 10.0.0.0 mask 8
#
ip address-set guoce_18.6.30and31 type object
 address 0 18.6.30.0 mask 24
 address 1 18.6.31.0 mask 24
#
ip address-set tencent_10.215.0.10 type object
 address 0 10.215.0.10 mask 32
#
ip address-set ********_10 type object
 address 0 range ******** ********0
#
ip address-set RTQDB_*********** type object
 address 0 *********** mask 32
#
ip address-set 104_0_0_0_8 type object
 address 0 ********* mask 8
#
ip address-set *********/8. type object
 address 0 ********* mask 8
#
ip address-set ********/8. type object
 address 0 ******** mask 8
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set USAP_104.22.9.22 type object
 address 0 104.22.9.22 mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set 10.214.0.62./32 type object
 address 0 10.214.0.62 mask 32
#                                         
ip address-set 10.213.0.0/16. type object
 address 0 10.213.0.0 mask 16
#
ip address-set Guo2_Monitor_10.213.8.158 type object
 address 0 10.213.8.158 mask 32
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set 10.219.5.103_105 type object
 address 0 10.219.5.103 mask 32
 address 1 10.219.5.104 mask 32
 address 2 10.219.5.105 mask 32
#
ip address-set CSLC_104.255.255.1 type object
 address 0 104.255.255.1 mask 32
#
ip address-set 赵明薇VDI type object
 address 0 10.219.4.11 mask 32
#                                         
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set 18.2.1.2_4 type object
 address 0 range 18.2.1.2 ********
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set 信息发布中心 type object
 address 0 ********** mask 32
#
ip address-set BT_********1 type object
 address 0 ********1 mask 32
#
ip address-set BT_********15 type object
 address 0 ********15 mask 32
#
ip address-set **********/16. type object
 address 0 ********** mask 16
#
ip address-set **********/32. type object
 address 0 ********** mask 32             
#
ip address-set F5_18.0.11.123 type object
 address 0 18.0.11.123 mask 32
#
ip address-set **********0_103 type object
 address 0 range **********0 **********3
#
ip address-set *********50/32. type object
 address 0 *********50 mask 32
#
ip address-set F5_********50 type object
 address 0 ********50 mask 32
#
ip address-set *********/24. type object
 address 0 ********* mask 24
#
ip address-set 192.168.167.99/32. type object
 address 0 192.168.167.99 mask 32
 address 1 192.168.167.37 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#                                         
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********51_154 type object
 address 0 *********51 mask 32
 address 1 *********52 mask 32
 address 2 *********53 mask 32
 address 3 *********54 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set 18.0.92.10_13/*********0_30 type object
 address 0 range 18.0.92.10 18.0.92.13    
 address 1 range *********0 18.0.92.30
#
ip address-set ********00/32. type object
 address 0 ********00 mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set T2T4T5node type object
 address 0 ********** mask 32
 address 1 48.0.7.100 mask 32
 address 2 ********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set 48.0.7.100/32. type object 
 address 0 48.0.7.100 mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set 4.189.0.1_10 type object
 address 0 range 4.189.0.1 4.189.0.10
#
ip address-set 18.6.23.28_29 type object
 address 0 18.6.23.28 mask 32
 address 1 18.6.23.29 mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set *********13/32. type object
 address 0 *********13 mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set ***************/32. type object
 address 0 *************** mask 32        
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set 18.6.4.160. type object
 address 0 18.6.4.160 mask 32
#
ip address-set 18.6.4.63/32. type object
 address 0 18.6.4.63 mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set *********53/32. type object
 address 0 *********53 mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set 18.0.97.21_28 type object  
 address 0 range 18.0.97.21 18.0.97.28
#
ip address-set **********1/32. type object
 address 0 **********1 mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#                                         
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set ********/24. type object
 address 0 ******** mask 24
#
ip address-set *********_18 type object
 address 0 range ********* *********      
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set **************. type object
 address 0 ************** mask 32
#
ip address-set *********_sjj type object
 address 0 ********* mask 32
#
ip address-set **********/32_coding type object
 address 0 ********** mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set **********_62 type object
 address 0 ********** mask 32
 address 1 ********** mask 32
#                                         
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set ***********/32. type object
 address 0 *********** mask 32
#
ip address-set **********_36 type object
 address 0 range ********** **********
#
ip address-set *********_73 type object
 address 0 range ********* *********
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set **********/24. type object
 address 0 ********** mask 24             
#
ip address-set **********/16. type object
 address 0 ********** mask 16
#
ip address-set **********/24. type object
 address 0 ********** mask 24
#
ip address-set *********/32. type object
 address 0 ********* mask 32
#
ip address-set ***********/24. type object
 address 0 *********** mask 24
#
ip address-set 10_211_4_149 type object
 address 0 ************ mask 32
#
ip address-set 18_0_4_75 type object
 address 0 ********* mask 32
#
ip address-set 18_4_0_75 type object
 address 0 ********* mask 32
#
ip address-set 18_6_36_38 type object     
 address 0 ********** mask 32
#
ip address-set 10_211_6_57 type object
 address 0 *********** mask 32
#
ip address-set 18_6_32_38 type object
 address 0 *********8 mask 32
#
ip address-set 18_0_95_0/24 type object
 address 0 ********* mask 24
#
ip address-set 28_0_1_0/24 type object
 address 0 ******** mask 24
#
ip address-set 18_0_95_20_121 type object
 address 0 range ********** ***********
#
ip address-set 10_216_5_4 type object
 address 0 ********** mask 32
#
ip address-set 28_0_0_0/8 type object
 address 0 ******** mask 8
#                                         
ip address-set 104_22_9_42 type object
 address 0 *********** mask 32
#
ip address-set 38_0_1_0/24 type object
 address 0 ******** mask 24
#
ip address-set 10_218_0_0/16 type object
 address 0 ********** mask 16
#
ip address-set 18_0_1_248 type object
 address 0 ********48 mask 32
#
ip address-set 18_6_32_40/32 type object
 address 0 *********0 mask 32
#
ip address-set 18_6_32_41/32 type object
 address 0 *********1 mask 32
#
ip address-set 18_0_97_11 type object
 address 0 ********** mask 32
#
ip address-set 18_0_117_11/32 type object
 address 0 *********** mask 32            
#
ip address-set 18_6_32_42 type object
 address 0 *********2 mask 32
#
ip address-set 18_6_32_37 type object
 address 0 ********** mask 32
#
ip address-set 18_6_32_0/24 type object
 address 0 ********* mask 24
#
ip address-set **********/32. type object
 address 0 ********** mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set *************/32. type object
 address 0 ************* mask 32
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set F5VIP********** type object
 address 0 ********** mask 32
#
ip address-set ************/32. type object
 address 0 ************ mask 32
#
ip address-set **********_ type object
 address 0 ********** mask 32
#
ip address-set 3.0.0.0_ type object
 address 0 3.0.0.0 mask 8
#                                         
ip address-set *********5_ type object
 address 0 *********5 mask 32
#
ip address-set **********/24_ type object
 address 0 ********** mask 24
#
ip address-set **********_ type object
 address 0 ********** mask 32
#
ip address-set ********/24_ type object
 address 0 ******** mask 24
#
ip address-set ********/8_ type object
 address 0 ******** mask 8
#
ip address-set 28.0.1.201_ type object
 address 0 28.0.1.201 mask 32
#
ip address-set 18.6.203.1_18、44 type object
 address 0 range 18.6.203.1 18.6.203.18
 address 1 18.6.203.44 mask 32
#
ip address-set **********_ type object    
 address 0 ********** mask 32
#
ip address-set ***********_ type object
 address 0 *********** mask 32
#
ip address-set 10.211.13.2_10.211.3.55 type object
 address 0 10.211.13.2 mask 32
 address 1 10.211.3.55 mask 32
#
ip address-set *********_ type object
 address 0 ********* mask 32
#
ip address-set *********6_ type object
 address 0 *********6 mask 32
#
ip address-set *********_ type object
 address 0 ********* mask 32
#
ip address-set 10.211.13.2_10.211.3.5 type object
 address 0 10.211.13.2 mask 32
 address 1 10.211.3.5 mask 32
#
ip address-set 38.0.160.59_ type object   
 address 0 38.0.160.59 mask 32
#
ip address-set 10.213.3.60_ type object
 address 0 10.213.3.60 mask 32
#
ip address-set ***********_ type object
 address 0 *********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set 18.0.140.1218.0.140.39 type object
 address 0 18.0.140.12 mask 32
 address 1 18.0.140.39 mask 32
 address 2 *********7 mask 32
 address 3 *********8 mask 32
#
ip address-set 18.0.160.10_ type object
 address 0 18.0.160.10 mask 32
#                                         
ip address-set waipan type object
 address 0 76.223.0.185 mask 32
 address 1 13.248.128.120 mask 32
 address 2 76.223.84.133 mask 32
 address 3 13.248.208.118 mask 32
#
ip address-set 76.223.81.133_ type object
 address 0 76.223.81.133 mask 32
#
ip address-set 38.0.160.10—— type object
 address 0 38.0.160.1 mask 32
#
ip address-set 104.255.225.45_ type object
 address 0 104.255.225.45 mask 32
#
ip address-set VC18.252.254.202 type object
 address 0 18.252.254.202 mask 32
#
ip address-set ********29_8080 type object
 address 0 ********29 mask 32
#
ip address-set **********_ type object
 address 0 ********** mask 32             
#
ip address-set 10.211.6.45_59 type object
 address 0 10.211.6.45 mask 32
 address 1 10.211.6.59 mask 32
#
ip address-set 18.0.185.12_37 type object
 address 0 18.0.185.12 mask 32
 address 1 18.0.185.37 mask 32
#
ip address-set 18.0.185.39_ type object
 address 0 18.0.185.39 mask 32
#
ip address-set 18.4.170.0_ type object
 address 0 18.4.170.0 mask 24
#
ip address-set 18.4.128.170_ type object
 address 0 18.4.128.170 mask 32
#
ip address-set *********9_ type object
 address 0 *********9 mask 32
#
ip address-set LB_SELF type object
 address 0 10.218.20.233 mask 32          
#
ip address-set *********0_ type object
 address 0 *********0 mask 32
#
ip address-set 10.211.6.0_24 type object
 address 0 10.211.6.0 mask 24
#
ip address-set 104.21.2.64_ type object
 address 0 104.21.2.64 mask 32
#
ip address-set 104.21.2.64_1 type object
 address 0 104.21.2.64 mask 32
#
ip address-set **********_36 type object
 address 0 range ********** **********
#
ip address-set *********1_ type object
 address 0 *********1 mask 32
#
ip address-set 18.0.95.97_ type object
 address 0 18.0.95.97 mask 32
#
ip address-set *********2_性能测试 type object
 address 0 *********2 mask 32
#
ip address-set **********_性能测试 type object
 address 0 ********0 mask 32
#
ip address-set 10.211.3.108_VDI性能测试 type object
 address 0 10.211.3.108 mask 32
#
ip address-set ***********/32_SDN type object
 address 0 *********** mask 32
#
ip address-set **********/24_0518 type object
 address 0 ********** mask 24
#
ip address-set *********3_0518 type object
 address 0 *********3 mask 32
#
ip address-set 172.16.0.1_2_0518 type object
 address 0 range 172.16.0.1 172.16.0.2
#
ip address-set ********73_0518 type object
 address 0 ********73 mask 32
#                                         
ip address-set ************_魏宏安全主机 type object
 address 0 ************ mask 32
#
ip address-set ********/16_0519 type object
 address 0 ******** mask 16
#
ip address-set ***********/32张跃vdi type object
 address 0 *********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ************31付中豪测试机 type object
 address 0 ************31 mask 32
#
ip address-set **************测试 type object
 address 0 ************** mask 32
#
ip address-set 10.211.4.57. type object
 address 0 10.211.4.57 mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32             
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set 阿里云测试********** type object
 address 0 ********** mask 24
#
ip address-set 阿里云测试地址*********** type object
 address 0 *********** mask 20
#
ip address-set *********4/*********** type object
 address 0 *********4 mask 32
#
ip address-set ***********阿里云测试 type object
 address 0 *********** mask 32
#
ip address-set *********/16. type object
 address 0 ********* mask 16
#
ip address-set *********/16. type object
 address 0 ********* mask 16
#
ip address-set **************/32. type object
 address 0 ************** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set *********. type object
 address 0 ********* mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip address-set *********7/32T5 type object
 address 0 *********7 mask 32
#
ip address-set **********/32t5 type object
 address 0 ********** mask 32
#
ip address-set **********T5 type object
 description T5
 address 0 ********** mask 32             
#
ip address-set ************&2 type object
 address 0 range ************ ************
#
ip address-set ***********_30 type object
 address 0 range *********** ***********
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set 王轩测试 type object
 address 0 *********** mask 32
#
ip address-set ***********. type object
 address 0 *********** mask 32
#
ip address-set **********. type object
 address 0 ********** mask 32
#
ip address-set ************. type object
 address 0 ************ mask 32
#
ip service-set rsa_syslog type group      
 service 0 service-set rsa_syslog_tcp
 service 1 service-set rsa_syslog_udp
#
ip service-set udp_50086 type object
 service 0 protocol udp source-port 0 to 65535 destination-port 50086
#
ip service-set rpc+ type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6222
 service 1 protocol udp source-port 0 to 65535 destination-port 6222
#
ip service-set cisco_radius type object
 service 0 protocol udp source-port 0 to 65535 destination-port 1645
 service 1 protocol udp source-port 0 to 65535 destination-port 1646
 service 2 protocol udp source-port 0 to 65535 destination-port 1812
 service 3 protocol udp source-port 0 to 65535 destination-port 1813
#
ip service-set rsa_syslog_tcp type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 445
 service 1 protocol tcp source-port 0 to 65535 destination-port 135
 service 2 protocol tcp source-port 0 to 65535 destination-port 137
 service 3 protocol tcp source-port 0 to 65535 destination-port 138
 service 4 protocol tcp source-port 0 to 65535 destination-port 139
#                                         
ip service-set rsa_syslog_udp type object
 service 0 protocol udp source-port 0 to 65535 destination-port 445
 service 1 protocol udp source-port 0 to 65535 destination-port 135
 service 2 protocol udp source-port 0 to 65535 destination-port 137
 service 3 protocol udp source-port 0 to 65535 destination-port 138
 service 4 protocol udp source-port 0 to 65535 destination-port 139
#
ip service-set tcp_52704 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 52704
#
ip service-set ftp_8020&8021 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8020
 service 1 protocol tcp source-port 0 to 65535 destination-port 8021
#
ip service-set tcp_7777&20001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7777
 service 1 protocol tcp source-port 0 to 65535 destination-port 20001
#
ip service-set tcp_30000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30000
#
ip service-set remote_desktop type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#
ip service-set tcp_20002 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20002
#
ip service-set tcp_5056 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 5056
#
ip service-set tcp_8020&8021 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8020
 service 1 protocol tcp source-port 0 to 65535 destination-port 8021
#
ip service-set ftp_8000_9000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8000 to 9000
#
ip service-set tcp_1_65535 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1 to 65535
#
ip service-set http_8081 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8081
#
ip service-set tcp_1000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1000
#                                         
ip service-set http_8082 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8082
#
ip service-set tcp_8055 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8055
#
ip service-set http_9998 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9998
#
ip service-set tcp_135 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 135
#
ip service-set tcp_1433&1434 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1433 to 1434
#
ip service-set mgmt_nbu type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 13724
 service 1 protocol tcp source-port 0 to 65535 destination-port 13782
 service 2 protocol tcp source-port 0 to 65535 destination-port 1556
#
ip service-set mgmt_patrol type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 2059
 service 1 protocol tcp source-port 0 to 65535 destination-port 3181
#
ip service-set mgmt_control_m type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7005
 service 1 protocol tcp source-port 0 to 65535 destination-port 7006
#
ip service-set http_7779 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7779
#
ip service-set tcp_8008 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8008
#
ip service-set tcp_20083 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20083
#
ip service-set tcp_8066 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8066
#
ip service-set tcp_8090 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8090
#
ip service-set tcp_8080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#                                         
ip service-set tcp_22 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 22
#
ip service-set tcp_20 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20
#
ip service-set tcp_8065 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8065
#
ip service-set tcp_9000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9000
#
ip service-set tcp_52701 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 52701
#
ip service-set tcp_8067 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8067
#
ip service-set tcp_8069 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8069
#
ip service-set ms_sql type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1433
#
ip service-set nbname type object
 service 0 protocol udp source-port 0 to 65535 destination-port 137
#
ip service-set smb type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 139
 service 1 protocol tcp source-port 0 to 65535 destination-port 445
#
ip service-set tcp_7778 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7778
#
ip service-set tcp_31051 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31051
#
ip service-set tcp_31060 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31060
#
ip service-set tcp_31070 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31070
#
ip service-set tcp_41099 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 41099
#                                         
ip service-set tcp_23215_20 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 23215 to 23217
 service 1 protocol tcp source-port 0 to 65535 destination-port 23220
#
ip service-set TCP31050_70 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31050
 service 1 protocol tcp source-port 0 to 65535 destination-port 31060
 service 2 protocol tcp source-port 0 to 65535 destination-port 31070
#
ip service-set TCP31051 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31051
#
ip service-set TCP41099 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 41099
#
ip service-set tcp_7003 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7003
#
ip service-set ospf type object
 service 0 protocol 89
#
ip service-set TCP_6611 type object
#                                         
ip service-set rpc_6611 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6611
 service 1 protocol udp source-port 0 to 65535 destination-port 6611
#
ip service-set rpc_6610_6614 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6610 to 6614
 service 1 protocol udp source-port 0 to 65535 destination-port 6610 to 6614
#
ip service-set TCP_1433 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1433
#
ip service-set tcp_58662 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 58662
#
ip service-set TCP6379 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6379
#
ip service-set tcp_3306 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3306
#
ip service-set tcp_6662 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6662
#                                         
ip service-set TCP_4433 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 4433
#
ip service-set TCP8081 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8081
#
ip service-set "TCP 50443" type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 50443
#
ip service-set "TCP 8080" type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set tcp_50443 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 50443
#
ip service-set tcp8080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set tcp31306 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31306
#
ip service-set tcp_8530_8531 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8530
 service 1 protocol tcp source-port 0 to 65535 destination-port 8531
#
ip service-set tcp_20201 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20201
#
ip service-set tcp_9080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9080
#
ip service-set tcp_3080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3080
#
ip service-set TCP_80_3080_9080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 80
 service 1 protocol tcp source-port 0 to 65535 destination-port 9080
 service 2 protocol tcp source-port 0 to 65535 destination-port 3080
#
ip service-set "TCP 9080" type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9080
#
ip service-set tcp_8101_8103_8104 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8101
 service 1 protocol tcp source-port 0 to 65535 destination-port 8103
 service 2 protocol tcp source-port 0 to 65535 destination-port 8104
#
ip service-set TCP8091 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8091
#
ip service-set tcp_4444 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 4444
#
ip service-set TCP_8008 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8008
#
ip service-set tcp_1521 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1521
#
ip service-set tcp1521 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1521
#
ip service-set TCP_9201 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9201
#
ip service-set TCP_9000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9000
#
ip service-set TCP_23215 type object      
 service 0 protocol tcp source-port 0 to 65535 destination-port 23215
#
ip service-set tcp_23215 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 23215
#
ip service-set TCP_20001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20001
#
ip service-set TCP_9003 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9003
#
ip service-set TCP_21514 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 21514
#
ip service-set Tcp_7001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7001
#
ip service-set TCP_21598 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 21598
#
ip service-set TCP_3389 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
#                                         
ip service-set TCP21405 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 21405
#
ip service-set TCP8443 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8443
#
ip service-set TCP_20406 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20406
#
ip service-set TCP_20407 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20407
#
ip service-set TCP_3191 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3191
#
ip service-set TCP_21910 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 21910
#
ip service-set TCP_7001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7001
#
ip service-set TCP_4000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 4000
#
ip service-set TCP_3000_8686 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3000
 service 1 protocol tcp source-port 0 to 65535 destination-port 8686
#
ip service-set tcp_8088 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8088
#
ip service-set tcp_8000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8000
#
ip service-set TCP_10500 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10500
#
ip service-set TCP_2181 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 2181
#
ip service-set TCP_162 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 162
#
ip service-set tcp_10051 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10051
#                                         
ip service-set TCP_28081 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 28081
#
ip service-set tcp_22601 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 22601
#
ip service-set tcp_21406 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 21406
#
ip service-set TCP_20880 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20880
#
ip service-set TCP_32600 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 32600
#
ip service-set TCP_28070 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 28070
#
ip service-set tcp_29092 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 29092
#
ip service-set TCP30400 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30400
#
ip service-set TCP_9001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9001
#
ip service-set TCP9100 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9100
#
ip service-set TCP_5480 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 5480
#
ip service-set TCP9443 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9443
#
ip service-set TCP_28080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 28080
#
ip service-set TCP_50094 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 50094
#
ip service-set TCP_1099 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1099
#
ip service-set TCP_5000 type object       
 service 0 protocol tcp source-port 0 to 65535 destination-port 5000
#
ip service-set TCP_445 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 445
#
ip service-set TCP10080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10080
#
ip service-set TCP_8000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8000
#
ip service-set TCP_8010 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8010
#
ip service-set TCP_8000_8010 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8000 to 8010
#
ip service-set TCP8011 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8011
#
ip service-set TCP_30600 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30600
#                                         
ip service-set TCP_31306 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31306
#
ip service-set TCP_9090 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9090
#
ip service-set TCP_9091 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9091
#
ip service-set TCP_9093 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9093
#
ip service-set TCP_3000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3000
#
ip service-set TCP_3558 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3558
#
ip service-set TCP_30300 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30300
#
ip service-set TCP_30416 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30416
#
ip service-set TCP_8080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_25 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 25
#
ip service-set TCP_29200 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 29200
#
ip service-set TCP/UDP111 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 111
 service 1 protocol udp source-port 0 to 65535 destination-port 111
#
ip service-set TCP/UDP_2049 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 2049
 service 1 protocol udp source-port 0 to 65535 destination-port 2049
#
ip service-set TCP/UDP_32768_65535 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 32768 to 65535
 service 1 protocol udp source-port 0 to 65535 destination-port 32768 to 65535
#
ip service-set TCP_8089 type object       
 service 0 protocol tcp source-port 0 to 65535 destination-port 8089
#
ip service-set TCP_32306 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 32306
#
ip service-set TCP_8086 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8086
#
ip service-set TCP_8087 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8087
#
ip service-set TCP9080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9080
#
ip service-set TCP_8182 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8182
#
ip service-set TCP8000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8000
#
ip service-set TCP_8013 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8013
#                                         
ip service-set TCP_8018 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8018
#
ip service-set TCP_186 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 186
#
ip service-set TCP_30426 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30426
#
ip service-set TCP8080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8080
#
ip service-set TCP_5672 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 5672
#
ip service-set TCP_8004 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8004
#
ip service-set TCP_389 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 389
#
ip service-set TCP_3555 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3555
#
ip service-set TCP_20050 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20050
#
ip service-set TCP_20617 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20617
#
ip service-set TCP_10050 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10050
#
ip service-set TCP_8090 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8090
#
ip service-set TCP_6008 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6008
#
ip service-set TCP_6006 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6006
#
ip service-set TCP_20150 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20150
#
ip service-set TCP_6370 type object       
 service 0 protocol tcp source-port 0 to 65535 destination-port 6370
#
ip service-set TCP_34443 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 34443
#
ip service-set TCP_50095 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 50095
#
ip service-set TCP_6021 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6021
#
ip service-set TCP_6201 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6201
#
ip service-set TCP_21 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 21
#
ip service-set TCP_3268 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3268
#
ip service-set TCP_8085 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8085
#                                         
ip service-set TCP_8184 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8184
#
ip service-set TCP_8181 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8181
#
ip service-set TCP_3556 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3555
#
ip service-set TCP3556 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3556
#
ip service-set TCP_3557 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3557
#
ip service-set TCP_30001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30001
#
ip service-set TCP_5044 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 5044
#
ip service-set TCP_8001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001
#
ip service-set TCP_24433 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 24433
#
ip service-set TCP_34433 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 34433
#
ip service-set TCP8022 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8022
#
ip service-set TCP_36524 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 36524
#
ip service-set TCP_9092 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9092
#
ip service-set TCP_14433 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 14433
#
ip service-set TCP_8098 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8098
#
ip service-set TCP_4100_4200 type object  
 service 0 protocol tcp source-port 0 to 65535 destination-port 4100 to 4200
#
ip service-set TCP_4422 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 4422
#
ip service-set TCP_62738 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 62738
#
ip service-set TCP_5003 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 5003
#
ip service-set TCP_18080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 18080
#
ip service-set TCP_8989 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8989
#
ip service-set TCP_7004 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7004
#
ip service-set TCP_7005 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7005
#                                         
ip service-set TCP_8428 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8428
#
ip service-set TCP_69 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 69
 service 1 protocol udp source-port 0 to 65535 destination-port 69
#
ip service-set TCP_6000 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6000
#
ip service-set TCP20082 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20082
#
ip service-set TCP28082 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 28082
#
ip service-set TCP_5432 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 5432
#
ip service-set tcp5000_5008 type object
 service 0 protocol tcp source-port 5000 to 5008 destination-port 0 to 65535
#
ip service-set tcp_5000_5008 type object  
 service 0 protocol tcp source-port 0 to 65535 destination-port 5000 to 5008
#
ip service-set tcp6370 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 6370
#
ip service-set tcp28090 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 28090
#
ip service-set tcp44433 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 44433
#
ip service-set tcp_4191 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 4191
#
ip service-set TCP_31050_31051_31060 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31050
 service 1 protocol tcp source-port 0 to 65535 destination-port 31051
 service 2 protocol tcp source-port 0 to 65535 destination-port 31060
#
ip service-set TCP_10248 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10248
#
ip service-set TCP_27003 type object      
 service 0 protocol tcp source-port 0 to 65535 destination-port 27003
#
ip service-set TCP8011_8020 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8011 to 8020
#
ip service-set TCP_8083 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8083
#
ip service-set UDP_123 type object
 service 0 protocol udp source-port 0 to 65535 destination-port 123
#
ip service-set TCP_50 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 50
#
ip service-set TCP_449 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 449
#
ip service-set TCP_10 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10
#
ip service-set TCP_3559 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3559
#                                         
ip service-set tcp2881totcp2888 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 2881 to 2888
#
ip service-set tcp135totcp139 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 135 to 139
#
ip service-set udp135toudp139 type object
 service 0 protocol udp source-port 0 to 65535 destination-port 135 to 139
#
ip service-set tcp80 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 80
#
ip service-set TCP31050_31051 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31050 to 31051
#
ip service-set TCP/UDP22 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 22
 service 1 protocol udp source-port 0 to 65535 destination-port 22
#
ip service-set TCP8001_8020 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8001 to 8020
#
ip service-set TCP_4555 type object       
 service 0 protocol tcp source-port 0 to 65535 destination-port 4555
#
ip service-set TCP_2883 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 2883
#
ip service-set TCP_31582 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31582
#
ip service-set TCP_3001 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3001
#
ip service-set TCP_34431_34435 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 34431 to 34435
#
ip service-set TCP30900 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30900
#
ip service-set TCP_10280 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10280
#
ip service-set TCP_30011_30012 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30011 to 30012
#                                         
ip service-set TCP_30010 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30010
#
ip service-set TCP_30200 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30200
#
ip service-set TCP_19080 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 19080
#
ip service-set TCP_8092 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8092
#
ip service-set TCP_1443 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 1443
#
ip service-set TCP_8866 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8866
#
ip service-set TCP_161 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 161
#
ip service-set TCP_9999 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 9999
#
ip service-set TCP_7569 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 7569
#
ip service-set TCP_10014 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 10014
#
ip service-set TCP_8899 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8899
#
ip service-set TCP_30002 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30002
#
ip service-set TCP8083&8084 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 8083 to 8084
#
ip service-set tcp100 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 100
#
ip service-set 3389 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 3389
 service 1 protocol tcp source-port 0 to 65535 destination-port 22
 service 2 protocol tcp source-port 0 to 65535 destination-port 23
 service 3 protocol tcp source-port 0 to 65535 destination-port 24
 service 4 protocol tcp source-port 0 to 65535 destination-port 25
#
ip service-set 514 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 514
#
ip service-set TCP30021 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30021
#
ip service-set TCP_10035 type object
 service 0 protocol tcp destination-port 10035
#
ip service-set TCP_30108 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 30108
#
ip service-set TCP_11668 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 11668
#
ip service-set TCP_31003 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 31003
#
ip service-set tcp:20443 type object
 service 0 protocol tcp source-port 0 to 65535 destination-port 20443
#
 slb
#
right-manager server-group
#
sa
#
 nat address-group SANT_*********
 section 0 ********* **********
 nat address-group **********20
 section 0 ********** **********
 nat address-group *********
 section 0 ********* *********
 nat address-group SNAT*********
 section 0 ********* *********
 nat address-group SNAT_*********
 section 0 ********* *********
 nat address-group **************
 section 0 ************** **************
 nat address-group NAT_*********
 section 0 ********* *********
 nat address-group *********
 section 0 ********* *********            
 nat address-group T2_NAT_*********
 section 0 ********* *********
 nat address-group T5_NAT_*********
 section 0 ********* *********
 nat address-group T4_*********
 section 0 ********* *********
 nat address-group NAT_*********
 section 0 ********* *********
 nat address-group T1_NAT_*********
 section 0 ********* *********
 nat address-group NAT_*********
 section 0 ********* *********
 nat address-group NAT_*********
 section 0 ********* *********
 nat address-group NAT_**********
 section 0 ********** **********
 nat address-group NAT_*********1
 section 0 *********1 *********1
 nat address-group NAT_*********2
 section 0 *********2 *********2
 nat address-group NAT*********3
 section 0 *********3 *********3
 nat address-group NAT_*********4         
 section 0 *********4 *********4
 nat address-group NAT_*********5
 section 0 *********5 *********5
#
location
#
 domain-set name URL
  add domain inforequest.sporttery.cn
#
agile-network
#
api
#
device-classification
 device-group pc
 device-group mobile-terminal
 device-group undefined-group
#
security-policy
 rule name AMS下线版本网络开通
  source-zone egress
  destination-zone core
  source-address address-set *************/32.
  source-address address-set ***********/32张跃vdi
  source-address address-set kaifaceshitools_********0
  destination-address address-set **********/32t5
  destination-address address-set **********T5
  destination-address address-set ********/8.
  action permit
 rule name test李阳
  source-zone core
  destination-zone egress
  source-address address-set ********/8.
  destination-address address-set **********/16.
  action permit
 rule name 第三代竞猜型游戏交易系统
  source-zone core
  destination-zone egress
  source-address address-set ********/8.
  destination-address address-set ***********/24.
  destination-address address-set **********/24.
  destination-address address-set ***********./24
  service TCP_6370
  service TCP30021
  service TCP_8083
  action permit                           
 rule name 出访问
  source-zone core
  destination-zone egress
  action permit
 rule name 魏宏安全扫描***********
  source-zone egress
  destination-zone core
  source-address address-set ***********/32张跃vdi
  source-address address-set 10.211.6.0_24
  destination-address address-set *********4/***********
  destination-address address-set ***********阿里云测试
  action permit
 rule name 姜大伟数据采集机
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set ************~160_数据采集机
  destination-address address-set **********/24_0518
  destination-address address-set *********3_0518
  action permit
 rule name 性能测试
  disable
  source-zone egress                      
  destination-zone core
  destination-address address-set *********2_性能测试
  destination-address address-set **********_性能测试
  action permit
 rule name agent_TO_T1_景一0629
  source-zone egress
  destination-zone core
  source-address address-set **********/32.
  source-address address-set **********.
  source-address address-set *************/32.
  source-address address-set ***********/32张跃vdi
  destination-address address-set ********/24.
  destination-address address-set **************/32.
  action permit
 rule name 20230523李景一
  source-zone egress
  destination-zone core
  source-address address-set **********.
  destination-address address-set *********.
  destination-address address-set *********9_
  action permit
 rule name 离朱
  source-zone egress                      
  destination-zone core
  destination-address address-set *********1_
  destination-address address-set 18.0.95.97_
  destination-address address-set **********_36_0516
  destination-address address-set *********_73_0516
  action permit
 rule name 10.211.6.59TO18.0.95.94
  disable
  source-zone egress
  destination-zone core
  source-address address-set 10.211.6.0_24
  source-address address-set **********_36
  destination-address address-set *********0_
  service tcp100
  service icmp
  service 3389
  service 514
  action permit
 rule name T1_**********
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set **********.
  service TCP8443
  action permit
 rule name T5_18.0.160.10
  description 访问外盘
  source-zone core
  destination-zone egress
  destination-address address-set waipan
  destination-address address-set 76.223.81.133_
  service icmp
  service https
  action permit
 rule name 魏宏安全扫描
  description **********-223nat*********1
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set **********.
  source-address address-set *********_
  action permit
 rule name 魏宏安全监控
  source-zone egress
  destination-zone core
  source-address address-set ************_魏宏安全主机
  source-address address-set ***********/32张跃vdi
  destination-address address-set ********/16.
  destination-address address-set ********/16_0519
  action permit
 rule name internetservicelinshitest
  source-zone egress
  destination-zone core
  source-address address-set LBSNAT
  source-address address-set LB_SELF
  destination-address address-set **********.
  destination-address address-set **********./32
  destination-address address-set ***********.
  action permit
 rule name 10.21118.0.140
  source-zone egress
  destination-zone core
  source-address address-set 10.211.6.45_59
  destination-address address-set 18.0.140.1218.0.140.39
  destination-address address-set ********29_8080
  destination-address address-set **********_
  destination-address address-set 18.0.185.12_37
  destination-address address-set 18.0.185.39_
  destination-address address-set 18.4.128.170_
  destination-address address-set *********9_
  action permit
 rule name *********6
  source-zone egress
  destination-zone core
  destination-address address-set *********6_
  destination-address address-set 38.0.160.59_
  destination-address address-set ***********_
  action permit
 rule name T5test
  source-zone core
  destination-zone egress
  destination-address address-set 10.213.3.60_
  service TCP_34443
  service icmp
  action permit
 rule name "**********to3.0 linshi"
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set **********_
  destination-address address-set 3.0.0.0_
  action permit
 rule name 信息发布中心V22.11.01
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set *********5_
  action permit
 rule name monitest_yuanxiaojie
  source-zone egress
  destination-zone core
  destination-address address-set F5VIP**********
  service TCP8083&8084
  action permit
 rule name 模拟运营环境交易路由
  source-zone egress
  destination-zone core
  destination-address address-set 18_0_97_11
  destination-address address-set 18_0_117_11/32
  action permit
 rule name 安全PKI测试系统
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set **************/32.
  action permit
 rule name ************
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set ************/32.
  source-address address-set **********/24.
  destination-address address-set 18_6_32_42
  action permit
 rule name WAF_tianyan
  source-zone egress
  destination-zone core
  source-address address-set ************/32.
  source-address address-set ************/32.
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set *********_73
  service https
  action permit
 rule name mimajishu
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set *********/8.
  source-address address-set 10.0.0.0/8.
  destination-address address-set **********/32.
  destination-address address-set ********/24.
  action permit
 rule name sjj2
  source-zone egress
  destination-zone core
  source-address address-set *************/32.
  source-address address-set *************/32.
  source-address address-set *************/32.
  source-address address-set *************/32.
  source-address address-set *************/32.
  source-address address-set ***********/32.
  destination-address address-set *********_sjj
  service TCP8000
  service TCP8022
  service tcp_8088
  service TCP_9999
  service TCP_161
  service TCP_7569
  service TCP_8080
  action permit
 rule name xuan
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set **********.
  source-address address-set ********/16.
  action permit
 rule name to_ng_1443
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********_10
  source-address address-set **********/32.
  source-address address-set **************.
  source-address address-set **************.
  source-address address-set **************.
  source-address address-set **************.
  destination-address address-set **********.
  service TCP_1443
  action permit
 rule name deny_ng_8443_1443
  source-zone core
  destination-zone egress
  destination-address address-set **********.
  service TCP8443
  service TCP_1443
  action deny
 rule name jujiabangong1
  source-zone egress
  destination-zone core
  destination-address address-set **********./32
  service TCP_3389
  action permit
 rule name AGENT_to_xiaofu
  source-zone core
  destination-zone egress                 
  source-address address-set *********13/32.
  source-address address-set *********14.
  destination-address address-set **********/24.
  service https
  service icmp
  action permit
 rule name ping
  service icmp
  action permit
 rule name T1_waipan
  source-zone core
  destination-zone egress
  source-address address-set *********53/32.
  destination-address address-set **********/32.
  service https
  service icmp
  action permit
 rule name *************
  source-zone egress
  destination-zone core
  source-address address-set *************/32.
  destination-address address-set ********/8_
  action permit                           
 rule name guoce_any
  source-zone egress
  destination-zone core
  source-address address-set guoce_18.6.30and31
  action permit
 rule name kejibangongto_T5
  source-zone egress
  destination-zone core
  source-address address-set ************.
  source-address address-set **********/24.
  destination-address address-set ***********/32.
  destination-address address-set 18_6_32_37
  service TCP_8092
  service icmp
  action permit
 rule name ********_to_***********
  source-zone core
  destination-zone egress
  source-address address-set ********/8.
  source-address address-set ********/16.
  destination-address address-set ***********./24
  action permit
 rule name 10.216._to_T518.0.160          
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  source-address address-set **********/24.
  source-address address-set **********/24.
  source-address address-set **********/24.
  destination-address address-set T2_T3_**********
  action permit
 rule name 18.6.3_to_200.3
  source-zone egress
  destination-zone core
  source-address address-set *********7.
  destination-address address-set *************.
  service https
  service http
  action permit
 rule name 104.23.13_to_jincai
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  source-address address-set **********/24_
  source-address address-set 10.0.0.0/8.
  destination-address address-set **********.
  destination-address address-set ***********/32.
  destination-address address-set ***********./32
  destination-address address-set T5_************
  destination-address address-set **********/12/120
  destination-address address-set **********_
  service http
  service tcp_52701
  service tcp_7003
  service http_8082
  action permit
 rule name 10.213_to_T1_core
  source-zone egress
  destination-zone core
  source-address address-set ***********_65
  source-address address-set **********.
  destination-address address-set ********_8
  destination-address address-set ********/32.
  destination-address address-set ********0/32.
  destination-address address-set ***********./32
  destination-address address-set ***********.
  destination-address address-set ***********.
  destination-address address-set *************.
  destination-address address-set *********12_214
  service ssh
  service TCP_8000_8010
  service TCP_10050
  service TCP_9091
  service http
  service icmp
  action permit
 rule name vpn_to_xietong
  source-zone core
  destination-zone egress
  source-address address-set vpn_bangong
  destination-address address-set **********/24.
  action permit
 rule name bosrouter
  source-zone core
  destination-zone egress
  source-address address-set **********/24.
  source-address address-set T2_T3_**********
  source-address address-set **********.
  source-address address-set *********/32.
  source-address address-set *********/32.
  source-address address-set *********/32.
  destination-address address-set ***********/32.
  action permit
 rule name RMX150
  source-zone core
  destination-zone egress
  source-address address-set *********/32.
  source-address address-set *********51_154
  destination-address address-set ***********/32.
  service https
  action permit
 rule name T1_BT_to104
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set **********/32.
  service TCP_3389
  action permit
 rule name 18.0.7._to_104.23.
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set **********.
  source-address address-set **********.
  destination-address address-set CSLC_**********/24
  action permit
 rule name T1_to_104.200.100
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set *************/24.
  service TCP_34433
  action permit
 rule name T5_to_10.212
  source-zone core
  destination-zone egress
  source-address address-set T2_T3_**********
  destination-address address-set **********/24.
  destination-address address-set ***********/24.
  destination-address address-set 18.6.23.28_29
  service TCP_9092
  service TCP_34433
  service TCP_7001
  action permit
 rule name T1_to_**********_32
  source-zone core
  destination-zone egress
  source-address address-set ********/24. 
  destination-address address-set **********_32
  service TCP_28080
  action permit
 rule name T4_to_USAP
  source-zone core
  destination-zone egress
  source-address address-set T4:*********14
  source-address address-set ***********.
  source-address address-set *************/24.
  source-address address-set **********/32.
  source-address address-set ********/8.
  source-address address-set 28_0_0_0/8
  source-address address-set ********/8.
  source-address address-set ********/8_
  destination-address address-set USAP:***********
  destination-address address-set **********/24.
  destination-address address-set ************/32.
  destination-address address-set *************/24.
  destination-address address-set ************.
  destination-address address-set **********/24.
  destination-address address-set F5_********50
  action permit
 rule name T1core_to_10.213               
  source-zone core
  destination-zone egress
  source-address address-set ********_8
  source-address address-set ********/32.
  source-address address-set ********0/32.
  source-address address-set *********12_214
  source-address address-set ***********./32
  source-address address-set ***********.
  source-address address-set ***********.
  destination-address address-set ***********.
  destination-address address-set **********4.
  destination-address address-set ***********_33
  destination-address address-set ***********_42
  destination-address address-set ***********.
  service TCP_8000_8010
  service TCP_7001
  service TCP_5672
  service TCP_5044
  service TCP_31306
  service ssh
  action permit
 rule name 167_to*********7
  source-zone egress                      
  destination-zone core
  source-address address-set **************.
  source-address address-set **************.
  destination-address address-set *********7.
  destination-address address-set ********/24.
  service TCP_3389
  service TCP_445
  action permit
 rule name T2ZiDongCaoPan
  source-zone core
  destination-zone egress
  source-address address-set 28.0.122.194./32
  source-address address-set **********/32.
  action permit
 rule name G3SHITI_TO_t1
  source-zone egress
  destination-zone core
  source-address address-set **********/32.
  destination-address address-set .**********/24
  destination-address address-set T1_***********
  destination-address address-set NTP_**********
  destination-address address-set *********/24.
  action permit                           
 rule name 104.23._T1
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  source-address address-set ***********/24.
  source-address address-set ***********/24.
  destination-address address-set ********_8
  destination-address address-set ********/32.
  destination-address address-set ********0/32.
  action permit
 rule name SNMP_T2
  source-zone egress
  destination-zone core
  source-address address-set zabbix_*********01
  source-address address-set solarwinds_*********00
  service snmp
  service snmptrap
  service ssh
  service TCP_10050
  action permit
 rule name DMZ_to_oldT1
  source-zone egress
  destination-zone core                   
  source-address address-set **********.
  destination-address address-set **********/32.
  action permit
 rule name 104.19_to_T2T5
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  destination-address address-set ***********./32
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  destination-address address-set ********/24.
  destination-address address-set ********/24.
  destination-address address-set T3_********/24
  service tcp_7003
  service http_8082
  service TCP_8086
  service tcp_22
  action permit
 rule name T1_to_Ocp和腾讯云
  source-zone core
  destination-zone egress
  source-address address-set ********/24. 
  source-address address-set NTP_**********
  destination-address address-set **********/24.
  destination-address address-set ***********/24.
  service http
  service ssh
  service TCP_8080
  action permit
 rule name T1_104.23.
  source-zone core
  destination-zone egress
  source-address address-set ********_8
  source-address address-set ********/32.
  source-address address-set ********0/32.
  destination-address address-set ***********/24.
  destination-address address-set ***********/24.
  destination-address address-set ***********/24.
  action permit
 rule name XWHT4_to118
  source-zone egress
  destination-zone core
  source-address address-set XWH_T4_NAT_**********
  destination-address address-set **************/32.
  service tcp_22                          
  service TCP_8000_8010
  service TCP_10050
  service TCP_9091
  action permit
 rule name G3T1_TO_T1
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set ************/32.
  service TCP31050_31051
  action permit
 rule name 10.214_to_T1
  source-zone egress
  destination-zone core
  source-address address-set **********/16.
  destination-address address-set ********20/32.
  service https
  service http
  action permit
 rule name OPC_to_ITO
  source-zone core
  destination-zone egress
  source-address address-set T1_18.2.1.9  
  source-address address-set T1_18.2.1.224
  source-address address-set T1_18.2.1.249
  destination-address address-set *********/32.
  destination-address address-set T4_18.6.4.51
  service TCP30900
  action permit
 rule name **********_net
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  source-address address-set **********.
  action permit
 rule name T4_to_104.21.20
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set **********.
  destination-address address-set ***********/24.
  destination-address address-set ***********./24
  destination-address address-set ***********/32.
  destination-address address-set ***********/24.
  action permit                           
 rule name T1_to104_10
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/16.
  destination-address address-set ************.
  destination-address address-set ************/32.
  destination-address address-set **********.
  destination-address address-set **********0.
  destination-address address-set ************.
  destination-address address-set ***************.
  destination-address address-set **********/24.
  destination-address address-set ***********/24.
  action permit
 rule name 118_to_T4
  source-zone core
  destination-zone egress
  source-address address-set **************/32.
  destination-address address-set XWH_T4_NAT_*********
  destination-address address-set *********/32.
  destination-address address-set *********/32.
  destination-address address-set *********/32.
  service TCP/UDP111                      
  service TCP/UDP_2049
  service TCP/UDP_32768_65535
  service TCP_7001
  service TCP/UDP22
  service TCP_5044
  service TCP_9090
  service TCP_5672
  service TCP8001_8020
  service TCP_31306
  action permit
 rule name T2T4T5to_104.
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set T3_38.2.1.0/24
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set T3_********/24
  source-address address-set ********/24.
  source-address address-set ***********/32.
  source-address address-set T2_T3_**********
  destination-address address-set ***********/24.
  destination-address address-set **********/24.
  action permit
 rule name TO_T4XWH_NFS
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set ********/24.
  destination-address address-set NFS_18.6.4.48/32
  service TCP/UDP111
  service TCP/UDP_2049
  service TCP/UDP_32768_65535
  service icmp
  action permit
 rule name t1_g3t1
  source-zone core
  destination-zone egress
  source-address address-set ************/32.
  destination-address address-set ***********/32.
  service TCP31050_31051
  action permit
 rule name wangzhuoshi_ceshi              
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set *********/32.
  service TCP_3559
  action permit
 rule name 10.213_to_T1
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  source-address address-set ***********/24.
  source-address address-set ***********/24.
  source-address address-set ***********/24.
  source-address address-set ************/24.
  source-address address-set **********/24.
  source-address address-set **********/24.
  source-address address-set ***********/24.
  source-address address-set **********./24
  destination-address address-set ********/24.
  destination-address address-set ********/24.
  destination-address address-set **********.
  destination-address address-set .**********/24
  destination-address address-set **********/24.
  service tcp_22
  service TCP_8086
  service http_8082
  service tcp_52701
  service tcp_7003
  service http
  action permit
 rule name TI_to_svn
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/24.
  destination-address address-set **************.
  destination-address address-set ************.
  destination-address address-set ************.
  action permit
 rule name ********_to_guojiashiyanshi
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/16.
  source-address address-set ********/24.
  destination-address address-set *************/24.
  destination-address address-set **********/24.
  destination-address address-set ***************.
  destination-address address-set ***********.
  destination-address address-set ************.
  service tcp_30000
  service TCP_20050
  service tcp_22
  service tcp1521
  action permit
 rule name T4_104.21.20
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set ********/32.
  source-address address-set ********/24.
  source-address address-set T3_38.2.1.0/24
  source-address address-set T3_********/24
  source-address address-set ********/24.
  destination-address address-set ***********/24.
  destination-address address-set *************/24.
  destination-address address-set **********/24.
  service TCP_20050                       
  service tcp_30000
  service TCP_8080
  service TCP8443
  service TCP_34431_34435
  action permit
 rule name T1_TO_XWHT418.6.4
  source-zone core
  destination-zone egress
  source-address address-set ********/32.
  source-address address-set ********0/32.
  destination-address address-set XWH_T4_NAT_*********_31
  service TCP_5000
  service TCP_7001
  service TCP_5672
  service TCP_8004
  service TCP/UDP111
  service TCP/UDP_2049
  service TCP/UDP_32768_65535
  service TCP8011
  service UDP_123
  service TCP_50
  service TCP_449
  service TCP_10                          
  service TCP_8001
  service TCP_9090
  action permit
 rule name T1_to_104.200/10.213
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********_8
  source-address address-set ********/32.
  destination-address address-set *************/24.
  destination-address address-set **********/24.
  service tcp_30000
  service TCP8443
  service TCP_20050
  service TCP_8080
  action permit
 rule name SCAN
  source-zone core
  destination-zone egress
  source-address address-set *********./32
  action permit
 rule name to_cslc_tengxunyun
  source-zone core                        
  destination-zone egress
  source-address address-set **********.
  source-address address-set 18.0.99.99_100
  source-address address-set *********/24.
  destination-address address-set ************/32.
  destination-address address-set 10.0.0.0/8.
  destination-address address-set *********/8.
  action permit
 rule name To_CSLC_104.22
  source-zone core
  destination-zone egress
  source-address address-set *************/24.
  destination-address address-set **********/24.
  service tcp_22
  action permit
 rule name 32.109_to_T4
  source-zone core
  destination-zone egress
  source-address address-set BG_**************
  destination-address address-set *********.
  destination-address address-set *********.
  service ssh
  action permit                           
 rule name BGToXWH
  source-zone core
  destination-zone egress
  source-address address-set **************/32.
  source-address address-set **************_236
  destination-address address-set XWHG3_*********/24
  destination-address address-set ********.
  service TCP8011_8020
  action permit
 rule name CSLC_TO_18.5.48.51
  source-zone core
  destination-zone egress
  source-address address-set CSLC**************
  destination-address address-set XWH_********28/32
  service tcp_29092
  action permit
 rule name 104.23_to_T5
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  destination-address address-set T2_T3_**********
  destination-address address-set T3_********/24
  service TCP_8086                        
  service TCP_28080
  action permit
 rule name CSLC_To_Bangong_MailServer
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set *************/32.
  service TCP_25
  action permit
 rule name T1_To_XWH_*********01
  source-zone core
  destination-zone egress
  source-address address-set ***********/32.
  destination-address address-set *********01/32.
  service TCP_27003
  action permit
 rule name XWHT1_TO_T1
  source-zone egress
  destination-zone core
  source-address address-set *********/32.
  destination-address address-set **********/32.
  destination-address address-set ***********/32.
  service http_8082                       
  service TCP_8092
  action permit
 rule name XWH_TO_T1...
  source-zone egress
  destination-zone core
  source-address address-set *********./32
  destination-address address-set **********/32.
  service http_8082
  service TCP_8085
  service TCP_8087
  action permit
 rule name CSLC_XWH_To_T4
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set ********/24.
  destination-address address-set **********.
  service icmp
  service TCP_21
  service tcp_22
  service http_8082
  action permit                           
 rule name T4_To_CSLC_XWH
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set ********/24.
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  service icmp
  service TCP_9092
  action permit
 rule name T1_To_XWH_T4_MS
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  destination-address address-set *********.
  service TCP_29200
  action permit
 rule name T1_To_XWH************
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set ************/32.
  destination-address address-set T3_NAT_********
  action permit
 rule name T1_to_CSLC_3.90
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set **********/24.
  service icmp
  service TCP_10248
  action permit
 rule name XWHT3_18.5.85.1_To_T1
  source-zone egress
  destination-zone core
  source-address address-set XWHT3_18.5.81.5/32
  destination-address address-set ***********.
  destination-address address-set ***********_228
  service tcp_22
  service TCP_31050_31051_31060
  service tcp_29092
  action permit
 rule name T1_To_XWH_18.5.81.5
  source-zone core
  destination-zone egress                 
  source-address address-set ***********_228
  source-address address-set ***********.
  source-address address-set T1_*********/24
  destination-address address-set XWHT3_18.5.81.5/32
  service tcp_22
  service TCP_31050_31051_31060
  service tcp_29092
  service TCP_4555
  action permit
 rule name CSLC_To_**********
  source-zone core
  destination-zone egress
  source-address address-set ************.
  destination-address address-set **********.
  destination-address address-set *********.
  service ssh
  action permit
 rule name T4_to_***********
  source-zone core
  destination-zone egress
  source-address address-set T4_68.0.0.0/8
  source-address address-set T4_48.1.14.0
  source-address address-set ********/24. 
  source-address address-set **********.
  destination-address address-set ***********/32.
  destination-address address-set ***********/24.
  service TCP8080
  service TCP_9092
  action permit
 rule name SNMP
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set *********.
  source-address address-set zabbix_*********01
  source-address address-set Solarwinds*********00
  destination-address address-set MGMT_18.4.0.0/24
  destination-address address-set ************.
  destination-address address-set *************./32
  service snmp
  service snmptrap
  service ssh
  service telnet
  action permit
 rule name XWH_To_T1_*********7_239
  source-zone egress                      
  destination-zone core
  source-address address-set *********.
  source-address address-set *********/24.
  source-address address-set XWHG3_*********/24
  destination-address address-set *********6_239
  service TCP_9092
  action permit
 rule name R141_TO_mail
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set *************/32.
  service TCP_25
  action permit
 rule name NTP_18.0.216.78
  source-zone egress
  destination-zone core
  source-address address-set RTQ_18.5.84.81_82
  destination-address address-set NTP_18.0.216.78
  service ntp
  action permit
 rule name TO_NTP_********83              
  source-zone egress
  destination-zone core
  destination-address address-set NTP_********83
  service ntp
  service ssh
  action permit
 rule name lijingyi2
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set T1_18.2.1.34/32
  service TCP_20050
  service tcp_22
  service TCP_34433
  action permit
 rule name lijingyi1
  source-zone core
  destination-zone egress
  source-address address-set T1_18.2.1.34/32
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  service TCP_20050                       
  service TCP_34433
  action permit
 rule name CSLC_To_T1_*********
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set *********.
  service tcp_7003
  action permit
 rule name T1_To_CSLC_***********
  source-zone core
  destination-zone egress
  source-address address-set *********.
  destination-address address-set ***********/32.
  service tcp_7003
  action permit
 rule name lilibinTEST
  source-zone egress
  destination-zone core
  source-address address-set *********.
  source-address address-set G3AMS_18.5.84.0./24
  destination-address address-set ************./32
  destination-address address-set **********.
  service TCP_6000
  service TCP_4555
  action permit
 rule name TIAOBANJI
  source-zone core
  destination-zone egress
  source-address ************ mask ***************
  source-address address-set **********.
  source-address address-set ***********/32.
  action permit
 rule name 204.1_To_XWH
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set SDN_172.16.21.1/32
  action permit
 rule name XWH_T5
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set ********/8.
  destination-address address-set T3_38.2.1.0/24
  destination-address address-set ********/24.
  destination-address address-set *********./32
  service TCP_20050
  service TCP8080
  service TCP_8080
  service tcp_7003
  service icmp
  service telnet
  service tcp_22
  action permit
 rule name T5_XWH
  source-zone core
  destination-zone egress
  source-address address-set ********/8.
  source-address address-set T3_38.2.1.0/24
  source-address address-set ********/24.
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  service TCP_20050
  service TCP8080
  service icmp
  service telnet
  action permit                           
 rule name To_18.1.3.0
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  source-address address-set ***********.
  source-address address-set ***********/32.
  destination-address address-set *********.
  destination-address address-set *********./24
  service http
  service http_8082
  action permit
 rule name To_XWH_T19
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set **********/24_
  service ssh
  service http
  service https
  action permit
 rule name lousao_to_T1
  source-zone egress
  destination-zone core                   
  source-address address-set ***************/32.
  source-address address-set ***************/32.
  action permit
 rule name to_keji_block2
  disable
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set ***********/32.
  destination-address address-set *************/24.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  destination-address address-set *************/24.
  destination-address address-set ***********/24.
  destination-address address-set **********/16.
  action permit
 rule name DENY
  source-zone core
  destination-zone egress
  source-address address-set ********/24. 
  destination-address address-set **********./32
  destination-address address-set **********./32
  destination-address address-set ********27./32
  service http
  service tcp_9080
  action permit
 rule name **********_To_XWH_********
  disable
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set ********/16.
  action permit
 rule name T4_To_T1_**********_213
  source-zone egress
  destination-zone core
  source-address address-set T4_*********/24
  destination-address address-set T1_**********_213
  service TCP_62738
  action permit
 rule name T4_18.5.82.1_20_To_T1
  source-zone egress
  destination-zone core                   
  source-address address-set T4_*********/24
  source-address address-set SDAS_18.5.80.161_162
  destination-address address-set T1_**********/32
  destination-address address-set T4_**********/32
  service TCP_31306
  action permit
 rule name T4_***********_To_T1
  source-zone egress
  destination-zone core
  source-address address-set XWH_T4_***********/32
  destination-address address-set ********/16.
  service TCP_8080
  action permit
 rule name T1_To_8.5.88.161:80
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set XWH_T4_***********/32
  destination-address address-set XWH_T4_***********/32
  service http
  action permit
 rule name T1_To_***********
  source-zone core                        
  destination-zone egress
  source-address address-set ********7_18
  source-address address-set **********.
  source-address address-set ********.
  source-address address-set ********.
  source-address address-set **********.
  destination-address address-set XWH_T4_***********/32
  service TCP_32600
  action permit
 rule name TO_*************
  source-zone egress
  destination-zone core
  source-address address-set ********49/32.
  destination-address address-set *************/32.
  service ssh
  service icmp
  service TCP_4422
  action permit
 rule name XWHT3_to_cslc
  source-zone egress
  destination-zone core
  source-address address-set *********4/32.
  destination-address address-set CSLC_***********/24
  destination-address address-set ***********/32.
  service TCP_8098
  service icmp
  service http
  action permit
 rule name cslc_to_XWH_T3_
  source-zone core
  destination-zone egress
  source-address address-set ***********/24.
  destination-address address-set *********/32.
  service TCP_7001
  service TCP_8001
  action permit
 rule name T4_RMXDB_T1_RMX
  source-zone egress
  destination-zone core
  source-address address-set ********.
  source-address address-set V3_CORE_4.190.80.0/21
  destination-address address-set **********.
  service TCP_3555
  action permit
 rule name YYXNZM_18.2.1.120_123
  source-zone egress                      
  destination-zone core
  source-address address-set YunYing_192.168.215.50
  source-address address-set YunYing_192.168.215.51
  destination-address address-set 4F_18.2.1.120_123
  service TCP_3389
  service icmp
  action permit
 rule name YunYing_192.168.215.50_51_Deny
  source-zone egress
  source-address address-set YunYing_192.168.215.50
  source-address address-set YunYing_192.168.215.51
  action deny
 rule name Sunkai_Ceshi
  source-zone core
  destination-zone egress
  source-address address-set ************./32
  destination-address address-set ***********./32
  service TCP30400
  action permit
 rule name G2_TRANSROUTE_V3_30400
  policy logging
  source-zone core
  destination-zone egress                 
  source-address address-set **********_84
  destination-address address-set V3_GW_4.190.40.0/21
  destination-address address-set ***********./32
  service TCP30400
  service ssh
  action permit
 rule name lishuaiqi_test
  source-zone core
  destination-zone egress
  source-address address-set T1_18.0.2.133/32
  destination-address address-set V3_4.190.80.73/32
  service TCP_3558
  service ssh
  action permit
 rule name 18.5.95.5_to_*********
  source-zone egress
  destination-zone core
  source-address address-set **********.
  destination-address address-set *********/24.
  service TCP_10050
  service TCP_9091
  service ssh
  action permit                           
 rule name 第三方测试到国家实验室
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set **********.
  source-address address-set ********/16.
  destination-address address-set CSLC_104.23.0.10
  destination-address address-set CSLC_104.23.0.11
  destination-address address-set CSLC_104.23.0.12_14
  destination-address address-set **********8_99
  destination-address address-set **********.
  destination-address address-set **********0.
  action permit
 rule name ********_to_T4
  source-zone core
  destination-zone egress
  source-address address-set T1_********/32
  source-address address-set T1_********
  source-address address-set *********12_214
  source-address address-set ********/16.
  destination-address address-set ********/16.
  service ssh
  service TCP_30001                       
  service TCP_8086
  service TCP_7001
  service TCP_31306
  service TCP_3555
  service TCP_3558
  service TCP_28080
  service TCP_28081
  service TCP_29200
  service TCP_5000
  service TCP_8004
  service TCP_5672
  service ntp
  service TCP_5044
  service TCP_8001
  service TCP_9090
  service TCP8011
  service TCP/UDP_32768_65535
  service TCP/UDP_2049
  service TCP/UDP111
  action permit
 rule name T1_To_XWH_T3
  source-zone core
  destination-zone egress                 
  source-address address-set ********.
  destination-address address-set *********/21.
  service TCP_7001
  action permit
 rule name snmp123
  source-zone core
  destination-zone egress
  service snmp
  service snmptrap
  service TCP_3555
  service TCP3556
  service TCP_3557
  service TCP_3558
  service smtp
  service TCP_8080
  service tcp_8080
  service dns
  service TCP_9092
  service tcp_22
  action permit
 rule name T4_F5_to_T1_*********
  source-zone egress
  destination-zone core                   
  source-address address-set ********/16.
  destination-address address-set *********./24
  destination-address address-set *********.
  service TCP_8087
  service http_8082
  service TCP_8085
  service http
  action permit
 rule name T4_to_T1_AD
  source-zone egress
  destination-zone core
  source-address address-set ********/16.
  destination-address address-set ********.
  destination-address address-set T1_AD_F5_18.0.10.200
  destination-address address-set ********./32
  action permit
 rule name T1_OCS_to_T4
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set ********/16.
  service ssh
  service TCP_31306                       
  service TCP_3555
  service TCP_3558
  action permit
 rule name V3_to_JiGuan
  source-zone egress
  destination-zone core
  source-address address-set G3_MS_4.190.120.0/22
  destination-address address-set JiGuan_192.168.182.130
  service http
  service https
  action permit
 rule name JiGuan_to_V3
  source-zone core
  destination-zone egress
  source-address address-set JiGuan_192.168.182.130
  destination-address address-set G3_MS_4.190.120.0/22
  service http
  service https
  action permit
 rule name CSLC_TO_NEWT3
  source-zone core
  destination-zone egress
  source-address address-set CSLC_104.21.2.0/24
  source-address address-set **********/16.
  source-address address-set **********./24
  source-address address-set NAT_27.16.18.10.
  source-address address-set CSLC_172.20.30.0/24
  source-address address-set **********.
  destination-address address-set T3_NAT_********
  service ssh
  service TCP_7001
  service https
  service http
  service TCP_5672
  service tcp_8080
  service TCP_9001
  service TCP9080
  service TCP_8001
  service TCP_8989
  service TCP_34443
  service TCP_3389
  action permit
 rule name aopeng_test
  source-zone egress
  destination-zone core
  source-address address-set ***********/22.
  destination-address address-set ********/16.
  action permit
 rule name CSLC_to_T3
  source-zone core
  destination-zone egress
  source-address address-set CSLC_***********/24
  source-address address-set CSLC_***********/24
  source-address address-set CSLC_172.20.30.0/24
  source-address address-set CSLC_172.20.23.0/24
  destination-address address-set ********.
  destination-address address-set ********.
  destination-address address-set **********./32
  service https
  service TCP8443
  service TCP_8080
  action permit
 rule name Test_ITO_Kongfanqiang
  source-zone egress
  destination-zone core
  source-address address-set XWH_T1_4.191.80.0/24
  source-address address-set "********** /24"
  destination-address address-set ********.
  action permit                           
 rule name XWH_T3_To_18.2.1.245
  source-zone egress
  destination-zone core
  source-address address-set XWH_T3_********31
  source-address address-set *********5.
  source-address address-set T19_18.6.19.4
  source-address address-set **********/24.
  source-address address-set **********/24.
  destination-address address-set Dsvs_18.2.1.245
  service tcp_8000
  action permit
 rule name T1_to_CSLC
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set *********/24.
  source-address address-set *********./24
  source-address address-set *********./32
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  service TCP_20050
  service icmp
  service TCP_8080                        
  action permit
 rule name to_************
  source-zone egress
  destination-zone core
  source-address address-set *********4/32.
  destination-address address-set ************/32.
  service TCP_6006
  action permit
 rule name ***********_to_T4
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set T4_**********
  destination-address address-set T4_**********
  destination-address address-set **********.
  service http_8082
  service tcp_7003
  service icmp
  action permit
 rule name T1_To_XWH1
  source-zone core
  destination-zone egress                 
  source-address address-set T1_********/32
  source-address address-set *********/24.
  destination-address address-set XWH_**********_9
  action permit
 rule name XWH_To_T1_1
  source-zone egress
  destination-zone core
  source-address address-set XWH_**********_9
  source-address address-set T4_*********
  destination-address address-set T1_********/32
  destination-address address-set *********/24.
  destination-address address-set T1_********
  action permit
 rule name 500WAN&JCW_to_T2N1
  source-zone egress
  destination-zone core
  source-address address-set 500wan_********/24
  source-address address-set JCW_***********
  source-address address-set uazz_********/24
  source-address address-set *************.
  source-address address-set **********/24.
  source-address address-set **********/24.
  destination-address address-set T2N1_**********
  destination-address address-set T5_************
  destination-address address-set **********/24.
  service tcp_52701
  action permit
 rule name TEST_XWH_To_T1
  source-zone egress
  destination-zone core
  source-address address-set G3_MS_4.190.120.0/22
  source-address address-set ***********/22.
  source-address address-set ***********/24.
  source-address address-set 104.21.2.64_
  destination-address address-set ************/24.
  destination-address address-set **********/24.
  action permit
 rule name NEW_T3_TO_***********
  source-zone egress
  destination-zone core
  source-address address-set ********42.
  source-address address-set ********37.
  destination-address address-set ***********.
  service TCP8443
  action permit
 rule name NEW_T3_TO_T3                   
  source-zone egress
  destination-zone core
  source-address address-set *************.
  source-address address-set T3_NAT_********
  destination-address address-set T3_***********/32
  destination-address address-set T3_*********/32
  destination-address address-set T3_*********/32
  destination-address address-set ***********/32.
  destination-address address-set ***********./32
  service tcp_8088
  service TCP_28080
  service TCP_6021
  service TCP_6201
  service ssh
  service icmp
  service TCP_8086
  service TCP_8087
  action permit
 rule name XWH_NEW_T3_to_T2_AB_T3
  source-zone egress
  destination-zone core
  source-address address-set T3_NAT_********
  source-address address-set ***********/32.
  source-address address-set ***********.
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set ********/8.
  service http_8082
  service TCP_50094
  service TCP_50095
  service tcp_52701
  service tcp_7003
  action permit
 rule name T4_to_T1_*********1_114
  source-zone egress
  destination-zone core
  source-address address-set ***********.
  source-address address-set T4_*********
  destination-address address-set ********.
  service TCP_8080
  action permit
 rule name T4_To_XWH
  source-zone core
  destination-zone egress
  source-address address-set T4_48.0.100.0/24
  source-address address-set T3_********/24
  source-address address-set T2_T3_**********
  source-address address-set ********/8.
  source-address address-set **********.
  source-address address-set ********/8.
  source-address address-set ********/16.
  destination-address address-set **********.
  destination-address address-set *********.
  destination-address address-set *********./24
  action permit
 rule name T4_to_T1_18.0.5.2126
  source-zone egress
  destination-zone core
  source-address address-set ********.
  destination-address address-set ********./24
  service Tcp_7001
  action permit
 rule name "LuYu_G3_ILO  To  T1_********"
  source-zone egress
  destination-zone core
  source-address address-set G3_ILO_4.191.249.0/24
  source-address address-set G3_MS_4.190.122.0/24
  source-address address-set G3_MS_4.190.120.0/22
  destination-address address-set ********/16.
  action permit
 rule name XWH_T3_To_CSLC
  source-zone egress
  destination-zone core
  source-address address-set ********33.
  destination-address address-set CSLC_104.21.2.51/32
  service TCP_28080
  service TCP_20617
  action permit
 rule name Ceshi_IF_flow
  source-zone core
  destination-zone egress
  source-address address-set ***********/32_
  destination-address address-set *************/32_
  service TCP_5000
  action permit
 rule name Ceshi_SNMP
  source-zone core
  destination-zone egress
  source-address address-set ***********/32_
  source-address address-set ***********/32_
  destination-address address-set **********_16
  service snmp                            
  service icmp
  action permit
 rule name T1_To_G3_SNMP
  source-zone core
  destination-zone egress
  source-address address-set T1_18.0.254.242/32
  destination-address address-set XWH_G3_4.176.0.0/24
  destination-address address-set XWH_G3_4.176.1.0/24
  action permit
 rule name G3_To_T1_**********
  source-zone egress
  destination-zone core
  source-address address-set *******./8
  destination-address address-set **********.
  action permit
 rule name G3_TEST
  source-zone core
  destination-zone egress
  destination-address address-set *******./8
  action permit
 rule name T1_To_XWH_G3_*******/8
  source-zone core
  destination-zone egress                 
  source-address address-set ********/8_
  destination-address address-set XWH_G3_*******/8
  action permit
 rule name CSLC_To_BG_**************
  source-zone egress
  destination-zone core
  source-address address-set CSLC_**********
  destination-address address-set BG_**************
  service http
  service https
  action permit
 rule name T1_To_CSLC
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set CSLC_**********
  service ssh
  action permit
 rule name BG_To_CSLC
  source-zone core
  destination-zone egress
  source-address address-set BG_**************
  source-address address-set **************_236
  destination-address address-set CSLC_**********
  service http
  service https
  action permit
 rule name CSLC_To_BG
  source-zone egress
  destination-zone core
  source-address address-set CSLC_**********
  destination-address address-set **************_236
  service TCP_32600
  action permit
 rule name To_CSLC_TiaoBanJi
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set CSLC_TiaoBanJi_**********
  destination-address address-set CSLC_TiaoBanJi_**********
  service TCP_3389
  service tcp_135
  service tcp135totcp139
  service udp135toudp139
  service TCP_445
  action permit                           
 rule name TO_CSLC_TiaoBanJi
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  source-address address-set *********/24.
  source-address address-set ********/24.
  destination-address address-set CSLC_TiaoBanJi_**********
  service ssh
  service TCP_8000
  action permit
 rule name BG_To_XWH_T4MS
  source-zone core
  destination-zone egress
  source-address address-set T1_********/32
  source-address address-set *********/24.
  source-address address-set ***********./32
  destination-address address-set XWH_T4MS_*********/32
  service TCP/UDP111
  service TCP/UDP_2049
  service TCP/UDP_32768_65535
  action permit
 rule name T1_To_XWHG3
  source-zone core                        
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set XWHG3_**********_37
  destination-address address-set XWH_磁带机_18.5.85.1
  destination-address address-set XWH_磁带机_18.5.85.2
  service tcp_29092
  service tcp_30000
  action permit
 rule name CeShi_To_XWH
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/16.
  destination-address address-set XWH_**********_8
  destination-address address-set **********.
  service TCP_8000_8010
  service http
  service tcp_22
  service TCP_8013
  service TCP_8018
  service TCP_186
  action permit
 rule name BG_To_XWH_22                   
  source-zone core
  destination-zone egress
  source-address address-set BG_**************
  destination-address address-set XWH_**********_8
  service tcp_22
  action permit
 rule name BG_To_XWH
  source-zone core
  destination-zone egress
  source-address address-set **************_236
  destination-address address-set XWH_**********01_103
  service http
  action permit
 rule name BG_To_XWH_116
  source-zone core
  destination-zone egress
  source-address address-set **************_236
  destination-address address-set XWH_**********_8
  service TCP8011
  action permit
 rule name XWH_To_BG_52.232
  source-zone egress
  destination-zone core                   
  source-address address-set ************_103
  source-address address-set ************.
  destination-address address-set BG_**************
  destination-address address-set ********01.
  destination-address address-set T3_Harbor_**********
  destination-address address-set T1_********04/32
  destination-address address-set **********.
  destination-address address-set **************/32.
  destination-address address-set ********/24.
  service http
  service https
  service ssh
  action permit
 rule name XWH_To_BG_52.234
  source-zone egress
  destination-zone core
  source-address address-set XWH_**********_8
  destination-address address-set **************_236
  service TCP_32600
  action permit
 rule name XWH_To_BG_32.18
  source-zone egress
  destination-zone core                   
  source-address address-set XWH_**********_8
  source-address address-set XWHG3_*********/24
  source-address address-set **********/24_
  destination-address address-set BG_*************
  destination-address address-set *************/**************
  destination-address address-set *************.
  service smtp
  service icmp
  service TCP_25
  service TCP_1433
  action permit
 rule name XWH_To_BG_32.121
  source-zone egress
  destination-zone core
  source-address address-set XWH_**********_8
  destination-address address-set BG_**************
  service TCP_5000
  service TCP10080
  action permit
 rule name T1_To_CSLC_Lab
  source-zone core
  destination-zone egress
  source-address address-set ********/24. 
  destination-address address-set CSLC_*************/24
  service https
  service TCP9443
  action permit
 rule name CSLC_To_XWHT3_tiaobanji
  source-zone core
  destination-zone egress
  source-address address-set CSLC_***********/24
  destination-address address-set XWH_T3_********
  service TCP_3389
  action permit
 rule name T1_To_XWHG4_*********/24
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set XWHG3_*********/24
  service tcp_8088
  service tcp_22
  service http
  service TCP30400
  service TCP_30600
  service tcp31306
  action permit                           
 rule name T1_To_XWHG3_*********/24
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  source-address address-set "********* /24"
  destination-address address-set XWHG3_*********/21
  service tcp_8088
  action permit
 rule name XWHG3_To_T1
  source-zone egress
  destination-zone core
  source-address address-set XWHG3_*********/24
  destination-address address-set "********* /24"
  destination-address address-set **********/32.
  service TCP_28080
  service TCP_28081
  action permit
 rule name **********_To_XWH_T4
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set ********/24.
  source-address address-set **********_  
  destination-address address-set XWH_T4NAT_********/24
  destination-address address-set XWH_G3_********/24
  destination-address address-set **********./24
  action permit
 rule name XWH_T4_To_T1_***********
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set XWH_T4_NAT_**********
  source-address address-set XWHG3_*********/24
  destination-address address-set T1_***********
  service tcp31306
  action permit
 rule name BG_To_XWH_T4_NAT
  source-zone core
  destination-zone egress
  source-address address-set BG_**************
  source-address address-set ********/24.
  destination-address address-set XWH_T4_*********_9
  destination-address address-set XWH_T4_NAT_*********_31
  service ssh
  service http
  service TCP_8000_8010                   
  action permit
 rule name XWH_T4_NAT_**************
  source-zone egress
  destination-zone core
  source-address address-set XWH_T4_NAT_**********
  destination-address address-set BG_**************
  service TCP10080
  service TCP_5000
  action permit
 rule name XWHT4_To_T1
  source-zone egress
  destination-zone core
  source-address address-set XWH_T4_NAT_**********
  source-address address-set XWHG3_*********/24
  source-address address-set XWH_T4_NAT_18.6.4.134
  destination-address address-set ********/16.
  action permit
 rule name backup
  source-zone core
  destination-zone egress
  source-address address-set 192.168.66.150_32
  service ssh
  service snmp                            
  action permit
 rule name XWH_T3_To_T1
  source-zone egress
  destination-zone core
  source-address address-set XWH_T3_********30
  source-address address-set ***********/24.
  source-address address-set ***********/24.
  destination-address address-set T1_JMJ_18.2.1.244
  service TCP_8008
  service icmp
  action permit
 rule name To_XWH_172.16.7.0/24
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set XWH_172.16.7.0/24
  action permit
 rule name To_XWH_********
  source-zone core
  destination-zone egress
  source-address address-set *************/24.
  destination-address address-set ********/32.
  service http                            
  action permit
 rule name To_XWH_T3_********
  source-zone core
  destination-zone egress
  source-address address-set T3_38.2.1.0/24
  destination-address address-set ********/32_
  action permit
 rule name to_vxlan_T3
  source-zone core
  destination-zone egress
  source-address address-set ***************/32_
  destination-address address-set ********/32_
  service TCP_445
  service TCP_3389
  service icmp
  action permit
 rule name To_***********
  source-zone core
  destination-zone egress
  source-address address-set *********4.
  source-address address-set **********/32.
  destination-address address-set ***********.
  service TCP_3389                        
  service TCP_1099
  action permit
 rule name ceshi
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set **********.
  destination-address address-set ********/16.
  destination-address address-set 18.1.2.241_32
  action permit
 rule name ceshi_To_XWHT3_tiaobanji
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set XWHT3_tiaobanji_********/24
  service TCP_3389
  service icmp
  action permit
 rule name egress_to_core_Csljcto500w
  source-zone egress
  destination-zone core
  source-address address-set **************/32.
  destination-address address-set *********/24.
  destination-address address-set *********/22.
  action permit
 rule name Csljcto500w
  policy logging
  source-zone egress
  destination-zone fez
  source-address address-set 500wan**************
  destination-address address-set *********/24.
  action permit
 rule name uuzz1
  source-zone egress
  destination-zone fez
  source-address address-set ********./27
  destination-address address-set *********/24.
  action permit
 rule name ospf
  service ospf
  service echo-udp
  service udp
  action permit
 rule name egress_to_egw_20
  source-zone egress
  destination-zone egw                    
  source-address address-set ***********/24.
  destination-address address-set PAS************
  action permit
 rule name egress_to_egw_0
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set abc
  destination-address address-set eft_gw_out
  service http
  action permit
 rule name egress_to_egw_1
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set ccb
  destination-address address-set eft_gw_out
  service http
  action permit
 rule name egw_to_core_28
  source-zone egw
  destination-zone core
  source-address address-set ABSBISOnlineA02_IN
  destination-address address-set absbcsaaa07
  service tcp_8080
  action permit
 rule name egw_to_core_26
  source-zone egw
  destination-zone core
  source-address address-set pasdb_************.
  source-address address-set pasdb_************.
  destination-address address-set TAS*********1
  destination-address address-set TAS*********2
  destination-address address-set TAS*********0
  service tcp_8080
  action permit
 rule name egress_to_egw_2
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set abc
  destination-address address-set eft_gw_out
  service ftp
  service ftp_8000_9000
  action permit
 rule name egress_to_egw_3                
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set ccb
  destination-address address-set eft_gw_out
  service ftp
  service ftp_8000_9000
  action permit
 rule name egress_to_egw_4
  policy logging
  source-zone egress
  destination-zone egw
  service icmp
  action permit
 rule name fez_to_core_7
  policy logging
  source-zone fez
  destination-zone core
  destination-address address-set trendmicro_server
  destination-address address-set ***********.
  action permit
 rule name fez_core_24
  source-zone fez                         
  destination-zone core
  source-address address-set "********* /24"
  destination-address address-set "********* /24"
  service TCP_20406
  service TCP_20407
  action permit
 rule name fez_core_25
  source-zone fez
  destination-zone core
  source-address address-set "********* /24"
  destination-address address-set "********* /24"
  service TCP_3191
  service TCP_7001
  action permit
 rule name fez_core_26
  source-zone fez
  destination-zone core
  source-address address-set "********* /24"
  destination-address address-set "********* /24"
  service TCP_21910
  action permit
 rule name egress_to_egw_5
  policy logging                          
  source-zone egress
  destination-zone egw
  source-address address-set ccb
  destination-address address-set eft_gw_out
  service tcp_30000
  action permit
 rule name egress_to_egw_6
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set abc
  destination-address address-set eft_gw_out
  service http_9998
  action permit
 rule name egress_to_core_16
  source-zone egress
  destination-zone core
  source-address address-set *********/24.
  destination-address address-set *********1.
  destination-address address-set *********2.
  destination-address address-set TLS_*********3.
  service rpc_6610_6614
  action permit                           
 rule name egress_to_egw_7
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set abc
  destination-address address-set eft_gw_out
  service http_7779
  action permit
 rule name egress_to_egw_8
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set ccb
  destination-address address-set eft_gw_out
  service http_7779
  service tcp_7778
  action permit
 rule name egress_to_egw_9
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set icbc
  destination-address address-set eft_gw_out
  service ftp
  service ftp_8000_9000
  action permit
 rule name egress_to_egw_10
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set icbc
  destination-address address-set eft_gw_out
  service http_7779
  action permit
 rule name egress_to_egw_11
  disable
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set *************/32.
  destination-address address-set eft_gw_out
  service http_7779
  action permit
 rule name egress_to_egw_12
  policy logging
  source-zone egress                      
  destination-zone egw
  source-address address-set *************/32.
  destination-address address-set bisonline1_*********
  destination-address address-set bisonline2_*********
  destination-address address-set bisonline3_*********
  service tcp_8066
  action permit
 rule name egress_to_egw_13
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set ali*************
  source-address address-set lianlian**************
  destination-address address-set *********/32.
  service ftp
  service icmp
  service tcp_20
  service tcp_22
  service tcp_8065
  action permit
 rule name egress_to_egw_14
  policy logging
  source-zone egress                      
  destination-zone egw
  source-address address-set **********/32.
  action permit
 rule name egress_egw_15
  policy logging
  source-zone egress
  destination-zone egw
  source-address address-set ***********/32.
  source-address address-set Lottery_18.4.252.96/27
  destination-address address-set ABSBISOnline_OUT
  service tcp_8066
  action permit
 rule name egw_to_egress_0
  policy logging
  source-zone egw
  destination-zone egress
  source-address address-set eft_gw_out
  destination-address address-set abc
  service https
  service tcp_8055
  action permit
 rule name egw_to_egress_1
  policy logging                          
  session logging
  source-zone egw
  destination-zone egress
  source-address address-set eft_gw_out
  destination-address address-set ccb
  service tcp_20002
  service tcp_7777&20001
  action permit
 rule name egw_to_egress_2
  policy logging
  session logging
  source-zone egw
  destination-zone egress
  action permit
 rule name egw_to_egress_3
  policy logging
  session logging
  source-zone egw
  destination-zone egress
  source-address address-set sms/mail_gw_out
  destination-address address-set message&mail_system
  service http
  action permit                           
 rule name egw_to_egress_4
  policy logging
  session logging
  source-zone egw
  destination-zone egress
  source-address address-set eft_gw_out
  destination-address address-set mail_system
  service https
  action permit
 rule name egw_to_egress_5
  policy logging
  session logging
  source-zone egw
  destination-zone egress
  source-address address-set sms/mail_gw_out
  destination-address address-set mail_system
  service https
  action permit
 rule name egw_to_egress_6
  policy logging
  session logging
  source-zone egw
  destination-zone egress                 
  source-address address-set sms_gw_out
  destination-address address-set message_system
  service http
  service https
  action permit
 rule name egw_to_egress_7
  policy logging
  session logging
  source-zone egw
  destination-zone egress
  source-address address-set emial_gw_out
  destination-address address-set mail_system
  service http
  service https
  action permit
 rule name egw_to_egress_8
  policy logging
  session logging
  source-zone egw
  destination-zone egress
  source-address address-set eft_gw_out
  destination-address address-set icbc
  service ftp_8000_9000                   
  service tcp_20083
  action permit
 rule name egress_fez_10
  policy logging
  session logging
  source-zone egw
  destination-zone egress
  source-address address-set eft_gw_out
  destination-address address-set ************/27.
  service icmp
  service tcp_23215_20
  action permit
 rule name egress_to_fez_3
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  service icmp
  action permit
 rule name egress_to_fez_0
  policy logging
  session logging
  source-zone egress                      
  destination-zone fez
  source-address address-set cslvsts
  destination-address address-set as_out
  destination-address address-set asdb
  action permit
 rule name egress_to_fez_1
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  source-address address-set sporttery
  destination-address address-set absas_lb1_out
  service http_8081
  service http_8082
  service https
  action permit
 rule name egress_to_fez_9
  policy logging
  session logging
  source-zone egress
  source-zone egw
  destination-zone fez
  destination-zone egress                 
  source-address address-set sporttery
  destination-address address-set absasftp_cluster_virture_ip
  service ftp
  action permit
 rule name egress_to_fez_4
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  source-address address-set ca_server(outline)
  destination-address address-set ca_server(all)
  service http
  service https
  action permit
 rule name egress_to_fez_5
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  source-address address-set **********/24.
  destination-address address-set as_out
  service ftp
  service https                           
  action permit
 rule name egress_to_fez_15
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  source-address address-set **********/24.
  destination-address address-set ********/24/
  destination-address address-set *********/24.
  service tcp_20201
  service tcp8080
  service icmp
  action permit
 rule name egress_to_fez_6
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  source-address address-set 500wan**************/32
  source-address address-set tencent***********/24
  source-address address-set uuzz********
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set uuzz************
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set **************/32.
  source-address address-set yunying_************
  destination-address address-set as_out
  service ftp
  service https
  action permit
 rule name egress_to_fez_7
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  source-address address-set **********/32.
  action permit
 rule name fez_to_egress_0
  policy logging
  session logging
  source-zone fez
  destination-zone egress
  service icmp                            
  action permit
 rule name fez_to_egress_1
  policy logging
  session logging
  source-zone fez
  destination-zone egress
  source-address address-set ca_server(all)
  destination-address address-set ca_server(outline)
  service http
  service https
  action permit
 rule name fez_to_egress_2
  policy logging
  session logging
  source-zone fez
  destination-zone egress
  source-address address-set as_out
  destination-address address-set **********/24.
  action permit
 rule name egress_to_core_0
  policy logging
  session logging
  source-zone egress                      
  destination-zone core
  source-address address-set egress_aaa_client
  destination-address address-set ise_server
  service cisco_radius
  action permit
 rule name egress_to_core_1
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set egress_snmp_client
  destination-address address-set eccom_neteagle
  destination-address address-set hp_openview01
  destination-address address-set snmp_server(lms)
  service snmp
  action permit
 rule name egress_to_core_2
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set sporttery
  destination-address address-set cb_info_hub
  service tcp_52704
  action permit
 rule name New_T3_to_CSLC_CASSIT
  source-zone egress
  destination-zone core
  source-address address-set *************.
  source-address address-set T3_NAT_********
  destination-address address-set ************.
  destination-address address-set ************/
  service http
  service https
  service icmp
  service TCP9080
  action permit
 rule name New_T3_to_cslc_***********
  source-zone egress
  destination-zone core
  source-address address-set *************.
  destination-address address-set ***********.
  service TCP8443
  service icmp
  service tcp_8080
  service https                           
  service TCP_4433
  service TCP_24433
  action permit
 rule name egress_to_core_3
  policy logging
  session logging
  source-zone egress
  destination-zone core
  service http
  service https
  service icmp
  service ntp
  service nntp
  service rpc_6611
  service snmp
  service ssh
  service tcp_3080
  service TCP8443
  service TCP_21
  action permit
 rule name egress_to_core_4
  policy logging
  session logging                         
  source-zone egress
  destination-zone core
  source-address address-set call_center
  destination-address address-set customerserivcegw_out
  service http
  service https
  action permit
 rule name egress_to_core_5
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set cslvsts
  destination-address address-set matserver_out
  action permit
 rule name egress_to_core_6
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set *********/24.
  service tcp_52704                       
  action permit
 rule name egress_to_core_7
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set 500wan**************/32
  source-address address-set tencent***********/24
  source-address address-set uuzz********
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set uuzz************
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set 500wan_*************
  source-address address-set 500wan_*************
  source-address address-set yunying_************
  service tcp_52701
  service tcp_52704
  action permit
 rule name egress_to_core_8               
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set **********/32.
  action permit
 rule name egress_to_core_9
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set cslcump*********
  destination-address address-set g2cbgw_*********
  destination-address address-set g2matserver_*********
  action permit
 rule name egress_to_core_11
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set "Webserver *********/24"
  destination-address address-set (*********/24
  destination-address address-set *********/24.
  service remote_desktop
  action permit
 rule name core_to_egress_test
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set **********.
  source-address address-set *********3.
  source-address address-set ********_
  source-address address-set ***********.
  source-address address-set **********_80
  source-address address-set ************/32.
  action permit
 rule name core_to_egress_1
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set eccom_neteagle
  source-address address-set hp_openview
  source-address address-set snmp_server(lms)
  destination-address address-set egress_snmp_client
  service snmp
  action permit
 rule name core_to_egress_2
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ************.
  action permit
 rule name core_to_egress_3
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  destination-address address-set cslc_**********
  destination-address address-set cslc_***********
  destination-address address-set cslc_***********
  action permit
 rule name core_to_egress_4
  policy logging                          
  session logging
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  destination-address address-set cslcump*********
  destination-address address-set cslcump*********
  action permit
 rule name core_to_egress_5
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set *********/28.
  destination-address address-set cslcump*********
  action permit
 rule name core_egress_6
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set yanpiaoserver1
  source-address address-set **********.
  source-address address-set *********4.  
  source-address address-set absbisaba01
  source-address address-set absbisaba02
  source-address address-set absbisaba05
  destination-address address-set CSLCyanpiaoserver1
  destination-address address-set CSLCyanpiaoserver2
  destination-address address-set ************/32.
  destination-address address-set *************/32.
  service tcp_8080
  service smtp
  service ftp
  action permit
 rule name egw_to_fez_0
  policy logging
  session logging
  source-zone egw
  destination-zone fez
  service ftp
  service icmp
  service remote_desktop
  service http_8081
  action permit
 rule name fez_to_egw_0
  policy logging                          
  session logging
  source-zone fez
  destination-zone egw
  source-address address-set as_in
  destination-address address-set sms/mail_gw_in
  service ftp
  action permit
 rule name fez_to_egw_1
  policy logging
  session logging
  source-zone fez
  destination-zone egw
  service ftp
  service icmp
  service remote_desktop
  action permit
 rule name fez_to_egw_2
  policy logging
  session logging
  source-zone fez
  destination-zone egw
  source-address address-set as_out
  destination-address address-set sms/mail_gw_in
  service ftp
  action permit
 rule name fez_to_egw_3
  policy logging
  session logging
  source-zone fez
  destination-zone egw
  source-address address-set as_out
  destination-address address-set eft_gw_out
  service tcp_9000
  action permit
 rule name egw_to_core_0
  policy logging
  session logging
  source-zone egw
  destination-zone core
  destination-address address-set trendmicro_server
  action permit
 rule name egw_to_core_1
  policy logging
  session logging
  source-zone egw
  destination-zone core                   
  source-address address-set eft_gw_in
  destination-address address-set uat_f6_testpc(*********/24)
  action permit
 rule name egw_to_core_2
  policy logging
  session logging
  source-zone egw
  destination-zone core
  service ftp
  service icmp
  service remote_desktop
  action permit
 rule name egw_to_core_3
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set eft_gw_in
  destination-address address-set oltp_ab
  service udp_50086
  action permit
 rule name egw_to_core_4
  policy logging                          
  session logging
  source-zone egw
  destination-zone core
  source-address address-set eft_gw_in
  destination-address address-set bis_ab
  service ftp
  action permit
 rule name egw_to_core_5
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set egw_snmp_client
  destination-address address-set eccom_neteagle
  destination-address address-set hp_openview
  destination-address address-set snmp_server(lms)
  action permit
 rule name egw_to_core_6
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set egw_aaa_client
  destination-address address-set ise_server
  service cisco_radius
  action permit
 rule name egw_to_core_7
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set eft_gw_in
  destination-address address-set monsvc_out
  service tcp_1000
  action permit
 rule name egw_to_core_8
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set eft&emai&sms(********/24)
  destination-address address-set nbu_server
  service mgmt_nbu
  action permit
 rule name egw_to_core_9
  policy logging                          
  session logging
  source-zone egw
  destination-zone core
  source-address address-set eft&emai&sms(********/24)
  destination-address address-set control_m
  service mgmt_control_m
  action permit
 rule name egw_to_core_10
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set eft&emai&sms(********/24)
  destination-address address-set patrol_server
  service mgmt_patrol
  action permit
 rule name egw_to_core_11
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set egw_ad
  destination-address address-set patrol_server
  service mgmt_patrol
  action permit
 rule name egw_to_core_12
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set egw_ad
  destination-address address-set *********6/32.
  action permit
 rule name egw_to_core_14
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set absbisonline_sql_in
  source-address address-set absbisonline_web01_in
  source-address address-set absbisonline_web02_in
  destination-address address-set absbisonline_sql
  destination-address address-set absbisonlinea01
  destination-address address-set absbisonlinea02
  service ftp_8020&8021
  service tcp_8067                        
  action permit
 rule name egw_to_core_15
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set eft&emai&sms(********/24)
  destination-address address-set zabbix_**********
  action permit
 rule name egw_to_core_16
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set absbisonline_msdtc_in
  source-address address-set absbisonline_sql_in
  source-address address-set absbisonline_web01_in
  source-address address-set absbisonline_web02_in
  source-address address-set absbisonline_web_vip_in
  destination-address address-set stm_svc_out
  service http
  action permit
 rule name egw_to_core_17                 
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set absbisonline_msdtc_in
  source-address address-set absbisonline_sql_in
  source-address address-set absbisonline_web01_in
  source-address address-set absbisonline_web02_in
  source-address address-set absbisonline_web_vip_in
  destination-address address-set absbisonline_msdtc
  destination-address address-set absbisonline_sql
  destination-address address-set absbisonline_vip
  destination-address address-set absbisonlinea01
  destination-address address-set absbisonlinea02
  service tcp_8069
  action permit
 rule name core_to_egw_0
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set trendmicro_server
  action permit                           
 rule name core_to_egw_1
  disable
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set uat_f6_testpc(*********/24)
  destination-address address-set eft_gw_out
  action permit
 rule name core_to_egw_2
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set uat_f6_testpc(*********/24)
  destination-address address-set eft_gw_in
  action permit
 rule name core_to_egw_3
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set bcs_aa       
  destination-address address-set sms/mail_gw_in
  service ftp
  action permit
 rule name core_egw_42
  source-zone core
  destination-zone egw
  source-address address-set *************.
  destination-address address-set pas_************/32.
  service http
  service tcp_8080
  service icmp
  action permit
 rule name core_to_egw_4
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set oltp_ab
  destination-address address-set sms/mail_gw_in
  service ftp
  action permit
 rule name core_to_egw_5
  policy logging                          
  session logging
  source-zone core
  destination-zone egw
  source-address address-set oltp_ab
  destination-address address-set eft_gw_in
  service udp_50086
  action permit
 rule name core_to_egw_6
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set rsa_syslog_server
  destination-address address-set egw_syslog_client
  service rsa_syslog
  action permit
 rule name core_to_egw_7
  policy logging
  session logging
  source-zone core
  destination-zone egw
  service ftp
  service icmp                            
  service remote_desktop
  action permit
 rule name core_to_egw_test
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set **********.
  source-address address-set **********.
  source-address address-set *********3.
  action permit
 rule name core_to_egw_8
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set uat_f6_testpc
  destination-address address-set absgwzdca01
  action permit
 rule name core_to_egw_9
  policy logging
  session logging
  source-zone core                        
  destination-zone egw
  source-address address-set eccom_neteagle
  source-address address-set snmp_server(lms)
  source-address address-set hp_openview
  destination-address address-set egw_snmp_client
  service snmp
  action permit
 rule name core_to_egw_10
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set bcs_aa
  destination-address address-set emial_gw_in
  service ftp
  action permit
 rule name core_to_egw_11
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set oltp_ab
  destination-address address-set emial_gw_in
  service ftp
  action permit
 rule name core_to_egw_12
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set bcs_aa
  destination-address address-set sms_gw_in
  service ftp
  action permit
 rule name core_to_egw_13
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set oltp_ab
  destination-address address-set sms_gw_in
  service ftp
  action permit
 rule name core_to_egw_14
  policy logging
  session logging                         
  source-zone core
  destination-zone egw
  source-address address-set nbu_server
  destination-address address-set eft&emai&sms(********/24)
  service mgmt_nbu
  action permit
 rule name core_to_egw_15
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set control_m
  destination-address address-set eft&emai&sms(********/24)
  service mgmt_control_m
  action permit
 rule name core_to_egw_16
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set patrol_server
  destination-address address-set eft&emai&sms(********/24)
  service mgmt_patrol                     
  action permit
 rule name core_to_egw_17
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set patrol_server
  destination-address address-set egw_ad
  service mgmt_patrol
  action permit
 rule name core_to_egw_18
  disable
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set ********/24.
  destination-address address-set emial_gw_in
  destination-address address-set sms_gw_in
  action permit
 rule name core_to_egw_19
  disable
  policy logging                          
  session logging
  source-zone core
  destination-zone egw
  source-address address-set hp_openview02
  destination-address address-set abseftgwa02_in
  action permit
 rule name core_to_egw_20
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set *********7/32.
  destination-address address-set egw_ad
  action permit
 rule name core_to_egw_22
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set absbisonline_sql
  source-address address-set absbisonlinea01
  source-address address-set absbisonlinea02
  destination-address address-set absbisonline_sql_in
  destination-address address-set absbisonline_web01_in
  destination-address address-set absbisonline_web02_in
  service ftp_8020&8021
  service tcp_8067
  action permit
 rule name core_to_egw_23
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set *********/24.
  destination-address address-set *********/32.
  service tcp_8066
  action permit
 rule name core_to_egw_24
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set *********/24.
  destination-address address-set *********/32.
  service tcp_8066
  action permit                           
 rule name core_to_egw_25
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set zabbix_**********
  destination-address address-set eft&emai&sms(********/24)
  action permit
 rule name fez_to_core_16
  source-zone fez
  destination-zone core
  source-address address-set as&asdb(*********/24)
  destination-address address-set *********.
  service TCP6379
  service tcp_8080
  service tcp_3306
  action permit
 rule name fez_to_core_0
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as_in        
  destination-address address-set bcs_aa
  service rpc+
  action permit
 rule name fez_to_core_1
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as_in
  destination-address address-set absstmtsvc_lb_out
  service http
  action permit
 rule name fez_to_core_2
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as_in
  destination-address address-set igw_out
  service tcp_135
  service tcp_5056
  action permit
 rule name fez_to_core_3                  
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set bj_prod_lb_a01&a02
  source-address address-set snmp_fez_client
  destination-address address-set eccom_neteagle
  destination-address address-set snmp_server(lms)
  destination-address address-set hp_openview
  action permit
 rule name fez_to_core_4
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set aaa_fez_client
  destination-address address-set ise_server
  service cisco_radius
  action permit
 rule name fez_to_core_6
  policy logging
  session logging
  source-zone fez                         
  destination-zone core
  source-address address-set as_in
  destination-address address-set monsvc_out
  service ftp
  service https
  service tcp_1000
  action permit
 rule name fez_to_core_8
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as&asdb(*********/24)
  destination-address address-set nbu_server
  service mgmt_nbu
  action permit
 rule name fez_to_core_9
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as&asdb(*********/24)
  destination-address address-set control_m
  service mgmt_control_m
  action permit
 rule name fez_to_core_10
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as&asdb(*********/24)
  destination-address address-set patrol_server
  service mgmt_patrol
  action permit
 rule name fez_to_core_11
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set *********/24.
  destination-address address-set odc_as_server
  destination-address address-set odc_betslip
  destination-address address-set ************.
  action permit
 rule name fez_to_core_12
  policy logging                          
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as&asdb(*********/24)
  destination-address address-set *********6/32.
  action permit
 rule name fez_to_core_13
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as&asdb(*********/24)
  destination-address address-set zabbix_**********
  action permit
 rule name fez_to_core_5
  source-zone fez
  destination-zone core
  action permit
 rule name core_to_fez_0
  policy logging
  session logging
  source-zone core
  destination-zone fez                    
  source-address address-set rsa_syslog_server
  destination-address address-set fez_syslog_client
  service rsa_syslog
  action permit
 rule name core_to_fez_1
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set monsvc_out
  destination-address address-set as_in
  service ftp
  action permit
 rule name core_to_fez_2
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set *********/24.
  destination-address address-set ca_dsvs01
  destination-address address-set ca_dsvs02
  destination-address address-set ca_gateway_3
  service http                            
  service icmp
  action permit
 rule name core_to_fez_3
  source-zone core
  destination-zone fez
  service ftp
  service icmp
  service remote_desktop
  service ssh
  service TCP8443
  service snmp
  action permit
 rule name core_to_fez_test
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set **********.
  source-address address-set **********.
  source-address address-set *********3.
  action permit
 rule name core_to_fez_4
  policy logging                          
  session logging
  source-zone core
  destination-zone fez
  source-address address-set oltp_ab
  destination-address address-set as_in
  service udp_50086
  action permit
 rule name core_to_fez_5
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set trendmicro_server
  action permit
 rule name core_to_fez_6
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set eccom_neteagle
  source-address address-set snmp_server(lms)
  source-address address-set hp_openview
  destination-address address-set bj_prod_lb_a01&a02
  destination-address address-set snmp_fez_client
  service snmp
  action permit
 rule name core_to_fez_7
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set vsts_test_pc
  destination-address address-set asdb
  service tcp_1433&1434
  action permit
 rule name core_to_fez_8
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set nbu_server
  destination-address address-set as&asdb(*********/24)
  service mgmt_nbu
  action permit
 rule name core_to_fez_9
  policy logging                          
  session logging
  source-zone core
  destination-zone fez
  source-address address-set patrol_server
  destination-address address-set as&asdb(*********/24)
  service mgmt_patrol
  action permit
 rule name core_to_fez_10
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set control_m
  destination-address address-set as&asdb(*********/24)
  service mgmt_control_m
  action permit
 rule name core_to_fez_11
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set ************/23.
  destination-address address-set *********/24.
  action permit
 rule name core_to_fez_12
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set hp_openview02
  destination-address address-set absasdba01
  action permit
 rule name core_fez_32
  source-zone core
  destination-zone fez
  source-address address-set *********.
  destination-address address-set **********.
  destination-address address-set **********.
  destination-address address-set **********1.
  destination-address address-set **********.
  destination-address address-set **********2.
  service TCP_1433
  service tcp_58662
  action permit
 rule name core_to_fez_13
  policy logging                          
  session logging
  source-zone core
  destination-zone fez
  source-address address-set *********7/32.
  destination-address address-set as&asdb(*********/24)
  action permit
 rule name core_to_fez_14
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set ********/28.
  destination-address address-set absas_lb1_out
  destination-address address-set as_out
  action permit
 rule name core_to_fez_15
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set csl_ab_mat03_**********
  source-address address-set csl_ab_mat05_**********
  destination-address address-set absasftp_cluster_virture_ip
  service tcp_8090
  action permit
 rule name core_to_fez_16
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set csl_ab_opcc01_**********
  source-address address-set csl_ab_opcc02_**********
  source-address address-set csl_ab_opcc03_**********
  source-address address-set csl_ab_opcc04_**********
  source-address address-set csl_ab_opcc05_**********
  destination-address address-set absasftp_cluster_virture_ip
  destination-address address-set **********/32.
  service tcp_8090
  action permit
 rule name core_to_fez_17
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set csl_ab_opcc01_**********
  destination-address address-set ca_gateway_2
  service tcp_8080
  action permit
 rule name core_to_fez_18
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set zabbix_**********
  destination-address address-set as&asdb(*********/24)
  action permit
 rule name core_to_egw_26
  source-zone core
  destination-zone egw
  source-address ********** mask *************
  destination-address address-set eft_gw_in
  service tcp_9000
  action permit
 rule name core_to_egw_27
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address ********** mask *************
  destination-address address-set absasa03_out
  service tcp_9000
  action permit
 rule name egw_to_core_18
  session logging
  source-zone egw
  destination-zone core
  source-address address-set lottreasure************/24
  source-address address-set uuzz********
  destination-address ********** mask *************
  service tcp_8080
  action permit
 rule name egw_to_core_19
  session logging
  source-zone egw
  destination-zone core
  source-address address-set ABSBISOnline_IN
  destination-address address-set ABSBISOnlinecore
  service ms_sql
  service smb
  service nbname
  action permit
 rule name Core_to_FEZ_19                 
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set **********.
  destination-address address-set as&asdb(*********/24)
  service ssh
  action permit
 rule name Core_to_FEZ_20
  session logging
  source-zone core
  destination-zone fez
  source-address address-set absbcsaaa01
  destination-address address-set **********/32.
  service tcp_8090
  action permit
 rule name fez_to_egw_4
  session logging
  source-zone fez
  destination-zone egw
  source-address address-set absasa01_in
  destination-address address-set abseftgwa01_in
  service tcp_9000                        
  action permit
 rule name core_to_egw_28
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set bis_ab
  destination-address address-set ABSBISOnline_IN
  service ftp
  action permit
 rule name 20
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set ********/24.
  destination-address address-set *************/32.
  action permit
 rule name 21
  policy logging
  session logging
  source-zone egw
  destination-zone core                   
  source-address address-set eft&emai&sms(********/24)
  source-address address-set egw_ad
  destination-address address-set WSUS
  service http
  action permit
 rule name 22
  source-zone egw
  destination-zone core
  source-address address-set ABSBISOnline_IN
  source-address address-set eft_gw_in
  destination-address address-set zabbix_**********
  service tcp_31051
  action permit
 rule name 30
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set *************/32.
  destination-address address-set ********/24.
  destination-address address-set *********/24.
  service icmp
  service tcp_8066                        
  action permit
 rule name 31
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set **********/32.
  source-address address-set ********39/32.
  source-address address-set **********.
  source-address address-set **********.
  action permit
 rule name 32
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set zabbix_**********
  destination-address address-set eft&emai&sms(********/24)
  service tcp_31060
  service tcp_31070
  action permit
 rule name 33
  policy logging                          
  session logging
  source-zone core
  destination-zone egw
  source-address address-set **********.
  destination-address address-set eft&emai&sms(********/24)
  service tcp_41099
  action permit
 rule name 34
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set bis_ab
  destination-address address-set eft_gw_in
  service ftp_8000_9000
  application app FTP
  action permit
 rule name 35
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set CSLC_PASS01_************
  source-address address-set CSLC_PASS03_************
  source-address address-set CSLC_PASS04_************
  source-address address-set CSLC_PASS02_*************
  destination-address address-set eft_gw_out
  service tcp_9000
  action permit
 rule name 36
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set *************/24.
  destination-address address-set ABSBISOnline_OUT
  service icmp
  service ssh
  service tcp_8065
  action permit
 rule name core_fez_21
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set Betslip101   
  source-address address-set Betslip105
  source-address address-set Betslip106
  source-address address-set Betslip205
  destination-address address-set absas_lb1_out
  service http_8081
  service https
  action permit
 rule name core_fez_22
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set ***********/24.
  source-address address-set **********/24.
  source-address address-set **********/24.
  destination-address address-set *********/24.
  service ftp
  service https
  action permit
 rule name core_fez_23
  policy logging
  session logging
  source-zone core                        
  destination-zone fez
  source-address address-set ************/24.
  source-address address-set **********/24.
  destination-address address-set *********/24.
  service ftp
  service https
  action permit
 rule name egress_egw_16
  policy logging
  session logging
  source-zone egress
  destination-zone egw
  source-address address-set *********/24.
  destination-address address-set ************.
  destination-address address-set ************.
  service tcp_1433&1434
  service tcp_8008
  action permit
 rule name fez_core_14
  policy logging
  session logging
  source-zone fez
  destination-zone core                   
  source-address address-set nbu_server
  destination-address address-set absasdba01
  destination-address address-set absasdba02
  destination-address address-set absasdba03
  destination-address address-set asdb
  action permit
 rule name core_fez_24
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set absasdba01
  source-address address-set absasdba02
  source-address address-set absasdba03
  source-address address-set asdb
  destination-address address-set nbu_server
  action permit
 rule name egress_to_core_10
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set "500WAN_INFO *********/27"
  destination-address address-set CB_Info_HUB5
  destination-address address-set CB_Info_HUB6
  destination-address address-set CB_Info_HUB7
  service tcp_52704
  action permit
 rule name "egress_to fez_10"
  source-zone egress
  destination-zone fez
  source-address address-set eft_gw_out
  destination-address address-set ************/27.
  action permit
 rule name core_egress_7
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set (*********/24
  source-address address-set *********/24.
  destination-address address-set "Webserver *********/24"
  action permit
 rule name core_fez_25
  policy logging
  session logging                         
  source-zone core
  destination-zone fez
  source-address address-set zabbix_**********
  destination-address address-set ********/24.
  destination-address address-set as&asdb(*********/24)
  service TCP31050_70
  action permit
 rule name fez_core_15
  policy logging
  session logging
  source-zone fez
  destination-zone core
  source-address address-set as_in
  source-address address-set asdb
  destination-address address-set zabbix_**********
  service TCP31051
  action permit
 rule name egress_17
  policy logging
  session logging
  source-zone egress
  destination-zone egw
  source-address address-set *********/24.
  destination-address address-set ABSBISOnline_Cluster_OUT_Group
  destination-address address-set ABSBISOnline_OUT
  service tcp_8066
  action permit
 rule name core_fez_26
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set **********.
  destination-address address-set as&asdb(*********/24)
  service TCP41099
  action permit
 rule name core_egress_8
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set BetGW01**********
  source-address address-set BetGW01**********
  source-address address-set BetGW03**********
  source-address address-set BetGW04**********
  destination-address address-set UMP*********
  destination-address address-set UMP*********
  action permit
 rule name core_fez_27
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set uuzz**********
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set uuzz*************
  source-address address-set UUZZ2_********
  destination-address address-set *********/24.
  service http
  service https
  service http_8081
  action permit
 rule name core_egress_9
  policy logging
  session logging
  source-zone core
  destination-zone egress                 
  source-address address-set **********/24.
  destination-address address-set UMP*********
  service http
  action permit
 rule name egress_fez_11
  policy logging
  session logging
  source-zone egress
  destination-zone fez
  source-address address-set TaoBao_18.4.2.160/27
  destination-address address-set absas_lb1_out
  destination-address address-set absasftp_cluster_virture_ip
  service ftp
  service https
  action permit
 rule name core_egress_10
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  destination-address address-set UMP*********
  destination-address address-set UMP*********
  action permit
 rule name egw_core_23
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set ABSBISOnline_IN
  destination-address address-set stm_svc_out
  destination-address address-set ABSBISOnlinecore
  service tcp_8069
  service http
  action permit
 rule name egw_to_core_24
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set PAS************
  destination-address address-set TAS*********0
  destination-address address-set TTS*********0
  service tcp_8080
  action permit
 rule name egw_core_25                    
  policy logging
  session logging
  source-zone egw
  destination-zone core
  source-address address-set PBS
  destination-address address-set zabbix_**********
  service TCP31051
  action permit
 rule name core_egw_37
  policy logging
  session logging
  source-zone core
  destination-zone egw
  source-address address-set *************/24.
  destination-address address-set ABSBISOnline_OUT
  service ftp
  action permit
 rule name core_egw_38
  policy logging
  source-zone core
  destination-zone egw
  source-address address-set *********4/32.
  destination-address address-set ABSBISOnlineA02_IN
  service tcp_8065
  action permit
 rule name core_egw_39
  policy logging
  source-zone core
  destination-zone egw
  source-address address-set *********/24.
  destination-address address-set PAS************
  service tcp_8080
  action permit
 rule name core_egw_40
  policy logging
  source-zone core
  destination-zone egw
  source-address address-set TAS*********1
  destination-address address-set PAS************
  service tcp_8080
  service icmp
  action permit
 rule name core_egw_41
  policy logging
  source-zone core
  destination-zone egw                    
  source-address address-set zabbix_**********
  destination-address address-set PBS
  service TCP31050_70
  action permit
 rule name core_fez_28
  policy logging
  source-zone core
  destination-zone fez
  source-address address-set **********.
  source-address address-set **********/
  source-address address-set **********.
  destination-address address-set as&asdb(*********/24)
  service ssh
  action permit
 rule name egress_to_core_12
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set UMP*********/22
  destination-address address-set *********.
  service tcp_7003
  action permit
 rule name core_egress_11                 
  policy logging
  source-zone core
  destination-zone egress
  source-address address-set *********4/32.
  destination-address address-set **********/32.
  service https
  application app FTP
  action permit
 rule name core_fez_29
  source-zone core
  destination-zone fez
  source-address address-set **************/32.
  destination-address address-set *********/24.
  service https
  action permit
 rule name core_fez_30
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set *********1.
  source-address address-set absbcsaaa07
  destination-address address-set absasftp_cluster_virture_ip
  destination-address address-set ABSASFTP_ClusterIP_IN
  service tcp_8090
  action permit
 rule name core_fez_31
  policy logging
  session logging
  source-zone core
  destination-zone fez
  source-address address-set *********/24.
  destination-address address-set absas_lb1_out
  service https
  service http_8081
  action permit
 rule name core_egress_12
  policy logging
  source-zone core
  destination-zone fez
  source-address address-set *********/32.
  destination-address address-set CSLC*************
  service ftp
  action permit
 rule name core_egress_13
  policy logging                          
  source-zone core
  destination-zone egress
  source-address address-set 500wan**************
  destination-address address-set UMP*********/22
  service ftp
  action permit
 rule name egress_egw18
  source-zone egress
  destination-zone egw
  source-address address-set **********/32.
  destination-address address-set ************.
  service tcp_8008
  action permit
 rule name egress_core_13
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set ************.
  destination-address address-set TTS*********0
  service tcp_8080
  action permit
 rule name egress_core_14
  policy logging                          
  source-zone egress
  destination-zone core
  source-address address-set 500wan**************
  destination-address address-set CB_Info_HUB7
  service tcp_52701
  service tcp_52704
  action permit
 rule name egress_core_15
  source-zone egress
  destination-zone core
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  destination-address address-set zabbix_**********
  service TCP31051
  action permit
 rule name egress_fez_12
  policy logging
  source-zone egress
  destination-zone fez
  source-address address-set 500wan**************
  source-address address-set absasftp_cluster_virture_ip
  destination-address address-set absas_lb1_out
  service https
  service ftp
  action permit
 rule name core_egress_14
  policy logging
  source-zone core
  destination-zone egress
  source-address address-set zabbix_**********
  destination-address address-set **********/32.
  service TCP31050_70
  action permit
 rule name egress_to_core_17
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set **********/32.
  service http_8081
  action permit
 rule name DUANXIN
  policy logging                          
  session logging
  source-zone egress
  destination-zone core
  source-address address-set *********/24.
  action permit
 rule name egress_to_egress_1
  source-zone egress
  destination-zone egress
  source-address *********** mask *************
  destination-address ********** mask ***************
  service https
  action permit
 rule name uuzz
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set uuzz_**********/28
  destination-address address-set UMP*********/22
  service http
  action permit
 rule name egress_to_egw_19
  source-zone egress                      
  destination-zone egw
  source-address address-set **********/32.
  source-address address-set **********/32.
  destination-address address-set ************.
  service tcp_8008
  action permit
 rule name core_to_egw_30
  source-zone core
  destination-zone egw
  source-address address-set *************.
  destination-address address-set pas_************/32.
  service tcp_8080
  service icmp
  action permit
 rule name egw_to_core_27
  source-zone egw
  destination-zone core
  source-address address-set pas_************/32.
  source-address address-set pas_18.4.130.142/32.
  source-address address-set pasdb_************.
  source-address address-set pasdb_************.
  destination-address address-set TLS_*********3.
  service tcp_6662                        
  action permit
 rule name test_tiantou_CSLC
  policy logging
  session logging
  source-zone core
  destination-zone egress
  source-address address-set BetGW01**********
  source-address address-set BetGW01**********
  source-address address-set BetGW03**********
  source-address address-set BetGW04**********
  source-address address-set group*********
  destination-address address-set CSLC_103.11.1.12
  destination-address address-set CSLC_172.26.22.108
  destination-address address-set CSLC_172.26.22.200
  service TCP_4433
  service ftp
  service icmp
  action permit
 rule name jingcaiwang
  source-zone egress
  destination-zone core
  source-address address-set jingcaiwangsoureceip
  destination-address address-set *********/24.
  service icmp
  service tcp_52701
  service tcp_52704
  action permit
 rule name ICMPANY
  source-zone core
  destination-zone egress
  service icmp
  action permit
 rule name AQSM
  description anquansaomiao
  source-address address-set 安全扫描
  action permit
 rule name core_fez_33
  source-zone core
  destination-zone fez
  source-address address-set FSS01
  destination-address address-set **********/32.
  service ftp
  action permit
 rule name core_fez_34
  source-zone core
  destination-zone fez                    
  source-address address-set *********/32.
  destination-address address-set **********/32.
  service telnet
  action permit
 rule name fez_core_17
  source-zone fez
  destination-zone core
  source-address address-set **********/32.
  destination-address address-set *********/32.
  service telnet
  action permit
 rule name core_fez_35
  source-zone core
  destination-zone fez
  source-address address-set OLTPAB
  destination-address address-set **********/32.
  service telnet
  action permit
 rule name Core_egress_15
  source-zone core
  destination-zone egress
  source-address address-set 500wan**************/32
  source-address address-set 500wan**************
  source-address address-set 500wan**************.
  source-address address-set 500wan_*************
  source-address address-set 500wan_*************
  destination-address address-set *********/24.
  service tcp_52701
  service tcp_52704
  action permit
 rule name core_fez_36
  source-zone core
  destination-zone fez
  source-address address-set ********/24.
  destination-address address-set AS_**********
  service http_8081
  action permit
 rule name "core_to_fez 21"
  source-zone core
  destination-zone fez
  source-address address-set ********/24.
  destination-address address-set *********/24.
  service https
  service http
  service TCP8081
  action permit                           
 rule name core_to_fez_31
  source-zone core
  destination-zone fez
  source-address address-set **********/23.
  source-address address-set ********/32.
  source-address address-set ********/16.
  source-address address-set ********/24.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  service icmp
  service tcp8080
  service tcp_50443
  service telnet
  service ssh
  action permit
 rule name fez_to_core_17
  source-zone fez
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set **********/23.
  destination-address address-set ********/32.
  destination-address address-set ********/16.
  service tcp31306                        
  action permit
 rule name "u2 client to scp"
  source-zone egress
  destination-zone core
  source-address address-set yinwu_192.168.8.0
  destination-address address-set SCPvip_*********01
  service icmp
  service telnet
  service tcp_50443
  service https
  action permit
 rule name saomadui
  source-zone egress
  destination-zone fez
  source-address address-set zhifubao_172.16.2.0/24
  source-address address-set tencent_***********/24
  destination-address address-set SCPAPIGW_vip
  service icmp
  service tcp_50443
  action permit
 rule name taobao
  source-zone fez
  destination-zone egress                 
  source-address address-set SCPAPIGW_vip
  destination-address address-set zhifubao_172.16.2.0/24
  service tcp_50443
  action permit
 rule name core_to_fez_32
  source-zone core
  destination-zone fez
  source-address address-set ********/24.
  destination-address address-set SCPAPIGW_vip
  service tcp_50443
  action permit
 rule name fez_to_core_18
  source-zone fez
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set ********/16.
  action permit
 rule name fez_to_core_19
  source-zone fez
  destination-zone core
  source-address address-set ********/24/
  source-address address-set ********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  action permit
 rule name core_fez_40
  source-zone core
  destination-zone fez
  source-address address-set *********/24.
  source-address address-set *********/24.
  source-address address-set *********/24.
  source-address address-set *********/24.
  source-address address-set *********/24.
  source-address address-set ********/16.
  destination-address address-set ********/24.
  destination-address address-set ********/24/
  action permit
 rule name Core_to_egress_16
  source-zone core
  destination-zone egress
  source-address ********* mask ***************
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  service ssh
  action permit
 rule name 4.199_to_64.30
  source-zone core
  destination-zone fez
  source-address address-set **********.
  destination-address address-set absas_lb1_out
  service http_8082
  service https
  action permit
 rule name 4.234_to_64.30
  source-zone core
  destination-zone fez
  source-address address-set *********4/32.
  destination-address address-set absas_lb1_out
  service http_8082
  service https
  action permit
 rule name linshi
  source-zone fez
  destination-zone egress
  source-address address-set ***********.
  action permit                           
 rule name solarwinds
  source-zone fez
  source-address address-set ***********.
  action permit
 rule name solarwinds1
  service snmp
  service snmptrap
  action permit
 rule name solarwinds2
  source-zone core
  destination-zone fez
  source-address address-set *********.
  destination-address address-set ***********.
  action permit
 rule name solarwinds3
  source-zone egress
  destination-zone fez
  source-address address-set **********.
  destination-address address-set ***********.
  action permit
 rule name solarwinds4
  source-zone egw
  destination-zone fez                    
  source-address address-set **********.
  destination-address address-set ***********.
  action permit
 rule name syslog
  service syslog
  action permit
 rule name egw_to_core(WSUS)
  source-zone egw
  destination-zone core
  source-address address-set 18.4.3.1_50
  destination-address address-set WSUS
  service tcp_8530_8531
  action permit
 rule name egw_to_core(WSUS)2
  source-zone egw
  destination-zone core
  source-address address-set 18.4.4.1_18.4.4.50
  destination-address address-set WSUS
  service tcp_8530_8531
  action permit
 rule name egw_to_core(WSUS)3
  source-zone egw
  destination-zone core                   
  source-address address-set 18.4.129_18.4.130
  destination-address address-set WSUS
  service tcp_8530_8531
  action permit
 rule name fez_to_core(WSUS)
  source-zone fez
  destination-zone core
  source-address address-set *********_24
  destination-address address-set WSUS
  service tcp_8530_8531
  action permit
 rule name core_to_fez_34
  source-zone core
  destination-zone fez
  source-address address-set ********/24.
  source-address address-set *********/24.
  destination-address address-set ********/24/
  destination-address address-set ********/24.
  service tcp_20201
  service tcp31306
  action permit
 rule name fez_to_egress_3
  source-zone fez                         
  destination-zone egress
  source-address address-set ********/24/
  destination-address address-set **********/24.
  service tcp_9080
  service icmp
  service tcp_1521
  action permit
 rule name core_to_egress_12
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set *********/24.
  source-address address-set ***********;
  destination-address address-set **********/24.
  service tcp_3080
  service http
  service icmp
  service ssh
  service tcp1521
  action permit
 rule name core_to_egress_13
  source-zone core
  destination-zone egress                 
  source-address address-set ********/24.
  source-address address-set *********/24.
  destination-address address-set "New sporttery_**************"
  destination-address address-set "YunYing_Pay sys_**************"
  service http
  service icmp
  service ssh
  service tcp
  service TCP_80_3080_9080
  action permit
 rule name egress_to_fez
  description 172.18.60_to_18.5.1
  source-zone egress
  destination-zone fez
  source-address address-set ***********/24.
  destination-address address-set ********/24/
  destination-address address-set APIGWF5
  service tcp_20201
  action permit
 rule name fez_to_egress_4
  disable
  source-zone fez
  destination-zone egress                 
  source-address address-set ********/24/
  destination-address address-set ***********/24.
  action permit
 rule name To_CCB_*************
  source-zone fez
  destination-zone egress
  source-address address-set ccbgw_********91
  source-address address-set FAMBKGWA01_********1
  source-address address-set FAMBKGMGA01_*********
  destination-address address-set ccb_*************
  service https
  service icmp
  action permit
 rule name To_ccb_*************
  source-zone fez
  destination-zone egress
  source-address address-set ccbgw_********91
  destination-address address-set ccb_*************
  service icmp
  service tcp_8101_8103_8104
  action permit
 rule name To_internet
  source-zone fez                         
  destination-zone egress
  source-address address-set ccbgw_********91
  service icmp
  service http
  service https
  action permit
 rule name To_CSLC_************
  source-zone fez
  destination-zone egress
  source-address address-set FAMELPGWA01_********
  source-address address-set FAMELPGWB02_********
  source-address address-set ELP
  destination-address address-set CSLC_************
  destination-address address-set CSLC_************
  destination-address address-set ELP_F5
  service TCP8091
  service icmp
  action permit
 rule name 新竟彩网_to_webdcF5
  source-zone egress
  destination-zone core
  source-address address-set 新竟彩网
  destination-address address-set webdc*********
  destination-address address-set "********* /24"
  service tcp_52701
  action permit
 rule name webdcF5_to_新竟彩网
  source-zone core
  destination-zone egress
  source-address address-set webdc*********
  destination-address address-set 新竟彩网
  action permit
 rule name egress_to_fez_4444
  policy logging
  source-zone egress
  destination-zone fez
  source-address address-set ***********.
  destination-address address-set 18..5.0.0.
  service tcp_4444
  service Tcp_7001
  action permit
 rule name egress_to_core_4444
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set ***********. 
  destination-address address-set 18..5.0.0.
  service tcp_4444
  service TCP_7001
  action permit
 rule name egress_to_core_ICMP
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set ********./27
  destination-address address-set ********/16.
  service icmp
  action permit
 rule name jingcaiwangzhifuxitong
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set *********.
  action permit
 rule name core_to_liantongjingcai(2017092)
  policy logging
  source-zone core
  destination-zone egress
  source-address address-set **********(C043)
  destination-address address-set liantongjingcai
  action permit
 rule name internet
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set ********.
  source-address address-set ********.
  source-address address-set ************.
  source-address address-set ***********/32.
  source-address address-set ************./32
  source-address address-set ************./32
  source-address address-set **********./32
  source-address address-set zhiqi_************
  source-address address-set *********/24.
  action permit
 rule name ssh_http                       
  source-zone core
  destination-zone egress
  destination-address address-set *********/22.
  service http
  service ssh
  action permit
 rule name jiamiji
  source-zone core
  destination-zone egress
  source-address address-set jimiji
  destination-address address-set To_T2&T3_*************
  action permit
 rule name jiamiji_in
  source-zone egress
  destination-zone core
  source-address address-set To_T2&T3_*************
  destination-address address-set jiamiji
  action permit
 rule name jiankong
  source-zone core
  destination-zone fez
  source-address address-set zabbix_***********&232
  destination-address address-set zhifu_********/24_********/24
  action permit
 rule name jiankong_
  source-zone fez
  destination-zone core
  source-address address-set zhifu_********/24_********/24
  destination-address address-set zabbix_***********&232
  action permit
 rule name To_TencentCloud
  source-zone core
  destination-zone egress
  source-address address-set Corenat********/8
  destination-address address-set Tencent_Coudnet_**********/16
  action permit
 rule name To_keji_************
  source-zone core
  destination-zone egress
  source-address address-set OMS_*********/24
  destination-address address-set CSLC_************
  action permit
 rule name From_TencentCloud
  source-zone egress
  destination-zone fez
  source-address address-set TencentCloud 
  action permit
 rule name From_Tencent_2
  source-zone egress
  destination-zone core
  source-address 10.0.0.0 mask *********
  destination-address address-set Corenat********/8
  action permit
 rule name To_WechatNum_***********
  source-zone egress
  destination-zone core
  destination-address address-set WechatNum_***********
  action permit
 rule name wechat
  source-zone core
  destination-zone egress
  source-address address-set WechatNum_***********
  action permit
 rule name To_inforhub
  source-zone egress
  destination-zone core
  source-address address-set ***********_
  destination-address address-set *********_
  action permit                           
 rule name fez_To_TencentCloud
  source-zone fez
  destination-zone egress
  source-address address-set ********/8_
  destination-address address-set Tencent_Coudnet_**********/16
  action permit
 rule name To_APIGW
  source-zone core
  destination-zone fez
  source-address address-set ceshiClient_********/24
  destination-address address-set APIGW
  service TCP_9201
  action permit
 rule name snmp
  service snmp
  service snmptrap
  action permit
 rule name To__F5_PreGW
  source-zone egress
  destination-zone core
  destination-address address-set F5_PreGW
  service https
  service http                            
  action permit
 rule name PREGW
  source-zone core
  destination-zone egress
  source-address address-set PREGW
  action permit
 rule name To_ELP
  source-zone core
  destination-zone egress
  destination-address address-set new_elp
  action permit
 rule name AB_To_NewSit
  source-zone egress
  destination-zone core
  source-address address-set ab_ip
  destination-address address-set New_Sit
  action permit
 rule name LINSHI
  source-zone core
  destination-zone webserver
  action permit
 rule name LINSHI_2
  source-zone webserver                   
  destination-zone core
  action permit
 rule name To_Core
  source-zone core
  destination-zone fez
  source-address address-set Core
  destination-address address-set zhifu_core
  action permit
 rule name egress_to_egw_30
  source-zone egress
  destination-zone egw
  source-address address-set keji
  destination-address address-set EFTGW
  service TCP_9000
  action permit
 rule name zabbix_23215
  source-zone egw
  destination-zone core
  source-address address-set zabbix_*********
  destination-address address-set zabbix_client
  service TCP_23215
  action permit
 rule name To_keji_elpgw                  
  source-zone fez
  destination-zone egress
  source-address address-set AS_**********
  destination-address address-set keji_elpgw
  service TCP_9000
  action permit
 rule name To_EFTGW
  source-zone egw
  destination-zone core
  source-address address-set *********_
  destination-address address-set **********_
  service TCP_23215
  service TCP_20001
  service TCP_9003
  action permit
 rule name To_********01
  source-zone core
  destination-zone fez
  source-address address-set ********_
  destination-address address-set ********01_
  action permit
 rule name zhifu1.1_3
  source-zone core                        
  destination-zone egress
  source-address address-set duanxingw
  action permit
 rule name From_zhifu1.1
  source-zone core
  destination-zone egress
  source-address address-set zhifu1.1_5
  action permit
 rule name zhifu1.1_port_21514
  source-zone egress
  destination-zone core
  destination-address address-set zhifu_ip.port=21514
  service TCP_21514
  action permit
 rule name From_zhifu1.1_to_jianhang
  source-zone webserver
  destination-zone egress
  source-address address-set zhifu1.1_webserver
  action permit
 rule name To_Yinlian
  source-zone fez
  destination-zone egress
  source-address address-set To_Yinlian   
  action permit
 rule name From_Yinlian
  source-zone egress
  destination-zone fez
  destination-address address-set zhifu_ip.port=21514
  action permit
 rule name To_docker_7001
  source-zone core
  destination-zone fez
  source-address address-set Core_*********/24
  destination-address address-set To_Docker_7001
  service Tcp_7001
  action permit
 rule name To_500w
  source-zone core
  destination-zone egress
  source-address address-set Core_net
  destination-address address-set 500Wan
  action permit
 rule name From_500Wan
  source-zone egress
  destination-zone core
  source-address address-set 500Wan       
  destination-address address-set Core_net
  action permit
 rule name To_Dsvs
  source-zone egress
  destination-zone core
  destination-address address-set Dsvs_18.2.1.245
  action permit
 rule name To_pptp
  source-zone egress
  destination-zone core
  destination-address address-set PPTP
  action permit
 rule name From_CeshiManage
  source-zone core
  destination-zone fez
  source-address address-set ********/24.
  destination-address address-set ********/32_
  service TCP_21598
  action permit
 rule name From_yunying
  source-zone egw
  destination-zone core
  source-address address-set Yunying20180206
  destination-address address-set From_yunying_to_T1
  action permit
 rule name From_yunying20180208
  source-zone egress
  destination-zone core
  source-address address-set Yunying20180206
  destination-address address-set From_yunying_to_T1
  action permit
 rule name To_yunyingzhifu
  source-zone core
  destination-zone egress
  source-address address-set From_yunying_to_T1
  destination-address address-set Yunying20180206
  action permit
 rule name yingxiao
  source-zone core
  destination-zone fez
  source-address address-set *********.
  destination-address address-set *********.
  destination-address address-set *********.
  service TCP21405
  action permit
 rule name yunying_test                   
  source-zone egress
  destination-zone egw
  source-address address-set "********** /24"
  action permit
 rule name yunying_test_01
  source-zone egress
  destination-zone fez
  source-address address-set "********** /24"
  action permit
 rule name yunying_test_02
  source-zone egress
  destination-zone core
  source-address address-set "********** /24"
  action permit
 rule name ceshi_yunying
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set "********** /24"
  action permit
 rule name TEST18.2.17.102TOF5VIP
  source-zone core
  destination-zone fez                    
  source-address address-set test18.2.17.102
  destination-address address-set F5VIP**********
  service TCP8443
  action permit
 rule name core_to_egress_17
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set **********/32.
  destination-address address-set F5VIP**********
  destination-address address-set *********/24.
  service TCP8443
  action permit
 rule name core_to_fez
  source-zone core
  destination-zone fez
  source-address address-set "******** /32"
  destination-address address-set "********* /24"
  service tcp31306
  action permit
 rule name fez_core_27
  source-zone fez
  destination-zone core                   
  source-address address-set "********* /24"
  destination-address address-set "********* /24"
  service TCP_7001
  action permit
 rule name core_fez_57
  source-zone core
  destination-zone fez
  destination-address address-set "********* /24"
  service TCP_7001
  action permit
 rule name yunyinggongsi_2
  source-zone egress
  destination-zone fez
  source-address address-set yygg
  destination-address address-set yy
  action permit
 rule name To_**********
  source-zone core
  destination-zone fez
  source-address address-set ********/24.
  destination-address address-set **********_
  action permit
 rule name To_***********:7001            
  source-zone egress
  destination-zone core
  destination-address address-set ***********_
  service TCP_7001
  action permit
 rule name TIDB_core_to_egress
  source-zone core
  destination-zone egress
  source-address *********1 mask ***************
  source-address address-set *********/24.
  destination-address address-set TIDB_RS
  service TCP_4000
  action permit
 rule name Tidb_egress_to_core
  source-zone egress
  destination-zone core
  source-address address-set Tidb
  destination-address ********** mask ***************
  service TCP_3000_8686
  action permit
 rule name TiDB_core_to_egress
  source-zone core
  destination-zone egress                 
  source-address ********** mask ***************
  destination-address address-set Tidb
  service TCP_3000_8686
  action permit
 rule name any
  source-zone egress
  destination-zone core
  source-address address-set *********/24.
  destination-address address-set *********/24.
  action permit
 rule name any1
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  source-address address-set ********/24.
  destination-address address-set *********/24.
  action permit
 rule name any2
  source-zone core
  destination-zone egress
  source-address ********73 mask ***************
  destination-address address-set *********/24.
  action permit                           
 rule name any3
  source-zone egress
  destination-zone core
  source-address address-set *********/24.
  source-address address-set T4_172.16.8.0/24
  source-address address-set "********** /24"
  destination-address ********73 mask ***************
  action permit
 rule name ********73
  source-zone core
  destination-zone egress
  source-address address-set ********73_
  action permit
 rule name "G3 业务地址访问"
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set G3
  service https
  service http
  service ssh
  action permit
 rule name to_OPS                         
  source-zone egress
  destination-zone core
  source-address ********** mask *************
  destination-address ************ mask *************
  action permit
 rule name to_OPS_
  source-zone core
  destination-zone egress
  source-address ************ mask *************
  destination-address ********** mask *************
  action permit
 rule name Egress_to_OFFICE_105
  policy logging
  source-zone egress
  destination-zone core
  source-address address-set 18_5_252_0
  source-address address-set *********/24.
  destination-address ************** mask ***************
  destination-address ************** mask ***************
  destination-address ************** mask ***************
  destination-address ************** mask ***************
  destination-address ************** mask ***************
  destination-address ************** mask ***************
  destination-address address-set *************/**************
  action permit
 rule name Office_105_TO_Egress
  policy logging
  source-zone core
  destination-zone egress
  source-address ************** mask ***************
  source-address ************** mask ***************
  source-address ************** mask ***************
  source-address ************** mask ***************
  source-address ************** mask ***************
  source-address ************** mask ***************
  source-address address-set *************.
  destination-address address-set 18_5_252_0
  destination-address address-set *********/24.
  destination-address address-set *********/22.
  destination-address address-set ***********.
  action permit
 rule name to_F5_***********
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set *********/24.
  destination-address *********** mask ***************
  service icmp
  service tcp_8088
  action permit
 rule name Egress_to_Core_Office
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set *************/24.
  action permit
 rule name Office_Core_to_Egress
  source-zone core
  destination-zone egress
  source-address address-set *************/24.
  destination-address address-set **********/24.
  action permit
 rule name to_172.16.1.1
  source-zone core
  destination-zone egress
  source-address ********* mask *************
  destination-address ********** mask *************
  destination-address address-set *********/24.
  action permit                           
 rule name to_SWA04
  source-zone egress
  destination-zone core
  source-address ********** mask *************
  source-address address-set *********/24.
  destination-address ********* mask *************
  action permit
 rule name to_**********
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address ********** mask *************
  destination-address address-set XWH_172.16.7.0/24
  action permit
 rule name *********_to_DSVS
  source-zone core
  destination-zone fez
  source-address ********* mask ***************
  destination-address ********** mask ***************
  destination-address ********** mask ***************
  service tcp_8000
  service icmp
  action permit                           
 rule name CLJC_FEZ
  source-zone core
  destination-zone fez
  source-address address-set CSLC_************
  destination-address address-set elpgw_*********/72
  service TCP8091
  action permit
 rule name FEZ_CLJC
  source-zone fez
  destination-zone core
  source-address address-set elpgw_*********/72
  destination-address address-set CSLC_************
  service TCP8091
  action permit
 rule name BanGong_15/16
  source-zone core
  destination-zone egress
  source-address address-set **************_16
  destination-address address-set **********/24.
  service tcp_22
  service TCP_10500
  service http
  service TCP_2181                        
  service TCP_7001
  service tcp31306
  action permit
 rule name Egress_TO_BanGong_15/16
  source-zone egress
  destination-zone core
  destination-address address-set **************_16
  service TCP_162
  service tcp_10051
  action permit
 rule name G3_TO_***********
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set ***********.
  service TCP_28081
  action permit
 rule name Core_to_FEZ_**********_8000
  source-zone core
  destination-zone fez
  source-address address-set **********.
  destination-address address-set **********/32.
  service tcp_8000                        
  action permit
 rule name BanGong_52
  source-zone core
  destination-zone egress
  source-address address-set **************_236
  destination-address address-set **********/24.
  service tcp8080
  service tcp_22
  action permit
 rule name fez_core_28
  source-zone fez
  destination-zone core
  source-address address-set ********/24.
  destination-address address-set ***********/24_
  service http
  action permit
 rule name core_fez_280
  source-zone core
  destination-zone fez
  source-address address-set ***********/24_
  destination-address address-set ********/24.
  service tcp_22601
  action permit                           
 rule name core_fez_55
  source-zone core
  destination-zone fez
  source-address address-set ***********/24_
  source-address address-set ************_
  source-address address-set ********/24.
  destination-address address-set **********/24_
  destination-address address-set ********/24.
  service tcp_21406
  service icmp
  action permit
 rule name Core_Fez_64_65
  source-zone core
  destination-zone fez
  source-address address-set "********* /24"
  destination-address address-set "********* /24"
  service TCP_20880
  action permit
 rule name 182.168.32.108_TO_18.5.252/253
  source-zone core
  destination-zone egress
  source-address address-set **************.
  destination-address address-set 18.5.252/253
  service TCP_32600
  service ssh
  service http
  action permit
 rule name **********_Internet
  source-zone core
  destination-zone egress
  source-address address-set **********.
  action permit
 rule name 18.0.10_TO_18.5.252
  source-zone core
  destination-zone egress
  source-address address-set **********/68
  destination-address address-set **********71.
  destination-address address-set **********72.
  destination-address address-set ************.
  destination-address address-set ***********.
  service TCP_28081
  service TCP_28070
  action permit
 rule name EGRESS_Core
  source-zone egress
  destination-zone core                   
  source-address address-set ***********_63
  destination-address address-set **********_63
  service tcp_29092
  action permit
 rule name 18.5.252_TO_***********_28081
  source-zone egress
  destination-zone core
  source-address address-set **********.
  destination-address address-set ***********.
  service TCP_28081
  action permit
 rule name TiCai_APP_TO_T1
  source-zone egress
  destination-zone core
  source-address address-set T1Cai_APP
  action permit
 rule name YunYing_TO_T1
  source-zone egress
  destination-zone core
  source-address address-set 172.30.*.*.
  destination-address address-set **********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/32.
  destination-address address-set 18.5.128.*
  destination-address address-set **********.
  destination-address address-set *********.
  destination-address address-set **********.
  destination-address address-set "********* /24"
  destination-address *********** mask ***************
  destination-address address-set ***********.
  action permit
 rule name TO_***********_80
  source-zone core
  destination-zone egress
  destination-address address-set ***********.
  service http
  action permit
 rule name test
  source-zone egress
  destination-zone core
  destination-address address-set "********** /24"
  destination-address address-set ***********.
  action permit
 rule name To_***********_30400
  source-zone core
  destination-zone egress                 
  source-address address-set ********/24.
  destination-address address-set ***********.
  service TCP30400
  action permit
 rule name ZiDongHua_TO_G3
  source-zone core
  destination-zone egress
  source-address address-set *************.
  source-address address-set **************_16
  destination-address address-set **********/24.
  destination-address address-set **********.
  destination-address address-set **********/24.
  destination-address address-set 18.5.6.13_TO_18
  destination-address address-set *********/22.
  destination-address address-set ************/32.
  destination-address address-set ***********.
  action permit
 rule name TCP_30400
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set ***********.
  action permit                           
 rule name JiGuan
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set **********_211_212_213
  action permit
 rule name TESTYUNPINGTAItoT1DMZ
  source-zone egress
  destination-zone dmz
  source-address address-set TESTYUNPINGTAI
  action permit
 rule name TESTYUNPINGTAItoT1CORE
  source-zone egress
  destination-zone core
  source-address address-set TESTYUNPINGTAI
  action permit
 rule name TESTYUNPINGTAItoT1EGW
  source-zone egress
  destination-zone egw
  source-address address-set TESTYUNPINGTAI
  action permit
 rule name TESTYUNPINGTAItoT1FEZ
  source-zone egress                      
  destination-zone fez
  source-address address-set TESTYUNPINGTAI
  action permit
 rule name Egress_TO_BanGong_192.168.200.
  source-zone egress
  destination-zone core
  source-address address-set *********/22.
  destination-address address-set *************.
  action permit
 rule name yunpingtai
  source-zone egress
  destination-zone core
  source-address address-set *********/22.
  destination-address address-set ***********.
  service TCP_28081
  action permit
 rule name 18.7.8_TO_Core
  source-zone egress
  destination-zone core
  source-address address-set ********/24.
  destination-address address-set *********/24.
  destination-address address-set ********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  destination-address address-set *********/24.
  destination-address address-set ********/16.
  service TCP_9001
  action permit
 rule name egress_to_core
  source-zone egress
  destination-zone core
  source-address address-set ********/24.
  destination-address address-set ********/16.
  destination-address address-set *********/24.
  destination-address address-set ********/24.
  destination-address address-set *********/24.
  destination-address address-set *********/24.
  destination-address address-set **********/24.
  destination-address address-set **********.
  destination-address address-set *********/24_
  service TCP9100
  action permit
 rule name Core_G3
  description 20190220                    
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set **********_211_212_213
  service http
  service tcp8080
  action permit
 rule name G3_Core
  description 20190220
  source-zone egress
  destination-zone core
  source-address address-set **********_211_212_213
  destination-address address-set ********/16.
  service tcp8080
  service http
  action permit
 rule name core_egress
  source-zone core
  destination-zone egress
  source-address address-set ********37/32_
  destination-address address-set ***********/32_
  action permit
 rule name t1_w5r                         
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set ***********.
  service TCP_5480
  action permit
 rule name t1tiaobanji_w5r
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set ***********.
  action permit
 rule name T1_To_XWHT3
  source-zone core
  destination-zone egress
  source-address address-set T1_VC
  source-address address-set Mips_VC
  source-address address-set T1_vlan10
  source-address address-set 172.16.110.0_24
  destination-address address-set XWH_T3
  destination-address address-set 172.16.11.0_24
  action permit
 rule name "XWH_T3 To T1"                 
  source-zone egress
  destination-zone core
  source-address address-set XWH_T3
  source-address address-set 172.16.11.0_24
  destination-address address-set T1_VC
  destination-address address-set T1_vlan10
  destination-address address-set Mips_VC
  destination-address address-set 172.16.110.0_24
  action permit
 rule name To_172.16.11.0/24
  source-zone egress
  destination-zone core
  destination-address address-set 172.16.11.0_24
  action permit
 rule name egress_to_core_18.0.204.14
  source-zone egress
  destination-zone core
  destination-address address-set ***********.
  action permit
 rule name g3chat
  source-zone egress
  destination-zone core
  source-address address-set **********.  
  source-address address-set ***********.
  action permit
 rule name to_internet1
  source-zone core
  destination-zone egress
  source-address address-set 18.0.99.99_100
  action permit
 rule name 9443
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set **********_16
  service TCP9443
  service TCP_8013
  service https
  action permit
 rule name To_500wan
  source-zone core
  destination-zone egress
  source-address address-set T3_38.5.80.0/21
  source-address address-set T3_38.5.72.0/21
  destination-address address-set 500wan_10.0.1.211/32
  action permit                           
 rule name 500wan1
  source-zone core
  destination-zone egress
  source-address address-set T3_38.5.80.0/21
  destination-address address-set 500wan_10.0.1.211/32
  action permit
 rule name 500wan2
  source-zone egress
  destination-zone core
  source-address address-set 500wan_10.0.1.211/32
  destination-address address-set 38.5.34.0_24
  destination-address address-set 28.4.246.0_24
  destination-address address-set T3_38.5.80.0/21
  action permit
 rule name G3_WCS部署
  description 20190408
  source-zone egress
  destination-zone core
  source-address address-set **********.
  source-address address-set **********/24.
  source-address address-set ********/22.
  source-address address-set *********/22.
  source-address address-set ***********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  service TCP_28080
  action permit
 rule name To_**********
  source-zone egress
  destination-zone core
  source-address address-set ***********.
  destination-address address-set **********.
  service TCP_50094
  action permit
 rule name To_YunGuanPingTai
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set YunGuanPingTai_172.16.16.1
  service http
  service tcp_3306
  service TCP_5000
  service tcp_8080
  action permit
 rule name T1_To_XWHT4_tiaobanji
  source-zone core                        
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set XWH_T4_NAT_*********
  destination-address address-set XWH_T4_NAT_*********
  service ssh
  service TCP_162
  service tcp_10051
  action permit
 rule name BG_To_XWHG3
  source-zone core
  destination-zone egress
  source-address address-set **************_236
  destination-address address-set XWH_T4_NAT_*********
  destination-address address-set XWH_T4_NAT_*********
  service TCP8011
  action permit
 rule name XWHG3_To_BG
  source-zone egress
  destination-zone core
  source-address address-set XWH_T4_NAT_**********
  destination-address address-set **************_236
  service TCP_32600
  action permit                           
 rule name core_XWH_G3
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set ********.
  service tcp_22
  service TCP_31306
  service TCP9100
  service TCP_9090
  service TCP_9091
  service TCP_9093
  service TCP_3000
  service TCP_3558
  action permit
 rule name T15Fcsj_to_G3_F5
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set **********_16_17
  service TCP_30300
  service TCP_30416
  service TCP_8080
  action permit                           
 rule name ********_T4
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set *********/21_
  action permit
 rule name ********/32_T4_F5
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set **********/32_
  service TCP30400
  action permit
 rule name 123
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********.
  source-address address-set *********/24.
  destination-address address-set *********/24_
  action permit
 rule name CORE_EGRESS
  source-zone core                        
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set *********_9
  destination-address address-set T3_NAT_********
  service TCP_29200
  service tcp_30000
  service ssh
  action permit
 rule name new_T3_to_CSLC
  policy logging
  session logging
  source-zone egress
  destination-zone core
  source-address address-set ********33.
  source-address address-set ********37.
  source-address address-set ********49/32.
  source-address address-set ********32.
  destination-address address-set **************.
  destination-address address-set ************.
  destination-address address-set CSLC_104.21.2.0/24
  destination-address address-set CSLC_104.21.1.0/24
  service TCP_8089
  service TCP_8087                        
  service http
  service TCP_20050
  service TCP_8090
  service TCP_8086
  service tcp_8088
  service TCP_8083
  action permit
 rule name **************_3.133
  source-zone core
  destination-zone egress
  source-address address-set **************.
  destination-address address-set ********33.
  destination-address address-set ********32.
  action permit
 rule name CSL_to_new_T3
  source-zone core
  destination-zone egress
  source-address address-set ************.
  source-address address-set *************.
  destination-address address-set ********0.
  destination-address address-set ********.
  service TCP_7001
  service https                           
  action permit
 rule name G3tsinfluxdb01
  source-zone egress
  destination-zone core
  source-address address-set ***********.
  destination-address address-set ***********.
  service TCP_8086
  action permit
 rule name TO_***************/221
  source-zone egress
  destination-zone core
  source-address address-set ********35/136
  destination-address address-set ***************/224
  service https
  service ssh
  action permit
 rule name Egress_CORE_CAS
  source-zone egress
  destination-zone core
  source-address address-set XWH_T4_NAT_**********
  destination-address address-set **********/32.
  service TCP_28080
  action permit                           
 rule name CSLC_to_newT3_DB
  source-zone core
  destination-zone egress
  source-address address-set **************.
  destination-address address-set XWH_T4NAT_********/24
  service TCP_31306
  action permit
 rule name CSLC_to_NEWT3_DB
  source-zone core
  destination-zone egress
  source-address address-set **************.
  destination-address address-set XWHT3_tiaobanji_********/24
  service TCP_31306
  action permit
 rule name G3_tiaoshi
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set G3_*********
  action permit
 rule name JH_to_T3_SFTP
  source-zone core
  destination-zone egress                 
  destination-address address-set T3_*********
  action permit
 rule name T1_to_XWH18.5.252/74
  source-zone core
  destination-zone egress
  source-address address-set **********_84
  destination-address address-set ***********//28
  destination-address address-set *********//27
  destination-address address-set *********.
  service http
  service tcp_29092
  action permit
 rule name G3_To_T1
  source-zone egress
  destination-zone core
  source-address address-set "G3_*********/16 to T1"
  destination-address address-set ********/16.
  service ftp
  action permit
 rule name T3GW_to_CCB
  source-zone egress
  destination-zone core
  source-address address-set ********32.  
  service TCP_8182
  service TCP_8184
  service TCP_8181
  action permit
 rule name TO_*********
  source-zone egress
  destination-zone core
  source-address address-set ***********.
  destination-address address-set *********.
  service http
  action permit
 rule name OPS_to_T4MS
  source-zone core
  destination-zone egress
  source-address address-set ops_********11
  source-address address-set OPS_********12
  source-address address-set OPS_********13
  source-address address-set **********.
  destination-address address-set T4MS_18.5.99.11
  action permit
 rule name "G3_MS_4.190.120.0/22 To T1"
  source-zone egress
  destination-zone core                   
  source-address address-set G3_MS_4.190.120.0/22
  destination-address address-set ********73_
  service http
  action permit
 rule name G3AMS_to_T1
  source-zone egress
  destination-zone core
  source-address address-set GWAMSF5_18.5.98.15
  destination-address address-set ********/16.
  service TCP_30426
  action permit
 rule name G3_to_***********
  source-zone egress
  destination-zone core
  source-address address-set ********42.
  destination-address address-set ***********.
  service TCP8443
  action permit
 rule name New_T3_to_qc
  source-zone egress
  destination-zone core
  source-address address-set *************.
  destination-address address-set qc_*************
  service icmp
  service TCP8080
  action permit
 rule name New_T3_to_email1
  source-zone egress
  destination-zone core
  source-address address-set ********44/.32
  destination-address address-set 192.168.66.150_32
  service icmp
  service smtp
  action permit
 rule name T1_*********_to_G3
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  source-address address-set ********/8.
  source-address address-set ********./16
  source-address address-set ***********.
  source-address address-set ***********.
  source-address address-set ***********./32
  destination-address address-set XWHG3_*********/24
  destination-address address-set XWH_G3_********/24
  action permit                           
 rule name G3_to_T1_*********
  source-zone egress
  destination-zone core
  source-address address-set XWHG3_*********/24
  source-address address-set XWH_G3_********/24
  destination-address address-set *********/24.
  service ssh
  service TCP_8000_8010
  service ntp
  action permit
 rule name T3_to_CLSC
  source-zone egress
  destination-zone core
  source-address address-set ********43.
  source-address address-set *********4/32.
  destination-address address-set ***********./24
  destination-address address-set ************/32.
  service https
  service TCP_389
  service TCP_9090
  service TCP9080
  service TCP_6006
  action permit                           
 rule name G3AMS_to_T1_18.0.2.36
  source-zone egress
  destination-zone core
  source-address address-set ********.
  destination-address address-set ********.
  destination-address address-set *********.
  service TCP_3555
  action permit
 rule name NEWT3_to_CSLC
  session logging
  source-zone egress
  destination-zone core
  source-address address-set T3_NAT_********
  destination-address address-set **********./24
  action permit
 rule name G3_***********_to_T1_NTP
  source-zone egress
  destination-zone core
  source-address address-set ***********./24
  source-address address-set **********_16
  destination-address address-set ********73_
  destination-address address-set ***********./24
  service ntp                             
  action permit
 rule name T1_***********_to_T4
  source-zone core
  destination-zone egress
  source-address address-set ***********./32
  destination-address address-set **********./32
  service TCP_8080
  action permit
 rule name T3_to_CSLC_SIT
  source-zone egress
  destination-zone core
  source-address address-set ********46/32.
  destination-address address-set *************&************
  service TCP_8080
  service TCP_8090
  service icmp
  action permit
 rule name "G3_MS To T1_**********"
  source-zone egress
  destination-zone core
  source-address address-set G3_MS_4.190.121.231/32
  destination-address address-set **********.
  service TCP_3000                        
  action permit
 rule name T1_to_XWH_PD_G3_01
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address ********* mask ***********
  service tcp_22
  action permit
 rule name XWH_PD_G3_to_T1
  source-zone egress
  destination-zone core
  source-address address-set G3_MS_4.190.120.0/22
  source-address address-set 10.219.5.103_105
  destination-address address-set ********01.
  service http
  action permit
 rule name TO_**********/24
  source-zone egress
  destination-zone core
  source-address address-set ********47/32.
  source-address address-set T3_NAT_********
  destination-address address-set **********/24.
  service TCP_6008                        
  service https
  service TCP_4433
  service TCP_6006
  service icmp
  service TCP_34433
  action permit
 rule name to_***********/98
  source-zone egress
  destination-zone core
  source-address address-set ********48/32.
  destination-address address-set ***********/98
  service TCP8080
  service TCP_20150
  service icmp
  action permit
 rule name XWH_PD_to_T1_DC
  source-zone egress
  destination-zone core
  source-address address-set XWH_G3_*******/8
  destination-address address-set ********.
  service TCP_389
  action permit
 rule name T4_to_QC                       
  source-zone egress
  destination-zone core
  source-address address-set **********/32.
  destination-address address-set *************/32.
  service TCP8080
  service icmp
  action permit
 rule name TO_CSLC_************
  source-zone egress
  destination-zone core
  source-address address-set ********46/32.
  destination-address address-set ************/32.
  service http
  service icmp
  action permit
 rule name XWH_磁带机_to_T1
  source-zone egress
  destination-zone core
  source-address address-set XWH_磁带机_18.5.85.1
  source-address address-set XWH_磁带机_18.5.85.2
  destination-address address-set ********/16.
  destination-address address-set ********./16
  service TCP_6370                        
  service dns
  service TCP_5000
  service TCP_4100_4200
  service TCP_5003
  action permit
 rule name T4_to_email
  source-zone egress
  destination-zone core
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set *********11/32.
  source-address address-set *********85/32.
  source-address address-set *********86/32.
  destination-address address-set *************/**************
  service smtp
  action permit
 rule name to_***********
  source-zone egress
  destination-zone core
  source-address address-set *********1/32.
  destination-address address-set ***********/32.
  service icmp
  service TCP_34443                       
  action permit
 rule name TO_***********
  source-zone egress
  destination-zone core
  source-address address-set *************.
  source-address address-set ********37.
  destination-address address-set ***********/32.
  service TCP_8080
  service icmp
  service ssh
  action permit
 rule name TO_*********_44
  source-zone core
  destination-zone egress
  source-address address-set **************/22/**************
  source-address address-set **************/32.
  source-address address-set **************/32.
  destination-address address-set *********_44
  destination-address address-set ********/32.
  destination-address address-set *********/32.
  action permit
 rule name 500WAN_to_XWH_T3
  source-zone egress                      
  destination-zone core
  source-address address-set 500WAN_*************
  source-address address-set 500WAN_************
  destination-address address-set T3_NAT_********
  service https
  service TCP8443
  action permit
 rule name TO_**************
  source-zone egress
  destination-zone core
  source-address address-set ********34/32.
  destination-address address-set **************.
  service TCP_8087
  action permit
 rule name WSUS_to_Internet
  source-zone core
  destination-zone egress
  source-address address-set ********88/32.
  destination-address address-set WSUS_Internet
  service https
  service http
  action permit
 rule name FangBingDu_to_Internet         
  source-zone core
  destination-zone egress
  source-address address-set ********39/32.
  destination-address address-set FangBingDu_Internet
  service http
  service https
  action permit
 rule name core_to_egress_0
  policy logging
  session logging
  source-zone core
  destination-zone egress
  service icmp
  service https
  service ftp
  service http
  service ntp
  service nntp
  service ssh
  service tcp_3080
  service TCP_3389
  service tcp31306
  service TCP_8000                        
  service dns
  service dns-tcp
  action permit
 rule name NEW_to_Cslc
  source-zone egress
  destination-zone core
  source-address address-set ********48/32.
  source-address address-set ********42.
  destination-address address-set ***********/32.
  service TCP_21
  action permit
 rule name T4_to_***********
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set ***********/32.
  service TCP_20050
  action permit
 rule name XWH_to_T3_CSLC
  source-zone egress
  destination-zone core
  source-address address-set T3_NAT_********
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  destination-address address-set **********/32.
  service TCP_20150
  service http_8082
  service TCP_50094
  service TCP_50095
  service TCP_8087
  action permit
 rule name T2_to_CSLC
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/8.
  source-address address-set T3_38.2.1.0/24
  source-address ******** mask ***********
  source-address address-set **********.
  source-address address-set ********/24.
  source-address address-set ***********/32.
  destination-address address-set ***********/32.
  destination-address address-set ***********/32.
  service TCP_8080
  service TCP_20050
  service icmp                            
  action permit
 rule name CSLC_TO_T2
  source-zone egress
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set **********/32.
  destination-address address-set ********/32.
  destination-address address-set ********/32.
  destination-address address-set T3_38.2.1.0/24
  destination-address ******** mask ***********
  destination-address address-set *********.
  destination-address address-set ********/8.
  service tcp_7003
  service http_8082
  service icmp
  service ssh
  action permit
 rule name 支付1.9.6
  source-zone core
  destination-zone egress
  source-address address-set T2_T3_**********
  destination-address address-set *********.
  service ssh                             
  action permit
 rule name T3_to_CSLC
  source-zone egress
  destination-zone core
  source-address address-set ********32.
  source-address address-set ********33.
  destination-address address-set CSLC_***********/24
  destination-address address-set CSLC_***********/24
  destination-address address-set CSLC_172.20.23.0/24
  service TCP_9090
  service https
  action permit
 rule name CSLC_to_T1
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  destination-address address-set *********.
  service tcp_7003                        
  service http_8082
  service icmp
  action permit
 rule name T3_Client_to_CSLC
  source-zone egress
  destination-zone core
  source-address address-set *************.
  destination-address address-set *************/32.
  service TCP_8080
  service icmp
  action permit
 rule name ********_to_*********
  source-zone core
  destination-zone egress
  source-address address-set T1_********/32
  destination-address address-set T4_*********
  action permit
 rule name *********_to_********
  source-zone egress
  destination-zone core
  source-address address-set T4_*********
  destination-address address-set T1_********/32
  service TCP_8000_8010                   
  service ntp
  service TCP_10050
  service TCP_9091
  action permit
 rule name T4_AMS_to_T1_BMSDB
  source-zone egress
  destination-zone egress
  source-address address-set G3AMS_18.5.84.0./24
  destination-address address-set ********.
  destination-address address-set *********.
  service TCP_3555
  action permit
 rule name 第三方测试
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set CSLC_104.23.0.10
  destination-address address-set CSLC_104.23.0.11
  service TCP_28080
  service tcp_8088
  action permit
 rule name V3_to_OCS
  source-zone egress                      
  destination-zone core
  source-address address-set *******./8
  destination-address address-set **********.
  service ftp
  action permit
 rule name V3_hanyi
  source-zone egress
  destination-zone core
  source-address address-set V3_CORE_4.190.80.0/21
  destination-address address-set ********.
  service TCP_3555
  service TCP_3558
  action permit
 rule name V3_to_RMXDB_Linshj
  source-zone egress
  destination-zone core
  source-address address-set *******./8
  destination-address address-set ********/16.
  action permit
 rule name T1_RMXDB_to_V3
  source-zone core
  destination-zone egress
  source-address address-set ********/16. 
  source-address address-set ********/24.
  destination-address address-set *******./8
  action permit
 rule name TEST
  source-zone egress
  destination-zone core
  service TCP3556
  service TCP_3555
  service TCP_3557
  service TCP_3558
  service ssh
  action permit
 rule name ssh_snmp
  source-zone core
  destination-zone egress
  source-address address-set ***********/32_
  destination-address address-set *******./8
  service ssh
  service snmp
  action permit
 rule name New_T3_Client_to_CSLC
  source-zone egress
  destination-zone core                   
  source-address address-set *************.
  destination-address address-set ************.
  service TCP_8089
  service icmp
  action permit
 rule name *********_********
  source-zone egress
  destination-zone core
  source-address address-set *********/21.
  destination-address address-set ********./24
  service TCP_7001
  action permit
 rule name BG_To_T1
  source-zone egress
  destination-zone core
  source-address address-set BG_192.168.249.0/24
  destination-address address-set ********.
  service TCP_8001
  action permit
 rule name yunguanpingtai_bangong
  source-zone egress
  destination-zone core
  source-address address-set ************/32.
  destination-address address-set *************/32.
  service https
  service ssh
  service http
  service TCP_389
  service icmp
  action permit
 rule name T1_18.1.21.11_*********
  source-zone core
  destination-zone egress
  source-address address-set *********/24_
  destination-address address-set *********.
  service tcp_8088
  action permit
 rule name TEST1111
  source-zone egress
  destination-zone core
  source-address address-set *************/24.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set ***********/32_SDN
  destination-address address-set ********/24.
  action permit                           
 rule name *************_Test
  source-zone egress
  destination-zone core
  source-address address-set *************/24.
  destination-address address-set *********/24.
  destination-address address-set Corenat********/8
  service TCP_3389
  service TCP8022
  action permit
 rule name **********_84_to_***********
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set ***********./32
  service TCP_36524
  action permit
 rule name luyu_01
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set G3_ILO_4.191.249.0/24
  service tcp_8088
  action permit                           
 rule name luyu_02
  source-zone egress
  destination-zone core
  source-address address-set G3_ILO_4.191.249.0/24
  destination-address address-set ********/16.
  service tcp_8088
  action permit
 rule name TCP_COPY
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set **********./32
  service TCP_8080
  action permit
 rule name XWH_T4_to_kaifa
  source-zone egress
  destination-zone core
  source-address address-set **********/32.
  destination-address address-set **************/26.
  service TCP_9092
  action permit
 rule name XWH_T3_to_cslc
  source-zone egress                      
  destination-zone core
  source-address address-set ********47/32.
  source-address address-set ********37.
  source-address address-set ********43.
  destination-address address-set ***********./24
  destination-address address-set **********/24.
  service tcp_9080
  service TCP_6006
  service TCP_14433
  service TCP_24433
  service TCP_34433
  action permit
 rule name cslc_to_XWH_T3
  source-zone core
  destination-zone egress
  source-address address-set ***********./24
  destination-address address-set *********/32.
  service tcp_8080
  action permit
 rule name BigG3_desktop_to_cslc
  source-zone egress
  destination-zone core
  source-address address-set ********37.  
  destination-address address-set **********/24.
  service https
  service icmp
  action permit
 rule name T1_TO_XWH
  source-zone core
  destination-zone egress
  source-address address-set ********_8
  destination-address address-set *********./32
  service tcp_8080
  action permit
 rule name YJT2_CSLC
  source-zone core
  destination-zone egress
  source-address address-set ********/8.
  destination-address address-set ***********/32.
  service TCP_20050
  action permit
 rule name CSLC_YJT2
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set ********/8.
  service http_8082
  service tcp_22
  action permit
 rule name T19_CSLC
  source-zone egress
  destination-zone core
  source-address address-set T19_18.6.19.0
  destination-address address-set ***********/32.
  destination-address address-set ***********.
  service TCP_21
  service TCP8443
  action permit
 rule name T1CORE_TO_CSLC
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set **********8_99
  service ssh
  action permit
 rule name T1_to_bangong
  source-zone core
  destination-zone egress
  source-address address-set **********/16.
  destination-address address-set ***************/32.
  action permit
 rule name keji_to_T1jiamiji
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  source-address address-set CSLC_**********/24
  destination-address address-set T1_JMJ_18.2.1.244
  destination-address address-set Dsvs_18.2.1.245
  service TCP_8000
  service TCP_8008
  action permit
 rule name ***********_to_********
  source-zone egress
  destination-zone core
  source-address address-set ***********./24
  destination-address address-set ********/8.
  action permit
 rule name XWH_new_t3_to_jianhang
  source-zone egress
  destination-zone core
  source-address address-set ********33.
  destination-address address-set *************/32.
  service tcp_8101_8103_8104
  service icmp
  action permit
 rule name T4_jiaoyiluyou
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  service ssh
  action permit
 rule name T4_***********
  source-zone egress
  destination-zone core
  source-address address-set *********./32
  destination-address address-set ***********./32
  service TCP_8085
  service TCP_8087
  service http_8082
  action permit
 rule name T3_TO_UMP                      
  source-zone egress
  destination-zone core
  source-address address-set ********33.
  destination-address address-set ***********./24
  service TCP_18080
  action permit
 rule name "T1_To_************* 9100"
  source-zone core
  destination-zone egress
  source-address address-set ***********/32.
  destination-address address-set *************/32.
  service TCP9100
  action permit
 rule name XHW_**********_To_T1
  source-zone egress
  destination-zone core
  source-address address-set XWHT4_**********
  destination-address address-set ***********/32.
  service TCP_9090
  action permit
 rule name TO_104.23.11.4
  source-zone core
  destination-zone egress                 
  source-address address-set T2_T3_**********
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  source-address address-set **********/32.
  destination-address address-set ***********/24.
  service TCP_7001
  action permit
 rule name XWH_T3_to_keji_tiaobanji
  source-zone egress
  destination-zone core
  source-address address-set ********37.
  destination-address address-set ***********/24.
  service TCP_3389
  service icmp
  action permit
 rule name T3_*********                   
  source-zone egress
  destination-zone core
  source-address address-set *********/24.
  destination-address address-set *********/32.
  service TCP_34443
  action permit
 rule name T5_T3ELPSFTP
  source-zone core
  destination-zone egress
  source-address address-set ********/8.
  destination-address address-set T3_NAT_********
  destination-address address-set ***********/32.
  service TCP_34443
  service http
  service tcp_9080
  action permit
 rule name TO_10080
  source-zone core
  destination-zone egress
  source-address address-set CSLC_104.21.2.0/24
  destination-address address-set T3_NAT_********
  service TCP10080
  action permit                           
 rule name T3_***********_216
  source-zone egress
  destination-zone core
  source-address address-set *********.
  destination-address address-set **********/32.
  service TCP8022
  action permit
 rule name TO_*********
  source-zone egress
  destination-zone core
  source-address address-set T3_NAT_********
  destination-address address-set *********./32
  destination-address address-set ********/16.
  destination-address address-set *********/24.
  service TCP_7004
  service TCP_7005
  service tcp_7003
  action permit
 rule name TO_T3_USAP
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  destination-address address-set ********27./32
  service http
  action permit
 rule name youzhen
  source-zone core
  destination-zone egress
  source-address address-set ***********/32.
  source-address address-set ********/16.
  destination-address address-set ***********/24.
  service TCP_8428
  service https
  action permit
 rule name XWH_T3_T1
  source-zone egress
  destination-zone core
  source-address address-set *********./32
  source-address address-set XWT_T4_*********/32
  destination-address address-set T1_********/32
  service TCP/UDP111
  service TCP/UDP_2049
  service TCP/UDP_32768_65535
  action permit
 rule name bangongvpn_to_sslGateway
  source-zone core                        
  destination-zone egress
  source-address address-set ************/24.
  destination-address address-set **********/32.
  service https
  service icmp
  action permit
 rule name cslc_to_T3
  source-zone core
  destination-zone egress
  source-address address-set **************.
  destination-address address-set *********/32.
  destination-address address-set *********/32.
  destination-address address-set *********/32.
  destination-address address-set *********/32.
  service TCP_31306
  action permit
 rule name TEST_MATSERVER
  source-zone egress
  destination-zone core
  destination-address address-set *********./24
  action permit
 rule name SSL_TO_USAP
  source-zone core                        
  destination-zone egress
  source-address address-set *********./24
  destination-address address-set T3_NAT_********
  action permit
 rule name testxxxxxxx
  source-zone fez
  destination-zone core
  service TCP_69
  action permit
 rule name T1_XWHT4
  source-zone core
  destination-zone egress
  source-address address-set ********_8
  destination-address address-set *********//27
  destination-address address-set T4_18.5.97.0
  service http
  service http_8082
  action permit
 rule name to_G3
  source-zone core
  destination-zone egress
  source-address address-set (*********/24
  destination-address address-set *********/32.
  destination-address address-set **********.
  service TCP_28080
  service TCP28082
  action permit
 rule name ********83_NTP
  source-zone core
  destination-zone egress
  source-address address-set NTP_********83
  service ntp
  action permit
 rule name NTP_********83
  source-zone egress
  destination-zone core
  source-address address-set CSLC_**********/24
  destination-address address-set NTP_********83
  service ntp
  action permit
 rule name t1matserver_to_***********
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set ***********/32.
  service icmp                            
  service TCP8080
  action permit
 rule name T1ShouPiaoGongju
  source-zone core
  destination-zone egress
  source-address address-set kaifaceshitools_********0
  destination-address address-set T4_18.5.97.0
  service http
  action permit
 rule name RTQ_UMP
  source-zone egress
  destination-zone core
  source-address address-set RTQ_18.5.84.81_82
  destination-address address-set UMP_18.0.117.3
  service TCP_34443
  action permit
 rule name BASDB_ENCY
  source-zone core
  destination-zone egress
  source-address address-set BASDB_********5
  destination-address address-set *********/24_
  service TCP_8018
  action permit                           
 rule name T1_to_G3
  source-zone core
  destination-zone egress
  source-address address-set *********6_239
  source-address address-set *********6/32.
  source-address address-set *********7/32.
  source-address address-set *********8/32.
  source-address address-set *********9/32.
  destination-address address-set **********.
  destination-address address-set **********.
  destination-address address-set **********.
  service TCP_3555
  action permit
 rule name NTP_**********
  disable
  source-zone egress
  destination-zone core
  source-address address-set 18.5.85.11_12
  destination-address address-set NTP_**********
  service ntp
  action permit
 rule name T5_to_inter
  source-zone egress                      
  destination-zone core
  source-address address-set T2_T3_**********
  service http
  action permit
 rule name T1_104.23.1.0
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set ***********_25
  destination-address address-set CSLC_**********/24
  service TCP_3389
  service icmp
  action permit
 rule name T5MATserver
  source-zone core
  destination-zone egress
  source-address address-set ***********1.
  destination-address address-set ***********/32.
  action permit
 rule name to_104
  source-zone core
  destination-zone egress
  source-address address-set ********/24. 
  source-address address-set ********/8.
  destination-address address-set ***********/32.
  service TCP_21
  action permit
 rule name insie
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set ***********1.
  service tcp_7003
  service icmp
  action permit
 rule name ftp
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set T1_18.2.1.34/32
  destination-address address-set *********/32.
  service tcp_22
  action permit
 rule name TO_T1Apollo
  source-zone egress
  destination-zone core                   
  source-address address-set CSLC_**********/24
  destination-address address-set *********/24.
  service TCP_28070
  service TCP_28081
  action permit
 rule name ***********/216
  source-zone egress
  destination-zone core
  source-address address-set *********.
  destination-address address-set *********./24
  service tcp_5000_5008
  service tcp6370
  service tcp28090
  action permit
 rule name *************_to_31306
  source-zone egress
  destination-zone core
  source-address address-set *************/24.
  destination-address address-set ********/16.
  destination-address address-set ********/16.
  action permit
 rule name T5_L05
  source-zone core                        
  destination-zone egress
  source-address address-set *********.
  source-address address-set *********.
  source-address address-set ***********/32.
  destination-address address-set ************.
  service telnet
  service icmp
  service http
  action permit
 rule name T5_L05ump
  source-zone core
  destination-zone egress
  source-address address-set T3_38.2.1.0/24
  source-address address-set **********.
  destination-address address-set ***********/32.
  service TCP8080
  service icmp
  service telnet
  action permit
 rule name L05_T5
  source-zone egress
  destination-zone core
  source-address address-set ************.
  destination-address address-set *********.
  destination-address address-set *********.
  destination-address address-set ***********/32.
  service telnet
  service http_8082
  service icmp
  service tcp_22
  action permit
 rule name L05ump_T5
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set **********.
  service tcp_7003
  service icmp
  service telnet
  action permit
 rule name v316_T4
  source-zone egress
  destination-zone core
  source-address address-set **************/32.
  source-address address-set **************/32.
  destination-address address-set **********/32.
  destination-address address-set *********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  service TCP_3389
  action permit
 rule name to_***********_125
  source-zone core
  destination-zone egress
  source-address address-set ***********_228
  destination-address address-set ***********_125
  service TCP_3191
  service tcp_4191
  action permit
 rule name T4AD_T1AD
  source-zone core
  destination-zone egress
  source-address address-set ********.
  source-address address-set ********./32
  destination-address address-set *********.
  action permit
 rule name FTP
  source-zone egress
  destination-zone core                   
  destination-address address-set ************./32
  action permit
 rule name OCP_to_OB
  source-zone egress
  destination-zone core
  source-address address-set ************.
  destination-address address-set ***********.
  destination-address address-set ***********.
  destination-address address-set ***********.
  service tcp_22
  service tcp2881totcp2888
  action permit
 rule name T1_bangong10.248.255
  source-zone egress
  destination-zone core
  source-address address-set ********/24.
  destination-address address-set **************.
  action permit
 rule name TO_104.23.99.33
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/16. 
  destination-address address-set ***********../24
  service dns
  service TCP_2883
  action permit
 rule name to_kafka
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set ***********.
  service TCP_9092
  action permit
 rule name qudaoDB
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set ************.
  service TCP_2883
  action permit
 rule name tencent
  source-zone egress
  destination-zone core
  source-address address-set ************/24.
  destination-address address-set ********.
  service TCP8001_8020
  action permit
 rule name T1_To_Tengxunyun
  source-zone core
  destination-zone egress
  source-address address-set ********_8
  source-address address-set ********/32.
  source-address address-set ********0/32.
  destination-address address-set ************/24.
  service TCP_31582
  service tcp_30000
  action permit
 rule name TO_CSLC
  session logging
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set *************./32
  destination-address address-set **************./32
  destination-address address-set ***********./32
  service TCP_389
  service TCP_6006
  service TCP_3001                        
  action permit
 rule name T1_104.21_10.216
  source-zone core
  destination-zone egress
  source-address address-set 18.0.71.0_18.0.75.0
  destination-address address-set ************/24.
  destination-address address-set **********/24.
  service TCP_389
  service TCP_30001
  action permit
 rule name lousao
  source-zone core
  destination-zone egress
  source-address address-set ***************/32.
  source-address address-set **************/32.
  destination-address address-set *************/32.
  action permit
 rule name 赔率服务器
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set **********/32.
  destination-address address-set T2N1_**********
  destination-address address-set T5_************
  service tcp_52701
  service TCP_8080
  action permit
 rule name SDN_T1
  source-zone egress
  destination-zone core
  source-address address-set SDN_4.189.0.0/16
  destination-address address-set T1_NAT_78.0.0.0/16
  action permit
 rule name ************.
  source-zone core
  destination-zone egress
  source-address address-set ************.
  source-address address-set ************.
  source-address address-set ************.
  destination-address address-set **********.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  destination-address address-set *************/24.
  action permit
 rule name T1_104.21.19.
  source-zone core                        
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/24.
  destination-address address-set ***********/24.
  service TCP_20050
  service TCP_8080
  action permit
 rule name **********.
  source-zone egress
  destination-zone core
  source-address address-set *********6/32.
  source-address address-set **********/32.
  destination-address address-set ***********/32.
  destination-address address-set BG_**************
  service https
  service TCP_10280
  action permit
 rule name testuSAP
  source-zone egress
  destination-zone core
  destination-address address-set **********./32
  destination-address address-set **********./32
  action permit                           
 rule name T4_to_104.21/104.200
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/24.
  destination-address address-set *************/24.
  destination-address address-set ***********/24.
  action permit
 rule name NPM管理
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set ************./32
  service tcp_8088
  action permit
 rule name 140/141_to_coding_linshi
  source-zone core
  destination-zone egress
  source-address address-set 192.168.140/141.0
  destination-address address-set **********/24.
  action permit
 rule name coding_to_140/141_linshi
  source-zone egress                      
  destination-zone core
  source-address address-set **********/24.
  destination-address address-set 192.168.140/141.0
  action permit
 rule name 192.168.116_104
  source-zone core
  destination-zone egress
  source-address address-set *************/24.
  destination-address address-set ***********/24.
  service TCP_7001
  action permit
 rule name shentouceshi
  source-zone core
  destination-zone egress
  source-address address-set *************/32.
  destination-address address-set **********/32.
  action permit
 rule name 10.219.10_to_keji
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  destination-address address-set ************_56
  action permit                           
 rule name keji_10.219.10
  source-zone core
  destination-zone egress
  source-address address-set ************_56
  source-address address-set ************.
  destination-address address-set ***********_8
  action permit
 rule name ********_9_to_
  source-zone core
  destination-zone egress
  source-address address-set ********_8
  source-address address-set ********/32.
  destination-address address-set *********/32.
  service tcp_8088
  action permit
 rule name T518.0.160.
  source-zone core
  destination-zone egress
  source-address address-set T2_T3_**********
  destination-address address-set **********/24.
  destination-address address-set **********.
  service TCP_25
  service TCP_30200                       
  action permit
 rule name "CIDEV OPS"
  source-zone core
  destination-zone egress
  source-address address-set ********/32.
  source-address address-set ********0/32.
  destination-address address-set XWH_T4_***********/32
  service TCP_32600
  action permit
 rule name luyu_tiaobanji
  source-zone egress
  destination-zone core
  source-address address-set ************/24.
  destination-address address-set ***********/32.
  action permit
 rule name yunnodo_toBOS
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  destination-address address-set **********.
  service http
  action permit
 rule name T5_to_104caiwuSFTP             
  source-zone core
  destination-zone egress
  source-address address-set T2_T3_**********
  destination-address address-set ***********/24.
  destination-address address-set ***********/24.
  destination-address address-set ***********/24.
  service TCP_34443
  service ssh
  action permit
 rule name XL09(ssl)_to_transrouter
  source-zone egress
  destination-zone core
  source-address address-set *************/24.
  destination-address address-set ***********/32.
  service http_8082
  action permit
 rule name Autotest
  source-zone core
  destination-zone egress
  source-address address-set ********_10
  destination-address address-set ************/32.
  destination-address address-set tencent_10.215.0.10
  destination-address address-set CSLC_104.255.255.1
  destination-address address-set **********.
  service http
  service https
  service TCP_1443
  action permit
 rule name VDI_QC
  source-zone egress
  destination-zone core
  source-address address-set ************/32.
  source-address address-set 赵明薇VDI
  destination-address address-set qc_*************
  service TCP_1433
  action permit
 rule name T2_XXFBPT
  source-zone core
  destination-zone egress
  source-address address-set **********/24.
  destination-address address-set 信息发布中心
  service TCP_30200
  action permit
 rule name T1_BT
  source-zone core
  destination-zone egress                 
  source-address address-set BT_********1
  source-address address-set BT_********15
  destination-address address-set *************/24.
  service tcp_30000
  action permit
 rule name T4T5_USAP
  source-zone core
  destination-zone egress
  source-address address-set T3_38.2.1.0/24
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set T2_T3_**********
  source-address address-set **********.
  source-address address-set 18.0.92.10_13/*********0_30
  source-address address-set ********00/32.
  source-address address-set T2T4T5node
  source-address address-set 18.0.97.21_28
  source-address address-set **********1/32.
  source-address address-set *********_18
  source-address address-set ********/8.
  destination-address address-set USAP:***********
  service TCP9080
  service TCP_19080                       
  service http
  service https
  service icmp
  action permit
 rule name T1_xietongVDI
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  destination-address address-set **********/24.
  action permit
 rule name yunwei_zabbix
  source-zone core
  destination-zone egress
  source-address address-set ***********/32.
  destination-address address-set ***********/32.
  service TCP31051
  action permit
 rule name yunwei_zabbix1
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set ***********/32.
  service TCP31050_31051                  
  action permit
 rule name SDN_toT1
  source-zone egress
  destination-zone core
  source-address address-set 4.189.0.1_10
  destination-address address-set ********/24.
  service http
  service ssh
  service https
  service TCP_3389
  service TCP_8000_8010
  action permit
 rule name anquansaomiao
  source-zone egress
  destination-zone core
  source-address address-set ************/32.
  action permit
 rule name T1_*********13
  source-zone core
  destination-zone egress
  source-address address-set *********13/32.
  destination-address address-set ************/32.
  destination-address address-set *************/32.
  destination-address address-set USAP:***********
  destination-address address-set ***************/32.
  destination-address address-set ************.
  destination-address address-set **********/24.
  destination-address address-set ************/32.
  service TCP_30010
  service https
  service TCP_2883
  service tcp_30000
  service TCP9080
  service tcp_1521
  service TCP_19080
  action permit
 rule name yukong_T2VDI
  source-zone egress
  destination-zone core
  source-address address-set *********.
  destination-address address-set **************.
  destination-address address-set **************.
  action permit
 rule name T2VDI_to_yukong
  source-zone core
  destination-zone egress                 
  source-address address-set **************.
  source-address address-set **************.
  destination-address address-set *********.
  destination-address address-set **********.
  destination-address address-set USAP:***********
  action permit
 rule name anquanshengtou_6/30
  disable
  source-zone egress
  destination-zone core
  source-address address-set ***********/32.
  destination-address address-set **********/32.
  destination-address address-set **********/32.
  service TCP_8866
  action permit
 rule name anquanshentou
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set **********.
  service TCP_1443
  action permit
 rule name sjj                            
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set *************/32.
  service TCP_10014
  action permit
 rule name BT_coding
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set ********/24.
  source-address address-set T3_38.2.1.0/24
  destination-address address-set **********/32_coding
  service http
  action permit
 rule name guoce_T5
  source-zone egress
  destination-zone core
  source-address address-set *************/32.
  destination-address address-set *********/32.
  service TCP_8899
  action permit                           
 rule name AGENT_to_BOSROUTER
  source-zone core
  destination-zone egress
  source-address address-set ********_10
  destination-address address-set **********_62
  service http_8082
  action permit
 rule name xiaofu
  source-zone core
  destination-zone egress
  source-address address-set *********13/32.
  source-address address-set *********14.
  destination-address address-set ***********/32.
  service TCP_30002
  action permit
 rule name 全业务链环境
  source-zone core
  destination-zone egress
  source-address address-set *********/32.
  destination-address address-set **************/32.
  service tcp_30000
  action permit
 rule name YZ_VDI1                        
  source-zone egress
  destination-zone core
  source-address address-set ************/24.
  destination-address address-set **********/24.
  destination-address address-set T2_T3_**********
  destination-address address-set **********.
  service ssh
  action permit
 rule name YZVD_TO_38
  source-zone egress
  destination-zone core
  source-address address-set ************/24.
  destination-address address-set T2_T3_**********
  destination-address address-set 38_0_1_0/24
  service TCP_8086
  service TCP_28080
  action permit
 rule name YZ_VDI2
  source-zone egress
  destination-zone core
  source-address address-set ************/24.
  destination-address address-set ********0/32.
  destination-address address-set T3_*********/32
  destination-address address-set *********/32.
  service TCP_28080
  action permit
 rule name tenxunyun_to_t5
  source-zone egress
  destination-zone core
  source-address address-set ***********/24.
  destination-address address-set ***********/32.
  service http_8082
  action permit
 rule name "harbor 中转机"
  source-zone egress
  destination-zone core
  source-address address-set ************.
  destination-address address-set 28.0.1.201_
  service https
  service http
  action permit
 rule name 安全魏宏安全月临时开放
  policy logging
  session logging
  source-zone egress
  destination-zone core                   
  source-address address-set 10_211_4_149
  source-address address-set 10_211_6_57
  destination-address address-set 18_0_4_75
  destination-address address-set 18_4_0_75
  destination-address address-set 18_6_32_38
  action permit
 rule name 174_T1_To_10_216
  source-zone core
  destination-zone egress
  source-address address-set 18_0_95_20_121
  source-address address-set 28_0_0_0/8
  destination-address address-set 10_216_5_4
  destination-address address-set 104_22_9_42
  action permit
 rule name VC管理
  source-zone core
  destination-zone egress
  source-address address-set 18_0_1_248
  source-address address-set **********./32
  destination-address address-set CSLC_104.255.255.1
  action permit
 rule name moniyunying
  policy logging                          
  session logging
  source-zone egress
  destination-zone core
  source-address address-set 10.0.0.0/8.
  destination-address address-set ********/16.
  action permit
 rule name 18_0to18.5.98
  source-zone core
  destination-zone egress
  source-address address-set ********/8_
  destination-address address-set *********/24_
  action permit
 rule name 10&104to38any
  source-zone egress
  destination-zone core
  source-address address-set *********/8.
  source-address address-set 10.0.0.0/8.
  source-address address-set ********/8_
  destination-address address-set ********/8.
  destination-address address-set ********/8.
  destination-address address-set 18_6_32_0/24
  destination-address address-set ********/8_
  action permit                           
 rule name 安全服务产品线
  source-zone core
  destination-zone egress
  source-address address-set ***************/32.
  destination-address address-set ************/32.
  service TCP_8080
  action permit
 rule name PKICA
  source-zone core
  destination-zone egress
  source-address address-set *********_
  source-address address-set **********/32.
  destination-address address-set 18.6.203.1_18、44
  action permit
 rule name "VCMGMTTOOLS TO VC10.252.254.202"
  source-zone egress
  destination-zone core
  source-address address-set 104.255.225.45_
  source-address address-set 104.21.2.64_1
  destination-address address-set VC18.252.254.202
  action permit
 rule name 20230926李洋需求
  source-zone core                        
  destination-zone egress
  source-address address-set ***********_30
  destination-address address-set **********.
  service tcp:20443
  action permit
#
auth-policy
#
traffic-policy
#
policy-based-route
#
nat-policy
 rule name 王轩跳板机
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set ***********.
  destination-address address-set **********/16.
  action nat address-group NAT_*********5
 rule name TO_************
  source-zone core
  destination-zone egress                 
  source-address address-set **********.
  destination-address address-set ************.
  service TCP_8086
  action nat address-group NAT_*********
 rule name TO_************
  source-zone core
  destination-zone egress
  source-address address-set *********.
  destination-address address-set ************.
  service TCP_8086
  action nat address-group *********
 rule name 阿里云测试
  source-zone core
  destination-zone egress
  source-address address-set 阿里云测试**********
  source-address address-set *********/24.
  source-address address-set ********/16.
  destination-address address-set 阿里云测试地址***********
  destination-address address-set *********/16.
  destination-address address-set *********/16.
  action nat address-group NAT_*********
 rule name 封浩亮测试访问G32财务中心SFTP
  source-zone core                        
  destination-zone egress
  source-address address-set **********.
  destination-address address-set **********.
  action nat address-group NAT*********3
 rule name 赵树军测试
  source-zone core
  destination-zone egress
  source-address address-set ********73_0518
  destination-address address-set 172.16.0.1_2_0518
  action nat address-group NAT_*********
 rule name 安全扫描魏宏
  description **********-223
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set **********.
  action nat address-group NAT_*********1
 rule name ***********
  source-zone core
  destination-zone egress
  destination-address address-set 10.213.3.60_
  action nat address-group NAT_**********
 rule name **********                     
  source-zone core
  destination-zone egress
  source-address address-set **********_
  destination-address address-set 3.0.0.0_
  action nat address-group **************
 rule name **********0
  source-zone core
  destination-zone egress
  source-address address-set NTP_**********
  source-address address-set **********.
  destination-address address-set **********0.
  action nat address-group NAT_*********
 rule name TO_*********
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set *********./32
  action nat address-group SANT_*********
 rule name To_*********
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set *********./32
  action nat address-group SANT_*********
 rule name TO_**********
  source-zone core
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set **********8_99
  action nat address-group **********20
 rule name TO_USAP_**********
  source-zone core
  destination-zone egress
  source-address address-set *********.
  destination-address address-set T3_NAT_********
  action nat address-group SNAT*********
 rule name TO_USAP_**********
  source-zone core
  destination-zone egress
  source-address address-set *********./32
  destination-address address-set T3_NAT_********
  action nat address-group SNAT_*********
 rule name TO_18.6.4.48
  source-zone core
  destination-zone egress
  source-address address-set ********/16. 
  destination-address address-set NFS_18.6.4.48/32
  action nat address-group SANT_*********
 rule name wangxuan_to_tencent_cloud
  source-zone core
  destination-zone egress
  source-address address-set **********./32
  source-address address-set zhiqi_************
  source-address address-set ************./32
  source-address address-set ************.
  source-address address-set ************./32
  source-address address-set **********.
  source-address address-set ********_10
  source-address address-set 18.2.1.2_4
  destination-address address-set tencent_10.215.0.10
  destination-address address-set ***********/24.
  destination-address address-set 10.0.0.0/8.
  destination-address address-set *********/8.
  destination-address address-set **********/24.
  destination-address address-set **********/16.
  action nat address-group NAT_*********4
 rule name ********_to_tencent_cloud
  source-zone core
  destination-zone egress                 
  source-address address-set ********/24.
  destination-address address-set ***********/24.
  destination-address address-set tencent_10.215.0.10
  action nat address-group NAT_*********4
 rule name 自动化平台
  source-zone core
  destination-zone egress
  source-address address-set *********12_214
  source-address address-set ********_10
  source-address address-set **********.
  source-address address-set **********/32.
  destination-address address-set ***********.
  destination-address address-set **********4.
  destination-address address-set ***********.
  destination-address address-set ***********_33
  destination-address address-set ***********_42
  destination-address address-set ************/32.
  destination-address address-set USAP_104.22.9.22
  destination-address address-set USAP:***********
  destination-address address-set *************/24.
  destination-address address-set ************.
  destination-address address-set **********/24.
  destination-address address-set *************/32.
  destination-address address-set ************/32.
  destination-address address-set **********/24.
  action nat address-group NAT_*********
 rule name 38.0.160TO10.212
  source-zone core
  destination-zone egress
  source-address address-set T2_T3_**********
  source-address address-set T3_38.2.1.0/24
  source-address address-set ********/24.
  source-address address-set T3_********/24
  source-address address-set **********/32.
  source-address address-set ********/24.
  source-address address-set ********/8_
  source-address address-set ********/24_
  source-address address-set ********/8_
  destination-address address-set **********/24.
  destination-address address-set ***********/24.
  destination-address address-set *********/8.
  destination-address address-set **********/24.
  action nat address-group NAT_*********
 rule name T5_RTQ_UMP
  source-zone core
  destination-zone egress                 
  source-address address-set RTQDB_***********
  destination-address address-set ***********/32.
  action nat address-group NAT_*********
 rule name 180.11.237to104&10
  source-zone core
  destination-zone egress
  source-address address-set ***********/32.
  source-address address-set 18.0.99.99_100
  source-address address-set 18_0_95_0/24
  destination-address address-set 10.0.0.0/8.
  destination-address address-set 104_0_0_0_8
  action nat address-group NAT_*********4
 rule name **********
  source-zone core
  destination-zone egress
  source-address address-set **********.
  source-address address-set BT_********1
  source-address address-set BT_********15
  destination-address address-set *********/8.
  action nat address-group *********
 rule name T2_********/8
  source-zone core
  destination-zone egress                 
  source-address address-set ***********/32.
  source-address address-set ***********/32.
  source-address address-set ********/24.
  source-address address-set **********/32.
  source-address address-set 28_0_1_0/24
  destination-address address-set *********/8.
  action nat address-group T2_NAT_*********
 rule name T1_********/24
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set 18.0.92.10_13/*********0_30
  source-address address-set 18.0.97.21_28
  source-address address-set **********1/32.
  source-address address-set ********/24.
  source-address address-set ********/24.
  destination-address address-set *********/8.
  action nat address-group NAT_*********
 rule name T4
  source-zone core
  destination-zone egress
  source-address address-set ********/24.
  source-address address-set ***********. 
  source-address address-set **********.
  source-address address-set 48.0.7.100/32.
  source-address address-set ********/8.
  destination-address address-set *********/8.
  destination-address address-set **********/24.
  destination-address address-set **********/24.
  action nat address-group T4_*********
 rule name lishuaiqi
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set **********/16.
  destination-address address-set 10.213.0.0/16.
  destination-address address-set **********/16.
  action nat address-group T5_NAT_*********
 rule name bangong
  source-zone core
  destination-zone egress
  source-address address-set ***************.
  destination-address address-set ***********/24.
  action nat address-group NAT_*********
 rule name OPS_GuoCe
  source-zone core                        
  destination-zone egress
  source-address address-set ********/16.
  destination-address address-set Guo2_Monitor_10.213.8.158
  destination-address address-set **********/16.
  destination-address address-set 172.16.0.1_2_0518
  action nat address-group NAT_*********
 rule name T2_GC
  source-zone core
  destination-zone egress
  source-address address-set **************.
  source-address address-set **************.
  destination-address address-set *********/8.
  action nat address-group T2_NAT_*********
 rule name T2_XXFBPT
  source-zone core
  destination-zone egress
  source-address address-set **********/24.
  destination-address address-set **********/24.
  action nat address-group T1_NAT_*********
 rule name SDAS_USAP
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set F5_********50
  action nat address-group NAT_*********
 rule name T1_xietong
  source-zone core
  destination-zone egress
  source-address address-set *********/24.
  destination-address address-set **********/24.
  action nat address-group NAT_*********
 rule name *************
  source-zone core
  destination-zone egress
  source-address address-set *************/24.
  destination-address address-set USAP:***********
  action nat address-group NAT_*********
 rule name T2_********/8_TO_10
  source-zone core
  destination-zone egress
  source-address address-set 28_0_1_0/24
  destination-address address-set 10.0.0.0/8.
  action nat address-group T2_NAT_*********
 rule name 174_T1_T2_to_104_10
  source-zone core
  destination-zone egress                 
  source-address address-set T1_*********/24
  source-address address-set ********/8.
  destination-address address-set **********/24.
  destination-address address-set USAP:***********
  action nat address-group T2_NAT_*********
 rule name egwto10.218
  source-zone egw
  destination-zone egress
  source-address address-set ********/24.
  destination-address address-set 10_218_0_0/16
  action nat address-group T1_NAT_*********
 rule name 安全PKI终端**********
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set *************/32.
  destination-address address-set **************/32.
  action nat address-group T1_NAT_*********
 rule name 安全服务产品线
  source-zone core
  destination-zone egress
  source-address address-set ***************/32.
  destination-address address-set ************/32.
  service TCP_8080
  action nat address-group NAT_*********
 rule name ***********
  source-zone core
  destination-zone egress
  source-address address-set ***********_
  action nat address-group NAT_*********
 rule name PKICA
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set 18.6.203.1_18、44
  action nat address-group NAT_*********
 rule name 王轩T1环境出公网
  source-zone core
  destination-zone egress
  source-address address-set **********.  
  action nat address-group NAT_*********4
 rule name 电子安全认证服务pki/ca系统测试环
  source-zone core
  destination-zone egress
  source-address address-set **********/32.
  destination-address address-set ************&2
  action nat address-group NAT_*********4
 rule name 云下访问云上交易
  source-zone core
  destination-zone egress
  source-address address-set **********.
  destination-address address-set ************.
  action nat address-group NAT_*********4
#
proxy-policy
#
quota-policy
#
 multi-interface
#
return

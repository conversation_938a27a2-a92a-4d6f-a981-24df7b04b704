{"default": [{"rule_name": "HA_local_check", "action": "permit", "source_zone": "HA,local", "source_ip": "any", "destination_ip": "any", "destination_zone": "HA,local", "service": "icmp,protocol"}, {"rule_name": "permit_icmp", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "icmp"}, {"rule_name": "permit_ntp", "action": "permit", "source_zone": "dmz,local,trust", "source_ip": "any", "destination_ip": "*******", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "permit_dns", "action": "permit", "source_zone": "dmz,local,trust", "source_ip": "any", "destination_ip": "**********", "destination_zone": "untrust", "service": "dns,dns-tcp"}, {"rule_name": "permit_gtmToDns", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "*********** ***********", "destination_zone": "trust", "service": "dns,dns-tcp"}, {"rule_name": "20231130_ocsToAliyunAso", "action": "permit", "source_zone": "untrust", "source_ip": "*********** ************", "destination_ip": "***********", "destination_zone": "trust", "service": "https"}, {"rule_name": "20231130_ocsToAliyunAscm", "action": "permit", "source_zone": "untrust", "source_ip": "*********** ************", "destination_ip": "***********", "destination_zone": "trust", "service": "https"}, {"rule_name": "20231130_ocsToAliyunTianji", "action": "permit", "source_zone": "untrust", "source_ip": "*********** ************", "destination_ip": "***********,************", "destination_zone": "trust", "service": "https"}, {"rule_name": "20231130_ocsToAliyunEdas", "action": "permit", "source_zone": "untrust", "source_ip": "*********** ************", "destination_ip": "*************", "destination_zone": "trust", "service": "https"}, {"rule_name": "20231130_ocsToAliyunManage", "action": "permit", "source_zone": "untrust", "source_ip": "*********** ************", "destination_ip": "*********", "destination_zone": "trust", "service": "ftp,rdp-tcp,ssh"}, {"rule_name": "20231130_EccToBaoLeiJi", "action": "permit", "source_zone": "untrust", "source_ip": "********,*********,*********,*********", "destination_ip": "************ ************", "destination_zone": "trust", "service": "https,tcp_5900,tcp_60021,tcp_60022,tcp_63306,tcp_63389"}, {"rule_name": "20231130_scannerToAliyun", "action": "permit", "source_zone": "untrust", "source_ip": "************", "destination_ip": "any", "destination_zone": "dmz,local,trust", "service": "any"}, {"rule_name": "20231130_aliyunToSOC", "action": "permit", "source_zone": "dmz,local,trust", "source_ip": "any", "destination_ip": "***********", "destination_zone": "trust", "service": "syslog"}, {"rule_name": "20231130_baoLeiJiToYuKong", "action": "permit", "source_zone": "trust", "source_ip": "************ ************", "destination_ip": "******** ********", "destination_zone": "untrust", "service": "tcp_389,tcp_636"}, {"rule_name": "20231130_baoLeiJiToShuangYinSu", "action": "permit", "source_zone": "trust", "source_ip": "************ ************", "destination_ip": "********** **********", "destination_zone": "untrust", "service": "http,radius"}, {"rule_name": "20231130_AOPSToAliyun", "action": "permit", "source_zone": "untrust", "source_ip": "************ ************", "destination_ip": "************,************,************,************,************,************", "destination_zone": "trust", "service": "http,https"}, {"rule_name": "20231130_monitorToAliyun", "action": "permit", "source_zone": "untrust", "source_ip": "***********,************ ************", "destination_ip": "************,************", "destination_zone": "trust", "service": "http,https"}, {"rule_name": "20231130_monitorToKafka", "action": "permit", "source_zone": "untrust", "source_ip": "***********", "destination_ip": "************* *************", "destination_zone": "trust", "service": "tcp_8080"}, {"rule_name": "20231130_monitorToRedis", "action": "permit", "source_zone": "untrust", "source_ip": "***********", "destination_ip": "any", "destination_zone": "trust", "service": "tcp_6379"}, {"rule_name": "20231130_monitorToKFPT", "action": "permit", "source_zone": "untrust", "source_ip": "***********", "destination_ip": "***********,***********", "destination_zone": "trust", "service": "http"}, {"rule_name": "20231130_aliyunToMonitor", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********", "destination_zone": "untrust", "service": "http,tcp_30033,tcp_30040,tcp_30897,tcp_9897"}, {"rule_name": "20231207_nginxToKFTP", "action": "permit", "source_zone": "untrust", "source_ip": "*********** ***********,*********** ***********,*********** ***********", "destination_ip": "***********,***********", "destination_zone": "trust", "service": "http"}, {"rule_name": "20231207_usapToKFTP", "action": "permit", "source_zone": "untrust", "source_ip": "*********** ***********,*********** ***********", "destination_ip": "***********,***********", "destination_zone": "trust", "service": "http"}, {"rule_name": "20231207_KFTPToUSAP", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "***********", "destination_zone": "untrust", "service": "tcp_19080"}, {"rule_name": "20231207_KFTPToSec", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*********", "destination_zone": "untrust", "service": "tcp_10006"}, {"rule_name": "20231207_KFTPToDmp", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "**********", "destination_zone": "untrust", "service": "tcp_8080"}, {"rule_name": "20231225_KFPTtoFUWU", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "************,*************,*************,*************,*************,*************,*************,*************,************,**********,***********,***********,***********,************,**********,**********,**********,********,**********,***********,**********,***********,4.24.11.70,4.24.11.90,4.27.41.210,4.27.41.90,4.28.10.16,**********,4.60.12.100,4.60.12.70,4.60.8.205", "destination_zone": "untrust", "service": "tcp_10080,tcp_18080,tcp_28080,tcp_30000,tcp_30001,tcp_30011,tcp_30200,tcp_30201,tcp_30202,tcp_30203,tcp_30204,tcp_31000,tcp_32020,tcp_32201,tcp_32202,tcp_80,tcp_8080,tcp_8081,tcp_8082,tcp_8083,tcp_8085,tcp_8087,tcp_8330,tcp_8989"}, {"rule_name": "20231130_monitorToBaoLeiJi", "action": "permit", "source_zone": "untrust", "source_ip": "3.254.209.0", "destination_ip": "10.89.225.11 ************", "destination_zone": "trust", "service": "https"}]}
{"default": [{"name": "tcp_10006", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10006"}]}, {"name": "tcp_10080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10080"}]}, {"name": "tcp_18080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18080"}]}, {"name": "tcp_19080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19080"}]}, {"name": "tcp_28080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28080"}]}, {"name": "tcp_30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}, {"name": "tcp_30001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30001"}]}, {"name": "tcp_30011", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30011"}]}, {"name": "tcp_30033", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30033"}]}, {"name": "tcp_30040", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30040"}]}, {"name": "tcp_30200", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30200"}]}, {"name": "tcp_30201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30201"}]}, {"name": "tcp_30202", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30202"}]}, {"name": "tcp_30203", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30203"}]}, {"name": "tcp_30204", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30204"}]}, {"name": "tcp_30897", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30897"}]}, {"name": "tcp_31000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31000"}]}, {"name": "tcp_32020", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32020"}]}, {"name": "tcp_32201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32201"}]}, {"name": "tcp_32202", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32202"}]}, {"name": "tcp_389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "389"}]}, {"name": "tcp_5900", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5900"}]}, {"name": "tcp_60021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "60021"}]}, {"name": "tcp_60022", "type": "object", "service": [{"protocol": "tcp", "destination-port": "60022"}]}, {"name": "tcp_63306", "type": "object", "service": [{"protocol": "tcp", "destination-port": "63306"}]}, {"name": "tcp_63389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "63389"}]}, {"name": "tcp_636", "type": "object", "service": [{"protocol": "tcp", "destination-port": "636"}]}, {"name": "tcp_6379", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6379"}]}, {"name": "tcp_80", "type": "object", "service": [{"protocol": "tcp", "destination-port": "80"}]}, {"name": "tcp_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "tcp_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "tcp_8082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8082"}]}, {"name": "tcp_8083", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8083"}]}, {"name": "tcp_8085", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8085"}]}, {"name": "tcp_8087", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8087"}]}, {"name": "tcp_8330", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8330"}]}, {"name": "tcp_8989", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8989"}]}, {"name": "tcp_9897", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9897"}]}]}
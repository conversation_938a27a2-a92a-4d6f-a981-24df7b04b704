{"default": [], "A_Core": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-8088", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8088"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "NAS", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}, {"protocol": "tcp", "destination-port": "4049"}, {"protocol": "udp", "destination-port": "4049"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "A_Normal": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "tcp-30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_34443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "34443"}]}, {"name": "TCP_18081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18081"}]}, {"name": "TCP_7001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7001"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP-4422", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4422"}]}, {"name": "NAS", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}, {"protocol": "tcp", "destination-port": "4049"}, {"protocol": "udp", "destination-port": "4049"}]}, {"name": "TCP_30202", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30202"}]}, {"name": "TCP_20006", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20006"}]}, {"name": "TCP_20110", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20110"}]}, {"name": "TCP_30601", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30601"}]}, {"name": "TCP_443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "443"}]}, {"name": "TCP_6006", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6006"}]}, {"name": "TCP_8931", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8931"}]}, {"name": "TCP_1521", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1521"}]}, {"name": "TCP_30010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30010"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_32600", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32600"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_8989", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8989"}]}], "A_Others": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_8000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "389"}]}, {"name": "udp-389", "type": "object", "service": [{"protocol": "udp", "destination-port": "389"}]}, {"name": "tcp-636", "type": "object", "service": [{"protocol": "tcp", "destination-port": "636"}]}, {"name": "tcp-3268", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3268"}]}, {"name": "tcp-3269", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3269"}]}, {"name": "tcp-5722", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5722"}]}, {"name": "464", "type": "object", "service": [{"protocol": "tcp", "destination-port": "464"}, {"protocol": "udp", "destination-port": "464"}]}, {"name": "tcp-9389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9389"}]}, {"name": "tcp-2535", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2535"}]}, {"name": "udp-2535", "type": "object", "service": [{"protocol": "udp", "destination-port": "2535"}]}, {"name": "49152-65535", "type": "object", "service": [{"protocol": "tcp", "destination-port": "65535"}, {"protocol": "udp", "destination-port": "65535"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP-10102", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10102"}]}, {"name": "TCP-5601", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5601"}]}, {"name": "TCP-4451", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4451"}]}, {"name": "TCP_25601", "type": "object", "service": [{"protocol": "tcp", "destination-port": "25601"}]}, {"name": "TCP_30900", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30900"}]}, {"name": "TCP_3000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3000"}]}, {"name": "TCP_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "TCP_18081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18081"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_8880", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8880"}]}, {"name": "TCP_8889", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8889"}]}, {"name": "TCP_8400", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8400"}]}, {"name": "TCP_30038", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30038"}]}, {"name": "TCP_30039", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30039"}]}, {"name": "tcp_19090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19090"}]}, {"name": "TCP_10000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10000"}]}, {"name": "SecOCS-PORT", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}], "B_Core": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_5080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5080"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_8330", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8330"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_19080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19080"}]}, {"name": "TCP_9080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9080"}]}, {"name": "TCP_30010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30010"}]}, {"name": "TCP_54102", "type": "object", "service": [{"protocol": "tcp", "destination-port": "54102"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_19091", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19091"}]}, {"name": "TCP_19092", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19092"}]}, {"name": "TCP_19093", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19093"}]}, {"name": "TCP_19094", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19094"}]}, {"name": "TCP_19095", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19095"}]}, {"name": "TCP_31001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31001"}]}], "B_Normal": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_34443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "34443"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "tcp-30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}], "B_Others": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "B_SGW": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_5080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5080"}]}, {"name": "TCP_18081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18081"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_7001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7001"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_8330", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8330"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_19080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19080"}]}, {"name": "TCP_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "TCP_7443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7443"}]}, {"name": "TCP_32600", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32600"}]}, {"name": "TCP_UDP_389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "389"}, {"protocol": "udp", "destination-port": "389"}]}, {"name": "TCP_19091", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19091"}]}, {"name": "TCP_19092", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19092"}]}, {"name": "TCP_19093", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19093"}]}, {"name": "TCP_19094", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19094"}]}, {"name": "TCP_19095", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19095"}]}], "CSLJC": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP_8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP_8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_52701", "type": "object", "service": [{"protocol": "tcp", "destination-port": "52701"}]}], "B_ELB": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_28601", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28601"}]}, {"name": "TCP_8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP_8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP_8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "SGW": []}
{"default": [{"rule_name": "ha", "action": "permit", "source_zone": "dmz,local", "source_ip": "any", "destination_ip": "any", "destination_zone": "dmz,local", "service": "any"}, {"rule_name": "ntp", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "*******", "destination_zone": "any", "service": "ntp"}, {"rule_name": "trap syslog", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "jiankong", "destination_zone": "any", "service": "snmptrap,syslog"}, {"rule_name": "snmp", "action": "permit", "source_zone": "any", "source_ip": "jiankong", "destination_ip": "any", "destination_zone": "any", "service": "snmp"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "radius", "action": "permit", "source_zone": "local", "source_ip": "any", "destination_ip": "**********", "destination_zone": "trust", "service": "icmp,radius"}], "A_Core": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050,snmptrap,syslog"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "drop", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "any"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "B1  source-zone untrust", "action": "permit", "source_zone": "any", "source_ip": "**********-3,*********-3,*********-22,*********-22", "destination_ip": "***********-12,***********-22", "destination_zone": "trust", "service": "ssh,tcp-8088"}, {"rule_name": "CSLP-PY-TO-SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********,**********", "destination_ip": "***********-12", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "CSLP-BH-TO-SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********-44,**********-47", "destination_ip": "***********-22", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "BuHaoZhongDuan_to_SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********-22", "destination_ip": "***********-22", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "PenYinServer_to_SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "***********-12", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "YY-OCS_TO_SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "************,***********", "destination_ip": "***********-12,***********-22", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "***********-12", "destination_ip": "************-202", "destination_zone": "untrust", "service": "NAS,icmp"}, {"rule_name": "NAS-", "action": "permit", "source_zone": "untrust", "source_ip": "************-202", "destination_ip": "***********-12", "destination_zone": "trust", "service": "NAS,icmp"}, {"rule_name": "radius", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "**********", "destination_zone": "untrust", "service": "udp-1812"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "A_Normal": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050,snmptrap,syslog"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "ZFK8S to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "************/24,*********/24,********/24,host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "tcp-30000"}, {"rule_name": "SFTP500WAN", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "*********/27", "destination_zone": "untrust", "service": "ssh"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Shu<PERSON>u<PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100,host_4.9.5.100", "destination_ip": "host_4.101.50.1", "destination_zone": "trust", "service": "TCP_34443"}, {"rule_name": "JenkisTo1050", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "**********/24", "destination_zone": "trust", "service": "TCP_34443,ssh"}, {"rule_name": "JenkisTo1051", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "**********/24", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_34443"}, {"rule_name": "nginxSFTP", "action": "permit", "source_zone": "trust", "source_ip": "**********-2", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_34443"}, {"rule_name": "SJJM-Nginx", "action": "permit", "source_zone": "untrust", "source_ip": "**********-12", "destination_ip": "***********", "destination_zone": "trust", "service": "http"}, {"rule_name": "SJJM_NG_T0_SGW", "action": "permit", "source_zone": "trust", "source_ip": "***********", "destination_ip": "Host_4.101.9.100", "destination_zone": "untrust", "service": "TCP_18081"}, {"rule_name": "SGW_to_KFPT-openapi", "action": "permit", "source_zone": "untrust", "source_ip": "Range_4.254.127.51-55,SGW_*********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_7001"}, {"rule_name": "nginx_to_apiserver", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_7001"}, {"rule_name": "SGW_to_CodingNG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "trust", "service": "http"}, {"rule_name": "CodingNG_to_app", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "YYYW_to_CodingNG", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-4422"}, {"rule_name": "CodingNG_to_TEST-Coding", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP-4422"}, {"rule_name": "HainanDiantou to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********-65,host_4.98.46.100,host_4.98.46.11,host_4.98.46.21", "destination_ip": "***********", "destination_zone": "trust", "service": "tcp-30000"}, {"rule_name": "500wSFTP", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "***********", "destination_zone": "trust", "service": "tcp-30000"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "************-202", "destination_ip": "***********-12", "destination_zone": "untrust", "service": "NAS,icmp"}, {"rule_name": "NAS-", "action": "permit", "source_zone": "untrust", "source_ip": "***********-12", "destination_ip": "************-202", "destination_zone": "trust", "service": "NAS,icmp"}, {"rule_name": "XXFB_to_KFPTng", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_30202"}, {"rule_name": "KFPTng_to_YeZhuDuan", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_20006"}, {"rule_name": "KFPT-NG_to_YeZhuDuan", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_20110"}, {"rule_name": "DZTZ_TO_KFPT-NG", "action": "permit", "source_zone": "untrust", "source_ip": "net_10.194.101-105", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_30601"}, {"rule_name": "KFPT-NG_to_clb-pt-oap", "action": "permit", "source_zone": "trust", "source_ip": "KFPT_4.101.52.21-22", "destination_ip": "G3_10.194.119.8", "destination_zone": "untrust", "service": "http"}, {"rule_name": "G3BOSROUTER_TO_NAS", "action": "permit", "source_zone": "untrust", "source_ip": "Range_4.103.120.41-42", "destination_ip": "************-202", "destination_zone": "trust", "service": "NAS,icmp"}, {"rule_name": "HaiNai_To_JianGuan-1", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.20.26.122,host_4.98.46.100,host_4.98.46.11,host_4.98.46.21", "destination_ip": "host_4.101.51.10", "destination_zone": "trust", "service": "TCP_443"}, {"rule_name": "JianGuan_To_HaiNai-1", "action": "permit", "source_zone": "trust", "source_ip": "host_4.101.51.20", "destination_ip": "host_4.98.46.200", "destination_zone": "untrust", "service": "TCP_6006,TCP_8931"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>_To_Iner-1", "action": "permit", "source_zone": "untrust", "source_ip": "host_***********", "destination_ip": "host_4.101.51.20", "destination_zone": "trust", "service": "TCP_6006,TCP_8931"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>_To_Iner-2", "action": "permit", "source_zone": "trust", "source_ip": "host_4.101.51.10,host_4.101.51.20", "destination_ip": "host_4.98.46.200", "destination_zone": "untrust", "service": "TCP_6006,TCP_8931"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>_To_Iner-3", "action": "permit", "source_zone": "trust", "source_ip": "host_4.101.51.10,host_4.101.51.20", "destination_ip": "**********-12,host_4.20.26.1", "destination_zone": "untrust", "service": "TCP_1521"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>_To_HaiNai-2", "action": "permit", "source_zone": "trust", "source_ip": "host_4.101.51.10", "destination_ip": "host_4.98.46.100,host_4.98.46.11,host_4.98.46.21", "destination_zone": "untrust", "service": "TCP_443"}, {"rule_name": "HaiNai_To_SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********-55,**********-72,host_***********,host_4.98.46.100,host_4.98.46.11,host_4.98.46.21", "destination_ip": "host_4.101.50.1", "destination_zone": "trust", "service": "TCP_34443"}, {"rule_name": "UMP_To_JianGuan", "action": "permit", "source_zone": "untrust", "source_ip": "**********-34", "destination_ip": "host_4.101.51.10", "destination_zone": "trust", "service": "TCP_30010"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>_Nginx", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.20.26.122,host_***********", "destination_ip": "host_4.101.51.10,host_4.101.51.20", "destination_zone": "trust", "service": "TCP_30010"}, {"rule_name": "radius", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "**********", "destination_zone": "untrust", "service": "udp-1812"}, {"rule_name": "aopsnginx_to_aops", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************-38", "destination_zone": "untrust", "service": "TCP_32600,TCP_8080,http"}, {"rule_name": "SGW_to_aopsnginx", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_32600,http"}, {"rule_name": "ecc_to_aopsnginx", "action": "permit", "source_zone": "untrust", "source_ip": "*********/24", "destination_ip": "any", "destination_zone": "trust", "service": "http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "YunYing to SFTP", "action": "drop", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "any"}, {"rule_name": "aops_to_aops_nginx", "action": "permit", "source_zone": "untrust", "source_ip": "************-38", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_32600,TCP_8080,http"}, {"rule_name": "**********_to_DNS", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "DNS_3.9.20.100", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "VDI_to_BOCCSFTP", "action": "permit", "source_zone": "untrust", "source_ip": "*********-25,X<PERSON><PERSON>BOCC,YJBOCC,YZBOCC", "destination_ip": "host_4.101.50.1", "destination_zone": "trust", "service": "TCP_34443"}, {"rule_name": "20231207_nginx_to_kfpt", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}, {"rule_name": "ALTX_to_KFPTnginx", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8989"}], "A_Others": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050,snmptrap,syslog"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "TerminalToNetworkDisk", "action": "permit", "source_zone": "untrust", "source_ip": "Terminal", "destination_ip": "host_4.101.91.11", "destination_zone": "trust", "service": "TCP_8000,TCP_8080,http,https"}, {"rule_name": "CIMSProxyToNetworkDisk", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.255.10.11", "destination_ip": "host_4.101.91.11", "destination_zone": "trust", "service": "http"}, {"rule_name": "NetworkDiskToCIMS-AD", "action": "permit", "source_zone": "trust", "source_ip": "host_4.101.91.11,host_4.101.91.21,host_4.101.91.31", "destination_ip": "host_4.255.10.21,host_4.255.10.22", "destination_zone": "untrust", "service": "464,49152-65535,TCP_389,bootps,dns,dns-tcp,kerberos-tcp,kerberos-udp,netbios-datagram,netbios-name,netbios-session,netbios-ssn,rpc,smb,smtp,tcp-3268,tcp-3269,tcp-5722,tcp-636,tcp-9389,udp-2535,udp-389"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "BaoLeiJi-To-G3Monitor", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "host_198.3.100.240,host_4.28.10.30", "destination_zone": "untrust", "service": "TCP-5601"}, {"rule_name": "BaoLeiJi-To-ShuangYinSu", "action": "permit", "source_zone": "trust", "source_ip": "host_4.101.90.10", "destination_ip": "host_**********", "destination_zone": "untrust", "service": "TCP-4451"}, {"rule_name": "VDI-To-BaoLeiJi_1", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "host_4.101.90.10", "destination_zone": "trust", "service": "TCP-10102,http,https"}, {"rule_name": "VDI-To-BaoLeiJi_2", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.98.1.20", "destination_ip": "Range_4.101.90.11-14", "destination_zone": "trust", "service": "TCP-3389,TCP_389"}, {"rule_name": "BaoLeiJi-To-JC_KIBANA", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "Range_4.190.121.81-82,host_4.190.163.5", "destination_zone": "untrust", "service": "TCP_25601"}, {"rule_name": "BaoLeiJi-To-JC_Debug", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "Range_4.190.83.1-2", "destination_zone": "untrust", "service": "TCP_30900"}, {"rule_name": "BaoLeiJi-To-G3Monitor-1", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "host_3.254.1.10", "destination_zone": "untrust", "service": "TCP_3000"}, {"rule_name": "SGW_to_CS", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8080,TCP_8081"}, {"rule_name": "CS-NG_To_b2csweb", "action": "permit", "source_zone": "trust", "source_ip": "***********-22", "destination_ip": "host_4.35.31.10", "destination_zone": "untrust", "service": "TCP_8080,TCP_8081"}, {"rule_name": "BaoLeiJi_To_SJJM-WEB-NG", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "host_4.35.20.30", "destination_zone": "untrust", "service": "http"}, {"rule_name": "DAM-Server_To_NG", "action": "permit", "source_zone": "untrust", "source_ip": "Range_4.35.20.46-47", "destination_ip": "host_4.101.90.30", "destination_zone": "trust", "service": "TCP_18081"}, {"rule_name": "SJJM-WEB-NG_To_SSL", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.31-32", "destination_ip": "host_4.98.1.14", "destination_zone": "untrust", "service": "TCP_18081"}, {"rule_name": "BaoLeiJi-To-SJFX", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "********", "destination_zone": "untrust", "service": "ssh"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "BaoLeiJi-To-YeWu", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080,TCP_8880"}, {"rule_name": "BaoLeiJi-To-SJZT", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "**********,**********,**********", "destination_zone": "untrust", "service": "TCP_8080,TCP_8889"}, {"rule_name": "BaoLeiJi-To-SOClogging", "action": "permit", "source_zone": "trust", "source_ip": "host_4.101.90.10", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8400,syslog"}, {"rule_name": "20231116_BaoLeiJi-To-dns", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "any", "destination_zone": "untrust", "service": "dns,dns-tcp"}, {"rule_name": "20231116_BaoLeiJi-To-jiankong", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_30038,TCP_30039,http"}, {"rule_name": "Baoleiji-To-ZhongTai", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.101.90.11-14,host_4.101.90.10", "destination_ip": "host3.14.10.200", "destination_zone": "untrust", "service": "icmp,tcp_19090"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}, {"rule_name": "SecOCS-To-Monitor-1", "action": "permit", "source_zone": "trust", "source_ip": "***********-54", "destination_ip": "Sec_Monitor", "destination_zone": "untrust", "service": "https"}, {"rule_name": "SecOCS-To-Monitor-2", "action": "permit", "source_zone": "trust", "source_ip": "***********-54", "destination_ip": "host_************8", "destination_zone": "untrust", "service": "TCP_8081"}, {"rule_name": "VDI-To-SecOCS_1", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "host_4.101.90.50", "destination_zone": "trust", "service": "http"}, {"rule_name": "VDI-To-SecOCS_2", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.98.1.20", "destination_ip": "***********-52,host_4.101.90.50", "destination_zone": "trust", "service": "TCP-10102,TCP_10000,https"}, {"rule_name": "VDI-To-SecOCS_3", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.98.1.20", "destination_ip": "***********-54", "destination_zone": "trust", "service": "SecOCS-POR<PERSON>,TCP-3389,TCP_389"}, {"rule_name": "SecOCS-To-ShuangYinSu", "action": "permit", "source_zone": "trust", "source_ip": "***********-52,***********-54", "destination_ip": "host_**********", "destination_zone": "untrust", "service": "TCP-4451"}], "B_Core": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050,snmptrap,syslog"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "SGW to ELB", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "elb_4.101.128.10", "destination_zone": "trust", "service": "TCP_5080"}, {"rule_name": "nginx to ELB", "action": "permit", "source_zone": "trust", "source_ip": "elb_4.101.128.11-14", "destination_ip": "elb_4.13.5.9", "destination_zone": "untrust", "service": "http"}, {"rule_name": "internal to ELB_NG", "action": "permit", "source_zone": "untrust", "source_ip": "elb_4.9.6.1-2", "destination_ip": "elb_4.101.128.10,elb_4.101.128.11-14", "destination_zone": "trust", "service": "TCP_5080"}, {"rule_name": "payProxy to nginx", "action": "permit", "source_zone": "untrust", "source_ip": "payProxy_4.13.5.211-214", "destination_ip": "payProxyNG", "destination_zone": "trust", "service": "https"}, {"rule_name": "payProxyNG to weixin", "action": "permit", "source_zone": "trust", "source_ip": "payProxyNG", "destination_ip": "weixin_dnat_4.98.128.10", "destination_zone": "untrust", "service": "https"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "SGW to STQD_NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "STQD_NG", "destination_zone": "trust", "service": "TCP_8330"}, {"rule_name": "STQD_NG to STQD", "action": "permit", "source_zone": "trust", "source_ip": "STQD_NG", "destination_ip": "STQD_4.24.11.90", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "SGW to YWZT_NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "YWZT_NG", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "YWZT_NG to YWZT_4.60.12.10", "action": "permit", "source_zone": "trust", "source_ip": "YWZT_NG", "destination_ip": "YWZT_4.60.12.10", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "K8S-******** to K8S-NG", "action": "permit", "source_zone": "untrust", "source_ip": "************/24,K8S-********", "destination_ip": "K8S-NG", "destination_zone": "trust", "service": "TCP_8080,https"}, {"rule_name": "K8S-NG to TC_APP_XIAOXI", "action": "permit", "source_zone": "trust", "source_ip": "K8S-NG", "destination_ip": "TC_APP_XIAOXI", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW to YWZT_NG_500W", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "YWZT_NG_500W", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "YWZT_NG_500W to YWZT_4.60.12.10", "action": "permit", "source_zone": "trust", "source_ip": "YWZT_NG_500W", "destination_ip": "YWZT_4.60.12.10", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "K8S-NG to 500W_XIAOXI", "action": "permit", "source_zone": "trust", "source_ip": "K8S-NG", "destination_ip": "500W_XIAOXI", "destination_zone": "untrust", "service": "https"}, {"rule_name": "SGW to USAP_NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "USAP_NG", "destination_zone": "trust", "service": "TCP_19080,http"}, {"rule_name": "USAP_NG to USAP_Service", "action": "permit", "source_zone": "trust", "source_ip": "USAP_NG", "destination_ip": "USAP_Service", "destination_zone": "untrust", "service": "TCP_9080"}, {"rule_name": "G3_to_USAP", "action": "permit", "source_zone": "untrust", "source_ip": "***********,G3_************,GTM_3.9.20.X", "destination_ip": "USAP_NG", "destination_zone": "trust", "service": "TCP_19080"}, {"rule_name": "USAP_to_G3", "action": "permit", "source_zone": "trust", "source_ip": "USAP_4.101.129.61-62", "destination_ip": "************,G3_10.194.119.12", "destination_zone": "untrust", "service": "TCP_30010,TCP_31001,TCP_54102"}, {"rule_name": "G3_to_K8S-NG", "action": "permit", "source_zone": "untrust", "source_ip": "G3_10.194.120.0", "destination_ip": "K8S-NG", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "radius", "action": "permit", "source_zone": "trust", "source_ip": "***********/24", "destination_ip": "**********", "destination_zone": "untrust", "service": "udp-1812"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "SJZT-NG_to_SJZT-VS", "action": "permit", "source_zone": "trust", "source_ip": "SJZT-NG", "destination_ip": "SJZT-VS", "destination_zone": "untrust", "service": "TCP_19091,TCP_19092,TCP_19093,TCP_19094,TCP_19095"}, {"rule_name": "SGW_to_SJZT-NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "SJZT-NG", "destination_zone": "trust", "service": "TCP_19091,TCP_19092,TCP_19093,TCP_19094,TCP_19095"}], "B_Normal": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_34443,ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050,snmptrap,syslog"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "psbc to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "psbc_nat_4.98.131.1", "destination_ip": "SFPT_4.101.170.1", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "ELB to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "elb_4.13.5.51-59", "destination_ip": "SFPT_4.101.170.1", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "YunYing to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "***********-49,<PERSON><PERSON><PERSON>_4.98.129.8-9,host_4.98.129.28,host_4.98.129.29", "destination_ip": "SFPT_4.101.170.1", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>hongTai to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "sjzt_4.14.100.1", "destination_ip": "SFPT_4.101.170.1", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "JGXT_TO_KFPT-SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********-35,**********-75,***********,host_4.98.46.100,host_4.98.46.11,host_4.98.46.21", "destination_ip": "SFPT_4.101.170.1", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "KFSJFX_TO_KFPT-SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********-55,host_4.98.12.35,host_4.98.12.36", "destination_ip": "SFPT_4.101.170.1", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SJSJ_TO_XQTC", "action": "permit", "source_zone": "untrust", "source_ip": "************,************,************,************,*********", "destination_ip": "SFPT_4.101.170.1", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "B_Others": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050,snmptrap,syslog"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "B_SGW": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050,snmptrap,syslog"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "external to SGW", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SGW_4.101.5.0/24", "destination_zone": "trust", "service": "TCP_19091,TCP_19092,TCP_19093,TCP_19094,TCP_19095,TCP_7443,TCP_8443,https"}, {"rule_name": "SGW to ELB", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "elb_4.101.128.10", "destination_zone": "untrust", "service": "TCP_5080"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SJJM_NG_T0_SGW", "action": "permit", "source_zone": "untrust", "source_ip": "Host_***********", "destination_ip": "Host_4.101.9.100", "destination_zone": "trust", "service": "TCP_18081"}, {"rule_name": "SJJM_SGW-In_To_SGW-Out", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Host_4.98.1.10", "destination_zone": "untrust", "service": "any"}, {"rule_name": "SGW_to_KFPT-openapi", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_7001"}, {"rule_name": "SGW_to_CodingNG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "SGW to STQD_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "STQD_NG", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "SGW to YWZT_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "YWZT_NG", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW to YWZT_NG_500W", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "YWZT_NG_500W", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW to USAP_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "USAP_NG", "destination_zone": "untrust", "service": "TCP_19080,http"}, {"rule_name": "SGW to youchu_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "CT-Cloud_to_SGW", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SGW_4.101.5.0/24", "destination_zone": "trust", "service": "TCP_8080,TCP_8081"}, {"rule_name": "SGW_to_CS", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080,TCP_8081"}, {"rule_name": "USAP_To_SGW", "action": "permit", "source_zone": "untrust", "source_ip": "************/24", "destination_ip": "SGW_4.101.9.100", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW_to_XT-SGW", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.101.6.1,SGW_4.101.7.1,SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "SGW to AOPS_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_32600,http"}, {"rule_name": "SSL_CRL", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*********,*********", "destination_zone": "untrust", "service": "TCP_UDP_389"}, {"rule_name": "SGW to VDI_OCS", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}, {"rule_name": "SGW_to_SecOCS", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_SJZT-NG-VS", "action": "permit", "source_zone": "trust", "source_ip": "SGW_*********/24", "destination_ip": "SJZT-NG-VS", "destination_zone": "untrust", "service": "TCP_19091,TCP_19092,TCP_19093,TCP_19094,TCP_19095"}], "CSLJC": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "soc_to_trust", "action": "permit", "source_zone": "any", "source_ip": "soc_***********/24", "destination_ip": "any", "destination_zone": "any", "service": "TCP_8890,TCP_8891,https,icmp,snmp,ssh"}, {"rule_name": "trust_to_soc", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "soc_***********/24", "destination_zone": "any", "service": "TCP_8999,rdp-tcp,snmptrap,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "YunYingFengKong to JunCai", "action": "permit", "source_zone": "untrust", "source_ip": "YunYingGongSi", "destination_ip": "JunCai", "destination_zone": "trust", "service": "https"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "TCP-139,TCP-3389,ssh,telnet"}, {"rule_name": "SGW to TC-APP_NG", "action": "permit", "source_zone": "untrust", "source_ip": "TC_APP_address", "destination_ip": "Juncai_address_4.190.161.12", "destination_zone": "trust", "service": "TCP_52701"}, {"rule_name": "YYXNH to WEBDC", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_52701"}, {"rule_name": "YYXNH to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "DEVTEST_YYOS_VDI", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "B_ELB": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "soc_to_trust", "action": "permit", "source_zone": "any", "source_ip": "soc_***********/24", "destination_ip": "any", "destination_zone": "any", "service": "TCP_8890,TCP_8891,https,icmp,snmp,ssh"}, {"rule_name": "trust_to_soc", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "soc_***********/24", "destination_zone": "any", "service": "TCP_8999,rdp-tcp,snmptrap,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SGW to ELB", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_*********/24", "destination_ip": "elb_4.101.131.10", "destination_zone": "trust", "service": "http"}, {"rule_name": "nginx to ELB", "action": "permit", "source_zone": "trust", "source_ip": "elb_4.101.131.11-14", "destination_ip": "elb_4.13.5.9", "destination_zone": "untrust", "service": "http"}, {"rule_name": "payProxy to nginx", "action": "permit", "source_zone": "untrust", "source_ip": "payProxy_4.13.5.201-202", "destination_ip": "payProxyNG", "destination_zone": "trust", "service": "TCP_28601"}, {"rule_name": "payProxyNG to psbc", "action": "permit", "source_zone": "trust", "source_ip": "payProxyNG", "destination_ip": "psbc_nat_4.98.131.1", "destination_zone": "untrust", "service": "TCP_28601"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "SGW": [{"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}]}
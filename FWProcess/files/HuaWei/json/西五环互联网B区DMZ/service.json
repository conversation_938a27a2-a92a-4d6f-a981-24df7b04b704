{"default": [], "Core": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}, {"protocol": "tcp", "destination-port": "1234"}, {"protocol": "udp", "destination-port": "1234"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "tcp-20", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_8510", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8510"}]}, {"name": "TCP_34443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "34443"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "Normal": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_30201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30201"}]}, {"name": "TCP_8082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8082"}]}, {"name": "TCP_8088", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8088"}]}, {"name": "NAS", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}, {"protocol": "tcp", "destination-port": "4049"}, {"protocol": "udp", "destination-port": "4049"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}], "Others": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-9883", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9883"}]}, {"name": "tcp-9995", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9995"}]}, {"name": "tcp-9994", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9994"}]}, {"name": "TCP-8090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8090"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_8444", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8444"}]}, {"name": "TCP_8066", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8066"}]}, {"name": "TCP_9200", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9200"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_38081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "38081"}]}, {"name": "TCP_38082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "38082"}]}, {"name": "TCP_6280", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6280"}]}, {"name": "TCP_6279", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6279"}]}, {"name": "TCP_38083", "type": "object", "service": [{"protocol": "tcp", "destination-port": "38083"}]}, {"name": "TCP_30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}, {"name": "TCP_25", "type": "object", "service": [{"protocol": "tcp", "destination-port": "25"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "AZZD": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_81", "type": "object", "service": [{"protocol": "tcp", "destination-port": "81"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_5080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5080"}]}, {"name": "TCP_8000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}, {"name": "TCP_34443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "34443"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP-8090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8090"}]}, {"name": "tcp-26389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "26389"}]}], "SGW": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "TCP_389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "389"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8501", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8501"}]}, {"name": "TCP_8502", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8502"}]}, {"name": "TCP_8503", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8503"}]}, {"name": "TCP_8510", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8510"}]}, {"name": "TCP_8010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8010"}]}, {"name": "TCP_8520", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8520"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_7001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7001"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_81", "type": "object", "service": [{"protocol": "tcp", "destination-port": "81"}]}, {"name": "TCP_10088", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10088"}]}, {"name": "TCP_10080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10080"}]}, {"name": "TCP_8000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}, {"name": "TCP_18080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18080"}]}, {"name": "TCP_8181", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8181"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_8023", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8023"}]}, {"name": "TCP_8024", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8024"}]}, {"name": "TCP_9443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9443"}]}, {"name": "TCP_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "TCP_8082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8082"}]}, {"name": "TCP_8083", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8083"}]}, {"name": "TCP_8084", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8084"}]}, {"name": "TCP_7443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7443"}]}], "STQD": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8330", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8330"}]}, {"name": "TCP_8501", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8501"}]}, {"name": "TCP_8502", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8502"}]}, {"name": "TCP_8503", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8503"}]}, {"name": "appauth-app", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11000"}, {"protocol": "tcp", "destination-port": "11010"}, {"protocol": "tcp", "destination-port": "11020"}, {"protocol": "tcp", "destination-port": "11030"}, {"protocol": "tcp", "destination-port": "11040"}, {"protocol": "tcp", "destination-port": "11050"}, {"protocol": "tcp", "destination-port": "11060"}, {"protocol": "tcp", "destination-port": "11080"}, {"protocol": "tcp", "destination-port": "11090"}]}, {"name": "TCP_2195", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2195"}]}, {"name": "TCP_8181", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8181"}]}, {"name": "TCP_3128", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3128"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_8000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}, {"name": "TCP_7000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7000"}]}, {"name": "TCP_12131", "type": "object", "service": [{"protocol": "tcp", "destination-port": "12131"}]}, {"name": "TCP_10080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10080"}]}, {"name": "TCP_8600", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8600"}]}, {"name": "TCP_20128", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20128"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_110", "type": "object", "service": [{"protocol": "tcp", "destination-port": "110"}]}, {"name": "TCP_43", "type": "object", "service": [{"protocol": "tcp", "destination-port": "43"}]}, {"name": "TCP_143", "type": "object", "service": [{"protocol": "tcp", "destination-port": "143"}]}, {"name": "TCP_995", "type": "object", "service": [{"protocol": "tcp", "destination-port": "995"}]}, {"name": "TCP_993", "type": "object", "service": [{"protocol": "tcp", "destination-port": "993"}]}, {"name": "TCP_1995", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1995"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP_26379", "type": "object", "service": [{"protocol": "tcp", "destination-port": "26379"}]}, {"name": "tcp-6379", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6379"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "SJZT": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "TCP_9092", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9092"}]}, {"name": "YZProxy service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9001"}, {"protocol": "tcp", "destination-port": "9009"}, {"protocol": "tcp", "destination-port": "3000"}, {"protocol": "tcp", "destination-port": "1818"}, {"protocol": "tcp", "destination-port": "8083"}, {"protocol": "tcp", "destination-port": "8888"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP_1812", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1812"}]}, {"name": "TCP_19080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19080"}]}, {"name": "TCP_8040-8140", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8140"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_30051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30051"}]}, {"name": "TCP_8888", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8888"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP-18088", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18088"}]}, {"name": "TCP_5601", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5601"}]}, {"name": "TCP_30002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30002"}]}, {"name": "TCP-21050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "21050"}]}, {"name": "TCP-10000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10000"}]}, {"name": "TCP-2181", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2181"}]}], "YZD": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_8330", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8330"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "UDP_514", "type": "object", "service": [{"protocol": "udp", "destination-port": "514"}]}], "YHLX": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_18080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18080"}]}, {"name": "TCP_28081-28090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28090"}]}, {"name": "TCP_8330", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8330"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "TYXXFB": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP-7070", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7070"}]}, {"name": "TCP_7070", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7070"}]}, {"name": "TCP_17070", "type": "object", "service": [{"protocol": "tcp", "destination-port": "17070"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_9333", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9333"}]}, {"name": "TCP_19333", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19333"}]}, {"name": "TCP_26379", "type": "object", "service": [{"protocol": "tcp", "destination-port": "26379"}]}, {"name": "TCP_6379", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6379"}]}, {"name": "TCP_1935", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1935"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "4.10.10.41", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP_5671", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5671"}]}, {"name": "TCP_11666", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11666"}]}, {"name": "TCP_30201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30201"}]}, {"name": "TCP-11671", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11671"}]}, {"name": "TCP_30202", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30202"}]}, {"name": "TCP_30203", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30203"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_30204", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30204"}]}, {"name": "TCP-30205", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30205"}]}], "KFPT": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_11666-11670", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11666"}, {"protocol": "tcp", "destination-port": "11667"}, {"protocol": "tcp", "destination-port": "11668"}, {"protocol": "tcp", "destination-port": "11669"}, {"protocol": "tcp", "destination-port": "11670"}]}, {"name": "TCP_12001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "12001"}]}, {"name": "TCP-4321", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4321"}]}, {"name": "TCP_30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}, {"name": "tcp-9999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9999"}]}, {"name": "tcp-55382", "type": "object", "service": [{"protocol": "tcp", "destination-port": "55382"}]}, {"name": "tcp-8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "tcp-8090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8090"}]}, {"name": "tcp-30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}, {"name": "tcp-1001-1005", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1005"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP-11671", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11671"}]}, {"name": "tcp-30101", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30101"}]}, {"name": "tcp-30103", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30103"}]}, {"name": "tcp-30102", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30102"}]}, {"name": "tcp-30104", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30104"}]}, {"name": "tcp-31002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31002"}]}, {"name": "TCP_7001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7001"}]}, {"name": "TCP_31003", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31003"}]}, {"name": "tcp-30105", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30105"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "TCP_31002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31002"}]}, {"name": "TCP_31101", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31101"}]}, {"name": "tcp-31004", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31004"}]}, {"name": "tcp-31005", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31005"}]}, {"name": "tcp-8743", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8743"}]}, {"name": "TCP_30201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30201"}]}, {"name": "TCP_30000-31000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31000"}]}, {"name": "TCP_30108", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30108"}]}, {"name": "TCP_30109", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30109"}]}, {"name": "TCP_5017", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5017"}]}, {"name": "TCP_31201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31201"}]}, {"name": "TCP_10031", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10031"}]}, {"name": "TCP_33000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "33000"}]}, {"name": "TCP_11669-11670", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11670"}]}, {"name": "TCP_10032", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10032"}]}, {"name": "TCP_31202", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31202"}]}, {"name": "TCP_31301", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31301"}]}, {"name": "TCP_10024", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10024"}]}, {"name": "TCP_10029", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10029"}]}, {"name": "TCP_10030", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10030"}]}, {"name": "TCP_32000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32000"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_11668", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11668"}]}, {"name": "TCP_30203", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30203"}]}, {"name": "TCP_10033", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10033"}]}, {"name": "TCP_8083", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8083"}]}, {"name": "TCP_32201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32201"}]}, {"name": "TCP_8084", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8084"}]}, {"name": "TCP_32202", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32202"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP_31302", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31302"}]}, {"name": "TCP-18081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18081"}]}, {"name": "TCP_10034", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10034"}]}, {"name": "TCP_8802", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8802"}]}, {"name": "TCP_10035", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10035"}]}, {"name": "TCP_20001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20001"}]}, {"name": "TCP_10036", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10036"}]}], "CSLP": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8010"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "USAP": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP_8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP_8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-7004", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7004"}]}, {"name": "TCP_10080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10080"}]}, {"name": "TCP_9080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9080"}]}, {"name": "TCP_10081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10081"}]}, {"name": "tcp-18080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18080"}]}, {"name": "tcp-8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "tcp-8089", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8089"}]}, {"name": "tcp-8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "tcp-54106", "type": "object", "service": [{"protocol": "tcp", "destination-port": "54106"}]}, {"name": "tcp-9090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9090"}]}, {"name": "tcp-8087", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8087"}]}, {"name": "tcp-8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "tcp-19080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19080"}]}, {"name": "TCP_10082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10082"}]}, {"name": "TCP_7001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7001"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP-7004", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7004"}]}, {"name": "TCP_8800", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8800"}]}, {"name": "TCP_19080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19080"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_30001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30001"}]}, {"name": "TCP_30010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30010"}]}, {"name": "TCP_30080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30080"}]}, {"name": "TCP_8330", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8330"}]}, {"name": "TCP_8088", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8088"}]}, {"name": "TCP_6002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6002"}]}, {"name": "TCP_8021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8021"}]}, {"name": "TCP_30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}, {"name": "TCP_30500", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30500"}]}, {"name": "TCP_8010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8010"}]}, {"name": "TCP-8090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8090"}]}, {"name": "TCP_30501", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30501"}]}, {"name": "TCP_30220", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30220"}]}, {"name": "TCP_30011", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30011"}]}, {"name": "TCP_30221", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30221"}]}, {"name": "TCP_30222", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30222"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_30017", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30017"}]}, {"name": "TCP_30223", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30223"}]}, {"name": "TCP_30058", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30058"}]}, {"name": "TCP_30224", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30224"}]}, {"name": "TCP_30225", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30225"}]}, {"name": "TCP_30226", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30226"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "TCP_30023", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30023"}]}, {"name": "TCP-30021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30021"}]}, {"name": "TCP_30024", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30024"}]}, {"name": "TCP_30101", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30101"}]}, {"name": "TCP_30227", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30227"}]}, {"name": "TCP_30510", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30510"}]}, {"name": "TCP_31001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31001"}]}, {"name": "TCP_31011", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31011"}]}], "WSJC": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP_8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP_8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP-10005", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10005"}]}, {"name": "TCP-10009", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10009"}]}, {"name": "TCP_5222", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5222"}]}, {"name": "TCP_7070", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7070"}]}, {"name": "TCP-10010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10010"}]}, {"name": "TCP-10020", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10020"}]}, {"name": "TCP-10021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10021"}]}, {"name": "TCP-10022", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10022"}]}, {"name": "TCP-10011", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10011"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "QKL": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP_8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP_8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_8444", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8444"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "CSLJC": [{"name": "TCP_28080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28080"}]}, {"name": "TCP_28081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28081"}]}, {"name": "TCP_28082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28082"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_8023", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8023"}]}, {"name": "TCP_8024", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8024"}]}, {"name": "TCP_9443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9443"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_7070", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7070"}]}, {"name": "TCP_2181", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2181"}]}, {"name": "TCP_8088", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8088"}]}], "TICAIAPP": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP_8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP_8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "YJVPN": [{"name": "UDP-8778", "type": "object", "service": [{"protocol": "udp", "destination-port": "8778"}]}, {"name": "TCP-8777", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8777"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP-1812", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1812"}]}, {"name": "TCP-1813", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1813"}]}, {"name": "UDP-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}, {"name": "UDP-1813", "type": "object", "service": [{"protocol": "udp", "destination-port": "1813"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_10102", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10102"}]}, {"name": "389", "type": "object", "service": [{"protocol": "udp", "destination-port": "389"}, {"protocol": "tcp", "destination-port": "389"}]}, {"name": "TCP-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP-8889", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8889"}]}]}
{"default": [{"rule_name": "ha", "action": "permit", "source_zone": "dmz,local", "source_ip": "any", "destination_ip": "any", "destination_zone": "dmz,local", "service": "any"}, {"rule_name": "ntp", "action": "permit", "source_zone": "local,trust", "source_ip": "any", "destination_ip": "ntp_4.9.0.1", "destination_zone": "trust", "service": "ntp"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "Jiankong", "destination_zone": "any", "service": "snmptrap,syslog"}, {"rule_name": "snmp", "action": "permit", "source_zone": "any", "source_ip": "Jiankong", "destination_ip": "any", "destination_zone": "any", "service": "snmp"}, {"rule_name": "radius", "action": "permit", "source_zone": "local", "source_ip": "any", "destination_ip": "**********", "destination_zone": "trust", "service": "radius"}], "Core": [{"rule_name": "XXFB&UMP_2_NEW_dowload", "action": "permit", "source_zone": "untrust", "source_ip": "UMP_server,XXFB_server", "destination_ip": "NEW_down_ng", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_3.9.1.11-12,NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_3.9.1.11-12,NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SGW to download server vip", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "download_vip_4.103.100.10", "destination_zone": "trust", "service": "TCP_8443,http"}, {"rule_name": "UMP to download servers", "action": "permit", "source_zone": "untrust", "source_ip": "servers_4.13.10.1-12\"", "destination_ip": "servers_4.103.100.11-12\"", "destination_zone": "trust", "service": "ftp,ssh"}, {"rule_name": "XXFB to download servers", "action": "permit", "source_zone": "untrust", "source_ip": "FTP_3.9.0.21,FTP_3.9.0.22,XXFB_3.9.4.74-76", "destination_ip": "servers_4.103.100.11-12\"", "destination_zone": "trust", "service": "ftp,ssh"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "host_198.3.10.18", "destination_ip": "servers_4.103.100.11-12\"", "destination_zone": "trust", "service": "ftp"}, {"rule_name": "MG to download server", "action": "permit", "source_zone": "untrust", "source_ip": "MG_9.0.0.0", "destination_ip": "servers_4.103.100.11-12\",download_vip_4.103.100.10", "destination_zone": "trust", "service": "ftp"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SCP-1", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "************-22", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SCP-2", "action": "permit", "source_zone": "trust", "source_ip": "************-22", "destination_ip": "any", "destination_zone": "untrust", "service": "ssh"}, {"rule_name": "SGW to pcitupd_ng", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "pcitupd_ng_4.103.100.20", "destination_zone": "trust", "service": "http"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_34443,ssh"}, {"rule_name": "get_TLSterminal_log", "action": "permit", "source_zone": "untrust", "source_ip": "log_server_3.13.10.24", "destination_ip": "servers_4.103.100.11-12\"", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "XXFB_arthur-TO-download", "action": "permit", "source_zone": "untrust", "source_ip": "XXFB_4.27.41.41-43", "destination_ip": "servers_4.103.100.11-12\"", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SJZT-FTP to download servers", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "servers_4.103.100.11-12\"", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "YZECCdownload se", "action": "permit", "source_zone": "untrust", "source_ip": "YZECCPJ_3.30.11.221-223", "destination_ip": "servers_4.103.100.11-12\"", "destination_zone": "trust", "service": "ftp,https,ssh"}, {"rule_name": "SOC-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "ump-normal_to_download", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SGW_to_pcitinst", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "install_Dis_to_download", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "YZECCdownload se", "action": "drop", "source_zone": "any", "source_ip": "***********-226", "destination_ip": "any", "destination_zone": "any", "service": "any"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "Core_4.103.100.0/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "Normal": [{"rule_name": "CFZX-SZQB_To_SZRMB", "action": "permit", "source_zone": "untrust", "source_ip": "CFZX_SERVER,SZQB_SERVER", "destination_ip": "SZRMB_SERVER", "destination_zone": "trust", "service": "TCP_8080,TCP_8081,https"}, {"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SGW to sleyeNginx", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "sleye_<PERSON>inx_VIP", "destination_zone": "trust", "service": "http"}, {"rule_name": "sleyeNginx-to-container-ingress", "action": "permit", "source_zone": "trust", "source_ip": "sleye_nginx", "destination_ip": "container-platform-ingress", "destination_zone": "untrust", "service": "http"}, {"rule_name": "sleye to internet qyapi", "action": "permit", "source_zone": "trust", "source_ip": "sleye_msgw", "destination_ip": "************,***************,**************", "destination_zone": "untrust", "service": "dns,http,https"}, {"rule_name": "sleye to internet", "action": "permit", "source_zone": "trust", "source_ip": "sleye_msgw", "destination_ip": "any", "destination_zone": "untrust", "service": "dns,http,https"}, {"rule_name": "container to sleye_message", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.28.10.0/24", "destination_ip": "sleye_msgw", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "YXZX-K8S to G3-KFPT-NG", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_30201"}, {"rule_name": "ShuJuChanPin_To_msgw", "action": "permit", "source_zone": "untrust", "source_ip": "Node_************,Range_4.14.100.23-24,Range_4.14.100.51-54", "destination_ip": "sleye_msgw", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "SGW_to_CSLJC-BOS", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8082"}, {"rule_name": "CSLJC-CORE-K8S_TO_BOSROUTE-F5", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.190.84.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8082"}, {"rule_name": "G3BOSROUTER_TO_DMZ-NAS", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.41-42", "destination_ip": "************-202", "destination_zone": "untrust", "service": "NAS,icmp"}, {"rule_name": "JC-BOSROUTER_TO_BOS-WCSROUTE", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.41-42", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8088"}, {"rule_name": "JC-K8S_TO_BOSRouter-F5", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.190.44.0/24", "destination_ip": "host_4.103.120.49", "destination_zone": "trust", "service": "TCP_8082"}, {"rule_name": "BOSRouter_To_Internet", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.41-42", "destination_ip": "any", "destination_zone": "untrust", "service": "dns,https"}, {"rule_name": "BOSRouter_To_DNS", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.41-42", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "BOSRouter_To_txodds", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.41-42", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "SZRMB-NG_To_DNS", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.51-52", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "SZRMB-NG_To_Internet", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.51-52", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "CY_To_SZRMB-NG", "action": "permit", "source_zone": "untrust", "source_ip": "CFZX,Range_4.13.5.201-220", "destination_ip": "host_4.103.120.50", "destination_zone": "trust", "service": "https"}, {"rule_name": "YWZD_to_CSLJC-BOS", "action": "permit", "source_zone": "untrust", "source_ip": "Range_4.190.120.41-42,Range_4.190.83.1-2", "destination_ip": "Range_4.103.120.41-42", "destination_zone": "trust", "service": "TCP_8082,ssh"}, {"rule_name": "SZRMB_To_BAJQKHZX", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.120.61-62", "destination_ip": "BAJQKHZX", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SZRMB_SSL_To_SZRMB_NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0,SZRMB_SSL", "destination_ip": "host_4.103.120.60", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "Normal_4.103.120.0/24,Normal_4.103.121.0/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "Others": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "k01", "action": "permit", "source_zone": "local,trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP-8090,dns,icmp,tcp-9883,tcp-9994,tcp-9995"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SGW_to_<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "YJZH_4.103.150.11", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW_to_JiaoYiJian<PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "JYJK_4.103.150.21", "destination_zone": "trust", "service": "http"}, {"rule_name": "JYJK_nginx_to_DNS", "action": "permit", "source_zone": "trust", "source_ip": "JYJK_4.103.150.21,YJZH_4.103.150.11", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "JYJK_nginx_to_internet", "action": "permit", "source_zone": "trust", "source_ip": "JYJK_4.103.150.21,YJZH_4.103.150.11", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "JYJK_nginx_to_APP", "action": "permit", "source_zone": "trust", "source_ip": "JYJK_4.103.150.21,YJZH_4.103.150.11", "destination_ip": "***********-12", "destination_zone": "untrust", "service": "TCP_6279,TCP_6280,TCP_8080"}, {"rule_name": "Security_to_TaiShiGanZhi", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8444,https"}, {"rule_name": "TaiShiGan<PERSON>hi_to_Security", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8066,TCP_9200,https"}, {"rule_name": "Security_to_duplex", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "TaiShiGanZhi_to_duplex", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "any"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "nginx_to_monitorAPP", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "**********-12,************-12", "destination_zone": "untrust", "service": "TCP_38081,TCP_38083"}, {"rule_name": "SGW_to_monitorNG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "http"}, {"rule_name": "G3-Monitor-NG_TO_internet", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "G3-Monitor-NG_to_internet-DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "G3-Monitor_TO_Monitor-NG", "action": "permit", "source_zone": "untrust", "source_ip": "G3-Monitor,Monitor_4.254.127.21-22", "destination_ip": "Monitor-NG", "destination_zone": "trust", "service": "TCP_38082"}, {"rule_name": "JuHeZhiFu-SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SFTP_4.103.150.100", "destination_zone": "trust", "service": "TCP_30000,ssh"}, {"rule_name": "SGW_to_CS", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "host_4.103.150.50", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "CS_to_b2csweb", "action": "permit", "source_zone": "trust", "source_ip": "************-52", "destination_ip": "host_**********", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "b2csweb_to_mail", "action": "permit", "source_zone": "untrust", "source_ip": "**********-12", "destination_ip": "host_4.103.150.55", "destination_zone": "trust", "service": "TCP_25"}, {"rule_name": "mail_to_Internet", "action": "permit", "source_zone": "trust", "source_ip": "************-57", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_25,dns,icmp"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "Normal_4.103.150.0/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "SOAS_to_TIP", "action": "permit", "source_zone": "untrust", "source_ip": "***********/24", "destination_ip": "host_4.103.150.61", "destination_zone": "trust", "service": "TCP-8090,https,syslog"}, {"rule_name": "TIP_to_DNS", "action": "permit", "source_zone": "trust", "source_ip": "host_4.103.150.61", "destination_ip": "any", "destination_zone": "untrust", "service": "dns,icmp"}, {"rule_name": "TIP_to_Internet", "action": "permit", "source_zone": "trust", "source_ip": "host_4.103.150.61", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "TIP_to_Internet-1", "action": "permit", "source_zone": "trust", "source_ip": "host_4.103.150.61", "destination_ip": "TIP-IP", "destination_zone": "untrust", "service": "https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}, {"rule_name": "SecOCS-To-TIP", "action": "permit", "source_zone": "untrust", "source_ip": "***********-54", "destination_ip": "host_4.103.150.61", "destination_zone": "trust", "service": "https"}], "AZZD": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SGW to pcittscs", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "QRAS", "destination_zone": "trust", "service": "TCP_81"}, {"rule_name": "SGW to pcittscsup", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "update_server", "destination_zone": "trust", "service": "http"}, {"rule_name": "QRAS-to-qianguanzhi", "action": "permit", "source_zone": "trust", "source_ip": "QRAS", "destination_ip": "host_4.13.20.50", "destination_zone": "untrust", "service": "http"}, {"rule_name": "zidongbushu_update_server", "action": "permit", "source_zone": "untrust", "source_ip": "host_*******", "destination_ip": "update_server", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_34443,ssh"}, {"rule_name": "SGW_to_AZZD-js-epb", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "SGW_to_AZZD-infohub", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "any", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW_to_AZZD-downld", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "any", "destination_zone": "trust", "service": "http"}, {"rule_name": "auth-nginx_to_internetDNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "auth-nginx_to_internet", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "http,https"}, {"rule_name": "API_to_ELB-proxy", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_5080,http"}, {"rule_name": "infohub_to_SIH", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8000"}, {"rule_name": "ump_to_download", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_34443,ftp,http,ssh"}, {"rule_name": "AZZD-manage", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "http,ssh"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "ump-normal_to_azzd-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_34443"}, {"rule_name": "ZiDongHua_To_AZZD-NG", "action": "permit", "source_zone": "untrust", "source_ip": "**********,host_3.252.101.2,host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_34443"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "tc-cloud_to_bp-pmc", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8090,TCP_8080"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}, {"rule_name": "jk-26389", "action": "permit", "source_zone": "untrust", "source_ip": "*********/24,***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "tcp-26389"}], "SGW": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "internet to sleye ssl", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "sleye_ssl_4.103.200.11", "destination_zone": "trust", "service": "https"}, {"rule_name": "internet to singdownload ssl", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "singdownload_ssl_4.103.200.13", "destination_zone": "trust", "service": "https"}, {"rule_name": "internet to doubDownload ssl", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "doubdownload_ssl_4.103.200.14", "destination_zone": "trust", "service": "https"}, {"rule_name": "internet to SQTD", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "STQD_SSL_4.103.200.12", "destination_zone": "trust", "service": "TCP_8010,TCP_8501,TCP_8502,TCP_8503,TCP_8510,TCP_8520,https"}, {"rule_name": "internet to UMP", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "ump_4.103.200.17", "destination_zone": "trust", "service": "https"}, {"rule_name": "internet to UMP_Duplex", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "umpDuplex_4.103.200.18", "destination_zone": "trust", "service": "https"}, {"rule_name": "internet to ildg", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "ildg_ssl_4.103.200.25", "destination_zone": "trust", "service": "https"}, {"rule_name": "internet to SGW", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SGW_4.103.200.0/24", "destination_zone": "trust", "service": "TCP_10088,TCP_7001,TCP_7443,TCP_8023,TCP_8024,TCP_8443,TCP_8510,TCP_8520,TCP_9443,https"}, {"rule_name": "SGW to sleye", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "sleye_nginx_vip", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to LDAP", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "*********,LDAP_4.11.0.10", "destination_zone": "untrust", "service": "TCP_389"}, {"rule_name": "SGW to STQD_appauth", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "appauth_nginx_4.103.12.100", "destination_zone": "untrust", "service": "TCP_8501,TCP_8502,TCP_8503"}, {"rule_name": "SGW to STQD_tc-map", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "STQD_tc-map_4.103.12.110", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to download server", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "download_server_4.103.100.10", "destination_zone": "untrust", "service": "TCP_8443,http"}, {"rule_name": "SGW to stqd_web", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "stqd_web_4.103.12.60", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to stqd_api", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "stqd_api_4.103.12.70", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to stqd_pic", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "stqd_pic_4.103.12.80", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to webseal", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "webseal_4.13.10.65", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to cslp_ildg", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "cslp_ildg_4.103.18.10", "destination_zone": "untrust", "service": "TCP_8010"}, {"rule_name": "SGW to famcache", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8084"}, {"rule_name": "SGW to famsdk", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8081"}, {"rule_name": "SGW to famtrade", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8082"}, {"rule_name": "SGW to famuser", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8083"}, {"rule_name": "SGW to openapi", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "openapi_4.60.12.90", "destination_zone": "untrust", "service": "TCP_7001"}, {"rule_name": "SGW to ticket", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to pcittscs", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "pcittscs_3.29.1.31-32,pcittscs_4.103.11.101-102", "destination_zone": "untrust", "service": "TCP_81"}, {"rule_name": "SGW to pcittscsup", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "pcittscsup_3.29.1.41-42,pcittscsup_4.103.11.111-112", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to Android_download", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "download_3.29.1.10", "destination_zone": "untrust", "service": "TCP_10080"}, {"rule_name": "SGW to Android_infohub", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "infohub_3.17.10.10", "destination_zone": "untrust", "service": "TCP_8000"}, {"rule_name": "SGW to Android_js-epb", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "js-epb_3.29.1.50", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW to sjzt_api", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "sjzt_3.29.8.10", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to sjzt_receive", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "sjzt_3.29.9.10", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW to usap", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "usap_3.29.11.10", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to XXFB_tapi", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "xxfb_3.29.4.10,xxfb_4.103.16.10", "destination_zone": "untrust", "service": "TCP_8080,http"}, {"rule_name": "SGW to XXFB_report", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "xxfb_3.29.4.30,xxfb_4.103.16.30", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to XXFB_yoda", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "xxfb_3.29.4.70,xxfb_4.103.16.70", "destination_zone": "untrust", "service": "TCP_8080,http"}, {"rule_name": "SGW to YXYF_yjzh", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "yxyf_3.29.7.11", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to YXYF_jyjk", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "yxyf_3.29.7.12", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to WSJC", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "wsjc_3.29.12.10", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to STQD_tc-auth", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "STQD_3.29.2.125,STQD_4.103.12.30-32", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to STQD_bi", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "STQD_3.29.2.130", "destination_zone": "untrust", "service": "TCP_8181"}, {"rule_name": "SGW to LX_pnup-ha", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "***********-14,LX_3.29.3.10", "destination_zone": "untrust", "service": "TCP_18080"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SGW to USAP_jcqd", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "USAP_jcqd_***********", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to pcitupd_ng", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "pcitupd_ng_4.103.100.20", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to DATA_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "net_4.103.17.20-22", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to Receive_APP_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "net_4.103.17.10-16", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW to BMS_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "net_4.103.17.30-32", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "YJZH_4.103.150.11", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_JiaoYiJian<PERSON>", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "JYJK_4.103.150.21", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_AZZD-js-epb", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW_to_AZZD-infohub", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_AZZD-downld", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_STQD-ecnginx", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "SGW_to_CSLJC", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "any"}, {"rule_name": "SGW_to_pcitinst", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW_to_fnc", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW_to_monitorNG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_XXFB-CDN", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_CSLJC-BOS", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8082"}, {"rule_name": "SGW_to_CS", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SGW-To-SZRMB_NG", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "host_4.103.120.60", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SSL_to_YODA_F5", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "***********", "destination_zone": "untrust", "service": "http"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "STQD": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SGW to tc-map", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "tc-map-vip_4.103.12.110", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW to appauth-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "appauth_nginx_4.103.12.100", "destination_zone": "trust", "service": "TCP_8501,TCP_8502,TCP_8503"}, {"rule_name": "SGW to stqd_web", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "stqd_web_4.103.12.60", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW to stqd_api", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "stqd_api_4.103.12.70", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW to stqd_pic", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "stqd_pic_4.103.12.80", "destination_zone": "trust", "service": "http"}, {"rule_name": "tc-map-nginx to app", "action": "permit", "source_zone": "trust", "source_ip": "tc-map_nginx", "destination_ip": "host_4.24.11.10", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "appauth-nginx to app", "action": "permit", "source_zone": "trust", "source_ip": "appauth_nginx_4.103.12.100", "destination_ip": "appauth-app_4.24.10.21,appauth-app_4.24.10.31", "destination_zone": "untrust", "service": "appauth-app"}, {"rule_name": "appauth-nginx to internet", "action": "permit", "source_zone": "trust", "source_ip": "appauth_nginx_4.103.12.100", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_2195,dns"}, {"rule_name": "appauth-app to nginx", "action": "permit", "source_zone": "untrust", "source_ip": "appauth-app_4.24.10.21,appauth-app_4.24.10.31", "destination_ip": "appauth_nginx_4.103.12.100", "destination_zone": "trust", "service": "TCP_3128"}, {"rule_name": "ShiMingFuWu_API", "action": "permit", "source_zone": "untrust", "source_ip": "ShiMingFuWu_API", "destination_ip": "Shi<PERSON>ingY<PERSON><PERSON>hen_group,host_4.103.12.120", "destination_zone": "trust", "service": "TCP_8181"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to Internet", "action": "permit", "source_zone": "trust", "source_ip": "ShiMingYanZhen_group", "destination_ip": "***************,**************,BaiDu_DNS_************", "destination_zone": "untrust", "service": "dns,http,https"}, {"rule_name": "web-nginx to app", "action": "permit", "source_zone": "trust", "source_ip": "stqd_web_4.103.12.60,stqd_web_nginx", "destination_ip": "STQD_4.24.11.20-28,stqd_web_3.24.10.230,stqs_web_3.24.10.231-238", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "api-nginx to app", "action": "permit", "source_zone": "trust", "source_ip": "stqd_api_nginx", "destination_ip": "STQD_4.24.11.30-38,stqd_api_3.24.10.50-58,stqd_api_3.24.10.59", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "pic-nginx to app", "action": "permit", "source_zone": "trust", "source_ip": "stqd_pic_nginx", "destination_ip": "stqd_pic_3.24.10.16", "destination_zone": "untrust", "service": "http"}, {"rule_name": "api_client to api_nginx", "action": "permit", "source_zone": "untrust", "source_ip": "api_client", "destination_ip": "api_nginx", "destination_zone": "trust", "service": "TCP_8181"}, {"rule_name": "api_nginx to Internet", "action": "permit", "source_zone": "trust", "source_ip": "api_nginx", "destination_ip": "BaiDu_DNS_************", "destination_zone": "untrust", "service": "dns,http,https"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "app_to_message-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "STQD_4.24.11.141-144,STQD_***********-184,STQD_4.24.11.20-28,STQD_4.24.11.30-38,STQD_4.24.11.40-44,STQD_4.24.11.71-74,STQD_4.24.11.71-78", "destination_ip": "STQD_4.103.12.20-22", "destination_zone": "trust", "service": "TCP_12131"}, {"rule_name": "message-nginx_to_internet", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.20-22", "destination_ip": "BaiDu_DNS_************,<PERSON><PERSON>_t<PERSON><PERSON>an", "destination_zone": "untrust", "service": "TCP_12131,dns,https"}, {"rule_name": "auth-nginx_to_internet", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.130-138", "destination_ip": "BaiDu_DNS_************", "destination_zone": "untrust", "service": "dns,https"}, {"rule_name": "pic-nginx_to_pic-app", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.81-82", "destination_ip": "STQD_4.24.11.80-88", "destination_zone": "untrust", "service": "TCP_10080,http"}, {"rule_name": "app_to_api-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "STQD_4.24.11.90-94", "destination_ip": "STQD_4.103.12.140-142", "destination_zone": "trust", "service": "TCP_8181,http"}, {"rule_name": "SGW to stqd_net", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "STQD_4.103.12.0/24", "destination_zone": "trust", "service": "http"}, {"rule_name": "nginx_to_auth", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.30-32", "destination_ip": "STQD_4.24.13.10-12,STQD_4.24.13.20-22", "destination_zone": "untrust", "service": "TCP_8000,TCP_8330"}, {"rule_name": "nginx_to_auth-EFS", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.130-138", "destination_ip": "STQD_4.24.13.40-42", "destination_zone": "untrust", "service": "TCP_7000"}, {"rule_name": "app_TO_msg", "action": "permit", "source_zone": "untrust", "source_ip": "************/24,STQD_4.24.13.10-12", "destination_ip": "STQD_4.103.12.20-22", "destination_zone": "trust", "service": "TCP_12131"}, {"rule_name": "app_TO_auth-true", "action": "permit", "source_zone": "untrust", "source_ip": "STQD_4.24.13.10-12,STQD_4.24.13.20-22", "destination_ip": "STQD_4.103.12.130-138", "destination_zone": "trust", "service": "TCP_8181"}, {"rule_name": "ops_TO_STQD", "action": "permit", "source_zone": "untrust", "source_ip": "STQD_4.24.13.166/32", "destination_ip": "STQD_4.103.12.0/24", "destination_zone": "trust", "service": "TCP_12131,TCP_8330,http"}, {"rule_name": "SGW_to_STQD-ecnginx", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "http"}, {"rule_name": "STQD-ecnginx_to_app", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8600"}, {"rule_name": "STQD-ecnginx_to_FS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_20128"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "ECAPP_to_STQD-mail", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "smtp"}, {"rule_name": "STQD-mail_to_internet", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_110,TCP_143,TCP_1995,TCP_43,TCP_993,TCP_995,dns,smtp"}, {"rule_name": "radius", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "udp-1812"}, {"rule_name": "Message-NG_to_SMS-GW", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.20-22", "destination_ip": "host_10.194.119.2", "destination_zone": "untrust", "service": "http"}, {"rule_name": "message-nginx_to_Redis", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.21-22", "destination_ip": "STQD_4.24.11.171-174", "destination_zone": "untrust", "service": "TCP_26379,tcp-6379"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.0/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "STQD_4.103.12.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "SJZT": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "**********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "**********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SGW to DATA_NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "net_4.103.17.20-22", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW to SGW to Receive_NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "net_4.103.17.10-16", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "SGW to BMS_NG", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "net_4.103.17.30-32", "destination_zone": "trust", "service": "http"}, {"rule_name": "SJZT_BG_NG to YYBI", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.20-22", "destination_ip": "yybi_4.27.11.106", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SJZT_BG_NG to YYtableau", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.20-22", "destination_ip": "yyableau_3.28.10.20", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SJZT_BG_NG to yzproxy", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.20-22", "destination_ip": "proxy_3.28.10.30-31", "destination_zone": "untrust", "service": "\"YZProxy,http"}, {"rule_name": "SJZT_Internet_NG to xwproxy", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.30-32", "destination_ip": "proxy_4.35.10.51-52", "destination_zone": "untrust", "service": "TCP_8080,TCP_8081,http"}, {"rule_name": "YZ_LOG to xw_log", "action": "permit", "source_zone": "untrust", "source_ip": "net_************,net_198.3.100.94", "destination_ip": "net_4.103.17.10-16", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "logcollection to logcache", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.10-16", "destination_ip": "Range_198.3.100.53-57,Range_************-93", "destination_zone": "untrust", "service": "TCP-18088,TCP_9092"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "SJZT_BG_NG to yzsanbox", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "net_3.14.100.1-2", "destination_zone": "untrust", "service": "\"YZProxy,http"}, {"rule_name": "SJZT_BG_NG to YY_tableau", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "**********-63,net_3.14.100.11", "destination_zone": "untrust", "service": "http"}, {"rule_name": "YZ_TO_XW_SJZT-SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "net_3.14.10.31-40", "destination_ip": "net_4.103.17.10-16", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "SJZT_To_Radius", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "host_**********", "destination_zone": "untrust", "service": "TCP_1812,udp-1812"}, {"rule_name": "SJZT-FR_To_USAP-NG", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "host_***********", "destination_zone": "untrust", "service": "TCP_19080"}, {"rule_name": "SJZT-FR_To_SJZT-NG", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "host_4.35.10.10", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "USAP-NG_To_SJZT", "action": "permit", "source_zone": "untrust", "source_ip": "***********-14", "destination_ip": "net_4.103.17.21-22", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "SJZT-NG_To_FanRuan", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "host_4.35.21.10", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "SJZT-NG_To_TOMCAT", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "SJZT-TOMCAT", "destination_zone": "untrust", "service": "TCP_8040-8140"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "SJZT_NG-To-G3_HGJG", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.20-22", "destination_ip": "G3_10.194.123.10", "destination_zone": "untrust", "service": "TCP_30051"}, {"rule_name": "SJZT-NG_To_Tableau", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "host_198.3.100.97", "destination_zone": "untrust", "service": "TCP_8080,http,https"}, {"rule_name": "SJZT-NG_To_HUE", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "HUE", "destination_zone": "untrust", "service": "TCP_8888"}, {"rule_name": "SJZT_BG_NG_To_tableau", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "net_3.14.100.12-13", "destination_zone": "untrust", "service": "TCP_8080,http,https"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "SJZT-NG_To_kibana", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_5601"}, {"rule_name": "SJZT-NG_To_Grafana", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.21-22", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_30002"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "USAP_to_AOMSCLB", "action": "permit", "source_zone": "trust", "source_ip": "net_4.103.17.10-16", "destination_ip": "Range_198.3.100.62-66,host_4.35.10.10", "destination_zone": "untrust", "service": "TCP-10000,TCP-21050,TCP-2181,TCP_9092"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "YZD": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_outside", "action": "permit", "source_zone": "untrust", "source_ip": "host_18.5.127.222", "destination_ip": "host_4.103.14.100", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "DaiXiaoZhe_API", "action": "permit", "source_zone": "trust", "source_ip": "DaiXiaoZhe_nginx", "destination_ip": "host_4.24.11.70", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "ump", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "host_4.103.14.10", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "sso", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "host_4.103.14.12", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "MiGuan_To_Internet", "action": "permit", "source_zone": "trust", "source_ip": "Range_4.103.14.10-12", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "Miguan_To_*************", "action": "permit", "source_zone": "trust", "source_ip": "Miguan_4.103.14.31", "destination_ip": "*************", "destination_zone": "untrust", "service": "UDP_514"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "YHLX": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SSL-TO-LX_nginx_IN", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "LX_nginx_IN", "destination_zone": "trust", "service": "TCP_18080"}, {"rule_name": "LX_nginx_OUT-to-URL", "action": "permit", "source_zone": "trust", "source_ip": "LX_nginx_OUT", "destination_ip": "any", "destination_zone": "untrust", "service": "http,https"}, {"rule_name": "LX_nginx_DNS", "action": "permit", "source_zone": "trust", "source_ip": "LX_nginx_OUT", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "docker_4.28.10.0-TO-LX_nginx_OUT", "action": "permit", "source_zone": "untrust", "source_ip": "docker_4.28.10.0", "destination_ip": "LX_nginx_OUT", "destination_zone": "trust", "service": "TCP_28081-28090"}, {"rule_name": "LX_nginx_IN-TO-docker_treafik", "action": "permit", "source_zone": "trust", "source_ip": "LX_nginx_IN", "destination_ip": "docker_treafik,treafik_3.22.18.21-24", "destination_zone": "untrust", "service": "http"}, {"rule_name": "*******-TO-LX_nginx_OUT", "action": "permit", "source_zone": "untrust", "source_ip": "host_*******", "destination_ip": "***********-14", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "***********-4,<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "LX_nginx-TO-map", "action": "permit", "source_zone": "trust", "source_ip": "LX_nginx_OUT", "destination_ip": "map_3.24.10.60", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "LX_nginx-TO-STQD", "action": "permit", "source_zone": "trust", "source_ip": "LX_nginx_OUT", "destination_ip": "STQD_4.24.10.160", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "Ӫinx", "action": "permit", "source_zone": "untrust", "source_ip": "**********-15", "destination_ip": "LX_nginx_IN", "destination_zone": "trust", "service": "TCP_18080"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "Docker-Net_To_LX-Nginx-OUT", "action": "permit", "source_zone": "untrust", "source_ip": "docker_*********", "destination_ip": "LX_nginx_OUT", "destination_zone": "trust", "service": "TCP_28081-28090"}, {"rule_name": "Docker-201_TO_LX-Nginx-OUT", "action": "permit", "source_zone": "untrust", "source_ip": "host_***********", "destination_ip": "***********-14", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "G3_to_LX_nginx", "action": "permit", "source_zone": "untrust", "source_ip": "G3_10.194.120.0", "destination_ip": "LX_nginx_IN", "destination_zone": "trust", "service": "TCP_18080"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "TYXXFB": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_4.255.209.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-7070,TCP_10050,http"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SGW to XXFB", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "XXFB_4.103.16.0", "destination_zone": "trust", "service": "http"}, {"rule_name": "XXFB_API", "action": "permit", "source_zone": "trust", "source_ip": "***********-18,XXFB_API_4.103.16.11-14", "destination_ip": "***********,***********,***********,***********,XXFB_API_4.27.41.50", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "XXFB_seaweed_dmz-to-core-master", "action": "permit", "source_zone": "trust", "source_ip": "XXFB_seaweed_4.103.16.51-54", "destination_ip": "XXFB_seaweed_master_**********-13", "destination_zone": "untrust", "service": "TCP_17070,TCP_7070"}, {"rule_name": "XXFB_seaweed_dmz-to-core-data", "action": "permit", "source_zone": "trust", "source_ip": "XXFB_seaweed_4.103.16.51-54", "destination_ip": "XXFB_seaweed_data_4.27.41.21-24", "destination_zone": "untrust", "service": "TCP_19333,TCP_9333"}, {"rule_name": "XXFB_seaweed_core-data-to-dmz", "action": "permit", "source_zone": "untrust", "source_ip": "**********-14,XXFB_seaweed_data_4.27.41.21-24", "destination_ip": "XXFB_seaweed_4.103.16.51-54", "destination_zone": "trust", "service": "TCP_17070,TCP_7070"}, {"rule_name": "XXFB_H5", "action": "permit", "source_zone": "trust", "source_ip": "XXFB_H5_4.103.16.71-75", "destination_ip": "XXFB_H5_4.27.41.210", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "XXFB_YODA-TO-XXFB_LOG", "action": "permit", "source_zone": "untrust", "source_ip": "XXFB_YODA_4.27.41.211-216,XXFB_bifrost_4.27.41.91-96", "destination_ip": "XXFB_LOG_4.103.16.30,XXFB_LOG_4.103.16.31-32", "destination_zone": "trust", "service": "http"}, {"rule_name": "XXFB_LOG-TO-DNS", "action": "permit", "source_zone": "trust", "source_ip": "XXFB_LOG_4.103.16.31-32", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "XXFB_LOG-TO-CDN", "action": "permit", "source_zone": "trust", "source_ip": "XXFB_LOG_4.103.16.31-32", "destination_ip": "any", "destination_zone": "untrust", "service": "http,https"}, {"rule_name": "XXFB_seaweed-TO-redis", "action": "permit", "source_zone": "trust", "source_ip": "XXFB_seaweed_4.103.16.51-54", "destination_ip": "XXFB_4.27.41.61-62", "destination_zone": "untrust", "service": "TCP_26379,TCP_6379"}, {"rule_name": "XXFB_DMZ-TO-seaweed", "action": "permit", "source_zone": "untrust", "source_ip": "XXFB_3.29.4.21-22", "destination_ip": "XXFB_seaweed_4.103.16.50,XXFB_seaweed_4.103.16.51-54", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "XXFB_relay-TO-live", "action": "permit", "source_zone": "trust", "source_ip": "relay_4.103.16.41-42", "destination_ip": "live_3.20.1.201-202", "destination_zone": "untrust", "service": "TCP_1935"}, {"rule_name": "CDN-TO-rtmp", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "XXFB_4.103.16.40", "destination_zone": "trust", "service": "TCP_1935"}, {"rule_name": "CDN-TO-tstatic", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "***********,XXFB_4.103.16.20", "destination_zone": "trust", "service": "http"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "radius  policy logging", "action": "permit", "source_zone": "trust", "source_ip": "XXFB_4.103.16.0", "destination_ip": "any", "destination_zone": "untrust", "service": "radius"}, {"rule_name": "nginx_XXFB-to-XXFBURL", "action": "permit", "source_zone": "trust", "source_ip": "nginx_XXFB", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_5671,http,https"}, {"rule_name": "nginx_XXFB-to-DNS", "action": "permit", "source_zone": "trust", "source_ip": "nginx_XXFB", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "G3_to_XXFB", "action": "permit", "source_zone": "untrust", "source_ip": "G3_10.194.120.0", "destination_ip": "***********", "destination_zone": "trust", "service": "TCP-11671,TCP-30205,TCP_11666,TCP_30201,TCP_30202,TCP_30203,TCP_30204,TCP_5671"}, {"rule_name": "XXFB_relay-TO-DNS", "action": "permit", "source_zone": "trust", "source_ip": "relay_4.103.16.41-42", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "XXFB_relay-TO-rtmp", "action": "permit", "source_zone": "trust", "source_ip": "relay_4.103.16.41-42", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "DaPing-TO-rtmp", "action": "permit", "source_zone": "untrust", "source_ip": "DaPing_Address", "destination_ip": "XXFB_4.103.16.40", "destination_zone": "trust", "service": "http"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "Monitor_to_XXFB", "action": "permit", "source_zone": "untrust", "source_ip": "Range_************-55", "destination_ip": "***********", "destination_zone": "trust", "service": "TCP_30203"}, {"rule_name": "kfpt_to_hulianwang", "action": "permit", "source_zone": "trust", "source_ip": "***********-94", "destination_ip": "**********", "destination_zone": "untrust", "service": "TCP_8080,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "KFPT": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "permit DNS", "action": "permit", "source_zone": "trust", "source_ip": "************/32,************/32,G3_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>x", "destination_ip": "any", "destination_zone": "untrust", "service": "dns,https"}, {"rule_name": "G3_Ying<PERSON>iao to G3_KaiFang_Nginx", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.28.10.0/24", "destination_ip": "host_************", "destination_zone": "trust", "service": "TCP-11671,TCP_10031,TCP_11666-11670,TCP_30201,tcp-30101,tcp-30103,tcp-30104"}, {"rule_name": "Nginx to Internet", "action": "permit", "source_zone": "trust", "source_ip": "G3_KaiFangPingTai_Nginx", "destination_ip": "_106.14.144.12", "destination_zone": "untrust", "service": "TCP-18081,TCP-4321,TCP_5017,http,https,tcp-8743"}, {"rule_name": "TiCaiGuanJia", "action": "permit", "source_zone": "untrust", "source_ip": "group_4.24.11.71-74", "destination_ip": "host_************,nginx_4.103.13.201-202", "destination_zone": "trust", "service": "TCP_12001"}, {"rule_name": "internet to SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SFTP_4.103.13.210", "destination_zone": "trust", "service": "TCP_30000"}, {"rule_name": "ZFK8S to nginx", "action": "permit", "source_zone": "untrust", "source_ip": "************/24,********/24", "destination_ip": "************/32", "destination_zone": "trust", "service": "http,https,tcp-1001-1005,tcp-30000,tcp-55382,tcp-8090,tcp-8443,tcp-9999"}, {"rule_name": "ZF Nginx to Internet", "action": "permit", "source_zone": "trust", "source_ip": "************/32,************/32", "destination_ip": "any", "destination_zone": "untrust", "service": "http,https,tcp-30000,tcp-55382,tcp-8090,tcp-8443,tcp-9999"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "XXFB-TO-KFPT_SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "**********-24,XXFB_bifrost_4.27.41.91-96", "destination_ip": "SFTP_4.103.13.210", "destination_zone": "trust", "service": "tcp-30000"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "ƽ̨", "action": "permit", "source_zone": "untrust", "source_ip": "*********-47", "destination_ip": "any", "destination_zone": "trust", "service": "tcp-30102"}, {"rule_name": "ʵ̨nginx", "action": "permit", "source_zone": "untrust", "source_ip": "**********-88,**********-28,**********-38,**********-44", "destination_ip": "************-202", "destination_zone": "trust", "service": "tcp-31002"}, {"rule_name": "SGW_to_KFPT-openapi", "action": "permit", "source_zone": "untrust", "source_ip": "Range_************-55,SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_7001"}, {"rule_name": "nginx_to_apiserver", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_7001"}, {"rule_name": "STQD-GM_to_KFPT-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "************-202", "destination_zone": "trust", "service": "TCP_31003"}, {"rule_name": "YXZX-NG_TO_KFPT-NG", "action": "permit", "source_zone": "untrust", "source_ip": "nginx_**********-15", "destination_ip": "************-202", "destination_zone": "trust", "service": "TCP_10024,TCP_10029,TCP_10030,TCP_10032,TCP_10033,TCP_30108,TCP_30109,tcp-30102,tcp-30105,tcp-31004,tcp-31005"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "STQD-DXZJK_to_KFPT-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "group_4.24.11.71-78", "destination_ip": "************-202", "destination_zone": "trust", "service": "TCP_12001,TCP_31002"}, {"rule_name": "SJZT_to_KFPT-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "************-202", "destination_zone": "trust", "service": "TCP_31101"}, {"rule_name": "G3YPT_to_KFPT-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "************-98,net_10.194.0.0/16", "destination_ip": "host_************", "destination_zone": "trust", "service": "TCP_20001,TCP_30000-31000,TCP_31002,TCP_31201,TCP_31202,TCP_31301,TCP_31302,TCP_32000"}, {"rule_name": "STQD_to_KFPT-NG", "action": "permit", "source_zone": "untrust", "source_ip": "***********-184", "destination_ip": "************-202", "destination_zone": "trust", "service": "TCP_31002"}, {"rule_name": "KFPT-NG_to_clb-pt-oap", "action": "permit", "source_zone": "trust", "source_ip": "KFPT_4.103.13.21-22", "destination_ip": "G3_10.194.119.8", "destination_zone": "untrust", "service": "http"}, {"rule_name": "UMP_to_KFPT-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "**********-24,css,css_api", "destination_ip": "host_************", "destination_zone": "trust", "service": "TCP_33000"}, {"rule_name": "Monitor_to_KFPT-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "Range_************-55", "destination_ip": "************-202,host_************", "destination_zone": "trust", "service": "TCP_11668,TCP_11669-11670,TCP_12001,TCP_30108,TCP_30203,tcp-30101,tcp-30102,tcp-30103,tcp-30104,tcp-31004,tcp-31005"}, {"rule_name": "G3-KFPT-NG_To_CCRS", "action": "permit", "source_zone": "trust", "source_ip": "G3_KaiFangPingTai_Nginx", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP-8890"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,http,tcp-8443"}, {"rule_name": "G3_to_KFPT", "action": "permit", "source_zone": "untrust", "source_ip": "G3_10.194.120.0", "destination_ip": "host_************", "destination_zone": "trust", "service": "TCP_10024,TCP_10029,TCP_10030,TCP_10031,TCP_10032,TCP_10033,TCP_10034,TCP_10035,TCP_10036,TCP_11666-11670,TCP_30108,TCP_30109,tcp-30101,tcp-30102,tcp-30103,tcp-30104,tcp-30105"}, {"rule_name": "kfpt_to_nginx", "action": "permit", "source_zone": "untrust", "source_ip": "*********/24,G3_10.194.119.0", "destination_ip": "G3_KaiFangPingTai_Nginx", "destination_zone": "trust", "service": "TCP_32201,TCP_32202"}, {"rule_name": "Nginx_to_JuncaiInternet", "action": "permit", "source_zone": "trust", "source_ip": "G3_KaiFangPingTai_Nginx", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_32201,TCP_8083,TCP_8084"}, {"rule_name": "radius", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "**********", "destination_zone": "untrust", "service": "udp-1812"}, {"rule_name": "G3-KFPT-NG_To_QYHG", "action": "permit", "source_zone": "trust", "source_ip": "G3_KaiFangPingTai_Nginx", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8802"}, {"rule_name": "WXGGH-TO-KFPT_SFTP", "action": "permit", "source_zone": "untrust", "source_ip": "***********/24,***********,**********-50", "destination_ip": "SFTP_4.103.13.210", "destination_zone": "trust", "service": "any"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "BlackBox_TO-NG", "action": "permit", "source_zone": "untrust", "source_ip": "*********-12", "destination_ip": "************-202", "destination_zone": "trust", "service": "TCP_8083"}, {"rule_name": "boce_to_KFPT-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "***********/24", "destination_ip": "nginx_4.103.13.201-202", "destination_zone": "trust", "service": "TCP_11668,TCP_30108,TCP_31003,tcp-30102,tcp-30104"}, {"rule_name": "20231207_nginx_to_kfpt", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "CSLP": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "**********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "**********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SGW to cslp_ildg", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "ildg_vip_4.103.18.10", "destination_zone": "trust", "service": "TCP_8010"}, {"rule_name": "cslp_tool to cslp_ildg", "action": "permit", "source_zone": "untrust", "source_ip": "cslp_tool_3.12.41.50", "destination_ip": "ildg_4.103.18.10-12", "destination_zone": "trust", "service": "TCP_8010"}, {"rule_name": "cslp_ctrix to cslp_ildg", "action": "permit", "source_zone": "untrust", "source_ip": "cslp_ctrix_3.26.10.101-104", "destination_ip": "ildg_4.103.18.10-12", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "ildg to web", "action": "permit", "source_zone": "trust", "source_ip": "ildg_4.103.18.10-12", "destination_ip": "host_4.20.50.50,ildg_web_4.20.50.31-32", "destination_zone": "untrust", "service": "TCP_8010"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "USAP": [{"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "soc-to-trust", "action": "permit", "source_zone": "any", "source_ip": "soc_***********/24", "destination_ip": "any", "destination_zone": "any", "service": "TCP_8890,TCP_8891,https,icmp,snmp,ssh"}, {"rule_name": "trust-to-soc", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "soc_***********/24", "destination_zone": "any", "service": "TCP_8999,rdp-tcp,rdp-udp,snmptrap,syslog"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "SGW to nginx", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "USAP_4.103.19.0", "destination_zone": "trust", "service": "http"}, {"rule_name": "USAPMatserver", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-7004"}, {"rule_name": "nginx_WX-to-out", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_WX", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "nginx_WX-to-URL", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_WX", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "nginx_WX-to-DNS", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_WX", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "service-to-nginx_vip", "action": "permit", "source_zone": "trust,untrust", "source_ip": "USAP_Service,USAP_nginx_Internet", "destination_ip": "USAP_Service,USAP_nginx_Internet_VIP", "destination_zone": "trust,untrust", "service": "TCP_10080,TCP_10081,TCP_9080,http"}, {"rule_name": "ͳһnginx", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "**********-74", "destination_zone": "untrust", "service": "TCP_9080,http"}, {"rule_name": "USAP_nginxӦάϵͳ", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-18080"}, {"rule_name": "USAP_nginxƽ̨", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-8081"}, {"rule_name": "USAP_nginxϵ", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-8089"}, {"rule_name": "USAP_nginxAW&YY&JK&SJZT&XXFB", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "USAP_nginx֧  source-zone trust", "action": "permit", "source_zone": "any", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-54106"}, {"rule_name": "USAP_nginx", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_9080,tcp-9090"}, {"rule_name": "USAP_nginxͳһϵͳ", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "**********-47", "destination_zone": "untrust", "service": "http"}, {"rule_name": "USAP_nginxϵͳ", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-8087"}, {"rule_name": "USAP_nginxӪѯ", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-8001"}, {"rule_name": "USAP_nginx", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "*********-9", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "ϵͳUSAP_nginx", "action": "permit", "source_zone": "untrust", "source_ip": "systems\"", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "USAP_nginx-TO-XXFB", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "XXFB_4.27.41.130-133", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "USAP_to_nginx", "action": "permit", "source_zone": "untrust", "source_ip": "USAP_Service", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "TCP_10082"}, {"rule_name": "USAP_nginx", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "USAP-NG_TO_KFPT", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "**********", "destination_zone": "untrust", "service": "TCP_7001"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "USAPG2MATServer", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP-7004"}, {"rule_name": "USAPTableuau  source-zone trust", "action": "permit", "source_zone": "any", "source_ip": "***********-14", "destination_ip": "**********-63", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "ump-normal_to_usap-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_19080"}, {"rule_name": "usap-nginx_to_ump-normal", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8800"}, {"rule_name": "SGW_to_fnc", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "fncNg_to_fncApp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "USAP_to_<PERSON>obiao", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "***********-104", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "Baobiao_to_USAP", "action": "permit", "source_zone": "untrust", "source_ip": "***********-104", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "USAP-NG_TO_Docker-NG", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "Docker_TO_USAP-NG", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "G3_to_USAP", "action": "permit", "source_zone": "untrust", "source_ip": "************-55,G3_10.194.119.0", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "TCP_10080,TCP_10081,TCP_10082,tcp-19080"}, {"rule_name": "USAP_to_G3", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "G3_10.194.119.12,G3_10.194.120.76", "destination_zone": "untrust", "service": "TCP_30001,TCP_30010"}, {"rule_name": "USAPTableuau", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "G3_to_USAPVIP", "action": "permit", "source_zone": "untrust", "source_ip": "G3_10.194.120.0", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "USAPNG_to_clb-bp-ipc-01", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "G3_10.194.120.57", "destination_zone": "untrust", "service": "TCP_30080"}, {"rule_name": "USAP_to_YZDIP", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "USAP_to_XWDIP", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "tcp-8087,tcp-8089"}, {"rule_name": "YZDIP_to_USAPVIP", "action": "permit", "source_zone": "untrust", "source_ip": "************/24,***********/24,Host_4.60.8.5,Node_************,host_4.14.100.53", "destination_ip": "any", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "XWDIP_to_USAP", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "Service_To_USAP-nginx-Internet", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "**********-42,host_4.14.100.53", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "USAP-nginx-Internet_To_STQD", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "host_4.24.11.10,host_4.24.11.180", "destination_zone": "untrust", "service": "TCP_8330"}, {"rule_name": "STQD_to_USAPVIP", "action": "permit", "source_zone": "untrust", "source_ip": "STQD_4.24.11.11-14,STQD_***********-184,STQD_**********-28,STQD_**********-38,STQD_**********-44", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "USAPNG_to_clb-pt-oap-mgr", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "G3_10.194.119.11", "destination_zone": "untrust", "service": "http"}, {"rule_name": "USAP-NG_To_FanRuan", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "host_4.35.21.10,net_4.103.17.21-22", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "FanRuan_to_USAP", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.35.21.10,net_4.103.17.21-22", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080,tcp-8080"}, {"rule_name": "CSLJC-GW-K8S_to_USAP", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.190.44.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "USAPNG_to_CSLJC-BOS-WCSROUTE-F5", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "CSLJC_4.190.161.1", "destination_zone": "untrust", "service": "TCP_8088"}, {"rule_name": "ZGWS_To_USAP-NG", "action": "permit", "source_zone": "untrust", "source_ip": "ZGWS_3.22.10.3-4,host_3.22.10.116", "destination_ip": "USAP_nginx_Internet,USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "TCP_19080"}, {"rule_name": "USAP-NG_To_ZGWS", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "ZGWS_3.22.10.3-4,host_3.22.10.116", "destination_zone": "untrust", "service": "TCP_6002"}, {"rule_name": "USAP-nginx-Internet_To_JC-DAS", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "host_4.190.166.2", "destination_zone": "untrust", "service": "TCP_8021"}, {"rule_name": "JC-DAS_to_USAPVIP", "action": "permit", "source_zone": "untrust", "source_ip": "*************-253", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "USAP-NG_To_STQD-CLB", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "Node_************,host_10.194.123.17", "destination_zone": "untrust", "service": "TCP_30000,TCP_30001,TCP_31001,TCP_31011"}, {"rule_name": "USAP-NG_To_JK-CLB", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "host_10.194.123.14", "destination_zone": "untrust", "service": "TCP_30500"}, {"rule_name": "JK-<PERSON>de_To_USAP-NG", "action": "permit", "source_zone": "trust", "source_ip": "Node_************", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "untrust", "service": "http,tcp-19080"}, {"rule_name": "USAP_NG_To_JKED", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "JKED_F5_VIP", "destination_zone": "untrust", "service": "TCP-8090"}, {"rule_name": "JKED_To_USAP_NG", "action": "permit", "source_zone": "untrust", "source_ip": "**********-52,JKED_Internet,host_4.13.70.32", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "TCP_19080"}, {"rule_name": "USAP-NG_To_JKDG", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "host_4.20.50.50", "destination_zone": "untrust", "service": "TCP_8010"}, {"rule_name": "JK-<PERSON>de_To_USAP-NG-1", "action": "permit", "source_zone": "untrust", "source_ip": "Node_************", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "tcp-19080"}, {"rule_name": "USAP-NG_To_basmpa", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "host_10.194.123.48", "destination_zone": "untrust", "service": "TCP_30222,TCP_30224,TCP_30227,TCP_30501"}, {"rule_name": "USAP-NG_To_SOM-CLB", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "G3_10.194.123.22", "destination_zone": "untrust", "service": "TCP_30011,TCP_30017,tcp-8080"}, {"rule_name": "USAP-NG_To_dgsdam", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "G3_10.194.123.30", "destination_zone": "untrust", "service": "TCP_30220"}, {"rule_name": "USAP-NG_To_rpt", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "G3_10.194.123.35", "destination_zone": "untrust", "service": "TCP_30221"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8002,TCP_8443,http,tcp-8001"}, {"rule_name": "USAP_To_bpmca-CLB", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_30223,TCP_30225,TCP_30226"}, {"rule_name": "USAP-NG_To_clb-sf-csm", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "G3_10.194.123.26", "destination_zone": "untrust", "service": "TCP_30058"}, {"rule_name": "radius", "action": "permit", "source_zone": "trust", "source_ip": "USAP_4.103.19.0", "destination_ip": "**********", "destination_zone": "untrust", "service": "udp-1812"}, {"rule_name": "USAP_nginx_to_kfgd", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "**********", "destination_zone": "untrust", "service": "tcp-8080"}, {"rule_name": "kfgd_to_usap", "action": "permit", "source_zone": "untrust", "source_ip": "**********-12", "destination_ip": "USAP_nginx_Internet_VIP", "destination_zone": "trust", "service": "TCP_19080"}, {"rule_name": "USAP-NG_To_of-fns-clb", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_30023"}, {"rule_name": "USAP_to_AOMSCLB", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "*************", "destination_zone": "untrust", "service": "TCP-30021"}, {"rule_name": "USAP-NG_To_clb-of-ids", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_30024"}, {"rule_name": "USAP-NG_To_clb-cf-pms-01", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_30101"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8002,TCP_8443,http,https"}, {"rule_name": "USAP-Fengxianfangkong", "action": "permit", "source_zone": "trust", "source_ip": "***********-14", "destination_ip": "*************", "destination_zone": "untrust", "service": "TCP_30510"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "20231207_nginx_to_kfptMG", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "20231207_aliKFPT_to_usap-nginx", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_19080"}, {"rule_name": "tableau-to-usap", "action": "permit", "source_zone": "untrust", "source_ip": "tableau", "destination_ip": "USAP_4.103.19.0", "destination_zone": "trust", "service": "TCP_19080,icmp"}, {"rule_name": "usap-to-tableau", "action": "permit", "source_zone": "trust", "source_ip": "USAP_nginx_Internet", "destination_ip": "tableau", "destination_zone": "untrust", "service": "http,icmp"}], "WSJC": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "soc-to-trust", "action": "permit", "source_zone": "any", "source_ip": "soc_***********/24", "destination_ip": "any", "destination_zone": "any", "service": "TCP_8890,TCP_8891,https,icmp,snmp,ssh"}, {"rule_name": "trust-to-soc", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "soc_***********/24", "destination_zone": "any", "service": "TCP_8999,rdp-tcp,rdp-udp,snmptrap,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "nginx_to_k8s", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "*********/24", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW_to_WSJC-NGINX", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "http"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "Docker_to_WS-NG", "action": "permit", "source_zone": "untrust", "source_ip": "*********/24,**********-**********,**********-**********", "destination_ip": "***********-***********", "destination_zone": "trust", "service": "TCP-10005,TCP-10009,TCP-10010,TCP-10011,TCP-10020,TCP-10021,TCP-10022"}, {"rule_name": "WS-NG_to_URL", "action": "permit", "source_zone": "trust", "source_ip": "***********-***********", "destination_ip": "any", "destination_zone": "untrust", "service": "https"}, {"rule_name": "WS-NG_to_DNS", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "nginx_to_WS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_5222,TCP_7070,TCP_8999,http"}, {"rule_name": "Nginx_to_JueCe", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********-119,host_3.22.10.116,host_3.22.10.3", "destination_zone": "untrust", "service": "http"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "QKL": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "soc-to-trust", "action": "permit", "source_zone": "any", "source_ip": "soc_***********/24", "destination_ip": "any", "destination_zone": "any", "service": "TCP_8890,TCP_8891,https,icmp,snmp,ssh"}, {"rule_name": "trust-to-soc", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "soc_***********/24", "destination_zone": "any", "service": "TCP_8999,rdp-tcp,rdp-udp,snmptrap,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "QKL_PROX-TO-DMZ", "action": "permit", "source_zone": "untrust", "source_ip": "QKL_4.27.31.2-3", "destination_ip": "QKL_4.103.21.11-12", "destination_zone": "trust", "service": "https"}, {"rule_name": "QKL_DMZ-TO-TEST", "action": "permit", "source_zone": "trust", "source_ip": "QKL_4.103.21.11-12", "destination_ip": "TEST_111.205.92.112", "destination_zone": "untrust", "service": "https"}, {"rule_name": "<PERSON><PERSON>", "action": "permit", "source_zone": "untrust", "source_ip": "<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Security_to_TaiShiGanZhi", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8444"}, {"rule_name": "Security_to_duplex", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "any"}, {"rule_name": "TaiShiGanZhi_to_duplex", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "CSLJC": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "juncai_to_office_mail", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "smtp"}, {"rule_name": "internet_to_juncai", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_28080,TCP_28081,TCP_28082"}, {"rule_name": "juncai_to_internetDNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "juncai_to_internet", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "http,https"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "internet_to_sdas", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8023,TCP_8024,TCP_8443,TCP_9443,https"}, {"rule_name": "juncai_to_getSaleData", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_2181,TCP_7070"}, {"rule_name": "USAPNG_to_CSLJC-BOS-WCSROUTE-F5", "action": "permit", "source_zone": "untrust", "source_ip": "***********-14", "destination_ip": "CSLJC_4.190.161.1", "destination_zone": "trust", "service": "TCP_8088"}, {"rule_name": "JC-BOSROUTER_TO_BOS-WCSROUTE", "action": "permit", "source_zone": "untrust", "source_ip": "Range_4.103.120.41-42", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_8088"}, {"rule_name": "juncai_to_office_mail-1", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "smtp"}, {"rule_name": "jc_4.103.120.41_42", "action": "permit", "source_zone": "trust", "source_ip": "4.190", "destination_ip": "4.103.120.41_42", "destination_zone": "untrust", "service": "ssh"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "TICAIAPP": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "soc_to_trust", "action": "permit", "source_zone": "any", "source_ip": "soc_***********/24", "destination_ip": "any", "destination_zone": "any", "service": "TCP_8890,TCP_8891,https,icmp,snmp,ssh"}, {"rule_name": "trust_to_soc", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "soc_***********/24", "destination_zone": "any", "service": "TCP_8999,rdp-tcp,snmptrap,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SGW_to_Nginx", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Nginx_to_ticket", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "http"}, {"rule_name": "Nginx_to_appServer", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_8080"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "EDR_DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "YJVPN": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "Any-VPN_VS", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "VPN_VS", "destination_zone": "trust", "service": "TCP-8777,UDP-8778"}, {"rule_name": "VPN_To_SYS", "action": "permit", "source_zone": "trust", "source_ip": "***********-3,***********6/28", "destination_ip": "**********-42", "destination_zone": "untrust", "service": "TCP-1812,TCP-1813,UDP-1812,UDP-1813"}, {"rule_name": "EDR", "action": "permit", "source_zone": "trust", "source_ip": "************/28", "destination_ip": "************/32", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443"}, {"rule_name": "GTM", "action": "permit", "source_zone": "trust", "source_ip": "************/28", "destination_ip": "**********/32", "destination_zone": "untrust", "service": "dns,dns-tcp"}, {"rule_name": "OCS_out", "action": "permit", "source_zone": "trust", "source_ip": "************/28", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_10102,https"}, {"rule_name": "OCS_in", "action": "permit", "source_zone": "untrust", "source_ip": "**********/24", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "OCS_out2", "action": "permit", "source_zone": "trust", "source_ip": "************/28", "destination_ip": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>,OCS_yukong", "destination_zone": "untrust", "service": "389,TCP-3389"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}]}
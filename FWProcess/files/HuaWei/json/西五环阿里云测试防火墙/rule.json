{"default": [{"rule_name": "主备心跳", "action": "permit", "source_zone": "DAD,local", "source_ip": "any", "destination_ip": "any", "destination_zone": "DAD,local", "service": "udp,18514"}, {"rule_name": "icmp", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "icmp"}, {"rule_name": "tmp-vdi-to-manage1", "action": "permit", "source_zone": "untrust", "source_ip": "**********/16", "destination_ip": "*********/16", "destination_zone": "trust", "service": "any"}, {"rule_name": "tmp-juncai-to-manage1", "action": "permit", "source_zone": "untrust", "source_ip": "********/8", "destination_ip": "*********/16", "destination_zone": "trust", "service": "any"}, {"rule_name": "tmp-to-testVPC", "action": "permit", "source_zone": "untrust", "source_ip": "**********/16,********/8", "destination_ip": "*********/16,*********/16", "destination_zone": "trust", "service": "any"}, {"rule_name": "Coding_to_AliCloud", "action": "permit", "source_zone": "untrust", "source_ip": "**********/24,**********/24", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "g3_jiankong01", "action": "permit", "source_zone": "untrust", "source_ip": "************-103,*************-12,***********-40", "destination_ip": "*********/16", "destination_zone": "trust", "service": "http"}, {"rule_name": "g3_jiankong02", "action": "permit", "source_zone": "untrust", "source_ip": "************-95", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "g3_jiankong03", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "**************", "destination_zone": "untrust", "service": "http,tcp_30030-30040"}, {"rule_name": "信创OA测试", "action": "permit", "source_zone": "trust", "source_ip": "*************-227", "destination_ip": "*************", "destination_zone": "untrust", "service": "tcp_2883"}, {"rule_name": "信创OA漏洞扫描", "action": "permit", "source_zone": "untrust", "source_ip": "************", "destination_ip": "*************-227", "destination_zone": "trust", "service": "any"}, {"rule_name": "信创OA测试01", "action": "permit", "source_zone": "untrust", "source_ip": "***********", "destination_ip": "*************", "destination_zone": "trust", "service": "http,https,tcp_8888"}, {"rule_name": "灾备云到测试OB", "action": "permit", "source_zone": "trust", "source_ip": "*********/18", "destination_ip": "***************,***************,***************,*************** ***************", "destination_zone": "untrust", "service": "2883"}, {"rule_name": "灾备云生产到竞猜", "action": "permit", "source_zone": "trust", "source_ip": "*********/16", "destination_ip": "***********", "destination_zone": "untrust", "service": "http"}, {"rule_name": "灾备云生产到LDAP", "action": "permit", "source_zone": "trust", "source_ip": "*********/16", "destination_ip": "**************,**************,************* *************", "destination_zone": "untrust", "service": "tcp_389,tcp_6006"}, {"rule_name": "等保测评", "action": "permit", "source_zone": "untrust", "source_ip": "*************/32,************-54", "destination_ip": "*********/16", "destination_zone": "trust", "service": "any"}, {"rule_name": "云上DNS到云下DNS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***************,**************", "destination_zone": "untrust", "service": "dns,dns-tcp"}, {"rule_name": "云上DNS到云下DNStmp", "action": "permit", "source_zone": "untrust", "source_ip": "**************", "destination_ip": "any", "destination_zone": "trust", "service": "dns,dns-tcp"}, {"rule_name": "20231103_cloudToAD", "action": "permit", "source_zone": "trust", "source_ip": "*************,***********", "destination_ip": "************* *************", "destination_zone": "untrust", "service": "tcp_389,tcp_636"}, {"rule_name": "20231103_cloudToDoubleFactor", "action": "permit", "source_zone": "trust", "source_ip": "*************,***********", "destination_ip": "*************", "destination_zone": "untrust", "service": "tcp_1821,udp_1812"}, {"rule_name": "20231103_cloudToMonitor", "action": "permit", "source_zone": "trust", "source_ip": "**********", "destination_ip": "************", "destination_zone": "untrust", "service": "ssh,syslog"}, {"rule_name": "20240108_cloudToMonitor", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "************", "destination_zone": "untrust", "service": "syslog"}, {"rule_name": "20231103_usap-nginx-to-cloud", "action": "permit", "source_zone": "untrust", "source_ip": "***********", "destination_ip": "*********", "destination_zone": "trust", "service": "http"}, {"rule_name": "20231103_cloud-to-usap-nginx", "action": "permit", "source_zone": "trust", "source_ip": "*********/18", "destination_ip": "***********", "destination_zone": "untrust", "service": "tcp_9080"}, {"rule_name": "20231103_cloud-to-passwordservice", "action": "permit", "source_zone": "trust", "source_ip": "*********/18", "destination_ip": "*************,**************", "destination_zone": "untrust", "service": "tcp_10006"}, {"rule_name": "20231103_cloud-to-shujuzhongtai", "action": "permit", "source_zone": "trust", "source_ip": "*********/18", "destination_ip": "************,************,************", "destination_zone": "untrust", "service": "tcp_8080,tcp_8089"}, {"rule_name": "20231103_usap-to-nginx-cloud", "action": "permit", "source_zone": "untrust", "source_ip": "***********", "destination_ip": "*********,*********", "destination_zone": "trust", "service": "http"}, {"rule_name": "20231103_cloud-to-usap-nginx2", "action": "permit", "source_zone": "trust", "source_ip": "*********/18", "destination_ip": "***********", "destination_zone": "untrust", "service": "tcp_30010,tcp_8081"}, {"rule_name": "20231128_开放平台压测tmp01", "action": "permit", "source_zone": "untrust", "source_ip": "***********,***********,*********** ***********", "destination_ip": "*********,*********", "destination_zone": "trust", "service": "http"}, {"rule_name": "20231128_开放平台压测tmp02", "action": "permit", "source_zone": "trust", "source_ip": "*********** ***********", "destination_ip": "***************,************", "destination_zone": "untrust", "service": "tcp_2883,tcp_9090"}, {"rule_name": "20231128_开放平台测试", "action": "permit", "source_zone": "trust", "source_ip": "*********** ***********", "destination_ip": "any", "destination_zone": "untrust", "service": "any"}]}
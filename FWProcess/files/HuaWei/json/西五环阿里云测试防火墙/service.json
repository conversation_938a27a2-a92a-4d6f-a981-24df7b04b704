{"default": [{"name": "2883", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2883"}, {"protocol": "udp", "destination-port": "2883"}]}, {"name": "tcp_10006", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10006"}]}, {"name": "tcp_1812", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1812"}]}, {"name": "tcp_1821", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1821"}]}, {"name": "tcp_2883", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2883"}]}, {"name": "tcp_30010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30010"}]}, {"name": "tcp_30030-30040", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30040"}]}, {"name": "tcp_389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "389"}]}, {"name": "tcp_6006", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6006"}]}, {"name": "tcp_636", "type": "object", "service": [{"protocol": "tcp", "destination-port": "636"}]}, {"name": "tcp_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "tcp_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "tcp_8089", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8089"}]}, {"name": "tcp_8888", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8888"}]}, {"name": "tcp_9080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9080"}]}, {"name": "tcp_9090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9090"}]}, {"name": "udp_1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}]}
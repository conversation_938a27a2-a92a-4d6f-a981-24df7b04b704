{"default": [], "Core": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}], "Normal": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}], "Others": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}], "ILMS": [{"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_20050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20050"}]}, {"name": "TCP_6379", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6379"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "udp-1812", "type": "object", "service": [{"protocol": "udp", "destination-port": "1812"}]}], "SGW": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "TCP_389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "389"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_8500", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8500"}]}, {"name": "TCP_8600", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8600"}]}, {"name": "TCP_20050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20050"}]}, {"name": "TCP_8091", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8091"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}], "WSJC": [{"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}], "description": "Zabbix"}, {"name": "TCP_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "NAS service port", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}, {"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}, {"protocol": "tcp", "destination-port": "4046"}, {"protocol": "udp", "destination-port": "4046"}, {"protocol": "tcp", "destination-port": "635"}, {"protocol": "udp", "destination-port": "635"}]}, {"name": "TCP_4505", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4505"}]}, {"name": "TCP_4506", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4506"}]}, {"name": "TCP_10001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10001"}]}, {"name": "TCP_10002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10002"}]}, {"name": "TCP_10003", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10003"}]}, {"name": "TCP_10004", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10004"}]}, {"name": "TCP_10005", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10005"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP-8890", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8890"}]}, {"name": "TCP-8891", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8891"}]}, {"name": "TCP-8999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8999"}]}, {"name": "TCP_10006", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10006"}]}, {"name": "tcp-139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "tcp-3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP_10009", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10009"}]}, {"name": "TCP_10007", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10007"}]}, {"name": "TCP_10008", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10008"}]}, {"name": "TCP_18081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18081"}]}, {"name": "TCP_10010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10010"}]}, {"name": "TCP_10012", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10012"}]}, {"name": "TCP_10013", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10013"}]}, {"name": "TCP_6677", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6677"}]}, {"name": "TCP_7788", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7788"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_8002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8002"}]}, {"name": "TCP_8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}]}
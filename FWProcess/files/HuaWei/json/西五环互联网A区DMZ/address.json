{"default": [{"address_name": "ntp_*******", "type": "object", "addresses": [{"ip": "*******/32"}]}, {"address_name": "jiankong", "type": "object", "addresses": [{"ip": "***********/32"}, {"ip": "************/32"}, {"ip": "************/32"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/32"}]}], "Core": [{"address_name": "host_*********", "type": "object", "addresses": [{"ip": "*********/32"}], "description": "sytem ops"}, {"address_name": "CIMS_Servers", "type": "object", "addresses": [{"range": "********** **********"}, {"range": "**********1 **********0"}, {"range": "*********** ***********"}, {"range": "***********1 ***********0"}, {"range": "************ ************"}, {"ip": "*********/24"}, {"range": "*********** ***********"}, {"ip": "***********/32"}]}, {"address_name": "Zabbix_JianKong", "type": "object", "addresses": [{"range": "************ ************"}, {"range": "************1 ************2"}, {"ip": "************8/32"}, {"range": "************ ************"}, {"ip": "***********/24"}]}, {"address_name": "NTP_Server", "type": "object", "addresses": [{"ip": "*******/32"}, {"ip": "*******/32"}]}, {"address_name": "YUM Server", "type": "object", "addresses": [{"ip": "*************/32"}, {"ip": "***********/32"}]}, {"address_name": "NAS_*********", "type": "object", "addresses": [{"ip": "*********/32"}]}, {"address_name": "Saltstack Master", "type": "object", "addresses": [{"ip": "************/32"}, {"ip": "************/32"}]}, {"address_name": "net_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "NVS", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "************-************", "type": "object", "addresses": [{"range": "************ ************"}]}, {"address_name": "***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "************", "type": "object", "addresses": [{"ip": "************/32"}]}, {"address_name": "*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "*********", "type": "object", "addresses": [{"ip": "*********/16"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/24"}]}], "Normal": [{"address_name": "host_*********", "type": "object", "addresses": [{"ip": "*********/32"}], "description": "sytem ops"}, {"address_name": "CIMS_Servers", "type": "object", "addresses": [{"range": "********** **********"}, {"range": "**********1 **********0"}, {"range": "*********** ***********"}, {"range": "***********1 ***********0"}, {"range": "************ ************"}, {"range": "*********** ***********"}, {"ip": "***********/32"}]}, {"address_name": "Zabbix_JianKong", "type": "object", "addresses": [{"range": "************ ************"}, {"range": "************1 ************2"}, {"ip": "************8/32"}, {"range": "************ ************"}, {"ip": "***********/24"}]}, {"address_name": "NTP_Server", "type": "object", "addresses": [{"ip": "*******/32"}, {"ip": "*******/32"}]}, {"address_name": "YUM Server", "type": "object", "addresses": [{"ip": "*************/32"}, {"ip": "***********/32"}]}, {"address_name": "NAS_*********", "type": "object", "addresses": [{"ip": "*********/32"}]}, {"address_name": "Saltstack Master", "type": "object", "addresses": [{"ip": "************/32"}, {"ip": "************/32"}]}, {"address_name": "net_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "NVS", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "************-************", "type": "object", "addresses": [{"range": "************ ************"}]}, {"address_name": "*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "*********", "type": "object", "addresses": [{"ip": "*********/16"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/24"}]}], "Others": [{"address_name": "host_*********", "type": "object", "addresses": [{"ip": "*********/32"}], "description": "sytem ops"}, {"address_name": "CIMS_Servers", "type": "object", "addresses": [{"range": "********** **********"}, {"range": "**********1 **********0"}, {"range": "*********** ***********"}, {"range": "***********1 ***********0"}, {"range": "************ ************"}, {"range": "*********** ***********"}, {"ip": "***********/32"}]}, {"address_name": "Zabbix_JianKong", "type": "object", "addresses": [{"range": "************ ************"}, {"range": "************1 ************2"}, {"ip": "************8/32"}, {"range": "************ ************"}, {"ip": "***********/24"}]}, {"address_name": "NTP_Server", "type": "object", "addresses": [{"ip": "*******/32"}, {"ip": "*******/32"}]}, {"address_name": "YUM Server", "type": "object", "addresses": [{"ip": "*************/32"}, {"ip": "***********/32"}]}, {"address_name": "NAS_*********", "type": "object", "addresses": [{"ip": "*********/32"}]}, {"address_name": "Saltstack Master", "type": "object", "addresses": [{"ip": "************/32"}, {"ip": "************/32"}]}, {"address_name": "net_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "NVS", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "host_4.102.150.10", "type": "object", "addresses": [{"ip": "4.102.150.10/32"}]}, {"address_name": "host_4.102.150.20", "type": "object", "addresses": [{"ip": "4.102.150.20/32"}]}, {"address_name": "************-************", "type": "object", "addresses": [{"range": "************ ************"}]}, {"address_name": "*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "*********", "type": "object", "addresses": [{"ip": "*********/16"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/24"}]}], "ILMS": [{"address_name": "ILMS_4.102.11.0/24", "type": "object", "addresses": [{"ip": "4.102.11.0/24"}]}, {"address_name": "ȫϵͳ", "type": "object", "addresses": [{"range": "********** **********"}, {"range": "**********1 **********0"}]}, {"address_name": "Monitor_ZabbixServers", "type": "object", "addresses": [{"ip": "4.255.209.11/32"}, {"ip": "4.255.209.12/32"}, {"ip": "************1/32"}]}, {"address_name": "Monitor_ZabbixProxy", "type": "object", "addresses": [{"ip": "4.255.209.13/32"}, {"ip": "************/32"}, {"ip": "************2/32"}]}, {"address_name": "host_*********", "type": "object", "addresses": [{"ip": "*********/32"}], "description": "sytem ops"}, {"address_name": "CIMS_Servers", "type": "object", "addresses": [{"range": "********** **********"}, {"range": "**********1 **********0"}, {"range": "*********** ***********"}, {"range": "***********1 ***********0"}, {"range": "************ ************"}, {"range": "*********** ***********"}, {"ip": "***********/32"}]}, {"address_name": "Zabbix_JianKong", "type": "object", "addresses": [{"range": "************ ************"}, {"range": "************1 ************2"}, {"ip": "************8/32"}, {"range": "************ ************"}, {"ip": "***********/24"}]}, {"address_name": "NTP_Server", "type": "object", "addresses": [{"ip": "*******/32"}, {"ip": "*******/32"}]}, {"address_name": "YUM Server", "type": "object", "addresses": [{"ip": "*************/32"}, {"ip": "***********/32"}]}, {"address_name": "NAS_group", "type": "object", "addresses": [{"ip": "*********/32"}, {"ip": "4.9.1.202/32"}]}, {"address_name": "Saltstack Master", "type": "object", "addresses": [{"ip": "************/32"}, {"ip": "************/32"}]}, {"address_name": "net_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "host_4.12.70.50", "type": "object", "addresses": [{"ip": "4.12.70.50/32"}], "description": "YouXiDaoRuGongJu"}, {"address_name": "ILMS_QianZhi_group", "type": "object", "addresses": [{"ip": "4.102.11.11/32"}, {"ip": "4.102.11.12/32"}, {"ip": "4.102.11.13/32"}, {"ip": "4.102.11.14/32"}, {"ip": "***********/32"}]}, {"address_name": "ILMS_QianZhi_C_group", "type": "object", "addresses": [{"ip": "4.102.11.15/32"}, {"ip": "4.102.11.16/32"}, {"ip": "4.102.11.30/32"}]}, {"address_name": "host_3.12.41.50", "type": "object", "addresses": [{"ip": "3.12.41.50/32"}]}, {"address_name": "CAS_3.11.10.22", "type": "object", "addresses": [{"ip": "3.11.10.22/32"}]}, {"address_name": "CAS_4.11.70.30", "type": "object", "addresses": [{"ip": "4.11.70.30/32"}]}, {"address_name": "ILMS_GengXin_group", "type": "object", "addresses": [{"ip": "4.102.11.21/32"}, {"ip": "4.102.11.22/32"}, {"ip": "***********/32"}]}, {"address_name": "ILMS_QianZhi_VIP", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "QianZhiDB_4.26.10.20-22", "type": "object", "addresses": [{"ip": "4.26.10.21/32"}, {"ip": "4.26.10.22/32"}, {"ip": "4.26.10.20/32"}]}, {"address_name": "SGW_***********", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "ILMS_C_vip_***********", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "ILMS_UPDATE_vip_***********", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "NVS", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "************-************", "type": "object", "addresses": [{"range": "************ ************"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/32"}]}, {"address_name": "4.102.11.0/24", "type": "object", "addresses": [{"ip": "4.102.120.0/24"}]}, {"address_name": "************", "type": "object", "addresses": [{"ip": "************/32"}]}, {"address_name": "*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "*********", "type": "object", "addresses": [{"ip": "*********/16"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/24"}]}, {"address_name": "Monitor_Zabbix", "type": "group", "addresses": [{"address-set": "Monitor_ZabbixServers"}, {"address-set": "Monitor_ZabbixProxy"}]}], "SGW": [{"address_name": "CIMS_Servers", "type": "object", "addresses": [{"range": "********** **********"}, {"range": "**********1 **********0"}, {"range": "*********** ***********"}, {"range": "***********1 ***********0"}, {"range": "************ ************"}, {"range": "*********** ***********"}, {"ip": "***********/32"}]}, {"address_name": "Zabbix_JianKong", "type": "object", "addresses": [{"range": "************ ************"}, {"range": "************1 ************2"}, {"ip": "************8/32"}, {"range": "************ ************"}, {"ip": "***********/24"}]}, {"address_name": "SGW_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "SGW_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "LDAP_*********", "type": "object", "addresses": [{"ip": "*********/32"}]}, {"address_name": "NTP_Server", "type": "object", "addresses": [{"ip": "*******/32"}, {"ip": "*******/32"}]}, {"address_name": "YUM Server", "type": "object", "addresses": [{"ip": "*************/32"}, {"ip": "***********/32"}]}, {"address_name": "NAS_*********", "type": "object", "addresses": [{"ip": "*********/32"}]}, {"address_name": "Saltstack Master", "type": "object", "addresses": [{"ip": "************/32"}, {"ip": "************/32"}]}, {"address_name": "net_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "SIE Group5_**********", "type": "object", "addresses": [{"ip": "**********/32"}]}, {"address_name": "QH_IDP_**********", "type": "object", "addresses": [{"ip": "**********/32"}]}, {"address_name": "SIE Group1_**********", "type": "object", "addresses": [{"ip": "**********/32"}]}, {"address_name": "SIE Group2_**********", "type": "object", "addresses": [{"ip": "**********/32"}]}, {"address_name": "SIE Group3_**********", "type": "object", "addresses": [{"ip": "**********/32"}]}, {"address_name": "SIE Group4_**********", "type": "object", "addresses": [{"ip": "**********/32"}]}, {"address_name": "IDP_Group1", "type": "object", "addresses": [{"ip": "***********/32"}, {"ip": "**********/32"}, {"ip": "**********/32"}, {"ip": "**********/32"}], "description": "SX,NM,LN,HL,SD,HA"}, {"address_name": "IDP_Group2", "type": "object", "addresses": [{"ip": "***********/32"}, {"ip": "**********/32"}], "description": "FJ,GD,GX,SC,XZ,NX"}, {"address_name": "IDP_Group3", "type": "object", "addresses": [{"ip": "***********/32"}, {"ip": "**********/32"}, {"ip": "**********/32"}], "description": "SH,JS,ZJ,AH,HB"}, {"address_name": "IDP_Group4", "type": "object", "addresses": [{"ip": "***********/32"}, {"ip": "**********/32"}, {"ip": "**********/32"}], "description": "BJ,TJ,JX,CQ,GZ,YN"}, {"address_name": "IDP_Group6", "type": "object", "addresses": [{"ip": "***********/32"}, {"ip": "**********/32"}], "description": "JL,QH"}, {"address_name": "IDP_Group7", "type": "object", "addresses": [{"ip": "***********/32"}, {"ip": "**********/32"}, {"ip": "***********/32"}, {"ip": "**********/32"}, {"ip": "**********/32"}], "description": "HE,HN,HI,SN,GS,XJ"}, {"address_name": "ILFS_*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "ILFS_C_*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "ILMS_UPDATE_*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "ILMS_***********", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "ILMS_***********", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "ILMS_***********", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "WSJC_*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "WSJC_*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "WSJC_Nginx_VIP", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "WSJC_mailServer", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "NVS", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "************-************", "type": "object", "addresses": [{"range": "************ ************"}]}, {"address_name": "*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "*********", "type": "object", "addresses": [{"ip": "*********/16"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/24"}]}, {"address_name": "*********", "type": "object", "addresses": [{"ip": "*********/32"}]}], "WSJC": [{"address_name": "host_*********", "type": "object", "addresses": [{"ip": "*********/32"}], "description": "sytem ops"}, {"address_name": "CIMS_Servers", "type": "object", "addresses": [{"range": "********** **********"}, {"range": "**********1 **********0"}, {"ip": "*********/24"}, {"range": "*********** ***********"}, {"range": "***********1 ***********0"}, {"range": "************ ************"}, {"range": "*********** ***********"}, {"ip": "***********/32"}]}, {"address_name": "NTP_Server", "type": "object", "addresses": [{"ip": "*******/32"}, {"ip": "*******/32"}]}, {"address_name": "YUM Server", "type": "object", "addresses": [{"ip": "*************/32"}, {"ip": "***********/32"}]}, {"address_name": "NAS_group", "type": "object", "addresses": [{"ip": "4.9.1.212/32"}]}, {"address_name": "Zabbix_JianKong", "type": "object", "addresses": [{"range": "************ ************"}, {"range": "************1 ************2"}, {"ip": "************8/32"}, {"range": "************ ************"}, {"ip": "***********/24"}]}, {"address_name": "Saltstack Master", "type": "object", "addresses": [{"ip": "************/32"}, {"ip": "************/32"}]}, {"address_name": "net_***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "WSJC_Nginx", "type": "object", "addresses": [{"ip": "4.102.12.11/32"}, {"ip": "4.102.12.12/32"}, {"ip": "***********/32"}]}, {"address_name": "WSJC_Nginx_VIP", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "WSJC_Nginx_OUT", "type": "object", "addresses": [{"ip": "4.102.12.21/32"}]}, {"address_name": "WSJC_mail", "type": "object", "addresses": [{"ip": "***********/32"}]}, {"address_name": "jueCe_3.22.0.0", "type": "object", "addresses": [{"ip": "3.22.0.0/16"}]}, {"address_name": "jueCe_sendMail", "type": "object", "addresses": [{"ip": "3.22.10.114/32"}, {"ip": "3.22.10.73/32"}, {"ip": "3.22.10.142/32"}, {"ip": "3.22.10.116/32"}, {"ip": "3.22.10.141/32"}]}, {"address_name": "jueCe_Edge", "type": "object", "addresses": [{"ip": "3.22.10.51/32"}]}, {"address_name": "baiDuDNS", "type": "object", "addresses": [{"ip": "180.76.76.76/32"}]}, {"address_name": "aliDNS", "type": "object", "addresses": [{"ip": "223.5.5.5/32"}]}, {"address_name": "SGW_4.103.211.0/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "docker_3.27.20.31-38", "type": "object", "addresses": [{"range": "3.27.20.31 3.27.20.38"}]}, {"address_name": "mailManage", "type": "object", "addresses": [{"ip": "3.22.10.141/32"}, {"ip": "3.22.10.142/32"}, {"ip": "3.22.10.37/32"}, {"ip": "3.22.10.38/32"}, {"ip": "3.22.10.40/32"}, {"ip": "3.22.10.140/32"}]}, {"address_name": "***********/24", "type": "object", "addresses": [{"ip": "***********/24"}]}, {"address_name": "ZJYC_122.224.162.68", "type": "object", "addresses": [{"ip": "122.224.162.68/32"}]}, {"address_name": "NVS", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "************-************", "type": "object", "addresses": [{"range": "************ ************"}]}, {"address_name": "docker_3.28.20.0", "type": "object", "addresses": [{"ip": "3.28.20.0/24"}]}, {"address_name": "CXweb", "type": "object", "addresses": [{"ip": "47.93.1.140/32"}]}, {"address_name": "host_3.22.10.51", "type": "object", "addresses": [{"ip": "3.22.10.51/32"}]}, {"address_name": "4.102.12.0/24", "type": "object", "addresses": [{"ip": "4.102.120.0/24"}]}, {"address_name": "************", "type": "object", "addresses": [{"ip": "************/32"}]}, {"address_name": "*************", "type": "object", "addresses": [{"ip": "*************/32"}]}, {"address_name": "*********", "type": "object", "addresses": [{"ip": "*********/16"}]}, {"address_name": "**********", "type": "object", "addresses": [{"ip": "**********/24"}]}]}
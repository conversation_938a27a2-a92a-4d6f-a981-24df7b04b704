{"default": [{"rule_name": "ha", "action": "permit", "source_zone": "dmz,local", "source_ip": "any", "destination_ip": "any", "destination_zone": "dmz,local", "service": "any"}, {"rule_name": "ntp", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "ntp_4.9.0.1", "destination_zone": "any", "service": "icmp,ntp"}, {"rule_name": "jiankong", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "jiankong", "destination_zone": "any", "service": "snmptrap,syslog"}, {"rule_name": "snmp", "action": "permit", "source_zone": "any", "source_ip": "jiankong", "destination_ip": "any", "destination_zone": "any", "service": "snmp"}, {"rule_name": "radius", "action": "permit", "source_zone": "local", "source_ip": "any", "destination_ip": "**********", "destination_zone": "trust", "service": "radius"}], "Core": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "ntp_service", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "trust", "service": "ntp"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "***********/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "Normal": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "Others": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "ump", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "host_4.102.150.10", "destination_zone": "trust", "service": "TCP_8080"}, {"rule_name": "sso", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "host_4.102.150.20", "destination_zone": "trust", "service": "http"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "ILMS": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "Internet to ILMS", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "ILMS_C_vip_4.102.11.19,ILMS_Qian<PERSON>hi_VIP,ILMS_UPDATE_vip_4.102.11.20", "destination_zone": "trust", "service": "TCP_8443"}, {"rule_name": "DaoRuGongJu_Client", "action": "permit", "source_zone": "untrust", "source_ip": "host_3.12.41.50,host_4.12.70.50", "destination_ip": "ILMS_GengXin_group,ILMS_QianZhi_C_group,ILMS_QianZhi_VIP,ILMS_QianZhi_group", "destination_zone": "trust", "service": "TCP_8443"}, {"rule_name": "to CAS", "action": "permit", "source_zone": "trust", "source_ip": "ILMS_QianZhi_C_group,ILMS_QianZhi_group", "destination_ip": "CAS_3.11.10.22,CAS_4.11.70.30", "destination_zone": "untrust", "service": "TCP_20050"}, {"rule_name": "to QianZhiDB", "action": "permit", "source_zone": "trust", "source_ip": "ILMS_QianZhi_C_group,ILMS_QianZhi_group", "destination_ip": "QianZhiDB_4.26.10.20-22", "destination_zone": "untrust", "service": "TCP_6379"}, {"rule_name": "CAS to QianZhi", "action": "permit", "source_zone": "untrust", "source_ip": "CAS_4.11.70.30", "destination_ip": "ILMS_QianZhi_C_group,ILMS_QianZhi_group", "destination_zone": "trust", "service": "any"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "radius", "action": "permit", "source_zone": "trust", "source_ip": "ILMS_**********/24", "destination_ip": "**********", "destination_zone": "untrust", "service": "udp-1812"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "SGW": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_4.9.1.201", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_4.9.1.201", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "internet to TLS ShouPiao", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SGW_4.102.200.0/24", "destination_zone": "trust", "service": "TCP_8443,TCP_8500"}, {"rule_name": "internet to TLS IDP", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SGW_4.102.200.0/24", "destination_zone": "trust", "service": "TCP_8600"}, {"rule_name": "internet to ILFS", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "ILFS_4.102.200.201", "destination_zone": "trust", "service": "TCP_8443"}, {"rule_name": "internet to ILFS_C", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "ILFS_C_4.102.200.202", "destination_zone": "trust", "service": "TCP_8443"}, {"rule_name": "internet to ILMS_UPDATE", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "ILMS_UPDATE_4.102.200.203", "destination_zone": "trust", "service": "TCP_8443"}, {"rule_name": "internet to WSJC", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "WSJC_4.102.200.204,WSJC_4.102.200.205", "destination_zone": "trust", "service": "https"}, {"rule_name": "internet to  SSL", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "SGW_4.102.200.0/24", "destination_zone": "trust", "service": "https"}, {"rule_name": "SGW to LDAP", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "*********,LDAP_4.11.0.10", "destination_zone": "untrust", "service": "TCP_389"}, {"rule_name": "SGW to SIE Group1", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "Group1_4.11.10.30\"", "destination_zone": "untrust", "service": "TCP_20050"}, {"rule_name": "SGW to SIE Group2", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "Group2_4.11.20.30\"", "destination_zone": "untrust", "service": "TCP_20050"}, {"rule_name": "SGW to SIE Group3", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "Group3_4.11.30.30\"", "destination_zone": "untrust", "service": "TCP_20050"}, {"rule_name": "SGW to SIE Group4", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "Group4_4.11.40.30\"", "destination_zone": "untrust", "service": "TCP_20050"}, {"rule_name": "SGW to SIE Group5", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "Group5_4.11.50.30\"", "destination_zone": "untrust", "service": "TCP_20050"}, {"rule_name": "SGW to IDP Group1", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "IDP_Group1", "destination_zone": "untrust", "service": "TCP_8091"}, {"rule_name": "SGW to IDP Group2", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "IDP_Group2", "destination_zone": "untrust", "service": "TCP_8091"}, {"rule_name": "SGW to IDP Group3", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "IDP_Group3", "destination_zone": "untrust", "service": "TCP_8091"}, {"rule_name": "SGW to IDP Group4", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "IDP_Group4", "destination_zone": "untrust", "service": "TCP_8091"}, {"rule_name": "SGW to IDP Group6", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "IDP_Group6", "destination_zone": "untrust", "service": "TCP_8091"}, {"rule_name": "SGW to IDP Group7", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "IDP_Group7", "destination_zone": "untrust", "service": "TCP_8091"}, {"rule_name": "SGW to ILMS", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "ILMS_4.102.11.10,ILMS_4.102.11.19,ILMS_4.102.11.20", "destination_zone": "untrust", "service": "TCP_8443"}, {"rule_name": "SGW to WSJC_Nginx", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "WSJC_Nginx_VIP", "destination_zone": "untrust", "service": "http"}, {"rule_name": "SGW to WSJC_mail", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "WSJC_mailServer", "destination_zone": "untrust", "service": "http,https"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "SGW_to_G3", "action": "permit", "source_zone": "trust", "source_ip": "SGW_4.102.211.0/24", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_30000"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}], "WSJC": [{"rule_name": "icmp", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "any", "destination_ip": "any", "destination_zone": "local,trust,untrust", "service": "icmp"}, {"rule_name": "SOC", "action": "permit", "source_zone": "local,trust,untrust", "source_ip": "***********/24", "destination_ip": "any", "destination_zone": "trust", "service": "TCP-8890,TCP-8891,https,icmp,snmptrap,ssh"}, {"rule_name": "soc", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "***********/24", "destination_zone": "local,trust,untrust", "service": "TCP-8999,rdp-tcp,rdp-udp,snmp,syslog"}, {"rule_name": "CIMS-Management", "action": "permit", "source_zone": "untrust", "source_ip": "CIMS_Servers", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "Sysops-Management", "action": "permit", "source_zone": "untrust", "source_ip": "host_4.9.1.100", "destination_ip": "any", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "Zabbix_JianKong", "action": "permit", "source_zone": "untrust", "source_ip": "Zabbix_JianKong", "destination_ip": "any", "destination_zone": "trust", "service": "TCP_10050"}, {"rule_name": "Zabbix_Jiankong", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Zabbix_JianKong", "destination_zone": "untrust", "service": "TCP_10051,snmptrap,syslog"}, {"rule_name": "ntp", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NTP_Server", "destination_zone": "untrust", "service": "ntp"}, {"rule_name": "yum", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Server\"", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NAS", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "NAS_group", "destination_zone": "untrust", "service": "\"NAS"}, {"rule_name": "NAS duplexing", "action": "permit", "source_zone": "untrust", "source_ip": "NAS_group", "destination_ip": "any", "destination_zone": "trust", "service": "\"NAS"}, {"rule_name": "saltstack master", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "Master\"", "destination_zone": "untrust", "service": "TCP_4505,TCP_4506"}, {"rule_name": "snmp get", "action": "permit", "source_zone": "untrust", "source_ip": "net_4.255.210.0/24", "destination_ip": "any", "destination_zone": "trust", "service": "snmp"}, {"rule_name": "SGW to Nginx", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "WSJC_Nginx_VIP", "destination_zone": "trust", "service": "http"}, {"rule_name": "SGW to mailServer", "action": "permit", "source_zone": "untrust", "source_ip": "SGW_4.103.211.0/24", "destination_ip": "WSJC_mail", "destination_zone": "trust", "service": "http,https"}, {"rule_name": "Internet to mailServer", "action": "permit", "source_zone": "untrust", "source_ip": "any", "destination_ip": "WSJC_mail", "destination_zone": "trust", "service": "imap,pop3,smtp"}, {"rule_name": "nginx to jueCe", "action": "permit", "source_zone": "trust", "source_ip": "WSJC_Nginx", "destination_ip": "docker_3.27.20.31-38,jueCe_3.22.0.0", "destination_zone": "untrust", "service": "http,https"}, {"rule_name": "jue<PERSON>e to Nginx_OUT", "action": "permit", "source_zone": "untrust", "source_ip": "jueCe_Edge", "destination_ip": "WSJC_Nginx_OUT", "destination_zone": "trust", "service": "TCP_10001,TCP_10002,TCP_10003,TCP_10004,TCP_10005,TCP_10006,TCP_10007,TCP_10008,TCP_10009,TCP_10012,http,https"}, {"rule_name": "jueCe to mailServer", "action": "permit", "source_zone": "untrust", "source_ip": "docker_3.27.20.31-38,jue<PERSON>e_sendMail", "destination_ip": "WSJC_mail", "destination_zone": "trust", "service": "imap,pop3,smtp"}, {"rule_name": "Nginx_OUT to internet", "action": "permit", "source_zone": "trust", "source_ip": "WSJC_Nginx_OUT", "destination_ip": "any", "destination_zone": "untrust", "service": "dns,http,https"}, {"rule_name": "Nginx_OUT to internetMail", "action": "permit", "source_zone": "trust", "source_ip": "WSJC_mail", "destination_ip": "any", "destination_zone": "untrust", "service": "smtp"}, {"rule_name": "Nginx_OUT to DNS", "action": "permit", "source_zone": "trust", "source_ip": "WSJ<PERSON>_Nginx_OUT,WSJC_mail", "destination_ip": "aliDNS,baiDuDNS", "destination_zone": "untrust", "service": "dns"}, {"rule_name": "mailManage to mailServer", "action": "permit", "source_zone": "untrust", "source_ip": "mailManage", "destination_ip": "WSJC_mail", "destination_zone": "trust", "service": "TCP_8080,http,smtp"}, {"rule_name": "Nginx_OUT to ZJ_YC", "action": "permit", "source_zone": "trust", "source_ip": "WSJC_Nginx_OUT", "destination_ip": "ZJYC_122.224.162.68", "destination_zone": "untrust", "service": "http"}, {"rule_name": "NVS", "action": "permit", "source_zone": "untrust", "source_ip": "NVS", "destination_ip": "any", "destination_zone": "trust", "service": "any"}, {"rule_name": "soc-1", "action": "permit", "source_zone": "any", "source_ip": "************-************", "destination_ip": "any", "destination_zone": "any", "service": "ssh,tcp-139,tcp-3389,telnet"}, {"rule_name": "Docker_to_WS-NG-OUT", "action": "permit", "source_zone": "untrust", "source_ip": "docker_3.28.20.0", "destination_ip": "WSJC_Nginx_OUT", "destination_zone": "trust", "service": "TCP_10009"}, {"rule_name": "Nginx_OUT to CXweb", "action": "permit", "source_zone": "trust", "source_ip": "WSJC_Nginx_OUT", "destination_ip": "CXweb", "destination_zone": "untrust", "service": "TCP_18081"}, {"rule_name": "Edge-server_to_WS-NG-OUT", "action": "permit", "source_zone": "untrust", "source_ip": "host_3.22.10.51", "destination_ip": "WSJC_Nginx_OUT", "destination_zone": "trust", "service": "TCP_10010,TCP_10013"}, {"rule_name": "EDR_to_Agent", "action": "permit", "source_zone": "trust", "source_ip": "any", "destination_ip": "any", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http"}, {"rule_name": "z<PERSON><PERSON>_to_EDR", "action": "permit", "source_zone": "trust", "source_ip": "**********/24", "destination_ip": "************", "destination_zone": "untrust", "service": "TCP_6677,TCP_7788,TCP_8001,TCP_8002,TCP_8443,http,https"}, {"rule_name": "OCS_to_*********", "action": "permit", "source_zone": "untrust", "source_ip": "**********", "destination_ip": "*********", "destination_zone": "trust", "service": "ssh"}, {"rule_name": "*********_to_syslog", "action": "permit", "source_zone": "trust", "source_ip": "*********", "destination_ip": "*************", "destination_zone": "untrust", "service": "syslog"}]}
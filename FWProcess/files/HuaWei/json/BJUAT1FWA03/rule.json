{"default": [{"rule_name": "AMS下线版本网络开通", "action": "permit", "source_zone": "egress", "source_ip": "*************/32.,***********/32张跃vdi,ka<PERSON><PERSON><PERSON><PERSON>ols_********0", "destination_ip": "**********/32t5,18.6.32.68T5,********/8.", "destination_zone": "core", "service": "any"}, {"rule_name": "test李阳", "action": "permit", "source_zone": "core", "source_ip": "********/8.", "destination_ip": "**********/16.", "destination_zone": "egress", "service": "any"}, {"rule_name": "第三代竞猜型游戏交易系统", "action": "permit", "source_zone": "core", "source_ip": "********/8.", "destination_ip": "***********/24.,**********/24.,***********./24", "destination_zone": "egress", "service": "TCP_6370,TCP30021,TCP_8083"}, {"rule_name": "出访问", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "魏宏安全扫描***********", "action": "permit", "source_zone": "egress", "source_ip": "***********/32张跃vdi,10.211.6.0_24", "destination_ip": "**********/***********,***********阿里云测试", "destination_zone": "core", "service": "any"}, {"rule_name": "姜大伟数据采集机", "action": "permit", "source_zone": "egress", "source_ip": "************~160_数据采集机", "destination_ip": "**********/24_0518,18.6.32.53_0518", "destination_zone": "core", "service": "any"}, {"rule_name": "性能测试", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "18.6.32.52_性能测试,**********_性能测试", "destination_zone": "core", "service": "any"}, {"rule_name": "agent_TO_T1_景一0629", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,**********.,*************/32.,***********/32张跃vdi", "destination_ip": "********/24.,**************/32.", "destination_zone": "core", "service": "any"}, {"rule_name": "20230523李景一", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "*********.,18.6.32.49_", "destination_zone": "core", "service": "any"}, {"rule_name": "离朱", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "18.6.32.51_,18.0.95.97_,18.6.32.35_36_0516,18.4.0.72_73_0516", "destination_zone": "core", "service": "any"}, {"rule_name": "10.211.6.59TO18.0.95.94", "action": "permit", "source_zone": "egress", "source_ip": "10.211.6.0_24,18.6.32.35_36", "destination_ip": "18.6.32.50_", "destination_zone": "core", "service": "tcp100,icmp,3389,514"}, {"rule_name": "T1_**********", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "**********.", "destination_zone": "egress", "service": "TCP8443"}, {"rule_name": "T5_18.0.160.10", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "<PERSON><PERSON><PERSON>,76.223.81.133_", "destination_zone": "egress", "service": "icmp,https"}, {"rule_name": "魏宏安全扫描", "action": "permit", "source_zone": "core", "source_ip": "**********.,**********.,18.6.32.1_", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "魏宏安全监控", "action": "permit", "source_zone": "egress", "source_ip": "************_魏宏安全主机,***********/32张跃vdi", "destination_ip": "********/16.,********/16_0519", "destination_zone": "core", "service": "any"}, {"rule_name": "internetservicelinshitest", "action": "permit", "source_zone": "egress", "source_ip": "LBSNAT,LB_SELF", "destination_ip": "**********.,**********./32,***********.", "destination_zone": "core", "service": "any"}, {"rule_name": "10.21118.0.140", "action": "permit", "source_zone": "egress", "source_ip": "10.211.6.45_59", "destination_ip": "18.0.140.1218.0.140.39,********29_8080,**********_,18.0.185.12_37,18.0.185.39_,18.4.128.170_,18.6.32.49_", "destination_zone": "core", "service": "any"}, {"rule_name": "**********", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "**********_,38.0.160.59_,18.0.160.59_", "destination_zone": "core", "service": "any"}, {"rule_name": "T5test", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "10.213.3.60_", "destination_zone": "egress", "service": "TCP_34443,icmp"}, {"rule_name": "**********to3.0 linshi", "action": "permit", "source_zone": "core", "source_ip": "**********_", "destination_ip": "3.0.0.0_", "destination_zone": "egress", "service": "any"}, {"rule_name": "信息发布中心V22.11.01", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "18.6.32.45_", "destination_zone": "core", "service": "any"}, {"rule_name": "monitest_yuanxia<PERSON>jie", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "F5VIP**********", "destination_zone": "core", "service": "TCP8083&8084"}, {"rule_name": "模拟运营环境交易路由", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "18_0_97_11,18_0_117_11/32", "destination_zone": "core", "service": "any"}, {"rule_name": "安全PKI测试系统", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "*************/32.,*************/32.,*************/32.,*************/32.,*************/32.,*************/32.,*************/32.,**************/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "************", "action": "permit", "source_zone": "egress", "source_ip": "************/32.,**********/24.", "destination_ip": "18_6_32_42", "destination_zone": "core", "service": "any"}, {"rule_name": "<PERSON><PERSON>_tianyan", "action": "permit", "source_zone": "egress", "source_ip": "************/32.,************/32.,***********/32.,***********/32.", "destination_ip": "18.4.0.71_73", "destination_zone": "core", "service": "https"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "egress", "source_ip": "*********/8.,10.0.0.0/8.", "destination_ip": "**********/32.,********/24.", "destination_zone": "core", "service": "any"}, {"rule_name": "sjj2", "action": "permit", "source_zone": "egress", "source_ip": "*************/32.,*************/32.,*************/32.,*************/32.,*************/32.,***********/32.", "destination_ip": "18.0.3.10_sjj", "destination_zone": "core", "service": "TCP8000,TCP8022,tcp_8088,TCP_9999,TCP_161,TCP_7569,TCP_8080"}, {"rule_name": "xuan", "action": "permit", "source_zone": "core", "source_ip": "**********.,**********.,********/16.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "to_ng_1443", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********_10,**********/32.,**************.,**************.,**************.,**************.", "destination_ip": "**********.", "destination_zone": "egress", "service": "TCP_1443"}, {"rule_name": "deny_ng_8443_1443", "action": "deny", "source_zone": "core", "source_ip": "any", "destination_ip": "**********.", "destination_zone": "egress", "service": "TCP8443,TCP_1443"}, {"rule_name": "jujiabangong1", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "**********./32", "destination_zone": "core", "service": "TCP_3389"}, {"rule_name": "AGENT_to_xiaofu", "action": "permit", "source_zone": "core", "source_ip": "***********/32.,***********.", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "https,icmp"}, {"rule_name": "ping", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "icmp"}, {"rule_name": "T1_waipan", "action": "permit", "source_zone": "core", "source_ip": "***********/32.", "destination_ip": "**********/32.", "destination_zone": "egress", "service": "https,icmp"}, {"rule_name": "*************", "action": "permit", "source_zone": "egress", "source_ip": "*************/32.", "destination_ip": "********/8_", "destination_zone": "core", "service": "any"}, {"rule_name": "guoce_any", "action": "permit", "source_zone": "egress", "source_ip": "guoce_18.6.30and31", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "kejibangongto_T5", "action": "permit", "source_zone": "egress", "source_ip": "************.,**********/24.", "destination_ip": "***********/32.,18_6_32_37", "destination_zone": "core", "service": "TCP_8092,icmp"}, {"rule_name": "********_to_***********", "action": "permit", "source_zone": "core", "source_ip": "********/8.,********/16.", "destination_ip": "***********./24", "destination_zone": "egress", "service": "any"}, {"rule_name": "10.216._to_T518.0.160", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.,**********/24.,**********/24.,**********/24.", "destination_ip": "T2_T3_38.0.160.0", "destination_zone": "core", "service": "any"}, {"rule_name": "18.6.3_to_200.3", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "*************.", "destination_zone": "core", "service": "https,http"}, {"rule_name": "104.23.13_to_jincai", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.,**********/24_,10.0.0.0/8.", "destination_ip": "**********.,***********/32.,***********./32,T5_***********0,*********1/12/120,18.6.32.37_", "destination_zone": "core", "service": "http,tcp_52701,tcp_7003,http_8082"}, {"rule_name": "10.213_to_T1_core", "action": "permit", "source_zone": "egress", "source_ip": "10.213.0.60_65,**********.", "destination_ip": "********_8,********/32.,*********/32.,***********./32,***********.,***********.,*************.,18.4.21.212_214", "destination_zone": "core", "service": "ssh,TCP_8000_8010,TCP_10050,TCP_9091,http,icmp"}, {"rule_name": "vpn_to_xietong", "action": "permit", "source_zone": "core", "source_ip": "vpn_bangong", "destination_ip": "10.217.2.0/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "bosrouter", "action": "permit", "source_zone": "core", "source_ip": "**********/24.,T2_T3_38.0.160.0,**********.,28.0.7.77/32.,48.0.7.77/32.,58.0.7.77/32.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "RMX150", "action": "permit", "source_zone": "core", "source_ip": "*********/32.,18.4.21.151_154", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "https"}, {"rule_name": "T1_BT_to104", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "**********/32.", "destination_zone": "egress", "service": "TCP_3389"}, {"rule_name": "18.0.7._to_104.23.", "action": "permit", "source_zone": "core", "source_ip": "********/24.,**********.,**********.", "destination_ip": "CSLC_**********/24", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_to_104.200.100", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "*************/24.", "destination_zone": "egress", "service": "TCP_34433"}, {"rule_name": "T5_to_10.212", "action": "permit", "source_zone": "core", "source_ip": "T2_T3_38.0.160.0", "destination_ip": "**********/24.,***********/24.,18.6.23.28_29", "destination_zone": "egress", "service": "TCP_9092,TCP_34433,TCP_7001"}, {"rule_name": "T1_to_18.5.88.31_32", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "18.5.88.31_32", "destination_zone": "egress", "service": "TCP_28080"}, {"rule_name": "T4_to_USAP", "action": "permit", "source_zone": "core", "source_ip": "T4:***********,***********.,*************/24.,********50/32.,********/8.,28_0_0_0/8,********/8.,********/8_", "destination_ip": "USAP:***********,**********/24.,************/32.,*************/24.,************.,**********/24.,F5_********50", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1core_to_10.213", "action": "permit", "source_zone": "core", "source_ip": "********_8,********/32.,*********/32.,18.4.21.212_214,***********./32,***********.,***********.", "destination_ip": "***********.,**********4.,10.213.3.31_33,10.213.3.41_42,***********.", "destination_zone": "egress", "service": "TCP_8000_8010,TCP_7001,TCP_5672,TCP_5044,TCP_31306,ssh"}, {"rule_name": "167_to*********7", "action": "permit", "source_zone": "egress", "source_ip": "**************.,**************.", "destination_ip": "*********7.,********/24.", "destination_zone": "core", "service": "TCP_3389,TCP_445"}, {"rule_name": "T2ZiDongCaoPan", "action": "permit", "source_zone": "core", "source_ip": "************./32,********00/32.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "G3SHITI_TO_t1", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": ".**********/24,T1_*********01,NTP_**********,*********/24.", "destination_zone": "core", "service": "any"}, {"rule_name": "104.23._T1", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.,***********/24.,***********/24.", "destination_ip": "********_8,********/32.,*********/32.", "destination_zone": "core", "service": "any"}, {"rule_name": "SNMP_T2", "action": "permit", "source_zone": "egress", "source_ip": "zabbix_*********01,solarwinds_*********00", "destination_ip": "any", "destination_zone": "core", "service": "snmp,snmptrap,ssh,TCP_10050"}, {"rule_name": "DMZ_to_oldT1", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "**********/32.", "destination_zone": "core", "service": "any"}, {"rule_name": "104.19_to_T2T5", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.", "destination_ip": "***********./32,***********/32.,***********/32.,***********/32.,********/24.,********/24.,T3_38.0.1.0/24", "destination_zone": "core", "service": "tcp_7003,http_8082,TCP_8086,tcp_22"}, {"rule_name": "T1_to_Ocp和腾讯云", "action": "permit", "source_zone": "core", "source_ip": "********/24.,NTP_**********", "destination_ip": "**********/24.,***********/24.", "destination_zone": "egress", "service": "http,ssh,TCP_8080"}, {"rule_name": "T1_104.23.", "action": "permit", "source_zone": "core", "source_ip": "********_8,********/32.,*********/32.", "destination_ip": "***********/24.,***********/24.,***********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "XWHT4_to118", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T4_NAT_**********", "destination_ip": "**************/32.", "destination_zone": "core", "service": "tcp_22,TCP_8000_8010,TCP_10050,TCP_9091"}, {"rule_name": "G3T1_TO_T1", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "************/32.", "destination_zone": "core", "service": "TCP31050_31051"}, {"rule_name": "10.214_to_T1", "action": "permit", "source_zone": "egress", "source_ip": "**********/16.", "destination_ip": "********20/32.", "destination_zone": "core", "service": "https,http"}, {"rule_name": "OPC_to_ITO", "action": "permit", "source_zone": "core", "source_ip": "T1_18.2.1.9,T1_18.2.1.224,T1_18.2.1.249", "destination_ip": "*********/32.,T4_18.6.4.51", "destination_zone": "egress", "service": "TCP30900"}, {"rule_name": "**********_net", "action": "permit", "source_zone": "core", "source_ip": "**********/32.,**********.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "T4_to_104.21.20", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/24.,**********.", "destination_ip": "***********/24.,***********./24,***********/32.,***********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_to104_10", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/16.", "destination_ip": "************.,************/32.,**********.,***********.,************.,***************.,**********/24.,***********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "118_to_T4", "action": "permit", "source_zone": "core", "source_ip": "**************/32.", "destination_ip": "XWH_T4_NAT_18.6.4.27,*********/32.,*********/32.,*********/32.", "destination_zone": "egress", "service": "TCP/UDP111,TCP/UDP_2049,TCP/UDP_32768_65535,TCP_7001,TCP/UDP22,TCP_5044,TCP_9090,TCP_5672,TCP8001_8020,TCP_31306"}, {"rule_name": "T2T4T5to_104.", "action": "permit", "source_zone": "core", "source_ip": "********/24.,T3_38.2.1.0/24,********/24.,********/24.,********/24.,T3_38.0.1.0/24,********/24.,***********/32.,T2_T3_38.0.160.0", "destination_ip": "***********/24.,**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "TO_T4XWH_NFS", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/24.,********/24.,********/24.", "destination_ip": "NFS_18.6.4.48/32", "destination_zone": "egress", "service": "TCP/UDP111,TCP/UDP_2049,TCP/UDP_32768_65535,icmp"}, {"rule_name": "t1_g3t1", "action": "permit", "source_zone": "core", "source_ip": "************/32.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP31050_31051"}, {"rule_name": "wang<PERSON><PERSON><PERSON>_ceshi", "action": "permit", "source_zone": "egress", "source_ip": "18.5.81.123/32.", "destination_ip": "18.0.2.38/32.", "destination_zone": "core", "service": "TCP_3559"}, {"rule_name": "10.213_to_T1", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.,10.216.15.0/24.,***********/24.,10.216.16.0/24.,************/24.,**********/24.,10.216.8.0/24.,10.216.11.0/24.,10.216.9.0./24", "destination_ip": "********/24.,********/24.,**********.,.**********/24,18.0.117.0/24.", "destination_zone": "core", "service": "tcp_22,TCP_8086,http_8082,tcp_52701,tcp_7003,http"}, {"rule_name": "TI_to_svn", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/24.", "destination_ip": "**************.,************.,104.21.20.27.", "destination_zone": "egress", "service": "any"}, {"rule_name": "********_to_g<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/16.,********/24.", "destination_ip": "*************/24.,**********/24.,***************.,104.11.0.81.,104.21.20.16.", "destination_zone": "egress", "service": "tcp_30000,TCP_20050,tcp_22,tcp1521"}, {"rule_name": "T4_104.21.20", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/24.,48.0.7.4/32.,********/24.,T3_38.2.1.0/24,T3_38.0.1.0/24,********/24.", "destination_ip": "***********/24.,*************/24.,**********/24.", "destination_zone": "egress", "service": "TCP_20050,tcp_30000,TCP_8080,TCP8443,TCP_34431_34435"}, {"rule_name": "T1_TO_XWHT418.6.4", "action": "permit", "source_zone": "core", "source_ip": "********/32.,*********/32.", "destination_ip": "XWH_T4_NAT_18.6.4.23_31", "destination_zone": "egress", "service": "TCP_5000,TCP_7001,TCP_5672,TCP_8004,TCP/UDP111,TCP/UDP_2049,TCP/UDP_32768_65535,TCP8011,UDP_123,TCP_50,TCP_449,TCP_10,TCP_8001,TCP_9090"}, {"rule_name": "T1_to_104.200/10.213", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********_8,********/32.", "destination_ip": "*************/24.,**********/24.", "destination_zone": "egress", "service": "tcp_30000,TCP8443,TCP_20050,TCP_8080"}, {"rule_name": "SCAN", "action": "permit", "source_zone": "core", "source_ip": "18.2.1.75./32", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "to_cslc_tengxunyun", "action": "permit", "source_zone": "core", "source_ip": "**********.,18.0.99.99_100,*********/24.", "destination_ip": "************/32.,10.0.0.0/8.,*********/8.", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_CSLC_104.22", "action": "permit", "source_zone": "core", "source_ip": "*************/24.", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "tcp_22"}, {"rule_name": "32.109_to_T4", "action": "permit", "source_zone": "core", "source_ip": "BG_192.168.32.109", "destination_ip": "18.5.95.4.,18.6.4.26.", "destination_zone": "egress", "service": "ssh"}, {"rule_name": "BGToXWH", "action": "permit", "source_zone": "core", "source_ip": "192.168.32.109/32.,192.168.52.234_236", "destination_ip": "XWHG3_*********/24,********.", "destination_zone": "egress", "service": "TCP8011_8020"}, {"rule_name": "CSLC_TO_18.5.48.51", "action": "permit", "source_zone": "core", "source_ip": "CSLC172.26.100.200", "destination_ip": "XWH_18.6.3.128/32", "destination_zone": "egress", "service": "tcp_29092"}, {"rule_name": "104.23_to_T5", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.", "destination_ip": "T2_T3_38.0.160.0,T3_38.0.1.0/24", "destination_zone": "core", "service": "TCP_8086,TCP_28080"}, {"rule_name": "CSLC_To_Bangong_MailServer", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "TCP_25"}, {"rule_name": "T1_To_XWH_***********", "action": "permit", "source_zone": "core", "source_ip": "***********/32.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP_27003"}, {"rule_name": "XWHT1_TO_T1", "action": "permit", "source_zone": "egress", "source_ip": "*********/32.", "destination_ip": "**********/32.,***********/32.", "destination_zone": "core", "service": "http_8082,TCP_8092"}, {"rule_name": "XWH_TO_T1...", "action": "permit", "source_zone": "egress", "source_ip": "*********./32", "destination_ip": "**********/32.", "destination_zone": "core", "service": "http_8082,TCP_8085,TCP_8087"}, {"rule_name": "CSLC_XWH_To_T4", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,***********/32.", "destination_ip": "********/24.,**********.", "destination_zone": "core", "service": "icmp,TCP_21,tcp_22,http_8082"}, {"rule_name": "T4_To_CSLC_XWH", "action": "permit", "source_zone": "core", "source_ip": "**********.,********/24.", "destination_ip": "***********/32.,***********/32.,***********/32.", "destination_zone": "egress", "service": "icmp,TCP_9092"}, {"rule_name": "T1_To_XWH_T4_MS", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "*********.", "destination_zone": "egress", "service": "TCP_29200"}, {"rule_name": "T1_To_XWH************", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "************/32.,T3_NAT_18.6.3.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_to_CSLC_3.90", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "icmp,TCP_10248"}, {"rule_name": "XWHT3_18.5.85.1_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "XWHT3_18.5.81.5/32", "destination_ip": "***********.,18.0.95.223_228", "destination_zone": "core", "service": "tcp_22,TCP_31050_31051_31060,tcp_29092"}, {"rule_name": "T1_To_XWH_18.5.81.5", "action": "permit", "source_zone": "core", "source_ip": "18.0.95.223_228,***********.,T1_18.0.95.0/24", "destination_ip": "XWHT3_18.5.81.5/32", "destination_zone": "egress", "service": "tcp_22,TCP_31050_31051_31060,tcp_29092,TCP_4555"}, {"rule_name": "CSLC_To_**********", "action": "permit", "source_zone": "core", "source_ip": "************.", "destination_ip": "**********.,*********.", "destination_zone": "egress", "service": "ssh"}, {"rule_name": "T4_to_***********", "action": "permit", "source_zone": "core", "source_ip": "T4_68.0.0.0/8,T4_48.1.14.0,********/24.,**********.", "destination_ip": "***********/32.,***********/24.", "destination_zone": "egress", "service": "TCP8080,TCP_9092"}, {"rule_name": "SNMP", "action": "permit", "source_zone": "egress", "source_ip": "*********.,zabbix_*********01,Solarwinds*********00", "destination_ip": "MGMT_18.4.0.0/24,************.,*************./32", "destination_zone": "core", "service": "snmp,snmptrap,ssh,telnet"}, {"rule_name": "XWH_To_T1_**********_239", "action": "permit", "source_zone": "egress", "source_ip": "*********.,*********/24.,XWHG3_*********/24", "destination_ip": "**********_239", "destination_zone": "core", "service": "TCP_9092"}, {"rule_name": "R141_TO_mail", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,***********/32.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "TCP_25"}, {"rule_name": "NTP_18.0.216.78", "action": "permit", "source_zone": "egress", "source_ip": "RTQ_18.5.84.81_82", "destination_ip": "NTP_18.0.216.78", "destination_zone": "core", "service": "ntp"}, {"rule_name": "TO_NTP_********83", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "NTP_********83", "destination_zone": "core", "service": "ntp,ssh"}, {"rule_name": "lijingyi2", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,***********/32.", "destination_ip": "T1_18.2.1.34/32", "destination_zone": "core", "service": "TCP_20050,tcp_22,TCP_34433"}, {"rule_name": "lijingyi1", "action": "permit", "source_zone": "core", "source_ip": "T1_18.2.1.34/32", "destination_ip": "***********/32.,***********/32.", "destination_zone": "egress", "service": "TCP_20050,TCP_34433"}, {"rule_name": "CSLC_To_T1_*********", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "*********.", "destination_zone": "core", "service": "tcp_7003"}, {"rule_name": "T1_To_CSLC_***********", "action": "permit", "source_zone": "core", "source_ip": "*********.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "tcp_7003"}, {"rule_name": "lilibinTEST", "action": "permit", "source_zone": "egress", "source_ip": "*********.,G3AMS_18.5.84.0./24", "destination_ip": "************./32,**********.", "destination_zone": "core", "service": "TCP_6000,TCP_4555"}, {"rule_name": "TIAOBANJI", "action": "permit", "source_zone": "core", "source_ip": "**********.,***********/32.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "204.1_To_XWH", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "SDN_172.16.21.1/32", "destination_zone": "egress", "service": "any"}, {"rule_name": "XWH_T5", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,***********/32.", "destination_ip": "********/8.,T3_38.2.1.0/24,********/24.,*********./32", "destination_zone": "core", "service": "TCP_20050,TCP8080,TCP_8080,tcp_7003,icmp,telnet,tcp_22"}, {"rule_name": "T5_XWH", "action": "permit", "source_zone": "core", "source_ip": "********/8.,T3_38.2.1.0/24,********/24.", "destination_ip": "***********/32.,***********/32.", "destination_zone": "egress", "service": "TCP_20050,TCP8080,icmp,telnet"}, {"rule_name": "To_18.1.3.0", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.,***********.,***********/32.", "destination_ip": "*********.,*********./24", "destination_zone": "core", "service": "http,http_8082"}, {"rule_name": "To_XWH_T19", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "**********/24_", "destination_zone": "egress", "service": "ssh,http,https"}, {"rule_name": "lousao_to_T1", "action": "permit", "source_zone": "egress", "source_ip": "***************/32.,***************/32.", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "to_keji_block2", "action": "permit", "source_zone": "core", "source_ip": "**********.,***********/32.", "destination_ip": "*************/24.,**********/24.,**********/24.,**********/24.,**********/24.,**********/24.,*************/24.,***********/24.,**********/16.", "destination_zone": "egress", "service": "any"}, {"rule_name": "DENY", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "**********./32,**********./32,**********./32", "destination_zone": "egress", "service": "http,tcp_9080"}, {"rule_name": "**********_To_XWH_********", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "********/16.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T4_To_T1_18.0.5.211_213", "action": "permit", "source_zone": "egress", "source_ip": "T4_*********/24", "destination_ip": "T1_18.0.5.211_213", "destination_zone": "core", "service": "TCP_62738"}, {"rule_name": "T4_18.5.82.1_20_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "T4_*********/24,SDAS_18.5.80.161_162", "destination_ip": "T1_18.0.2.189/32,T4_18.0.5.204/32", "destination_zone": "core", "service": "TCP_31306"}, {"rule_name": "T4_18.5.88.161_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T4_18.5.88.161/32", "destination_ip": "********/16.", "destination_zone": "core", "service": "TCP_8080"}, {"rule_name": "T1_To_8.5.88.161:80", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "XWH_T4_18.5.88.161/32,XWH_T4_18.5.88.181/32", "destination_zone": "egress", "service": "http"}, {"rule_name": "T1_To_18.5.88.161", "action": "permit", "source_zone": "core", "source_ip": "********7_18,********54.,********.,********.,********52.", "destination_ip": "XWH_T4_18.5.88.161/32", "destination_zone": "egress", "service": "TCP_32600"}, {"rule_name": "TO_*************", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "ssh,icmp,TCP_4422"}, {"rule_name": "XWHT3_to_cslc", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "CSLC_172.20.22.0/24,172.26.5.70/32.", "destination_zone": "core", "service": "TCP_8098,icmp,http"}, {"rule_name": "cslc_to_XWH_T3_", "action": "permit", "source_zone": "core", "source_ip": "172.26.26.0/24.", "destination_ip": "18.6.3.15/32.", "destination_zone": "egress", "service": "TCP_7001,TCP_8001"}, {"rule_name": "T4_RMXDB_T1_RMX", "action": "permit", "source_zone": "egress", "source_ip": "********.,V3_CORE_4.190.80.0/21", "destination_ip": "18.0.2.213.", "destination_zone": "core", "service": "TCP_3555"}, {"rule_name": "YYXNZM_18.2.1.120_123", "action": "permit", "source_zone": "egress", "source_ip": "YunYing_192.168.215.50,<PERSON><PERSON><PERSON>_192.168.215.51", "destination_ip": "4F_18.2.1.120_123", "destination_zone": "core", "service": "TCP_3389,icmp"}, {"rule_name": "YunYing_192.168.215.50_51_<PERSON><PERSON>", "action": "deny", "source_zone": "egress", "source_ip": "YunYing_192.168.215.50,<PERSON><PERSON><PERSON>_192.168.215.51", "destination_ip": "any", "destination_zone": "any", "service": "any"}, {"rule_name": "Sunkai_Ceshi", "action": "permit", "source_zone": "core", "source_ip": "************./32", "destination_ip": "***********./32", "destination_zone": "egress", "service": "TCP30400"}, {"rule_name": "G2_TRANSROUTE_V3_30400", "action": "permit", "source_zone": "core", "source_ip": "**********_84", "destination_ip": "V3_GW_4.190.40.0/21,***********./32", "destination_zone": "egress", "service": "TCP30400,ssh"}, {"rule_name": "lishuaiqi_test", "action": "permit", "source_zone": "core", "source_ip": "T1_18.0.2.133/32", "destination_ip": "V3_4.190.80.73/32", "destination_zone": "egress", "service": "TCP_3558,ssh"}, {"rule_name": "18.5.95.5_to_*********", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "*********/24.", "destination_zone": "core", "service": "TCP_10050,TCP_9091,ssh"}, {"rule_name": "第三方测试到国家实验室", "action": "permit", "source_zone": "core", "source_ip": "********/24.,**********.,********/16.", "destination_ip": "CSLC_104.23.0.10,CSLC_104.23.0.11,CSLC_104.23.0.12_14,104.23.0.78_99,**********.,***********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "********_to_T4", "action": "permit", "source_zone": "core", "source_ip": "T1_********/32,T1_********,18.4.21.212_214,********/16.", "destination_ip": "********/16.", "destination_zone": "egress", "service": "ssh,TCP_30001,TCP_8086,TCP_7001,TCP_31306,TCP_3555,TCP_3558,TCP_28080,TCP_28081,TCP_29200,TCP_5000,TCP_8004,TCP_5672,ntp,TCP_5044,TCP_8001,TCP_9090,TCP8011,TCP/UDP_32768_65535,TCP/UDP_2049,TCP/UDP111"}, {"rule_name": "T1_To_XWH_T3", "action": "permit", "source_zone": "core", "source_ip": "********.", "destination_ip": "*********/21.", "destination_zone": "egress", "service": "TCP_7001"}, {"rule_name": "snmp123", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "snmp,sn<PERSON><PERSON>,TCP_3555,TCP3556,TCP_3557,TCP_3558,smtp,TCP_8080,tcp_8080,dns,TCP_9092,tcp_22"}, {"rule_name": "T4_F5_to_T1_*********", "action": "permit", "source_zone": "egress", "source_ip": "********/16.", "destination_ip": "*********./24,*********.", "destination_zone": "core", "service": "TCP_8087,http_8082,TCP_8085,http"}, {"rule_name": "T4_to_T1_AD", "action": "permit", "source_zone": "egress", "source_ip": "********/16.", "destination_ip": "********.,T1_AD_F5_18.0.10.200,********./32", "destination_zone": "core", "service": "any"}, {"rule_name": "T1_OCS_to_T4", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "********/16.", "destination_zone": "egress", "service": "ssh,TCP_31306,TCP_3555,TCP_3558"}, {"rule_name": "V3_to_<PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "egress", "source_ip": "G3_MS_4.190.120.0/22", "destination_ip": "JiGuan_192.168.182.130", "destination_zone": "core", "service": "http,https"}, {"rule_name": "Ji<PERSON>uan_to_V3", "action": "permit", "source_zone": "core", "source_ip": "JiGuan_192.168.182.130", "destination_ip": "G3_MS_4.190.120.0/22", "destination_zone": "egress", "service": "http,https"}, {"rule_name": "CSLC_TO_NEWT3", "action": "permit", "source_zone": "core", "source_ip": "CSLC_104.21.2.0/24,**********/16.,**********./24,NAT_27.16.18.10.,CSLC_172.20.30.0/24,**********.", "destination_ip": "T3_NAT_18.6.3.0", "destination_zone": "egress", "service": "ssh,TCP_7001,https,http,TCP_5672,tcp_8080,TCP_9001,TCP9080,TCP_8001,TCP_8989,TCP_34443,TCP_3389"}, {"rule_name": "aopeng_test", "action": "permit", "source_zone": "egress", "source_ip": "***********/22.", "destination_ip": "********/16.", "destination_zone": "core", "service": "any"}, {"rule_name": "CSLC_to_T3", "action": "permit", "source_zone": "core", "source_ip": "CSLC_172.20.22.0/24,CSLC_172.20.29.0/24,CSLC_172.20.30.0/24,CSLC_172.20.23.0/24", "destination_ip": "********.,********.,**********./32", "destination_zone": "egress", "service": "https,TCP8443,TCP_8080"}, {"rule_name": "Test_ITO_Kongfanqiang", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T1_4.191.80.0/24,/24\"", "destination_ip": "********.", "destination_zone": "core", "service": "any"}, {"rule_name": "XWH_T3_To_18.2.1.245", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T3_18.6.3.131,**********.,T19_18.6.19.4,**********/24.,**********/24.", "destination_ip": "Dsvs_18.2.1.245", "destination_zone": "core", "service": "tcp_8000"}, {"rule_name": "T1_to_CSLC", "action": "permit", "source_zone": "core", "source_ip": "********/24.,*********/24.,*********./24,*********./32", "destination_ip": "***********/32.,***********/32.", "destination_zone": "egress", "service": "TCP_20050,icmp,TCP_8080"}, {"rule_name": "to_************", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "************/32.", "destination_zone": "core", "service": "TCP_6006"}, {"rule_name": "***********_to_T4", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,***********/32.", "destination_ip": "T4_48.0.2.121,T4_48.0.2.201,**********.", "destination_zone": "core", "service": "http_8082,tcp_7003,icmp"}, {"rule_name": "T1_To_XWH1", "action": "permit", "source_zone": "core", "source_ip": "T1_********/32,*********/24.", "destination_ip": "XWH_18.6.116.1_9", "destination_zone": "egress", "service": "any"}, {"rule_name": "XWH_To_T1_1", "action": "permit", "source_zone": "egress", "source_ip": "XWH_18.6.116.1_9,T4_*********", "destination_ip": "T1_********/32,*********/24.,T1_********", "destination_zone": "core", "service": "any"}, {"rule_name": "500WAN&JCW_to_T2N1", "action": "permit", "source_zone": "egress", "source_ip": "500wan_10.0.1.0/24,JCW_20.0.101.41,uazz_********/24,*************.,**********/24.,**********/24.", "destination_ip": "T2N1_38.1.21.11,T5_***********0,**********/24.", "destination_zone": "core", "service": "tcp_52701"}, {"rule_name": "TEST_XWH_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "G3_MS_4.190.120.0/22,***********/22.,***********/24.,104.21.2.64_", "destination_ip": "************/24.,**********/24.", "destination_zone": "core", "service": "any"}, {"rule_name": "NEW_T3_TO_***********", "action": "permit", "source_zone": "egress", "source_ip": "**********.,**********.", "destination_ip": "***********.", "destination_zone": "core", "service": "TCP8443"}, {"rule_name": "NEW_T3_TO_T3", "action": "permit", "source_zone": "egress", "source_ip": "*************.,T3_NAT_18.6.3.0", "destination_ip": "T3_38.0.160.14/32,T3_38.0.1.10/32,T3_38.0.2.33/32,***********/32.,***********./32", "destination_zone": "core", "service": "tcp_8088,TCP_28080,TCP_6021,TCP_6201,ssh,icmp,TCP_8086,TCP_8087"}, {"rule_name": "XWH_NEW_T3_to_T2_AB_T3", "action": "permit", "source_zone": "egress", "source_ip": "T3_NAT_18.6.3.0,***********/32.,***********.,***********/32.,***********/32.", "destination_ip": "********/8.", "destination_zone": "core", "service": "http_8082,TCP_50094,TCP_50095,tcp_52701,tcp_7003"}, {"rule_name": "T4_to_T1_*********1_114", "action": "permit", "source_zone": "egress", "source_ip": "***********.,T4_*********", "destination_ip": "********.", "destination_zone": "core", "service": "TCP_8080"}, {"rule_name": "T4_To_XWH", "action": "permit", "source_zone": "core", "source_ip": "T4_48.0.100.0/24,T3_38.0.1.0/24,T2_T3_38.0.160.0,********/8.,**********.,********/8.,********/16.", "destination_ip": "**********.,*********.,*********./24", "destination_zone": "egress", "service": "any"}, {"rule_name": "T4_to_T1_18.0.5.2126", "action": "permit", "source_zone": "egress", "source_ip": "********.", "destination_ip": "********./24", "destination_zone": "core", "service": "Tcp_7001"}, {"rule_name": "LuYu_G3_ILO  To  T1_********", "action": "permit", "source_zone": "egress", "source_ip": "G3_ILO_4.191.249.0/24,G3_MS_4.190.122.0/24,G3_MS_4.190.120.0/22", "destination_ip": "********/16.", "destination_zone": "core", "service": "any"}, {"rule_name": "XWH_T3_To_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "CSLC_104.21.2.51/32", "destination_zone": "core", "service": "TCP_28080,TCP_20617"}, {"rule_name": "Ceshi_IF_flow", "action": "permit", "source_zone": "core", "source_ip": "***********/32_", "destination_ip": "*************/32_", "destination_zone": "egress", "service": "TCP_5000"}, {"rule_name": "Ceshi_SNMP", "action": "permit", "source_zone": "core", "source_ip": "***********/32_,***********/32_", "destination_ip": "**********_16", "destination_zone": "egress", "service": "snmp,icmp"}, {"rule_name": "T1_To_G3_SNMP", "action": "permit", "source_zone": "core", "source_ip": "T1_18.0.254.242/32", "destination_ip": "XWH_G3_4.176.0.0/24,XWH_G3_4.176.1.0/24", "destination_zone": "egress", "service": "any"}, {"rule_name": "G3_To_T1_**********", "action": "permit", "source_zone": "egress", "source_ip": "*******./8", "destination_ip": "**********.", "destination_zone": "core", "service": "any"}, {"rule_name": "G3_TEST", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "*******./8", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_To_XWH_G3_*******/8", "action": "permit", "source_zone": "core", "source_ip": "********/8_", "destination_ip": "XWH_G3_*******/8", "destination_zone": "egress", "service": "any"}, {"rule_name": "CSLC_To_BG_192.168.52.232", "action": "permit", "source_zone": "egress", "source_ip": "CSLC_104.23.0.4", "destination_ip": "BG_192.168.52.232", "destination_zone": "core", "service": "http,https"}, {"rule_name": "T1_To_CSLC", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "CSLC_104.23.0.4", "destination_zone": "egress", "service": "ssh"}, {"rule_name": "BG_To_CSLC", "action": "permit", "source_zone": "core", "source_ip": "BG_192.168.52.232,192.168.52.234_236", "destination_ip": "CSLC_104.23.0.4", "destination_zone": "egress", "service": "http,https"}, {"rule_name": "CSLC_To_BG", "action": "permit", "source_zone": "egress", "source_ip": "CSLC_**********", "destination_ip": "192.168.52.234_236", "destination_zone": "core", "service": "TCP_32600"}, {"rule_name": "To_CSLC_TiaoBanJi", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "CSLC_TiaoBanJi_104.23.0.6,CSLC_TiaoBanJi_104.23.0.7", "destination_zone": "egress", "service": "TCP_3389,tcp_135,tcp135totcp139,udp135toudp139,TCP_445"}, {"rule_name": "TO_CSLC_TiaoBanJi", "action": "permit", "source_zone": "core", "source_ip": "********/16.,*********/24.,********/24.", "destination_ip": "CSLC_TiaoBanJi_**********", "destination_zone": "egress", "service": "ssh,TCP_8000"}, {"rule_name": "BG_To_XWH_T4MS", "action": "permit", "source_zone": "core", "source_ip": "T1_18.0.1.5/32,*********/24.,***********./32", "destination_ip": "XWH_T4MS_18.5.95.3/32", "destination_zone": "egress", "service": "TCP/UDP111,TCP/UDP_2049,TCP/UDP_32768_65535"}, {"rule_name": "T1_To_XWHG3", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "XWHG3_18.5.88.31_37,XWH_磁带机_18.5.85.1,XWH_磁带机_18.5.85.2", "destination_zone": "egress", "service": "tcp_29092,tcp_30000"}, {"rule_name": "CeShi_To_XWH", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/16.", "destination_ip": "XWH_18.6.116.1_8,**********.", "destination_zone": "egress", "service": "TCP_8000_8010,http,tcp_22,TCP_8013,TCP_8018,TCP_186"}, {"rule_name": "BG_To_XWH_22", "action": "permit", "source_zone": "core", "source_ip": "BG_192.168.32.109", "destination_ip": "XWH_18.6.116.1_8", "destination_zone": "egress", "service": "tcp_22"}, {"rule_name": "BG_To_XWH", "action": "permit", "source_zone": "core", "source_ip": "192.168.52.234_236", "destination_ip": "XWH_18.6.116.101_103", "destination_zone": "egress", "service": "http"}, {"rule_name": "BG_To_XWH_116", "action": "permit", "source_zone": "core", "source_ip": "192.168.52.234_236", "destination_ip": "XWH_18.6.116.1_8", "destination_zone": "egress", "service": "TCP8011"}, {"rule_name": "XWH_To_BG_52.232", "action": "permit", "source_zone": "egress", "source_ip": "18.5.116.101_103,************.", "destination_ip": "BG_192.168.52.232,*********1.,T3_Harbor_38.0.1.104,T1_*********4/32,**********.,**************/32.,********/24.", "destination_zone": "core", "service": "http,https,ssh"}, {"rule_name": "XWH_To_BG_52.234", "action": "permit", "source_zone": "egress", "source_ip": "XWH_18.6.116.1_8", "destination_ip": "192.168.52.234_236", "destination_zone": "core", "service": "TCP_32600"}, {"rule_name": "XWH_To_BG_32.18", "action": "permit", "source_zone": "egress", "source_ip": "XWH_18.6.116.1_8,XWHG3_*********/24,**********/24_", "destination_ip": "BG_192.168.32.18,*************/**************,*************.", "destination_zone": "core", "service": "smtp,icmp,TCP_25,TCP_1433"}, {"rule_name": "XWH_To_BG_32.121", "action": "permit", "source_zone": "egress", "source_ip": "XWH_18.6.116.1_8", "destination_ip": "BG_192.168.32.121", "destination_zone": "core", "service": "TCP_5000,TCP10080"}, {"rule_name": "T1_To_CSLC_Lab", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "CSLC_104.255.225.0/24", "destination_zone": "egress", "service": "https,TCP9443"}, {"rule_name": "CSLC_To_XWHT3_tia<PERSON><PERSON>ji", "action": "permit", "source_zone": "core", "source_ip": "CSLC_172.20.18.0/24", "destination_ip": "XWH_T3_18.6.3.1", "destination_zone": "egress", "service": "TCP_3389"}, {"rule_name": "T1_To_XWHG4_*********/24", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "XWHG3_*********/24", "destination_zone": "egress", "service": "tcp_8088,tcp_22,http,TCP30400,TCP_30600,tcp31306"}, {"rule_name": "T1_To_XWHG3_*********/24", "action": "permit", "source_zone": "core", "source_ip": "**********/32.,/24\"", "destination_ip": "XWHG3_*********/21", "destination_zone": "egress", "service": "tcp_8088"}, {"rule_name": "XWHG3_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "XWHG3_*********/24", "destination_ip": "/24\",**********/32.", "destination_zone": "core", "service": "TCP_28080,TCP_28081"}, {"rule_name": "**********_To_XWH_T4", "action": "permit", "source_zone": "core", "source_ip": "**********.,********/24.,**********_", "destination_ip": "XWH_T4NAT_18.6.3.0/24,XWH_G3_********/24,**********./24", "destination_zone": "egress", "service": "any"}, {"rule_name": "XWH_T4_To_T1_*********01", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T4_NAT_**********,XWHG3_*********/24", "destination_ip": "T1_*********01", "destination_zone": "core", "service": "tcp31306"}, {"rule_name": "BG_To_XWH_T4_NAT", "action": "permit", "source_zone": "core", "source_ip": "BG_192.168.32.109,********/24.", "destination_ip": "XWH_T4_18.5.95.1_9,XWH_T4_NAT_18.6.4.23_31", "destination_zone": "egress", "service": "ssh,http,TCP_8000_8010"}, {"rule_name": "XWH_T4_NAT_192.168.32.121", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T4_NAT_**********", "destination_ip": "BG_192.168.32.121", "destination_zone": "core", "service": "TCP10080,TCP_5000"}, {"rule_name": "XWHT4_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T4_NAT_**********,XWHG3_*********/24,XWH_T4_NAT_18.6.4.134", "destination_ip": "********/16.", "destination_zone": "core", "service": "any"}, {"rule_name": "backup", "action": "permit", "source_zone": "core", "source_ip": "192.168.66.150_32", "destination_ip": "any", "destination_zone": "egress", "service": "ssh,snmp"}, {"rule_name": "XWH_T3_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T3_18.6.3.130,***********/24.,***********/24.", "destination_ip": "T1_JMJ_18.2.1.244", "destination_zone": "core", "service": "TCP_8008,icmp"}, {"rule_name": "To_XWH_172.16.7.0/24", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "XWH_172.16.7.0/24", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_XWH_********", "action": "permit", "source_zone": "core", "source_ip": "*************/24.", "destination_ip": "********/32.", "destination_zone": "egress", "service": "http"}, {"rule_name": "To_XWH_T3_********", "action": "permit", "source_zone": "core", "source_ip": "T3_38.2.1.0/24", "destination_ip": "********/32_", "destination_zone": "egress", "service": "any"}, {"rule_name": "to_vxlan_T3", "action": "permit", "source_zone": "core", "source_ip": "***************/32_", "destination_ip": "********/32_", "destination_zone": "egress", "service": "TCP_445,TCP_3389,icmp"}, {"rule_name": "To_***********", "action": "permit", "source_zone": "core", "source_ip": "**********.,**********/32.", "destination_ip": "***********.", "destination_zone": "egress", "service": "TCP_3389,TCP_1099"}, {"rule_name": "ceshi", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "********/16.,18.1.2.241_32", "destination_zone": "core", "service": "any"}, {"rule_name": "ceshi_To_XWHT3_tia<PERSON><PERSON>ji", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "XWHT3_t<PERSON><PERSON><PERSON><PERSON>_18.6.3.0/24", "destination_zone": "egress", "service": "TCP_3389,icmp"}, {"rule_name": "egress_to_core_Csljcto500w", "action": "permit", "source_zone": "egress", "source_ip": "**************/32.", "destination_ip": "*********/24.,*********/22.", "destination_zone": "core", "service": "any"}, {"rule_name": "Csljcto500w", "action": "permit", "source_zone": "egress", "source_ip": "500wan**************", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "any"}, {"rule_name": "uuzz1", "action": "permit", "source_zone": "egress", "source_ip": "********./27", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "any"}, {"rule_name": "ospf", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "ospf,echo-udp,udp"}, {"rule_name": "egress_to_egw_20", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.", "destination_ip": "PAS18.4.130.141", "destination_zone": "egw", "service": "any"}, {"rule_name": "egress_to_egw_0", "action": "permit", "source_zone": "egress", "source_ip": "abc", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "http"}, {"rule_name": "egress_to_egw_1", "action": "permit", "source_zone": "egress", "source_ip": "ccb", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "http"}, {"rule_name": "egw_to_core_28", "action": "permit", "source_zone": "egw", "source_ip": "ABSBISOnlineA02_IN", "destination_ip": "absbcsaaa07", "destination_zone": "core", "service": "tcp_8080"}, {"rule_name": "egw_to_core_26", "action": "permit", "source_zone": "egw", "source_ip": "pasdb_18.4.129.141.,pasdb_18.4.129.142.", "destination_ip": "TAS18.0.4.171,TAS18.0.4.172,TAS18.0.4.180", "destination_zone": "core", "service": "tcp_8080"}, {"rule_name": "egress_to_egw_2", "action": "permit", "source_zone": "egress", "source_ip": "abc", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "ftp,ftp_8000_9000"}, {"rule_name": "egress_to_egw_3", "action": "permit", "source_zone": "egress", "source_ip": "ccb", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "ftp,ftp_8000_9000"}, {"rule_name": "egress_to_egw_4", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "any", "destination_zone": "egw", "service": "icmp"}, {"rule_name": "fez_to_core_7", "action": "permit", "source_zone": "fez", "source_ip": "any", "destination_ip": "trendmicro_server,***********.", "destination_zone": "core", "service": "any"}, {"rule_name": "fez_core_24", "action": "permit", "source_zone": "fez", "source_ip": "/24\"", "destination_ip": "/24\"", "destination_zone": "core", "service": "TCP_20406,TCP_20407"}, {"rule_name": "fez_core_25", "action": "permit", "source_zone": "fez", "source_ip": "/24\"", "destination_ip": "/24\"", "destination_zone": "core", "service": "TCP_3191,TCP_7001"}, {"rule_name": "fez_core_26", "action": "permit", "source_zone": "fez", "source_ip": "/24\"", "destination_ip": "/24\"", "destination_zone": "core", "service": "TCP_21910"}, {"rule_name": "egress_to_egw_5", "action": "permit", "source_zone": "egress", "source_ip": "ccb", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "tcp_30000"}, {"rule_name": "egress_to_egw_6", "action": "permit", "source_zone": "egress", "source_ip": "abc", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "http_9998"}, {"rule_name": "egress_to_core_16", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.", "destination_ip": "**********.,**********.,TLS_18.0.4.133.", "destination_zone": "core", "service": "rpc_6610_6614"}, {"rule_name": "egress_to_egw_7", "action": "permit", "source_zone": "egress", "source_ip": "abc", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "http_7779"}, {"rule_name": "egress_to_egw_8", "action": "permit", "source_zone": "egress", "source_ip": "ccb", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "http_7779,tcp_7778"}, {"rule_name": "egress_to_egw_9", "action": "permit", "source_zone": "egress", "source_ip": "icbc", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "ftp,ftp_8000_9000"}, {"rule_name": "egress_to_egw_10", "action": "permit", "source_zone": "egress", "source_ip": "icbc", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "http_7779"}, {"rule_name": "egress_to_egw_11", "action": "permit", "source_zone": "egress", "source_ip": "*************/32.", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "http_7779"}, {"rule_name": "egress_to_egw_12", "action": "permit", "source_zone": "egress", "source_ip": "*************/32.", "destination_ip": "bisonline1_18.4.4.31,bisonline2_18.4.4.32,bisonline3_*********", "destination_zone": "egw", "service": "tcp_8066"}, {"rule_name": "egress_to_egw_13", "action": "permit", "source_zone": "egress", "source_ip": "ali172.27.17.112,l<PERSON>lian192.168.110.12", "destination_ip": "*********/32.", "destination_zone": "egw", "service": "ftp,icmp,tcp_20,tcp_22,tcp_8065"}, {"rule_name": "egress_to_egw_14", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "any", "destination_zone": "egw", "service": "any"}, {"rule_name": "egress_egw_15", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,Lottery_18.4.252.96/27", "destination_ip": "ABSBISOnline_OUT", "destination_zone": "egw", "service": "tcp_8066"}, {"rule_name": "egw_to_egress_0", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_out", "destination_ip": "abc", "destination_zone": "egress", "service": "https,tcp_8055"}, {"rule_name": "egw_to_egress_1", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_out", "destination_ip": "ccb", "destination_zone": "egress", "service": "tcp_20002,tcp_7777&20001"}, {"rule_name": "egw_to_egress_2", "action": "permit", "source_zone": "egw", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "egw_to_egress_3", "action": "permit", "source_zone": "egw", "source_ip": "sms/mail_gw_out", "destination_ip": "message&mail_system", "destination_zone": "egress", "service": "http"}, {"rule_name": "egw_to_egress_4", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_out", "destination_ip": "mail_system", "destination_zone": "egress", "service": "https"}, {"rule_name": "egw_to_egress_5", "action": "permit", "source_zone": "egw", "source_ip": "sms/mail_gw_out", "destination_ip": "mail_system", "destination_zone": "egress", "service": "https"}, {"rule_name": "egw_to_egress_6", "action": "permit", "source_zone": "egw", "source_ip": "sms_gw_out", "destination_ip": "message_system", "destination_zone": "egress", "service": "http,https"}, {"rule_name": "egw_to_egress_7", "action": "permit", "source_zone": "egw", "source_ip": "emial_gw_out", "destination_ip": "mail_system", "destination_zone": "egress", "service": "http,https"}, {"rule_name": "egw_to_egress_8", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_out", "destination_ip": "icbc", "destination_zone": "egress", "service": "ftp_8000_9000,tcp_20083"}, {"rule_name": "egress_fez_10", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_out", "destination_ip": "************/27.", "destination_zone": "egress", "service": "icmp,tcp_23215_20"}, {"rule_name": "egress_to_fez_3", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "any", "destination_zone": "fez", "service": "icmp"}, {"rule_name": "egress_to_fez_0", "action": "permit", "source_zone": "egress", "source_ip": "cslvsts", "destination_ip": "as_out,asdb", "destination_zone": "fez", "service": "any"}, {"rule_name": "egress_to_fez_1", "action": "permit", "source_zone": "egress", "source_ip": "sporttery", "destination_ip": "absas_lb1_out", "destination_zone": "fez", "service": "http_8081,http_8082,https"}, {"rule_name": "egress_to_fez_9", "action": "permit", "source_zone": "egress,egw", "source_ip": "sporttery", "destination_ip": "absasftp_cluster_virture_ip", "destination_zone": "fez,egress", "service": "ftp"}, {"rule_name": "egress_to_fez_4", "action": "permit", "source_zone": "egress", "source_ip": "ca_server(outline)", "destination_ip": "ca_server(all)", "destination_zone": "fez", "service": "http,https"}, {"rule_name": "egress_to_fez_5", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "as_out", "destination_zone": "fez", "service": "ftp,https"}, {"rule_name": "egress_to_fez_15", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "********/24/,*********/24.", "destination_zone": "fez", "service": "tcp_20201,tcp8080,icmp"}, {"rule_name": "egress_to_fez_6", "action": "permit", "source_zone": "egress", "source_ip": "500wan192.168.41.160/32,tencent131.87.32.0/24,uuzz10.0.3.0,uuzz172.16.20.136,uuzz172.16.20.141,uuzz172.16.20.142,uuzz***********4,uuzz172.16.23.140,uuzz172.16.23.191,**************/32.,yunying_192.168.18.0", "destination_ip": "as_out", "destination_zone": "fez", "service": "ftp,https"}, {"rule_name": "egress_to_fez_7", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "any", "destination_zone": "fez", "service": "any"}, {"rule_name": "fez_to_egress_0", "action": "permit", "source_zone": "fez", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "icmp"}, {"rule_name": "fez_to_egress_1", "action": "permit", "source_zone": "fez", "source_ip": "ca_server(all)", "destination_ip": "ca_server(outline)", "destination_zone": "egress", "service": "http,https"}, {"rule_name": "fez_to_egress_2", "action": "permit", "source_zone": "fez", "source_ip": "as_out", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "egress_to_core_0", "action": "permit", "source_zone": "egress", "source_ip": "egress_aaa_client", "destination_ip": "ise_server", "destination_zone": "core", "service": "cisco_radius"}, {"rule_name": "egress_to_core_1", "action": "permit", "source_zone": "egress", "source_ip": "egress_snmp_client", "destination_ip": "eccom_neteagle,hp_openview01,snmp_server(lms)", "destination_zone": "core", "service": "snmp"}, {"rule_name": "egress_to_core_2", "action": "permit", "source_zone": "egress", "source_ip": "sporttery", "destination_ip": "cb_info_hub", "destination_zone": "core", "service": "tcp_52704"}, {"rule_name": "New_T3_to_CSLC_CASSIT", "action": "permit", "source_zone": "egress", "source_ip": "*************.,T3_NAT_18.6.3.0", "destination_ip": "************.,************/", "destination_zone": "core", "service": "http,https,icmp,TCP9080"}, {"rule_name": "New_T3_to_cslc_***********", "action": "permit", "source_zone": "egress", "source_ip": "*************.", "destination_ip": "***********.", "destination_zone": "core", "service": "TCP8443,icmp,tcp_8080,https,TCP_4433,TCP_24433"}, {"rule_name": "egress_to_core_3", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "any", "destination_zone": "core", "service": "http,https,icmp,ntp,nntp,rpc_6611,snmp,ssh,tcp_3080,TCP8443,TCP_21"}, {"rule_name": "egress_to_core_4", "action": "permit", "source_zone": "egress", "source_ip": "call_center", "destination_ip": "customerserivcegw_out", "destination_zone": "core", "service": "http,https"}, {"rule_name": "egress_to_core_5", "action": "permit", "source_zone": "egress", "source_ip": "cslvsts", "destination_ip": "matserver_out", "destination_zone": "core", "service": "any"}, {"rule_name": "egress_to_core_6", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "*********/24.", "destination_zone": "core", "service": "tcp_52704"}, {"rule_name": "egress_to_core_7", "action": "permit", "source_zone": "egress", "source_ip": "500wan192.168.41.160/32,tencent131.87.32.0/24,uuzz10.0.3.0,uuzz172.16.20.136,uuzz172.16.20.141,uuzz172.16.20.142,uuzz***********4,uuzz172.16.23.141,uuzz172.16.23.191,uuzz172.16.23.198,500wan_192.168.41.95,500wan_192.168.41.96,yunying_192.168.18.0", "destination_ip": "any", "destination_zone": "core", "service": "tcp_52701,tcp_52704"}, {"rule_name": "egress_to_core_8", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "egress_to_core_9", "action": "permit", "source_zone": "egress", "source_ip": "cslcump10.50.6.0", "destination_ip": "g2cbgw_18.1.13.5,g2matserver_*********", "destination_zone": "core", "service": "any"}, {"rule_name": "egress_to_core_11", "action": "permit", "source_zone": "egress", "source_ip": "*********/24\"", "destination_ip": "(*********/24,*********/24.", "destination_zone": "core", "service": "remote_desktop"}, {"rule_name": "core_to_egress_test", "action": "permit", "source_zone": "core", "source_ip": "**********.,**********.,**********.,********_,***********.,18.0.80.79_80,************/32.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "core_to_egress_1", "action": "permit", "source_zone": "core", "source_ip": "eccom_neteagle,hp_openview,snmp_server(lms)", "destination_ip": "egress_snmp_client", "destination_zone": "egress", "service": "snmp"}, {"rule_name": "core_to_egress_2", "action": "permit", "source_zone": "core", "source_ip": "********/24.,************.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "core_to_egress_3", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "cslc_**********,cslc_***********,cslc_172.26.14.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "core_to_egress_4", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "cslcump10.50.6.0,cslcump10.50.7.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "core_to_egress_5", "action": "permit", "source_zone": "core", "source_ip": "*********/28.", "destination_ip": "cslcump10.50.6.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "core_egress_6", "action": "permit", "source_zone": "core", "source_ip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,**********.,**********.,absbisaba01,absbisaba02,absbisaba05", "destination_ip": "CSLCyanpiaoserver1,CSLCyanpiaoserver2,************/32.,*************/32.", "destination_zone": "egress", "service": "tcp_8080,smtp,ftp"}, {"rule_name": "egw_to_fez_0", "action": "permit", "source_zone": "egw", "source_ip": "any", "destination_ip": "any", "destination_zone": "fez", "service": "ftp,icmp,remote_desktop,http_8081"}, {"rule_name": "fez_to_egw_0", "action": "permit", "source_zone": "fez", "source_ip": "as_in", "destination_ip": "sms/mail_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "fez_to_egw_1", "action": "permit", "source_zone": "fez", "source_ip": "any", "destination_ip": "any", "destination_zone": "egw", "service": "ftp,icmp,remote_desktop"}, {"rule_name": "fez_to_egw_2", "action": "permit", "source_zone": "fez", "source_ip": "as_out", "destination_ip": "sms/mail_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "fez_to_egw_3", "action": "permit", "source_zone": "fez", "source_ip": "as_out", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "tcp_9000"}, {"rule_name": "egw_to_core_0", "action": "permit", "source_zone": "egw", "source_ip": "any", "destination_ip": "trendmicro_server", "destination_zone": "core", "service": "any"}, {"rule_name": "egw_to_core_1", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_in", "destination_ip": "uat_f6_testpc(*********/24)", "destination_zone": "core", "service": "any"}, {"rule_name": "egw_to_core_2", "action": "permit", "source_zone": "egw", "source_ip": "any", "destination_ip": "any", "destination_zone": "core", "service": "ftp,icmp,remote_desktop"}, {"rule_name": "egw_to_core_3", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_in", "destination_ip": "oltp_ab", "destination_zone": "core", "service": "udp_50086"}, {"rule_name": "egw_to_core_4", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_in", "destination_ip": "bis_ab", "destination_zone": "core", "service": "ftp"}, {"rule_name": "egw_to_core_5", "action": "permit", "source_zone": "egw", "source_ip": "egw_snmp_client", "destination_ip": "eccom_neteagle,hp_openview,snmp_server(lms)", "destination_zone": "core", "service": "any"}, {"rule_name": "egw_to_core_6", "action": "permit", "source_zone": "egw", "source_ip": "egw_aaa_client", "destination_ip": "ise_server", "destination_zone": "core", "service": "cisco_radius"}, {"rule_name": "egw_to_core_7", "action": "permit", "source_zone": "egw", "source_ip": "eft_gw_in", "destination_ip": "monsvc_out", "destination_zone": "core", "service": "tcp_1000"}, {"rule_name": "egw_to_core_8", "action": "permit", "source_zone": "egw", "source_ip": "eft&emai&sms(********/24)", "destination_ip": "nbu_server", "destination_zone": "core", "service": "mgmt_nbu"}, {"rule_name": "egw_to_core_9", "action": "permit", "source_zone": "egw", "source_ip": "eft&emai&sms(********/24)", "destination_ip": "control_m", "destination_zone": "core", "service": "mgmt_control_m"}, {"rule_name": "egw_to_core_10", "action": "permit", "source_zone": "egw", "source_ip": "eft&emai&sms(********/24)", "destination_ip": "patrol_server", "destination_zone": "core", "service": "mgmt_patrol"}, {"rule_name": "egw_to_core_11", "action": "permit", "source_zone": "egw", "source_ip": "egw_ad", "destination_ip": "patrol_server", "destination_zone": "core", "service": "mgmt_patrol"}, {"rule_name": "egw_to_core_12", "action": "permit", "source_zone": "egw", "source_ip": "egw_ad", "destination_ip": "**********/32.", "destination_zone": "core", "service": "any"}, {"rule_name": "egw_to_core_14", "action": "permit", "source_zone": "egw", "source_ip": "absbisonline_sql_in,absbisonline_web01_in,absbisonline_web02_in", "destination_ip": "absbisonline_sql,absbisonlinea01,absbisonlinea02", "destination_zone": "core", "service": "ftp_8020&8021,tcp_8067"}, {"rule_name": "egw_to_core_15", "action": "permit", "source_zone": "egw", "source_ip": "eft&emai&sms(********/24)", "destination_ip": "zabbix_18.0.4.217", "destination_zone": "core", "service": "any"}, {"rule_name": "egw_to_core_16", "action": "permit", "source_zone": "egw", "source_ip": "absbisonline_msdtc_in,absbisonline_sql_in,absbisonline_web01_in,absbisonline_web02_in,absbisonline_web_vip_in", "destination_ip": "stm_svc_out", "destination_zone": "core", "service": "http"}, {"rule_name": "egw_to_core_17", "action": "permit", "source_zone": "egw", "source_ip": "absbisonline_msdtc_in,absbisonline_sql_in,absbisonline_web01_in,absbisonline_web02_in,absbisonline_web_vip_in", "destination_ip": "absbisonline_msdtc,absbisonline_sql,absbisonline_vip,absbisonlinea01,absbisonlinea02", "destination_zone": "core", "service": "tcp_8069"}, {"rule_name": "core_to_egw_0", "action": "permit", "source_zone": "core", "source_ip": "trendmicro_server", "destination_ip": "any", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_1", "action": "permit", "source_zone": "core", "source_ip": "uat_f6_testpc(*********/24)", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_2", "action": "permit", "source_zone": "core", "source_ip": "uat_f6_testpc(*********/24)", "destination_ip": "eft_gw_in", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_3", "action": "permit", "source_zone": "core", "source_ip": "bcs_aa", "destination_ip": "sms/mail_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "core_egw_42", "action": "permit", "source_zone": "core", "source_ip": "*************.", "destination_ip": "pas_18.4.130.141/32.", "destination_zone": "egw", "service": "http,tcp_8080,icmp"}, {"rule_name": "core_to_egw_4", "action": "permit", "source_zone": "core", "source_ip": "oltp_ab", "destination_ip": "sms/mail_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "core_to_egw_5", "action": "permit", "source_zone": "core", "source_ip": "oltp_ab", "destination_ip": "eft_gw_in", "destination_zone": "egw", "service": "udp_50086"}, {"rule_name": "core_to_egw_6", "action": "permit", "source_zone": "core", "source_ip": "rsa_syslog_server", "destination_ip": "egw_syslog_client", "destination_zone": "egw", "service": "rsa_syslog"}, {"rule_name": "core_to_egw_7", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "egw", "service": "ftp,icmp,remote_desktop"}, {"rule_name": "core_to_egw_test", "action": "permit", "source_zone": "core", "source_ip": "**********.,**********.,**********.", "destination_ip": "any", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_8", "action": "permit", "source_zone": "core", "source_ip": "uat_f6_testpc", "destination_ip": "absgwzdca01", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_9", "action": "permit", "source_zone": "core", "source_ip": "eccom_neteagle,snmp_server(lms),hp_openview", "destination_ip": "egw_snmp_client", "destination_zone": "egw", "service": "snmp"}, {"rule_name": "core_to_egw_10", "action": "permit", "source_zone": "core", "source_ip": "bcs_aa", "destination_ip": "emial_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "core_to_egw_11", "action": "permit", "source_zone": "core", "source_ip": "oltp_ab", "destination_ip": "emial_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "core_to_egw_12", "action": "permit", "source_zone": "core", "source_ip": "bcs_aa", "destination_ip": "sms_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "core_to_egw_13", "action": "permit", "source_zone": "core", "source_ip": "oltp_ab", "destination_ip": "sms_gw_in", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "core_to_egw_14", "action": "permit", "source_zone": "core", "source_ip": "nbu_server", "destination_ip": "eft&emai&sms(********/24)", "destination_zone": "egw", "service": "mgmt_nbu"}, {"rule_name": "core_to_egw_15", "action": "permit", "source_zone": "core", "source_ip": "control_m", "destination_ip": "eft&emai&sms(********/24)", "destination_zone": "egw", "service": "mgmt_control_m"}, {"rule_name": "core_to_egw_16", "action": "permit", "source_zone": "core", "source_ip": "patrol_server", "destination_ip": "eft&emai&sms(********/24)", "destination_zone": "egw", "service": "mgmt_patrol"}, {"rule_name": "core_to_egw_17", "action": "permit", "source_zone": "core", "source_ip": "patrol_server", "destination_ip": "egw_ad", "destination_zone": "egw", "service": "mgmt_patrol"}, {"rule_name": "core_to_egw_18", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "emial_gw_in,sms_gw_in", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_19", "action": "permit", "source_zone": "core", "source_ip": "hp_openview02", "destination_ip": "abseftgwa02_in", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_20", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "egw_ad", "destination_zone": "egw", "service": "any"}, {"rule_name": "core_to_egw_22", "action": "permit", "source_zone": "core", "source_ip": "absbisonline_sql,absbisonlinea01,absbisonlinea02", "destination_ip": "absbisonline_sql_in,absbisonline_web01_in,absbisonline_web02_in", "destination_zone": "egw", "service": "ftp_8020&8021,tcp_8067"}, {"rule_name": "core_to_egw_23", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "*********/32.", "destination_zone": "egw", "service": "tcp_8066"}, {"rule_name": "core_to_egw_24", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "*********/32.", "destination_zone": "egw", "service": "tcp_8066"}, {"rule_name": "core_to_egw_25", "action": "permit", "source_zone": "core", "source_ip": "zabbix_18.0.4.217", "destination_ip": "eft&emai&sms(********/24)", "destination_zone": "egw", "service": "any"}, {"rule_name": "fez_to_core_16", "action": "permit", "source_zone": "fez", "source_ip": "as&asdb(*********/24)", "destination_ip": "*********.", "destination_zone": "core", "service": "TCP6379,tcp_8080,tcp_3306"}, {"rule_name": "fez_to_core_0", "action": "permit", "source_zone": "fez", "source_ip": "as_in", "destination_ip": "bcs_aa", "destination_zone": "core", "service": "rpc+"}, {"rule_name": "fez_to_core_1", "action": "permit", "source_zone": "fez", "source_ip": "as_in", "destination_ip": "absstmtsvc_lb_out", "destination_zone": "core", "service": "http"}, {"rule_name": "fez_to_core_2", "action": "permit", "source_zone": "fez", "source_ip": "as_in", "destination_ip": "igw_out", "destination_zone": "core", "service": "tcp_135,tcp_5056"}, {"rule_name": "fez_to_core_3", "action": "permit", "source_zone": "fez", "source_ip": "bj_prod_lb_a01&a02,snmp_fez_client", "destination_ip": "eccom_neteagle,snmp_server(lms),hp_openview", "destination_zone": "core", "service": "any"}, {"rule_name": "fez_to_core_4", "action": "permit", "source_zone": "fez", "source_ip": "aaa_fez_client", "destination_ip": "ise_server", "destination_zone": "core", "service": "cisco_radius"}, {"rule_name": "fez_to_core_6", "action": "permit", "source_zone": "fez", "source_ip": "as_in", "destination_ip": "monsvc_out", "destination_zone": "core", "service": "ftp,https,tcp_1000"}, {"rule_name": "fez_to_core_8", "action": "permit", "source_zone": "fez", "source_ip": "as&asdb(*********/24)", "destination_ip": "nbu_server", "destination_zone": "core", "service": "mgmt_nbu"}, {"rule_name": "fez_to_core_9", "action": "permit", "source_zone": "fez", "source_ip": "as&asdb(*********/24)", "destination_ip": "control_m", "destination_zone": "core", "service": "mgmt_control_m"}, {"rule_name": "fez_to_core_10", "action": "permit", "source_zone": "fez", "source_ip": "as&asdb(*********/24)", "destination_ip": "patrol_server", "destination_zone": "core", "service": "mgmt_patrol"}, {"rule_name": "fez_to_core_11", "action": "permit", "source_zone": "fez", "source_ip": "*********/24.", "destination_ip": "odc_as_server,odc_betslip,************.", "destination_zone": "core", "service": "any"}, {"rule_name": "fez_to_core_12", "action": "permit", "source_zone": "fez", "source_ip": "as&asdb(*********/24)", "destination_ip": "**********/32.", "destination_zone": "core", "service": "any"}, {"rule_name": "fez_to_core_13", "action": "permit", "source_zone": "fez", "source_ip": "as&asdb(*********/24)", "destination_ip": "zabbix_18.0.4.217", "destination_zone": "core", "service": "any"}, {"rule_name": "fez_to_core_5", "action": "permit", "source_zone": "fez", "source_ip": "any", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "core_to_fez_0", "action": "permit", "source_zone": "core", "source_ip": "rsa_syslog_server", "destination_ip": "fez_syslog_client", "destination_zone": "fez", "service": "rsa_syslog"}, {"rule_name": "core_to_fez_1", "action": "permit", "source_zone": "core", "source_ip": "monsvc_out", "destination_ip": "as_in", "destination_zone": "fez", "service": "ftp"}, {"rule_name": "core_to_fez_2", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "ca_dsvs01,ca_dsvs02,ca_gateway_3", "destination_zone": "fez", "service": "http,icmp"}, {"rule_name": "core_to_fez_3", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "fez", "service": "ftp,icmp,remote_desktop,ssh,TCP8443,snmp"}, {"rule_name": "core_to_fez_test", "action": "permit", "source_zone": "core", "source_ip": "**********.,**********.,**********.", "destination_ip": "any", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_to_fez_4", "action": "permit", "source_zone": "core", "source_ip": "oltp_ab", "destination_ip": "as_in", "destination_zone": "fez", "service": "udp_50086"}, {"rule_name": "core_to_fez_5", "action": "permit", "source_zone": "core", "source_ip": "trendmicro_server", "destination_ip": "any", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_to_fez_6", "action": "permit", "source_zone": "core", "source_ip": "eccom_neteagle,snmp_server(lms),hp_openview", "destination_ip": "bj_prod_lb_a01&a02,snmp_fez_client", "destination_zone": "fez", "service": "snmp"}, {"rule_name": "core_to_fez_7", "action": "permit", "source_zone": "core", "source_ip": "vsts_test_pc", "destination_ip": "asdb", "destination_zone": "fez", "service": "tcp_1433&1434"}, {"rule_name": "core_to_fez_8", "action": "permit", "source_zone": "core", "source_ip": "nbu_server", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "mgmt_nbu"}, {"rule_name": "core_to_fez_9", "action": "permit", "source_zone": "core", "source_ip": "patrol_server", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "mgmt_patrol"}, {"rule_name": "core_to_fez_10", "action": "permit", "source_zone": "core", "source_ip": "control_m", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "mgmt_control_m"}, {"rule_name": "core_to_fez_11", "action": "permit", "source_zone": "core", "source_ip": "************/23.", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_to_fez_12", "action": "permit", "source_zone": "core", "source_ip": "hp_openview02", "destination_ip": "absasdba01", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_fez_32", "action": "permit", "source_zone": "core", "source_ip": "*********.", "destination_ip": "**********.,**********.,***********.,**********.,***********.", "destination_zone": "fez", "service": "TCP_1433,tcp_58662"}, {"rule_name": "core_to_fez_13", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_to_fez_14", "action": "permit", "source_zone": "core", "source_ip": "********/28.", "destination_ip": "absas_lb1_out,as_out", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_to_fez_15", "action": "permit", "source_zone": "core", "source_ip": "csl_ab_mat03_18.2.1.123,csl_ab_mat05_18.2.1.125", "destination_ip": "absasftp_cluster_virture_ip", "destination_zone": "fez", "service": "tcp_8090"}, {"rule_name": "core_to_fez_16", "action": "permit", "source_zone": "core", "source_ip": "csl_ab_opcc01_18.2.1.111,csl_ab_opcc02_18.2.1.112,csl_ab_opcc03_18.2.1.113,csl_ab_opcc04_18.2.1.114,csl_ab_opcc05_18.2.1.115", "destination_ip": "absasftp_cluster_virture_ip,**********/32.", "destination_zone": "fez", "service": "tcp_8090"}, {"rule_name": "core_to_fez_17", "action": "permit", "source_zone": "core", "source_ip": "csl_ab_opcc01_18.2.1.111", "destination_ip": "ca_gateway_2", "destination_zone": "fez", "service": "tcp_8080"}, {"rule_name": "core_to_fez_18", "action": "permit", "source_zone": "core", "source_ip": "zabbix_18.0.4.217", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_to_egw_26", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "eft_gw_in", "destination_zone": "egw", "service": "tcp_9000"}, {"rule_name": "core_to_egw_27", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "absasa03_out", "destination_zone": "egw", "service": "tcp_9000"}, {"rule_name": "egw_to_core_18", "action": "permit", "source_zone": "egw", "source_ip": "lottreasure************/24,uuzz10.0.3.0", "destination_ip": "any", "destination_zone": "core", "service": "tcp_8080"}, {"rule_name": "egw_to_core_19", "action": "permit", "source_zone": "egw", "source_ip": "ABSBISOnline_IN", "destination_ip": "ABSBISOnlinecore", "destination_zone": "core", "service": "ms_sql,smb,nbname"}, {"rule_name": "Core_to_FEZ_19", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "ssh"}, {"rule_name": "Core_to_FEZ_20", "action": "permit", "source_zone": "core", "source_ip": "absbcsaaa01", "destination_ip": "**********/32.", "destination_zone": "fez", "service": "tcp_8090"}, {"rule_name": "fez_to_egw_4", "action": "permit", "source_zone": "fez", "source_ip": "absasa01_in", "destination_ip": "abseftgwa01_in", "destination_zone": "egw", "service": "tcp_9000"}, {"rule_name": "core_to_egw_28", "action": "permit", "source_zone": "core", "source_ip": "bis_ab", "destination_ip": "ABSBISOnline_IN", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "20", "action": "permit", "source_zone": "egw", "source_ip": "********/24.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "any"}, {"rule_name": "21", "action": "permit", "source_zone": "egw", "source_ip": "eft&emai&sms(********/24),egw_ad", "destination_ip": "WSUS", "destination_zone": "core", "service": "http"}, {"rule_name": "22", "action": "permit", "source_zone": "egw", "source_ip": "ABSBISOnline_IN,eft_gw_in", "destination_ip": "zabbix_18.0.4.217", "destination_zone": "core", "service": "tcp_31051"}, {"rule_name": "30", "action": "permit", "source_zone": "core", "source_ip": "*************/32.", "destination_ip": "********/24.,*********/24.", "destination_zone": "egw", "service": "icmp,tcp_8066"}, {"rule_name": "31", "action": "permit", "source_zone": "core", "source_ip": "**********/32.,**********/32.,**********.,**********.", "destination_ip": "any", "destination_zone": "egw", "service": "any"}, {"rule_name": "32", "action": "permit", "source_zone": "core", "source_ip": "zabbix_18.0.4.217", "destination_ip": "eft&emai&sms(********/24)", "destination_zone": "egw", "service": "tcp_31060,tcp_31070"}, {"rule_name": "33", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "eft&emai&sms(********/24)", "destination_zone": "egw", "service": "tcp_41099"}, {"rule_name": "34", "action": "permit", "source_zone": "core", "source_ip": "bis_ab", "destination_ip": "eft_gw_in", "destination_zone": "egw", "service": "ftp_8000_9000"}, {"rule_name": "35", "action": "permit", "source_zone": "core", "source_ip": "CSLC_PASS01_172.26.20.10,CSLC_PASS03_172.26.21.74,CSLC_PASS04_172.26.42.10,CSLC_PASS02_172.26.20.110", "destination_ip": "eft_gw_out", "destination_zone": "egw", "service": "tcp_9000"}, {"rule_name": "36", "action": "permit", "source_zone": "core", "source_ip": "*************/24.", "destination_ip": "ABSBISOnline_OUT", "destination_zone": "egw", "service": "icmp,ssh,tcp_8065"}, {"rule_name": "core_fez_21", "action": "permit", "source_zone": "core", "source_ip": "Bet<PERSON><PERSON>101,<PERSON><PERSON><PERSON>105,<PERSON><PERSON><PERSON>106,<PERSON><PERSON><PERSON>205", "destination_ip": "absas_lb1_out", "destination_zone": "fez", "service": "http_8081,https"}, {"rule_name": "core_fez_22", "action": "permit", "source_zone": "core", "source_ip": "***********/24.,**********/24.,**********/24.", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "ftp,https"}, {"rule_name": "core_fez_23", "action": "permit", "source_zone": "core", "source_ip": "************/24.,**********/24.", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "ftp,https"}, {"rule_name": "egress_egw_16", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.", "destination_ip": "************.,************.", "destination_zone": "egw", "service": "tcp_1433&1434,tcp_8008"}, {"rule_name": "fez_core_14", "action": "permit", "source_zone": "fez", "source_ip": "nbu_server", "destination_ip": "absasdba01,absasdba02,absasdba03,asdb", "destination_zone": "core", "service": "any"}, {"rule_name": "core_fez_24", "action": "permit", "source_zone": "core", "source_ip": "absasdba01,absasdba02,absasdba03,asdb", "destination_ip": "nbu_server", "destination_zone": "fez", "service": "any"}, {"rule_name": "egress_to_core_10", "action": "permit", "source_zone": "egress", "source_ip": "*********/27\"", "destination_ip": "CB_Info_HUB5,CB_Info_HUB6,CB_Info_HUB7", "destination_zone": "core", "service": "tcp_52704"}, {"rule_name": "egress_to fez_10", "action": "permit", "source_zone": "egress", "source_ip": "eft_gw_out", "destination_ip": "************/27.", "destination_zone": "fez", "service": "any"}, {"rule_name": "core_egress_7", "action": "permit", "source_zone": "core", "source_ip": "(*********/24,*********/24.", "destination_ip": "*********/24\"", "destination_zone": "egress", "service": "any"}, {"rule_name": "core_fez_25", "action": "permit", "source_zone": "core", "source_ip": "zabbix_18.0.4.217", "destination_ip": "********/24.,as&asdb(*********/24)", "destination_zone": "fez", "service": "TCP31050_70"}, {"rule_name": "fez_core_15", "action": "permit", "source_zone": "fez", "source_ip": "as_in,asdb", "destination_ip": "zabbix_18.0.4.217", "destination_zone": "core", "service": "TCP31051"}, {"rule_name": "egress_17", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.", "destination_ip": "ABSBISOnline_Cluster_OUT_Group,ABSBISOnline_OUT", "destination_zone": "egw", "service": "tcp_8066"}, {"rule_name": "core_fez_26", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "TCP41099"}, {"rule_name": "core_egress_8", "action": "permit", "source_zone": "core", "source_ip": "BetGW0118.4.21.51,BetGW0118.4.21.52,BetGW0318.4.21.53,BetGW0418.4.21.54", "destination_ip": "UMP10.50.6.0,UMP10.50.7.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "core_fez_27", "action": "permit", "source_zone": "core", "source_ip": "uuzz10.0.3.181,uuzz172.16.20.141,uuzz172.16.23.141,uuzz172.16.23.191,uuzz172.16.23.198,UUZZ2_10.0.3.0", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "http,https,http_8081"}, {"rule_name": "core_egress_9", "action": "permit", "source_zone": "core", "source_ip": "**********/24.", "destination_ip": "UMP10.50.6.0", "destination_zone": "egress", "service": "http"}, {"rule_name": "egress_fez_11", "action": "permit", "source_zone": "egress", "source_ip": "TaoBao_18.4.2.160/27", "destination_ip": "absas_lb1_out,absasftp_cluster_virture_ip", "destination_zone": "fez", "service": "ftp,https"}, {"rule_name": "core_egress_10", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "UMP10.50.6.0,UMP10.50.7.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "egw_core_23", "action": "permit", "source_zone": "egw", "source_ip": "ABSBISOnline_IN", "destination_ip": "stm_svc_out,ABSBISOnlinecore", "destination_zone": "core", "service": "tcp_8069,http"}, {"rule_name": "egw_to_core_24", "action": "permit", "source_zone": "egw", "source_ip": "PAS18.4.130.141", "destination_ip": "TAS18.0.4.180,TTS18.0.4.200", "destination_zone": "core", "service": "tcp_8080"}, {"rule_name": "egw_core_25", "action": "permit", "source_zone": "egw", "source_ip": "PBS", "destination_ip": "zabbix_18.0.4.217", "destination_zone": "core", "service": "TCP31051"}, {"rule_name": "core_egw_37", "action": "permit", "source_zone": "core", "source_ip": "*************/24.", "destination_ip": "ABSBISOnline_OUT", "destination_zone": "egw", "service": "ftp"}, {"rule_name": "core_egw_38", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "ABSBISOnlineA02_IN", "destination_zone": "egw", "service": "tcp_8065"}, {"rule_name": "core_egw_39", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "PAS18.4.130.141", "destination_zone": "egw", "service": "tcp_8080"}, {"rule_name": "core_egw_40", "action": "permit", "source_zone": "core", "source_ip": "TAS18.0.4.171", "destination_ip": "PAS18.4.130.141", "destination_zone": "egw", "service": "tcp_8080,icmp"}, {"rule_name": "core_egw_41", "action": "permit", "source_zone": "core", "source_ip": "zabbix_18.0.4.217", "destination_ip": "PBS", "destination_zone": "egw", "service": "TCP31050_70"}, {"rule_name": "core_fez_28", "action": "permit", "source_zone": "core", "source_ip": "**********.,**********/,**********.", "destination_ip": "as&asdb(*********/24)", "destination_zone": "fez", "service": "ssh"}, {"rule_name": "egress_to_core_12", "action": "permit", "source_zone": "egress", "source_ip": "UMP*********/22", "destination_ip": "*********.", "destination_zone": "core", "service": "tcp_7003"}, {"rule_name": "core_egress_11", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "**********/32.", "destination_zone": "egress", "service": "https"}, {"rule_name": "core_fez_29", "action": "permit", "source_zone": "core", "source_ip": "**************/32.", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "https"}, {"rule_name": "core_fez_30", "action": "permit", "source_zone": "core", "source_ip": "**********.,absbcsaaa07", "destination_ip": "absasftp_cluster_virture_ip,ABSASFTP_ClusterIP_IN", "destination_zone": "fez", "service": "tcp_8090"}, {"rule_name": "core_fez_31", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "absas_lb1_out", "destination_zone": "fez", "service": "https,http_8081"}, {"rule_name": "core_egress_12", "action": "permit", "source_zone": "core", "source_ip": "*********/32.", "destination_ip": "CSLC*************", "destination_zone": "fez", "service": "ftp"}, {"rule_name": "core_egress_13", "action": "permit", "source_zone": "core", "source_ip": "500wan**************", "destination_ip": "UMP*********/22", "destination_zone": "egress", "service": "ftp"}, {"rule_name": "egress_egw18", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "************.", "destination_zone": "egw", "service": "tcp_8008"}, {"rule_name": "egress_core_13", "action": "permit", "source_zone": "egress", "source_ip": "************.", "destination_ip": "TTS18.0.4.200", "destination_zone": "core", "service": "tcp_8080"}, {"rule_name": "egress_core_14", "action": "permit", "source_zone": "egress", "source_ip": "500wan**************", "destination_ip": "CB_Info_HUB7", "destination_zone": "core", "service": "tcp_52701,tcp_52704"}, {"rule_name": "egress_core_15", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,**********/32.,**********/32.,**********/32.,**********/32.", "destination_ip": "zabbix_18.0.4.217", "destination_zone": "core", "service": "TCP31051"}, {"rule_name": "egress_fez_12", "action": "permit", "source_zone": "egress", "source_ip": "500wan**************,absasftp_cluster_virture_ip", "destination_ip": "absas_lb1_out", "destination_zone": "fez", "service": "https,ftp"}, {"rule_name": "core_egress_14", "action": "permit", "source_zone": "core", "source_ip": "zabbix_18.0.4.217", "destination_ip": "**********/32.", "destination_zone": "egress", "service": "TCP31050_70"}, {"rule_name": "egress_to_core_17", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,***********/32.", "destination_ip": "**********/32.", "destination_zone": "core", "service": "http_8081"}, {"rule_name": "DUANXIN", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "egress_to_egress_1", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "https"}, {"rule_name": "uuzz", "action": "permit", "source_zone": "core", "source_ip": "uuzz_10.0.3.160/28", "destination_ip": "UMP*********/22", "destination_zone": "egress", "service": "http"}, {"rule_name": "egress_to_egw_19", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,**********/32.", "destination_ip": "************.", "destination_zone": "egw", "service": "tcp_8008"}, {"rule_name": "core_to_egw_30", "action": "permit", "source_zone": "core", "source_ip": "*************.", "destination_ip": "pas_18.4.130.141/32.", "destination_zone": "egw", "service": "tcp_8080,icmp"}, {"rule_name": "egw_to_core_27", "action": "permit", "source_zone": "egw", "source_ip": "pas_18.4.130.141/32.,pas_18.4.130.142/32.,pasdb_18.4.129.141.,pasdb_18.4.129.142.", "destination_ip": "TLS_18.0.4.133.", "destination_zone": "core", "service": "tcp_6662"}, {"rule_name": "test_tiantou_CSLC", "action": "permit", "source_zone": "core", "source_ip": "BetGW0118.4.21.51,BetGW0118.4.21.52,BetGW0318.4.21.53,BetGW0418.4.21.54,group*********", "destination_ip": "CSLC_103.11.1.12,CSLC_172.26.22.108,CSLC_172.26.22.200", "destination_zone": "egress", "service": "TCP_4433,ftp,icmp"}, {"rule_name": "jing<PERSON><PERSON>wang", "action": "permit", "source_zone": "egress", "source_ip": "jing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "destination_ip": "*********/24.", "destination_zone": "core", "service": "icmp,tcp_52701,tcp_52704"}, {"rule_name": "ICMPANY", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "icmp"}, {"rule_name": "AQSM", "action": "permit", "source_zone": "any", "source_ip": "安全扫描", "destination_ip": "any", "destination_zone": "any", "service": "any"}, {"rule_name": "core_fez_33", "action": "permit", "source_zone": "core", "source_ip": "FSS01", "destination_ip": "**********/32.", "destination_zone": "fez", "service": "ftp"}, {"rule_name": "core_fez_34", "action": "permit", "source_zone": "core", "source_ip": "*********/32.", "destination_ip": "**********/32.", "destination_zone": "fez", "service": "telnet"}, {"rule_name": "fez_core_17", "action": "permit", "source_zone": "fez", "source_ip": "**********/32.", "destination_ip": "*********/32.", "destination_zone": "core", "service": "telnet"}, {"rule_name": "core_fez_35", "action": "permit", "source_zone": "core", "source_ip": "OLTPAB", "destination_ip": "**********/32.", "destination_zone": "fez", "service": "telnet"}, {"rule_name": "Core_egress_15", "action": "permit", "source_zone": "core", "source_ip": "500wan192.168.41.160/32,500wan**************,500wan**************.,500wan_192.168.41.95,500wan_192.168.41.96", "destination_ip": "*********/24.", "destination_zone": "egress", "service": "tcp_52701,tcp_52704"}, {"rule_name": "core_fez_36", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "AS_18.4.11.11", "destination_zone": "fez", "service": "http_8081"}, {"rule_name": "core_to_fez 21", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "*********/24.", "destination_zone": "fez", "service": "https,http,TCP8081"}, {"rule_name": "core_to_fez_31", "action": "permit", "source_zone": "core", "source_ip": "**********/23.,********/32.,********/16.,********/24.", "destination_ip": "**********/24.,**********/24.", "destination_zone": "fez", "service": "icmp,tcp8080,tcp_50443,telnet,ssh"}, {"rule_name": "fez_to_core_17", "action": "permit", "source_zone": "fez", "source_ip": "**********/24.", "destination_ip": "**********/23.,********/32.,********/16.", "destination_zone": "core", "service": "tcp31306"}, {"rule_name": "u2 client to scp", "action": "permit", "source_zone": "egress", "source_ip": "yinwu_192.168.8.0", "destination_ip": "SCPvip_18.4.21.201", "destination_zone": "core", "service": "icmp,telnet,tcp_50443,https"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "egress", "source_ip": "<PERSON><PERSON><PERSON>bao_172.16.2.0/24,tencent_131.87.32.0/24", "destination_ip": "SCPAPIGW_vip", "destination_zone": "fez", "service": "icmp,tcp_50443"}, {"rule_name": "<PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "fez", "source_ip": "SCPAPIGW_vip", "destination_ip": "zhifubao_172.16.2.0/24", "destination_zone": "egress", "service": "tcp_50443"}, {"rule_name": "core_to_fez_32", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "SCPAPIGW_vip", "destination_zone": "fez", "service": "tcp_50443"}, {"rule_name": "fez_to_core_18", "action": "permit", "source_zone": "fez", "source_ip": "**********/24.", "destination_ip": "********/16.", "destination_zone": "core", "service": "any"}, {"rule_name": "fez_to_core_19", "action": "permit", "source_zone": "fez", "source_ip": "********/24/,********/24.", "destination_ip": "*********/24.,*********/24.,*********/24.,*********/24.,*********/24.", "destination_zone": "core", "service": "any"}, {"rule_name": "core_fez_40", "action": "permit", "source_zone": "core", "source_ip": "*********/24.,*********/24.,*********/24.,*********/24.,*********/24.,********/16.", "destination_ip": "********/24.,********/24/", "destination_zone": "fez", "service": "any"}, {"rule_name": "Core_to_egress_16", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "**********/32.,**********/32.", "destination_zone": "egress", "service": "ssh"}, {"rule_name": "4.199_to_64.30", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "absas_lb1_out", "destination_zone": "fez", "service": "http_8082,https"}, {"rule_name": "4.234_to_64.30", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "absas_lb1_out", "destination_zone": "fez", "service": "http_8082,https"}, {"rule_name": "linshi", "action": "permit", "source_zone": "fez", "source_ip": "***********.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "solarwinds", "action": "permit", "source_zone": "fez", "source_ip": "***********.", "destination_ip": "any", "destination_zone": "any", "service": "any"}, {"rule_name": "solarwinds1", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "snmp,snmptrap"}, {"rule_name": "solarwinds2", "action": "permit", "source_zone": "core", "source_ip": "*********.", "destination_ip": "***********.", "destination_zone": "fez", "service": "any"}, {"rule_name": "solarwinds3", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "***********.", "destination_zone": "fez", "service": "any"}, {"rule_name": "solarwinds4", "action": "permit", "source_zone": "egw", "source_ip": "**********.", "destination_ip": "***********.", "destination_zone": "fez", "service": "any"}, {"rule_name": "syslog", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "syslog"}, {"rule_name": "egw_to_core(WSUS)", "action": "permit", "source_zone": "egw", "source_ip": "18.4.3.1_50", "destination_ip": "WSUS", "destination_zone": "core", "service": "tcp_8530_8531"}, {"rule_name": "egw_to_core(WSUS)2", "action": "permit", "source_zone": "egw", "source_ip": "18.4.4.1_18.4.4.50", "destination_ip": "WSUS", "destination_zone": "core", "service": "tcp_8530_8531"}, {"rule_name": "egw_to_core(WSUS)3", "action": "permit", "source_zone": "egw", "source_ip": "18.4.129_18.4.130", "destination_ip": "WSUS", "destination_zone": "core", "service": "tcp_8530_8531"}, {"rule_name": "fez_to_core(WSUS)", "action": "permit", "source_zone": "fez", "source_ip": "18.4.14.1_24", "destination_ip": "WSUS", "destination_zone": "core", "service": "tcp_8530_8531"}, {"rule_name": "core_to_fez_34", "action": "permit", "source_zone": "core", "source_ip": "********/24.,*********/24.", "destination_ip": "********/24/,********/24.", "destination_zone": "fez", "service": "tcp_20201,tcp31306"}, {"rule_name": "fez_to_egress_3", "action": "permit", "source_zone": "fez", "source_ip": "********/24/", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "tcp_9080,icmp,tcp_1521"}, {"rule_name": "core_to_egress_12", "action": "permit", "source_zone": "core", "source_ip": "********/24.,*********/24.,***********;", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "tcp_3080,http,icmp,ssh,tcp1521"}, {"rule_name": "core_to_egress_13", "action": "permit", "source_zone": "core", "source_ip": "********/24.,*********/24.", "destination_ip": "sporttery_111.205.55.153\",sys_111.205.55.152\"", "destination_zone": "egress", "service": "http,icmp,ssh,tcp,TCP_80_3080_9080"}, {"rule_name": "egress_to_fez", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.", "destination_ip": "********/24/,APIGWF5", "destination_zone": "fez", "service": "tcp_20201"}, {"rule_name": "fez_to_egress_4", "action": "permit", "source_zone": "fez", "source_ip": "********/24/", "destination_ip": "***********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_CCB_124.127.94.51", "action": "permit", "source_zone": "fez", "source_ip": "ccbgw_********91,FAMBKGWA01_********1,FAMBKGMGA01_18.5.1.21", "destination_ip": "ccb_124.127.94.51", "destination_zone": "egress", "service": "https,icmp"}, {"rule_name": "To_ccb_128.192.182.4", "action": "permit", "source_zone": "fez", "source_ip": "ccbgw_********91", "destination_ip": "ccb_128.192.182.4", "destination_zone": "egress", "service": "icmp,tcp_8101_8103_8104"}, {"rule_name": "To_internet", "action": "permit", "source_zone": "fez", "source_ip": "ccbgw_********91", "destination_ip": "any", "destination_zone": "egress", "service": "icmp,http,https"}, {"rule_name": "To_CSLC_172.26.5.155", "action": "permit", "source_zone": "fez", "source_ip": "FAMELPGWA01_18.5.2.1,FAMELPGWB02_18.5.2.2,ELP", "destination_ip": "CSLC_172.26.5.155,CSLC_172.26.5.120,ELP_F5", "destination_zone": "egress", "service": "TCP8091,icmp"}, {"rule_name": "新竟彩网_to_webdcF5", "action": "permit", "source_zone": "egress", "source_ip": "新竟彩网", "destination_ip": "webdc18.1.3.10,/24\"", "destination_zone": "core", "service": "tcp_52701"}, {"rule_name": "webdcF5_to_新竟彩网", "action": "permit", "source_zone": "core", "source_ip": "webdc18.1.3.10", "destination_ip": "新竟彩网", "destination_zone": "egress", "service": "any"}, {"rule_name": "egress_to_fez_4444", "action": "permit", "source_zone": "egress", "source_ip": "***********.", "destination_ip": "18..5.0.0.", "destination_zone": "fez", "service": "tcp_4444,Tcp_7001"}, {"rule_name": "egress_to_core_4444", "action": "permit", "source_zone": "egress", "source_ip": "***********.", "destination_ip": "18..5.0.0.", "destination_zone": "core", "service": "tcp_4444,TCP_7001"}, {"rule_name": "egress_to_core_ICMP", "action": "permit", "source_zone": "egress", "source_ip": "********./27", "destination_ip": "********/16.", "destination_zone": "core", "service": "icmp"}, {"rule_name": "jingcaiwangzhifuxitong", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "*********/24.,*********/24.,*********/24.,*********/24.,*********/24.,*********/24.,*********.", "destination_zone": "core", "service": "any"}, {"rule_name": "core_to_liantongjingcai(2017092)", "action": "permit", "source_zone": "core", "source_ip": "**********(C043)", "destination_ip": "liantongjingcai", "destination_zone": "egress", "service": "any"}, {"rule_name": "internet", "action": "permit", "source_zone": "core", "source_ip": "**********.,********.,********.,************.,***********/32.,************./32,************./32,**********./32,zhiqi_18.0.200.253,*********/24.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "ssh_http", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "*********/22.", "destination_zone": "egress", "service": "http,ssh"}, {"rule_name": "<PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "<PERSON><PERSON><PERSON>", "destination_ip": "To_T2&T3_106.37.229.46", "destination_zone": "egress", "service": "any"}, {"rule_name": "<PERSON><PERSON><PERSON>_in", "action": "permit", "source_zone": "egress", "source_ip": "To_T2&T3_106.37.229.46", "destination_ip": "<PERSON><PERSON><PERSON>", "destination_zone": "core", "service": "any"}, {"rule_name": "jiankong", "action": "permit", "source_zone": "core", "source_ip": "zabbix_18.5.16.231&232", "destination_ip": "zhifu_********/24_********/24", "destination_zone": "fez", "service": "any"}, {"rule_name": "jiankong_", "action": "permit", "source_zone": "fez", "source_ip": "zhifu_********/24_********/24", "destination_ip": "zabbix_18.5.16.231&232", "destination_zone": "core", "service": "any"}, {"rule_name": "To_TencentCloud", "action": "permit", "source_zone": "core", "source_ip": "Corenat********/8", "destination_ip": "Tencent_Coudnet_10.192.0.0/16", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_keji_172.26.5.155", "action": "permit", "source_zone": "core", "source_ip": "OMS_*********/24", "destination_ip": "CSLC_172.26.5.155", "destination_zone": "egress", "service": "any"}, {"rule_name": "From_TencentCloud", "action": "permit", "source_zone": "egress", "source_ip": "TencentCloud", "destination_ip": "any", "destination_zone": "fez", "service": "any"}, {"rule_name": "From_Tencent_2", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "Corenat********/8", "destination_zone": "core", "service": "any"}, {"rule_name": "To_WechatNum_18.4.39.122", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "WechatNum_18.4.39.122", "destination_zone": "core", "service": "any"}, {"rule_name": "wechat", "action": "permit", "source_zone": "core", "source_ip": "WechatNum_18.4.39.122", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_inforhub", "action": "permit", "source_zone": "egress", "source_ip": "20.0.101.41_", "destination_ip": "18.1.11.7_", "destination_zone": "core", "service": "any"}, {"rule_name": "fez_To_TencentCloud", "action": "permit", "source_zone": "fez", "source_ip": "********/8_", "destination_ip": "Tencent_Coudnet_10.192.0.0/16", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_APIGW", "action": "permit", "source_zone": "core", "source_ip": "ceshiClient_********/24", "destination_ip": "APIGW", "destination_zone": "fez", "service": "TCP_9201"}, {"rule_name": "snmp", "action": "permit", "source_zone": "any", "source_ip": "any", "destination_ip": "any", "destination_zone": "any", "service": "snmp,snmptrap"}, {"rule_name": "To__F5_PreGW", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "F5_PreGW", "destination_zone": "core", "service": "https,http"}, {"rule_name": "PREGW", "action": "permit", "source_zone": "core", "source_ip": "PREGW", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_ELP", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "new_elp", "destination_zone": "egress", "service": "any"}, {"rule_name": "AB_To_NewSit", "action": "permit", "source_zone": "egress", "source_ip": "ab_ip", "destination_ip": "New_Sit", "destination_zone": "core", "service": "any"}, {"rule_name": "LINSHI", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "webserver", "service": "any"}, {"rule_name": "LINSHI_2", "action": "permit", "source_zone": "webserver", "source_ip": "any", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "To_Core", "action": "permit", "source_zone": "core", "source_ip": "Core", "destination_ip": "zhifu_core", "destination_zone": "fez", "service": "any"}, {"rule_name": "egress_to_egw_30", "action": "permit", "source_zone": "egress", "source_ip": "keji", "destination_ip": "EFTGW", "destination_zone": "egw", "service": "TCP_9000"}, {"rule_name": "zabbix_23215", "action": "permit", "source_zone": "egw", "source_ip": "zabbix_18.4.4.14", "destination_ip": "zabbix_client", "destination_zone": "core", "service": "TCP_23215"}, {"rule_name": "To_keji_elpgw", "action": "permit", "source_zone": "fez", "source_ip": "AS_18.4.11.11", "destination_ip": "keji_elpgw", "destination_zone": "egress", "service": "TCP_9000"}, {"rule_name": "To_EFTGW", "action": "permit", "source_zone": "egw", "source_ip": "18.4.3.11_", "destination_ip": "18.4.21.51_", "destination_zone": "core", "service": "TCP_23215,TCP_20001,TCP_9003"}, {"rule_name": "To_18.5.2.101", "action": "permit", "source_zone": "core", "source_ip": "18.2.1.4_", "destination_ip": "18.5.2.101_", "destination_zone": "fez", "service": "any"}, {"rule_name": "zhifu1.1_3", "action": "permit", "source_zone": "core", "source_ip": "<PERSON><PERSON><PERSON><PERSON>", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "From_zhifu1.1", "action": "permit", "source_zone": "core", "source_ip": "zhifu1.1_5", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "zhifu1.1_port_21514", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "zhifu_ip.port=21514", "destination_zone": "core", "service": "TCP_21514"}, {"rule_name": "From_zhifu1.1_to_jianhang", "action": "permit", "source_zone": "webserver", "source_ip": "zhifu1.1_webserver", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "<PERSON>_<PERSON><PERSON>", "action": "permit", "source_zone": "fez", "source_ip": "<PERSON>_<PERSON><PERSON>", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "From_Yinlian", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "zhifu_ip.port=21514", "destination_zone": "fez", "service": "any"}, {"rule_name": "To_docker_7001", "action": "permit", "source_zone": "core", "source_ip": "Core_*********/24", "destination_ip": "To_Docker_7001", "destination_zone": "fez", "service": "Tcp_7001"}, {"rule_name": "To_500w", "action": "permit", "source_zone": "core", "source_ip": "Core_net", "destination_ip": "500Wan", "destination_zone": "egress", "service": "any"}, {"rule_name": "From_500Wan", "action": "permit", "source_zone": "egress", "source_ip": "500Wan", "destination_ip": "Core_net", "destination_zone": "core", "service": "any"}, {"rule_name": "To_Dsvs", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "Dsvs_18.2.1.245", "destination_zone": "core", "service": "any"}, {"rule_name": "To_pptp", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "PPTP", "destination_zone": "core", "service": "any"}, {"rule_name": "From_CeshiManage", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "********/32_", "destination_zone": "fez", "service": "TCP_21598"}, {"rule_name": "From_yunying", "action": "permit", "source_zone": "egw", "source_ip": "Yunying20180206", "destination_ip": "From_yunying_to_T1", "destination_zone": "core", "service": "any"}, {"rule_name": "From_yunying20180208", "action": "permit", "source_zone": "egress", "source_ip": "Yunying20180206", "destination_ip": "From_yunying_to_T1", "destination_zone": "core", "service": "any"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "From_yunying_to_T1", "destination_ip": "Yunying20180206", "destination_zone": "egress", "service": "any"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "*********.", "destination_ip": "*********.,*********.", "destination_zone": "fez", "service": "TCP21405"}, {"rule_name": "yunying_test", "action": "permit", "source_zone": "egress", "source_ip": "/24\"", "destination_ip": "any", "destination_zone": "egw", "service": "any"}, {"rule_name": "yunying_test_01", "action": "permit", "source_zone": "egress", "source_ip": "/24\"", "destination_ip": "any", "destination_zone": "fez", "service": "any"}, {"rule_name": "yunying_test_02", "action": "permit", "source_zone": "egress", "source_ip": "/24\"", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "ceshi_yunying", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "/24\"", "destination_zone": "egress", "service": "any"}, {"rule_name": "TEST18.2.17.102TOF5VIP", "action": "permit", "source_zone": "core", "source_ip": "test18.2.17.102", "destination_ip": "F5VIP18.4.64.30", "destination_zone": "fez", "service": "TCP8443"}, {"rule_name": "core_to_egress_17", "action": "permit", "source_zone": "core", "source_ip": "********/24.,**********/32.", "destination_ip": "F5VIP18.4.64.30,*********/24.", "destination_zone": "egress", "service": "TCP8443"}, {"rule_name": "core_to_fez", "action": "permit", "source_zone": "core", "source_ip": "/32\"", "destination_ip": "/24\"", "destination_zone": "fez", "service": "tcp31306"}, {"rule_name": "fez_core_27", "action": "permit", "source_zone": "fez", "source_ip": "/24\"", "destination_ip": "/24\"", "destination_zone": "core", "service": "TCP_7001"}, {"rule_name": "core_fez_57", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "/24\"", "destination_zone": "fez", "service": "TCP_7001"}, {"rule_name": "yunyinggongsi_2", "action": "permit", "source_zone": "egress", "source_ip": "yygg", "destination_ip": "yy", "destination_zone": "fez", "service": "any"}, {"rule_name": "To_18.4.11.35", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "18.4.11.35_", "destination_zone": "fez", "service": "any"}, {"rule_name": "To_18.5.128.14:7001", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "18.5.128.14_", "destination_zone": "core", "service": "TCP_7001"}, {"rule_name": "TIDB_core_to_egress", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "TIDB_RS", "destination_zone": "egress", "service": "TCP_4000"}, {"rule_name": "Tidb_egress_to_core", "action": "permit", "source_zone": "egress", "source_ip": "Tidb", "destination_ip": "any", "destination_zone": "core", "service": "TCP_3000_8686"}, {"rule_name": "TiDB_core_to_egress", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "Tidb", "destination_zone": "egress", "service": "TCP_3000_8686"}, {"rule_name": "any", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.", "destination_ip": "*********/24.", "destination_zone": "core", "service": "any"}, {"rule_name": "any1", "action": "permit", "source_zone": "core", "source_ip": "*********/24.,********/24.", "destination_ip": "*********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "any2", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "*********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "any3", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.,T4_172.16.8.0/24,/24\"", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "********73", "action": "permit", "source_zone": "core", "source_ip": "********73_", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "G3 业务地址访问", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "G3", "destination_zone": "egress", "service": "https,http,ssh"}, {"rule_name": "to_OPS", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "to_OPS_", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "Egress_to_OFFICE_105", "action": "permit", "source_zone": "egress", "source_ip": "18_5_252_0,*********/24.", "destination_ip": "*************/**************", "destination_zone": "core", "service": "any"}, {"rule_name": "Office_105_TO_Egress", "action": "permit", "source_zone": "core", "source_ip": "*************.", "destination_ip": "18_5_252_0,*********/24.,*********/22.,***********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "to_F5_***********", "action": "permit", "source_zone": "core", "source_ip": "********/24.,*********/24.", "destination_ip": "any", "destination_zone": "egress", "service": "icmp,tcp_8088"}, {"rule_name": "Egress_to_Core_Office", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "*************/24.", "destination_zone": "core", "service": "any"}, {"rule_name": "Office_Core_to_Egress", "action": "permit", "source_zone": "core", "source_ip": "*************/24.", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "to_172.16.1.1", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "*********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "to_SWA04", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "to_172.16.4.0", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "XWH_172.16.7.0/24", "destination_zone": "egress", "service": "any"}, {"rule_name": "18.0.5.41_to_DSVS", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "fez", "service": "tcp_8000,icmp"}, {"rule_name": "CLJC_FEZ", "action": "permit", "source_zone": "core", "source_ip": "CSLC_172.26.5.155", "destination_ip": "elpgw_18.5.2.71/72", "destination_zone": "fez", "service": "TCP8091"}, {"rule_name": "FEZ_CLJC", "action": "permit", "source_zone": "fez", "source_ip": "elpgw_18.5.2.71/72", "destination_ip": "CSLC_172.26.5.155", "destination_zone": "core", "service": "TCP8091"}, {"rule_name": "BanGong_15/16", "action": "permit", "source_zone": "core", "source_ip": "192.168.105.15_16", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "tcp_22,TCP_10500,http,TCP_2181,TCP_7001,tcp31306"}, {"rule_name": "Egress_TO_BanGong_15/16", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "192.168.105.15_16", "destination_zone": "core", "service": "TCP_162,tcp_10051"}, {"rule_name": "G3_TO_***********", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "***********.", "destination_zone": "core", "service": "TCP_28081"}, {"rule_name": "Core_to_FEZ_**********_8000", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "**********/32.", "destination_zone": "fez", "service": "tcp_8000"}, {"rule_name": "BanGong_52", "action": "permit", "source_zone": "core", "source_ip": "192.168.52.234_236", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "tcp8080,tcp_22"}, {"rule_name": "fez_core_28", "action": "permit", "source_zone": "fez", "source_ip": "********/24.", "destination_ip": "***********/24_", "destination_zone": "core", "service": "http"}, {"rule_name": "core_fez_280", "action": "permit", "source_zone": "core", "source_ip": "***********/24_", "destination_ip": "********/24.", "destination_zone": "fez", "service": "tcp_22601"}, {"rule_name": "core_fez_55", "action": "permit", "source_zone": "core", "source_ip": "***********/24_,172.26.5.155_,********/24.", "destination_ip": "**********/24_,********/24.", "destination_zone": "fez", "service": "tcp_21406,icmp"}, {"rule_name": "Core_<PERSON>z_64_65", "action": "permit", "source_zone": "core", "source_ip": "/24\"", "destination_ip": "/24\"", "destination_zone": "fez", "service": "TCP_20880"}, {"rule_name": "182.168.32.108_TO_18.5.252/253", "action": "permit", "source_zone": "core", "source_ip": "**************.", "destination_ip": "18.5.252/253", "destination_zone": "egress", "service": "TCP_32600,ssh,http"}, {"rule_name": "**********_Internet", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "18.0.10_TO_18.5.252", "action": "permit", "source_zone": "core", "source_ip": "**********/68", "destination_ip": "************.,************.,************.,***********.", "destination_zone": "egress", "service": "TCP_28081,TCP_28070"}, {"rule_name": "EGRESS_Core", "action": "permit", "source_zone": "egress", "source_ip": "18.5.253.61_63", "destination_ip": "18.5.48.61_63", "destination_zone": "core", "service": "tcp_29092"}, {"rule_name": "18.5.252_TO_***********_28081", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "***********.", "destination_zone": "core", "service": "TCP_28081"}, {"rule_name": "TiCai_APP_TO_T1", "action": "permit", "source_zone": "egress", "source_ip": "T1Cai_APP", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "YunYing_TO_T1", "action": "permit", "source_zone": "egress", "source_ip": "172.30.*.*.", "destination_ip": "**********/24.,*********/24.,*********/32.,18.5.128.*,**********.,*********.,**********.,/24\",***********.", "destination_zone": "core", "service": "any"}, {"rule_name": "TO_***********_80", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "***********.", "destination_zone": "egress", "service": "http"}, {"rule_name": "test", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "/24\",***********.", "destination_zone": "core", "service": "any"}, {"rule_name": "To_***********_30400", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "***********.", "destination_zone": "egress", "service": "TCP30400"}, {"rule_name": "ZiDongHua_TO_G3", "action": "permit", "source_zone": "core", "source_ip": "*************.,192.168.105.15_16", "destination_ip": "**********/24.,**********.,**********/24.,18.5.6.13_TO_18,*********/22.,************/32.,***********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "TCP_30400", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "***********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "<PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "18.5.252.1_211_212_213", "destination_zone": "egress", "service": "any"}, {"rule_name": "TESTYUNPINGTAItoT1DMZ", "action": "permit", "source_zone": "egress", "source_ip": "TESTYUNPINGTAI", "destination_ip": "any", "destination_zone": "dmz", "service": "any"}, {"rule_name": "TESTYUNPINGTAItoT1CORE", "action": "permit", "source_zone": "egress", "source_ip": "TESTYUNPINGTAI", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "TESTYUNPINGTAItoT1EGW", "action": "permit", "source_zone": "egress", "source_ip": "TESTYUNPINGTAI", "destination_ip": "any", "destination_zone": "egw", "service": "any"}, {"rule_name": "TESTYUNPINGTAItoT1FEZ", "action": "permit", "source_zone": "egress", "source_ip": "TESTYUNPINGTAI", "destination_ip": "any", "destination_zone": "fez", "service": "any"}, {"rule_name": "Egress_TO_BanGong_192.168.200.", "action": "permit", "source_zone": "egress", "source_ip": "*********/22.", "destination_ip": "*************.", "destination_zone": "core", "service": "any"}, {"rule_name": "yunpingtai", "action": "permit", "source_zone": "egress", "source_ip": "*********/22.", "destination_ip": "***********.", "destination_zone": "core", "service": "TCP_28081"}, {"rule_name": "18.7.8_TO_Core", "action": "permit", "source_zone": "egress", "source_ip": "********/24.", "destination_ip": "*********/24.,********/24.,*********/24.,*********/24.,**********/24.,**********/24.,*********/24.,********/16.", "destination_zone": "core", "service": "TCP_9001"}, {"rule_name": "egress_to_core", "action": "permit", "source_zone": "egress", "source_ip": "********/24.", "destination_ip": "********/16.,*********/24.,********/24.,*********/24.,*********/24.,**********/24.,**********.,*********/24_", "destination_zone": "core", "service": "TCP9100"}, {"rule_name": "Core_G3", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "18.5.252.1_211_212_213", "destination_zone": "egress", "service": "http,tcp8080"}, {"rule_name": "G3_Core", "action": "permit", "source_zone": "egress", "source_ip": "18.5.252.1_211_212_213", "destination_ip": "********/16.", "destination_zone": "core", "service": "tcp8080,http"}, {"rule_name": "core_egress", "action": "permit", "source_zone": "core", "source_ip": "**********/32_", "destination_ip": "***********/32_", "destination_zone": "egress", "service": "any"}, {"rule_name": "t1_w5r", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "***********.", "destination_zone": "egress", "service": "TCP_5480"}, {"rule_name": "t1<PERSON><PERSON><PERSON><PERSON>_w5r", "action": "permit", "source_zone": "core", "source_ip": "**********.", "destination_ip": "***********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_To_XWHT3", "action": "permit", "source_zone": "core", "source_ip": "T1_<PERSON>,<PERSON><PERSON>_<PERSON>,T1_vlan10,172.16.110.0_24", "destination_ip": "XWH_T3,172.16.11.0_24", "destination_zone": "egress", "service": "any"}, {"rule_name": "XWH_T3 To T1", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T3,172.16.11.0_24", "destination_ip": "T1_<PERSON>,T1_vlan10,<PERSON><PERSON>_VC,172.16.110.0_24", "destination_zone": "core", "service": "any"}, {"rule_name": "To_172.16.11.0/24", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "172.16.11.0_24", "destination_zone": "core", "service": "any"}, {"rule_name": "egress_to_core_18.0.204.14", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "***********.", "destination_zone": "core", "service": "any"}, {"rule_name": "g3chat", "action": "permit", "source_zone": "egress", "source_ip": "**********.,***********.", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "to_internet1", "action": "permit", "source_zone": "core", "source_ip": "18.0.99.99_100", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "9443", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "**********_16", "destination_zone": "egress", "service": "TCP9443,TCP_8013,https"}, {"rule_name": "To_500wan", "action": "permit", "source_zone": "core", "source_ip": "T3_38.5.80.0/21,T3_38.5.72.0/21", "destination_ip": "500wan_10.0.1.211/32", "destination_zone": "egress", "service": "any"}, {"rule_name": "500wan1", "action": "permit", "source_zone": "core", "source_ip": "T3_38.5.80.0/21", "destination_ip": "500wan_10.0.1.211/32", "destination_zone": "egress", "service": "any"}, {"rule_name": "500wan2", "action": "permit", "source_zone": "egress", "source_ip": "500wan_10.0.1.211/32", "destination_ip": "38.5.34.0_24,28.4.246.0_24,T3_38.5.80.0/21", "destination_zone": "core", "service": "any"}, {"rule_name": "G3_WCS部署", "action": "permit", "source_zone": "egress", "source_ip": "**********.,**********/24.,********/22.,*********/22.,***********/32.", "destination_ip": "**********/32.,**********/32.", "destination_zone": "core", "service": "TCP_28080"}, {"rule_name": "To_**********", "action": "permit", "source_zone": "egress", "source_ip": "***********.", "destination_ip": "**********.", "destination_zone": "core", "service": "TCP_50094"}, {"rule_name": "To_YunGuanPingTai", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "YunGuanPingTai_172.16.16.1", "destination_zone": "egress", "service": "http,tcp_3306,TCP_5000,tcp_8080"}, {"rule_name": "T1_To_XWHT4_tia<PERSON><PERSON>ji", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "XWH_T4_NAT_18.6.4.27,XWH_T4_NAT_18.6.4.28", "destination_zone": "egress", "service": "ssh,TCP_162,tcp_10051"}, {"rule_name": "BG_To_XWHG3", "action": "permit", "source_zone": "core", "source_ip": "192.168.52.234_236", "destination_ip": "XWH_T4_NAT_18.6.4.27,XWH_T4_NAT_18.6.4.28", "destination_zone": "egress", "service": "TCP8011"}, {"rule_name": "XWHG3_To_BG", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T4_NAT_**********", "destination_ip": "192.168.52.234_236", "destination_zone": "core", "service": "TCP_32600"}, {"rule_name": "core_XWH_G3", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "********.", "destination_zone": "egress", "service": "tcp_22,TCP_31306,TCP9100,TCP_9090,TCP_9091,TCP_9093,TCP_3000,TCP_3558"}, {"rule_name": "T15Fcsj_to_G3_F5", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "*********5_16_17", "destination_zone": "egress", "service": "TCP_30300,TCP_30416,TCP_8080"}, {"rule_name": "********_T4", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "*********/21_", "destination_zone": "egress", "service": "any"}, {"rule_name": "********/32_T4_F5", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "**********/32_", "destination_zone": "egress", "service": "TCP30400"}, {"rule_name": "123", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********.,*********/24.", "destination_ip": "*********/24_", "destination_zone": "egress", "service": "any"}, {"rule_name": "CORE_EGRESS", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "18.5.88.1_9,T3_NAT_18.6.3.0", "destination_zone": "egress", "service": "TCP_29200,tcp_30000,ssh"}, {"rule_name": "new_T3_to_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "**********.,**********.,**********/32.,**********.", "destination_ip": "**************.,************.,CSLC_104.21.2.0/24,CSLC_104.21.1.0/24", "destination_zone": "core", "service": "TCP_8089,TCP_8087,http,TCP_20050,TCP_8090,TCP_8086,tcp_8088,TCP_8083"}, {"rule_name": "**************_3.133", "action": "permit", "source_zone": "core", "source_ip": "**************.", "destination_ip": "**********.,**********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "CSL_to_new_T3", "action": "permit", "source_zone": "core", "source_ip": "************.,*************.", "destination_ip": "*********.,********.", "destination_zone": "egress", "service": "TCP_7001,https"}, {"rule_name": "G3tsinfluxdb01", "action": "permit", "source_zone": "egress", "source_ip": "***********.", "destination_ip": "***********.", "destination_zone": "core", "service": "TCP_8086"}, {"rule_name": "TO_192.168.181.224/221", "action": "permit", "source_zone": "egress", "source_ip": "**********/136", "destination_ip": "***************/224", "destination_zone": "core", "service": "https,ssh"}, {"rule_name": "Egress_CORE_CAS", "action": "permit", "source_zone": "egress", "source_ip": "XWH_T4_NAT_**********", "destination_ip": "**********/32.", "destination_zone": "core", "service": "TCP_28080"}, {"rule_name": "CSLC_to_newT3_DB", "action": "permit", "source_zone": "core", "source_ip": "**************.", "destination_ip": "XWH_T4NAT_18.6.3.0/24", "destination_zone": "egress", "service": "TCP_31306"}, {"rule_name": "CSLC_to_NEWT3_DB", "action": "permit", "source_zone": "core", "source_ip": "**************.", "destination_ip": "XWHT3_t<PERSON><PERSON><PERSON><PERSON>_18.6.3.0/24", "destination_zone": "egress", "service": "TCP_31306"}, {"rule_name": "G3_tiaoshi", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "G3_4.191.0.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "JH_to_T3_SFTP", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "T3_18.6.3.67", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_to_XWH18.5.252/74", "action": "permit", "source_zone": "core", "source_ip": "**********_84", "destination_ip": "***********//28,*********//27,*********.", "destination_zone": "egress", "service": "http,tcp_29092"}, {"rule_name": "G3_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "T1\"", "destination_ip": "********/16.", "destination_zone": "core", "service": "ftp"}, {"rule_name": "T3GW_to_CCB", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "any", "destination_zone": "core", "service": "TCP_8182,TCP_8184,TCP_8181"}, {"rule_name": "TO_*********", "action": "permit", "source_zone": "egress", "source_ip": "***********.", "destination_ip": "*********.", "destination_zone": "core", "service": "http"}, {"rule_name": "OPS_to_T4MS", "action": "permit", "source_zone": "core", "source_ip": "ops_********11,OPS_********12,OPS_********13,**********.", "destination_ip": "T4MS_18.5.99.11", "destination_zone": "egress", "service": "any"}, {"rule_name": "G3_MS_4.190.120.0/22 To T1", "action": "permit", "source_zone": "egress", "source_ip": "G3_MS_4.190.120.0/22", "destination_ip": "********73_", "destination_zone": "core", "service": "http"}, {"rule_name": "G3AMS_to_T1", "action": "permit", "source_zone": "egress", "source_ip": "GWAMSF5_18.5.98.15", "destination_ip": "********/16.", "destination_zone": "core", "service": "TCP_30426"}, {"rule_name": "G3_to_***********", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "***********.", "destination_zone": "core", "service": "TCP8443"}, {"rule_name": "New_T3_to_qc", "action": "permit", "source_zone": "egress", "source_ip": "*************.", "destination_ip": "qc_*************", "destination_zone": "core", "service": "icmp,TCP8080"}, {"rule_name": "New_T3_to_email1", "action": "permit", "source_zone": "egress", "source_ip": "**********/.32", "destination_ip": "192.168.66.150_32", "destination_zone": "core", "service": "icmp,smtp"}, {"rule_name": "T1_*********_to_G3", "action": "permit", "source_zone": "core", "source_ip": "*********/24.,********/8.,********./16,***********.,***********.,***********./32", "destination_ip": "XWHG3_*********/24,XWH_G3_********/24", "destination_zone": "egress", "service": "any"}, {"rule_name": "G3_to_T1_*********", "action": "permit", "source_zone": "egress", "source_ip": "XWHG3_*********/24,XWH_G3_********/24", "destination_ip": "*********/24.", "destination_zone": "core", "service": "ssh,TCP_8000_8010,ntp"}, {"rule_name": "T3_to_CLSC", "action": "permit", "source_zone": "egress", "source_ip": "**********.,**********/32.", "destination_ip": "***********./24,************/32.", "destination_zone": "core", "service": "https,TCP_389,TCP_9090,TCP9080,TCP_6006"}, {"rule_name": "G3AMS_to_T1_18.0.2.36", "action": "permit", "source_zone": "egress", "source_ip": "********.", "destination_ip": "********.,*********.", "destination_zone": "core", "service": "TCP_3555"}, {"rule_name": "NEWT3_to_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "T3_NAT_18.6.3.0", "destination_ip": "**********./24", "destination_zone": "core", "service": "any"}, {"rule_name": "G3_***********_to_T1_NTP", "action": "permit", "source_zone": "egress", "source_ip": "***********./24,**********_16", "destination_ip": "********73_,***********./24", "destination_zone": "core", "service": "ntp"}, {"rule_name": "T1_***********_to_T4", "action": "permit", "source_zone": "core", "source_ip": "***********./32", "destination_ip": "**********./32", "destination_zone": "egress", "service": "TCP_8080"}, {"rule_name": "T3_to_CSLC_SIT", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "*************&************", "destination_zone": "core", "service": "TCP_8080,TCP_8090,icmp"}, {"rule_name": "G3_MS To T1_**********", "action": "permit", "source_zone": "egress", "source_ip": "G3_MS_4.190.121.231/32", "destination_ip": "**********.", "destination_zone": "core", "service": "TCP_3000"}, {"rule_name": "T1_to_XWH_PD_G3_01", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "any", "destination_zone": "egress", "service": "tcp_22"}, {"rule_name": "XWH_PD_G3_to_T1", "action": "permit", "source_zone": "egress", "source_ip": "G3_MS_4.190.120.0/22,10.219.5.103_105", "destination_ip": "*********1.", "destination_zone": "core", "service": "http"}, {"rule_name": "TO_**********/24", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,T3_NAT_18.6.3.0", "destination_ip": "**********/24.", "destination_zone": "core", "service": "TCP_6008,https,TCP_4433,TCP_6006,icmp,TCP_34433"}, {"rule_name": "to_***********/98", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "***********/98", "destination_zone": "core", "service": "TCP8080,TCP_20150,icmp"}, {"rule_name": "XWH_PD_to_T1_DC", "action": "permit", "source_zone": "egress", "source_ip": "XWH_G3_*******/8", "destination_ip": "********.", "destination_zone": "core", "service": "TCP_389"}, {"rule_name": "T4_to_QC", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "TCP8080,icmp"}, {"rule_name": "TO_CSLC_************", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "************/32.", "destination_zone": "core", "service": "http,icmp"}, {"rule_name": "XWH_磁带机_to_T1", "action": "permit", "source_zone": "egress", "source_ip": "XWH_磁带机_18.5.85.1,XWH_磁带机_18.5.85.2", "destination_ip": "********/16.,********./16", "destination_zone": "core", "service": "TCP_6370,dns,TCP_5000,TCP_4100_4200,TCP_5003"}, {"rule_name": "T4_to_email", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,**********/32.,***********/32.,***********/32.,***********/32.", "destination_ip": "*************/**************", "destination_zone": "core", "service": "smtp"}, {"rule_name": "to_***********", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "***********/32.", "destination_zone": "core", "service": "icmp,TCP_34443"}, {"rule_name": "TO_***********", "action": "permit", "source_zone": "egress", "source_ip": "*************.,**********.", "destination_ip": "***********/32.", "destination_zone": "core", "service": "TCP_8080,icmp,ssh"}, {"rule_name": "TO_18.6.4.38_44", "action": "permit", "source_zone": "core", "source_ip": "**************/22/**************,**************/32.,**************/32.", "destination_ip": "18.6.4.38_44,********/32.,*********/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "500WAN_to_XWH_T3", "action": "permit", "source_zone": "egress", "source_ip": "500WAN_106.37.229.46,500WAN_183.3.220.35", "destination_ip": "T3_NAT_18.6.3.0", "destination_zone": "core", "service": "https,TCP8443"}, {"rule_name": "TO_**************", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "**************.", "destination_zone": "core", "service": "TCP_8087"}, {"rule_name": "WSUS_to_Internet", "action": "permit", "source_zone": "core", "source_ip": "********88/32.", "destination_ip": "WSUS_Internet", "destination_zone": "egress", "service": "https,http"}, {"rule_name": "FangBingDu_to_Internet", "action": "permit", "source_zone": "core", "source_ip": "********39/32.", "destination_ip": "FangBingDu_Internet", "destination_zone": "egress", "service": "http,https"}, {"rule_name": "core_to_egress_0", "action": "permit", "source_zone": "core", "source_ip": "any", "destination_ip": "any", "destination_zone": "egress", "service": "icmp,https,ftp,http,ntp,nntp,ssh,tcp_3080,TCP_3389,tcp31306,TCP_8000,dns,dns-tcp"}, {"rule_name": "NEW_to_Cslc", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,**********.", "destination_ip": "***********/32.", "destination_zone": "core", "service": "TCP_21"}, {"rule_name": "T4_to_***********", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP_20050"}, {"rule_name": "XWH_to_T3_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "T3_NAT_18.6.3.0", "destination_ip": "***********/32.,***********/32.,**********/32.", "destination_zone": "core", "service": "TCP_20150,http_8082,TCP_50094,TCP_50095,TCP_8087"}, {"rule_name": "T2_to_CSLC", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/8.,T3_38.2.1.0/24,**********.,********/24.,***********/32.", "destination_ip": "***********/32.,***********/32.", "destination_zone": "egress", "service": "TCP_8080,TCP_20050,icmp"}, {"rule_name": "CSLC_TO_T2", "action": "permit", "source_zone": "egress", "source_ip": "**********/24.", "destination_ip": "**********/32.,********/32.,********/32.,T3_38.2.1.0/24,*********.,********/8.", "destination_zone": "core", "service": "tcp_7003,http_8082,icmp,ssh"}, {"rule_name": "支付1.9.6", "action": "permit", "source_zone": "core", "source_ip": "T2_T3_38.0.160.0", "destination_ip": "*********.", "destination_zone": "egress", "service": "ssh"}, {"rule_name": "T3_to_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "**********.,**********.", "destination_ip": "CSLC_172.20.22.0/24,CSLC_172.20.29.0/24,CSLC_172.20.23.0/24", "destination_zone": "core", "service": "TCP_9090,https"}, {"rule_name": "CSLC_to_T1", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.,***********/32.", "destination_ip": "**********/32.,**********/32.,**********/32.,**********/32.,*********.", "destination_zone": "core", "service": "tcp_7003,http_8082,icmp"}, {"rule_name": "T3_Client_to_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "*************.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "TCP_8080,icmp"}, {"rule_name": "********_to_*********", "action": "permit", "source_zone": "core", "source_ip": "T1_********/32", "destination_ip": "T4_*********", "destination_zone": "egress", "service": "any"}, {"rule_name": "*********_to_********", "action": "permit", "source_zone": "egress", "source_ip": "T4_*********", "destination_ip": "T1_********/32", "destination_zone": "core", "service": "TCP_8000_8010,ntp,TCP_10050,TCP_9091"}, {"rule_name": "T4_AMS_to_T1_BMSDB", "action": "permit", "source_zone": "egress", "source_ip": "G3AMS_18.5.84.0./24", "destination_ip": "********.,*********.", "destination_zone": "egress", "service": "TCP_3555"}, {"rule_name": "第三方测试", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "CSLC_104.23.0.10,CSLC_104.23.0.11", "destination_zone": "egress", "service": "TCP_28080,tcp_8088"}, {"rule_name": "V3_to_OCS", "action": "permit", "source_zone": "egress", "source_ip": "*******./8", "destination_ip": "18.0.2.171.", "destination_zone": "core", "service": "ftp"}, {"rule_name": "V3_hanyi", "action": "permit", "source_zone": "egress", "source_ip": "V3_CORE_4.190.80.0/21", "destination_ip": "********.", "destination_zone": "core", "service": "TCP_3555,TCP_3558"}, {"rule_name": "V3_to_RMXDB_Linshj", "action": "permit", "source_zone": "egress", "source_ip": "*******./8", "destination_ip": "********/16.", "destination_zone": "core", "service": "any"}, {"rule_name": "T1_RMXDB_to_V3", "action": "permit", "source_zone": "core", "source_ip": "********/16.,********/24.", "destination_ip": "*******./8", "destination_zone": "egress", "service": "any"}, {"rule_name": "TEST", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "any", "destination_zone": "core", "service": "TCP3556,TCP_3555,TCP_3557,TCP_3558,ssh"}, {"rule_name": "ssh_snmp", "action": "permit", "source_zone": "core", "source_ip": "***********/32_", "destination_ip": "*******./8", "destination_zone": "egress", "service": "ssh,snmp"}, {"rule_name": "New_T3_Client_to_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "*************.", "destination_ip": "************.", "destination_zone": "core", "service": "TCP_8089,icmp"}, {"rule_name": "*********_********", "action": "permit", "source_zone": "egress", "source_ip": "*********/21.", "destination_ip": "********./24", "destination_zone": "core", "service": "TCP_7001"}, {"rule_name": "BG_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "BG_192.168.249.0/24", "destination_ip": "********.", "destination_zone": "core", "service": "TCP_8001"}, {"rule_name": "yunguanpingtai_bangong", "action": "permit", "source_zone": "egress", "source_ip": "************/32.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "https,ssh,http,TCP_389,icmp"}, {"rule_name": "T1_18.1.21.11_*********", "action": "permit", "source_zone": "core", "source_ip": "*********/24_", "destination_ip": "*********.", "destination_zone": "egress", "service": "tcp_8088"}, {"rule_name": "TEST1111", "action": "permit", "source_zone": "egress", "source_ip": "*************/24.,**********/32.,**********/32.,***********/32_SDN", "destination_ip": "********/24.", "destination_zone": "core", "service": "any"}, {"rule_name": "*************_Test", "action": "permit", "source_zone": "egress", "source_ip": "*************/24.", "destination_ip": "*********/24.,Corenat********/8", "destination_zone": "core", "service": "TCP_3389,TCP8022"}, {"rule_name": "**********_84_to_***********", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "***********./32", "destination_zone": "egress", "service": "TCP_36524"}, {"rule_name": "luyu_01", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "G3_ILO_4.191.249.0/24", "destination_zone": "egress", "service": "tcp_8088"}, {"rule_name": "luyu_02", "action": "permit", "source_zone": "egress", "source_ip": "G3_ILO_4.191.249.0/24", "destination_ip": "********/16.", "destination_zone": "core", "service": "tcp_8088"}, {"rule_name": "TCP_COPY", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "**********./32", "destination_zone": "core", "service": "TCP_8080"}, {"rule_name": "XWH_T4_to_kaifa", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.", "destination_ip": "**************/26.", "destination_zone": "core", "service": "TCP_9092"}, {"rule_name": "XWH_T3_to_cslc", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,**********.,**********.", "destination_ip": "***********./24,**********/24.", "destination_zone": "core", "service": "tcp_9080,TCP_6006,TCP_14433,TCP_24433,TCP_34433"}, {"rule_name": "cslc_to_XWH_T3", "action": "permit", "source_zone": "core", "source_ip": "***********./24", "destination_ip": "*********/32.", "destination_zone": "egress", "service": "tcp_8080"}, {"rule_name": "BigG3_desktop_to_cslc", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "**********/24.", "destination_zone": "core", "service": "https,icmp"}, {"rule_name": "T1_TO_XWH", "action": "permit", "source_zone": "core", "source_ip": "********_8", "destination_ip": "*********./32", "destination_zone": "egress", "service": "tcp_8080"}, {"rule_name": "YJT2_CSLC", "action": "permit", "source_zone": "core", "source_ip": "********/8.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP_20050"}, {"rule_name": "CSLC_YJT2", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "********/8.", "destination_zone": "core", "service": "http_8082,tcp_22"}, {"rule_name": "T19_CSLC", "action": "permit", "source_zone": "egress", "source_ip": "T19_18.6.19.0", "destination_ip": "***********/32.,***********.", "destination_zone": "core", "service": "TCP_21,TCP8443"}, {"rule_name": "T1CORE_TO_CSLC", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "104.23.0.78_99", "destination_zone": "egress", "service": "ssh"}, {"rule_name": "T1_to_bangong", "action": "permit", "source_zone": "core", "source_ip": "**********/16.", "destination_ip": "***************/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "keji_to_T1jiamiji", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.,CSLC_**********/24", "destination_ip": "T1_JMJ_18.2.1.244,Dsvs_18.2.1.245", "destination_zone": "core", "service": "TCP_8000,TCP_8008"}, {"rule_name": "***********_to_********", "action": "permit", "source_zone": "egress", "source_ip": "***********./24", "destination_ip": "********/8.", "destination_zone": "core", "service": "any"}, {"rule_name": "XWH_new_t3_to_jianhang", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "*************/32.", "destination_zone": "core", "service": "tcp_8101_8103_8104,icmp"}, {"rule_name": "T4_jiaoyi<PERSON>you", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "**********/32.,**********/32.,**********/32.,**********/32.", "destination_zone": "core", "service": "ssh"}, {"rule_name": "T4_***********", "action": "permit", "source_zone": "egress", "source_ip": "*********./32", "destination_ip": "***********./32", "destination_zone": "core", "service": "TCP_8085,TCP_8087,http_8082"}, {"rule_name": "T3_TO_UMP", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "***********./24", "destination_zone": "core", "service": "TCP_18080"}, {"rule_name": "T1_To_***********52 9100", "action": "permit", "source_zone": "core", "source_ip": "***********/32.", "destination_ip": "***********52/32.", "destination_zone": "egress", "service": "TCP9100"}, {"rule_name": "XHW_18.5.82.22_To_T1", "action": "permit", "source_zone": "egress", "source_ip": "XWHT4_18.5.82.22", "destination_ip": "***********/32.", "destination_zone": "core", "service": "TCP_9090"}, {"rule_name": "TO_104.23.11.4", "action": "permit", "source_zone": "core", "source_ip": "T2_T3_38.0.160.0,**********/32.,**********/32.,**********/32.,**********/32.,**********/32.,**********/32.,**********/32.,**********/32.,**********/32.,**********/32.", "destination_ip": "***********/24.", "destination_zone": "egress", "service": "TCP_7001"}, {"rule_name": "XWH_T3_to_keji_t<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "egress", "source_ip": "**********.", "destination_ip": "***********/24.", "destination_zone": "core", "service": "TCP_3389,icmp"}, {"rule_name": "T3_*********", "action": "permit", "source_zone": "egress", "source_ip": "*********/24.", "destination_ip": "*********/32.", "destination_zone": "core", "service": "TCP_34443"}, {"rule_name": "T5_T3ELPSFTP", "action": "permit", "source_zone": "core", "source_ip": "********/8.", "destination_ip": "T3_NAT_18.6.3.0,***********/32.", "destination_zone": "egress", "service": "TCP_34443,http,tcp_9080"}, {"rule_name": "TO_10080", "action": "permit", "source_zone": "core", "source_ip": "CSLC_104.21.2.0/24", "destination_ip": "T3_NAT_18.6.3.0", "destination_zone": "egress", "service": "TCP10080"}, {"rule_name": "T3_***********_216", "action": "permit", "source_zone": "egress", "source_ip": "*********.", "destination_ip": "**********/32.", "destination_zone": "core", "service": "TCP8022"}, {"rule_name": "TO_*********", "action": "permit", "source_zone": "egress", "source_ip": "T3_NAT_18.6.3.0", "destination_ip": "*********./32,********/16.,*********/24.", "destination_zone": "core", "service": "TCP_7004,TCP_7005,tcp_7003"}, {"rule_name": "TO_T3_USAP", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "**********./32", "destination_zone": "egress", "service": "http"}, {"rule_name": "youzhen", "action": "permit", "source_zone": "core", "source_ip": "***********/32.,********/16.", "destination_ip": "***********/24.", "destination_zone": "egress", "service": "TCP_8428,https"}, {"rule_name": "XWH_T3_T1", "action": "permit", "source_zone": "egress", "source_ip": "*********./32,XWT_T4_*********/32", "destination_ip": "T1_********/32", "destination_zone": "core", "service": "TCP/UDP111,TCP/UDP_2049,TCP/UDP_32768_65535"}, {"rule_name": "bangongvpn_to_sslGateway", "action": "permit", "source_zone": "core", "source_ip": "************/24.", "destination_ip": "**********/32.", "destination_zone": "egress", "service": "https,icmp"}, {"rule_name": "cslc_to_T3", "action": "permit", "source_zone": "core", "source_ip": "**************.", "destination_ip": "*********/32.,18.6.23.5/32.,18.6.23.6/32.,18.6.23.7/32.", "destination_zone": "egress", "service": "TCP_31306"}, {"rule_name": "TEST_MATSERVER", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "*********./24", "destination_zone": "core", "service": "any"}, {"rule_name": "SSL_TO_USAP", "action": "permit", "source_zone": "core", "source_ip": "18.4.65.0./24", "destination_ip": "T3_NAT_18.6.3.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "testxxxxxxx", "action": "permit", "source_zone": "fez", "source_ip": "any", "destination_ip": "any", "destination_zone": "core", "service": "TCP_69"}, {"rule_name": "T1_XWHT4", "action": "permit", "source_zone": "core", "source_ip": "********_8", "destination_ip": "*********//27,T4_18.5.97.0", "destination_zone": "egress", "service": "http,http_8082"}, {"rule_name": "to_G3", "action": "permit", "source_zone": "core", "source_ip": "(*********/24", "destination_ip": "*********/32.,**********.", "destination_zone": "egress", "service": "TCP_28080,TCP28082"}, {"rule_name": "********83_NTP", "action": "permit", "source_zone": "core", "source_ip": "NTP_********83", "destination_ip": "any", "destination_zone": "egress", "service": "ntp"}, {"rule_name": "NTP_********83", "action": "permit", "source_zone": "egress", "source_ip": "CSLC_**********/24", "destination_ip": "NTP_********83", "destination_zone": "core", "service": "ntp"}, {"rule_name": "t1matserver_to_***********", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "icmp,TCP8080"}, {"rule_name": "T1ShouPiaoGongju", "action": "permit", "source_zone": "core", "source_ip": "kaifaceshitools_********0", "destination_ip": "T4_18.5.97.0", "destination_zone": "egress", "service": "http"}, {"rule_name": "RTQ_UMP", "action": "permit", "source_zone": "egress", "source_ip": "RTQ_18.5.84.81_82", "destination_ip": "UMP_18.0.117.3", "destination_zone": "core", "service": "TCP_34443"}, {"rule_name": "BASDB_ENCY", "action": "permit", "source_zone": "core", "source_ip": "BASDB_********5", "destination_ip": "*********/24_", "destination_zone": "egress", "service": "TCP_8018"}, {"rule_name": "T1_to_G3", "action": "permit", "source_zone": "core", "source_ip": "**********_239,**********/32.,**********/32.,**********/32.,**********/32.", "destination_ip": "**********.,**********.,**********.", "destination_zone": "egress", "service": "TCP_3555"}, {"rule_name": "NTP_**********", "action": "permit", "source_zone": "egress", "source_ip": "18.5.85.11_12", "destination_ip": "NTP_**********", "destination_zone": "core", "service": "ntp"}, {"rule_name": "T5_to_inter", "action": "permit", "source_zone": "egress", "source_ip": "T2_T3_38.0.160.0", "destination_ip": "any", "destination_zone": "core", "service": "http"}, {"rule_name": "T1_104.23.1.0", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "104.23.1.17_25,CSLC_**********/24", "destination_zone": "egress", "service": "TCP_3389,icmp"}, {"rule_name": "T5MATserver", "action": "permit", "source_zone": "core", "source_ip": "************.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "to_104", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/8.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP_21"}, {"rule_name": "insie", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "************.", "destination_zone": "core", "service": "tcp_7003,icmp"}, {"rule_name": "ftp", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "T1_18.2.1.34/32,*********/32.", "destination_zone": "core", "service": "tcp_22"}, {"rule_name": "TO_T1Apollo", "action": "permit", "source_zone": "egress", "source_ip": "CSLC_**********/24", "destination_ip": "*********/24.", "destination_zone": "core", "service": "TCP_28070,TCP_28081"}, {"rule_name": "***********/216", "action": "permit", "source_zone": "egress", "source_ip": "*********.", "destination_ip": "*********./24", "destination_zone": "core", "service": "tcp_5000_5008,tcp6370,tcp28090"}, {"rule_name": "*************_to_31306", "action": "permit", "source_zone": "egress", "source_ip": "*************/24.", "destination_ip": "********/16.,********/16.", "destination_zone": "core", "service": "any"}, {"rule_name": "T5_L05", "action": "permit", "source_zone": "core", "source_ip": "*********.,*********.,***********/32.", "destination_ip": "************.", "destination_zone": "egress", "service": "telnet,icmp,http"}, {"rule_name": "T5_L05ump", "action": "permit", "source_zone": "core", "source_ip": "T3_38.2.1.0/24,**********.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP8080,icmp,telnet"}, {"rule_name": "L05_T5", "action": "permit", "source_zone": "egress", "source_ip": "************.", "destination_ip": "*********.,*********.,***********/32.", "destination_zone": "core", "service": "telnet,http_8082,icmp,tcp_22"}, {"rule_name": "L05ump_T5", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "**********.", "destination_zone": "core", "service": "tcp_7003,icmp,telnet"}, {"rule_name": "v316_T4", "action": "permit", "source_zone": "egress", "source_ip": "**************/32.,**************/32.", "destination_ip": "**********/32.,*********/32.,**********/32.,**********/32.", "destination_zone": "core", "service": "TCP_3389"}, {"rule_name": "to_18.5.88.121_125", "action": "permit", "source_zone": "core", "source_ip": "18.0.95.223_228", "destination_ip": "18.5.88.121_125", "destination_zone": "egress", "service": "TCP_3191,tcp_4191"}, {"rule_name": "T4AD_T1AD", "action": "permit", "source_zone": "core", "source_ip": "********.,********./32", "destination_ip": "*********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "FTP", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "************./32", "destination_zone": "core", "service": "any"}, {"rule_name": "OCP_to_OB", "action": "permit", "source_zone": "egress", "source_ip": "************.", "destination_ip": "***********.,***********.,***********.", "destination_zone": "core", "service": "tcp_22,tcp2881totcp2888"}, {"rule_name": "T1_bangong10.248.255", "action": "permit", "source_zone": "egress", "source_ip": "********/24.", "destination_ip": "**************.", "destination_zone": "core", "service": "any"}, {"rule_name": "TO_104.23.99.33", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/16.", "destination_ip": "***********../24", "destination_zone": "egress", "service": "dns,TCP_2883"}, {"rule_name": "to_kafka", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "***********.", "destination_zone": "egress", "service": "TCP_9092"}, {"rule_name": "qudaoDB", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "************.", "destination_zone": "egress", "service": "TCP_2883"}, {"rule_name": "tencent", "action": "permit", "source_zone": "egress", "source_ip": "************/24.", "destination_ip": "********.", "destination_zone": "core", "service": "TCP8001_8020"}, {"rule_name": "T1_<PERSON>_<PERSON>", "action": "permit", "source_zone": "core", "source_ip": "********_8,********/32.,*********/32.", "destination_ip": "************/24.", "destination_zone": "egress", "service": "TCP_31582,tcp_30000"}, {"rule_name": "TO_CSLC", "action": "permit", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "*************./32,**************./32,***********./32", "destination_zone": "egress", "service": "TCP_389,TCP_6006,TCP_3001"}, {"rule_name": "T1_104.21_10.216", "action": "permit", "source_zone": "core", "source_ip": "18.0.71.0_18.0.75.0", "destination_ip": "************/24.,**********/24.", "destination_zone": "egress", "service": "TCP_389,TCP_30001"}, {"rule_name": "lousao", "action": "permit", "source_zone": "core", "source_ip": "***************/32.,**************/32.", "destination_ip": "*************/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "赔率服务器", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "**********/32.,T2N1_38.1.21.11,T5_***********0", "destination_zone": "core", "service": "tcp_52701,TCP_8080"}, {"rule_name": "SDN_T1", "action": "permit", "source_zone": "egress", "source_ip": "SDN_4.189.0.0/16", "destination_ip": "T1_NAT_78.0.0.0/16", "destination_zone": "core", "service": "any"}, {"rule_name": "************.", "action": "permit", "source_zone": "core", "source_ip": "************.,************.,************.", "destination_ip": "**********.,**********/24.,**********/24.,*************/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_104.21.19.", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/24.", "destination_ip": "***********/24.", "destination_zone": "egress", "service": "TCP_20050,TCP_8080"}, {"rule_name": "**********.", "action": "permit", "source_zone": "egress", "source_ip": "**********/32.,**********/32.", "destination_ip": "***********/32.,BG_192.168.32.121", "destination_zone": "core", "service": "https,TCP_10280"}, {"rule_name": "testuSAP", "action": "permit", "source_zone": "egress", "source_ip": "any", "destination_ip": "**********./32,**********./32", "destination_zone": "core", "service": "any"}, {"rule_name": "T4_to_104.21/104.200", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/24.", "destination_ip": "*************/24.,104.21.18.0/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "NPM管理", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "172.16.0.231./32", "destination_zone": "egress", "service": "tcp_8088"}, {"rule_name": "140/141_to_coding_linshi", "action": "permit", "source_zone": "core", "source_ip": "192.168.140/141.0", "destination_ip": "10.217.2.0/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "coding_to_140/141_linshi", "action": "permit", "source_zone": "egress", "source_ip": "10.217.2.0/24.", "destination_ip": "192.168.140/141.0", "destination_zone": "core", "service": "any"}, {"rule_name": "192.168.116_104", "action": "permit", "source_zone": "core", "source_ip": "192.168.116.0/24.", "destination_ip": "***********/24.", "destination_zone": "egress", "service": "TCP_7001"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "172.21.11.159/32.", "destination_ip": "18.6.23.31/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "10.219.10_to_keji", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.", "destination_ip": "172.16.30.51_56", "destination_zone": "core", "service": "any"}, {"rule_name": "keji_10.219.10", "action": "permit", "source_zone": "core", "source_ip": "172.16.30.51_56,************.", "destination_ip": "10.219.10.1_8", "destination_zone": "egress", "service": "any"}, {"rule_name": "********_9_to_", "action": "permit", "source_zone": "core", "source_ip": "********_8,********/32.", "destination_ip": "*********/32.", "destination_zone": "egress", "service": "tcp_8088"}, {"rule_name": "T518.0.160.", "action": "permit", "source_zone": "core", "source_ip": "T2_T3_38.0.160.0", "destination_ip": "**********/24.,**********.", "destination_zone": "egress", "service": "TCP_25,TCP_30200"}, {"rule_name": "CIDEV OPS", "action": "permit", "source_zone": "core", "source_ip": "********/32.,*********/32.", "destination_ip": "XWH_T4_18.5.88.161/32", "destination_zone": "egress", "service": "TCP_32600"}, {"rule_name": "luyu_t<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "egress", "source_ip": "************/24.", "destination_ip": "***********/32.", "destination_zone": "core", "service": "any"}, {"rule_name": "yunnodo_toBOS", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.", "destination_ip": "**********.", "destination_zone": "core", "service": "http"}, {"rule_name": "T5_to_104caiwuSFTP", "action": "permit", "source_zone": "core", "source_ip": "T2_T3_38.0.160.0", "destination_ip": "***********/24.,***********/24.,***********/24.", "destination_zone": "egress", "service": "TCP_34443,ssh"}, {"rule_name": "XL09(ssl)_to_transrouter", "action": "permit", "source_zone": "egress", "source_ip": "*************/24.", "destination_ip": "***********/32.", "destination_zone": "core", "service": "http_8082"}, {"rule_name": "Autotest", "action": "permit", "source_zone": "core", "source_ip": "********_10", "destination_ip": "************/32.,tencent_10.215.0.10,CSLC_104.255.255.1,**********.", "destination_zone": "egress", "service": "http,https,TCP_1443"}, {"rule_name": "VDI_QC", "action": "permit", "source_zone": "egress", "source_ip": "************/32.,赵明薇VDI", "destination_ip": "qc_*************", "destination_zone": "core", "service": "TCP_1433"}, {"rule_name": "T2_XXFBPT", "action": "permit", "source_zone": "core", "source_ip": "**********/24.", "destination_ip": "信息发布中心", "destination_zone": "egress", "service": "TCP_30200"}, {"rule_name": "T1_BT", "action": "permit", "source_zone": "core", "source_ip": "BT_********1,BT_********15", "destination_ip": "*************/24.", "destination_zone": "egress", "service": "tcp_30000"}, {"rule_name": "T4T5_USAP", "action": "permit", "source_zone": "core", "source_ip": "T3_38.2.1.0/24,********/24.,********/24.,T2_T3_38.0.160.0,**********.,18.0.92.10_13/18.0.92.20_30,**********/32.,T2T4T5node,18.0.97.21_28,***********/32.,58.0.7.11_18,********/8.", "destination_ip": "USAP:***********", "destination_zone": "egress", "service": "TCP9080,TCP_19080,http,https,icmp"}, {"rule_name": "T1_xietongVDI", "action": "permit", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "yunwei_zabbix", "action": "permit", "source_zone": "core", "source_ip": "***********/32.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP31051"}, {"rule_name": "yunwei_zabbix1", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "***********/32.", "destination_zone": "core", "service": "TCP31050_31051"}, {"rule_name": "SDN_toT1", "action": "permit", "source_zone": "egress", "source_ip": "4.189.0.1_10", "destination_ip": "********/24.", "destination_zone": "core", "service": "http,ssh,https,TCP_3389,TCP_8000_8010"}, {"rule_name": "anquansao<PERSON><PERSON>", "action": "permit", "source_zone": "egress", "source_ip": "************/32.", "destination_ip": "any", "destination_zone": "core", "service": "any"}, {"rule_name": "T1_***********", "action": "permit", "source_zone": "core", "source_ip": "***********/32.", "destination_ip": "************/32.,*************/32.,USAP:***********,***************/32.,************.,**********/24.,************/32.", "destination_zone": "egress", "service": "TCP_30010,https,TCP_2883,tcp_30000,TCP9080,tcp_1521,TCP_19080"}, {"rule_name": "yukong_T2VDI", "action": "permit", "source_zone": "egress", "source_ip": "*********.", "destination_ip": "**************.,**************.", "destination_zone": "core", "service": "any"}, {"rule_name": "T2VDI_to_yukong", "action": "permit", "source_zone": "core", "source_ip": "**************.,**************.", "destination_ip": "*********.,**********.,USAP:***********", "destination_zone": "egress", "service": "any"}, {"rule_name": "anquanshengtou_6/30", "action": "permit", "source_zone": "egress", "source_ip": "***********/32.", "destination_ip": "**********/32.,**********/32.", "destination_zone": "core", "service": "TCP_8866"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "**********.", "destination_zone": "egress", "service": "TCP_1443"}, {"rule_name": "sjj", "action": "permit", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "*************/32.", "destination_zone": "egress", "service": "TCP_10014"}, {"rule_name": "BT_coding", "action": "permit", "source_zone": "core", "source_ip": "********/24.,********/24.,********/24.,T3_38.2.1.0/24", "destination_ip": "**********/32_coding", "destination_zone": "egress", "service": "http"}, {"rule_name": "guoce_T5", "action": "permit", "source_zone": "egress", "source_ip": "*************/32.", "destination_ip": "*********/32.", "destination_zone": "core", "service": "TCP_8899"}, {"rule_name": "AGENT_to_BOSROUTER", "action": "permit", "source_zone": "core", "source_ip": "********_10", "destination_ip": "18.5.70.61_62", "destination_zone": "egress", "service": "http_8082"}, {"rule_name": "xia<PERSON><PERSON>", "action": "permit", "source_zone": "core", "source_ip": "***********/32.,***********.", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "TCP_30002"}, {"rule_name": "全业务链环境", "action": "permit", "source_zone": "core", "source_ip": "*********/32.", "destination_ip": "**************/32.", "destination_zone": "egress", "service": "tcp_30000"}, {"rule_name": "YZ_VDI1", "action": "permit", "source_zone": "egress", "source_ip": "************/24.", "destination_ip": "**********/24.,T2_T3_38.0.160.0,**********.", "destination_zone": "core", "service": "ssh"}, {"rule_name": "YZVD_TO_38", "action": "permit", "source_zone": "egress", "source_ip": "************/24.", "destination_ip": "T2_T3_38.0.160.0,38_0_1_0/24", "destination_zone": "core", "service": "TCP_8086,TCP_28080"}, {"rule_name": "YZ_VDI2", "action": "permit", "source_zone": "egress", "source_ip": "************/24.", "destination_ip": "*********/32.,T3_38.0.1.10/32,*********/32.", "destination_zone": "core", "service": "TCP_28080"}, {"rule_name": "tenxunyun_to_t5", "action": "permit", "source_zone": "egress", "source_ip": "***********/24.", "destination_ip": "***********/32.", "destination_zone": "core", "service": "http_8082"}, {"rule_name": "harbor 中转机", "action": "permit", "source_zone": "egress", "source_ip": "************.", "destination_ip": "28.0.1.201_", "destination_zone": "core", "service": "https,http"}, {"rule_name": "安全魏宏安全月临时开放", "action": "permit", "source_zone": "egress", "source_ip": "10_211_4_149,10_211_6_57", "destination_ip": "18_0_4_75,18_4_0_75,18_6_32_38", "destination_zone": "core", "service": "any"}, {"rule_name": "174_T1_To_10_216", "action": "permit", "source_zone": "core", "source_ip": "18_0_95_20_121,28_0_0_0/8", "destination_ip": "10_216_5_4,104_22_9_42", "destination_zone": "egress", "service": "any"}, {"rule_name": "VC管理", "action": "permit", "source_zone": "core", "source_ip": "18_0_1_248,**********./32", "destination_ip": "CSLC_104.255.255.1", "destination_zone": "egress", "service": "any"}, {"rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "permit", "source_zone": "egress", "source_ip": "10.0.0.0/8.", "destination_ip": "********/16.", "destination_zone": "core", "service": "any"}, {"rule_name": "18_0to18.5.98", "action": "permit", "source_zone": "core", "source_ip": "********/8_", "destination_ip": "*********/24_", "destination_zone": "egress", "service": "any"}, {"rule_name": "10&104to38any", "action": "permit", "source_zone": "egress", "source_ip": "*********/8.,10.0.0.0/8.,********/8_", "destination_ip": "********/8.,********/8.,18_6_32_0/24,********/8_", "destination_zone": "core", "service": "any"}, {"rule_name": "安全服务产品线", "action": "permit", "source_zone": "core", "source_ip": "***************/32.", "destination_ip": "************/32.", "destination_zone": "egress", "service": "TCP_8080"}, {"rule_name": "PKICA", "action": "permit", "source_zone": "core", "source_ip": "18.6.32.1_,**********/32.", "destination_ip": "18.6.203.1_18、44", "destination_zone": "egress", "service": "any"}, {"rule_name": "VCMGMTTOOLS TO VC10.252.254.202", "action": "permit", "source_zone": "egress", "source_ip": "104.255.225.45_,104.21.2.64_1", "destination_ip": "VC18.252.254.202", "destination_zone": "core", "service": "any"}, {"rule_name": "20230926李洋需求", "action": "permit", "source_zone": "core", "source_ip": "18.0.177.21_30", "destination_ip": "**********.", "destination_zone": "egress", "service": "tcp:20443"}, {"rule_name": "王轩跳板机", "action": "NAT_18.6.32.15", "source_zone": "core", "source_ip": "**********.", "destination_ip": "***********.,**********/16.", "destination_zone": "egress", "service": "any"}, {"rule_name": "TO_10.216.71.48", "action": "NAT_18.6.32.1", "source_zone": "core", "source_ip": "**********.", "destination_ip": "10.216.71.48.", "destination_zone": "egress", "service": "TCP_8086"}, {"rule_name": "TO_************", "action": "*********", "source_zone": "core", "source_ip": "18.0.92.2.", "destination_ip": "************.", "destination_zone": "egress", "service": "TCP_8086"}, {"rule_name": "阿里云测试", "action": "NAT_18.6.32.8", "source_zone": "core", "source_ip": "阿里云测试**********,*********/24.,********/16.", "destination_ip": "阿里云测试地址***********,*********/16.,*********/16.", "destination_zone": "egress", "service": "any"}, {"rule_name": "封浩亮测试访问G32财务中心SFTP", "action": "NAT18.6.32.13", "source_zone": "core", "source_ip": "**********.", "destination_ip": "**********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "赵树军测试", "action": "NAT_18.6.32.6", "source_zone": "core", "source_ip": "********73_0518", "destination_ip": "172.16.0.1_2_0518", "destination_zone": "egress", "service": "any"}, {"rule_name": "安全扫描魏宏", "action": "NAT_18.6.32.11", "source_zone": "core", "source_ip": "**********.,**********.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "***********", "action": "NAT_18.6.32.10", "source_zone": "core", "source_ip": "any", "destination_ip": "10.213.3.60_", "destination_zone": "egress", "service": "any"}, {"rule_name": "**********", "action": "**************", "source_zone": "core", "source_ip": "**********_", "destination_ip": "3.0.0.0_", "destination_zone": "egress", "service": "any"}, {"rule_name": "***********", "action": "NAT_18.6.32.6", "source_zone": "core", "source_ip": "NTP_**********,**********.", "destination_ip": "***********.", "destination_zone": "egress", "service": "any"}, {"rule_name": "TO_*********", "action": "SANT_*********", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "*********./32", "destination_zone": "egress", "service": "any"}, {"rule_name": "To_*********", "action": "SANT_*********", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "*********./32", "destination_zone": "egress", "service": "any"}, {"rule_name": "TO_**********", "action": "18.2.22.1120", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "104.23.0.78_99", "destination_zone": "egress", "service": "any"}, {"rule_name": "TO_USAP_**********", "action": "SNAT*********", "source_zone": "core", "source_ip": "*********.", "destination_ip": "T3_NAT_18.6.3.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "TO_USAP_**********", "action": "SNAT_*********", "source_zone": "core", "source_ip": "*********./32", "destination_ip": "T3_NAT_18.6.3.0", "destination_zone": "egress", "service": "any"}, {"rule_name": "TO_18.6.4.48", "action": "SANT_*********", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "NFS_18.6.4.48/32", "destination_zone": "egress", "service": "any"}, {"rule_name": "wangxuan_to_tencent_cloud", "action": "NAT_18.6.32.14", "source_zone": "core", "source_ip": "**********./32,<PERSON><PERSON><PERSON>_18.0.200.253,************./32,************.,************./32,**********.,********_10,18.2.1.2_4", "destination_ip": "tencent_10.215.0.10,***********/24.,10.0.0.0/8.,*********/8.,**********/24.,**********/16.", "destination_zone": "egress", "service": "any"}, {"rule_name": "********_to_tencent_cloud", "action": "NAT_18.6.32.14", "source_zone": "core", "source_ip": "********/24.", "destination_ip": "***********/24.,tencent_10.215.0.10", "destination_zone": "egress", "service": "any"}, {"rule_name": "自动化平台", "action": "NAT_18.6.32.1", "source_zone": "core", "source_ip": "18.4.21.212_214,********_10,**********.,********50/32.", "destination_ip": "***********.,**********4.,***********.,10.213.3.31_33,10.213.3.41_42,************/32.,USAP_104.22.9.22,USAP:***********,*************/24.,************.,**********/24.,*************/32.,************/32.,**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "38.0.160TO10.212", "action": "NAT_18.6.32.1", "source_zone": "core", "source_ip": "T2_T3_38.0.160.0,T3_38.2.1.0/24,********/24.,T3_38.0.1.0/24,**********/32.,********/24.,********/8_,********/24_,********/8_", "destination_ip": "**********/24.,***********/24.,*********/8.,**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T5_RTQ_UMP", "action": "NAT_18.6.32.1", "source_zone": "core", "source_ip": "RTQDB_38.0.160.96", "destination_ip": "***********/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "180.11.237to104&10", "action": "NAT_18.6.32.14", "source_zone": "core", "source_ip": "***********/32.,18.0.99.99_100,18_0_95_0/24", "destination_ip": "10.0.0.0/8.,104_0_0_0_8", "destination_zone": "egress", "service": "any"}, {"rule_name": "**********", "action": "*********", "source_zone": "core", "source_ip": "**********.,BT_********1,BT_********15", "destination_ip": "*********/8.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T2_********/8", "action": "T2_NAT_18.6.32.3", "source_zone": "core", "source_ip": "***********/32.,***********/32.,********/24.,**********/32.,28_0_1_0/24", "destination_ip": "*********/8.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_********/24", "action": "NAT_18.6.32.1", "source_zone": "core", "source_ip": "********/24.,18.0.92.10_13/18.0.92.20_30,18.0.97.21_28,***********/32.,********/24.,********/24.", "destination_ip": "*********/8.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T4", "action": "T4_18.6.32.5", "source_zone": "core", "source_ip": "********/24.,***********.,**********.,**********/32.,********/8.", "destination_ip": "*********/8.,**********/24.,**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "l<PERSON><PERSON><PERSON>i", "action": "T5_NAT_18.6.32.4", "source_zone": "core", "source_ip": "**********.", "destination_ip": "**********/16.,**********/16.,**********/16.", "destination_zone": "egress", "service": "any"}, {"rule_name": "bangong", "action": "NAT_18.6.32.6", "source_zone": "core", "source_ip": "***************.", "destination_ip": "***********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "OPS_GuoCe", "action": "NAT_18.6.32.6", "source_zone": "core", "source_ip": "********/16.", "destination_ip": "Guo2_Monitor_10.213.8.158,**********/16.,172.16.0.1_2_0518", "destination_zone": "egress", "service": "any"}, {"rule_name": "T2_GC", "action": "T2_NAT_18.6.32.3", "source_zone": "core", "source_ip": "**************.,**************.", "destination_ip": "*********/8.", "destination_zone": "egress", "service": "any"}, {"rule_name": "T2_XXFBPT", "action": "T1_NAT_18.6.32.7", "source_zone": "core", "source_ip": "**********/24.", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "SDAS_USAP", "action": "NAT_18.6.32.8", "source_zone": "core", "source_ip": "********50/32.", "destination_ip": "F5_********50", "destination_zone": "egress", "service": "any"}, {"rule_name": "T1_xietong", "action": "NAT_18.6.32.6", "source_zone": "core", "source_ip": "*********/24.", "destination_ip": "**********/24.", "destination_zone": "egress", "service": "any"}, {"rule_name": "*************", "action": "NAT_18.6.32.8", "source_zone": "core", "source_ip": "*************/24.", "destination_ip": "USAP:***********", "destination_zone": "egress", "service": "any"}, {"rule_name": "T2_********/8_TO_10", "action": "T2_NAT_18.6.32.3", "source_zone": "core", "source_ip": "28_0_1_0/24", "destination_ip": "10.0.0.0/8.", "destination_zone": "egress", "service": "any"}, {"rule_name": "174_T1_T2_to_104_10", "action": "T2_NAT_18.6.32.3", "source_zone": "core", "source_ip": "T1_18.0.95.0/24,********/8.", "destination_ip": "**********/24.,USAP:***********", "destination_zone": "egress", "service": "any"}, {"rule_name": "egwto10.218", "action": "T1_NAT_18.6.32.7", "source_zone": "egw", "source_ip": "********/24.", "destination_ip": "10_218_0_0/16", "destination_zone": "egress", "service": "any"}, {"rule_name": "安全PKI终端**********", "action": "T1_NAT_18.6.32.7", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "*************/32.,*************/32.,*************/32.,*************/32.,*************/32.,*************/32.,*************/32.,**************/32.", "destination_zone": "egress", "service": "any"}, {"rule_name": "安全服务产品线", "action": "NAT_18.6.32.6", "source_zone": "core", "source_ip": "***************/32.", "destination_ip": "************/32.", "destination_zone": "egress", "service": "TCP_8080"}, {"rule_name": "18.0.160.59", "action": "NAT_18.6.32.9", "source_zone": "core", "source_ip": "18.0.160.59_", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "PKICA", "action": "NAT_18.6.32.1", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "18.6.203.1_18、44", "destination_zone": "egress", "service": "any"}, {"rule_name": "王轩T1环境出公网", "action": "NAT_18.6.32.14", "source_zone": "core", "source_ip": "**********.", "destination_ip": "any", "destination_zone": "egress", "service": "any"}, {"rule_name": "电子安全认证服务pki/ca系统测试环", "action": "NAT_18.6.32.14", "source_zone": "core", "source_ip": "**********/32.", "destination_ip": "10.218.203.1&2", "destination_zone": "egress", "service": "any"}, {"rule_name": "云下访问云上交易", "action": "NAT_18.6.32.14", "source_zone": "core", "source_ip": "18.0.92.11.", "destination_ip": "10.216.71.27.", "destination_zone": "egress", "service": "any"}]}
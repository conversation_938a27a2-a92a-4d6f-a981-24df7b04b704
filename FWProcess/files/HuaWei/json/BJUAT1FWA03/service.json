{"default": [{"name": "rsa_syslog", "type": "group", "service": [{"protocol": "rsa_syslog_tcp", "destination-port": "rsa_syslog_tcp"}, {"protocol": "rsa_syslog_udp", "destination-port": "rsa_syslog_udp"}]}, {"name": "udp_50086", "type": "object", "service": [{"protocol": "udp", "destination-port": "50086"}]}, {"name": "rpc+", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6222"}, {"protocol": "udp", "destination-port": "6222"}]}, {"name": "cisco_radius", "type": "object", "service": [{"protocol": "udp", "destination-port": "1645"}, {"protocol": "udp", "destination-port": "1646"}, {"protocol": "udp", "destination-port": "1812"}, {"protocol": "udp", "destination-port": "1813"}]}, {"name": "rsa_syslog_tcp", "type": "object", "service": [{"protocol": "tcp", "destination-port": "445"}, {"protocol": "tcp", "destination-port": "135"}, {"protocol": "tcp", "destination-port": "137"}, {"protocol": "tcp", "destination-port": "138"}, {"protocol": "tcp", "destination-port": "139"}]}, {"name": "rsa_syslog_udp", "type": "object", "service": [{"protocol": "udp", "destination-port": "445"}, {"protocol": "udp", "destination-port": "135"}, {"protocol": "udp", "destination-port": "137"}, {"protocol": "udp", "destination-port": "138"}, {"protocol": "udp", "destination-port": "139"}]}, {"name": "tcp_52704", "type": "object", "service": [{"protocol": "tcp", "destination-port": "52704"}]}, {"name": "ftp_8020&8021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8020"}, {"protocol": "tcp", "destination-port": "8021"}]}, {"name": "tcp_7777&20001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7777"}, {"protocol": "tcp", "destination-port": "20001"}]}, {"name": "tcp_30000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30000"}]}, {"name": "remote_desktop", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "tcp_20002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20002"}]}, {"name": "tcp_5056", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5056"}]}, {"name": "tcp_8020&8021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8020"}, {"protocol": "tcp", "destination-port": "8021"}]}, {"name": "ftp_8000_9000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9000"}]}, {"name": "tcp_1_65535", "type": "object", "service": [{"protocol": "tcp", "destination-port": "65535"}]}, {"name": "http_8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "tcp_1000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1000"}]}, {"name": "http_8082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8082"}]}, {"name": "tcp_8055", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8055"}]}, {"name": "http_9998", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9998"}]}, {"name": "tcp_135", "type": "object", "service": [{"protocol": "tcp", "destination-port": "135"}]}, {"name": "tcp_1433&1434", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1434"}]}, {"name": "mgmt_nbu", "type": "object", "service": [{"protocol": "tcp", "destination-port": "13724"}, {"protocol": "tcp", "destination-port": "13782"}, {"protocol": "tcp", "destination-port": "1556"}]}, {"name": "mgmt_patrol", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2059"}, {"protocol": "tcp", "destination-port": "3181"}]}, {"name": "mgmt_control_m", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7005"}, {"protocol": "tcp", "destination-port": "7006"}]}, {"name": "http_7779", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7779"}]}, {"name": "tcp_8008", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8008"}]}, {"name": "tcp_20083", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20083"}]}, {"name": "tcp_8066", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8066"}]}, {"name": "tcp_8090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8090"}]}, {"name": "tcp_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "tcp_22", "type": "object", "service": [{"protocol": "tcp", "destination-port": "22"}]}, {"name": "tcp_20", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20"}]}, {"name": "tcp_8065", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8065"}]}, {"name": "tcp_9000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9000"}]}, {"name": "tcp_52701", "type": "object", "service": [{"protocol": "tcp", "destination-port": "52701"}]}, {"name": "tcp_8067", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8067"}]}, {"name": "tcp_8069", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8069"}]}, {"name": "ms_sql", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1433"}]}, {"name": "nbname", "type": "object", "service": [{"protocol": "udp", "destination-port": "137"}]}, {"name": "smb", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}, {"protocol": "tcp", "destination-port": "445"}]}, {"name": "tcp_7778", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7778"}]}, {"name": "tcp_31051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31051"}]}, {"name": "tcp_31060", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31060"}]}, {"name": "tcp_31070", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31070"}]}, {"name": "tcp_41099", "type": "object", "service": [{"protocol": "tcp", "destination-port": "41099"}]}, {"name": "tcp_23215_20", "type": "object", "service": [{"protocol": "tcp", "destination-port": "23217"}, {"protocol": "tcp", "destination-port": "23220"}]}, {"name": "TCP31050_70", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31050"}, {"protocol": "tcp", "destination-port": "31060"}, {"protocol": "tcp", "destination-port": "31070"}]}, {"name": "TCP31051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31051"}]}, {"name": "TCP41099", "type": "object", "service": [{"protocol": "tcp", "destination-port": "41099"}]}, {"name": "tcp_7003", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7003"}]}, {"name": "ospf", "type": "object", "service": [{"protocol": "89", "destination-port": "89"}]}, {"name": "TCP_6611", "type": "object", "service": []}, {"name": "rpc_6611", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6611"}, {"protocol": "udp", "destination-port": "6611"}]}, {"name": "rpc_6610_6614", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6614"}, {"protocol": "udp", "destination-port": "6614"}]}, {"name": "TCP_1433", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1433"}]}, {"name": "tcp_58662", "type": "object", "service": [{"protocol": "tcp", "destination-port": "58662"}]}, {"name": "TCP6379", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6379"}]}, {"name": "tcp_3306", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3306"}]}, {"name": "tcp_6662", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6662"}]}, {"name": "TCP_4433", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4433"}]}, {"name": "TCP8081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8081"}]}, {"name": "TCP 50443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "50443"}]}, {"name": "TCP 8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "tcp_50443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "50443"}]}, {"name": "tcp8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "tcp31306", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31306"}]}, {"name": "tcp_8530_8531", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8530"}, {"protocol": "tcp", "destination-port": "8531"}]}, {"name": "tcp_20201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20201"}]}, {"name": "tcp_9080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9080"}]}, {"name": "tcp_3080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3080"}]}, {"name": "TCP_80_3080_9080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "80"}, {"protocol": "tcp", "destination-port": "9080"}, {"protocol": "tcp", "destination-port": "3080"}]}, {"name": "TCP 9080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9080"}]}, {"name": "tcp_8101_8103_8104", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8101"}, {"protocol": "tcp", "destination-port": "8103"}, {"protocol": "tcp", "destination-port": "8104"}]}, {"name": "TCP8091", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8091"}]}, {"name": "tcp_4444", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4444"}]}, {"name": "TCP_8008", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8008"}]}, {"name": "tcp_1521", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1521"}]}, {"name": "tcp1521", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1521"}]}, {"name": "TCP_9201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9201"}]}, {"name": "TCP_9000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9000"}]}, {"name": "TCP_23215", "type": "object", "service": [{"protocol": "tcp", "destination-port": "23215"}]}, {"name": "tcp_23215", "type": "object", "service": [{"protocol": "tcp", "destination-port": "23215"}]}, {"name": "TCP_20001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20001"}]}, {"name": "TCP_9003", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9003"}]}, {"name": "TCP_21514", "type": "object", "service": [{"protocol": "tcp", "destination-port": "21514"}]}, {"name": "Tcp_7001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7001"}]}, {"name": "TCP_21598", "type": "object", "service": [{"protocol": "tcp", "destination-port": "21598"}]}, {"name": "TCP_3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}]}, {"name": "TCP21405", "type": "object", "service": [{"protocol": "tcp", "destination-port": "21405"}]}, {"name": "TCP8443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8443"}]}, {"name": "TCP_20406", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20406"}]}, {"name": "TCP_20407", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20407"}]}, {"name": "TCP_3191", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3191"}]}, {"name": "TCP_21910", "type": "object", "service": [{"protocol": "tcp", "destination-port": "21910"}]}, {"name": "TCP_7001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7001"}]}, {"name": "TCP_4000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4000"}]}, {"name": "TCP_3000_8686", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3000"}, {"protocol": "tcp", "destination-port": "8686"}]}, {"name": "tcp_8088", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8088"}]}, {"name": "tcp_8000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}, {"name": "TCP_10500", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10500"}]}, {"name": "TCP_2181", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2181"}]}, {"name": "TCP_162", "type": "object", "service": [{"protocol": "tcp", "destination-port": "162"}]}, {"name": "tcp_10051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10051"}]}, {"name": "TCP_28081", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28081"}]}, {"name": "tcp_22601", "type": "object", "service": [{"protocol": "tcp", "destination-port": "22601"}]}, {"name": "tcp_21406", "type": "object", "service": [{"protocol": "tcp", "destination-port": "21406"}]}, {"name": "TCP_20880", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20880"}]}, {"name": "TCP_32600", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32600"}]}, {"name": "TCP_28070", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28070"}]}, {"name": "tcp_29092", "type": "object", "service": [{"protocol": "tcp", "destination-port": "29092"}]}, {"name": "TCP30400", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30400"}]}, {"name": "TCP_9001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9001"}]}, {"name": "TCP9100", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9100"}]}, {"name": "TCP_5480", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5480"}]}, {"name": "TCP9443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9443"}]}, {"name": "TCP_28080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28080"}]}, {"name": "TCP_50094", "type": "object", "service": [{"protocol": "tcp", "destination-port": "50094"}]}, {"name": "TCP_1099", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1099"}]}, {"name": "TCP_5000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5000"}]}, {"name": "TCP_445", "type": "object", "service": [{"protocol": "tcp", "destination-port": "445"}]}, {"name": "TCP10080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10080"}]}, {"name": "TCP_8000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}, {"name": "TCP_8010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8010"}]}, {"name": "TCP_8000_8010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8010"}]}, {"name": "TCP8011", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8011"}]}, {"name": "TCP_30600", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30600"}]}, {"name": "TCP_31306", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31306"}]}, {"name": "TCP_9090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9090"}]}, {"name": "TCP_9091", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9091"}]}, {"name": "TCP_9093", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9093"}]}, {"name": "TCP_3000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3000"}]}, {"name": "TCP_3558", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3558"}]}, {"name": "TCP_30300", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30300"}]}, {"name": "TCP_30416", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30416"}]}, {"name": "TCP_8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_25", "type": "object", "service": [{"protocol": "tcp", "destination-port": "25"}]}, {"name": "TCP_29200", "type": "object", "service": [{"protocol": "tcp", "destination-port": "29200"}]}, {"name": "TCP/UDP111", "type": "object", "service": [{"protocol": "tcp", "destination-port": "111"}, {"protocol": "udp", "destination-port": "111"}]}, {"name": "TCP/UDP_2049", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2049"}, {"protocol": "udp", "destination-port": "2049"}]}, {"name": "TCP/UDP_32768_65535", "type": "object", "service": [{"protocol": "tcp", "destination-port": "65535"}, {"protocol": "udp", "destination-port": "65535"}]}, {"name": "TCP_8089", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8089"}]}, {"name": "TCP_32306", "type": "object", "service": [{"protocol": "tcp", "destination-port": "32306"}]}, {"name": "TCP_8086", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8086"}]}, {"name": "TCP_8087", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8087"}]}, {"name": "TCP9080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9080"}]}, {"name": "TCP_8182", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8182"}]}, {"name": "TCP8000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8000"}]}, {"name": "TCP_8013", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8013"}]}, {"name": "TCP_8018", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8018"}]}, {"name": "TCP_186", "type": "object", "service": [{"protocol": "tcp", "destination-port": "186"}]}, {"name": "TCP_30426", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30426"}]}, {"name": "TCP8080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8080"}]}, {"name": "TCP_5672", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5672"}]}, {"name": "TCP_8004", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8004"}]}, {"name": "TCP_389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "389"}]}, {"name": "TCP_3555", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3555"}]}, {"name": "TCP_20050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20050"}]}, {"name": "TCP_20617", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20617"}]}, {"name": "TCP_10050", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10050"}]}, {"name": "TCP_8090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8090"}]}, {"name": "TCP_6008", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6008"}]}, {"name": "TCP_6006", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6006"}]}, {"name": "TCP_20150", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20150"}]}, {"name": "TCP_6370", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6370"}]}, {"name": "TCP_34443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "34443"}]}, {"name": "TCP_50095", "type": "object", "service": [{"protocol": "tcp", "destination-port": "50095"}]}, {"name": "TCP_6021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6021"}]}, {"name": "TCP_6201", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6201"}]}, {"name": "TCP_21", "type": "object", "service": [{"protocol": "tcp", "destination-port": "21"}]}, {"name": "TCP_3268", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3268"}]}, {"name": "TCP_8085", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8085"}]}, {"name": "TCP_8184", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8184"}]}, {"name": "TCP_8181", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8181"}]}, {"name": "TCP_3556", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3555"}]}, {"name": "TCP3556", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3556"}]}, {"name": "TCP_3557", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3557"}]}, {"name": "TCP_30001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30001"}]}, {"name": "TCP_5044", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5044"}]}, {"name": "TCP_8001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8001"}]}, {"name": "TCP_24433", "type": "object", "service": [{"protocol": "tcp", "destination-port": "24433"}]}, {"name": "TCP_34433", "type": "object", "service": [{"protocol": "tcp", "destination-port": "34433"}]}, {"name": "TCP8022", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8022"}]}, {"name": "TCP_36524", "type": "object", "service": [{"protocol": "tcp", "destination-port": "36524"}]}, {"name": "TCP_9092", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9092"}]}, {"name": "TCP_14433", "type": "object", "service": [{"protocol": "tcp", "destination-port": "14433"}]}, {"name": "TCP_8098", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8098"}]}, {"name": "TCP_4100_4200", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4200"}]}, {"name": "TCP_4422", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4422"}]}, {"name": "TCP_62738", "type": "object", "service": [{"protocol": "tcp", "destination-port": "62738"}]}, {"name": "TCP_5003", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5003"}]}, {"name": "TCP_18080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "18080"}]}, {"name": "TCP_8989", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8989"}]}, {"name": "TCP_7004", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7004"}]}, {"name": "TCP_7005", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7005"}]}, {"name": "TCP_8428", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8428"}]}, {"name": "TCP_69", "type": "object", "service": [{"protocol": "tcp", "destination-port": "69"}, {"protocol": "udp", "destination-port": "69"}]}, {"name": "TCP_6000", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6000"}]}, {"name": "TCP20082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20082"}]}, {"name": "TCP28082", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28082"}]}, {"name": "TCP_5432", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5432"}]}, {"name": "tcp5000_5008", "type": "object", "service": [{"protocol": "tcp", "destination-port": "65535"}]}, {"name": "tcp_5000_5008", "type": "object", "service": [{"protocol": "tcp", "destination-port": "5008"}]}, {"name": "tcp6370", "type": "object", "service": [{"protocol": "tcp", "destination-port": "6370"}]}, {"name": "tcp28090", "type": "object", "service": [{"protocol": "tcp", "destination-port": "28090"}]}, {"name": "tcp44433", "type": "object", "service": [{"protocol": "tcp", "destination-port": "44433"}]}, {"name": "tcp_4191", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4191"}]}, {"name": "TCP_31050_31051_31060", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31050"}, {"protocol": "tcp", "destination-port": "31051"}, {"protocol": "tcp", "destination-port": "31060"}]}, {"name": "TCP_10248", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10248"}]}, {"name": "TCP_27003", "type": "object", "service": [{"protocol": "tcp", "destination-port": "27003"}]}, {"name": "TCP8011_8020", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8020"}]}, {"name": "TCP_8083", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8083"}]}, {"name": "UDP_123", "type": "object", "service": [{"protocol": "udp", "destination-port": "123"}]}, {"name": "TCP_50", "type": "object", "service": [{"protocol": "tcp", "destination-port": "50"}]}, {"name": "TCP_449", "type": "object", "service": [{"protocol": "tcp", "destination-port": "449"}]}, {"name": "TCP_10", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10"}]}, {"name": "TCP_3559", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3559"}]}, {"name": "tcp2881totcp2888", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2888"}]}, {"name": "tcp135totcp139", "type": "object", "service": [{"protocol": "tcp", "destination-port": "139"}]}, {"name": "udp135toudp139", "type": "object", "service": [{"protocol": "udp", "destination-port": "139"}]}, {"name": "tcp80", "type": "object", "service": [{"protocol": "tcp", "destination-port": "80"}]}, {"name": "TCP31050_31051", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31051"}]}, {"name": "TCP/UDP22", "type": "object", "service": [{"protocol": "tcp", "destination-port": "22"}, {"protocol": "udp", "destination-port": "22"}]}, {"name": "TCP8001_8020", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8020"}]}, {"name": "TCP_4555", "type": "object", "service": [{"protocol": "tcp", "destination-port": "4555"}]}, {"name": "TCP_2883", "type": "object", "service": [{"protocol": "tcp", "destination-port": "2883"}]}, {"name": "TCP_31582", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31582"}]}, {"name": "TCP_3001", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3001"}]}, {"name": "TCP_34431_34435", "type": "object", "service": [{"protocol": "tcp", "destination-port": "34435"}]}, {"name": "TCP30900", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30900"}]}, {"name": "TCP_10280", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10280"}]}, {"name": "TCP_30011_30012", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30012"}]}, {"name": "TCP_30010", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30010"}]}, {"name": "TCP_30200", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30200"}]}, {"name": "TCP_19080", "type": "object", "service": [{"protocol": "tcp", "destination-port": "19080"}]}, {"name": "TCP_8092", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8092"}]}, {"name": "TCP_1443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "1443"}]}, {"name": "TCP_8866", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8866"}]}, {"name": "TCP_161", "type": "object", "service": [{"protocol": "tcp", "destination-port": "161"}]}, {"name": "TCP_9999", "type": "object", "service": [{"protocol": "tcp", "destination-port": "9999"}]}, {"name": "TCP_7569", "type": "object", "service": [{"protocol": "tcp", "destination-port": "7569"}]}, {"name": "TCP_10014", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10014"}]}, {"name": "TCP_8899", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8899"}]}, {"name": "TCP_30002", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30002"}]}, {"name": "TCP8083&8084", "type": "object", "service": [{"protocol": "tcp", "destination-port": "8084"}]}, {"name": "tcp100", "type": "object", "service": [{"protocol": "tcp", "destination-port": "100"}]}, {"name": "3389", "type": "object", "service": [{"protocol": "tcp", "destination-port": "3389"}, {"protocol": "tcp", "destination-port": "22"}, {"protocol": "tcp", "destination-port": "23"}, {"protocol": "tcp", "destination-port": "24"}, {"protocol": "tcp", "destination-port": "25"}]}, {"name": "514", "type": "object", "service": [{"protocol": "tcp", "destination-port": "514"}]}, {"name": "TCP30021", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30021"}]}, {"name": "TCP_10035", "type": "object", "service": [{"protocol": "tcp", "destination-port": "10035"}]}, {"name": "TCP_30108", "type": "object", "service": [{"protocol": "tcp", "destination-port": "30108"}]}, {"name": "TCP_11668", "type": "object", "service": [{"protocol": "tcp", "destination-port": "11668"}]}, {"name": "TCP_31003", "type": "object", "service": [{"protocol": "tcp", "destination-port": "31003"}]}, {"name": "tcp:20443", "type": "object", "service": [{"protocol": "tcp", "destination-port": "20443"}]}]}
<YY-IAFW-01>dis cu
#
version 7.1.064, Release 9616P30
#
sysname YY-IAFW-01
#
clock timezone bj add 08:00:00
#
context Admin id 1
#
ip vpn-instance management
route-distinguisher 100:1
vpn-target 100:1 import-extcommunity
vpn-target 100:1 export-extcommunity
#
irf mac-address persistent timer
irf auto-update enable
undo irf link-delay
irf member 1 priority 32
irf member 2 priority 1
#
security-zone intra-zone default permit
#
track 1 interface GigabitEthernet1/4/1 physical
#             
track 2 interface GigabitEthernet2/4/1 physical
#
track 3 interface GigabitEthernet1/4/2 physical
#
track 4 interface GigabitEthernet2/4/2 physical
#
ospf 1
area 0.0.0.0
  network ************ *********
#
lldp global enable
#
password-recovery enable
#
vlan 1
#
vlan 200
#
irf-port 1/2
port group interface GigabitEthernet1/0/2
port group interface GigabitEthernet1/0/3
#
irf-port 2/1  
 port group interface GigabitEthernet2/0/2
port group interface GigabitEthernet2/0/3
#
object-group ip address ***********
security-zone YWYY
0 network host address ***********
#
object-group ip address ***********
security-zone YWYY
0 network host address ***********
#
object-group ip address ***********
security-zone YWYY
0 network host address ***********
#
object-group ip address ************-242
security-zone YWYY
0 network range ************ ************
#
object-group ip address ***********/16
security-zone Trust
0 network subnet *********** ***********
#             
object-group ip address ***********/24
description ***********/24
security-zone Trust
0 network subnet *********** *************
#
object-group ip address ***********/24
description ***********/24
security-zone Trust
0 network subnet *********** *************
#
object-group ip address *************
security-zone Trust
0 network subnet ************* *************
#
object-group ip address ************/24
description ************/24
security-zone Trust
0 network subnet ************ *************
#
object-group ip address ***********/24
description ***********/24
security-zone Trust
0 network subnet *********** *************
#
object-group ip address *************
security-zone Trust
0 network host address *************
#
object-group ip address *************
security-zone Trust
0 network host address *************
#
object-group ip address inside
security-zone Trust
0 network subnet ************ *************
10 network subnet ************ *************
20 network subnet ************* *************
30 network subnet ************* *************
40 network subnet ************* *************
50 network subnet ************* *************
60 network subnet ************* *************
70 network subnet ************* *************
80 network subnet ************* *************
90 network subnet ************* *************
100 network subnet ************* *************
110 network subnet ************* *************
120 network subnet ************* *************
130 network subnet ************* *************
140 network subnet ************* *************
#
object-group ip address RMOAS
security-zone Trust
0 network range *************** ***************
10 network range *************** ***************
20 network host address ***************
#
object-group ip address ShiPin
security-zone Trust
0 network host address ***************
10 network host address ***************
20 network range *************** ***************
30 network host address ***************
#
object-group ip address TC-10.209
security-zone YWYY
0 network subnet ********** *************
10 network subnet ********** *************
20 network subnet ********** *************
30 network subnet ********** *************
#
object-group ip address TC-**********
security-zone YWYY
0 network subnet ********** *************
#
object-group ip address VDI_10.209
security-zone YWYY
0 network subnet ********** *************
20 network subnet ********** *************
30 network subnet ********** *************
40 network subnet ********** ***************
#
object-group ip address VDI_172.100
security-zone Trust
0 network range ************* ***************
#
object-group ip address YW-VDI_Zhuomian
security-zone YWYY
0 network subnet ********** *************
10 network subnet ********** *************
20 network subnet ********** *************
#
object-group ip address YW_**********
security-zone YWYY
0 network subnet ********** *************
#
object-group ip address YY_Print
security-zone Trust
0 network range 172.100.201.201 172.100.201.204
10 network range 172.100.202.201 172.100.202.204
20 network range 172.100.207.201 172.100.207.202
#
object-group service TCP-137
0 service tcp destination eq 137
#
object-group service TCP-138
0 service tcp destination eq 138
#
object-group service TCP-139
0 service tcp destination eq 139
#
object-group service TCP-1433
0 service tcp destination eq 1433
#
object-group service TCP-3306
0 service tcp destination eq 3306
#
object-group service TCP_137
0 service tcp destination eq 137
#
object-group service UDP-137
0 service udp destination eq 137
#
object-group service UDP-138
0 service udp destination eq 138
#
object-group service UDP-139
0 service udp destination eq 139
#
object-group service UDP-4068
0 service udp destination eq 4068
#
object-group service UDP-4096
0 service udp destination eq 4096
#
interface Reth1
ip address 172.200.254.2 255.255.255.252
member interface GigabitEthernet1/4/1 priority 255
member interface GigabitEthernet2/4/1 priority 50
#
interface Reth2
ip address ************* 255.255.255.252
member interface GigabitEthernet1/4/2 priority 255
member interface GigabitEthernet2/4/2 priority 50
#
interface Reth3
ip address 10.209.0.245 255.255.255.252
member interface GigabitEthernet1/4/4 priority 250
member interface GigabitEthernet2/4/4 priority 50
#
interface NULL0
#
interface GigabitEthernet1/0/0
port link-mode route
combo enable copper
ip binding vpn-instance management
ip address 172.200.10.7 *************
#
interface GigabitEthernet1/0/1
port link-mode route
combo enable fiber
#             
interface GigabitEthernet1/4/1
port link-mode route
#
interface GigabitEthernet1/4/2
port link-mode route
#
interface GigabitEthernet1/4/3
port link-mode route
#
interface GigabitEthernet1/4/4
port link-mode route
#
interface GigabitEthernet1/4/5
port link-mode route
#
interface GigabitEthernet1/4/6
port link-mode route
#
interface GigabitEthernet1/4/7
port link-mode route
#
interface GigabitEthernet2/0/0
port link-mode route
combo enable fiber
#
interface GigabitEthernet2/0/1
port link-mode route
combo enable fiber
#
interface GigabitEthernet2/4/0
port link-mode route
shutdown
#
interface GigabitEthernet2/4/1
port link-mode route
#
interface GigabitEthernet2/4/2
port link-mode route
#
interface GigabitEthernet2/4/3
port link-mode route
#
interface GigabitEthernet2/4/4
port link-mode route
#
interface GigabitEthernet2/4/5
port link-mode route
#
interface GigabitEthernet2/4/6
port link-mode route
#
interface GigabitEthernet2/4/7
port link-mode route
#
interface GigabitEthernet1/4/0
port link-mode bridge
port link-type trunk
port trunk permit vlan all
#
interface GigabitEthernet1/0/2
combo enable fiber
#
interface GigabitEthernet1/0/3
combo enable fiber
#
interface GigabitEthernet2/0/2
combo enable fiber
#
interface GigabitEthernet2/0/3
combo enable fiber
#
security-zone name Local
#
security-zone name Trust
import interface GigabitEthernet1/4/2
import interface GigabitEthernet2/4/2
import interface Reth2
#
security-zone name DMZ
#
security-zone name Untrust
import interface GigabitEthernet1/4/1
import interface GigabitEthernet2/4/1
import interface Reth1
#
security-zone name Management
import interface GigabitEthernet1/0/0
#
security-zone name YWYY
import interface GigabitEthernet1/4/4
import interface GigabitEthernet2/4/4
import interface Reth3
#
scheduler logfile size 16
#
line class console
user-role network-admin
#
line class vty
user-role network-operator
#
line con 0 1
user-role network-admin
#
line vty 0 63
authentication-mode scheme
user-role network-admin
#
ip route-static 0.0.0.0 0 *************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static ********** 24 ************
ip route-static *********** 16 *************
ip route-static *********** 24 *************
ip route-static *********** 24 *************
ip route-static ************ 24 *************
ip route-static *********** 24 *************
ip route-static vpn-instance management 0.0.0.0 0 **************
#
info-center loghost source GigabitEthernet1/0/0
info-center loghost ************** facility local6
info-center source FILTER logfile deny
#
snmp-agent
snmp-agent local-engineid 800063A280743A20312A2C00000001
snmp-agent community read cslc_snmp
snmp-agent sys-info version all
#
ssh server enable
ssh user admin service-type stelnet authentication-type password
#
redundancy group aaa
member interface Reth1
member interface Reth2
node 1
  bind slot 1
  priority 100
  track 1 interface GigabitEthernet1/4/1
  track 2 interface GigabitEthernet2/4/1
node 2
  bind slot 2
  priority 50
  track 3 interface GigabitEthernet1/4/2
  track 4 interface GigabitEthernet2/4/2
#
ntp-service enable
ntp-service unicast-server *********** source GigabitEthernet1/0/0
#
domain system
#
domain default enable system
#
role name level-0
description Predefined level-0 role
#             
role name level-1
description Predefined level-1 role
#
role name level-2
description Predefined level-2 role
#
role name level-3
description Predefined level-3 role
#
role name level-4
description Predefined level-4 role
#
role name level-5
description Predefined level-5 role
#
role name level-6
description Predefined level-6 role
#
role name level-7
description Predefined level-7 role
#
role name level-8
description Predefined level-8 role
#
role name level-9
description Predefined level-9 role
#
role name level-10
description Predefined level-10 role
#
role name level-11
description Predefined level-11 role
#
role name level-12
description Predefined level-12 role
#
role name level-13
description Predefined level-13 role
#
role name level-14
description Predefined level-14 role
#
user-group system
#
local-user admin class manage
password hash $h$6$rXaYtGXCzZzO55OI$AKteBPRU4j7bgb+LYqfmKgIasy+753sy59BXnYNAUA6BEG/dmlnzQ8D/uKSMgw/srDtmHXrjgR89L2WmzuLGyQ==
service-type ssh terminal https
authorization-attribute user-role level-3
authorization-attribute user-role network-admin
authorization-attribute user-role network-operator
#
local-user cslc class manage
password hash $h$6$/jsYknL0sCZDCwYr$xTosT3MXvR2yTsmDFuFYgm34kLKdqJO+d/IPyBV39nul/FZ8nPgndeXdKx99Eun5lEQBAt2LyhjrjUfvRBWEjQ==
service-type ssh terminal https
authorization-attribute user-role network-admin
authorization-attribute user-role network-operator
#
local-user linshi class manage
password hash $h$6$63O5CHvySf810MS7$3JNXHbXuSZ/jv7gxnO+/d85W67pZvGtmwMFbl0V5yrjGB0xjtIk1XqyRSJ2BBJdIGomiCNBkryt6YWfDELRO9A==
service-type ssh telnet terminal http https
authorization-attribute work-directory slot1#sda0:
authorization-attribute user-role network-admin
#
local-user netadmin class manage
password hash $h$6$v1EDP0mecD7OMf3l$2Tg0Ourk20pk4q/UwvB6KeP4v/ymF669j8TfhkL02mC8lRtESKEY93+YtQNEWGekbVGLeNGL3WckUW/xImvf8A==
service-type ssh terminal http https
authorization-attribute work-directory slot1#sda0:
authorization-attribute user-role network-admin
#             
 session statistics enable
session synchronization enable
 session synchronization dns http
#
ipsec logging negotiation enable
#
nat policy
#
ike logging negotiation enable
#
ip https enable
webui log enable
#
loadbalance isp file sda0:/lbispinfo_v1.5.tp
#
security-policy ip
rule 1 name 内网出访
  action pass
  counting enable
  source-zone Trust
  source-zone Local
  destination-zone Untrust
  destination-zone Local
  source-ip ************/24
  source-ip ***********/24
  source-ip ***********/24
  source-ip ***********/24
  source-ip inside
  source-ip *************
  source-ip-host *************
 rule 3 name icmp
  action pass
  counting enable
  service ping
rule 2 name 入访deny
  counting enable
  source-zone Untrust
  destination-zone Trust
rule 4 name YW-VDI_To_Internet
  action pass
  counting enable
  source-zone YWYY
  destination-zone Untrust
  source-ip VDI_10.209
  source-ip ************-242
rule 5 name YW-VDI_To_RMOAS
  action pass
  logging enable
  counting enable
  source-zone YWYY
  destination-zone Trust
  source-ip YW-VDI_Zhuomian
  destination-ip RMOAS
  service https
  service http
  service TCP-1433
rule 6 name YW-TC_To_VDI
  action pass
  counting enable
  source-zone YWYY
  destination-zone Trust
  source-ip TC-10.209
  destination-ip VDI_172.100
  destination-ip *************
  destination-ip *************
rule 7 name YW-DNS_To_Internet
  action pass
  counting enable
  source-zone YWYY
  destination-zone Untrust
  source-ip YW_**********
  service dns-tcp
  service dns-udp
  service ping
  service ntp
rule 8 name Old-TC_To_New-VDI
  action pass
  counting enable
  source-zone Trust
  destination-zone YWYY
  source-ip ***********/16
  destination-ip ***********
  destination-ip ***********
  destination-ip ***********
rule 9 name YW-VDI_To_ShiPin
  action pass
  counting enable
  source-zone YWYY
  destination-zone Trust
  source-ip YW-VDI_Zhuomian
  destination-ip ShiPin
  service TCP-3306
  service UDP-4068
  service UDP-4096
  service smb
  service TCP-139
  service UDP-139
  service TCP-138
  service UDP-138
  service TCP_137
  service UDP-137
  service TCP-137
rule 10 name YW-VDI_To_Print
  action pass
  counting enable
  source-zone YWYY
  destination-zone Trust
  source-ip YW-VDI_Zhuomian
  destination-ip YY_Print
#
return

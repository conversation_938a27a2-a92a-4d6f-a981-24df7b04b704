<XWHPD-NE3DIL-FW01>dis curr
#
 version 7.1.064, Release 9141P38
#
 sysname XWHPD-NE3DIL-FW01
#
 clock timezone BeiJing add 08:00:00
#
context Admin id 1
#
failover group 1
 bind chassis 1 slot 6 cpu 1 primary
 bind chassis 2 slot 6 cpu 1 secondary
#
ip vpn-instance management
 route-distinguisher 1000000000:1
 vpn-target 1000000000:1 import-extcommunity
 vpn-target 1000000000:1 export-extcommunity
#
 irf domain 52
 irf mac-address persistent always
 irf auto-update enable
 irf auto-merge enable
 undo irf link-delay
 irf member 1 priority 32
 irf member 2 priority 1
#
 undo link-aggregation load-sharing mode local-first
#
track 1 interface Bridge-Aggregation1
#
track 3 interface Bridge-Aggregation3
#
track 101 interface Blade1/6/0/1 physical
#
track 102 interface Blade1/6/0/2 physical
#
track 201 interface Blade2/6/0/1 physical
#
track 202 interface Blade2/6/0/2 physical
#
 ip ttl-expires enable
#
 forwarding policy per-packet
#
 nat static-load-balance enable
#
 lldp global enable
#
 system-working-mode standard
 password-recovery enable
#
vlan 1
#
vlan 3011
 name TENANT01_MS
#
vlan 3012
 name TENANT02_MS
#
vlan 3013
 name TENANT03_MS
#
vlan 3014
 name TENANT04_MS
#
vlan 3015
 name TENANT05_MS
#
irf-port 1/2
 port group interface Ten-GigabitEthernet1/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet1/9/1/12 mode enhanced
#
irf-port 2/1
 port group interface Ten-GigabitEthernet2/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet2/9/1/12 mode enhanced
#
object-group ip address ********39
 0 network host address ********39
#
object-group ip address **********-162
 security-zone TENANT02_MS_Outside
 0 network range ********** **********
#
object-group ip address *********
 security-zone TENANT02_MS_Outside
 0 network host address *********
#
object-group ip address *********/24
 security-zone TENANT02_MS_Outside
 0 network subnet ********* *************
#
object-group ip address ***************
 security-zone TENANT02_MS_Outside
 0 network host address ***************
#
object-group ip address *******
 security-zone TENANT02_MS_Inside
 0 network subnet ******* *********
#
object-group ip address *******/8
 security-zone TENANT02_MS_Outside
 0 network subnet ******* *********
#
object-group ip address *********/16
 0 network subnet ********* ***********
#
object-group ip address **********/24
 0 network subnet ********** *************
#
object-group ip address *********
 security-zone TENANT02_MS_Outside
 0 network subnet ********* ***********
#
object-group ip address *********/16
 0 network subnet ********* ***********
#              
object-group ip address *********-2
 security-zone TENANT02_MS_Outside
 0 network range ********* *********
#
object-group ip address *************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address *************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address *************
 0 network host address *************
#
object-group ip address *************/32
 0 network host address *************
#
object-group ip address ***********
 security-zone TENANT02_MS_Inside
 0 network host address ***********
#
object-group ip address ***********
 security-zone TENANT02_MS_Inside
 0 network host name ***********
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ***********/22
 security-zone TENANT02_MS_Inside
 0 network subnet *********** *************
#
object-group ip address ***********/24
 security-zone TENANT02_MS_Inside
 0 network subnet *********** *************
#
object-group ip address ************-14
 0 network range ************ ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ***********/24
 security-zone TENANT02_MS_Inside
 0 network subnet *********** *************
#
object-group ip address ***********/24
 security-zone TENANT02_MS_Inside
 0 network subnet *********** *************
#
object-group ip address ***********/32
 security-zone TENANT02_MS_Inside
 0 network host address ***********
#
object-group ip address ***********
 security-zone TENANT02_MS_Inside
 0 network host address ***********
#
object-group ip address ***********
 security-zone TENANT02_MS_Outside
 0 network host address ***********
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address ************
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address ************
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address ************
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address ***********-14
 0 network range *********** **********4
#
object-group ip address ***********-59
 security-zone TENANT02_MS_Outside
 0 network range *********** ***********
#
object-group ip address **********-3
 security-zone TENANT02_MS_Outside
 0 network range ********** **********
#
object-group ip address **********1-16
 security-zone TENANT02_MS_Outside
 0 network range **********1 **********6
#
object-group ip address ***********8-220
 security-zone TENANT02_MS_Outside
 0 network range ***********8 ***********0
#
object-group ip address ***********-91
 0 network subnet *********** ***************
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address 4.190.81.42
 0 network host address 4.190.81.42
#
object-group ip address ************-103
 0 network range ************ 4.190.89.103
#
object-group ip address ***********/24
 security-zone TENANT02_MS_Outside
 0 network subnet *********** *************
#
object-group ip address **********
 0 network host address **********
#
object-group ip address 4A-*********
 security-zone TENANT02_MS_Outside
 0 network subnet ********* *************
#
object-group ip address AlarmManager_4.190.121.71-72
 security-zone TENANT02_MS_Inside
 0 network range 4.190.121.71 4.190.121.72
#
object-group ip address Ansbile-************
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address Ansbile-************
 0 network host address ************
#
object-group ip address Backup_Server_***********/32
 security-zone TENANT02_MS_Outside
 0 network host address ***********
#
object-group ip address BISMONITORCOLLECT-F5
 0 network host address 4.190.162.17
#
object-group ip address BOCC
 security-zone TENANT02_MS_Outside
 0 network subnet ******** *************
 10 network subnet 18.2.12.0 *************
 20 network subnet ********* *************
 30 network subnet ********* *************
 40 network subnet ********* *************
 50 network subnet ********** *************
 60 network subnet 3.30.11.0 *************
 70 network subnet 9.66.1.0 *************
 80 network subnet 9.66.2.0 *************
 90 network subnet 9.66.3.0 *************
 100 network subnet ********* *************
#
object-group ip address BOCC&4A
 0 network subnet ******** *************
 10 network subnet 18.2.12.0 *************
 20 network subnet ********* *************
 30 network subnet ********* *************
 40 network subnet ********* *************
 50 network subnet ********* *************
 60 network subnet ********** *************
 70 network subnet 9.66.1.0 *************
 80 network subnet 9.66.2.0 *************
 90 network subnet 9.66.3.0 *************
 100 network subnet ********* *************
#
object-group ip address BOS
 0 network range 4.103.120.41 4.103.120.42
#
object-group ip address CAS_F5_4.190.162.3
 0 network host address 4.190.162.3
#
object-group ip address CASGW_4.190.162.4
 0 network host address 4.190.162.4
#
object-group ip address Configcenter
 0 network group-object configcenter01_************
 1 network group-object configcenter02_************
 2 network group-object configcenter03_***********3
#
object-group ip address configcenter01_************
 0 network host address ************
#
object-group ip address configcenter02_************
 0 network host address ************
#
object-group ip address configcenter03_***********3
 0 network host address ***********3
#
object-group ip address "CORE ZABBIX PROXY-***********1"
 security-zone TENANT02_MS_Outside
 0 network host address ***********1
#
object-group ip address CORE-**********/24
 security-zone TENANT02_MS_Outside
 0 network subnet ********** *************
#
object-group ip address CORE-F5-4.190.162.0/24
 security-zone TENANT02_MS_Outside
 0 network subnet 4.190.162.0 *************
#
object-group ip address CSLC-baoleiji-*********
 security-zone TENANT02_MS_Outside
 0 network subnet ********* *************
#
object-group ip address CSLC-baoleiji-**********
 security-zone TENANT02_MS_Outside
 0 network subnet ********** *************
#
object-group ip address CSLC-Syslog-gateway-**********
 0 network host address **********
#
object-group ip address CSLRMSFSP01-********
 security-zone TENANT02_MS_Outside
 0 network host address *************
#
object-group ip address DES-**********-18
 security-zone TENANT02_MS_Outside
 0 network range ********** 4.176.1.18
#
object-group ip address DIP-************-57
 security-zone TENANT02_MS_Outside
 0 network range ************ 198.3.100.57
#
object-group ip address DIP-************-93
 security-zone TENANT02_MS_Outside
 0 network range ************ 198.3.100.93
#
object-group ip address DMZ_Zabbix-Proxy-***********
 security-zone TENANT02_MS_Outside
 0 network host address ***********
#
object-group ip address FAM-*********/24
 security-zone TENANT01_MS_Outside
 0 network subnet ********* *************
#
object-group ip address flink-*************-119
 security-zone TENANT02_MS_Inside
 0 network range ************* ***********19
#
object-group ip address FOC-*********
 0 network host address *********
#
object-group ip address FOC-***********
 0 network host address ***********
#
object-group ip address G2-**********-95
 security-zone TENANT02_MS_Outside
 0 network subnet ********** ***************
#              
object-group ip address G2-*********-92_94-95
 security-zone TENANT02_MS_Outside
 0 network range ********* *********
 10 network subnet ********* ***************
#
object-group ip address G2-ESXI-**********/16
 security-zone TENANT02_MS_Outside
 0 network subnet ********** ***********
#
object-group ip address G2-ESXI-**********/24
 security-zone TENANT02_MS_Inside
 0 network subnet ********** *************
#
object-group ip address G2-ESXI-**********/24
 security-zone TENANT02_MS_Inside
 0 network subnet ********** *************
#
object-group ip address G2-SDAS-Grafana-*********/32
 security-zone TENANT02_MS_Outside
 0 network host address *********
#
object-group ip address G2-Solarwinds-*************/32
 0 network host address *************
#
object-group ip address G2_DC_********/32
 security-zone TENANT02_MS_Outside
 0 network host address ********
#
object-group ip address G2_NTP
 security-zone TENANT02_MS_Outside
 0 network host address ********73
 10 network host address ***********
#
object-group ip address G2_RTQDB_*********/32
 security-zone TENANT02_MS_Outside
 0 network host address *********
#
object-group ip address G2_WEBDC_Group
 0 network host address **********
 10 network host address **********
 20 network host address **********
 30 network host address **********
#
object-group ip address G2DC
 security-zone TENANT02_MS_Outside
 0 network range ******** ********
#
object-group ip address G2DMZ-*********
 security-zone TENANT02_MS_Outside
 0 network subnet ********* *************
#
object-group ip address G2FTP-**********
 security-zone TENANT02_MS_Outside
 0 network host address **********
#
object-group ip address G2OCS-**********
 security-zone TENANT02_MS_Outside
 0 network host address **********
#
object-group ip address G2OCS_**********/32
 0 network host address **********
#
object-group ip address G2TOHERAPROXY01-temp
 0 network host address **********
#
object-group ip address G2TRANSROUTE-**********-84
 security-zone TENANT02_MS_Inside
 0 network range ********** **********
#              
object-group ip address G2TRANSROUTE_**********-84
 security-zone TENANT02_MS_Outside
 0 network range ********** **********
#
object-group ip address G3-MS-ELKF5-************/32
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address G3-MS-F5-************
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address G3_*********/16
 0 network subnet ********* ***********
#
object-group ip address G3_Mail_*********
 security-zone TENANT02_MS_Outside
 0 network host address *********
#
object-group ip address G3_MS_***********/24
 security-zone TENANT02_MS_Inside
 0 network subnet *********** *************
#              
object-group ip address G3_MS_***********1-26
 security-zone TENANT02_MS_Inside
 0 network range ***********1 ************
#
object-group ip address G3_MS_***********51-212
 security-zone TENANT02_MS_Inside
 0 network range ***********51 *************
#
object-group ip address G3_MS_*************-152
 security-zone TENANT02_MS_Inside
 0 network range ***********51 *************
#
object-group ip address G3_MS_*************-182
 security-zone TENANT02_MS_Inside
 0 network range ************* *************
#
object-group ip address G3_MS_*************-212
 security-zone TENANT03_MS_Inside
 0 network range ************* *************
#
object-group ip address G3_MS_************-32
 security-zone TENANT02_MS_Inside
 0 network range ************ ************
#
object-group ip address G3_MS_************-42
 security-zone TENANT02_MS_Inside
 0 network range ************ ************
#
object-group ip address G3_MS_************-63
 security-zone TENANT02_MS_Inside
 0 network range ************ ************
#
object-group ip address G3_MS_************-82
 security-zone TENANT02_MS_Inside
 0 network range ************ ************
#
object-group ip address G3_MS_***********1-24
 security-zone TENANT02_MS_Inside
 0 network range ***********1 ***********4
#
object-group ip address G3_MS_************/32
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address G3_MS_***********/32
 security-zone TENANT02_MS_Inside
 0 network host address ***********
#
object-group ip address G3_MS_***********/32
 security-zone TENANT02_MS_Inside
 0 network host address ***********
#
object-group ip address G3_MS_***********-11
 security-zone TENANT02_MS_Inside
 0 network range *********** ************
#
object-group ip address G3_MS_AIDB_*************-123
 security-zone TENANT02_MS_Inside
 0 network range ************* *************
#
object-group ip address G3_MS_CAS-F5_************/32
 0 network host address ************
#
object-group ip address G3_MS_CAS_*************-234
 0 network range ************* *************
#
object-group ip address G3_MS_GATEWAY_*************-244
 0 network range ************* *************
#              
object-group ip address G3_MS_GW-F5_************/32
 0 network host address ************
#
object-group ip address G3_MS_MONITOREDI
 security-zone TENANT02_MS_Inside
 0 network range ************ ************
 10 network host address ***********
#
object-group ip address G3_MS_MONITORGRAFAN
 0 network range ************ ************
 10 network host address ***********
#
object-group ip address G3_MS_SDAS01-**********91/32
 security-zone TENANT02_MS_Inside
 0 network host address **********91
#
object-group ip address G3_MS_SDAS01-**********92/32
 security-zone TENANT02_MS_Inside
 0 network host address **********92
#
object-group ip address G3_MS_TIDB-Mon_*************/32
 0 network host address *************
#              
object-group ip address G3_MS_VC_***********/32
 0 network host address ***********
#
object-group ip address G3_NTP_***********1-252
 security-zone TENANT02_MS_Outside
 0 network range ***********1 ***********2
#
object-group ip address G3BISMONTORCOLLECT
 0 network range ********** **********
#
object-group ip address G3BMSDB-***********-15
 security-zone TENANT02_MS_Outside
 0 network range *********** 4.190.88.15
#
object-group ip address G3BOSDB-************-104
 0 network range ************ 4.190.89.104
#
object-group ip address G3ECCSYS01-*************
 description G3ECCSYS01-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address G3ECCSYS01-*************
 description G3ECCSYS01-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address G3ELK-***********-5
 security-zone TENANT02_MS_Inside
 0 network range *********** 4.190.121.5
#
object-group ip address G3GAIA-F5-************
 security-zone TENANT02_MS_Inside
 0 network host address ************
#
object-group ip address G3MONITORGAIA-*************-162
 security-zone TENANT02_MS_Inside
 0 network range ************* ***********62
#
object-group ip address G3OPERVM
 security-zone TENANT02_MS_Outside
 0 network range ********** 4.190.83.2
#
object-group ip address G3OPERVM-**********-2
 security-zone TENANT02_MS_Outside
 0 network range ********** 4.190.83.2
#
object-group ip address G3RTQDBVIP-4.190.88.33
 security-zone TENANT02_MS_Outside
 0 network host address 4.190.88.33
#
object-group ip address G3SIMULATIONTRANSROUTER01
 0 network host address 4.190.49.1
#
object-group ip address G3TOHERAPROXY-**********-12
 security-zone TENANT02_MS_Outside
 0 network range ********** *********2
#
object-group ip address G3TOHERAPROXY-F5-***********
 security-zone TENANT02_MS_Outside
 0 network host address ***********
#
object-group ip address G3TSPAPP01-***********01
 0 network host address ***********01
#
object-group ip address G3TSPAPP02-*************
 0 network host address *************
#
object-group ip address "GW ZABBIX PROXY-************"
 security-zone TENANT02_MS_Outside
 0 network host address ************
#
object-group ip address GW-F5-4.190.161.0/24
 security-zone TENANT02_MS_Outside
 0 network subnet 4.190.161.0 *************
#
object-group ip address HARBOR
 0 network group-object HARBOR01_4.190.120.31
 1 network group-object HARBOR02_4.190.120.32
#
object-group ip address HARBOR01_4.190.120.31
 0 network host address 4.190.120.31
#
object-group ip address HARBOR02_4.190.120.32
 0 network host address 4.190.120.32
#
object-group ip address harbor_F5_***********
 0 network host address ***********
#
object-group ip address ItoSchedule-*************-172
 security-zone TENANT02_MS_Inside
 0 network range ************* ***********72
#
object-group ip address JianKong_***********
 0 network subnet *********** *************
#
object-group ip address JiGuan-***************
 security-zone TENANT02_MS_Outside
 0 network host address ***************
#
object-group ip address JIGUAN_***************
 security-zone TENANT02_MS_Outside
 0 network host address ***************
#
object-group ip address LinShi-18.5.0.0/16
 security-zone TENANT02_MS_Outside
 0 network subnet 18.5.0.0 ***********
#
object-group ip address LinShi_18.5.95.0/24
 security-zone TENANT02_MS_Outside
 0 network subnet 18.5.95.0 *************
#
object-group ip address LinShi_18.6.4.0/24
 security-zone TENANT02_MS_Outside
 0 network subnet 18.6.4.0 *************
#
object-group ip address MGMT_*********/16
 security-zone TENANT02_MS_Outside
 0 network subnet ********* ***********
#
object-group ip address MHA-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address MiMaGongJu-***********
 security-zone TENANT02_MS_Outside
 0 network host address ***********
#
object-group ip address MONITORALERTER01-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address MONITORALERTER02-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address MonitorZK-*************-135
 security-zone TENANT02_MS_Inside
 0 network range ************* ***********35
#
object-group ip address MS-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address MS-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address MS-F5-************
 security-zone TENANT02_MS_Outside
 0 network host address ************
#
object-group ip address MS_***********/22
 security-zone TENANT02_MS_Inside
 0 network subnet *********** *************
#
object-group ip address NAS-**********/32
 0 network host address **********
#
object-group ip address NAS_**********
 0 network host address **********
#
object-group ip address NAS_**********
 0 network host address **********
#
object-group ip address NET-NTP-**********
 0 network host address **********
#
object-group ip address Network_Mgt_**********/24
 0 network subnet ********** *************
#
object-group ip address NetworkOOB-*********/23
 0 network subnet ********* *************
 1 network subnet ********* *************
#
object-group ip address nfs-**********
 security-zone TENANT02_MS_Outside
 0 network host address **********
#
object-group ip address NFS-*********
 0 network host address *********
#
object-group ip address nginx01_************/32
 0 network host address ************
#
object-group ip address nginx02_************/32
 0 network host address ************
#
object-group ip address OPS
 0 network group-object OPS01_************
 1 network group-object OPS02_************
#
object-group ip address OPS01_************
 0 network host address ************
#
object-group ip address OPS02_************
 0 network host address ************
#
object-group ip address OPSTOOL_*************-182
 security-zone TENANT02_MS_Inside
 0 network range ************* *************
#
object-group ip address OTMServerV01
 0 network host address **********
#
object-group ip address Radius-**********
 security-zone TENANT02_MS_Outside
 0 network host address **********
#
object-group ip address RDC-F5-************
 0 network host address ************
#
object-group ip address redis-**********-9
 security-zone TENANT02_MS_Outside
 0 network range ********** **********
#
object-group ip address Redis_feioltp
 0 network group-object Redis_feioltp01_***********
 1 network group-object Redis_feioltp02_***********
 2 network group-object Redis_feioltp03_***********
 3 network group-object Redis_feioltp04_***********
 4 network group-object Redis_feioltp05_***********
 5 network group-object Redis_feioltp06_***********
 6 network group-object Redis_feioltp07_***********
 7 network group-object Redis_feioltp08_***********
 8 network group-object Redis_feioltp09_***********
#
object-group ip address Redis_feioltp01_***********
 0 network host address ***********
#              
object-group ip address Redis_feioltp02_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp03_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp04_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp05_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp06_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp07_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp08_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp09_***********
 0 network host address ***********
#
object-group ip address Redis_oltp
 0 network group-object Redis_oltp01_***********
 1 network group-object Redis_oltp02_***********
 2 network group-object Redis_oltp03_***********
 3 network group-object Redis_oltp04_***********
 4 network group-object Redis_oltp05_***********
 5 network group-object Redis_oltp06_***********
 6 network group-object Redis_oltp07_***********
 7 network group-object Redis_oltp08_***********
 8 network group-object Redis_oltp09_***********
#
object-group ip address Redis_oltp01_***********
 0 network host address ***********
#
object-group ip address Redis_oltp02_***********
 0 network host address ***********
#
object-group ip address Redis_oltp03_***********
 0 network host address ***********
#
object-group ip address Redis_oltp04_***********
 0 network host address ***********
#
object-group ip address Redis_oltp05_***********
 0 network host address ***********
#
object-group ip address Redis_oltp06_***********
 0 network host address ***********
#
object-group ip address Redis_oltp07_***********
 0 network host address ***********
#
object-group ip address Redis_oltp08_***********
 0 network host address ***********
#
object-group ip address Redis_oltp09_***********
 0 network host address ***********
#
object-group ip address SBSG2OPSFTP01-************
 security-zone TENANT02_MS_Outside
 0 network host address ************
#
object-group ip address SBSG2OTJob-***********
 security-zone TENANT02_MS_Outside
 0 network host address ***********
#
object-group ip address SFTP-************
 security-zone TENANT02_MS_Outside
 0 network host address ************
#
object-group ip address ShuMeiPai-**********
 description ShuMeiPai-**********
 security-zone TENANT02_MS_Outside
 0 network host address **********
#
object-group ip address SJZT
 0 network host address **********
#
object-group ip address SOC-*************-112
 0 network range ************* *************
#
object-group ip address Solarwinds-*************
 security-zone TENANT02_MS_Outside
 0 network host address *************
#
object-group ip address Solarwinds-**********
 0 network subnet ********** *************
#              
object-group ip address Storage_MGMT_***********/24
 0 network subnet *********** *************
#
object-group ip address T1-*************/24
 0 network subnet ************* *************
#
object-group ip address T1-Jiguan-*************/24
 security-zone TENANT02_MS_Outside
 0 network subnet ************* *************
#
object-group ip address T1_********/16
 0 network subnet ******** ***********
#
object-group ip address T1_********
 0 network subnet ******** *************
#
object-group ip address T1_**********/32
 0 network host address **********
#
object-group ip address T1_********73/32
 0 network host address ********73
#
object-group ip address T1_**********/32
 0 network host address **********
#
object-group ip address T1_********/24
 0 network subnet ******** *************
 100 network subnet ********* *************
#
object-group ip address T1_************/24
 0 network subnet ************ *************
#
object-group ip address T1_4.191.79.0/22
 0 network subnet ********** *************
#
object-group ip address T1_CeShi_********
 0 network host address ********
#
object-group ip address T1_harboe_********04
 0 network host address ********04
#
object-group ip address T1_NTP_***********
 0 network host address ***********
#
object-group ip address T1_NTP_***********/32
 0 network host address ***********
#
object-group ip address T4-MS-***********/24
 0 network subnet *********** *************
#
object-group ip address TENANT02_MS_4.190.120.31-32
 security-zone TENANT02_MS_Inside
 0 network range 4.190.120.31 4.190.120.32
#
object-group ip address TENANT02_MS_4.190.120.63/32
 security-zone TENANT02_MS_Inside
 0 network host address 4.190.120.63
#
object-group ip address Test-Baoleiji
 0 network range 4.101.90.10 4.101.90.14
#
object-group ip address Test-Port1-4.189.159.100/32
 0 network host address 4.189.159.100
#
object-group ip address Test-Port2-4.190.79.100/32
 0 network host name 4.190.79.100
#
object-group ip address Test_192.168.214.0/24
 security-zone TENANT02_MS_Outside
 0 network subnet 192.168.214.0 *************
#
object-group ip address TEST_*********/16
 0 network subnet ********* ***********
#
object-group ip address USAP-***********
 security-zone TENANT02_MS_Outside
 0 network host name ***********
#
object-group ip address V3_CORE_4.190.80.0/21
 0 network subnet 4.190.80.0 *************
#
object-group ip address V3_CORE_4.190.80.0/22
 0 network subnet 4.190.80.0 *************
#
object-group ip address V3_CORE_**********01/32
 0 network host address **********01
#
object-group ip address V3_CORE_4.190.80.71
 0 network host address 4.190.80.71
#
object-group ip address V3_CORE_4.190.80.71_73
 0 network group-object V3_CORE_4.190.80.71
 1 network group-object V3_CORE_4.190.80.72
 2 network group-object V3_CORE_4.190.80.73
#
object-group ip address V3_CORE_4.190.80.72
 0 network host address 4.190.80.72
#
object-group ip address V3_CORE_4.190.80.73
 0 network host address 4.190.80.73
#
object-group ip address V3_CORE_4.190.80.81
 0 network host address 4.190.80.81
#
object-group ip address V3_CORE_4.190.80.81_83
 0 network group-object V3_CORE_4.190.80.81
 1 network group-object V3_CORE_4.190.80.82
 2 network group-object V3_CORE_4.190.80.83
#
object-group ip address V3_CORE_4.190.80.82
 0 network host address 4.190.80.82
#
object-group ip address V3_CORE_4.190.80.83
 0 network host address 4.190.80.83
#              
object-group ip address V3_CORE_4.190.81.21
 0 network host address 4.190.81.21
#
object-group ip address V3_CORE_4.190.81.21_25
 0 network group-object V3_CORE_4.190.81.21
 1 network group-object V3_CORE_4.190.81.22
 2 network group-object V3_CORE_4.190.81.23
 3 network group-object V3_CORE_4.190.81.24
 4 network group-object V3_CORE_4.190.81.25
#
object-group ip address V3_CORE_4.190.81.22
 0 network host address 4.190.81.22
#
object-group ip address V3_CORE_4.190.81.23
 0 network host address 4.190.81.23
#
object-group ip address V3_CORE_4.190.81.24
 0 network host address 4.190.81.24
#
object-group ip address V3_CORE_4.190.81.25
 0 network host address 4.190.81.25
#
object-group ip address V3_CORE_CA_4.190.160.1/32
 0 network host address 4.190.160.1
#
object-group ip address V3_CORE_CA_4.190.160.2/32
 0 network host address 4.190.160.2
#
object-group ip address V3_CORE_F5_4.190.162.0/24
 0 network subnet 4.190.162.0 *************
#
object-group ip address V3_CORE_K8SNODE_4.190.84.0/24
 0 network subnet 4.190.84.0 *************
#
object-group ip address V3_CORE_Tidb_Clus_4.190.81.0/24
 0 network subnet 4.190.81.0 *************
#
object-group ip address V3_CORE_TIDB_F5_4.190.162.1
 0 network host address 4.190.162.1
#
object-group ip address V3_CORE_TIDB_F5_4.190.162.2
 0 network host address 4.190.162.2
#
object-group ip address V3_CORE_TIDB_F5_***********
 0 network host address ***********
#              
object-group ip address V3_CORE_TIDB_F5_***********
 0 network host address ***********
#
object-group ip address V3_DMZ_*********/24
 security-zone TENANT02_MS_Outside
 0 network subnet ********* *************
#
object-group ip address V3_DNS_***********/32
 0 network host address ***********
#
object-group ip address V3_DNS_***********/32
 0 network host address ***********
#
object-group ip address V3_ESXI_***********/24
 0 network subnet *********** *************
#
object-group ip address V3_GW_**********/21
 0 network subnet ********** *************
#
object-group ip address V3_GW_K8SNODE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_MS_***********/22
 0 network subnet *********** *************
#
object-group ip address V3_MS_***********01/32
 0 network host address ***********01
#
object-group ip address V3_MS_***********/24
 security-zone TENANT02_MS_Inside
 0 network subnet *********** *************
#
object-group ip address V3_MS_*************/32
 0 network host address *************
#
object-group ip address V3_MS_***********/32
 0 network host address ***********
#
object-group ip address V3_MS_***********1/32
 0 network host address ***********1
#
object-group ip address V3_MS_***********2/32
 0 network host address ***********2
#
object-group ip address V3_MS_F5_***********
 0 network host address ***********
#
object-group ip address V3_MS_Harbor_***********/32
 0 network host address ***********
#
object-group ip address V3_MS_K8SNODE_***********/24
 0 network subnet *********** *************
#
object-group ip address V3_MS_OPS_***********
 security-zone TENANT02_MS_Inside
 0 network host address ***********
#
object-group ip address V3_MS_Tidb_Monitor*************
 0 network host address *************
#
object-group ip address V3_VC_***********
 0 network host address ***********
#
object-group ip address V3MNYY_CORE_**********/24
 0 network subnet ********** *************
#
object-group ip address VC-************
 security-zone TENANT02_MS_Outside
 0 network host address ************
#
object-group ip address VPN-************
 security-zone TENANT02_MS_Outside
 0 network host address ************
#
object-group ip address VPN-***********
 security-zone TENANT02_MS_Outside
 0 network host address ***********
#
object-group ip address VulnerabilityScan-************
 security-zone TENANT02_MS_Outside
 0 network host address ************
#
object-group ip address W5R-*********/24
 0 network subnet ********* *************
#
object-group ip address W5RBOCC
 0 network subnet ********* *************
 10 network subnet ********* *************
#
object-group ip address W5RRDCC-*********-2
 0 network range ********* *********
#              
object-group ip address W5RRMSFSC01-*********
 0 network host address *********
#
object-group ip address YaChe_**********-4
 security-zone TENANT02_MS_Outside
 0 network range ********** **********
#
object-group ip address YIXIANYUNWEI
 0 network subnet ******** *************
 10 network subnet ********* *************
 20 network subnet ********* *************
 30 network subnet ********* *************
 40 network subnet ********** *************
 100 network subnet ********* *************
#
object-group ip address YJ-TS-*************/24
 security-zone TENANT02_MS_Outside
 0 network subnet ************* *************
#
object-group ip address yunyingclient_**********
 0 network subnet ********** *************
#
object-group ip address YZBOCC
 0 network subnet ********** *************
#
object-group ip address YZECC-*********
 0 network subnet ********* *************
#
object-group ip address "ZABBIX SERVER-************-42"
 security-zone TENANT02_MS_Inside
 0 network range ************ ************
#
object-group ip address Zabbix-Proxy-*************
 security-zone TENANT02_MS_Inside
 0 network host address *************
#
object-group ip address Zookeeper
 0 network group-object Zookeeper01_***********
 1 network group-object Zookeeper02_***********
 2 network group-object Zookeeper03_***********
 3 network group-object Zookeeper04_***********
 4 network group-object Zookeeper05_***********
#
object-group ip address Zookeeper01_***********
 0 network host address ***********
#              
object-group ip address Zookeeper02_***********
 0 network host address ***********
#
object-group ip address Zookeeper03_***********
 0 network host address ***********
#
object-group ip address Zookeeper04_***********
 0 network host address ***********
#
object-group ip address Zookeeper05_***********
 0 network host address ***********
#
object-group service TCP-10251-10252
 0 service tcp destination range 10251 10252
#
object-group service TCP-15000
 0 service tcp destination eq 15000
#
object-group service TCP-17778
 0 service tcp destination eq 17778
#
object-group service TCP-19080
 0 service tcp destination eq 19080
#
object-group service TCP-19200
 0 service tcp destination eq 19200
#
object-group service TCP-2154
 0 service tcp destination eq 2154
#
object-group service TCP-2379
 0 service tcp destination eq 2379
#
object-group service TCP-2380
 0 service tcp destination eq 2380
#
object-group service TCP-25
 0 service tcp destination eq 25
#
object-group service TCP-26100
 0 service tcp destination eq 26100
#
object-group service TCP-27001
 0 service tcp destination eq 27001
#
object-group service TCP-28000-30000
 0 service tcp destination range 28000 30000
#
object-group service TCP-28081
 0 service tcp destination eq 28081
#
object-group service TCP-28083
 0 service tcp destination eq 28083
#
object-group service TCP-28090
 0 service tcp destination eq 28090
#
object-group service TCP-28180
 0 service tcp destination eq 28180
#
object-group service TCP-29090
 0 service tcp destination eq 29090
#
object-group service TCP-29091
 0 service tcp destination eq 29091
#
object-group service TCP-29093
 0 service tcp destination eq 29093
#              
object-group service TCP-29200
 0 service tcp destination eq 29200
#
object-group service TCP-29200-29201
 0 service tcp destination range 29200 29201
#
object-group service TCP-29411
 0 service tcp destination eq 29411
#
object-group service TCP-30001
 0 service tcp destination eq 30001
#
object-group service TCP-30020
 0 service tcp destination eq 30020
#
object-group service TCP-30400
 0 service tcp destination eq 30400
#
object-group service TCP-30416
 0 service tcp destination eq 30416
#
object-group service TCP-30600
 0 service tcp destination eq 30600
#
object-group service TCP-31000
 0 service tcp destination eq 31000
#
object-group service TCP-31050-31051
 0 service tcp destination range 31050 31051
#
object-group service TCP-31306
 0 service tcp destination eq 31306
#
object-group service TCP-32000
 0 service tcp destination eq 32000
#
object-group service TCP-32768-65535
 0 service tcp destination gt 32767
#
object-group service TCP-3555
 0 service tcp destination eq 3555
#
object-group service TCP-3557
 0 service tcp destination eq 3557
#
object-group service TCP-3558
 0 service tcp destination eq 3558
#
object-group service TCP-38082
 0 service tcp destination eq 38082
#
object-group service TCP-4100
 0 service tcp destination eq 4100
#
object-group service TCP-4100-4130
 0 service tcp destination range 4100 4130
#
object-group service TCP-4343
 0 service tcp destination eq 4343
#
object-group service TCP-5000
 0 service tcp destination eq 5000
#
object-group service TCP-5000-5011
 0 service tcp destination range 5000 5011
#
object-group service TCP-5000-5030
 0 service tcp destination range 5000 5030
#              
object-group service TCP-5000_5007
 0 service tcp destination range 5000 5007
#
object-group service TCP-5001-5030
 0 service tcp destination range 5001 5030
#
object-group service TCP-5003
 0 service tcp destination eq 5003
#
object-group service TCP-5044
 0 service tcp destination eq 5044
#
object-group service TCP-51153
 0 service tcp destination eq 51153
#
object-group service TCP-514
 0 service tcp destination eq 514
#
object-group service TCP-5672
 0 service tcp destination eq 5672
#
object-group service TCP-5988-5989
 0 service tcp destination range 5988 5989
#
object-group service TCP-6370
 0 service tcp destination eq 6370
#
object-group service TCP-6379
 0 service tcp destination eq 6379
#
object-group service TCP-6443
 0 service tcp destination eq 6443
#
object-group service TCP-8000-8010
 0 service tcp destination range 8000 8010
#
object-group service TCP-8004
 0 service tcp destination eq 8004
#
object-group service TCP-8011
 0 service tcp destination eq 8011
#
object-group service TCP-8013
 0 service tcp destination eq 8013
#
object-group service TCP-8018
 0 service tcp destination eq 8018
#
object-group service TCP-8022
 0 service tcp destination eq 8022
#
object-group service TCP-8080
 0 service tcp destination eq 8080
#
object-group service TCP-8088
 0 service tcp destination eq 8088
#
object-group service TCP-8089
 0 service tcp destination eq 8089
#
object-group service TCP-8090
 0 service tcp destination eq 8090
#
object-group service TCP-8091
 0 service tcp destination eq 8091
#
object-group service TCP-8182
 0 service tcp destination eq 8182
#              
object-group service TCP-8249
 0 service tcp destination eq 8249
#
object-group service TCP-8250
 0 service tcp destination eq 8250
#
object-group service TCP-8400
 0 service tcp destination eq 8400
#
object-group service TCP-8400-8900
 0 service tcp destination range 8400 8900
#
object-group service TCP-8888
 0 service tcp destination eq 8888
#
object-group service TCP-8889
 0 service tcp destination eq 8889
#
object-group service tcp-9044
 description tcp-9044
 0 service tcp destination eq 9044
#
object-group service TCP-9090
 0 service tcp destination eq 9090
#
object-group service TCP-9092
 0 service tcp destination eq 9092
#
object-group service TCP-9100
 0 service tcp destination eq 9100
#
object-group service TCP-9100-20182
 0 service tcp destination range 9100 20182
#
object-group service TCP-9600
 0 service tcp destination eq 9600
#
object-group service TCP/UDP-111
 0 service tcp destination eq 111
 10 service udp destination eq 111
#
object-group service TCP/UDP-2049
 0 service tcp destination eq 2049
 10 service udp destination eq 2049
#
object-group service TCP_10098-10099
 0 service tcp destination range 10098 10099
#
object-group service TCP_10250
 0 service tcp destination eq 10250
#
object-group service TCP_10255
 0 service tcp destination eq 10255
#
object-group service TCP_10256
 0 service tcp destination eq 10256
#
object-group service TCP_12049
 0 service tcp destination eq 12049
#
object-group service TCP_19200
 0 service tcp destination eq 19200
#
object-group service TCP_19300
 0 service tcp destination eq 19300
#
object-group service TCP_23000
 0 service tcp destination eq 23000
#              
object-group service TCP_2379
 0 service tcp destination eq 2379
#
object-group service TCP_25061
 0 service tcp destination eq 25061
#
object-group service TCP_25601
 0 service tcp destination eq 25601
#
object-group service TCP_28070
 0 service tcp destination eq 28070
#
object-group service TCP_28080
 0 service tcp destination eq 28080
#
object-group service TCP_28081
 0 service tcp destination eq 28081
#
object-group service TCP_28088
 0 service tcp destination eq 28088
#
object-group service TCP_28180
 0 service tcp destination eq 28180
#
object-group service TCP_29000-29999
 0 service tcp destination range 29000 29999
#
object-group service TCP_29090
 0 service tcp destination eq 29090
#
object-group service TCP_29092
 0 service tcp destination eq 29092
#
object-group service TCP_29093
 0 service tcp destination eq 29093
#
object-group service TCP_29201
 0 service tcp destination eq 29201
#
object-group service TCP_29300
 0 service tcp destination eq 29300
#
object-group service TCP_29301
 0 service tcp destination eq 29301
#
object-group service TCP_29411
 0 service tcp destination eq 29411
#
object-group service TCP_3000
 0 service tcp destination eq 3000
#
object-group service TCP_31050-31051
 0 service tcp destination range 31050 31051
#
object-group service TCP_31306
 0 service tcp destination eq 31306
#
object-group service TCP_3191
 0 service tcp destination eq 3191
#
object-group service TCP_3268
 0 service tcp destination eq 3268
#
object-group service TCP_3389
 0 service tcp destination eq 3389
#
object-group service TCP_3555
 0 service tcp destination eq 3555
#              
object-group service TCP_3558
 0 service tcp destination eq 3558
#
object-group service TCP_389
 0 service tcp destination eq 389
#
object-group service TCP_5000
 0 service tcp destination eq 5000
#
object-group service TCP_5004
 0 service tcp destination eq 5004
#
object-group service TCP_5008
 0 service tcp destination eq 5008
#
object-group service TCP_5480
 0 service tcp destination eq 5480
#
object-group service TCP_6370
 0 service tcp destination eq 6370
#
object-group service TCP_7001
 0 service tcp destination eq 7001
#
object-group service TCP_7100
 0 service tcp destination eq 7100
#
object-group service TCP_8000
 0 service tcp destination eq 8000
#
object-group service TCP_8022
 0 service tcp destination eq 8022
#
object-group service TCP_8080
 0 service tcp destination eq 8080
#
object-group service TCP_8082
 0 service tcp destination eq 8082
#
object-group service TCP_8086
 0 service tcp destination eq 8086
#
object-group service TCP_8088
 0 service tcp destination eq 8088
#
object-group service TCP_8112
 0 service tcp destination eq 8112
#
object-group service TCP_8400
 0 service tcp destination eq 8400
#
object-group service TCP_902
 0 service tcp destination eq 902
#
object-group service TCP_9090-9091
 0 service tcp destination range 9090 9091
#
object-group service TCP_9100
 0 service tcp destination eq 9100
#
object-group service TCP_9443
 0 service tcp destination eq 9443
#
object-group service UDP-1812
 0 service udp destination eq 1812
#
object-group service UDP-2154
 0 service tcp destination eq 2154
#              
object-group service UDP-514
 0 service tcp destination eq 514
#
object-group service UDP-8182
 0 service tcp destination eq 8182
#
object-group service UDP-8514
 0 service udp
#
object-group service UDP_123
 0 service udp destination eq 123
#
object-group service UDP_8472
 0 service udp destination eq 8472
#
object-group service UDP_902
 0 service udp destination eq 902
#
interface Bridge-Aggregation1
 description "TO-XWHPD-NE3ACL-SW01 Agg1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 link-aggregation mode dynamic
#
interface Bridge-Aggregation3
 description "TO-XWHPD-NE3DIL-SW01 Agg2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 link-aggregation mode dynamic
#
interface Route-Aggregation100
 description IRF-BFD_MAD
 mad bfd enable
 mad ip address ************* *************** member 1
 mad ip address ************* *************** member 2
#
interface NULL0
#
interface GigabitEthernet1/0/1/1
 port link-mode route
 description "TO-XWHPD-NE3DIL-FW01 G2/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/0/1/2
 port link-mode route
#
interface GigabitEthernet1/0/1/3
 port link-mode route
#
interface GigabitEthernet1/0/1/4
 port link-mode route
#
interface GigabitEthernet1/9/1/1
 port link-mode route
 description "TO-XWHPD-NE3DIL-FW01 G2/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/9/1/2
 port link-mode route
#
interface GigabitEthernet1/9/1/3
 port link-mode route
#
interface GigabitEthernet1/9/1/4
 port link-mode route
#
interface GigabitEthernet2/0/1/1
 port link-mode route
 description "TO-XWHPD-NE3DIL-FW01 G1/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/0/1/2
 port link-mode route
#
interface GigabitEthernet2/0/1/3
 port link-mode route
#
interface GigabitEthernet2/0/1/4
 port link-mode route
#
interface GigabitEthernet2/9/1/1
 port link-mode route
 description "TO-XWHPD-NE3DIL-FW01 G1/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/9/1/2
 port link-mode route
#
interface GigabitEthernet2/9/1/3
 port link-mode route
#
interface GigabitEthernet2/9/1/4
 port link-mode route
#
interface M-GigabitEthernet1/0/0/0
 ip binding vpn-instance management
 ip address ********** *************
#
interface Ten-GigabitEthernet1/0/1/5
 port link-mode route
#
interface Ten-GigabitEthernet1/9/1/5
 port link-mode route
#
interface Ten-GigabitEthernet2/0/1/5
 port link-mode route
#
interface Ten-GigabitEthernet2/9/1/5
 port link-mode route
#
interface Ten-GigabitEthernet1/0/1/6
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T1/2/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/7
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T1/2/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/8
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T1/2/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/9
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T1/0/1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/10
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T1/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/11
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T1/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/6
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T1/3/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/7
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T1/3/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/8
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T1/3/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#              
interface Ten-GigabitEthernet1/9/1/9
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T1/0/5"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/10
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T1/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/11
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T1/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/6
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T2/2/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/7
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T2/2/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/8
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T2/2/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/9
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T2/0/1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/10
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T2/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/11
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T2/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/6
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T2/3/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/7
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T2/3/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/8
 port link-mode bridge
 description "TO-XWHPD-NE3DIL-SW01 T2/3/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/9
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T2/0/5"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/10
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T2/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/11
 port link-mode bridge
 description "TO-XWHPD-NE3ACL-SW01 T2/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3011 to 3015
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/12
 description "TO-XWHPD-NE3DIL-FW01 T2/0/1/12 IRF"
#
interface Ten-GigabitEthernet1/9/1/12
 description "TO-XWHPD-NE3DIL-FW01 T2/9/1/12 IRF"
#
interface Ten-GigabitEthernet2/0/1/12
 description "TO-XWHPD-NE3DIL-FW01 T1/0/1/12 IRF"
#
interface Ten-GigabitEthernet2/9/1/12
 description "TO-XWHPD-NE3DIL-FW01 T1/9/1/12 IRF"
#
interface Blade1/6/0/1
#
interface Blade1/6/0/2
#
interface Blade2/6/0/1
#              
interface Blade2/6/0/2
#
interface Blade-Aggregation1
 link-aggregation blade Blade4fw
#
interface Blade-Aggregation257
#
security-zone name Local
#
security-zone name Trust
 import interface Route-Aggregation100
#
security-zone name DMZ
#
security-zone name Untrust
#
security-zone name Management
 import interface M-GigabitEthernet1/0/0/0
#
security-zone name TENANT01_MS_Inside
 import interface Bridge-Aggregation1 vlan 3011
#
security-zone name TENANT01_MS_Outside
 import interface Bridge-Aggregation3 vlan 3011
#
security-zone name TENANT02_MS_Inside
 import interface Bridge-Aggregation1 vlan 3012
#
security-zone name TENANT02_MS_Outside
 import interface Bridge-Aggregation3 vlan 3012
#
security-zone name TENANT03_MS_Inside
 import interface Bridge-Aggregation1 vlan 3013
#
security-zone name TENANT03_MS_Outside
 import interface Bridge-Aggregation3 vlan 3013
#
security-zone name TENANT04_MS_Inside
 import interface Bridge-Aggregation1 vlan 3014
#
security-zone name TENANT04_MS_Outside
 import interface Bridge-Aggregation3 vlan 3014
#
security-zone name TENANT05_MS_Inside
 import interface Bridge-Aggregation1 vlan 3015
#              
security-zone name TENANT05_MS_Outside
 import interface Bridge-Aggregation3 vlan 3015
#
zone-pair security source Local destination Trust
 packet-filter 2000
#
zone-pair security source Trust destination Local
 packet-filter 2000
#
 scheduler logfile size 16
#
line class console
 user-role network-admin
#
line class vty
 user-role network-operator
#
line con 1/0 1/1
 authentication-mode scheme
 user-role network-admin
#
line con 2/0 2/1
 user-role network-admin
#
line con 1/6
 authentication-mode scheme
 user-role network-admin
#
line con 2/6
 user-role network-admin
#
line vty 0 63
 authentication-mode scheme
 user-role network-admin
#
 ip route-static vpn-instance management 0.0.0.0 0 ***********
#
 info-center timestamp loghost iso
 info-center loghost source M-GigabitEthernet1/0/0/0
 info-center loghost vpn-instance MGMT ***********
 info-center loghost vpn-instance management **********
 info-center loghost vpn-instance MGMT **********3
 info-center loghost vpn-instance MGMT ************
 info-center loghost vpn-instance MGMT ************
 info-center loghost vpn-instance MGMT *************
#              
 mad exclude interface M-GigabitEthernet1/0/0/0
#
 snmp-agent
 snmp-agent local-engineid 800063A280542BDE6420E300000001
 snmp-agent community read cipher $c$3$BcORv7EBgNI+CS5fpRzg5gh1wZoea887nkcCh35y acl name ACL-SNMP
 snmp-agent sys-info location fw01-w5r-D-17-3-20&D-18-3-20
 snmp-agent sys-info version v2c v3 
 snmp-agent target-host trap address udp-domain ************ vpn-instance MGMT params securityname cslc_snmp v2c
 snmp-agent target-host trap address udp-domain ************* vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain *********** params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain ********** vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent trap enable arp 
 snmp-agent trap enable radius 
 snmp-agent trap enable stp 
 snmp-agent trap enable syslog 
 snmp-agent trap source M-GigabitEthernet1/0/0/0
#
 ssh server enable
#
redundancy group 1
 member failover group 1
 node 1
  bind chassis 1
  priority 100
  track 101 interface Blade1/6/0/1
  track 102 interface Blade1/6/0/2
 node 2
  bind chassis 2
  priority 50
  track 201 interface Blade2/6/0/1
  track 202 interface Blade2/6/0/2
#
 ntp-service enable
 ntp-service unicast-server ********** vpn-instance management source M-GigabitEthernet1/0/0/0
#
acl basic 2000
 rule 0 permit source *********** *********
#
acl basic name ACL-SNMP
 description "Network monitor system"
 rule 0 permit vpn-instance management source ********** *********
 rule 5 permit vpn-instance management source ************* 0
 rule 10 permit vpn-instance management source *********** *********
 rule 15 permit vpn-instance management source *********** 0
 rule 20 permit vpn-instance MGMT source ************ 0
 rule 1000 deny vpn-instance management
#
domain system
#
 domain default enable system
#
role name level-0
 description Predefined level-0 role
#
role name level-1
 description Predefined level-1 role
#
role name level-2
 description Predefined level-2 role
#
role name level-3
 description Predefined level-3 role
#
role name level-4
 description Predefined level-4 role
#
role name level-5
 description Predefined level-5 role
#              
role name level-6
 description Predefined level-6 role
#
role name level-7
 description Predefined level-7 role
#
role name level-8
 description Predefined level-8 role
#
role name level-9
 description Predefined level-9 role
#
role name level-10
 description Predefined level-10 role
#
role name level-11
 description Predefined level-11 role
#
role name level-12
 description Predefined level-12 role
#
role name level-13
 description Predefined level-13 role
#
role name level-14
 description Predefined level-14 role
#
role name systemadmin
 rule 1 permit read web-menu m_monitor/m_atklog/m_blacklistlog
 rule 2 permit read web-menu m_monitor/m_atklog/m_singleatk
 rule 3 permit read web-menu m_monitor/m_atklog/m_scanatk
 rule 4 permit read web-menu m_monitor/m_atklog/m_floodatk
 rule 5 permit read web-menu m_monitor/m_atklog/m_threatlog
 rule 6 permit read web-menu m_monitor/m_atklog/m_urllog
 rule 7 permit read web-menu m_monitor/m_atklog/m_filefilterlog
 rule 8 permit read web-menu m_monitor/m_atklog/m_zonepairlog
 rule 9 permit read web-menu m_monitor/m_auditlogs/m_auditimchatlog
 rule 10 permit read web-menu m_monitor/m_auditlogs/m_auditcommunitylog
 rule 11 permit read web-menu m_monitor/m_auditlogs/m_auditsearchenginelog
 rule 12 permit read web-menu m_monitor/m_auditlogs/m_auditmaillog
 rule 13 permit read web-menu m_monitor/m_auditlogs/m_auditfiletransferlog
 rule 14 permit read web-menu m_monitor/m_auditlogs/m_auditrelaxstocklog
 rule 15 permit read web-menu m_monitor/m_auditlogs/m_auditotherapplog
 rule 16 permit read web-menu m_monitor/m_monitorlog/m_trafficlog
 rule 17 permit read web-menu m_monitor/m_rank/m_trafficrank
 rule 18 permit read web-menu m_monitor/m_rank/m_threadrank
 rule 19 permit read web-menu m_monitor/m_rank/m_urlfilterrank
 rule 20 permit read web-menu m_monitor/m_rank/m_ffilterrank
 rule 21 permit read web-menu m_monitor/m_rank/m_securityaudit
 rule 22 permit read web-menu m_monitor/m_rank/m_lb_serverreport
 rule 23 permit read web-menu m_monitor/m_rank/m_lb_linkreport
 rule 24 permit read web-menu m_monitor/m_rank/m_lb_dnsproxyreport
 rule 25 permit read web-menu m_monitor/m_trend/m_traffictrend
 rule 26 permit read web-menu m_monitor/m_trend/m_threadtrend
 rule 27 permit read web-menu m_monitor/m_trend/m_urlfiltertrend
 rule 28 permit read web-menu m_monitor/m_trend/m_ffiltertrend
 rule 29 permit read web-menu m_monitor/m_trend/m_lb_urltrend
 rule 30 permit read web-menu m_monitor/m_report
 rule 31 permit read web-menu m_monitor/m_session
 rule 32 permit read web-menu m_monitor/m_lb_dnscaches
 rule 33 permit read web-menu m_monitor/m_userinfocenter
 rule 34 permit read web-menu m_policy/m_firewall/m_secpolicy
 rule 35 permit read web-menu m_policy/m_firewall/m_redundancyrules
 rule 36 permit read web-menu m_policy/m_firewall/m_targetpolicy
 rule 37 permit read web-menu m_policy/m_attackdefense/m_atkpolicy
 rule 38 permit read web-menu m_policy/m_attackdefense/m_clientverifyprotectip
 rule 39 permit read web-menu m_policy/m_attackdefense/m_blacklistmanual
 rule 40 permit read web-menu m_policy/m_attackdefense/m_whitelistmanual
 rule 41 permit read web-menu m_policy/m_attackdefense/m_clientverifyzone
 rule 42 permit read web-menu m_policy/m_attackdefense/m_connlimitpolicies
 rule 43 permit read web-menu m_policy/m_attackdefense/m_urpf
 rule 44 permit read web-menu m_policy/m_nat/m_natoutboundconfig
 rule 45 permit read web-menu m_policy/m_nat/m_natserverconfig
 rule 46 permit read web-menu m_policy/m_nat/m_natstaticchange
 rule 47 permit read web-menu m_policy/m_nat/m_natoutbound444config
 rule 48 permit read web-menu m_policy/m_nat/m_natoutboundstatic444config
 rule 49 permit read web-menu m_policy/m_nat/m_natsettings
 rule 50 permit read web-menu m_policy/m_aft/m_aftaddrgrp
 rule 51 permit read web-menu m_policy/m_aft/m_aftnat64
 rule 52 permit read web-menu m_policy/m_aft/m_aftoutbound
 rule 53 permit read web-menu m_policy/m_aft/m_aftset
 rule 54 permit read web-menu m_policy/m_appaudit/m_auditpolicy
 rule 55 permit read web-menu m_policy/m_appaudit/m_keywordgroups
 rule 56 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthpolicy
 rule 57 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthchannel
 rule 58 permit read web-menu m_policy/m_bandwidthmanagement/m_interfacebandwidth
 rule 59 permit read web-menu m_policy/m_loadbalance/m_lb_globalconfig
 rule 60 permit read web-menu m_policy/m_loadbalance/m_lb_server
 rule 61 permit read web-menu m_policy/m_loadbalance/m_lb_link
 rule 62 permit read web-menu m_policy/m_netshare/m_netsharepolicy
 rule 63 permit read web-menu m_policy/m_netshare/m_netsharestatus
 rule 64 permit read web-menu m_policy/m_proxymanagement/m_proxypolicy
 rule 65 permit read web-menu m_policy/m_proxymanagement/m_whitelisthostname
 rule 66 permit read web-menu m_policy/m_proxymanagement/m_sslcertificate
 rule 67 permit read web-menu m_resource/m_healthmonitor
 rule 68 permit read web-menu m_resource/m_user/m_usercontrol
 rule 69 permit read web-menu m_resource/m_user/m_authentication
 rule 70 permit read web-menu m_resource/m_user/m_access
 rule 71 permit read web-menu m_resource/m_dpi/m_ipscfg
 rule 72 permit read web-menu m_resource/m_dpi/m_antiviruscfg
 rule 73 permit read web-menu m_resource/m_dpi/m_dfltcfg
 rule 74 permit read web-menu m_resource/m_dpi/m_ufltcfg
 rule 75 permit read web-menu m_resource/m_dpi/m_ffltcfg
 rule 76 permit read web-menu m_resource/m_dpi/m_apprecognition
 rule 77 permit read web-menu m_resource/m_dpi/m_securityaction
 rule 78 permit read web-menu m_resource/m_dpi/m_dpicfg
 rule 79 permit read web-menu m_resource/m_objectgroup/m_ipv4objectgroup
 rule 80 permit read web-menu m_resource/m_objectgroup/m_ipv6objectgroup
 rule 81 permit read web-menu m_resource/m_objectgroup/m_macobjectgroup
 rule 82 permit read web-menu m_resource/m_objectgroup/m_serviceobjectgroup
 rule 83 permit read web-menu m_resource/m_objectgroup/m_timerange
 rule 84 permit read web-menu m_resource/m_acl/m_ipv4acl
 rule 85 permit read web-menu m_resource/m_acl/m_ipv6acl
 rule 86 permit read web-menu m_resource/m_acl/m_macacl
 rule 87 permit read web-menu m_resource/m_ssl/m_sslserver
 rule 88 permit read web-menu m_resource/m_ssl/m_sslclient
 rule 89 permit read web-menu m_resource/m_ssl/m_ssladvancesettiing
 rule 90 permit read web-menu m_resource/m_publickey/m_publickeylocal
 rule 91 permit read web-menu m_resource/m_publickey/m_publickeypeer
 rule 92 permit read web-menu m_resource/m_pki_cert/m_pki
 rule 93 permit read web-menu m_resource/m_pki_cert/m_certificatepolicy
 rule 94 permit read web-menu m_resource/m_pki_cert/m_certificatesubject
 rule 95 permit read web-menu m_network/m_vrf
 rule 96 permit read web-menu m_network/m_if/m_interface
 rule 97 permit read web-menu m_network/m_if/m_inlineall
 rule 98 permit read web-menu m_network/m_if/m_lagg
 rule 99 permit read web-menu m_network/m_seczone
 rule 100 permit read web-menu m_network/m_link/m_vlan
 rule 101 permit read web-menu m_network/m_link/m_mac_sum
 rule 102 permit read web-menu m_network/m_dns_sum/m_dnshosts
 rule 103 permit read web-menu m_network/m_dns_sum/m_dns
 rule 104 permit read web-menu m_network/m_dns_sum/m_ddns
 rule 105 permit read web-menu m_network/m_dns_sum/m_dnsadvance
 rule 106 permit read web-menu m_network/m_ip_net/m_ip
 rule 107 permit read web-menu m_network/m_ip_net/m_arp
 rule 108 permit read web-menu m_network/m_ipv6_net/m_ipv6
 rule 109 permit read web-menu m_network/m_ipv6_net/m_nd
 rule 110 permit read web-menu m_network/m_vpn/m_gre
 rule 111 permit read web-menu m_network/m_vpn/m_ipsec
 rule 112 permit read web-menu m_network/m_vpn/m_advpn
 rule 113 permit read web-menu m_network/m_vpn/m_l2tp
 rule 114 permit read web-menu m_network/m_sslvpn/m_sslvpn_context
 rule 115 permit read web-menu m_network/m_sslvpn/m_sslvpn_gateway
 rule 116 permit read web-menu m_network/m_sslvpn/m_sslvpn_ipv4addrpool
 rule 117 permit read web-menu m_network/m_sslvpn/m_sslvpn_snatpool
 rule 118 permit read web-menu m_network/m_sslvpn/m_sslvpn_acif
 rule 119 permit read web-menu m_network/m_sslvpn/m_sslvpn_globalconfig
 rule 120 permit read web-menu m_network/m_sslvpn/m_sslvpn_tempmanagement
 rule 121 permit read web-menu m_network/m_sslvpn/m_sslvpn_statistics
 rule 122 permit read web-menu m_network/m_routing/m_routingtable
 rule 123 permit read web-menu m_network/m_routing/m_staticrouting
 rule 124 permit read web-menu m_network/m_routing/m_policyrouting
 rule 125 permit read web-menu m_network/m_routing/m_ospf
 rule 126 permit read web-menu m_network/m_routing/m_bgp
 rule 127 permit read web-menu m_network/m_routing/m_rip
 rule 128 permit read web-menu m_network/m_multicast/m_multicastrouting
 rule 129 permit read web-menu m_network/m_multicast/m_pim
 rule 130 permit read web-menu m_network/m_multicast/m_igmp
 rule 131 permit read web-menu m_network/m_dhcp/m_dhcpservice
 rule 132 permit read web-menu m_network/m_dhcp/m_dhcppool
 rule 133 permit read web-menu m_network/m_ipservice/m_ssh
 rule 134 permit read web-menu m_network/m_ipservice/m_ntp
 rule 135 permit read web-menu m_network/m_ipservice/m_ftp
 rule 136 permit read web-menu m_network/m_ipservice/m_telnet
 rule 137 permit read web-menu m_device/m_diagnosis/m_ping
 rule 138 permit read web-menu m_device/m_diagnosis/m_tracert
#
user-group system
#
local-user admin class manage
 password hash $h$6$W0SoD12fljOjH2jn$4U7WfVXfM3pbt47v79PSlrX3lHHle2eR9QjIWSPy6QduzmQA9dsE4m1xeEmReZs4oBq9DsfsSUi8qhRQyTumUw==
 service-type ssh terminal https
 authorization-attribute user-role level-3
 authorization-attribute user-role network-admin
 authorization-attribute user-role network-operator
#
local-user ftp class manage
 password hash $h$6$wl+6IzPqHJfwvMAT$cCWJBHDm+mb17Eh8M0BsW91jnMCWIwFD3ntzcQVVWHREhK2FFzeP1IVD+kZxc1NAK1BD3Ga23ZrzTBmrc8P31A==
 service-type ftp
 authorization-attribute user-role network-admin
#
local-user operator class manage
 password hash $h$6$2QwrwNcPdlZaP3dh$bwoLiE78K7t76PGXemJoGw0U9+/x/50f69T1b3vqIdCEDMrMP8ZR4HVaEeM1vO027gyT9bZfdcfdjjcJ8W7JJw==
 service-type ssh terminal https
 authorization-attribute user-role level-1
 authorization-attribute user-role systemadmin
#
 ftp server enable
#
 session statistics enable
 session synchronization enable 
#
 ip https enable
#
 inspect optimization no-acsignature disable
 inspect optimization raw disable
 inspect optimization uncompress disable
 inspect optimization url-normalization disable
 inspect optimization chunk disable
#
security-policy ip
 rule 225 name XIUSHI
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip ***********-91
  destination-ip ************
  destination-ip AlarmManager_4.190.121.71-72
  service TCP-29093
 rule 226 name XIUSHI2
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip ***********-91
  destination-ip *************
  destination-ip ************
  service TCP_31306
 rule 227 name XIUSHI3
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip OPS
  destination-ip ***********-91
  destination-ip ***********-14
  service ssh
 rule 191 name YZBOCC_Deny_HTTPS
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip YZBOCC
  source-ip YZECC-*********
  destination-ip G3_MS_VC_***********/32
  service https
 rule 68 name JiGuan_to_V3
  action pass
  counting enable
  source-ip JIGUAN_***************
  destination-ip HARBOR01_4.190.120.31
  destination-ip HARBOR02_4.190.120.32
  service http
  service https
 rule 97 name 4.190.101_to_***********
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********01/32
  destination-ip DMZ_Zabbix-Proxy-***********
  service ssh
 rule 2 name 1
  description V3-11
  action pass
  source-ip *********/16
  destination-ip *********/16
  service ssh
 rule 16 name 2
  description V3-3
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  source-ip V3_MS_***********/22
  destination-ip Zookeeper
  service TCP_3191
 rule 17 name 3
  description V3-4
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip Redis_oltp
  destination-ip Redis_feioltp
  service TCP_7001
 rule 18 name 4
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip V3_GW_K8SNODE_**********/24
  service UDP_8472
 rule 19 name 5
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  source-ip JianKong_***********
  destination-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip V3_GW_K8SNODE_**********/24
  service TCP_12049
  service TCP_9100
 rule 20 name 6
  description V3-20
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_Tidb_Clus_4.190.81.0/24
  service TCP_31306
  service ssh  
 rule 21 name 7
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip nginx02_************/32
  destination-ip nginx01_************/32
  service http
 rule 23 name 8
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********2/32
  source-ip V3_MS_***********1/32
  destination-ip V3_CORE_4.190.80.81_83
  destination-ip V3_CORE_4.190.81.21_25
  destination-ip V3_CORE_F5_4.190.162.0/24
  service TCP_31306
 rule 24 name 9
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********2/32
  source-ip V3_MS_***********1/32
  destination-ip V3_CORE_**********01/32
  service TCP_3555
 rule 28 name 10
  description V3-21
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC
  source-ip 4A-*********
  source-ip YJ-TS-*************/24
  source-ip CSLC-baoleiji-*********
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip *********/16
  service TCP_8088
  service TCP_31306
  service http
  service https
  service ssh
  service TCP_3558
  service TCP_3555
  service TCP-31050-31051
  service TCP-8022
  service TCP-9600
  service TCP_31050-31051
  service TCP_3389
  service TCP_5480
  service TCP_8080
  service TCP_8086
  service TCP_9443
  service TCP_25601
  service TCP_23000
  service TCP-31306
  service TCP-8000-8010
  service TCP_8082
  service TCP-28000-30000
  service TCP-27001
  service TCP-5000
  service TCP_28070
  service TCP_28081
  service TCP_19200
  service TCP_19300
  service TCP_29201
  service TCP-29200
  service TCP_29300
  service TCP_29301
  service TCP-8091
 rule 41 name 11
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_CORE_Tidb_Clus_4.190.81.0/24
  destination-ip V3_MS_Tidb_Monitor*************
  destination-ip V3_MS_*************/32
 rule 42 name 12
  description V3-1
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip HARBOR
  destination-ip OPS
  destination-ip V3_MS_Harbor_***********/32
  destination-ip V3_MS_OPS_***********
  service http
  service https
 rule 43 name 13
  description V3-5
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip Configcenter
  service TCP_28081
 rule 44 name 14
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip V3_MS_K8SNODE_***********/24
  service TCP_10250
  service TCP_10255
  service TCP_8080
  service TCP_2379
  service TCP_10256
 rule 45 name 15
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip V3_MS_K8SNODE_***********/24
  service UDP_8472
 rule 46 name 16
  description V3-24
  action pass
  logging enable
  counting enable
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_ESXI_***********/24
  source-ip G2-ESXI-**********/16
  destination-ip MS_***********/22
  service TCP_902
  service UDP_902
  service TCP-8182
  service UDP-8182
 rule 47 name 17
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_CORE_4.190.80.0/21
  source-ip V3_GW_**********/21
  destination-ip V3_MS_***********/22
  service TCP_28070
 rule 51 name V3-2
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip HARBOR
  source-ip harbor_F5_***********
  source-ip OPS
  source-ip V3_MS_OPS_***********
  service http
  service https
 rule 52 name V3-13
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_CORE_4.190.80.0/21
  destination-ip V3_MS_***********/22
  service TCP_31306
  service ssh
 rule 53 name V3-14
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********/22
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_4.190.80.0/21
  destination-ip ***********
  service TCP_31306
  service ssh
  service TCP_28081
  service TCP_28070
 rule 54 name V3-18
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G2OCS-**********
  source-ip **********-162
  destination-ip *********/16
  service TCP_31306
  service TCP_3555
  service TCP_3558
  service ssh
 rule 55 name V3-25
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip *********/16
  destination-ip T1_********73/32
  destination-ip T1_NTP_***********
  service ntp
  service http
 rule 56 name V3-26
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ***********
  destination-ip V3_GW_K8SNODE_**********/24
  service TCP_8086
 rule 57 name V3-27
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip ***********
  service TCP_8086
 rule 58 name V3-29
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip "GW ZABBIX PROXY-************"
  source-ip DMZ_Zabbix-Proxy-***********
  destination-ip "ZABBIX SERVER-************-42"
  destination-ip ************
  service TCP_31050-31051
 rule 59 name V3-30
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip "ZABBIX SERVER-************-42"
  source-ip ************
  destination-ip "GW ZABBIX PROXY-************"
  destination-ip V3_DMZ_*********/24
  service TCP_31050-31051
 rule 60 name V3-31
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip "CORE ZABBIX PROXY-***********1"
  destination-ip "ZABBIX SERVER-************-42"
  destination-ip ************
  service TCP_31050-31051
 rule 61 name V3-32
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip "ZABBIX SERVER-************-42"
  source-ip ************
  destination-ip "CORE ZABBIX PROXY-***********1"
  service TCP_31050-31051
 rule 66 name V3-33
  description 

  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G3OPERVM
  destination-ip *********/16
  service TCP_2379
  service TCP_3191
  service TCP_3555
  service TCP_3558
  service TCP_6370
  service TCP_7001
  service TCP_8080
  service UDP_8472
  service http
  service ssh
  service TCP-5003
  service TCP_31306
  service TCP-10251-10252
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service TCP_7100
  service https
 rule 62 name V3-41
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********/22
  destination-ip G2DC
  service TCP_389
  service TCP_3268
 rule 63 name V3-42
  action pass
  counting enable
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_MS_***********/22
  destination-ip ************
  service TCP_28080
  service https
 rule 64 name V3-43
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC
  source-ip CSLC-baoleiji-*********
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip V3_MS_***********/22
  destination-ip ************
  destination-ip ************
  service TCP_28088
  service TCP-28180
  service https
 rule 65 name V3-44
  action pass  
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip *********/16
  destination-ip V3_MS_***********/22
  service TCP_29092
  service TCP-6443
 rule 75 name V3-45
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********/22
  destination-ip V3_GW_**********/21
  destination-ip V3_CORE_4.190.80.0/22
  service TCP-29090
 rule 81 name V3-46
  action pass
  logging enable
  counting enable
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip MGMT_*********/16
  destination-ip ************
  destination-ip V3_MS_***********/22
  service TCP-514
  service UDP-514
  service syslog
 rule 84 name V3-47
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_**********/21
  destination-ip V3_CORE_4.190.80.0/21
  service TCP-9100
  service TCP_3191
  service TCP-5000_5007
  service TCP_5008
  service TCP-4100
 rule 85 name V3-48
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip ************
  destination-ip V3_MS_***********/24
  service TCP_28088
  service https
 rule 88 name V3-49
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip redis-**********-9
  destination-ip Redis_feioltp
  destination-ip Redis_oltp
  service TCP_7001
 rule 89 name V3-50
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_4.190.80.0/22
  destination-ip ***********
  service TCP_28070
 rule 90 name V3-51
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********/22
  destination-ip Storage_MGMT_***********/24
  destination-ip T1_********/16
  service TCP_8088
  service ssh
 rule 91 name V3-52
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Storage_MGMT_***********/24
  source-ip T1_********/16
  destination-ip MS_***********/22
  service TCP_8088
  service ssh
 rule 92 name V3-53
  description Prometheus

  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_K8SNODE_**********/24
  service TCP_29000-29999
 rule 93 name V3-54
  action pass  
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip AlarmManager_4.190.121.71-72
  source-ip OPSTOOL_*************-182
  destination-ip V3_DMZ_*********/24
  service smtp
 rule 94 name V3-55
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip Zabbix-Proxy-*************
  destination-ip DES-**********-18
  service TCP-8018
 rule 95 name V3-56
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS_***********/22
  destination-ip *********-2
  destination-ip DMZ_Zabbix-Proxy-***********
  service TCP-31050-31051
  service TCP-31306
 rule 96 name V3-57
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS-*************
  destination-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_4.190.80.0/22
  service TCP-30600
  service TCP-6370
 rule 98 name V3-58
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip *******
  destination-ip T1_********73/32
  destination-ip T1_NTP_***********
  service http
  service ntp
 rule 102 name V3-59
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_4.190.80.0/22
  service TCP-3558
  service TCP-31306
  service TCP-3555
 rule 50 name "Permit OSPF&Ping"
  description V3-12
  action pass
  service ospf
  service ping
 rule 22 name "V3_MS_***********-/2 To V3_CORE_4.190.80.71-73"
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********1/32
  source-ip V3_MS_***********2/32
  destination-ip V3_CORE_4.190.80.71_73
  service http
  service TCP_3558
 rule 37 name "V3_GW&CORE_K8SNODE To V3_MS_K8SNODE"
  description V3-7
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip V3_MS_K8SNODE_***********/24
  service TCP_2379
  service TCP_8080
  service TCP_10250
  service TCP_10255
  service TCP-6443
 rule 12 name "V3_MS_K8SNODE To V3_GW&CORE_K8SNODE"
  description V3-8
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_K8SNODE_4.190.84.0/24
  service TCP_2379
  service TCP_8080
  service TCP_10250
  service TCP_10255
 rule 13 name "V3_MS_K8SNODE To V3_GW&CORE_K8SNODE Flannel"
  description V3-9
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_K8SNODE_4.190.84.0/24
  service UDP_8472
 rule 38 name "V3_GW_K8SNODE To V3_MS_K8SNODE Flannel"
  description V3-10
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_MS_K8SNODE_***********/24
  service UDP_8472
 rule 11 name "V3_MS To V3_DNS"
  description V3-15
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  destination-ip V3_DNS_***********/32
  destination-ip V3_DNS_***********/32
  service dns-tcp
  service dns-udp
 rule 40 name "V3_CORE&GW_K8SNODE To V3_MS_F5_***********"
  description V3-16
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip V3_MS_F5_***********
  service TCP_8080
 rule 39 name "V3_CORE_K8SNODE To V3_MS_K8SNODE Flannel"
  description V3-17
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_CORE_K8SNODE_4.190.84.0/24
  destination-ip V3_MS_K8SNODE_***********/24
  service UDP_8472
 rule 8 name "V3-VC To  V3-ESXI"
  description V3-23
  action pass
  logging enable
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS_***********/22
  destination-ip V3_ESXI_***********/24
  destination-ip G2-ESXI-**********/16
  service http
  service https
  service UDP_902
  service TCP_902
  service TCP-8182
  service UDP-8182
 rule 4 name "V3_MS_***********01  To  V3MNYY_CORE_**********/24"
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********01/32
  destination-ip V3MNYY_CORE_**********/24
  destination-ip *********
 rule 14 name "V3_MS_Tidb_Monitor To V3_CORE_Tidb_Cluster"
  description V3-28
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_Tidb_Monitor*************
  source-ip V3_MS_*************/32
  destination-ip V3_CORE_Tidb_Clus_4.190.81.0/24
  service TCP_10250
  service TCP_10255
  service TCP_8080
  service TCP_2379
  service ssh
  service TCP_9090-9091
  service TCP-9100-20182
  service TCP-2379
  service TCP-2380
  service TCP-8249
  service TCP-8250
  service TCP_31306
 rule 15 name "V3_MS_TIDB_Monitor To V3_CORE_TIDB_4.190.162.1_2"
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_Tidb_Monitor*************
  destination-ip V3_CORE_TIDB_F5_4.190.162.2
  destination-ip V3_CORE_TIDB_F5_4.190.162.1
  service TCP_31306
 rule 79 name To_V3_DMZ_*********/24
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********01/32
  destination-ip V3_DMZ_*********/24
  service ssh
 rule 70 name T1_HarBor_V3
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip T1_**********/32
  destination-ip HARBOR
  service http
  service https
 rule 69 name V3-T1_harbor
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip HARBOR
  destination-ip T1_**********/32
  service http 
  service https
 rule 74 name Wangxuan-CeShi
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip *************
  source-ip *************
  destination-ip *******/8
 rule 31 name "LuYu_Test_Storage_MGMT To V3_MS"
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Storage_MGMT_***********/24
  destination-ip V3_MS_***********/32
  service https
  service ssh
 rule 6 name "LuYu-Test-V3-MS To Storage-MGMT"
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********/32
  destination-ip Storage_MGMT_***********/24
  service https
  service ssh
 rule 35 name T1_To_V3_VC_***********
  description V3-22
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip T1_********/16
  source-ip BOCC
  source-ip CSLC-baoleiji-*********
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip V3_VC_***********
  service TCP_5480
  service http
  service https
  service ssh
  service TCP_9443
  service TCP_29090
  service TCP_29093
  service TCP_25601
  service TCP_23000
  service TCP_29411
  service TCP_28080
  service TCP-19200
  service TCP-29200-29201
  service TCP-29091
 rule 27 name 

  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip T1_**********/32
  destination-ip *********/16
 rule 36 name T1_To_V3_MS_***********/22
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip T1_********/16
  destination-ip V3_MS_***********/22
  service TCP_5000
  service http
  service ssh
 rule 86 name V3-VC_To_Backup-Server-***********
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_VC_***********
  destination-ip Backup_Server_***********/32
  service https
  service TCP_902
 rule 87 name Backup-Server-***********_To_V3-VC
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Backup_Server_***********/32
  destination-ip V3_VC_***********
  service https
  service TCP_902
 rule 120 name G3_MS_To_Backup-Server
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_MS_***********/24
  destination-ip Backup_Server_***********/32
  service http
  service https
 rule 99 name TO_Backup_Server_***********
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip TENANT02_MS_4.190.120.63/32
  source-ip TENANT02_MS_4.190.120.31-32
  destination-ip Backup_Server_***********/32
  service ssh
  service TCP-8400-8900
 rule 100 name Backup_Server_To_TENANT02_MS
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Backup_Server_***********/32
  destination-ip TENANT02_MS_4.190.120.63/32
  destination-ip TENANT02_MS_4.190.120.31-32
  service ssh
  service TCP-8400-8900
 rule 103 name G3-V1.3-20200429-01
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G2OCS_**********/32
  destination-ip G3_*********/16
  service ssh
  service TCP_31306
  service TCP_3555
  service TCP_3558
 rule 104 name G3-V1.3-20200429-02
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip CSLC-baoleiji-*********
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip G3_*********/16
  service ssh
  service TCP_31306
  service TCP_3555
  service TCP_3558
  service TCP_8080
  service http
  service https
  service TCP_8000
  service UDP_902
  service TCP_902
  service TCP-15000
  service TCP-9090
 rule 105 name G3-V1.3-20200429-03
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip CSLC-baoleiji-*********
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip G3_MS_VC_***********/32
  service TCP_5480
  service TCP_9443
  service http
  service https
 rule 129 name BOCC-To-TIDB
  action pass
  logging enable
  counting enable
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC
  source-ip CSLC-baoleiji-*********
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip configcenter01_************
  destination-ip MS-*************
  destination-ip G3_MS_TIDB-Mon_*************/32
  service TCP_3000
  service TCP-9090
 rule 106 name G3-V1.3-20200429-04
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip CSLC-baoleiji-*********
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip G3_MS_CAS_*************-234
  destination-ip G3_MS_CAS-F5_************/32
  service TCP_28080
  service https
 rule 107 name G3-V1.3-20200429-05
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_GATEWAY_*************-244
  destination-ip G3_MS_GW-F5_************/32
  service TCP_28088
  service https
 rule 108 name G3-V1.3-20200429-06
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_************-63
  destination-ip AlarmManager_4.190.121.71-72
  service TCP-29093
  service TCP-29090
 rule 109 name G3-V1.3-20200429-07
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_************-82
  destination-ip G3_MS_***********/32
  service TCP_25061
 rule 110 name G3-V1.3-20200429-08
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_MONITORGRAFAN
  service TCP_23000
 rule 111 name G3-V1.3-20200429-09
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_MONITOREDI
  service TCP_29411
 rule 112 name G3-V1.3-20200429-10
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_***********-11
  destination-ip G3_MS_***********51-212
  service TCP_28080
  service TCP-28090
  service TCP-28180
 rule 113 name G3-V1.3-20200429-11
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip "ZABBIX SERVER-************-42"
  service TCP_31050-31051
  service TCP_8022
 rule 114 name G3-V1.3-20200429-12
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT03_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_*************-212
  service TCP_8000
 rule 115 name G3-V1.3-20200429-13
  action pass  
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_***********1-26
  service TCP-9600
 rule 116 name G3-V1.3-20200429-14
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_************-32
  destination-ip G3_MS_***********/32
  service TCP_29411
 rule 117 name G3-V1.3-20200429-15
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_************-42
  service http 
  service https
 rule 118 name G3-V1.3-20200429-16
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_************/32
  destination-ip G3_MS_************-42
  service TCP_8022
  service TCP_23000
  service TCP_25601
  service TCP-28000-30000
  service TCP-27001
  service http
  service https
  service TCP-8091
 rule 119 name G3-V1.3-20200429-17
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_*********/16
  destination-ip G2_NTP
  service ntp
  service UDP_123
 rule 121 name G3-V1.3-20200429-18
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_MS_CAS_*************-234
  source-ip G3_MS_***********1-24
  destination-ip G2_DC_********/32
  service TCP_3268
  service TCP_389
 rule 122 name G3-V1.3-20200429-19
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_*************-182
  service TCP_28180
 rule 173 name G3-V1.3-20200429-20
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_MS_***********/32
  service TCP_25601
 rule 124 name Solarwinds
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Solarwinds-*************
  source-ip Solarwinds-**********
  destination-ip V3_MS_***********/32
  destination-ip ***********
  destination-ip ***********
  service http
  service https
  service TCP-8088
  service TCP-5988-5989
 rule 123 name "Storage_MGMT To V3_MS"
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Storage_MGMT_***********/24
  destination-ip V3_MS_***********/32
  service https
  service ssh
 rule 125 name K8SNODE
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS_***********/22
  source-ip ***********/24
  destination-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_4.190.80.0/22
  service TCP-6370
  service TCP-5000_5007
  service TCP-5003
  service TCP-5001-5030
 rule 126 name CSLC-baoleiji
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip CSLC-baoleiji-**********
  source-ip CSLC-baoleiji-*********
  destination-ip *********/16
  service ssh
  service https
  service http
  service TCP_3389
  service TCP-8888
  service TCP-8889
  service TCP-8013
  service TCP-8090
  service TCP_8000
  service TCP_3555
  service TCP_3558
  service TCP_31306
  service TCP-5000
  service TCP-28000-30000
  service TCP-8400-8900
  service TCP-8004
  service TCP-8011
  service TCP-8018
  service TCP-8022
  service TCP-8088
  service TCP-8182
  service TCP-8249
  service TCP-5001-5030
 rule 127 name jingxiang
  action pass  
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip JiGuan-***************
  destination-ip TENANT02_MS_4.190.120.31-32
  service http
 rule 128 name TO_G2FTP
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip *********/16
  destination-ip G2FTP-**********
  service ftp
  service ssh
 rule 130 name To-ELK
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G2-*********-92_94-95
  source-ip G2-**********-95
  source-ip G2TRANSROUTE_**********-84
  source-ip G2_WEBDC_Group
  destination-ip G3ELK-***********-5
  service TCP_29092
 rule 131 name TO-MiMaGongJu
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_MS_*************-182
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip MiMaGongJu-***********
  service https
  service TCP-31306
 rule 132 name TO-G2-VC
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********01/32
  destination-ip T1_********/16
  destination-ip VC-************
  service ssh
  service https
 rule 133 name G301
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS-*************
  source-ip MS-*************
  destination-ip **********-3
  destination-ip **********1-16
  service TCP-6370
  service TCP-5000_5007
 rule 134 name G3

  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********/24
  source-ip ***********/24
  destination-ip *********-2
  service TCP-25
 rule 135 name G303
  action pass
  counting enable
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip *********-2
  destination-ip G3ELK-***********-5
  service TCP_29092
 rule 136 name TO-FAM
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT01_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip FAM-*********/24
  service TCP-31306
  service TCP-29200
  service TCP-9090
 rule 137 name TO-OTMServer
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS-*************
  source-ip MS-*************
  destination-ip OTMServerV01
  service TCP-8022
 rule 138 name G3_Jiankongerqi
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_MS_CAS_*************-234
  source-ip MONITORALERTER01-*************
  source-ip MONITORALERTER02-*************
  destination-ip G2TOHERAPROXY01-temp
  service TCP-28083
 rule 139 name G3_JiaKongErqi
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G2TOHERAPROXY01-temp
  destination-ip ************
  destination-ip G3-MS-F5-************
  service TCP_28080
  service https
 rule 140 name JCVSC_G2DMZ
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********01/32
  destination-ip G2DMZ-*********
  service ssh
 rule 141 name G3_R141_0714
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ***********/24
  destination-ip V3_CORE_4.190.80.0/22
  service TCP-4100
 rule 142 name anquanlousao
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip FOC-***********
  source-ip FOC-*********
 rule 143 name nfs
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS_***********/22
  destination-ip nfs-**********
  destination-ip **********
 rule 144 name OPCC-Flink
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip flink-*************-119
  service TCP-28081
  service TCP_10098-10099
 rule 145 name VPN_SYSLOG
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip VPN-************
  source-ip VPN-***********
  destination-ip V3_MS_***********/24
  service UDP-514
  service UDP-8514
 rule 146 name JCJK-1.3.7_01
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS_***********/22
  destination-ip G3TOHERAPROXY-F5-***********
  service TCP-28083
 rule 147 name JCJK1.3.7_02
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G3TOHERAPROXY-**********-12
  destination-ip G3GAIA-F5-************
  destination-ip ************
  destination-ip G3-MS-F5-************
  service TCP_28080
  service https
 rule 148 name JCJK1.3.7_03
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3MONITORGAIA-*************-162
  destination-ip V3_GW_K8SNODE_**********/24
  service http
 rule 149 name JCVSC-Solarwinds
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********01/32
  source-ip MS_***********/22
  destination-ip G2-Solarwinds-*************/32
  destination-ip Network_Mgt_**********/24
  service TCP-17778
 rule 150 name G3_MS-AIDB_To_G3_Mail
  action pass  
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_MS_AIDB_*************-123
  destination-ip G3_Mail_*********
  service smtp
 rule 151 name To_G3_CORE_NTP
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_*********/16
  destination-ip G3_NTP_***********1-252
  service ntp
 rule 152 name SSM-Ansbile
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip Ansbile-************
  destination-ip G3_*********/16
  service ssh
 rule 153 name MHA
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MHA-*************
  source-ip Ansbile-************
  destination-ip G3_*********/16
  service TCP-31306
 rule 154 name OPSFTP
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_*********/16
  destination-ip SBSG2OPSFTP01-************
  service ssh
  service ftp
 rule 155 name IRM1.15.0_01
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip CORE-**********/24
  destination-ip V3_MS_***********/24
  service TCP_3191
 rule 156 name IRM1.15.0_02
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G3OPERVM-**********-2
  destination-ip G3_*********/16
  service TCP-2379
  service TCP-3555
  service TCP-3558
  service TCP-5003
  service TCP_8080
  service UDP_8472
  service TCP_6370
  service TCP_7001
  service TCP_3191
  service http
  service ssh
  service TCP-8022
 rule 159 name IRM1.15.0_03
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip CORE-**********/24
  destination-ip MonitorZK-*************-135
  service TCP_3191
 rule 157 name ItoSchedule
  action pass  
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ItoSchedule-*************-172
  destination-ip G3_*********/16
  service TCP-3555
  service TCP-31306
  service ssh
  service TCP-3557
  service TCP_3558
 rule 158 name ItoSchedule_to_Mail
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_*********/16
  destination-ip *********-2
  service TCP-25
 rule 160 name G3-R160_01
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip SBSG2OTJob-***********
  destination-ip G3_*********/16
  service ssh  
 rule 161 name G3-R160_02
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G3_*********/16
  destination-ip JianKong_***********
  service TCP_29092
 rule 162 name 20201208
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip T1_********/16
  source-ip T1_********/24
  source-ip *********/16
  destination-ip MS_***********/22
 rule 165 name G2-SDAS-Grafana_To_G3-MS-ELK
  action pass
  counting enable
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G2-SDAS-Grafana-*********/32
  destination-ip G3ELK-***********-5
  destination-ip G3-MS-ELKF5-************/32
  service TCP-19200
  service TCP-29200-29201
 rule 166 name ECC01
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS-*************
  source-ip MS-*************
  destination-ip G3OPERVM-**********-2
  service tcp-9044
 rule 167 name ECC02
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3ECCSYS01-*************
  source-ip G3ECCSYS01-*************
  destination-ip MGMT_*********/16
  destination-ip Storage_MGMT_***********/24
  service snmp-request
  service snmp-trap
 rule 168 name ECC06-
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip AlarmManager_4.190.121.71-72
  destination-ip ShuMeiPai-**********
  service http
 rule 169 name TO-DIP
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS-*************
  source-ip MS-*************
  destination-ip DIP-************-93
  destination-ip DIP-************-57
  service TCP-9092
 rule 170 name Solarwinds-SSH
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Solarwinds-**********
  destination-ip G3_*********/16
  service ssh
 rule 171 name TO-OPeratorVM
  action pass  
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ***********/24
  destination-ip G3OPERVM-**********-2
  service tcp-9044
 rule 172 name CAS-TO-RDC
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_MS_CAS_*************-234
  source-ip G3_MS_***********1-24
  destination-ip RDC-F5-************
  service TCP_389
  service TCP_3268
 rule 174 name JCVSC-TO-Solarwinds
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT01_MS_Outside
  source-ip V3_MS_***********01/32
  destination-ip Solarwinds-**********
  service TCP-17778
 rule 189 name YZBOCC_Deny
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip YZBOCC
  source-ip YZECC-*********
  service TCP_31306
  service TCP_3558
  service ssh
  service TCP_3555
  service TCP_3389
 rule 175 name TO-SFTP
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip *********/16
  destination-ip SFTP-************
  service ssh
 rule 176 name G3_163_01
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip BISMONITORCOLLECT-F5
  service TCP-26100
 rule 177 name G3_163_02
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip G3SIMULATIONTRANSROUTER01
  service TCP_8082
 rule 178 name G3_JianCe2.0_01
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_K8SNODE_4.190.84.0/24
  service TCP-26100
 rule 179 name G3_JianCe2.0_02
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip G3TOHERAPROXY-F5-***********
  service TCP-28083
 rule 180 name G3_JianCe2.0_03
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC&4A
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip G3_MS_************/32
  destination-ip ItoSchedule-*************-172
  service TCP-8089
  service TCP-8090
 rule 181 name TENANT02_MS_InsideNANT02_MS_Outside_181_IPv4
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip Zabbix-Proxy-*************
  destination-ip CSLRMSFSP01-********
  destination-ip W5RRMSFSC01-*********
  destination-ip W5RRDCC-*********-2
  destination-ip *********/16
  service TCP-31050-31051
 rule 182 name TENANT02_MS_OutsideNANT02_MS_Inside_182_IPv4
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip CSLRMSFSP01-********
  source-ip W5RRDCC-*********-2
  source-ip W5RRMSFSC01-*********
  source-ip *********/16
  destination-ip Zabbix-Proxy-*************
  service TCP-31050-31051
 rule 183 name linshtest
  description 20210621

  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip V3_CORE_4.190.80.0/22
  destination-ip MS_***********/22
  service http
 rule 184 name MS_K8SNODE_TO_NAS
  action pass
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip NAS_**********
 rule 185 name Zabbix_for_Windows01
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Network_Mgt_**********/24
  source-ip Backup_Server_***********/32
  source-ip W5R-*********/24
  destination-ip Zabbix-Proxy-*************
  service TCP-31050-31051
 rule 186 name Zabbix_for_Windows02
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip Zabbix-Proxy-*************
  destination-ip Network_Mgt_**********/24
  destination-ip Backup_Server_***********/32
  destination-ip W5R-*********/24
  service TCP-31050-31051
 rule 187 name SSM-Ansbile-New
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip Ansbile-************
  destination-ip G3_*********/16
  service ssh
 rule 188 name OPERVM-TO-ELK
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G3OPERVM-**********-2
  source-ip G3BISMONTORCOLLECT
  destination-ip G3ELK-***********-5
  destination-ip G3-MS-ELKF5-************/32
  service TCP-29200
 rule 190 name Radius
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MS_***********/22
  destination-ip Radius-**********
  service UDP-1812
 rule 192 name R240-01
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3TSPAPP01-***********01
  source-ip G3TSPAPP02-*************
  destination-ip *********/16
  service TCP_7001
  service TCP_6370
  service TCP-5000_5007
  service TCP_31306
  service TCP_3558
  service TCP_3555
  service TCP_3191
  service UDP_8472
  service ssh
  service http
  service TCP_8080
 rule 193 name R240-02
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip BOCC&4A
  source-ip CSLC-baoleiji-**********
  source-ip *********/16
  destination-ip G3TSPAPP01-***********01
  destination-ip G3TSPAPP02-*************
  service TCP_28080
 rule 194 name R240-03
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_***********/22
  destination-ip NAS-**********/32
 rule 195 name AlarmManager_To_CSLC-Syslog-gateway
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip AlarmManager_4.190.121.71-72
  destination-ip CSLC-Syslog-gateway-**********
  service TCP-38082
 rule 198 name BOCC_BingDuServer
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip BOCC
  destination-ip ************
  service https
  service http
  service TCP-4343
  service TCP-8080
 rule 197 name OPERVM-TO-ItoSchedule
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G3OPERVM-**********-2
  destination-ip ItoSchedule-*************-172
  service https
 rule 199 name BINGDUtansfer
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip ********39
  destination-ip ************
  service TCP-8080
 rule 200 name MHA-To-G3BOSDB
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip MHA-*************
  destination-ip G3BOSDB-************-104
  service TCP-31306
  service ssh
 rule 196 name BingDuServer_BOCC
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ************
  destination-ip BOCC
  service TCP-51153
 rule 201 name ***********-**********
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ************-14
  destination-ip ************-103
  service TCP-31306
 rule 202 name R330-01
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip USAP-***********
  service TCP-19080
 rule 210 name VDI-TO-KIBANA
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip Test-Baoleiji
  destination-ip G3_MS_************-82
  destination-ip G3_MS_***********/32
  service TCP_25601
 rule 212 name Hermes-TO-Core
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ************-14
  destination-ip ***********8-220
  service TCP-5000-5011
 rule 213 name G3BOSRedis-TO-GW
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip ************-14
  destination-ip ***********-59
  service TCP_7001
 rule 214 name G3BISMONTORCOLLECT_R340
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip G3BISMONTORCOLLECT
  destination-ip *********/16
  service TCP-31306
  service TCP_3558
  service TCP_3555
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP-10251-10252
  service TCP_2379
  service TCP_8080
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP-9100
  service ssh
  service http
  service https
  service TCP_7100
 rule 215 name VulnerabilityScan_Network
  action pass
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip VulnerabilityScan-************
  destination-ip *********/16
 rule 216 name YIXIANYUNWEI_to_*************/32
  action pass  
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip YIXIANYUNWEI
  destination-ip *************/32
  service TCP-31000
  service TCP-32000
 rule 217 name OPS_BOS
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip OPS
  destination-ip BOS
  service ssh
 rule 221 name K8SNODE_to_*********/16
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip *********/16
  service TCP_31306
  service TCP_3558
  service TCP_3555
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP_2379
  service TCP-10251-10252
  service https
  service http
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service TCP_8080
  service ssh
  service TCP_7100
 rule 500 name linshi
  action pass
  disable
  source-zone TENANT02_MS_Outside
  destination-zone TENANT02_MS_Inside
  source-ip YZECC-*********
  destination-ip V3_MS_***********01/32
 rule 222 name flink_**********
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip flink-*************-119
  destination-ip **********
 rule 223 name TO-NFS
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  destination-ip NFS-*********
 rule 224 name MS_SJZT
  action pass
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip G3_MS_*************-212
  destination-ip SJZT
  service TCP-8080
 rule 228 name 20231026-dc-to-socSyslog
  action pass
  logging enable
  counting enable
  source-zone TENANT02_MS_Inside
  destination-zone TENANT02_MS_Outside
  source-ip V3_VC_***********
  destination-ip SOC-*************-112
  service TCP_8400
  service UDP-514
 rule 49 name deny
  logging enable
  counting enable
#
security-policy ipv6
#
return

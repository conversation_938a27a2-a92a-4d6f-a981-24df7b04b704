<XWHPD-NE1DIL-FW01>dis curr
#
 version 7.1.064, Release 9141P38
#
 sysname XWHPD-NE1DIL-FW01
#
 clock timezone BeiJing add 08:00:00
#
context Admin id 1
#
failover group 1
 bind chassis 1 slot 6 cpu 1 primary
 bind chassis 2 slot 6 cpu 1 secondary
#
ip vpn-instance management
 route-distinguisher 1000000000:1
 vpn-target 1000000000:1 import-extcommunity
 vpn-target 1000000000:1 export-extcommunity
#
 irf domain 50
 irf mac-address persistent always
 irf auto-update enable
 irf auto-merge enable
 undo irf link-delay
 irf member 1 priority 32
 irf member 2 priority 1
#
 undo link-aggregation load-sharing mode local-first
#
track 1 interface Bridge-Aggregation1
#
track 3 interface Bridge-Aggregation3
#
track 101 interface Blade1/6/0/1 physical
#
track 102 interface Blade1/6/0/2 physical
#
track 201 interface Blade2/6/0/1 physical
#
track 202 interface Blade2/6/0/2 physical
#
 ip ttl-expires enable
#
 forwarding policy per-packet
 ip load-sharing mode per-packet global
#
 nat static-load-balance enable
#              
 lldp global enable
#
 system-working-mode standard
 password-recovery enable
#
vlan 1
#
vlan 199
 name test_YaCe
#
vlan 3001
 name TENANT01_GW
#
vlan 3002
 name TENANT02_GW
#
vlan 3003
 name TENANT03_GW
#
vlan 3004
 name TENANT04_GW
#
vlan 3005      
 name TENANT05_GW
#
vlan 3550
#
irf-port 1/2
 port group interface Ten-GigabitEthernet1/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet1/9/1/12 mode enhanced
#
irf-port 2/1
 port group interface Ten-GigabitEthernet2/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet2/9/1/12 mode enhanced
#
object-group ip address *******
 security-zone Test_Outside
 0 network host address *******
#
object-group ip address *******
 security-zone TEST_Inside
 0 network host address *******
#
object-group ip address ***********-16
 security-zone TENANT02_GW_Outside
 0 network range *********** ***********
 10 network range ********** **********
#
object-group ip address **********-162
 security-zone TENANT02_GW_Outside
 0 network range ********** **********
#
object-group ip address ***********-199
 0 network subnet *********** ***************
#
object-group ip address 1_1_1_0_test
 0 network subnet ******* *************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address *******/8
 security-zone TENANT02_GW_Inside
 0 network subnet ******* *********
#
object-group ip address **********
 0 network host address **********
#
object-group ip address *********/16
 0 network subnet ********* ***********
#
object-group ip address ************-42
 0 network range ************ ************
#
object-group ip address *********/16
 0 network subnet ********* ***********
#
object-group ip address *********/16
 0 network subnet ********* ***********
#
object-group ip address *************
 security-zone TENANT02_GW_Outside
 0 network host address *************
#
object-group ip address *************
 security-zone TENANT02_GW_Outside
 0 network host address *************
#
object-group ip address ***********/22
 security-zone TENANT02_GW_Outside
 0 network subnet *********** *************
#              
object-group ip address ************-14
 security-zone TENANT02_GW_Outside
 0 network range ************ ************
#
object-group ip address ***********
 security-zone TENANT02_GW_Outside
 0 network subnet *********** *************
#
object-group ip address ***********
 security-zone TENANT02_GW_Outside
 0 network host address ***********
#
object-group ip address ***********0
 0 network host address ***********0
#
object-group ip address ***********9
 0 network host address ***********9
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address ***********
 security-zone TENANT02_GW_Outside
 0 network host address ***********
#
object-group ip address ************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address ************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address ***********/24
 security-zone TENANT02_GW_Inside
 0 network subnet *********** *************
#
object-group ip address ***********/24
 security-zone TENANT02_GW_Outside
 0 network subnet *********** *************
#
object-group ip address **********1
 security-zone TENANT02_GW_Inside
 0 network host address **********1
#
object-group ip address **********1-12
 0 network range **********1 **********2
#
object-group ip address **********3-14
 0 network range **********3 **********4
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address ***********-59
 security-zone TENANT02_GW_Inside
 0 network range *********** ***********
#
object-group ip address **********
 security-zone TENANT02_GW_Inside
 0 network host address **********
#
object-group ip address **********-4
 0 network range ********** **********
#
object-group ip address **********/22
 0 network subnet ********** *************
#
object-group ip address ************-132
 0 network range ************ ************
#
object-group ip address ***********-91
 0 network subnet *********** ***************
#
object-group ip address **********-2
 0 network range ********** **********
#
object-group ip address **********/22
 0 network subnet ********** *************
#
object-group ip address 4.190.84.1
 security-zone TENANT02_GW_Outside
 0 network host address 4.190.84.1
#
object-group ip address *********/24
 0 network subnet ********* *************
#
object-group ip address *********
 0 network host address *********
#
object-group ip address 4A-*********
 security-zone TENANT02_GW_Outside
 0 network subnet ********* *************
#
object-group ip address 500WAN-*********/27
 security-zone TENANT02_GW_Outside
 0 network subnet ********* ***************
#
object-group ip address AMS-CHANNEL-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address AMSRPT-F5-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address Ansbile-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address Ansbile-************
 0 network host address ************
#
object-group ip address BISRPT-F5-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address BMSDB-***********-15
 security-zone TENANT02_GW_Outside
 0 network range *********** 4.190.88.15
#
object-group ip address BOCC
 security-zone TENANT02_GW_Outside
 0 network subnet ******** *************
 10 network subnet 18.2.12.0 *************
 20 network subnet ********* *************
 30 network subnet ********* *************
 40 network subnet 4.128.1.0 *************
 50 network subnet ********** *************
 60 network subnet 3.30.11.0 *************
 70 network subnet 9.66.1.0 *************
 80 network subnet 9.66.2.0 *************
 90 network subnet 9.66.3.0 *************
 100 network subnet 9.66.32.0 *************
#
object-group ip address BOCC&4A
 0 network subnet ******** *************
 10 network subnet 18.2.12.0 *************
 20 network subnet ********* *************
 30 network subnet ********* *************
 40 network subnet ********* *************
 50 network subnet 4.128.1.0 *************
 60 network subnet ********** *************
 70 network subnet 9.66.1.0 *************
 80 network subnet 9.66.2.0 *************
 90 network subnet 9.66.3.0 *************
 100 network subnet 9.66.32.0 *************
#
object-group ip address BOSROUTER-F5-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address CAS_F5_4.190.162.3
 0 network host address 4.190.162.3
#
object-group ip address CASGW_4.190.162.4
 0 network host address 4.190.162.4
#
object-group ip address Configcenter
 security-zone TENANT02_GW_Outside
 0 network range ************ 4.190.120.13
#
object-group ip address ConfigCenter-***********
 security-zone TENANT02_GW_Outside
 0 network host address ***********
#
object-group ip address CORE-F5-***********/24
 security-zone TENANT02_GW_Outside
 0 network subnet *********** *************
#
object-group ip address CSLC-************/24
 0 network subnet ************ *************
#
object-group ip address CSLC-*********/24
 0 network subnet ********* *************
#
object-group ip address CSLC-*********/24
 0 network subnet ********* *************
#
object-group ip address CSLC-**********-96
 security-zone TENANT02_GW_Outside
 0 network range ********** 3.27.13.96
#
object-group ip address CSLC-*******
 security-zone TENANT02_GW_Outside
 0 network host address *******
#
object-group ip address CSLC-**********/24
 0 network subnet ********** *************
#
object-group ip address CSLC-**********-14
 0 network range ********** 4.17.10.14
#
object-group ip address CSLC-*********
 security-zone TENANT02_GW_Outside
 0 network subnet ********* *************
#
object-group ip address CSLC-***********
 security-zone TENANT02_GW_Outside
 0 network host name ***********
#
object-group ip address CSLC-***********-134
 0 network range *********** 4.27.13.134
#
object-group ip address CSLC-**********-98
 0 network range ********** 4.27.13.98
#              
object-group ip address CSLC-**********-96
 0 network range ********** 4.27.41.96
#
object-group ip address CSLC-**********-143
 0 network range ********** 4.60.6.143
#
object-group ip address CSLC-**********-213
 0 network range ********** 4.60.6.213
#
object-group ip address CSLC-baoleiji-**********
 security-zone TENANT02_GW_Outside
 0 network subnet ********** *************
#
object-group ip address CSLC-DIP-***********
 security-zone TENANT02_GW_Outside
 0 network subnet *********** *************
#
object-group ip address CSLC-EX-Subscriber-*********/24
 0 network subnet ********* *************
#
object-group ip address CSLC-GTM-********/24
 0 network subnet ******** *************
#              
object-group ip address CSLC-K8S-********/24
 0 network subnet ******** *************
#
object-group ip address CSLC-OPENAPI-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address CSLC-SIE-Proxy-********-9
 0 network range ******** ********
#
object-group ip address Cslc-TEST-**********/32
 0 network host address **********
#
object-group ip address CSLC-UMP-*********/24
 0 network subnet ********* *************
#
object-group ip address CSLC-XiaoFu-************/24
 security-zone TENANT02_GW_Outside
 0 network subnet ************ *************
#
object-group ip address CSLC_*******/32
 security-zone TENANT02_GW_Outside
 0 network host address *******
#
object-group ip address DMZ-SSL-*********/24
 security-zone TENANT02_GW_Inside
 0 network subnet ********* *************
#
object-group ip address ELP-**********-45
 security-zone TENANT02_GW_Outside
 0 network range ********** **********
#
object-group ip address ELP-*********-3
 security-zone TENANT02_GW_Outside
 0 network range ********* *********
#
object-group ip address F5-***********
 security-zone TENANT02_GW_Inside
 0 network host address ***********
#
object-group ip address FOC-*********
 0 network host address *********
#
object-group ip address FOC-***********
 0 network host address ***********
#              
object-group ip address FOC-***********-137
 0 network subnet *********** ***************
#
object-group ip address G2-Solarwinds-*************/32
 security-zone Management
 0 network host address *************
#
object-group ip address G2_AMSDB_*********/32
 security-zone TENANT02_GW_Outside
 0 network host address *********
#
object-group ip address G2_NTP
 security-zone TENANT02_GW_Outside
 0 network host address **********
 10 network host address ***********
#
object-group ip address G2_TRANSROUTE_**********-84
 security-zone TENANT02_GW_Outside
 0 network range ********** **********
#
object-group ip address G2_WEBDC
 security-zone TENANT02_GW_Outside
 0 network range ********** **********
 10 network range ********** **********
#
object-group ip address G2ELP-********
 security-zone TENANT02_GW_Outside
 0 network subnet ******** *************
#
object-group ip address G2FTP-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address G2FTP-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address G2OCS-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address G2WEBDC-*********
 security-zone TENANT02_GW_Outside
 0 network subnet ********* *************
#
object-group ip address G3-GW-**********/22
 security-zone TENANT02_GW_Inside
 0 network subnet ********** *************
#
object-group ip address G3-Test
 security-zone TENANT02_GW_Inside
 0 network subnet ********** *************
#
object-group ip address G3-TEST-*********/24
 security-zone TENANT02_GW_Outside
 0 network subnet ********* *************
#
object-group ip address G3-Test-**********/24
 0 network subnet ********** *************
#
object-group ip address G3-Test-4/176.9.0/24
 0 network subnet ********* *************
#
object-group ip address G3_*********/16
 security-zone TENANT03_GW_Inside
 0 network subnet ********* ***********
#
object-group ip address G3_**********/24
 security-zone TENANT03_GW_Inside
 0 network subnet ********** *************
#
object-group ip address G3_CORE_NTP_***********1-252
 security-zone TENANT02_GW_Outside
 0 network range ***********1 ***********2
#
object-group ip address G3_GW_**********/24
 security-zone TENANT02_GW_Inside
 0 network subnet ********** *************
#
object-group ip address G3AMS-************-102
 security-zone TENANT02_GW_Outside
 0 network range ************ ************
#
object-group ip address G3AMS-F5-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address G3ARESRISK
 0 network range ************ ************
#
object-group ip address G3BISMONTORCOLLECT
 0 network range ********** **********
#
object-group ip address G3ELP-***********
 security-zone TENANT02_GW_Inside
 0 network host address ***********
#
object-group ip address G3IHSGW-***********
 security-zone TENANT02_GW_Inside
 0 network host address ***********
#
object-group ip address G3LINSHISBCCOPYTOWCS01-0.200
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address G3MATGW-F5-***********
 security-zone TENANT02_GW_Inside
 0 network host address ***********
#
object-group ip address G3MONITORGAIA-*************-162
 security-zone TENANT02_GW_Outside
 0 network range ************* *************
#
object-group ip address G3OPERVM
 security-zone TENANT02_GW_Outside
 0 network range ********** **********
#
object-group ip address G3OPERVM01-**********
 0 network host address **********
#
object-group ip address G3OPERVM02-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address G3SIMULATIONTRANSROUTER01
 0 network host address **********
#
object-group ip address G3SSL-*********/24
 security-zone TENANT02_GW_Outside
 0 network subnet ********* *************
#
object-group ip address G3TSPAPP01-*************
 0 network host address *************
#
object-group ip address G3TSPAPP02-*************
 0 network host address *************
#
object-group ip address G3WCSDBVIP-4.190.80.73
 security-zone TENANT02_GW_Inside
 0 network host address 4.190.80.73
#
object-group ip address G3WCSINFO-**********-22
 security-zone TENANT02_GW_Outside
 0 network range ********** 4.190.0.22
#
object-group ip address G3WCSINFOSFTP-***********-42
 security-zone TENANT02_GW_Inside
 0 network range *********** 4.190.40.42
#
object-group ip address G3WCSINFOSFTP-F5-***********
 security-zone TENANT02_GW_Inside
 0 network host address ***********
#
object-group ip address "GW ZABBIX PROXY-************"
 security-zone TENANT02_GW_Inside
 0 network host address ************
#
object-group ip address GW-F5-***********/24
 security-zone TENANT02_GW_Inside
 0 network subnet *********** *************
#              
object-group ip address GW-F5-***********
 security-zone TENANT02_GW_Inside
 0 network host address ***********
#
object-group ip address HARBOR
 0 network group-object HARBOR01_4.190.120.31
 1 network group-object HARBOR02_4.190.120.32
#
object-group ip address HARBOR01_4.190.120.31
 0 network host address 4.190.120.31
#
object-group ip address HARBOR02_4.190.120.32
 0 network host address 4.190.120.32
#
object-group ip address harbor_F5_4.190.163.2
 0 network host address 4.190.163.2
#
object-group ip address hermes-***********-16
 security-zone TENANT02_GW_Outside
 0 network range *********** 4.190.80.16
#
object-group ip address Hermes-***********1-216
 0 network range ***********1 ***********6
#
object-group ip address Hermes-mata-4.190.80.201-203
 0 network range 4.190.80.201 4.190.80.203
#
object-group ip address HN-********/27
 security-zone TENANT02_GW_Outside
 0 network subnet ******** ***************
#
object-group ip address IHSF5-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address IHSGW-F5-***********
 security-zone TENANT02_GW_Outside
 0 network host address ***********
#
object-group ip address IRM-F5-***********6
 security-zone TENANT02_GW_Inside
 0 network host address ***********6
#
object-group ip address JianKong_4.190.121.0
 0 network subnet 4.190.121.0 *************
#              
object-group ip address JINGCAIWANG-********-6
 security-zone TENANT02_GW_Outside
 0 network range ******** 10.0.2.6
#
object-group ip address JINGCAIWANG-***********-11
 security-zone TENANT02_GW_Outside
 0 network subnet *********** ***************
#
object-group ip address JINGCAIWANG_***********-11
 0 network subnet *********** ***************
#
object-group ip address LinShi_18.5.81.2
 security-zone TENANT02_GW_Outside
 0 network host address 18.5.81.2
#
object-group ip address LinShi_RTQ_18.5.81.4
 security-zone TENANT02_GW_Outside
 0 network host address 18.5.81.4
#
object-group ip address Mail-*********-2
 security-zone TENANT02_GW_Outside
 0 network range ********* 4.190.0.2
#              
object-group ip address MatServer-F5-***********4/32
 security-zone TENANT02_GW_Inside
 0 network host address ***********4
#
object-group ip address MatServer-F5-***********5/32
 security-zone TENANT02_GW_Inside
 0 network host address ***********5
#
object-group ip address NAS-**********-6
 security-zone TENANT02_GW_Outside
 0 network range ********** 4.191.40.6
#
object-group ip address NAS_4.191.40.3
 security-zone TENANT02_GW_Outside
 0 network host address 4.191.40.3
#
object-group ip address NAS_4.191.41.1
 0 network host address 4.191.41.1
#
object-group ip address NAT-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#              
object-group ip address Network_Mgt_**********/24
 0 network subnet ********** *************
#
object-group ip address NetworkOOB-*********/23
 0 network subnet ********* *************
 1 network subnet 4.176.1.0 *************
#
object-group ip address NFS-*********
 0 network host address *********
#
object-group ip address nginx01_4.190.120.51/32
 0 network host address 4.190.120.51
#
object-group ip address nginx02_4.190.120.52/32
 0 network host address 4.190.120.52
#
object-group ip address OGS-***********8/32
 0 network host address ***********8
#
object-group ip address OPS
 0 network group-object OPS01_4.190.120.41
 1 network group-object OPS02_4.190.120.42
#              
object-group ip address OPS01_4.190.120.41
 0 network host address 4.190.120.41
#
object-group ip address OPS02_4.190.120.42
 0 network host address 4.190.120.42
#
object-group ip address QSCS-*********/32
 0 network host address *********
#
object-group ip address redis-**********-9
 security-zone TENANT02_GW_Inside
 0 network range ********** 4.190.40.9
#
object-group ip address Redis_feioltp
 0 network group-object Redis_feioltp01_***********
 1 network group-object Redis_feioltp02_***********
 2 network group-object Redis_feioltp03_***********
 3 network group-object Redis_feioltp04_***********
 4 network group-object Redis_feioltp05_***********
 5 network group-object Redis_feioltp06_***********
 6 network group-object Redis_feioltp07_***********
 7 network group-object Redis_feioltp08_***********
 8 network group-object Redis_feioltp09_***********
#
object-group ip address Redis_feioltp01_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp02_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp03_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp04_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp05_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp06_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp07_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp08_***********
 0 network host address ***********
#
object-group ip address Redis_feioltp09_***********
 0 network host address ***********
#
object-group ip address Redis_oltp
 0 network group-object Redis_oltp01_***********
 1 network group-object Redis_oltp02_***********
 2 network group-object Redis_oltp03_***********
 3 network group-object Redis_oltp04_***********
 4 network group-object Redis_oltp05_***********
 5 network group-object Redis_oltp06_***********
 6 network group-object Redis_oltp07_***********
 7 network group-object Redis_oltp08_***********
 8 network group-object Redis_oltp09_***********
#
object-group ip address Redis_oltp01_***********
 0 network host address ***********
#
object-group ip address Redis_oltp02_***********
 0 network host address ***********
#
object-group ip address Redis_oltp03_***********
 0 network host address ***********
#
object-group ip address Redis_oltp04_***********
 0 network host address ***********
#
object-group ip address Redis_oltp05_***********
 0 network host address ***********
#
object-group ip address Redis_oltp06_***********
 0 network host address ***********
#
object-group ip address Redis_oltp07_***********
 0 network host address ***********
#
object-group ip address Redis_oltp08_***********
 0 network host address ***********
#
object-group ip address Redis_oltp09_***********
 0 network host address ***********
#
object-group ip address RTQDB-***********-33
 security-zone TENANT02_GW_Outside
 0 network range *********** ***********
#
object-group ip address SaiShiTuiJian-F5-***********3
 security-zone TENANT02_GW_Inside
 0 network host address ***********3
#
object-group ip address SBSG2GRSAS-***********-32
 security-zone TENANT02_GW_Inside
 0 network range *********** ***********
#
object-group ip address SBSG2IHS-************-142
 security-zone TENANT02_GW_Outside
 0 network range ************ ************
#
object-group ip address SBSG2IRMAS-***********-62
 security-zone TENANT02_GW_Inside
 0 network range *********** ***********
#
object-group ip address SBSG2IRMDBVIP-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address SBSG2MATSERVER-***********-22
 security-zone TENANT02_GW_Inside
 0 network range *********** ***********
#
object-group ip address SBSG2OPSFTP01-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address SBSG2OTJob-***********
 security-zone TENANT02_GW_Outside
 0 network host address ***********
#
object-group ip address SBSG2TRANSROUTERB01-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address SBSG2WEBDC-**********1-12
 security-zone TENANT02_GW_Inside
 0 network range **********1 **********2
#
object-group ip address SD-**********/27
 security-zone TENANT02_GW_Outside
 0 network subnet ********** ***************
#
object-group ip address SDAS-BLIDB-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address SFTP-**********00
 security-zone TENANT02_GW_Outside
 0 network host address **********00
#
object-group ip address Solarwinds-**********
 security-zone TENANT02_GW_Outside
 0 network subnet ********** *************
#
object-group ip address Solarwinds-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address SYSLOG-F5-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address T1-********/8
 0 network subnet ******** *********
#
object-group ip address T1_********/16
 0 network subnet ******** ***********
#
object-group ip address T1_**********
 0 network host address **********
#
object-group ip address T1_**********/32
 0 network host address **********
#
object-group ip address T1_********/24
 0 network subnet ******** *************
#
object-group ip address T1_NTP_***********
 0 network host address ***********
#
object-group ip address TCPCOPYINTERCEPT01-***********
 security-zone TENANT02_GW_Inside
 0 network host address ***********
#
object-group ip address Test-Port1-*************/32
 security-zone TENANT02_GW_Outside
 0 network host address *************
#
object-group ip address Test-Port1-*************/32
 0 network host address *************
#
object-group ip address Test-Port2-************/32
 security-zone TENANT02_GW_Inside
 0 network host address ************
#
object-group ip address Test_*************/24
 security-zone TENANT02_GW_Outside
 0 network subnet ************* *************
#
object-group ip address tiaobanji-***********
 security-zone TENANT01_GW_Outside
 0 network host address ***********
#
object-group ip address tiaobanji_************-244
 security-zone TENANT01_GW_Outside
 0 network range ************ ************
#
object-group ip address TIDB_**********/24
 0 network subnet ********** *************
#
object-group ip address Tool-********
 0 network host address ********
#
object-group ip address Transrouter-F5-***********1/32
 security-zone TENANT02_GW_Inside
 0 network host address ***********1
#
object-group ip address Transrouter-F5-***********7
 security-zone TENANT02_GW_Inside
 0 network host address ***********7
#
object-group ip address UMP-AMS-F5-***********6/32
 security-zone TENANT02_GW_Inside
 0 network host address ***********6
#
object-group ip address USAP-***********/32
 0 network host address ***********
#
object-group ip address USAP-***********-14
 0 network range *********** ***********
#
object-group ip address USAP-***********/32
 0 network host address ***********
#
object-group ip address V3-MS-F5
 0 network subnet 4.190.163.0 *************
#
object-group ip address V3-MS-K8SNODE_***********/24
 0 network subnet *********** *************
#
object-group ip address V3_CORE_**********/21
 0 network subnet ********** 255.255.248.0
#
object-group ip address V3_CORE_K8SNODE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_DNS_4.190.80.51
 0 network host address 4.190.80.51
#
object-group ip address V3_DNS_4.190.80.52
 0 network host address 4.190.80.52
#
object-group ip address V3_GW_**********/21
 0 network subnet ********** 255.255.248.0
#
object-group ip address V3_GW_**********/22
 0 network subnet ********** *************
#              
object-group ip address V3_GW_CA_4.190.164.1/32
 0 network host address 4.190.164.1
#
object-group ip address V3_GW_CA_4.190.164.2/32
 0 network host address 4.190.164.2
#
object-group ip address V3_GW_K8SNODE_4.190.44.0/24
 0 network subnet 4.190.44.0 *************
#
object-group ip address V3_MS_***********/22
 0 network subnet *********** *************
#
object-group ip address V3_MS_4.190.120.101/32
 0 network host address 4.190.120.101
#
object-group ip address V3_MS_4.190.121.0/24
 security-zone TENANT02_GW_Outside
 0 network subnet 4.190.121.0 *************
#
object-group ip address V3_MS_F5_***********
 0 network host address ***********
#
object-group ip address V3_MS_F5_***********4
 security-zone TENANT02_GW_Outside
 0 network host address ***********4
#
object-group ip address V3_MS_K8SNODE_***********/24
 0 network subnet *********** *************
#
object-group ip address V3_MS_OPS_4.190.163.3
 security-zone TENANT02_GW_Outside
 0 network host address 4.190.163.3
#
object-group ip address V3MNYY_18.1.21.0/24
 security-zone TENANT01_GW_Inside
 0 network subnet 18.1.21.0 *************
#
object-group ip address V3MNYY_CORE_4.191.80.0/24
 0 network subnet 4.191.80.0 *************
#
object-group ip address VulnerabilityScan-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address W5RFTP-**********
 security-zone TENANT02_GW_Outside
 0 network host address **********
#
object-group ip address WEBDC-F5-***********2/32
 security-zone TENANT02_GW_Inside
 0 network host address ***********2
#
object-group ip address XuNi-************
 security-zone TENANT02_GW_Outside
 0 network host address ************
#
object-group ip address YaChe_4.190.45.1-4
 security-zone TENANT02_GW_Inside
 0 network range 4.190.45.1 4.190.45.4
#
object-group ip address YHTYtiaobanji_4.191.80.245
 security-zone TENANT01_GW_Outside
 0 network host address 4.191.80.245
#
object-group ip address YHTYtiaobanji_4.191.80.246
 security-zone TENANT01_GW_Outside
 0 network host address 4.191.80.246
#
object-group ip address YHTYtiaobanji_4.191.80.247
 security-zone TENANT01_GW_Outside
 0 network host address 4.191.80.247
#
object-group ip address YHTYtiaobanji_4.191.80.248
 security-zone TENANT01_GW_Outside
 0 network host address 4.191.80.248
#
object-group ip address YHTYtiaobanji_4.191.80.249
 security-zone TENANT01_GW_Outside
 0 network host address 4.191.80.249
#
object-group ip address YJ-ECC-*********-59
 security-zone TENANT02_GW_Outside
 0 network subnet ********* ***************
 100 network subnet 9.66.32.0 *************
#
object-group ip address YJ-TS-*************/24
 security-zone TENANT02_GW_Outside
 0 network subnet ************* *************
#
object-group ip address YPT-USAP-************/24
 0 network subnet ************ *************
#              
object-group ip address Yunpingtai-**********/16
 description Yunpingtai-**********/16
 0 network subnet ********** ***********
#
object-group ip address Yunpingtai-************/24
 0 network subnet ************ *************
#
object-group ip address yunwei-************-12
 security-zone TENANT02_GW_Outside
 0 network range ************ ************
#
object-group ip address yunyingclient_**********
 0 network subnet ********** *************
#
object-group ip address YZBOCC
 0 network subnet ********** *************
#
object-group ip address YZECC-*********
 0 network subnet ********* *************
#
object-group ip address "ZABBIX SERVER-************-42"
 security-zone TENANT02_GW_Outside
 0 network range ************ ************
#
object-group ip address Zookeeper
 0 network group-object Zookeeper01_***********
 1 network group-object Zookeeper02_***********
 2 network group-object Zookeeper03_***********
 3 network group-object Zookeeper04_***********
 4 network group-object Zookeeper05_***********
#
object-group ip address Zookeeper01_***********
 0 network host address ***********
#
object-group ip address Zookeeper02_***********
 0 network host address ***********
#
object-group ip address Zookeeper03_***********
 0 network host address ***********
#
object-group ip address Zookeeper04_***********
 0 network host address ***********
#
object-group ip address Zookeeper05_***********
 0 network host address ***********
#              
object-group ip address .191.80.254
 0 network host address ************
#
object-group service dns
#
object-group service TCP-10251-10252
 0 service tcp destination range 10251 10252
#
object-group service TCP-19080
 0 service tcp destination eq 19080
#
object-group service TCP-19765
 0 service tcp destination eq 19765
#
object-group service TCP-20001
 0 service tcp destination eq 20001
#
object-group service TCP-28088
 0 service tcp destination eq 28088
#
object-group service TCP-29090
 0 service tcp destination eq 29090
#              
object-group service TCP-30001
 0 service tcp destination eq 30001
#
object-group service TCP-30002
 0 service tcp destination eq 30002
#
object-group service TCP-30020
 0 service tcp destination eq 30020
#
object-group service TCP-30350
 0 service tcp destination eq 30350
#
object-group service TCP-30400
 0 service tcp destination eq 30400
#
object-group service TCP-30600
 0 service tcp destination eq 30600
#
object-group service TCP-31099
 0 service tcp destination eq 31099
#
object-group service TCP-31100
 0 service tcp destination eq 31100
#
object-group service TCP-31399
 0 service tcp destination eq 31399
#
object-group service TCP-31400
 0 service tcp destination eq 31400
#
object-group service TCP-35302
 0 service tcp destination eq 35302
#
object-group service TCP-36524
 0 service tcp destination eq 36524
#
object-group service TCP-4100
 0 service tcp destination eq 4100
#
object-group service TCP-4100-4130
 0 service tcp destination range 4100 4130
#
object-group service TCP-5000-5030
 0 service tcp destination range 5000 5030
#
object-group service TCP-5000_5007
 0 service tcp destination range 5000 5007
#
object-group service TCP-5001-5010
 0 service tcp destination range 5001 5010
#
object-group service TCP-5001-5030
 0 service tcp destination range 5001 5030
#
object-group service TCP-52701
 0 service tcp destination eq 52701
#
object-group service TCP-52704
 0 service tcp destination eq 52704
#
object-group service TCP-6379
 0 service tcp destination eq 6379
#
object-group service TCP-6443
 0 service tcp destination eq 6443
#
object-group service TCP-7003
 0 service tcp destination eq 7003
#              
object-group service TCP-7004
 0 service tcp destination eq 7004
#
object-group service TCP-7005
 0 service tcp destination eq 7005
#
object-group service TCP-8013
 0 service tcp destination eq 8013
#
object-group service TCP-8080
 0 service tcp destination eq 8080
#
object-group service TCP-8085
 0 service tcp destination eq 8085
#
object-group service TCP-8087
 0 service tcp destination eq 8087
#
object-group service TCP-8090
 0 service tcp destination eq 8090
#
object-group service TCP-8443
 0 service tcp destination eq 8443
#
object-group service TCP-8888
 0 service tcp destination eq 8888
#
object-group service TCP-8889
 0 service tcp destination eq 8889
#
object-group service TCP-9090
 0 service tcp destination eq 9090
#
object-group service TCP-9092
 0 service tcp destination eq 9092
#
object-group service TCP-9100
 0 service tcp destination eq 9100
#
object-group service TCP-9110
 0 service tcp destination eq 9110
#
object-group service TCP-9120
 0 service tcp destination eq 9120
#
object-group service TCP_10250
 0 service tcp destination eq 10250
#
object-group service TCP_10255
 0 service tcp destination eq 10255
#
object-group service TCP_12049
 0 service tcp destination eq 12049
#
object-group service TCP_23000
 0 service tcp destination eq 23000
#
object-group service TCP_2379
 0 service tcp destination eq 2379
#
object-group service TCP_25601
 0 service tcp destination eq 25601
#
object-group service TCP_28070
 0 service tcp destination eq 28070
#
object-group service TCP_28080
 0 service tcp destination eq 28080
#              
object-group service TCP_28081
 0 service tcp destination eq 28081
#
object-group service TCP_28088
 0 service tcp destination eq 28088
#
object-group service TCP_28180
 0 service tcp destination eq 28180
#
object-group service TCP_29000-29999
 0 service tcp destination range 29000 29999
#
object-group service TCP_29092
 0 service tcp destination eq 29092
#
object-group service TCP_29411
 0 service tcp destination eq 29411
#
object-group service TCP_30514
 0 service tcp destination eq 30514
#
object-group service TCP_31050-31051
 0 service tcp destination range 31050 31051
#
object-group service TCP_31306
 0 service tcp destination eq 31306
#
object-group service TCP_3191
 0 service tcp destination eq 3191
#
object-group service TCP_3389
 0 service tcp destination eq 3389
#
object-group service TCP_3555
 0 service tcp destination eq 3555
#
object-group service TCP_3558
 0 service tcp destination eq 3558
#
object-group service TCP_5003
 0 service tcp destination eq 5003
#
object-group service TCP_5004
 0 service tcp destination eq 5004
#
object-group service TCP_5480
 0 service tcp destination eq 5480
#
object-group service TCP_6370
 0 service tcp destination eq 6370
#
object-group service TCP_6677
 0 service tcp destination eq 6677
#
object-group service TCP_7001
 0 service tcp destination eq 7001
#
object-group service TCP_7100
 0 service tcp destination eq 7100
#
object-group service TCP_7788
 0 service tcp destination eq 7788
#
object-group service TCP_8000
 0 service tcp destination eq 8000
#
object-group service TCP_8001
 0 service tcp destination eq 8001
#              
object-group service TCP_8002
 0 service tcp destination eq 8002
#
object-group service TCP_8080
 0 service tcp destination eq 8080
#
object-group service TCP_8082
 0 service tcp destination eq 8082
#
object-group service TCP_8086
 0 service tcp destination eq 8086
#
object-group service TCP_8088
 0 service tcp destination eq 8088
#
object-group service TCP_8112
 0 service tcp destination eq 8112
#
object-group service TCP_8472
 0 service tcp destination eq 8472
#
object-group service TCP_9100
 0 service tcp destination eq 9100
#
object-group service TCP_9443
 0 service tcp destination eq 9443
#
object-group service TCP_9600
 0 service tcp destination eq 9600
#
object-group service UDP-1812-1813
 0 service udp destination eq 1812
 5 service udp destination eq 1813
#
object-group service UDP_123
 0 service udp destination eq 123
#
object-group service UDP_8472
 0 service udp destination eq 8472
#
interface Bridge-Aggregation1
 description "TO-XWHPD-NE1ACL-SW01 Agg1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 link-aggregation mode dynamic
#
interface Bridge-Aggregation3
 description "TO-XWHPD-NE1DIL-SW01 Agg2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 link-aggregation mode dynamic
#
interface Route-Aggregation100
 description IRF-BFD_MAD
 mad bfd enable
 mad ip address ************* *************** member 1
 mad ip address ************* *************** member 2
#
interface NULL0
#
interface GigabitEthernet1/0/1/1
 port link-mode route
 description "TO-XWHPD-NE1DIL-FW01 G2/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/0/1/2
 port link-mode route
#
interface GigabitEthernet1/0/1/3
 port link-mode route
#
interface GigabitEthernet1/0/1/4
 port link-mode route
#
interface GigabitEthernet1/9/1/1
 port link-mode route
 description "TO-XWHPD-NE1DIL-FW01 G2/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/9/1/2
 port link-mode route
#
interface GigabitEthernet1/9/1/3
 port link-mode route
#
interface GigabitEthernet1/9/1/4
 port link-mode route
#
interface GigabitEthernet2/0/1/1
 port link-mode route
 description "TO-XWHPD-NE1DIL-FW01 G1/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/0/1/2
 port link-mode route
#
interface GigabitEthernet2/0/1/3
 port link-mode route
#
interface GigabitEthernet2/0/1/4
 port link-mode route
#
interface GigabitEthernet2/9/1/1
 port link-mode route
 description "TO-XWHPD-NE1DIL-FW01 G1/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/9/1/2
 port link-mode route
#
interface GigabitEthernet2/9/1/3
 port link-mode route
#              
interface GigabitEthernet2/9/1/4
 port link-mode route
#
interface M-GigabitEthernet1/0/0/0
 ip binding vpn-instance management
 ip address ********** *************
#
interface Ten-GigabitEthernet2/0/1/5
 port link-mode route
#
interface Ten-GigabitEthernet2/9/1/5
 port link-mode route
#
interface Ten-GigabitEthernet1/0/1/5
 port link-mode bridge
 port access vlan 199
#
interface Ten-GigabitEthernet1/0/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/2/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/2/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/2/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/9
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T1/0/1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/10
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T1/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/11
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T1/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/5
 port link-mode bridge
 port access vlan 199
#              
interface Ten-GigabitEthernet1/9/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/3/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/3/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/3/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/9
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T1/0/5"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/10
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T1/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/11
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T1/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/2/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/2/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/2/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/9
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T2/0/1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/10
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T2/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/11
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T2/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/3/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/3/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/3/0/4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/9
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T2/0/5"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/10
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T2/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/11
 port link-mode bridge
 description "TO-XWHPD-NE1ACL-SW01 T2/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3001 to 3005
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/12
 description "TO-XWHPD-NE1DIL-FW01 T2/0/1/12 IRF"
#
interface Ten-GigabitEthernet1/9/1/12
 description "TO-XWHPD-NE1DIL-FW01 T2/9/1/12 IRF"
#
interface Ten-GigabitEthernet2/0/1/12
 description "TO-XWHPD-NE1DIL-FW01 T1/0/1/12 IRF"
#
interface Ten-GigabitEthernet2/9/1/12
 description "TO-XWHPD-NE1DIL-FW01 T1/9/1/12 IRF"
#
interface Blade1/6/0/1
#
interface Blade1/6/0/2
#
interface Blade2/6/0/1
#
interface Blade2/6/0/2
#
interface Blade-Aggregation1
 link-aggregation blade Blade4fw
#
interface Blade-Aggregation257
#
security-zone name Local
#
security-zone name Trust
 import interface Route-Aggregation100
#
security-zone name DMZ
#
security-zone name Untrust
#
security-zone name Management
 import interface M-GigabitEthernet1/0/0/0
#
security-zone name TENANT01_GW_Inside
 import interface Bridge-Aggregation1 vlan 3001
#              
security-zone name TENANT01_GW_Outside
 import interface Bridge-Aggregation3 vlan 3001
#
security-zone name TENANT02_GW_Inside
 import interface Bridge-Aggregation1 vlan 3002
#
security-zone name TENANT02_GW_Outside
 import interface Bridge-Aggregation3 vlan 3002
#
security-zone name TENANT03_GW_Inside
 import interface Bridge-Aggregation1 vlan 3003
#
security-zone name TENANT03_GW_Outside
 import interface Bridge-Aggregation3 vlan 3003
#
security-zone name TENANT04_GW_Inside
 import interface Bridge-Aggregation1 vlan 3004
#
security-zone name TENANT04_GW_Outside
 import interface Bridge-Aggregation3 vlan 3004
#
security-zone name TENANT05_GW_Inside
 import interface Bridge-Aggregation1 vlan 3005
#
security-zone name TENANT05_GW_Outside
 import interface Bridge-Aggregation3 vlan 3005
#
security-zone name TEST_Inside
 import interface Ten-GigabitEthernet1/0/1/5 vlan 199
#
security-zone name Test_Outside
 import interface Ten-GigabitEthernet1/9/1/5 vlan 199
#
zone-pair security source Local destination Trust
 packet-filter 2000
#
zone-pair security source Trust destination Local
 packet-filter 2000
#
 scheduler logfile size 16
#
line class console
 user-role network-admin
#
line class vty
 user-role network-operator
#
line con 1/0 1/1
 authentication-mode scheme
 user-role network-admin
#
line con 2/0 2/1
 user-role network-admin
#
line con 1/6
 authentication-mode scheme
 user-role network-admin
#
line con 2/6
 user-role network-admin
#
line vty 0 63
 authentication-mode scheme
 user-role network-admin
#
 ip route-static vpn-instance management 0.0.0.0 0 ***********
#
 info-center timestamp loghost iso
 info-center loghost source M-GigabitEthernet1/0/0/0
 info-center loghost vpn-instance MGMT ***********
 info-center loghost vpn-instance management **********
 info-center loghost vpn-instance MGMT **********3
 info-center loghost vpn-instance MGMT ************
 info-center loghost vpn-instance MGMT ************
 info-center loghost vpn-instance MGMT *************
#
 mad exclude interface M-GigabitEthernet1/0/0/0
#
 snmp-agent
 snmp-agent local-engineid 800063A28074EAC8346B3D00000001
 snmp-agent community read cipher $c$3$MoTi4uF+bI3PbvUHZkM/O45lloxnHKFQSdkz5BN6 acl name ACL-SNMP
 snmp-agent sys-info location fw01-w5r-D-13-3-20&D-14-3-20
 snmp-agent sys-info version v2c v3 
 snmp-agent target-host trap address udp-domain ************ vpn-instance MGMT params securityname cslc_snmp v2c
 snmp-agent target-host trap address udp-domain ************* vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain *********** params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain ********** vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent trap enable arp 
 snmp-agent trap enable radius 
 snmp-agent trap enable stp 
 snmp-agent trap enable syslog 
 snmp-agent trap source M-GigabitEthernet1/0/0/0
#
 ssh server enable
#
redundancy group 1
 member failover group 1
 node 1
  bind chassis 1
  priority 100
  track 101 interface Blade1/6/0/1
  track 102 interface Blade1/6/0/2
 node 2
  bind chassis 2
  priority 50
  track 201 interface Blade2/6/0/1
  track 202 interface Blade2/6/0/2
#
 ntp-service enable
 ntp-service unicast-server ********** vpn-instance management source M-GigabitEthernet1/0/0/0
#
acl basic 2000
 rule 0 permit source *********** *********
#
acl basic name ACL-SNMP
 description "Network monitor system"
 rule 0 permit vpn-instance management source ********** *********
 rule 5 permit vpn-instance management source ************* 0
 rule 10 permit vpn-instance management source *********** *********
 rule 15 permit vpn-instance management source *********** 0
 rule 20 permit vpn-instance MGMT source ************ 0
 rule 1000 deny vpn-instance management
#
domain system
#
 domain default enable system
#
role name level-0
 description Predefined level-0 role
#
role name level-1
 description Predefined level-1 role
#
role name level-2
 description Predefined level-2 role
#
role name level-3
 description Predefined level-3 role
#
role name level-4
 description Predefined level-4 role
#
role name level-5
 description Predefined level-5 role
#
role name level-6
 description Predefined level-6 role
#
role name level-7
 description Predefined level-7 role
#
role name level-8
 description Predefined level-8 role
#
role name level-9
 description Predefined level-9 role
#
role name level-10
 description Predefined level-10 role
#
role name level-11
 description Predefined level-11 role
#
role name level-12
 description Predefined level-12 role
#
role name level-13
 description Predefined level-13 role
#
role name level-14
 description Predefined level-14 role
#
role name systemadmin
 rule 1 permit read web-menu m_monitor/m_atklog/m_blacklistlog
 rule 2 permit read web-menu m_monitor/m_atklog/m_singleatk
 rule 3 permit read web-menu m_monitor/m_atklog/m_scanatk
 rule 4 permit read web-menu m_monitor/m_atklog/m_floodatk
 rule 5 permit read web-menu m_monitor/m_atklog/m_threatlog
 rule 6 permit read web-menu m_monitor/m_atklog/m_urllog
 rule 7 permit read web-menu m_monitor/m_atklog/m_filefilterlog
 rule 8 permit read web-menu m_monitor/m_atklog/m_zonepairlog
 rule 9 permit read web-menu m_monitor/m_auditlogs/m_auditimchatlog
 rule 10 permit read web-menu m_monitor/m_auditlogs/m_auditcommunitylog
 rule 11 permit read web-menu m_monitor/m_auditlogs/m_auditsearchenginelog
 rule 12 permit read web-menu m_monitor/m_auditlogs/m_auditmaillog
 rule 13 permit read web-menu m_monitor/m_auditlogs/m_auditfiletransferlog
 rule 14 permit read web-menu m_monitor/m_auditlogs/m_auditrelaxstocklog
 rule 15 permit read web-menu m_monitor/m_auditlogs/m_auditotherapplog
 rule 16 permit read web-menu m_monitor/m_monitorlog/m_trafficlog
 rule 17 permit read web-menu m_monitor/m_rank/m_trafficrank
 rule 18 permit read web-menu m_monitor/m_rank/m_threadrank
 rule 19 permit read web-menu m_monitor/m_rank/m_urlfilterrank
 rule 20 permit read web-menu m_monitor/m_rank/m_ffilterrank
 rule 21 permit read web-menu m_monitor/m_rank/m_securityaudit
 rule 22 permit read web-menu m_monitor/m_rank/m_lb_serverreport
 rule 23 permit read web-menu m_monitor/m_rank/m_lb_linkreport
 rule 24 permit read web-menu m_monitor/m_rank/m_lb_dnsproxyreport
 rule 25 permit read web-menu m_monitor/m_trend/m_traffictrend
 rule 26 permit read web-menu m_monitor/m_trend/m_threadtrend
 rule 27 permit read web-menu m_monitor/m_trend/m_urlfiltertrend
 rule 28 permit read web-menu m_monitor/m_trend/m_ffiltertrend
 rule 29 permit read web-menu m_monitor/m_trend/m_lb_urltrend
 rule 30 permit read web-menu m_monitor/m_report
 rule 31 permit read web-menu m_monitor/m_session
 rule 32 permit read web-menu m_monitor/m_lb_dnscaches
 rule 33 permit read web-menu m_monitor/m_userinfocenter
 rule 34 permit read web-menu m_policy/m_firewall/m_secpolicy
 rule 35 permit read web-menu m_policy/m_firewall/m_redundancyrules
 rule 36 permit read web-menu m_policy/m_firewall/m_targetpolicy
 rule 37 permit read web-menu m_policy/m_attackdefense/m_atkpolicy
 rule 38 permit read web-menu m_policy/m_attackdefense/m_clientverifyprotectip
 rule 39 permit read web-menu m_policy/m_attackdefense/m_blacklistmanual
 rule 40 permit read web-menu m_policy/m_attackdefense/m_whitelistmanual
 rule 41 permit read web-menu m_policy/m_attackdefense/m_clientverifyzone
 rule 42 permit read web-menu m_policy/m_attackdefense/m_connlimitpolicies
 rule 43 permit read web-menu m_policy/m_attackdefense/m_urpf
 rule 44 permit read web-menu m_policy/m_nat/m_natoutboundconfig
 rule 45 permit read web-menu m_policy/m_nat/m_natserverconfig
 rule 46 permit read web-menu m_policy/m_nat/m_natstaticchange
 rule 47 permit read web-menu m_policy/m_nat/m_natoutbound444config
 rule 48 permit read web-menu m_policy/m_nat/m_natoutboundstatic444config
 rule 49 permit read web-menu m_policy/m_nat/m_natsettings
 rule 50 permit read web-menu m_policy/m_aft/m_aftaddrgrp
 rule 51 permit read web-menu m_policy/m_aft/m_aftnat64
 rule 52 permit read web-menu m_policy/m_aft/m_aftoutbound
 rule 53 permit read web-menu m_policy/m_aft/m_aftset
 rule 54 permit read web-menu m_policy/m_appaudit/m_auditpolicy
 rule 55 permit read web-menu m_policy/m_appaudit/m_keywordgroups
 rule 56 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthpolicy
 rule 57 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthchannel
 rule 58 permit read web-menu m_policy/m_bandwidthmanagement/m_interfacebandwidth
 rule 59 permit read web-menu m_policy/m_loadbalance/m_lb_globalconfig
 rule 60 permit read web-menu m_policy/m_loadbalance/m_lb_server
 rule 61 permit read web-menu m_policy/m_loadbalance/m_lb_link
 rule 62 permit read web-menu m_policy/m_netshare/m_netsharepolicy
 rule 63 permit read web-menu m_policy/m_netshare/m_netsharestatus
 rule 64 permit read web-menu m_policy/m_proxymanagement/m_proxypolicy
 rule 65 permit read web-menu m_policy/m_proxymanagement/m_whitelisthostname
 rule 66 permit read web-menu m_policy/m_proxymanagement/m_sslcertificate
 rule 67 permit read web-menu m_resource/m_healthmonitor
 rule 68 permit read web-menu m_resource/m_user/m_usercontrol
 rule 69 permit read web-menu m_resource/m_user/m_authentication
 rule 70 permit read web-menu m_resource/m_user/m_access
 rule 71 permit read web-menu m_resource/m_dpi/m_ipscfg
 rule 72 permit read web-menu m_resource/m_dpi/m_antiviruscfg
 rule 73 permit read web-menu m_resource/m_dpi/m_dfltcfg
 rule 74 permit read web-menu m_resource/m_dpi/m_ufltcfg
 rule 75 permit read web-menu m_resource/m_dpi/m_ffltcfg
 rule 76 permit read web-menu m_resource/m_dpi/m_apprecognition
 rule 77 permit read web-menu m_resource/m_dpi/m_securityaction
 rule 78 permit read web-menu m_resource/m_dpi/m_dpicfg
 rule 79 permit read web-menu m_resource/m_objectgroup/m_ipv4objectgroup
 rule 80 permit read web-menu m_resource/m_objectgroup/m_ipv6objectgroup
 rule 81 permit read web-menu m_resource/m_objectgroup/m_macobjectgroup
 rule 82 permit read web-menu m_resource/m_objectgroup/m_serviceobjectgroup
 rule 83 permit read web-menu m_resource/m_objectgroup/m_timerange
 rule 84 permit read web-menu m_resource/m_acl/m_ipv4acl
 rule 85 permit read web-menu m_resource/m_acl/m_ipv6acl
 rule 86 permit read web-menu m_resource/m_acl/m_macacl
 rule 87 permit read web-menu m_resource/m_ssl/m_sslserver
 rule 88 permit read web-menu m_resource/m_ssl/m_sslclient
 rule 89 permit read web-menu m_resource/m_ssl/m_ssladvancesettiing
 rule 90 permit read web-menu m_resource/m_publickey/m_publickeylocal
 rule 91 permit read web-menu m_resource/m_publickey/m_publickeypeer
 rule 92 permit read web-menu m_resource/m_pki_cert/m_pki
 rule 93 permit read web-menu m_resource/m_pki_cert/m_certificatepolicy
 rule 94 permit read web-menu m_resource/m_pki_cert/m_certificatesubject
 rule 95 permit read web-menu m_network/m_vrf
 rule 96 permit read web-menu m_network/m_if/m_interface
 rule 97 permit read web-menu m_network/m_if/m_inlineall
 rule 98 permit read web-menu m_network/m_if/m_lagg
 rule 99 permit read web-menu m_network/m_seczone
 rule 100 permit read web-menu m_network/m_link/m_vlan
 rule 101 permit read web-menu m_network/m_link/m_mac_sum
 rule 102 permit read web-menu m_network/m_dns_sum/m_dnshosts
 rule 103 permit read web-menu m_network/m_dns_sum/m_dns
 rule 104 permit read web-menu m_network/m_dns_sum/m_ddns
 rule 105 permit read web-menu m_network/m_dns_sum/m_dnsadvance
 rule 106 permit read web-menu m_network/m_ip_net/m_ip
 rule 107 permit read web-menu m_network/m_ip_net/m_arp
 rule 108 permit read web-menu m_network/m_ipv6_net/m_ipv6
 rule 109 permit read web-menu m_network/m_ipv6_net/m_nd
 rule 110 permit read web-menu m_network/m_vpn/m_gre
 rule 111 permit read web-menu m_network/m_vpn/m_ipsec
 rule 112 permit read web-menu m_network/m_vpn/m_advpn
 rule 113 permit read web-menu m_network/m_vpn/m_l2tp
 rule 114 permit read web-menu m_network/m_sslvpn/m_sslvpn_context
 rule 115 permit read web-menu m_network/m_sslvpn/m_sslvpn_gateway
 rule 116 permit read web-menu m_network/m_sslvpn/m_sslvpn_ipv4addrpool
 rule 117 permit read web-menu m_network/m_sslvpn/m_sslvpn_snatpool
 rule 118 permit read web-menu m_network/m_sslvpn/m_sslvpn_acif
 rule 119 permit read web-menu m_network/m_sslvpn/m_sslvpn_globalconfig
 rule 120 permit read web-menu m_network/m_sslvpn/m_sslvpn_tempmanagement
 rule 121 permit read web-menu m_network/m_sslvpn/m_sslvpn_statistics
 rule 122 permit read web-menu m_network/m_routing/m_routingtable
 rule 123 permit read web-menu m_network/m_routing/m_staticrouting
 rule 124 permit read web-menu m_network/m_routing/m_policyrouting
 rule 125 permit read web-menu m_network/m_routing/m_ospf
 rule 126 permit read web-menu m_network/m_routing/m_bgp
 rule 127 permit read web-menu m_network/m_routing/m_rip
 rule 128 permit read web-menu m_network/m_multicast/m_multicastrouting
 rule 129 permit read web-menu m_network/m_multicast/m_pim
 rule 130 permit read web-menu m_network/m_multicast/m_igmp
 rule 131 permit read web-menu m_network/m_dhcp/m_dhcpservice
 rule 132 permit read web-menu m_network/m_dhcp/m_dhcppool
 rule 133 permit read web-menu m_network/m_ipservice/m_ssh
 rule 134 permit read web-menu m_network/m_ipservice/m_ntp
 rule 135 permit read web-menu m_network/m_ipservice/m_ftp
 rule 136 permit read web-menu m_network/m_ipservice/m_telnet
 rule 137 permit read web-menu m_device/m_diagnosis/m_ping
 rule 138 permit read web-menu m_device/m_diagnosis/m_tracert
#
user-group system
#
local-user admin class manage
 password hash $h$6$qs/pAXpu3E5qiB65$A0a1NA10+m83a/ZckUERayjtXqn6PWQ7qq0AXm63xcIkxgLlABnvtFjvr3YuaOVQ4DVJwZmyKJQUJYGrtTzspA==
 service-type ssh terminal https
 authorization-attribute user-role level-3
 authorization-attribute user-role network-admin
 authorization-attribute user-role network-operator
#
local-user operator class manage
 password hash $h$6$nlK+YorxghGZjvXJ$wCRbpMHwKNzKVLgLcCI477hAfaBDH8EuJQDJ3RsdZjZX4F2130sVM2TK/rOAdb9gbVtMvY2RfD11qqFrEj8Kbg==
 service-type ssh terminal https
 authorization-attribute user-role level-1
 authorization-attribute user-role systemadmin
#
 session statistics enable
 session synchronization enable 
#
 ip https enable
#
 inspect optimization no-acsignature disable
 inspect optimization raw disable
 inspect optimization uncompress disable
 inspect optimization url-normalization disable
 inspect optimization chunk disable
#
security-policy ip
 rule 157 name YZBOCC_Deny
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip YZBOCC
  source-ip YZECC-*********
  service TCP_31306
  service TCP_3558
  service ssh
  service TCP_3555
  service TCP_3389
 rule 96 name ELP_to_G3·
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip ELP-**********-45
  source-ip ELP-*********-3
  source-ip CSLC-**********-143
  source-ip CSLC-**********-213
  destination-ip G3ELP-***********
  service http
 rule 0 name 1
  description V3-6
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_**********/21
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_CORE_**********/21
  service TCP_6370
  service TCP_5004
  service TCP_5003
  service TCP-5000_5007
  service TCP-5001-5030
 rule 2 name 2
  description V3-1
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip harbor_F5_4.190.163.2
  destination-ip OPS
  destination-ip HARBOR
  destination-ip V3_MS_OPS_4.190.163.3
  service http
  service https
 rule 3 name 3
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip NAS_4.191.41.1
 rule 11 name 4
  description V3-3
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip Zookeeper
  service TCP_3191
 rule 12 name 5
  description V3-4
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip Redis_feioltp
  destination-ip Redis_oltp
  service TCP_7001
 rule 13 name 6
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_MS_K8SNODE_***********/24
  service TCP_10250
  service TCP_10255
  service TCP_8080
  service TCP_2379
 rule 14 name 7
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service UDP_8472
 rule 15 name 8
  description V3-20
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip TIDB_**********/24
  service TCP_31306
  service ssh
 rule 16 name 9
  description V3-11
  action pass  
  source-ip *********/16
  destination-ip *********/16
  service ssh
 rule 17 name 10
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip CASGW_4.190.162.4
  destination-ip CAS_F5_4.190.162.3
  service TCP_28080
  service TCP_28081
  service https
 rule 18 name 11
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_**********/21
  destination-ip V3_MS_***********/22
  service TCP_28070
 rule 19 name 12
  description V3-21
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip BOCC
  source-ip 4A-*********
  source-ip YJ-TS-*************/24
  source-ip *********/16
  destination-ip *********/16
  service TCP_8088
  service TCP_31306
  service http
  service https
  service ssh
  service TCP_28081
  service TCP_28080
  service TCP_3558
  service TCP_8112
  service TCP_3555
  service TCP_8086
  service TCP_7001
  service TCP_3389
  service TCP_8080
  service TCP-28088
  service TCP_28070
  service TCP_29000-29999
  service TCP_8000
  service TCP_5480
  service TCP_9443
  service TCP_28180
  service TCP_23000
  service TCP_29411
  service TCP_25601
  service TCP_9600
  service TCP_31050-31051
  service TCP_8082
  service TCP-20001
  service TCP-7004
  service TCP-7005
 rule 25 name 13
  description V3-9 V3-17
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_MS_K8SNODE_***********/24
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service UDP_8472
 rule 26 name 14
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip JianKong_4.190.121.0
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service TCP_12049
  service TCP_9100
 rule 27 name 15
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_MS_K8SNODE_***********/24
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip nginx01_4.190.120.51/32
  destination-ip nginx02_4.190.120.52/32
  service http
 rule 31 name 16
  action pass
  source-zone TENANT01_GW_Outside
  destination-zone TENANT01_GW_Inside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip NAS_4.191.41.1
 rule 35 name V3-2
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip HARBOR
  source-ip harbor_F5_4.190.163.2
  source-ip OPS
  source-ip V3_MS_OPS_4.190.163.3
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service http
  service https
 rule 36 name V3-5
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_**********/21
  destination-ip Configcenter
  service TCP_28081
 rule 37 name V3-18
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G2OCS-**********
  source-ip **********-162
  destination-ip *********/16
  service TCP_31306
  service TCP_3558
  service ssh
  service TCP_3555
 rule 38 name V3-19
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip yunwei-************-12
  destination-ip *********/16
  service ssh
  service TCP_3555
  service TCP_3558
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP_2379
  service UDP_8472
  service TCP_8080
  service TCP_8472
 rule 50 name V3-20
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  source-ip G3_GW_**********/24
  source-ip G3-GW-**********/22
  destination-ip NAS_4.191.40.3
  destination-ip NAS-**********-6
 rule 51 name V3-22
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3OPERVM
  destination-ip *********/16
  service TCP_6370
  service TCP_2379
  service TCP_5003
  service TCP_3555
  service TCP_3558
  service TCP_3191
  service TCP_7001
  service UDP_8472
  service TCP_8472
  service TCP_8080
  service http
  service ssh
  service TCP_31306
  service TCP-10251-10252
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service TCP_7100
  service https
 rule 39 name V3-25
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip T1_**********
  destination-ip T1_NTP_***********
  service ntp
  service http
 rule 40 name V3-26
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip ***********
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service TCP_8086
 rule 149 name RMOAS
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  source-ip G3SSL-*********/24
  destination-ip F5-***********
  service TCP_8086
 rule 41 name V3-27
  action pass
  counting enable
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  source-ip DMZ-SSL-*********/24
  destination-ip ***********
  service TCP_8086
 rule 42 name V3-29
  action pass  
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip "GW ZABBIX PROXY-************"
  destination-ip "ZABBIX SERVER-************-42"
  destination-ip ************
  service TCP_31050-31051
 rule 43 name V3-30
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip "ZABBIX SERVER-************-42"
  source-ip ************
  destination-ip "GW ZABBIX PROXY-************"
  service TCP_31050-31051
 rule 44 name V3-36
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G2WEBDC-*********
  destination-ip G3IHSGW-***********
  service TCP_8088
 rule 45 name V3-37
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G2ELP-********
  destination-ip G3ELP-***********
  service http
 rule 46 name V3-40
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip W5RFTP-**********
  destination-ip G2FTP-**********
  destination-ip CSLC-*******
  service ssh
 rule 47 name V3-42
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_MS_***********/22
  destination-ip ************
  service TCP_28080
  service https
  service http
 rule 48 name V3-44
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip V3_MS_***********/22
  service TCP_29092
 rule 49 name V3-45
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_CORE_**********/21
  destination-ip ***********
  service TCP_28081
  service TCP_28070
 rule 55 name V3-46
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_10250
  service TCP_10255
  service TCP-30350
 rule 56 name V3-47
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service TCP_10250
  service TCP_10255
 rule 58 name V3-48
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_MS_***********/22
  destination-ip V3_GW_**********/21
  service TCP-29090
 rule 59 name V3-49
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_9100
 rule 72 name V3-50
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3-MS-K8SNODE_***********/24
  destination-ip V3_GW_**********/21
  service TCP-5000_5007
  service TCP_3191
  service TCP_9100
  service TCP_7001
 rule 73 name V3-51
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_MS_F5_***********4
  destination-ip V3_MS_4.190.121.0/24
  service TCP-28088
  service https
 rule 75 name V3_52
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3-MS-K8SNODE_***********/24
  destination-ip redis-**********-9
  service TCP_7001
 rule 76 name V3-53
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_CORE_**********/21
  destination-ip ***********
  service TCP_28070
 rule 77 name V3-54
  description Prometheus

  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service TCP_29000-29999
 rule 81 name V3-55
  action pass  
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip hermes-***********-16
  service TCP_5003
 rule 82 name V3-56
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_MS_4.190.121.0/24
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service TCP-30600
 rule 83 name V3-57
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *******/8
  destination-ip T1_**********
  destination-ip T1_NTP_***********
  service http
  service ntp
 rule 1 name "V3_GW_4.190.44.0/24 TO Public_NAS_4.191.41.1"
  action pass  
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip NAS_4.191.41.1
 rule 7 name "V3-GW-K8SNODE To V3-MS-K8SNODE"
  description V3-7
  action pass
  counting enable
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3-MS-K8SNODE_***********/24
  service TCP_2379
  service TCP_8080
  service TCP_10255
  service TCP_10250
  service TCP-6443
 rule 9 name "V3-GW-K8SNODE To V3-MS-F5-***********"
  description V3-16
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3_MS_F5_***********
  service TCP_8080
 rule 22 name "V3_MS_K8SNODE To V3_GW_K8SNODE"
  description V3-8
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service TCP_2379
  service TCP_8080
  service TCP_10250
  service TCP_10255
 rule 8 name "V3-GW-K8SNODE To V3-MS&CORE-K8SNODE Flannel"
  description V3-10
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip V3-MS-K8SNODE_***********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service UDP_8472
 rule 6 name "V3_GW TO V3_DNS"
  description V3-15
  action pass
  counting enable
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  destination-ip V3_DNS_4.190.80.51
  destination-ip V3_DNS_4.190.80.52
  service dns-tcp
  service dns-udp
 rule 10 name ospf
  description V3-12
  action pass
  counting enable
  service ospf
  service ping
 rule 74 name V3-52
  description WX-linShi
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip **********1
  source-ip ***********
  destination-ip TIDB_**********/24
  service TCP-30400
  service TCP-36524
 rule 29 name 
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip T1_**********/32
  destination-ip *********/16
  service ssh
 rule 53 name Test
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip ***********/22
  service TCP-6443
 rule 71 name G2_TRANSROUTE_V3_30400
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G2_TRANSROUTE_**********-84
  source-ip XuNi-************
  destination-ip V3_GW_**********/22
  service TCP-30400
  service ssh
 rule 87 name G3-V1.3-20200429-01
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT03_GW_Inside
  source-ip G2OCS-**********
  destination-ip *********/16
  destination-ip G3_*********/16
  service ssh
  service TCP_31306
  service TCP_3555
  service TCP_3558
 rule 88 name G3-V1.3-20200429-02
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip BOCC&4A
  destination-ip G3_*********/16
  service ssh
  service TCP_31306
  service TCP_3555
  service TCP_3558
  service TCP_8080
  service http
  service https
  service TCP_8000
 rule 89 name G3-V1.3-20200429-03
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip F5-***********
  service TCP_8086
 rule 90 name G3-V1.3-20200429-04
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G2_TRANSROUTE_**********-84
  destination-ip G3_GW_**********/24
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service http
 rule 91 name G3-V1.3-20200429-05
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G2_WEBDC
  destination-ip G3IHSGW-***********
  service TCP_8088
 rule 92 name G3-V1.3-20200429-06
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip G2_NTP
  service ntp
  service UDP_123
 rule 93 name G3-V1.3-20200429-07
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT03_GW_Inside
  source-ip G2_WEBDC
  destination-ip G3_**********/24
  service TCP_8082
 rule 94 name G3-V1.3-20200429-08
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip W5RFTP-**********
  destination-ip G2FTP-**********
  destination-ip CSLC_*******/32
  service ssh
 rule 95 name G3-V1.3-20200429-09
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip G2_AMSDB_*********/32
  service TCP_3555
 rule 97 name CSLC-baoleiji
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-baoleiji-**********
  source-ip CSLC-*********
  destination-ip *********/16
  service ssh
  service https
  service http
  service TCP_3389
  service TCP-8888
  service TCP-8889
  service TCP-8013
  service TCP-8090
  service TCP_8000
  service TCP_3555
  service TCP_3558
  service TCP_31306
 rule 98 name TO_G2FTP
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip G2FTP-**********
  service ftp
  service ssh
 rule 99 name To_CSLC-ƽ̨
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip CSLC-DIP-***********
  service TCP-9092
 rule 100 name AMS-MATGW
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3AMS-************-102
  destination-ip G3MATGW-F5-***********
  service TCP-8085
 rule 101 name F5-TO-G2TRANSROUTE
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip GW-F5-***********/24
  destination-ip G2_TRANSROUTE_**********-84
  service TCP-8085
  service TCP_8082
  service TCP-8087
 rule 102 name anquanlousao
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip FOC-*********
  source-ip FOC-***********
 rule 103 name G3-R141b
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip G3_GW_**********/24
  destination-ip CSLC-OPENAPI-**********
  service TCP_7001
 rule 104 name RMX1.1.0_01
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3WCSINFO-**********-22
  destination-ip G3WCSINFOSFTP-***********-42
  destination-ip G3WCSINFOSFTP-F5-***********
  service ssh
 rule 105 name RMX1.1.0_02
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3WCSINFO-**********-22
  service TCP_7001
 rule 106 name JCJK1.3.7
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3MONITORGAIA-*************-162
  destination-ip V3_GW_K8SNODE_4.190.44.0/24
  service http
 rule 139 name MAIL
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip Mail-*********-2
  service smtp
 rule 107 name TO-SYSLOG
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip SYSLOG-F5-************
  destination-ip Solarwinds-**********
  service syslog
 rule 108 name To_G3_NTP
  action pass
  counting enable
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip G3_CORE_NTP_***********1-252
  service ntp
 rule 109 name SDAS-V2.15.1_01
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2GRSAS-***********-32
  destination-ip SDAS-BLIDB-************
  service TCP_31306
 rule 110 name SSM-Ansbile
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip Ansbile-************
  source-ip *************
  destination-ip *********/16
  service ssh
 rule 111 name IRM1.15.0_01
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3SSL-*********/24
  source-ip *********/16
  destination-ip IRM-F5-***********6
  service TCP-8443
 rule 112 name IRM1.15.0_02
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2IRMAS-***********-62
  destination-ip ***********-16
  destination-ip SBSG2IRMDBVIP-**********
  service TCP_3555
 rule 113 name IRM1.15.0_03
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2IRMAS-***********-62
  destination-ip CORE-F5-***********/24
  service https
 rule 115 name IRM1.15.0_04
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3OPERVM01-**********
  source-ip G3OPERVM02-**********
  destination-ip *********/16
  service TCP_3555
  service TCP_3558
  service TCP_3191
  service TCP_5003
  service TCP_6370
  service TCP_7001
  service TCP_8080
  service UDP_8472
  service TCP_2379
  service ssh
  service http
 rule 114 name OPSFTP
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip SBSG2OPSFTP01-************
  service ftp  
  service ssh
 rule 121 name G3-R160_06
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip SBSG2OTJob-***********
  destination-ip *********/16
  service ssh
 rule 116 name G3-R160_01
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2MATSERVER-***********-22
  destination-ip RTQDB-***********-33
  destination-ip BMSDB-***********-15
  service TCP_3555
 rule 117 name G3-R160_02
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2MATSERVER-***********-22
  destination-ip G3AMS-************-102
  destination-ip AMS-CHANNEL-************
  service TCP-30001
  service TCP-30002
 rule 118 name G3-R160_03
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2WEBDC-**********1-12
  destination-ip SBSG2IHS-************-142
  service TCP-31099
  service TCP-31100
  service TCP-31399
  service TCP-52704
  service TCP-35302
 rule 119 name G3-R160-04
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip SBSG2IHS-************-142
  destination-ip SBSG2WEBDC-**********1-12
  service TCP-31399
  service TCP-31400
 rule 120 name G3-R160_05
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip SBSG2IHS-************-142
  destination-ip SBSG2GRSAS-***********-32
  service TCP-31399
  service TCP-31400
 rule 122 name G3-R160_07
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2WEBDC-**********1-12
  destination-ip IHSF5-************
  service TCP-31099
 rule 123 name G3-R160_08
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2GRSAS-***********-32
  destination-ip SDAS-BLIDB-************
  service TCP_31306
 rule 124 name G3-R160_09
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip G3-GW-**********/22
  source-ip V3_GW_**********/21
  destination-ip V3_MS_4.190.121.0/24
  service TCP_29092
 rule 125 name G3-R160_10
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip G3-GW-**********/22
  source-ip V3_GW_**********/21
  destination-ip G3AMS-F5-************
  service TCP-30001
 rule 126 name G3-GW_To_USAP
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2MATSERVER-***********-22
  destination-ip USAP-***********/32
  destination-ip USAP-***********/32
  service TCP-19080
 rule 127 name G3-GW-K8S_To_CSLC-*******
  action pass  
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip CSLC_*******/32
  service ssh
 rule 128 name CSLC-QSCS-UMP_To-AMS-F5
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip QSCS-*********/32
  source-ip CSLC-UMP-*********/24
  destination-ip UMP-AMS-F5-***********6/32
  destination-ip GW-F5-***********
  service http
 rule 129 name CSLC-UMP_To_MatServer-F5
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-UMP-*********/24
  destination-ip MatServer-F5-***********4/32
  destination-ip MatServer-F5-***********5/32
  service https
  service http
 rule 130 name CSLC-SIE-proxy-To_Transrouter-F5
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-SIE-Proxy-********-9
  destination-ip Transrouter-F5-***********1/32
  service http
 rule 131 name CSLC_To_USAP-G2MatServer-F5
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-**********/24
  source-ip CSLC-***********-134
  source-ip CSLC-***********
  destination-ip MatServer-F5-***********5/32
  service TCP-7004
 rule 132 name CSLC_To_G2Matserver-F5
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-**********-98
  destination-ip MatServer-F5-***********5/32
  service TCP-7005
 rule 133 name CSLC-To_WebDC-F5
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-**********-98
  source-ip CSLC-**********-96
  source-ip CSLC-************/24
  destination-ip WEBDC-F5-***********2/32
  service TCP-52701
 rule 134 name CSLC_To_ELP-F5
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-*********/24
  source-ip CSLC-*********/24
  destination-ip G3ELP-***********
  service http
 rule 135 name CSLC_To_WEBDC-F5-SaiShiTuiJian-F5
  action pass  
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-EX-Subscriber-*********/24
  source-ip CSLC-**********-14
  destination-ip WEBDC-F5-***********2/32
  destination-ip SaiShiTuiJian-F5-***********3
  service TCP-52701
  service TCP-8080
 rule 136 name CSLC-K8S_To_WEBDC
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-K8S-********/24
  destination-ip WEBDC-F5-***********2/32
  destination-ip SBSG2WEBDC-**********1-12
  service TCP-52701
 rule 137 name _To_WEBDC-F5
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-**********-96
  destination-ip WEBDC-F5-***********2/32
  service TCP-52701
 rule 138 name CSLC-UMP_To_AMS-F5
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-UMP-*********/24
  destination-ip UMP-AMS-F5-***********6/32
  service http
 rule 140 name G2_To_G3-MatServer-***********-22
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip YJ-ECC-*********-59
  destination-ip SBSG2MATSERVER-***********-22
  service TCP_7001
  service TCP-7003
 rule 141 name 

  action pass
  logging enable
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip 500WAN-*********/27
  source-ip SD-**********/27
  source-ip HN-********/27
  source-ip JINGCAIWANG-********-6
  source-ip JINGCAIWANG-***********-11
  destination-ip WEBDC-F5-***********2/32
  service TCP-52701
 rule 142 name Solarwinds
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip Solarwinds-**********
  destination-ip *********/16
  service ssh
 rule 143 name AMSRPT
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2MATSERVER-***********-22
  destination-ip AMSRPT-F5-************
  destination-ip BISRPT-F5-************
  service TCP-8080
 rule 144 name SCAN
  action pass  
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip FOC-***********-137
  destination-ip V3_GW_**********/22
  destination-ip G3-GW-**********/22
 rule 145 name TO-SFTP
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip SFTP-**********00
  service ssh
 rule 146 name G3_163_01
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip G3SIMULATIONTRANSROUTER01
  service TCP_8082
 rule 147 name YunPingtai
  description YunPingtai
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip Yunpingtai-**********/16
  destination-ip Transrouter-F5-***********1/32
  service http
 rule 148 name YUNYINGtoWEBDC-F5
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip NAT-**********
  destination-ip WEBDC-F5-***********2/32
  service TCP-52701
 rule 150 name YunPingTai-USAP
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip YPT-USAP-************/24
  destination-ip MatServer-F5-***********5/32
  service TCP-7005
 rule 151 name CSLC-GTM
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-GTM-********/24
  destination-ip MatServer-F5-***********5/32
  service TCP-7005
 rule 152 name *********
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip *********/16
  destination-ip *********/16
  service ssh
  service TCP_31306
  service TCP_3555
  service TCP_3558
  service TCP-8080
  service https
  service http
  service TCP_8000
 rule 153 name G3RMX-R130-01
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2IRMAS-***********-62
  destination-ip G3ARESRISK
  service TCP-4100
 rule 154 name G3RMX-R130-02
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2IRMAS-***********-62
  destination-ip V3_CORE_**********/21
  service TCP-5001-5010
  service TCP_6370
 rule 155 name SSM-Ansbile-New
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip Ansbile-************
  source-ip *************
  destination-ip *********/16
  service ssh
 rule 156 name R165patch02
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  source-ip SBSG2IRMAS-***********-62
  destination-ip CSLC-***********
  service TCP-19080
 rule 158 name Yunpingtai-ELP
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip Yunpingtai-************/24
  source-ip Tool-********
  destination-ip G3ELP-***********
  service http
 rule 159 name R240-01
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3TSPAPP01-*************
  source-ip G3TSPAPP02-*************
  destination-ip *********/16
  service ssh
  service http
  service TCP_8080
  service TCP_8472
  service TCP_7001
 rule 161 name K8s-To-Hermes-sharding
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip Hermes-***********1-216
  service TCP-5001-5030
 rule 162 name USAP_TO_***********
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip USAP-***********-14
  source-ip ************-42
  destination-ip ***********
  service TCP_8088
 rule 163 name 4.190.44.0_TO_***********
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip USAP-***********/32
  service TCP-19080
 rule 164 name BOSShujutongbu
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip YPT-USAP-************/24
  destination-ip ***********
  service TCP_8088
 rule 180 name IRM-1.27.0
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip SBSG2IRMAS-***********-62
  destination-ip V3_CORE_**********/21
  service TCP_7001
 rule 181 name K8SNODE_BOSROUTER
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip V3_GW_K8SNODE_4.190.44.0/24
  destination-ip BOSROUTER-F5-************
  service TCP_8082
 rule 182 name G3BOSRedis-TO-GW
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip ************-14
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip ***********-59
  service TCP_7001
 rule 183 name CSLC-Xiaofu_To_Transrouter-F5
  action pass
  counting enable
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-XiaoFu-************/24
  destination-ip Transrouter-F5-***********7
  service http
 rule 185 name VulnerabilityScan_Network
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip VulnerabilityScan-************
  destination-ip *********/16
 rule 190 name ***********-199_***********
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip ***********-199
  destination-ip ***********
  service TCP_8086
 rule 186 name K8SNODE_to_*********/16
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3-MS-K8SNODE_***********/24
  destination-ip *********/16
  service TCP_31306
  service TCP_3558
  service TCP_3555
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP_2379
  service TCP-10251-10252
  service https
  service http
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service TCP_8080
  service ssh
  service TCP_7100
 rule 187 name CSLC_To_OGS
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-***********-134
  destination-ip OGS-***********8/32
  service http
 rule 184 name G3BISMONTORCOLLECT_R340
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip G3BISMONTORCOLLECT
  destination-ip *********/16
  service TCP_31306
  service TCP_3558
  service ssh
  service TCP_3555
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP-10251-10252
  service TCP_2379
  service TCP_8080
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service http
  service TCP_7100
  service https
 rule 188 name BOCC_************-132
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip BOCC
  destination-ip ************-132
  service TCP-9090
  service TCP-9100
  service TCP-9110
  service TCP-9120
 rule 192 name BOS-162
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip CSLC-************/24
  destination-ip ***********
  service http
 rule 193 name radius-**********
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip *********/16
  destination-ip **********
  service UDP-1812-1813
 rule 194 name RMX20230501
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip BOCC
  source-ip BOCC&4A
  destination-ip ***********9
  service TCP-8080
 rule 195 name TO-NFS
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  destination-ip NFS-*********
 rule 197 name XIUSHI
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip ***********-91
  destination-ip **********-4
  destination-ip G3_**********/24
  service ssh
 rule 198 name XIUSHI2
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip **********-2
  source-ip OPS
  destination-ip **********3-14
  destination-ip **********1-12
  service ssh
 rule 199 name XIUSHI3
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip **********3-14
  destination-ip ***********-91
  service TCP-30020
 rule 200 name XIUSHI4
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip *********
  source-ip *********/24
  destination-ip ***********
  service TCP-30020
 rule 202 name EDR
  action pass
  source-zone TENANT02_GW_Inside
  destination-zone TENANT02_GW_Outside
  source-ip G3_**********/24
  destination-ip ************
  service TCP-8443
  service TCP_6677
  service TCP_7788
  service TCP_8001
  service TCP_8002
  service http
 rule 201 name IRM_KYT
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip OGS-***********8/32
  service TCP_8088
 rule 203 name K8SNODE-to-URSF5-30514
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip **********/22
  source-ip **********/22
  destination-ip ***********0
  service TCP_30514
 rule 32 name deny
  logging enable
  counting enable
#
security-policy ipv6
#
return

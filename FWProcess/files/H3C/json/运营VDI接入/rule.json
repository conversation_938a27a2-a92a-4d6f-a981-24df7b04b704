[{"id": "1", "rule_name": "内网出访", "action": "pass", "source_zone": "Local", "source_ip": ["************/24", "***********/24", "***********/24", "***********/24", "inside", "*************", "*************"], "destination_ip": ["any"], "destination_zone": "Local", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "3", "rule_name": "icmp", "action": "pass", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["ping"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "2", "rule_name": "入访deny", "action": "drop", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "4", "rule_name": "YW-VDI_To_Internet", "action": "pass", "source_zone": "YWYY", "source_ip": ["VDI_10.209", "************-242"], "destination_ip": ["any"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "5", "rule_name": "YW-VDI_To_RMOAS", "action": "pass", "source_zone": "YWYY", "source_ip": ["YW-<PERSON><PERSON><PERSON>"], "destination_ip": ["RMOAS"], "destination_zone": "Trust", "services": ["https", "http", "TCP-1433"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "6", "rule_name": "YW-TC_To_VDI", "action": "pass", "source_zone": "YWYY", "source_ip": ["TC-10.209"], "destination_ip": ["VDI_172.100", "*************", "*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "7", "rule_name": "YW-DNS_To_Internet", "action": "pass", "source_zone": "YWYY", "source_ip": ["YW_10.209.4.0"], "destination_ip": ["any"], "destination_zone": "Untrust", "services": ["dns-tcp", "dns-udp", "ping", "ntp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "8", "rule_name": "Old-TC_To_New-VDI", "action": "pass", "source_zone": "Trust", "source_ip": ["***********/16"], "destination_ip": ["***********", "***********", "***********"], "destination_zone": "YWYY", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "9", "rule_name": "YW-VDI_To_ShiPin", "action": "pass", "source_zone": "YWYY", "source_ip": ["YW-<PERSON><PERSON><PERSON>"], "destination_ip": ["<PERSON><PERSON><PERSON>"], "destination_zone": "Trust", "services": ["TCP-3306", "UDP-4068", "UDP-4096", "smb", "TCP-139", "UDP-139", "TCP-138", "UDP-138", "TCP_137", "UDP-137", "TCP-137"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "10", "rule_name": "YW-VDI_To_Print", "action": "pass", "source_zone": "YWYY", "source_ip": ["YW-<PERSON><PERSON><PERSON>"], "destination_ip": ["YY_Print"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}]
[{"id": "157", "rule_name": "YZBOCC_Deny", "action": "drop", "source_zone": "TENANT02_GW_Outside", "source_ip": ["YZBOCC", "YZECC-*********"], "destination_ip": ["any"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_31306", "TCP_3558", "ssh", "TCP_3555", "TCP_3389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "96", "rule_name": "ELP_to_G3·", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["ELP-**********-45", "ELP-*********-3", "CSLC-**********-143", "CSLC-**********-213"], "destination_ip": ["G3ELP-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "0", "rule_name": "1", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_4.190.40.0/21", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_6370", "TCP_5004", "TCP_5003", "TCP-5000_5007", "TCP-5001-5030"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "2", "rule_name": "2", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["harbor_F5_4.190.163.2", "OPS", "HARBOR", "V3_MS_OPS_4.190.163.3"], "destination_zone": "TENANT02_GW_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "3", "rule_name": "3", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["NAS_4.191.41.1"], "destination_zone": "TENANT02_GW_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "11", "rule_name": "4", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["Zookeeper"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "12", "rule_name": "5", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["Redis_feioltp", "Redis_oltp"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "13", "rule_name": "6", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_10250", "TCP_10255", "TCP_8080", "TCP_2379"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "14", "rule_name": "7", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24", "V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "15", "rule_name": "8", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["TIDB_4.190.81.0/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_31306", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "16", "rule_name": "9", "action": "pass", "source_zone": "any", "source_ip": ["*********/16"], "destination_ip": ["*********/16"], "destination_zone": "any", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "17", "rule_name": "10", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["CASGW_4.190.162.4", "CAS_F5_4.190.162.3"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_28080", "TCP_28081", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "18", "rule_name": "11", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_4.190.40.0/21"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "19", "rule_name": "12", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["BOCC", "4A-*********", "YJ-TS-*************/24", "*********/16"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8088", "TCP_31306", "http", "https", "ssh", "TCP_28081", "TCP_28080", "TCP_3558", "TCP_8112", "TCP_3555", "TCP_8086", "TCP_7001", "TCP_3389", "TCP_8080", "TCP-28088", "TCP_28070", "TCP_29000-29999", "TCP_8000", "TCP_5480", "TCP_9443", "TCP_28180", "TCP_23000", "TCP_29411", "TCP_25601", "TCP_9600", "TCP_31050-31051", "TCP_8082", "TCP-20001", "TCP-7004", "TCP-7005"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "25", "rule_name": "13", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24", "V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "26", "rule_name": "14", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["JianKong_4.190.121.0"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_12049", "TCP_9100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "27", "rule_name": "15", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24", "V3_CORE_K8SNODE_**********/24"], "destination_ip": ["nginx01_4.190.120.51/32", "nginx02_4.190.120.52/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "31", "rule_name": "16", "action": "pass", "source_zone": "TENANT01_GW_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["NAS_4.191.41.1"], "destination_zone": "TENANT01_GW_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "35", "rule_name": "V3-2", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["HARBOR", "harbor_F5_4.190.163.2", "OPS", "V3_MS_OPS_4.190.163.3"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "36", "rule_name": "V3-5", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_4.190.40.0/21"], "destination_ip": ["Configcenter"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "37", "rule_name": "V3-18", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2OCS-**********", "**********-162"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_31306", "TCP_3558", "ssh", "TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "38", "rule_name": "V3-19", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["yunwei-************-12"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh", "TCP_3555", "TCP_3558", "TCP_3191", "TCP_7001", "TCP_6370", "TCP_2379", "UDP_8472", "TCP_8080", "TCP_8472"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "50", "rule_name": "V3-20", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "G3_GW_4.190.40.0/24", "G3-GW-**********/22"], "destination_ip": ["NAS_4.191.40.3", "NAS-**********-6"], "destination_zone": "TENANT02_GW_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "51", "rule_name": "V3-22", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3OPERVM"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_6370", "TCP_2379", "TCP_5003", "TCP_3555", "TCP_3558", "TCP_3191", "TCP_7001", "UDP_8472", "TCP_8472", "TCP_8080", "http", "ssh", "TCP_31306", "TCP-10251-10252", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "TCP_7100", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "39", "rule_name": "V3-25", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["T1_18.0.1.173", "T1_NTP_18.0.11.173"], "destination_zone": "TENANT02_GW_Outside", "services": ["ntp", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "40", "rule_name": "V3-26", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["***********"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8086"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "149", "rule_name": "RMOAS", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "G3SSL-*********/24"], "destination_ip": ["F5-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8086"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "41", "rule_name": "V3-27", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "DMZ-SSL-*********/24"], "destination_ip": ["***********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_8086"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "42", "rule_name": "V3-29", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["\"GW ZABBIX PROXY-************\""], "destination_ip": ["\"ZABBIX SERVER-************-42\"", "***********5"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "43", "rule_name": "V3-30", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["\"ZABBIX SERVER-************-42\"", "***********5"], "destination_ip": ["\"GW ZABBIX PROXY-************\""], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "44", "rule_name": "V3-36", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2WEBDC-*********"], "destination_ip": ["G3IHSGW-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8088"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "45", "rule_name": "V3-37", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2ELP-********"], "destination_ip": ["G3ELP-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "46", "rule_name": "V3-40", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["W5RFTP-**********", "G2FTP-**********", "CSLC-*******"], "destination_zone": "TENANT02_GW_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "47", "rule_name": "V3-42", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_MS_4.190.120.0/22", "************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_28080", "https", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "48", "rule_name": "V3-44", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_29092"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "49", "rule_name": "V3-45", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_**********/21", "***********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_28081", "TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "55", "rule_name": "V3-46", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_10250", "TCP_10255", "TCP-30350"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "56", "rule_name": "V3-47", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_10250", "TCP_10255"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "58", "rule_name": "V3-48", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["V3_GW_4.190.40.0/21"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-29090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "59", "rule_name": "V3-49", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_9100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "72", "rule_name": "V3-50", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3-MS-K8SNODE_***********/24"], "destination_ip": ["V3_GW_4.190.40.0/21"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-5000_5007", "TCP_3191", "TCP_9100", "TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "73", "rule_name": "V3-51", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_MS_F5_***********4", "V3_MS_4.190.121.0/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-28088", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "75", "rule_name": "V3_52", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3-MS-K8SNODE_***********/24"], "destination_ip": ["redis-**********-9"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "76", "rule_name": "V3-53", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_**********/21", "***********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "77", "rule_name": "V3-54", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_29000-29999"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "81", "rule_name": "V3-55", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["hermes-***********-16"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_5003"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "82", "rule_name": "V3-56", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_MS_4.190.121.0/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-30600"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "83", "rule_name": "V3-57", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*******/8"], "destination_ip": ["T1_18.0.1.173", "T1_NTP_18.0.11.173"], "destination_zone": "TENANT02_GW_Outside", "services": ["http", "ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "1", "rule_name": "V3_GW_4.190.44.0/24 TO Public_NAS_4.191.41.1", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["NAS_4.191.41.1"], "destination_zone": "TENANT02_GW_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "7", "rule_name": "V3-GW-K8SNODE To V3-MS-K8SNODE", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3-MS-K8SNODE_***********/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_2379", "TCP_8080", "TCP_10255", "TCP_10250", "TCP-6443"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "9", "rule_name": "V3-GW-K8SNODE To V3-MS-F5-***********", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_MS_F5_***********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "22", "rule_name": "V3_MS_K8SNODE To V3_GW_K8SNODE", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_2379", "TCP_8080", "TCP_10250", "TCP_10255"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "8", "rule_name": "V3-GW-K8SNODE To V3-MS&CORE-K8SNODE Flannel", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3-MS-K8SNODE_***********/24", "V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "6", "rule_name": "V3_GW TO V3_DNS", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["any"], "destination_ip": ["V3_DNS_4.190.80.51", "V3_DNS_4.190.80.52"], "destination_zone": "TENANT02_GW_Outside", "services": ["dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "10", "rule_name": "ospf", "action": "pass", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["ospf", "ping"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "74", "rule_name": "V3-52", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["**********1", "***********", "T1_18.0.1.238/32"], "destination_ip": ["TIDB_4.190.81.0/24", "*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-30400", "TCP-36524", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "74", "rule_name": "V3-52", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["**********1", "***********", "T1_18.0.1.238/32"], "destination_ip": ["TIDB_4.190.81.0/24", "*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-30400", "TCP-36524", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "53", "rule_name": "Test", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["***********/22"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-6443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "71", "rule_name": "G2_TRANSROUTE_V3_30400", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2_TRANSROUTE_18.1.13.81-84", "XuNi-************"], "destination_ip": ["V3_GW_4.190.40.0/22"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-30400", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "87", "rule_name": "G3-V1.3-20200429-01", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2OCS-**********"], "destination_ip": ["*********/16", "G3_*********/16"], "destination_zone": "TENANT03_GW_Inside", "services": ["ssh", "TCP_31306", "TCP_3555", "TCP_3558"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "88", "rule_name": "G3-V1.3-20200429-02", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["BOCC&4A"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh", "TCP_31306", "TCP_3555", "TCP_3558", "TCP_8080", "http", "https", "TCP_8000"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "89", "rule_name": "G3-V1.3-20200429-03", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["F5-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8086"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "90", "rule_name": "G3-V1.3-20200429-04", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2_TRANSROUTE_18.1.13.81-84"], "destination_ip": ["G3_GW_4.190.40.0/24", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "91", "rule_name": "G3-V1.3-20200429-05", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2_WEBDC"], "destination_ip": ["G3IHSGW-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8088"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "92", "rule_name": "G3-V1.3-20200429-06", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["G2_NTP"], "destination_zone": "TENANT02_GW_Outside", "services": ["ntp", "UDP_123"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "93", "rule_name": "G3-V1.3-20200429-07", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G2_WEBDC"], "destination_ip": ["G3_4.190.40.0/24"], "destination_zone": "TENANT03_GW_Inside", "services": ["TCP_8082"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "94", "rule_name": "G3-V1.3-20200429-08", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["W5RFTP-**********", "G2FTP-**********", "CSLC_*******/32"], "destination_zone": "TENANT02_GW_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "95", "rule_name": "G3-V1.3-20200429-09", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["G2_AMSDB_18.0.2.36/32"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "97", "rule_name": "CSLC-baoleiji", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-baoleiji-**********", "CSLC-*********"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh", "https", "http", "TCP_3389", "TCP-8888", "TCP-8889", "TCP-8013", "TCP-8090", "TCP_8000", "TCP_3555", "TCP_3558", "TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "98", "rule_name": "TO_G2FTP", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["G2FTP-**********"], "destination_zone": "TENANT02_GW_Outside", "services": ["ftp", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "99", "rule_name": "To_CSLC-ƽ̨", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["CSLC-DIP-***********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-9092"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "100", "rule_name": "AMS-MATGW", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3AMS-************-102"], "destination_ip": ["G3MATGW-F5-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-8085"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "101", "rule_name": "F5-TO-G2TRANSROUTE", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["GW-F5-***********/24"], "destination_ip": ["G2_TRANSROUTE_18.1.13.81-84"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-8085", "TCP_8082", "TCP-8087"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "102", "rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["FOC-*********", "FOC-***********"], "destination_ip": ["any"], "destination_zone": "TENANT02_GW_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "103", "rule_name": "G3-R141b", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["G3_GW_4.190.40.0/24"], "destination_ip": ["CSLC-OPENAPI-**********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "104", "rule_name": "RMX1.1.0_01", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3WCSINFO-**********-22"], "destination_ip": ["G3WCSINFOSFTP-***********-42", "G3WCSINFOSFTP-F5-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "105", "rule_name": "RMX1.1.0_02", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3WCSINFO-**********-22"], "destination_ip": ["any"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "106", "rule_name": "JCJK1.3.7", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3MONITORGAIA-*************-162"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "139", "rule_name": "MAIL", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["Mail-*********-2"], "destination_zone": "TENANT02_GW_Outside", "services": ["smtp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "107", "rule_name": "TO-SYSLOG", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["SYSLOG-F5-************", "Solarwinds-**********"], "destination_zone": "TENANT02_GW_Outside", "services": ["syslog"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "108", "rule_name": "To_G3_NTP", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["G3_CORE_NTP_4.190.80.251-252"], "destination_zone": "TENANT02_GW_Outside", "services": ["ntp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "109", "rule_name": "SDAS-V2.15.1_01", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2GRSAS-***********-32"], "destination_ip": ["SDAS-BLIDB-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "110", "rule_name": "SSM-Ansbile", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["Ansbile-************", "*************"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "111", "rule_name": "IRM1.15.0_01", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3SSL-*********/24", "*********/16"], "destination_ip": ["IRM-F5-***********6"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-8443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "112", "rule_name": "IRM1.15.0_02", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["***********-16", "SBSG2IRMDBVIP-**********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "113", "rule_name": "IRM1.15.0_03", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["CORE-F5-***********/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "115", "rule_name": "IRM1.15.0_04", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3OPERVM01-**********", "G3OPERVM02-**********"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_3555", "TCP_3558", "TCP_3191", "TCP_5003", "TCP_6370", "TCP_7001", "TCP_8080", "UDP_8472", "TCP_2379", "ssh", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "114", "rule_name": "OPSFTP", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["SBSG2OPSFTP01-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["ftp", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "121", "rule_name": "G3-R160_06", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["SBSG2OTJob-***********"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "116", "rule_name": "G3-R160_01", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2MATSERVER-***********-22"], "destination_ip": ["RTQDB-**********1-33", "BMSDB-***********-15"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "117", "rule_name": "G3-R160_02", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2MATSERVER-***********-22"], "destination_ip": ["G3AMS-************-102", "AMS-CHANNEL-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-30001", "TCP-30002"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "118", "rule_name": "G3-R160_03", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2WEBDC-***********-12"], "destination_ip": ["SBSG2IHS-************-142"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-31099", "TCP-31100", "TCP-31399", "TCP-52704", "TCP-35302"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "119", "rule_name": "G3-R160-04", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["SBSG2IHS-************-142"], "destination_ip": ["SBSG2WEBDC-***********-12"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-31399", "TCP-31400"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "120", "rule_name": "G3-R160_05", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["SBSG2IHS-************-142"], "destination_ip": ["SBSG2GRSAS-***********-32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-31399", "TCP-31400"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "122", "rule_name": "G3-R160_07", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2WEBDC-***********-12"], "destination_ip": ["IHSF5-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-31099"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "123", "rule_name": "G3-R160_08", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2GRSAS-***********-32"], "destination_ip": ["SDAS-BLIDB-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "124", "rule_name": "G3-R160_09", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["G3-GW-**********/22", "V3_GW_4.190.40.0/21"], "destination_ip": ["V3_MS_4.190.121.0/24"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_29092"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "125", "rule_name": "G3-R160_10", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["G3-GW-**********/22", "V3_GW_4.190.40.0/21"], "destination_ip": ["G3AMS-F5-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-30001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "126", "rule_name": "G3-GW_To_USAP", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2MATSERVER-***********-22"], "destination_ip": ["USAP-***********/32", "USAP-***********/32"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-19080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "127", "rule_name": "G3-GW-K8S_To_CSLC-*******", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["CSLC_*******/32"], "destination_zone": "TENANT02_GW_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "128", "rule_name": "CSLC-QSCS-UMP_To-AMS-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["QSCS-*********/32", "CSLC-UMP-*********/24"], "destination_ip": ["UMP-AMS-F5-***********6/32", "GW-F5-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "129", "rule_name": "CSLC-UMP_To_MatServer-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-UMP-*********/24"], "destination_ip": ["MatServer-F5-***********4/32", "MatServer-F5-***********5/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["https", "http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "130", "rule_name": "CSLC-SIE-proxy-To_Transrouter-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-SIE-Proxy-********-9"], "destination_ip": ["Transrouter-F5-***********1/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "131", "rule_name": "CSLC_To_USAP-G2MatServer-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-**********/24", "CSLC-***********-134", "CSLC-***********"], "destination_ip": ["MatServer-F5-***********5/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-7004"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "132", "rule_name": "CSLC_To_G2Matserver-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-**********-98"], "destination_ip": ["MatServer-F5-***********5/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-7005"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "133", "rule_name": "CSLC-To_WebDC-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-**********-98", "CSLC-**********-96", "CSLC-************/24"], "destination_ip": ["WEBDC-F5-***********2/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-52701"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "134", "rule_name": "CSLC_To_ELP-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-*********/24", "CSLC-*********/24"], "destination_ip": ["G3ELP-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "135", "rule_name": "CSLC_To_WEBDC-F5-SaiShiTuiJian-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-EX-Subscriber-*********/24", "CSLC-**********-14"], "destination_ip": ["WEBDC-F5-***********2/32", "SaiShiTuiJian-F5-***********3"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-52701", "TCP-8080"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "136", "rule_name": "CSLC-K8S_To_WEBDC", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-K8S-********/24"], "destination_ip": ["WEBDC-F5-***********2/32", "SBSG2WEBDC-***********-12"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-52701"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "137", "rule_name": "_To_WEBDC-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-**********-96"], "destination_ip": ["WEBDC-F5-***********2/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-52701"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "138", "rule_name": "CSLC-UMP_To_AMS-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-UMP-*********/24"], "destination_ip": ["UMP-AMS-F5-***********6/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "140", "rule_name": "G2_To_G3-MatServer-***********-22", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["YJ-ECC-*********-59", "500WAN-*********/27", "SD-**********/27", "HN-********/27", "JINGCAIWANG-********-6", "JINGCAIWANG-***********-11"], "destination_ip": ["SBSG2MATSERVER-***********-22", "WEBDC-F5-***********2/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_7001", "TCP-7003", "TCP-52701"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "140", "rule_name": "G2_To_G3-MatServer-***********-22", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["YJ-ECC-*********-59", "500WAN-*********/27", "SD-**********/27", "HN-********/27", "JINGCAIWANG-********-6", "JINGCAIWANG-***********-11"], "destination_ip": ["SBSG2MATSERVER-***********-22", "WEBDC-F5-***********2/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_7001", "TCP-7003", "TCP-52701"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "142", "rule_name": "Solarwinds", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["Solarwinds-**********"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "143", "rule_name": "AMSRPT", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2MATSERVER-***********-22"], "destination_ip": ["AMSRPT-F5-************", "BISRPT-F5-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "144", "rule_name": "SCAN", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["FOC-***********-137"], "destination_ip": ["V3_GW_4.190.40.0/22", "G3-GW-**********/22"], "destination_zone": "TENANT02_GW_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "145", "rule_name": "TO-SFTP", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["SFTP-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "146", "rule_name": "G3_163_01", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["G3SIMULATIONTRANSROUTER01"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8082"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "147", "rule_name": "YunPingtai", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["Yunpingtai-**********/16"], "destination_ip": ["Transrouter-F5-***********1/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "148", "rule_name": "YUNYINGtoWEBDC-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["NAT-**********"], "destination_ip": ["WEBDC-F5-***********2/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-52701"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "150", "rule_name": "YunPingTai-USAP", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["YPT-USAP-************/24"], "destination_ip": ["MatServer-F5-***********5/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-7005"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "151", "rule_name": "CSLC-GTM", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-GTM-********/24"], "destination_ip": ["MatServer-F5-***********5/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-7005"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "152", "rule_name": "*********", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["*********/16"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh", "TCP_31306", "TCP_3555", "TCP_3558", "TCP-8080", "https", "http", "TCP_8000"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "153", "rule_name": "G3RMX-R130-01", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["G3ARESRISK"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-4100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "154", "rule_name": "G3RMX-R130-02", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-5001-5010", "TCP_6370"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "155", "rule_name": "SSM-Ansbile-New", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["Ansbile-************", "*************"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "156", "rule_name": "R165patch02", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "SBSG2IRMAS-***********-62"], "destination_ip": ["CSLC-***********"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-19080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "158", "rule_name": "Yunpingtai-ELP", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["Yunpingtai-************/24", "Tool-********"], "destination_ip": ["G3ELP-***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "159", "rule_name": "R240-01", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3TSPAPP01-*************", "G3TSPAPP02-*************"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh", "http", "TCP_8080", "TCP_8472", "TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "161", "rule_name": "K8s-To-Hermes-sharding", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["Hermes-************-216"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-5001-5030"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "162", "rule_name": "USAP_TO_***********", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["USAP-***********-14", "************-42"], "destination_ip": ["***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8088"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "163", "rule_name": "4.190.44.0_TO_***********", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["USAP-***********/32"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-19080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "164", "rule_name": "BOSShujutongbu", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["YPT-USAP-************/24"], "destination_ip": ["***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8088"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "180", "rule_name": "IRM-1.27.0", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "181", "rule_name": "K8SNODE_BOSROUTER", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["BOSROUTER-F5-************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP_8082"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "182", "rule_name": "G3BOSRedis-TO-GW", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["************-14", "V3_CORE_K8SNODE_**********/24"], "destination_ip": ["***********-59"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "183", "rule_name": "CSLC-Xiaofu_To_Transrouter-F5", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-XiaoFu-************/24"], "destination_ip": ["Transrouter-F5-***********7"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "185", "rule_name": "VulnerabilityScan_Network", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["VulnerabilityScan-************"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "190", "rule_name": "***********-199_***********", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["***********-199"], "destination_ip": ["***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8086"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "186", "rule_name": "K8SNODE_to_*********/16", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3-MS-K8SNODE_***********/24"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_31306", "TCP_3558", "TCP_3555", "TCP_3191", "TCP_7001", "TCP_6370", "TCP_2379", "TCP-10251-10252", "https", "http", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "TCP_8080", "ssh", "TCP_7100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "187", "rule_name": "CSLC_To_OGS", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-***********-134"], "destination_ip": ["OGS-***********8/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "184", "rule_name": "G3BISMONTORCOLLECT_R340", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["G3BISMONTORCOLLECT"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_31306", "TCP_3558", "ssh", "TCP_3555", "TCP_3191", "TCP_7001", "TCP_6370", "TCP-10251-10252", "TCP_2379", "TCP_8080", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "http", "TCP_7100", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "188", "rule_name": "BOCC_************-132", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["BOCC"], "destination_ip": ["************-132"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-9090", "TCP-9100", "TCP-9110", "TCP-9120"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "192", "rule_name": "BOS-162", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["CSLC-************/24"], "destination_ip": ["***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "193", "rule_name": "radius-**********", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["*********/16"], "destination_ip": ["**********"], "destination_zone": "TENANT02_GW_Outside", "services": ["UDP-1812-1813"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "194", "rule_name": "RMX20230501", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["BOCC", "BOCC&4A"], "destination_ip": ["***********9"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "195", "rule_name": "TO-NFS", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["any"], "destination_ip": ["NFS-*********"], "destination_zone": "TENANT02_GW_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "197", "rule_name": "XIUSHI", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["***********-91"], "destination_ip": ["**********-4", "G3_4.190.40.0/24"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "198", "rule_name": "XIUSHI2", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["**********-2", "OPS"], "destination_ip": ["**********3-14", "**********1-12"], "destination_zone": "TENANT02_GW_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "199", "rule_name": "XIUSHI3", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["**********3-14"], "destination_ip": ["***********-91"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-30020"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "200", "rule_name": "XIUSHI4", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["*********", "*********/24"], "destination_ip": ["***********"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP-30020"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "202", "rule_name": "EDR", "action": "pass", "source_zone": "TENANT02_GW_Inside", "source_ip": ["G3_4.190.40.0/24"], "destination_ip": ["************"], "destination_zone": "TENANT02_GW_Outside", "services": ["TCP-8443", "TCP_6677", "TCP_7788", "TCP_8001", "TCP_8002", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "201", "rule_name": "IRM_KYT", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["OGS-***********8/32"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_8088"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "203", "rule_name": "K8SNODE-to-URSF5-30514", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["**********/22", "**********/22"], "destination_ip": ["***********0"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_30514"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "32", "rule_name": "deny", "action": "drop", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}]
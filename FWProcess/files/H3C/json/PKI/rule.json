[{"id": "46", "rule_name": "lousao", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["lousao"], "destination_zone": "Management", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "1", "rule_name": "ping", "action": "pass", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["ping"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "0", "rule_name": "management", "action": "pass", "source_zone": "Trust", "source_ip": ["*********", "*********"], "destination_ip": ["local-ip", "*********", "*********", "*********", "*********", "*********", "******** ********", "******** ********", "******** ********", "******** ********", "*********** ************"], "destination_zone": "PKIKM", "services": ["http", "https", "ssh", "telnet", "ping", "TCP-18060", "TCP-17080", "TCP-3306", "TCP-17090", "TCP-1521"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "3", "rule_name": "ntp", "action": "pass", "source_zone": "PKIKM", "source_ip": ["1???", "newp<PERSON><PERSON><PERSON>"], "destination_ip": ["newntp", "*******", "*******"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "5", "rule_name": "??§æ¡¤??t?§æPKI???", "action": "pass", "source_zone": "Untrust", "source_ip": ["********* *********"], "destination_ip": ["********* *********"], "destination_zone": "Trust", "services": ["TCP-7809-7849"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "6", "rule_name": "?t?§æ¡¤??¬DKI???", "action": "pass", "source_zone": "Trust", "source_ip": ["********* *********"], "destination_ip": ["********* *********"], "destination_zone": "Untrust", "services": ["TCP-7809-7849"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "7", "rule_name": "NBU¡¤??KI???", "action": "pass", "source_zone": "Untrust", "source_ip": ["********", "********", "********"], "destination_ip": ["*********", "*********"], "destination_zone": "Trust", "services": ["NBU??"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "8", "rule_name": "PKI?????NBU", "action": "pass", "source_zone": "Trust", "source_ip": ["*********", "*********"], "destination_ip": ["********", "********", "********"], "destination_zone": "Untrust", "services": ["NBU??"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "9", "rule_name": "InsideToOutside", "action": "pass", "source_zone": "Trust", "source_ip": ["******** *************", "*********** *************"], "destination_ip": ["any"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "11", "rule_name": "UMP??¡¤?", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********-172", "********* *********2", "*********31 *********34", "*********51 *********54"], "destination_ip": ["********* *********"], "destination_zone": "Trust", "services": ["https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "12", "rule_name": "¡À¡è]??", "action": "pass", "source_zone": "Untrust", "source_ip": ["citrix??", "************ ************", "************ ************"], "destination_ip": ["*********", "*********"], "destination_zone": "Trust", "services": ["https", "TCP-3389"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "13", "rule_name": "SOC??", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********** ***********6"], "destination_ip": ["*********", "*********", "*********", "*********", "*********", "*********"], "destination_zone": "Trust", "services": ["UDP-8516", "TCP-8888-8889", "TCP-8999", "TCP-9998-9999", "UDP-10050-10051", "UDP-8514"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "15", "rule_name": "NAS1????", "action": "pass", "source_zone": "Untrust", "source_ip": ["********* *********"], "destination_ip": ["********* *********"], "destination_zone": "Trust", "services": ["NAS1?"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "16", "rule_name": "NAS1????", "action": "pass", "source_zone": "Trust", "source_ip": ["********* *********"], "destination_ip": ["********* *********"], "destination_zone": "Untrust", "services": ["NAS1?"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "17", "rule_name": "??zabbix¡¤??KI", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********** *************"], "destination_ip": ["********* *********", "********* *********"], "destination_zone": "Trust", "services": ["tcp-10050"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "18", "rule_name": "PKI¡¤???zabbix", "action": "pass", "source_zone": "Trust", "source_ip": ["********* *********", "********* *********"], "destination_ip": ["*********** *************"], "destination_zone": "Untrust", "services": ["tcp-10051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "21", "rule_name": "??", "action": "pass", "source_zone": "Management", "source_ip": ["PKI??IP", "************"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["syslog", "snmp-trap"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "22", "rule_name": "PKI¡¤??cafee", "action": "pass", "source_zone": "Trust", "source_ip": ["********* *********", "********* *********"], "destination_ip": ["*********"], "destination_zone": "Untrust", "services": ["TCP-8080", "TCP-443", "TCP-1443"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "23", "rule_name": "mcafee¡¤??KI", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********"], "destination_ip": ["********* *********", "********* *********"], "destination_zone": "Trust", "services": ["TCP_8081"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "24", "rule_name": "Storage_To_Monitor", "action": "pass", "source_zone": "Management", "source_ip": ["************-93"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["syslog", "snmp-trap", "ping"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "25", "rule_name": "ca-to-outside", "action": "pass", "source_zone": "Trust", "source_ip": ["******** ********"], "destination_ip": ["4.10.10.10", "4.10.10.30", "4.10.1.51 4.10.1.52", "4.10.10.33 4.10.10.34"], "destination_zone": "Trust", "services": ["TCP-6006", "TCP-389", "TCP-4444", "TCP-9080"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "26", "rule_name": "ca-to-pkikm", "action": "pass", "source_zone": "Trust", "source_ip": ["******** ********"], "destination_ip": ["*********", "******** ********"], "destination_zone": "PKIKM", "services": ["TCP-17090"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "27", "rule_name": "pkikm-to-IBMLDAP", "action": "pass", "source_zone": "Trust", "source_ip": ["4.10.2.4 255.255.255.254"], "destination_ip": ["4.10.10.30 4.10.10.32"], "destination_zone": "Untrust", "services": ["TCP-389"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "28", "rule_name": "pkikm-to-ca", "action": "pass", "source_zone": "PKIKM", "source_ip": ["******** ********"], "destination_ip": ["******** ********"], "destination_zone": "Trust", "services": ["TCP-3306"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "29", "rule_name": "UMP-to-radk", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********-172", "********* *********2", "*********31 *********34", "*********51 *********54"], "destination_ip": ["*********", "******** ********"], "destination_zone": "Trust", "services": ["TCP-10289"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "30", "rule_name": "ZABBIX-to-caguanli", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********** *************"], "destination_ip": ["4.255.232.0 *************", "******** ********", "******** ********"], "destination_zone": "PKIKM", "services": ["tcp-10050"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "31", "rule_name": "caguanli-to-ZABBIX", "action": "pass", "source_zone": "PKIKM", "source_ip": ["4.255.232.0 *************", "******** ********", "******** ********"], "destination_ip": ["*********** *************"], "destination_zone": "Untrust", "services": ["tcp-10051"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "32", "rule_name": "caguanli-to-syslog-SNMP", "action": "pass", "source_zone": "Management", "source_ip": ["4.255.232.0 *************"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["UDP-162", "UDP-514"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "33", "rule_name": "UMP-to-radk-01", "action": "pass", "source_zone": "Untrust", "source_ip": ["********* *********2", "*********31 *********34", "*********51 *********54", "*********** *********72"], "destination_ip": ["*********", "*********", "******** ********"], "destination_zone": "Trust", "services": ["TCP-10289", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "34", "rule_name": "mifu-to-ca", "action": "pass", "source_zone": "Untrust", "source_ip": ["3.15.0.41 3.15.0.42"], "destination_ip": ["*********", "******** ********"], "destination_zone": "Trust", "services": ["TCP-18443", "TCP-17060", "https", "ssh", "TCP-17080"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "35", "rule_name": "ca-to-mifutuisong", "action": "pass", "source_zone": "Trust", "source_ip": ["******** ********"], "destination_ip": ["3.15.0.40", "3.15.0.41 3.15.0.43"], "destination_zone": "Untrust", "services": ["TCP-11001", "TCP-11002", "https", "ssh"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "36", "rule_name": "test-to-ca", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********** *************"], "destination_ip": ["*********", "******** ********"], "destination_zone": "Trust", "services": ["TCP-18443", "TCP-17080", "https", "ssh"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "37", "rule_name": "ca-to-pkikm-01", "action": "pass", "source_zone": "Trust", "source_ip": ["******** ********"], "destination_ip": ["*********", "******** ********"], "destination_zone": "PKIKM", "services": ["TCP-17090"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "38", "rule_name": "<PERSON><PERSON><PERSON><PERSON>", "action": "pass", "source_zone": "Untrust", "source_ip": ["********* *************"], "destination_ip": ["********", "********", "************ ************"], "destination_zone": "PKIKM", "services": ["UDP-161"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "39", "rule_name": "canginx-to-oldca", "action": "pass", "source_zone": "any", "source_ip": ["******** ********"], "destination_ip": ["********* *********"], "destination_zone": "any", "services": ["https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "40", "rule_name": "pkikm-to-yum", "action": "pass", "source_zone": "PKIKM", "source_ip": ["******** *************", "******** *************"], "destination_ip": ["************", "************0"], "destination_zone": "Untrust", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "41", "rule_name": "nas-in", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********"], "destination_ip": ["******** ********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "42", "rule_name": "nas-out", "action": "pass", "source_zone": "Trust", "source_ip": ["******** ********"], "destination_ip": ["*********"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "43", "rule_name": "dialing-test", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********** *************"], "destination_ip": ["*********"], "destination_zone": "Trust", "services": ["tcp destination eq 17080", "tcp destination eq 18443"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "44", "rule_name": "zabbixMonitor-in", "action": "pass", "source_zone": "Untrust", "source_ip": ["*********** *************"], "destination_ip": ["******** *************"], "destination_zone": "Trust", "services": ["tcp destination eq 10050"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "45", "rule_name": "zabbixMonitor-out", "action": "pass", "source_zone": "Trust", "source_ip": ["******** *************"], "destination_ip": ["*********** *************"], "destination_zone": "Untrust", "services": ["tcp destination eq 10051"], "is_logging": "", "is_counting": "enable", "rule_status": ""}]
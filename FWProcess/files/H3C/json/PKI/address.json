[{"address_name": "***********-172", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "************-93", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "citrix??", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "********* *************"}, {"type": "subnet", "value": "********* *************"}]}, {"address_name": "local-ip", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "lousao", "security-zone": null, "ip_address": [{"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "manage", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********* *************"}, {"type": "subnet", "value": "********* *************"}, {"type": "subnet", "value": "********* ***********"}, {"type": "host", "value": "**********"}]}, {"address_name": "newntp", "security-zone": null, "ip_address": [{"type": "host", "value": "*******"}, {"type": "host", "value": "*******"}]}, {"address_name": "newp<PERSON><PERSON><PERSON>", "security-zone": null, "ip_address": [{"type": "range", "value": "*********** ***********2"}, {"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "******** *************"}]}, {"address_name": "PKI??IP", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "1???", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}]
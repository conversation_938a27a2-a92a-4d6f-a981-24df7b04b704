[{"id": "268", "rule_name": "YZBOCC_4.128.10.3_3555", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["YZBOCC_4.128.10.3"], "destination_ip": ["RTQDB-**********3"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "269", "rule_name": "YZBOCC_Deny", "action": "drop", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["YZBOCC", "YZECC-*********"], "destination_ip": ["any"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306", "TCP_3558", "ssh", "TCP_3555", "TCP_3389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "184", "rule_name": "G3_To_CSLC_NTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_*********/16"], "destination_ip": ["CSLCNTP-*******"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ntp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "125", "rule_name": "G3-WCSDB-G2-SBCDB", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_4.190.80.71", "V3_CORE_4.190.80.72", "V3_CORE_4.190.80.73"], "destination_ip": ["G2SBCDB-*********-37"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3556"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "120", "rule_name": "_to_18.0.6.200", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["G3LINSHISBCCOPYTOWCS01-0.200"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh", "TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "121", "rule_name": "18.0.6.200_WCSDB", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G3LINSHISBCCOPYTOWCS01-0.200"], "destination_ip": ["V3_CORE_4.190.80.73"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_3558"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "15", "rule_name": "V3-CORE-K8SNODE To V3-MS-K8SNODE", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_2379", "TCP_8080", "TCP_10255", "TCP_10250", "TCP-6443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "28", "rule_name": "V3_MS-K8SNODE To V3_CORE_K8SNODE", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_2379", "TCP_8080", "TCP_10250", "TCP_10255", "TCP_28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "29", "rule_name": "V3-MS-K8SNODE To V3-CORE-K8SNODE Flannel", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "16", "rule_name": "V3-CORE-K8SNODE To V3-GW&MS-K8SNODE Flannel", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "30", "rule_name": "V3-GW-K8SNODE To V3-CORE-K8SNODE Flannel", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "53", "rule_name": "To TENANT-PUBLIC-NFS", "action": "pass", "source_zone": "TENANT_PUBLIC_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_**********/24", "V3MNYY_CORE_4.191.80.0/24"], "destination_ip": ["NAS_4.191.41.1"], "destination_zone": "TENANT_PUBLIC_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "47", "rule_name": "V3-CORE To Harbor", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["HARBOR", "OPS", "nginx01_4.190.120.51/32", "nginx02_4.190.120.52/32", "Harbor-F5-***********/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "17", "rule_name": "V3-CORE-K8SNODE To V3-MS-F5-***********", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_MS_F5_***********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "18", "rule_name": "V3-CORE-Tidb-Cluster To V3-MS-Tidb-Monitor", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_Tidb_Clus_4.190.81.0/24"], "destination_ip": ["V3_MS_Tidb_Monitor4.190.121.221", "V3_MS_4.190.121.231/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "261", "rule_name": "R1.6.5.1", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["ShuJuJiChengPingTai-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-22"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "262", "rule_name": "Ansbile", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["Ansbile-************"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "263", "rule_name": "OPERVM-TO-ELK", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2", "G3BISMONTORCOLLECT"], "destination_ip": ["G3ELK-***********-5", "G3-MS-ELKF5-***********9"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-29200"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "264", "rule_name": "R165patch02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["T1_18.2.1.0/24", "*********/16", "YZ-ECC-*********", "BOCC"], "destination_ip": ["G3RMXLOGON-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-9130", "TCP-9131"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "49", "rule_name": "G3-CORE-Tidb-Cluster To G3-MS-Tidb-Monitor", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_Tidb_Clus_4.190.81.0/24"], "destination_ip": ["V3_MS_Tidb_Monitor4.190.121.221", "V3_MS_4.190.121.231/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "5", "rule_name": "1", "action": "pass", "source_zone": "any", "source_ip": ["*********/16"], "destination_ip": ["*********/16"], "destination_zone": "any", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "11", "rule_name": "3", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24", "V3_MS_4.190.120.0/22"], "destination_ip": ["NAS_4.191.41.1", "nfs-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "19", "rule_name": "4", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["Configcenter"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "20", "rule_name": "5", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_10250", "TCP_10255", "TCP_8080", "TCP_2379", "TCP_10256"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "21", "rule_name": "6", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "22", "rule_name": "7", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["CAS_F5_4.190.162.3", "***********", "***********", "casgw_4.190.162.4", "V3_CORE_**********/22"], "destination_ip": ["T1_CeShi_********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "23", "rule_name": "8", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/21"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "24", "rule_name": "9", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BOCC", "4A-*********", "YJ-TS-*************/24", "*********/16"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_3558", "TCP_28080", "TCP_28081", "ssh", "http", "https", "TCP_31306", "TCP_8088", "TCP_3555", "TCP_7001", "TCP_3389", "TCP_8080", "TCP_28070", "TCP-29090", "TCP_8086", "TCP_31050-31051", "TCP_8000", "TCP_28088", "TCP_28180", "TCP_23000", "TCP_29411", "TCP_25601", "TCP_9600", "TCP_9443", "TCP_5480", "TCP_29093", "TCP-8022", "TCP-9090", "TCP-3601", "TCP-8018", "TCP-5000", "TCP-8161", "TCP-8080", "TCP-30900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "25", "rule_name": "10", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_**********/21", "V3_MS_4.190.120.0/22"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_6370", "TCP_5004", "TCP_5003", "TCP-5000_5007", "TCP_5008", "TCP-4100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "31", "rule_name": "11", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_Tidb_Monitor4.190.121.221", "V3_MS_4.190.121.231/32"], "destination_ip": ["V3_CORE_Tidb_Clus_4.190.81.0/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_10250", "TCP_10255", "TCP_8080", "TCP_2379", "ssh", "TCP_9090-9091", "TCP-2379-2380", "TCP-9100-20182", "TCP-8249-8250", "TCP_31306", "TCP-10080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "32", "rule_name": "12", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_Tidb_Monitor4.190.121.221"], "destination_ip": ["V3_CORE_TIDB_F5_4.190.162.1", "V3_CORE_TIDB_F5_4.190.162.2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "33", "rule_name": "13", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24", "V3_GW_K8SNODE_4.190.44.0/24", "V3_MS_4.190.120.0/22"], "destination_ip": ["Zookeeper"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "34", "rule_name": "14", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["Redis_oltp", "Redis_feioltp"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "35", "rule_name": "15", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "36", "rule_name": "16", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["UDP_8472", "TCP-30350"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "37", "rule_name": "17", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["JianKong_4.190.121.0"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_12049", "TCP_9100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "38", "rule_name": "18", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_Tidb_Clus_4.190.81.0/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "40", "rule_name": "20", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["casgw_4.190.162.4", "CAS_F5_4.190.162.3"], "destination_zone": "TENANT02_CORE_Inside", "services": ["https", "TCP_28080", "TCP_28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "41", "rule_name": "21", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_4.190.122.12/32", "V3_MS_************/32"], "destination_ip": ["V3_CORE_4.190.80.71_73"], "destination_zone": "TENANT02_CORE_Inside", "services": ["http", "TCP_3558"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "42", "rule_name": "22", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_4.190.122.12/32", "V3_MS_************/32"], "destination_ip": ["V3_CORE_4.190.80.81_83", "V3_CORE_F5_***********/24", "V3_CORE_4.190.81.21_25"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "43", "rule_name": "23", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_4.190.122.12/32", "V3_MS_************/32"], "destination_ip": ["V3_CORE_**********01/32"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "57", "rule_name": "V3-1", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["HARBOR", "OPS", "Harbor-F5-***********/32", "V3_MS_OPS_4.190.163.3"], "destination_zone": "TENANT02_CORE_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "58", "rule_name": "V3-2", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["HARBOR", "Harbor-F5-***********/32", "OPS", "V3_MS_OPS_4.190.163.3"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "59", "rule_name": "V3-13", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/21"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_31306", "ssh"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "60", "rule_name": "V3-14", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_4.190.120.0/22", "V3_MS_K8SNODE_***********/24", "DMZ-*********/24"], "destination_ip": ["V3_CORE_**********/21", "***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306", "ssh", "TCP_28081", "TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "61", "rule_name": "V3-18", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2OCS-**********", "**********-162"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306", "TCP_3555", "TCP_3558", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "62", "rule_name": "V3-19", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["yunwei-************-12"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "TCP_3555", "TCP_3558", "TCP_3191", "TCP_7001", "TCP_6370", "TCP_2379", "TCP_8472", "UDP_8472", "TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "77", "rule_name": "V3-20", "action": "pass", "source_zone": "TENANT_PUBLIC_Outside", "source_ip": ["V3_CORE_**********/22", "V3_CORE_K8SNODE_**********/24", "V3_GW_4.190.44.0/22", "DMZ_NAS_4.191.56.0/22", "V3_MS_4.190.120.0/22", "G3-GW-**********/24", "**********/22", "**********/22"], "destination_ip": ["NAS-**********"], "destination_zone": "TENANT_PUBLIC_Inside", "services": ["any"], "is_logging": "enable", "is_counting": "", "rule_status": ""}, {"id": "63", "rule_name": "V3-25", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["*********/16"], "destination_ip": ["T1_********73", "T1_NTP_*********73"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ntp", "http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "64", "rule_name": "V3-31", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["\"CORE ZABBIX PROXY-************\""], "destination_ip": ["\"ZABBIX SERVER-************-42\"", "***********5"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "65", "rule_name": "V3-32", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["\"ZABBIX SERVER-************-42\"", "***********5"], "destination_ip": ["\"CORE ZABBIX PROXY-************\""], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "66", "rule_name": "V3-34", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/22"], "destination_ip": ["G2RMXDB-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "67", "rule_name": "V3-35", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["\"G3 RMX REPORT-**********21\""], "destination_ip": ["W5RFTP-**********", "G2FTP-**********", "********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ftp", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "68", "rule_name": "V3-38", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3AMS-**********31-132"], "destination_ip": ["G2BMSDB"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "69", "rule_name": "V3-39", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********"], "destination_ip": ["G2RTQDB"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "70", "rule_name": "V3-40", "action": "pass", "source_zone": "any", "source_ip": ["**********"], "destination_ip": ["W5RFTP-**********", "G2FTP-**********", "CSLC-*******"], "destination_zone": "any", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "76", "rule_name": "V3-41", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/22", "V3_CORE_K8SNODE_**********/24", "CAS-**********/22"], "destination_ip": ["NAS_4.191.40.3", "NAS_**********", "NAS-**********-6"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "71", "rule_name": "V3-44", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["*********/16"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_29092"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "72", "rule_name": "V3-45", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "DMZ-*********/24"], "destination_ip": ["V3_CORE_**********/22", "***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "73", "rule_name": "V3-46", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BOCC", "*********/16"], "destination_ip": ["V3_CORE_**********/22", "***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "74", "rule_name": "V3-47", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["CSLC-*******", "CSLC-*******"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh", "TCP-34443"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "78", "rule_name": "V3-48", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh", "TCP_3191", "TCP_3555", "TCP_3558", "TCP_5003", "TCP_6370", "TCP_7001", "TCP_8080", "TCP_8472", "UDP_8472", "TCP_2379", "http", "TCP_31306", "TCP-10251-10252", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "TCP_7100", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "90", "rule_name": "V3-49", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_10250", "TCP_10255"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "91", "rule_name": "V3-50", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_10250", "TCP_10255"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "97", "rule_name": "V3_51", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-29090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "103", "rule_name": "V3_52", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["**********"], "destination_ip": ["**********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "98", "rule_name": "V3-52", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_9100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "108", "rule_name": "V3-53", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_3191", "TCP_9100", "TCP-5000_5007", "TCP-6379"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "113", "rule_name": "V3-54", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["Redis_feioltp", "Redis_oltp"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "116", "rule_name": "V3-55", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/21", "V3_CORE_K8SNODE_**********/24"], "destination_ip": ["G2SBCDB-*********-37", "G2SBCDB-**********-36"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555", "TCP-3556"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "117", "rule_name": "V3-56", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_**********/22", "***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "118", "rule_name": "V3-57", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["DMZ-*********/24", "*********/16", "CSLC-baoleiji-**********", "TicaiDC-********-2"], "destination_ip": ["V3_DNS_4.190.80.51", "V3_DNS_4.190.80.52"], "destination_zone": "TENANT02_CORE_Inside", "services": ["dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "122", "rule_name": "V3-58", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["hermes-***********-16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_5003"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "123", "rule_name": "V3-59", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["V3_CORE_**********/22"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_6370"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "124", "rule_name": "V3-60", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["*******/8"], "destination_ip": ["T1_********73", "T1_NTP_*********73"], "destination_zone": "TENANT02_CORE_Outside", "services": ["http", "ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "137", "rule_name": "V3-61", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_**********/22"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3558", "TCP-31306", "TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "119", "rule_name": "V3_AMS-To-<PERSON><PERSON><PERSON>", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["F5-AMS-************"], "destination_ip": ["JiGuan-***************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["https", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "4", "rule_name": "ospf", "action": "pass", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["ospf", "ping"], "is_logging": "enable", "is_counting": "", "rule_status": ""}, {"id": "26", "rule_name": "T1 To G3-**********/22", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["T1_18.0.0.0/16", "TEST_4.176.0.0/16"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "ftp", "tftp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "80", "rule_name": "V3-T1_Harbor", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["********01"], "destination_zone": "TENANT02_CORE_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "109", "rule_name": "T1_DNS_V3_DNS", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["T1_CeShi_********"], "destination_ip": ["V3_DNS_4.190.80.51", "V3_DNS_4.190.80.52"], "destination_zone": "TENANT02_CORE_Inside", "services": ["dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "126", "rule_name": "G3-RMX-G2RMX", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/22"], "destination_ip": ["T1_18.0.2.133/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "133", "rule_name": "Backups-***********", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["TENANT02-CORE-**********53/32", "TENANT02-CORE-***********/32", "TENANT02-CORE-***********-16", "TENANT02-CORE-**********71-173"], "destination_ip": ["Backup-Server_***********/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh", "TCP-8400-8900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "134", "rule_name": "Backup_Server_To_TENANT02_CORE", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["Backup-Server_***********/32"], "destination_ip": ["TENANT02-CORE-**********53/32", "TENANT02-CORE-***********-16", "TENANT02-CORE-**********71-173", "TENANT02-CORE-***********/32"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "TCP-8400-8900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "136", "rule_name": "G3-Network-Test", "action": "pass", "source_zone": "TENANT03_CORE_Inside", "source_ip": ["Test-Port1-*************/32"], "destination_ip": ["Test-Port2-************/32"], "destination_zone": "TENANT03_CORE_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "138", "rule_name": "G3-V1.3-20200429-01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2OCS_**********/32"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "TCP_31306", "TCP_3555", "TCP_3558"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "139", "rule_name": "G3-V1.3-20200429-02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "TCP_31306", "TCP_3555", "TCP_3558", "TCP_8080", "http", "https", "TCP_8000", "TCP-3557"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "140", "rule_name": "G3-V1.3-20200429-03", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_CORE_**********41-143", "G3_CORE_F5_***********/32"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "141", "rule_name": "G3-V1.3-20200429-04", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["RMXAS-VIP_**********33", "G3_CORE__4.190.162.7/32", "G3_CORE_************/32", "CAS_F5_4.190.162.3", "G3AMS-**********31-132"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3601", "TCP-9090", "https", "TCP-8022", "TCP_9110", "TCP_9100", "TCP_9130", "TCP_9120", "TCP-9130"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "142", "rule_name": "G3-V1.3-20200429-05", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_*********/16"], "destination_ip": ["G2_NTP"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ntp", "UDP_123"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "143", "rule_name": "G3-V1.3-20200429-06", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["***********", "***********", "CAS_F5_4.190.162.3"], "destination_ip": ["G2_DC_********/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "144", "rule_name": "G3-V1.3-20200429-07", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_4.190.80.71_73"], "destination_ip": ["G2_SBCDB_18.0.2.37/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3556"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "145", "rule_name": "G3-V1.3-20200429-08", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3-CORE_RMXDB_***********1-113"], "destination_ip": ["G2_RMXDB_**********/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "146", "rule_name": "G3-V1.3-20200429-09", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_RMX-Report_**********22/32", "\"G3 RMX REPORT-**********21\""], "destination_ip": ["W5RFTP-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-21"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "147", "rule_name": "G3-V1.3-20200429-10", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2MATSERVER"], "destination_ip": ["F5-AMS-************", "G3AMS-TEMP-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-30001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "148", "rule_name": "G3-V1.3-20200429-11", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_CORE_**********02/32", "V3_CORE_**********01/32"], "destination_ip": ["G2SBCDB-*********-37", "G2SBCDB-**********-36"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "149", "rule_name": "G3-V1.3-20200429-12", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["G2-RTQ"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "150", "rule_name": "G3-V1.3-20200429-13", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["\"G3 RMX REPORT-**********21\"", "G3_RMX-Report_**********22/32"], "destination_ip": ["W5RFTP-**********", "G2FTP-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-21"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "151", "rule_name": "G3-V1.3-20200429-14", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["W5RFTP-**********", "G2FTP-**********", "CSLC_*******/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "152", "rule_name": "G3-V1.3-20200429-15", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2_AMS"], "destination_ip": ["Redis_feioltp01_4.190.80.31", "Redis_feioltp02_4.190.80.32", "Redis_feioltp03_4.190.80.33", "Redis_feioltp04_4.190.80.34", "Redis_feioltp05_4.190.80.35", "Redis_feioltp06_4.190.80.36", "Redis_feioltp07_4.190.80.37", "Redis_feioltp08_4.190.80.38", "Redis_feioltp09_4.190.80.39"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "153", "rule_name": "G3-V1.3-20200429-16", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_CORE_SDAS01_**********91/32", "G3_CORE_SDAS02_**********92/32"], "destination_ip": ["G2_18.0.2.96/32", "G2_18.0.1.75/32", "G2_18.0.2.189/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3555", "TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "154", "rule_name": "To_Backup_Server-***********", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["Backup-Server_***********/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["https", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "155", "rule_name": "G3-V1.3-OPERVM-G2RTQ", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["G2-RTQ"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "156", "rule_name": "G3CORE-to-G2ARMREDIS", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********"], "destination_ip": ["*********-26"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "157", "rule_name": "_to_", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["CSLC_*******/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "158", "rule_name": "RMXDB_TO_CSLC", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3-CORE_RMXDB_***********1-113", "V3_CORE_4.190.80.71_73"], "destination_ip": ["CSLC-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-1521"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "159", "rule_name": "CSLC-baoleiji", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["CSLC-baoleiji-**********", "CSLC-baoleiji-*********"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "https", "http", "TCP_3389", "TCP-8888", "TCP-8889", "TCP-8013", "TCP-8090", "TCP_8000", "TCP_3555", "TCP_3558", "TCP_31306", "TCP-5001-5030", "TCP-8080", "TCP-4555", "TCP-30900", "TCP_30902", "TCP-8022", "TCP-8018"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "160", "rule_name": "TO_G2FTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["*********/16"], "destination_ip": ["G2FTP-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ftp", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "161", "rule_name": "G3-V131", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BASDB-*********-75"], "destination_ip": ["DES-F5-***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-8018"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "162", "rule_name": "G3-V131-01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BASDB-*********-75"], "destination_ip": ["G3_CORE_F5_***********/32"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_28070", "TCP_28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "163", "rule_name": "G3-V131-02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BASDB-*********-75"], "destination_ip": ["V3_CORE_TIDB_F5_4.190.162.1"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "164", "rule_name": "To-ƽ̨", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24", "V3_CORE_**********/22", "V3_CORE_K8SNODE_4.190.85.0/24", "V3_CORE_K8SNODE_4.190.86.0/24"], "destination_ip": ["CSLC-DIP-***********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-9092", "TCP-2181"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "165", "rule_name": "V3-62", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_CORE_**********02/32", "V3_CORE_**********01/32"], "destination_ip": ["G3MATGW-F5-***********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-8085"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "166", "rule_name": "G3OPERVM-TO-TRANSROUTE", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["G2TRANSROUTE-**********-84"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "167", "rule_name": "IRM_170_IRM_G3", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["IRMDB-***********-133"], "destination_ip": ["G3-CORE_RMXDB_***********1-113", "V3_CORE_4.190.80.71_73"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3558", "TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "168", "rule_name": "IRM_170_G3_IRM", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["IRMDB-***********-133"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "169", "rule_name": "G3", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["JianKong_4.190.121.0"], "destination_ip": ["hermes-***********-16", "**********-3"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-5000_5007", "TCP_6370"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "170", "rule_name": "G3V140", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/22", "**********", "**********/24"], "destination_ip": ["G2_18.0.2.189/32", "G2ARMDB-**********", "G2ActiveMQ-**********-213", "G2_18.0.2.96/32", "G2_18.0.1.75/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-31306", "TCP-3555", "TCP-62738"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "171", "rule_name": "G3V140-1", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_**********/21", "V3_MS_4.190.120.0/22", "V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-4100", "TCP-6370", "TCP-5000_5007", "TCP_5008"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "172", "rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["FOC-*********", "FOC-***********"], "destination_ip": ["any"], "destination_zone": "TENANT02_CORE_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "173", "rule_name": "R1401_20200724", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["G2_18.0.1.75/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "174", "rule_name": "G3_R1.4.0.2", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["Caiwuzhong_FTP-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-34443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "175", "rule_name": "Xshell", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********/24"], "destination_ip": ["T1_18.2.1.0/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-6000"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "176", "rule_name": "G2-BASDB_G3-BASDB", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BASDB-*********-75"], "destination_ip": ["**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "177", "rule_name": "TO_CSLC_SJZT", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_4.190.80.71", "V3_CORE_4.190.80.72", "G3-CORE_RMXDB_***********1-113"], "destination_ip": ["SJZT-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-1521"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "178", "rule_name": "TO_G2_BASDB", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["G2_18.0.1.75/32", "G2BISDB-*********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "179", "rule_name": "G3_CORE_K8SNODE To CSLC_OPENAPI", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_CORE_K8SNODE_**********/24"], "destination_ip": ["CSLC_OPENAPI_4.60.12.90/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": "Disable"}, {"id": "180", "rule_name": "G3RDC-G2RDC", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3RDC-**********-2"], "destination_ip": ["ABSRDCB01-*********", "CSLRDCA-********-2", "CSLRDCB-*********-2", "CSLRDCP-********-2", "W5RRDC-*********-2", "*********/16"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "enable", "is_counting": "", "rule_status": ""}, {"id": "181", "rule_name": "G2RDC-G3RDC", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["ABSRDCB01-*********", "CSLRDCA-********-2", "CSLRDCB-*********-2", "CSLRDCP-********-2", "W5RRDC-*********-2", "*********/16"], "destination_ip": ["G3RDC-**********-2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "182", "rule_name": "G3NTP-CSLCNTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3NTP-************-252"], "destination_ip": ["CSLCNTP-*******"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "183", "rule_name": "G3RDC_CSLCNTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3RDC-**********-2"], "destination_ip": ["CSLCNTP-*******"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "185", "rule_name": "To_G3_CORE_NTP", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G3_*********/16", "G3_*********/16"], "destination_ip": ["G3NTP-************-252"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ntp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "186", "rule_name": "G3_CAS_To_G2_AD", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_CORE_CAS_***********-42", "G3_CORE_CAS_4.190.81.51-52"], "destination_ip": ["G2_AD_18.0.10.200/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "187", "rule_name": "SDAS-V2.15.1_01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2BISRV-**********-42"], "destination_ip": ["BASDB-***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "188", "rule_name": "SDAS-2.15.1_02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2BISRV-**********-42", "SBSG2GRSAS-***********-32"], "destination_ip": ["SDAS-BLIDB-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "229", "rule_name": "SDAS-V2.15.1_02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2BISRV-**********-42"], "destination_ip": ["RTQDB-***********-33"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "231", "rule_name": "SDAS-2.15.1_03", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_CORE_SDAS01_**********91/32", "G3_CORE_SDAS02_**********92/32"], "destination_ip": ["CSLC-DIP-***********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-2181", "TCP-7070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "189", "rule_name": "G2IRM_G3IRM", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["IRMDB-***********-133"], "destination_ip": ["G3IRMDBVIP-**********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "190", "rule_name": "SSM-Ansbile", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["Ansbile-************", "*************"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "191", "rule_name": "MHA", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["MHA-*************", "Ansbile-************"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "192", "rule_name": "DIP-IRMDB", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["CSLC-DIP-***********", "**********-55", "**********-74"], "destination_ip": ["G3IRMDBVIP-**********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "193", "rule_name": "IRMDB_W5RFTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3IRMDB-**********-2"], "destination_ip": ["W5RFTP-*********", "ShuJuJiChengPingTai-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "194", "rule_name": "IRM1.15.0_01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2IRMAS-***********-62", "SBSG2IRMBMO-**********-32"], "destination_ip": ["G3IRMDBVIP-**********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "195", "rule_name": "IRM1.15.0_02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["CORE-F5-***********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "196", "rule_name": "OPSFTP", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["*********/16"], "destination_ip": ["SBSG2OPSFTP01-**********31"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "ftp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "197", "rule_name": "IRM1.15.0_03", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********/24"], "destination_ip": ["JianKong_4.190.121.0"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "198", "rule_name": "IRM1.15.0_04", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3555", "TCP-3558", "TCP-6370", "TCP_3191", "TCP_7001", "TCP_2379", "TCP_5003", "ssh", "http", "UDP_8472", "TCP-8022"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "200", "rule_name": "IRM1.15.0_05", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********/24"], "destination_ip": ["MonitorZK-*************-135"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "199", "rule_name": "ItoSchedule", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["ItoSchedule-*************-172"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306", "TCP-3555", "TCP-3557", "TCP-3558", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "201", "rule_name": "G3-R160_01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2MATSERVER-***********-22"], "destination_ip": ["BMSDB-**********1-15", "RTQDB-***********-33"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "202", "rule_name": "G3-R160_02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2MATSERVER-***********-22"], "destination_ip": ["G3AMS-**********01-102", "AMS-CHANNEL-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-30001", "TCP-30002"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "203", "rule_name": "G3-R160_03", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2WEBDC-**********1-12"], "destination_ip": ["SBSG2IHS-************-142"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31099", "TCP-31100", "TCP-31399", "TCP-52704", "TCP-35302"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "204", "rule_name": "G3-R160_04", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SBSG2IHS-************-142"], "destination_ip": ["SBSG2WEBDC-**********1-12"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-31399", "TCP-31400"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "205", "rule_name": "G3-R160_05", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["BASDB-***********-43"], "destination_ip": ["W5ROPCC-*********02", "*********/16"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-21"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "206", "rule_name": "G3-R160_06", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SBSG2IHS-************-142"], "destination_ip": ["SBSG2GRSAS-***********-32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-31399", "TCP-31400"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "207", "rule_name": "G3-R160_07", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********/24"], "destination_ip": ["G3_*********/16", "BOCC", "*********/16"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh", "ftp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "208", "rule_name": "G3-R160_08", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2WEBDC-**********1-12"], "destination_ip": ["IHSF5-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31099"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "209", "rule_name": "G3-R160_09", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2GRSAS-***********-32"], "destination_ip": ["SDAS-BLIDB-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "210", "rule_name": "G3-R160_10", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_**********/21", "G3-GW-**********/22"], "destination_ip": ["F5-AMS-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-30001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "211", "rule_name": "SBSG2BMSDB_To_CSLC-Display-DB", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SBSG2BMSDB-**********1-13"], "destination_ip": ["CSLC-Display-DB-*************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-1521"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "212", "rule_name": "_To_CaiWu-FTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["CaiWu-FTP-**********/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-34443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "213", "rule_name": "SBSG2BIBASDB_To_CSLC-financialFTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["BASDB-***********-43", "RTQDB-***********-33"], "destination_ip": ["CSLC-financialFTP-**********-12"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ftp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "214", "rule_name": "RTQDB_To_UMP-FTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["RTQDB-***********-33"], "destination_ip": ["CSLC-*******"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-34443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "215", "rule_name": "BASDB_To_ShuJuJIChengPingTai", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["BASDB-***********-43"], "destination_ip": ["ShuJuJiChengPingTai-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-22"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "216", "rule_name": "SBSG2_To_HiveDaShuJu", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["BASDB-***********-43", "SBSG2GRSDS01-***********"], "destination_ip": ["Hive-DaShuJu-*********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-6100-6200"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "217", "rule_name": "SBSG2GRSDS01_To_ShuJuZhongTai", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SBSG2GRSDS01-***********"], "destination_ip": ["Shu<PERSON><PERSON><PERSON><PERSON><PERSON>-***********/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-16000", "TCP-16010", "TCP-16020", "TCP-2181", "TCP-7070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "218", "rule_name": "SDASBISer", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SDASBISer-**********11-112"], "destination_ip": ["<PERSON><PERSON><PERSON><PERSON><PERSON>-************-58", "<PERSON><PERSON><PERSON><PERSON><PERSON>-************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-7070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "219", "rule_name": "SDASBISer_To_ShuJuZhongTai-2181", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SDASBISer-**********11-112"], "destination_ip": ["<PERSON><PERSON><PERSON><PERSON><PERSON>-************-58"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-2181"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "220", "rule_name": "RTQDB_To_ReXian-FTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["RTQDB-***********-33"], "destination_ip": ["CSLC-*******"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-21"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "221", "rule_name": "G3-Core_To_CSLC-*******", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3-CORE-**********/22"], "destination_ip": ["CSLC_*******/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "222", "rule_name": "G3-CORE_To_CSLC-**********", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3-CORE-**********/22"], "destination_ip": ["CSLC-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-1521", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "223", "rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_To_SBSGRSDBVIP", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["<PERSON><PERSON><PERSON><PERSON><PERSON>-************/32", "**********-55"], "destination_ip": ["SDAS-BLIDB-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "224", "rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_To_SBSG2IRMDBVIP", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["Shu<PERSON><PERSON><PERSON><PERSON><PERSON>-***********/24"], "destination_ip": ["G3IRMDBVIP-**********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "225", "rule_name": "CSLC-K8S_To_CAS", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["CSLC-K8S-********/24"], "destination_ip": ["CAS-**********/22"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh", "TCP-31306", "TCP-18081", "TCP-28080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "226", "rule_name": "CSLC_To_CASGW-F5", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["CSLC-********/8", "CSLC-*******/8", "CSLC-K8S-********/24"], "destination_ip": ["CASGW-F5-************/32"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-28080", "TCP-28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "227", "rule_name": "IRM1.15.0_06", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********/24"], "destination_ip": ["G2_AD_18.0.10.200/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3268", "TCP_389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "228", "rule_name": "MAIL", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_*********/16"], "destination_ip": ["Mail-*********-2"], "destination_zone": "TENANT02_CORE_Outside", "services": ["smtp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "230", "rule_name": "DBlinshichuanshu", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2SBCDB-**********-36", "**********-95", "*********-95", "G2SBCDB-*********-37"], "destination_ip": ["**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "232", "rule_name": "G3-GRSDS_TO_G2-RTQDB", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SBSG2GRSDS01-***********"], "destination_ip": ["G2_18.0.2.96/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "233", "rule_name": "G3-MS-SDAS_To_G3-DMZ-SDAS", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3_CORE_SDAS01_**********91/32", "G3_CORE_SDAS02_**********92/32"], "destination_ip": ["G3-DMZ-SDAS-**********/32"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-8023-8024"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "234", "rule_name": "ECC01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BizInfo-*************-212"], "destination_ip": ["G3OPERVM01-**********-2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["tcp-9044"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "235", "rule_name": "Solarwinds", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["Solarwinds-**********"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "236", "rule_name": "LinShirleyARMDB", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2ARMDB-**********"], "destination_ip": ["ARMDB-***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "237", "rule_name": "LinShiFTP", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2FTP-**********"], "destination_ip": ["FTP-**********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "238", "rule_name": "LINSHI-RESID", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G2REDIS-********/24"], "destination_ip": ["G3-CORE-**********/22"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "239", "rule_name": "LINSHIRPT", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["**********"], "destination_ip": ["**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-22"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "240", "rule_name": "G3OPERVM_XWHFTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2", "**********/24"], "destination_ip": ["W5RFTP-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh", "ftp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "241", "rule_name": "XWHBOCC_FTP", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BOCC", "*********/16"], "destination_ip": ["SBSG2OPSFTP01-**********31"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ftp", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "242", "rule_name": "TO_OperatorVM", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["G3OPERVM01-**********-2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["tcp-9044"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "243", "rule_name": "AMSRPT", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SBSG2MATSERVER-***********-22", "T1_18.2.1.0/24", "G3RDC-**********-2"], "destination_ip": ["AMSRPT-F5-************", "BISRPT-F5-************", "G3RDC-**********-2", "T1_18.2.1.0/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "243", "rule_name": "AMSRPT", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["SBSG2MATSERVER-***********-22", "T1_18.2.1.0/24", "G3RDC-**********-2"], "destination_ip": ["AMSRPT-F5-************", "BISRPT-F5-************", "G3RDC-**********-2", "T1_18.2.1.0/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "246", "rule_name": "CAS-TO-RDC", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G3_MS_4.190.122.21-24", "G3_MS_4.190.121.231-234"], "destination_ip": ["RDC-F5-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-3268", "TCP_389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "247", "rule_name": "SCAN", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["FOC-***********-137"], "destination_ip": ["G3RDC-**********-2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "248", "rule_name": "TO-SFTP", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["*********/16"], "destination_ip": ["SFTP-************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "249", "rule_name": "G3_163_01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["BISMONITORCOLLECT-F5"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-26100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "250", "rule_name": "G3_JianCe2.0_01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["G3_CORE_K8SNODE_**********/24"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-26100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "251", "rule_name": "CSLOPCC", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["CSLCOPCC-*********/24"], "destination_ip": ["************", "************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "252", "rule_name": "linshtest", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3-CORE-**********/22"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_CORE_Outside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": "Disable"}, {"id": "253", "rule_name": "***********", "action": "pass", "source_zone": "TENANT_PUBLIC_Outside", "source_ip": ["***********"], "destination_ip": ["NAS-**********/21"], "destination_zone": "TENANT_PUBLIC_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "254", "rule_name": "SDAS-1.18.0", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["CSLC-baoleiji-**********"], "destination_ip": ["G3_CORE_************/32", "G3_CORE_SDAS01_**********91/32", "G3_CORE_SDAS02_**********92/32"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-8022"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "255", "rule_name": "XWHBOCC_TO_NTP", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["*********/16", "4.176.11-13.0/24"], "destination_ip": ["G3NTP-************-252"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "256", "rule_name": "YZECC_TO_RDC", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["YZ-ECC-*********"], "destination_ip": ["G3RDC-**********-2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "257", "rule_name": "RDC_TO_YZECC", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3RDC-**********-2"], "destination_ip": ["YZ-ECC-*********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "258", "rule_name": "MS_K8SNODE_TO_NAS", "action": "pass", "source_zone": "TENANT_PUBLIC_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["NAS_**********_PUBLIC_OUTSIDE"], "destination_zone": "TENANT_PUBLIC_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "259", "rule_name": "G3RMX-R130-01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["G3ARESRISK"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-4100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "260", "rule_name": "G3RMX-R130-02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-5001-5030", "TCP-6370"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "266", "rule_name": "YZBOCC-ANY", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["YZBOCC"], "destination_ip": ["G3RDC-**********-2", "RDC-F5-************"], "destination_zone": "TENANT02_CORE_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "267", "rule_name": "ANY-YZBOCC", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3RDC-**********-2", "RDC-F5-************"], "destination_ip": ["YZBOCC"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "270", "rule_name": "<PERSON><PERSON>", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3-CORE-**********/22", "CAS-**********/22"], "destination_ip": ["Radius-**********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["UDP-1812"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "271", "rule_name": "RMX131", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["BOCC&4A"], "destination_ip": ["RMXAS-VIP_**********33"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_9120"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "272", "rule_name": "R240-01", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G3TSPAPP01-*************", "G3TSPAPP02-*************"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_7001", "TCP_6370", "TCP-5000_5007", "TCP_31306", "TCP_3558", "TCP_3555", "TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "273", "rule_name": "R240-02", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["G3TSPAPP01-*************", "G3TSPAPP02-*************"], "destination_ip": ["V3_CORE_TIDB_F5_4.190.162.1", "V3_CORE_TIDB_F5_4.190.162.2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "274", "rule_name": "R240-03", "action": "pass", "source_zone": "TENANT_PUBLIC_Outside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["NAS-**********/32"], "destination_zone": "TENANT_PUBLIC_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "278", "rule_name": "OPERVM-TO-ItoSchedule", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["ItoSchedule-*************-172"], "destination_zone": "TENANT02_CORE_Outside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "279", "rule_name": "*************-*********", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_*************/32"], "destination_ip": ["*********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "281", "rule_name": "K8s-To-Hermes-sharding", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["Hermes-************-216", "************-220"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-5001-5030"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "282", "rule_name": "MHA-To-G3BOSDB", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["MHA-*************"], "destination_ip": ["G3BOSDB-************-104"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "283", "rule_name": "**********-To-************", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8082"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "284", "rule_name": "**********-To-*************", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["*************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_30200", "TCP_30201"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "285", "rule_name": "***********-**********", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["************-14"], "destination_ip": ["************-103"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "291", "rule_name": "IRM-1.27.0", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SBSG2IRMAS-***********-62"], "destination_ip": ["V3_CORE_**********/21"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "290", "rule_name": "CORE-K8S-TO-NAS", "action": "pass", "source_zone": "TENANT_PUBLIC_Outside", "source_ip": ["G3_CORE_K8SNODE_**********/24"], "destination_ip": ["NAS_**********"], "destination_zone": "TENANT_PUBLIC_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "292", "rule_name": "VDI-TO-debugtool", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["Test-<PERSON><PERSON><PERSON>"], "destination_ip": ["G3OPERVM01-**********-2"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-30900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "293", "rule_name": "Hermes-TO-Core", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["************-14"], "destination_ip": ["************-220"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-5000-5011"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "294", "rule_name": "bos-oddsdata-TO-G3BOSRedis", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24"], "destination_ip": ["***********-59"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "295", "rule_name": "G3BISMONTORCOLLECT_R340", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3BISMONTORCOLLECT"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-31306", "TCP-3558", "TCP_3555", "TCP_3191", "TCP_7001", "TCP_6370", "TCP_2379", "TCP-10251-10252", "TCP_8080", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "TCP-22", "http", "TCP_7100", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "296", "rule_name": "VulnerabilityScan_Network", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["VulnerabilityScan-************"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "298", "rule_name": "G3OPERVM01_************", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3OPERVM01-**********-2"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8082"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "297", "rule_name": "K8SNODE_to_*********/16", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_31306", "TCP_3558", "TCP_3555", "TCP_3191", "TCP_7001", "TCP_6370", "TCP_2379", "TCP-10251-10252", "https", "http", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "TCP_8080", "ssh", "TCP_7100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "300", "rule_name": "*********_************", "action": "pass", "source_zone": "TENANT_PUBLIC_Inside", "source_ip": ["*********/16"], "destination_ip": ["************"], "destination_zone": "TENANT_PUBLIC_Outside", "services": ["UDP_161", "UDP_162", "UDP_514"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "299", "rule_name": "flink_**********", "action": "pass", "source_zone": "TENANT_PUBLIC_Outside", "source_ip": ["flink-************1-119", "flink-*************-126"], "destination_ip": ["**********", "nfs-**********"], "destination_zone": "TENANT_PUBLIC_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "301", "rule_name": "PLSQL-lianjiegongju", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["PLSQL-gongju"], "destination_ip": ["**********5"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP_3557"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "302", "rule_name": "TO-NFS", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["any"], "destination_ip": ["NFS-*********"], "destination_zone": "TENANT02_CORE_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "303", "rule_name": "TO-523", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["**********"], "destination_ip": ["SDAS-BLIDB-************", "BASDB-***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-31306", "TCP-3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "304", "rule_name": "TC-************-203", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SG_10.196.128_129.0/24"], "destination_ip": ["Hermes-mata-************-203"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-6370"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "305", "rule_name": "TC-************-216", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["SG_10.196.128_129.0/24"], "destination_ip": ["Hermes-************-216"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-5000-5011"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "306", "rule_name": "*********_************", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["*********/16"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-30002"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "307", "rule_name": "TO_EDRagent", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["************-103", "CAS-**********/22", "G3NTP-************-252"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["tcp destination eq 8443", "tcp destination eq 6677", "tcp destination eq 7788", "tcp destination eq 8001", "tcp destination eq 8002", "tcp destination eq 80"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "308", "rule_name": "K8S-node_Meta", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["K8S-node"], "destination_ip": ["Meta"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-6370"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "309", "rule_name": "K8S-node_CWZX", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["K8S-node"], "destination_ip": ["CWZX"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-30021"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "310", "rule_name": "DNS_YZGTM", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_DNS_4.190.80.51", "V3_DNS_4.190.80.52"], "destination_ip": ["YZGTM"], "destination_zone": "TENANT02_CORE_Outside", "services": ["dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "311", "rule_name": "XIUSHI", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["***********-91"], "destination_ip": ["**********-4", "**********/24"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "312", "rule_name": "XIUSHI2", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********-2"], "destination_ip": ["***********-14", "***********-12"], "destination_zone": "TENANT02_CORE_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "313", "rule_name": "XIUSHI3", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["***********-91"], "destination_ip": ["************-72", "************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_29093"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "314", "rule_name": "XIUSHI4", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["OPS"], "destination_ip": ["***********-91"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "315", "rule_name": "XIUSHI5", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["***********-91"], "destination_ip": ["************", "*************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "316", "rule_name": "XIUSHI6", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["***********-14"], "destination_ip": ["***********-91"], "destination_zone": "TENANT02_CORE_Inside", "services": ["TCP-30020"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "318", "rule_name": "AMoffline", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["*********/16"], "destination_ip": ["************", "************", "*************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8001", "TCP_5001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "317", "rule_name": "IRM_KYT", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["**********"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8088"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "319", "rule_name": "dc-to-socSyslog", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["G3RDC-**********-2"], "destination_ip": ["SOC-*************-112"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_8400", "UDP_514"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "320", "rule_name": "K8SNODE-to-DUANXIN", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/22", "V3_CORE_K8SNODE_**********/24", "V3_CORE_K8SNODE_4.190.85.0/24", "V3_CORE_K8SNODE_4.190.86.0/24"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "321", "rule_name": "K8SNODE-to-URSF5-30514", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_**********/22", "V3_CORE_K8SNODE_**********/24", "V3_CORE_K8SNODE_4.190.85.0/24", "V3_CORE_K8SNODE_4.190.86.0/24"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["TCP_30514"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "46", "rule_name": "deny", "action": "drop", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}]
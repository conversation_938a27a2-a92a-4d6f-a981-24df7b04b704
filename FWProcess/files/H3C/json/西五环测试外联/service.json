[{"service": "135-139", "tcp": {"dst-port": "135 139"}, "udp": {"dst-port": "135 139"}}, {"service": "22443", "tcp": {"dst-port": "22443"}, "udp": {"dst-port": "22443"}}, {"service": "32111", "tcp": {"dst-port": "32111"}, "udp": {"dst-port": "32111"}}, {"service": "34443", "tcp": {"dst-port": "34443"}}, {"service": "4172", "tcp": {"dst-port": "4172"}, "udp": {"dst-port": "4172"}}, {"service": "80"}, {"service": "9427", "tcp": {"dst-port": "9427"}, "udp": {"dst-port": "9427"}}, {"service": "BT终端访问开发业务端口", "tcp": {"dst-port": "8443 30000 21"}}, {"service": "port\"", "tcp": {"dst-port": "111 2049 4046 635 1234"}, "udp": {"dst-port": "111 2049 4046 635 1234"}}, {"service": "30088\"", "tcp": {"dst-port": "30088"}}, {"service": "TCP-10050", "tcp": {"dst-port": "10050"}}, {"service": "tcp-123", "tcp": {"dst-port": "123"}}, {"service": "TCP-139", "tcp": {"dst-port": "139"}}, {"service": "TCP-1433", "tcp": {"dst-port": "1433"}}, {"service": "TCP-1443", "tcp": {"dst-port": "1443"}}, {"service": "TCP-172", "tcp": {"dst-port": "172"}}, {"service": "TCP-2181"}, {"service": "tcp-24432", "tcp": {"dst-port": "24432"}}, {"service": "tcp-30000", "tcp": {"dst-port": "30000"}}, {"service": "tcp-30071", "tcp": {"dst-port": "30071"}}, {"service": "TCP-31306", "tcp": {"dst-port": "31306"}}, {"service": "TCP-384", "tcp": {"dst-port": "384"}}, {"service": "tcp-389", "tcp": {"dst-port": "389"}}, {"service": "TCP-434", "tcp": {"dst-port": "434"}}, {"service": "TCP-44", "tcp": {"dst-port": "44"}}, {"service": "tcp-4422", "tcp": {"dst-port": "4422"}}, {"service": "TCP-445", "tcp": {"dst-port": "445"}}, {"service": "tcp-5080", "tcp": {"dst-port": "5080"}}, {"service": "TCP-5222", "tcp": {"dst-port": "5222"}}, {"service": "TCP-5480", "tcp": {"dst-port": "5480"}}, {"service": "TCP-8090", "tcp": {"dst-port": "8090"}}, {"service": "TCP10000", "tcp": {"dst-port": "10000"}}, {"service": "TCP1521", "tcp": {"dst-port": "1521"}}, {"service": "TCP21050", "tcp": {"dst-port": "21050"}}, {"service": "TCP21051", "tcp": {"dst-port": "21051"}}, {"service": "TCP2181", "tcp": {"dst-port": "2181"}}, {"service": "TCP22", "tcp": {"dst-port": "22"}}, {"service": "TCP2883", "tcp": {"dst-port": "2883"}}, {"service": "TCP30000", "tcp": {"dst-port": "30000"}}, {"service": "TCP30010", "tcp": {"dst-port": "30010"}}, {"service": "TCP30080", "tcp": {"dst-port": "30080"}}, {"service": "TCP3306", "tcp": {"dst-port": "3306"}}, {"service": "TCP3389", "tcp": {"dst-port": "3389"}}, {"service": "TCP6677", "tcp": {"dst-port": "6677"}}, {"service": "TCP7788", "tcp": {"dst-port": "7788"}}, {"service": "TCP8001", "tcp": {"dst-port": "8001"}}, {"service": "TCP8002", "tcp": {"dst-port": "8002"}}, {"service": "TCP8020", "tcp": {"dst-port": "8020"}}, {"service": "TCP8082", "tcp": {"dst-port": "8082"}}, {"service": "TCP8083", "tcp": {"dst-port": "8083"}}, {"service": "TCP8088", "tcp": {"dst-port": "8088"}}, {"service": "TCP8443", "tcp": {"dst-port": "8443"}}, {"service": "TCP8510", "tcp": {"dst-port": "8510"}}, {"service": "TCP8889", "tcp": {"dst-port": "8889"}}, {"service": "TCP9080", "tcp": {"dst-port": "9080"}}, {"service": "TCP9092", "tcp": {"dst-port": "9092"}}, {"service": "TCP9201", "tcp": {"dst-port": "9201"}}, {"service": "tcp_10081", "tcp": {"dst-port": "10081"}}, {"service": "tcp_10082", "tcp": {"dst-port": "10082"}}, {"service": "TCP_2181", "tcp": {"dst-port": "2181"}}, {"service": "tcp_25", "tcp": {"dst-port": "25"}}, {"service": "TCP_30071", "tcp": {"dst-port": "30071"}}, {"service": "TCP_30514", "tcp": {"dst-port": "30514"}}, {"service": "TCP_39092", "tcp": {"dst-port": "39092"}}, {"service": "tcp_4172", "tcp": {"dst-port": "4172"}}, {"service": "TCP_49092", "tcp": {"dst-port": "49092"}}, {"service": "tcp_53", "tcp": {"dst-port": "53"}}, {"service": "tcp_80", "tcp": {"dst-port": "80"}}, {"service": "tcp_8080", "tcp": {"dst-port": "8080"}}, {"service": "tcp_8081", "tcp": {"dst-port": "8081"}}, {"service": "tcp_8082", "tcp": {"dst-port": "8082"}}, {"service": "tcp_8443", "tcp": {"dst-port": "8443"}}, {"service": "TCP_9093", "tcp": {"dst-port": "9093"}}, {"service": "UDP-53", "udp": {"dst-port": "53"}}, {"service": "UDP443", "udp": {"dst-port": "443"}}, {"service": "UDP8443", "udp": {"dst-port": "8443"}}, {"service": "udp_4172", "udp": {"dst-port": "4172"}}, {"service": "骏彩访问服务", "tcp": {"dst-port": "4001 4002 66 68"}}, {"service": "开发测试环境-互联网访问服务", "tcp": {"dst-port": "8319 8090 30000 30001 8080 8082 10248 5080 34443 14433 24433 8889 81"}}]
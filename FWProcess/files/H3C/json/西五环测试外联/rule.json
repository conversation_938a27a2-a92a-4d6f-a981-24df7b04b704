[{"id": "195", "rule_name": "20231205丁楠需求2", "action": "pass", "source_zone": "Trust", "source_ip": ["************/32"], "destination_ip": ["************/32"], "destination_zone": "Untrust", "services": ["TCP22"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "194", "rule_name": "20231205丁楠需求", "action": "pass", "source_zone": "Trust", "source_ip": ["************/32"], "destination_ip": ["**********/32"], "destination_zone": "Untrust", "services": ["tcp-389"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "193", "rule_name": "测试监控_to_辅助运营", "action": "pass", "source_zone": "Trust", "source_ip": ["************"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "192", "rule_name": "20231204王玉玲需求", "action": "pass", "source_zone": "Trust", "source_ip": ["20231204王玉玲需求源地址"], "destination_ip": ["***********/32"], "destination_zone": "Untrust", "services": ["tcp_25"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "204", "rule_name": "uat-epb业务", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网F5selfip"], "destination_ip": ["************"], "destination_zone": "Trust", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "201", "rule_name": "办公网访问测试环境邮箱", "action": "pass", "source_zone": "Untrust", "source_ip": ["办公网蜜罐"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "200", "rule_name": "bmms业务", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网F5selfip"], "destination_ip": ["bmms项目"], "destination_zone": "Trust", "services": ["TCP30010"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "191", "rule_name": "魔方项目", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网F5selfip"], "destination_ip": ["***************", "***************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "187", "rule_name": "G3_jiance", "action": "pass", "source_zone": "Trust", "source_ip": ["\"G3 VDI&104\""], "destination_ip": ["G3172.20"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "186", "rule_name": "北京单场20231128（2）", "action": "pass", "source_zone": "Trust", "source_ip": ["***********/24", "***********/24", "***********/24"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["tcp_80"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "185", "rule_name": "北京单场20231128", "action": "pass", "source_zone": "Trust", "source_ip": ["**********/24"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["tcp_80"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "184", "rule_name": "20231128", "action": "pass", "source_zone": "Trust", "source_ip": ["**********/16"], "destination_ip": ["***********", "***********"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "183", "rule_name": "访问sement", "action": "pass", "source_zone": "Trust", "source_ip": ["any"], "destination_ip": ["生产工单系统"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "182", "rule_name": "王玉玲需求", "action": "pass", "source_zone": "Trust", "source_ip": ["王玉玲服务器"], "destination_ip": ["科技认证"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "149", "rule_name": "张润苗230829服务器安装agent", "action": "pass", "source_zone": "Untrust", "source_ip": ["**********-2", "张润苗需求源地址"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["TCP6677", "TCP7788", "TCP8001", "TCP8002", "http", "tcp_8443"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "174", "rule_name": "骏彩办公网访问测试VDI", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网（192.168段）", "骏彩办公网（NAT10.248.255.0）"], "destination_ip": ["骏彩访问服务器"], "destination_zone": "Trust", "services": ["骏彩访问服务"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "176", "rule_name": "西安办公区策略test", "action": "pass", "source_zone": "Untrust", "source_ip": ["***************-174"], "destination_ip": ["西安办公区开发终端连接测试环境", "鹏龙21层开发终端连接测试环境"], "destination_zone": "Trust", "services": ["TCP8088", "tcp_8080", "ftp", "ssh", "TCP-139", "TCP-445", "TCP8443", "TCP30000", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "181", "rule_name": "Test_VDI_To_YY-VDI", "action": "pass", "source_zone": "Trust", "source_ip": ["**********/16", "************", "**********", "10.218.45-46"], "destination_ip": ["yyvdi-10.209"], "destination_zone": "yuny_vdi", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "189", "rule_name": "YW-TC_To_TestVDI", "action": "pass", "source_zone": "yuny_vdi", "source_ip": ["10.209.2-9"], "destination_ip": ["***********", "************", "***********", "***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "190", "rule_name": "YY-V<PERSON>_To_GongXiangPan", "action": "pass", "source_zone": "yuny_vdi", "source_ip": ["yyvdi-10.209"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "198", "rule_name": "YW-VDI_To_JiaMi", "action": "pass", "source_zone": "yuny_vdi", "source_ip": ["yyvdi-10.209"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["TCP8443", "TCP-8090", "TCP-5222"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "202", "rule_name": "LotteryVDI_To_YW-Print", "action": "pass", "source_zone": "Trust", "source_ip": ["************-**************"], "destination_ip": ["YW-Print"], "destination_zone": "yuny_vdi", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "203", "rule_name": "LotteryVDI_To_YW-Gong<PERSON>iangPan", "action": "pass", "source_zone": "Trust", "source_ip": ["************-**************"], "destination_ip": ["**********"], "destination_zone": "yuny_vdi", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "72", "rule_name": "ping操作", "action": "pass", "source_zone": "Trust", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "Untrust", "services": ["icmp-address-mask", "icmp-dest-unreachable", "icmp-fragment-needed", "icmp-fragment-reassembly", "icmp-host-unreachable", "icmp-info", "icmp-parameter-problem", "icmp-port-unreachable", "icmp-protocol-unreach", "icmp-redirect", "icmp-redirect-host", "icmp-redirect-tos-host", "icmp-redirect-tos-net", "icmp-source-quench", "icmp-source-route-fail", "icmp-time-exceeded", "icmp-timestamp", "icmp-traceroute"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "172", "rule_name": "骏彩办公网访问**********", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["icmp-address-mask", "icmp-redirect-host", "icmp-port-unreachable", "icmp-parameter-problem", "icmp-info", "icmp-host-unreachable", "icmp-fragment-reassembly", "icmp-fragment-needed", "icmp-dest-unreachable", "icmp-protocol-unreach", "icmp-redirect", "icmp-redirect-tos-host", "icmp-redirect-tos-net", "icmp-source-quench", "icmp-source-route-fail", "icmp-time-exceeded", "icmp-timestamp", "icmp-traceroute", "dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "188", "rule_name": "彩银测试出向访问", "action": "pass", "source_zone": "Trust", "source_ip": ["彩银测试服务器地址"], "destination_ip": ["192.168"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "112", "rule_name": "彩银测试", "action": "pass", "source_zone": "Untrust", "source_ip": ["192.168"], "destination_ip": ["彩银测试服务器地址"], "destination_zone": "Trust", "services": ["34443", "tcp-5080"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "169", "rule_name": "vdi访问网络设备带外地址", "action": "pass", "source_zone": "Trust", "source_ip": ["**********"], "destination_ip": ["带外地址"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "168", "rule_name": "访问外网", "action": "pass", "source_zone": "Trust", "source_ip": ["10.217.130-137/139-140.0/24"], "destination_ip": ["*************-122"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "173", "rule_name": "西安办公BT链接测试", "action": "pass", "source_zone": "Untrust", "source_ip": ["***************-174"], "destination_ip": ["测试"], "destination_zone": "Trust", "services": ["TCP8443", "https", "TCP30000"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "167", "rule_name": "lotteryVDI 访问 sporttery VDI共享盘", "action": "pass", "source_zone": "Trust", "source_ip": ["*************", "************", "************", "10.217.130-139.0/24"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "166", "rule_name": "lotteryVDI访问打印机", "action": "pass", "source_zone": "Trust", "source_ip": ["10.217.131-137.0"], "destination_ip": ["运营虚拟化打印机"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "165", "rule_name": "lotteryVDI访问sportyy VDI数据库", "action": "pass", "source_zone": "Trust", "source_ip": ["10.217.131-137.0"], "destination_ip": ["***************-112", "***************"], "destination_zone": "Untrust", "services": ["TCP-1433"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "164", "rule_name": "lotteryVDI 访问 rmoas系统", "action": "pass", "source_zone": "Trust", "source_ip": ["10.217.131-137.0", "V3_CORE_K8SNODE_4.190.85.0/24", "V3_CORE_K8SNODE_4.190.86.0/24", "V3_CORE_**********/22"], "destination_ip": ["***************-102"], "destination_zone": "Untrust", "services": ["http", "https", "TCP-2181"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "163", "rule_name": "路宇访问vdi管理端", "action": "pass", "source_zone": "Trust", "source_ip": ["************-3"], "destination_ip": ["*************", "************"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "162", "rule_name": "辅助运营策略", "action": "pass", "source_zone": "Trust", "source_ip": ["**************/56"], "destination_ip": ["172.100/200.0.0"], "destination_zone": "Untrust", "services": ["ssh", "https", "TCP3389", "http", "tcp_53", "UDP-53", "TCP-5480"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "161", "rule_name": "辅助运营zbx监控", "action": "pass", "source_zone": "Trust", "source_ip": ["104.126.246-248.0"], "destination_ip": ["172.100/200.0.0"], "destination_zone": "Untrust", "services": ["TCP-10050"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "160", "rule_name": "同步传足、竞彩、北单数据及数据分析使用", "action": "pass", "source_zone": "Trust", "source_ip": ["桌面云VDI桌面"], "destination_ip": ["************-55"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "159", "rule_name": "工程中心考勤系统传数据到骏彩工时系统", "action": "pass", "source_zone": "Trust", "source_ip": ["***********"], "destination_ip": ["**************"], "destination_zone": "Untrust", "services": ["TCP-31306"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "170", "rule_name": "统一开发测试专网访问业务系统", "action": "pass", "source_zone": "Untrust", "source_ip": ["************", "************"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "157", "rule_name": "王庆帅1011", "action": "pass", "source_zone": "Untrust", "source_ip": ["*************"], "destination_ip": ["***********", "**********", "**********"], "destination_zone": "Trust", "services": ["ssh", "ftp", "icmp-address-mask", "icmp-dest-unreachable", "icmp-fragment-needed", "icmp-fragment-reassembly", "icmp-host-unreachable", "icmp-info", "icmp-parameter-problem", "icmp-port-unreachable", "icmp-protocol-unreach", "icmp-redirect", "icmp-redirect-host", "icmp-redirect-tos-host", "icmp-redirect-tos-net", "icmp-source-quench", "icmp-source-route-fail", "icmp-time-exceeded", "icmp-timestamp", "icmp-traceroute", "TCP8088"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "156", "rule_name": "西安办公区策略", "action": "pass", "source_zone": "Untrust", "source_ip": ["西安办公区终端组"], "destination_ip": ["西安办公区开发终端连接测试环境", "鹏龙21层开发终端连接测试环境"], "destination_zone": "Trust", "services": ["TCP8088", "tcp_8080", "ftp", "ssh", "TCP-139", "TCP-445", "TCP8443", "TCP30000", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "153", "rule_name": "BT访问测试网策略", "action": "pass", "source_zone": "Untrust", "source_ip": ["BT终端IP"], "destination_ip": ["20230726孙磊需求目标地址", "亦庄5鹏龙21测试团队目的地址", "开发测试环境-BT终端服务01", "开发测试环境-BT终端服务02", "开发测试环境-BT终端FTP", "开发测试环境-BT终端NTP", "BT终端访问开发业务目的地址"], "destination_zone": "Trust", "services": ["TCP8443", "tcp-30000", "https", "TCP-1443", "tcp_8080", "80", "tcp-123", "tcp-24432", "ntp", "ftp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "148", "rule_name": "定位终端", "action": "pass", "source_zone": "Untrust", "source_ip": ["**********"], "destination_ip": ["*************00"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "155", "rule_name": "张婷旭运营支持打印机到共享文件夹", "action": "pass", "source_zone": "Untrust", "source_ip": ["***************-202", "***************-202", "***************-202"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "136", "rule_name": "管理测试环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["测试环境管理（源地址）"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "151", "rule_name": "系统访问NAS", "action": "pass", "source_zone": "Untrust", "source_ip": ["系统访问NAS"], "destination_ip": ["系统组访问目标"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "132", "rule_name": "20230719路宇需求", "action": "pass", "source_zone": "Untrust", "source_ip": ["************/24"], "destination_ip": ["***********/24"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "129", "rule_name": "20230519", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["**************", "*************00", "测试环境操作终端业务", "**************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "144", "rule_name": "安全月应急策略", "action": "drop", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "145", "rule_name": "网闸关闭", "action": "drop", "source_zone": "Untrust", "source_ip": ["*************"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "146", "rule_name": "网闸关闭02", "action": "drop", "source_zone": "Trust", "source_ip": ["***********"], "destination_ip": ["*************"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "137", "rule_name": "20230726需求01", "action": "pass", "source_zone": "Untrust", "source_ip": ["*************/32"], "destination_ip": ["*************/32"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "138", "rule_name": "20230726需求02", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********/24", "***********"], "destination_ip": ["20230726目标地址01"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "139", "rule_name": "20230726需求03", "action": "pass", "source_zone": "Untrust", "source_ip": ["**************/32"], "destination_ip": ["***********-15"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "140", "rule_name": "20230726需求04", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********/24", "***********/24"], "destination_ip": ["20230726孙磊需求目标地址"], "destination_zone": "Trust", "services": ["tcp_8443", "tcp-30000", "https", "TCP-1443", "tcp_8080", "tcp_80", "tcp-123", "tcp-24432"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "141", "rule_name": "20230726需求05", "action": "pass", "source_zone": "Untrust", "source_ip": ["*************/32"], "destination_ip": ["*************/32"], "destination_zone": "Trust", "services": ["tcp-4422"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "142", "rule_name": "王庆帅0728", "action": "pass", "source_zone": "Untrust", "source_ip": ["王庆帅0728需求"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["TCP8088"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "143", "rule_name": "王庆帅0801", "action": "pass", "source_zone": "Untrust", "source_ip": ["鹏龙21层部分机器", "西安办公区终端组"], "destination_ip": ["西安办公区开发终端连接测试环境", "鹏龙21层开发终端连接测试环境"], "destination_zone": "Trust", "services": ["TCP-139", "TCP-445", "ssh", "ftp", "TCP8088", "tcp_8080", "TCP8443", "tcp-30000", "https", "tcp-24432", "tcp-123", "TCP-1443", "http"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "147", "rule_name": "朱云峰0817拉新项目互联网发布", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网F5selfip"], "destination_ip": ["************"], "destination_zone": "Trust", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "133", "rule_name": "朱云峰测试", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网F5selfip"], "destination_ip": ["*************", "*************", "***********", "***********", "************", "************-12", "***********", "**************"], "destination_zone": "Trust", "services": ["tcp_8080", "http", "https", "TCP9080", "tcp_8081", "TCP8082"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "19", "rule_name": "可以访问**********的用户", "action": "pass", "source_zone": "Untrust", "source_ip": ["运营中心VDI白名单"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "18", "rule_name": "**********", "action": "drop", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "127", "rule_name": "办公网访问协同考勤服务器", "action": "pass", "source_zone": "Untrust", "source_ip": ["办公网访问协调考勤主机"], "destination_ip": ["协同考勤服务器"], "destination_zone": "Trust", "services": ["80", "TCP3389", "TCP-1443", "TCP-1433"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "131", "rule_name": "20230627王庆帅需求", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "134", "rule_name": "亦庄5鹏龙21测试团队需求", "action": "pass", "source_zone": "Untrust", "source_ip": ["亦庄5鹏龙21测试团队源地址"], "destination_ip": ["亦庄5鹏龙21测试团队目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "2", "rule_name": "科技鹏龙有线网访问桌面云", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技鹏龙有线网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙无线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "综合运营中心VDI网段", "综合运营中心业务运营老网段", "中心有线网", "中心无线网", "印务办公网", "骏彩办公网", "亦庄办公网", "科技亦庄办公网"], "destination_ip": ["\"桌面云connect server\""], "destination_zone": "Trust", "services": ["tcp_80", "tcp_8443", "https"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "62", "rule_name": "Viminal 工单", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技鹏龙有线网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙无线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "综合运营中心VDI网段", "综合运营中心业务运营老网段", "中心有线网", "中心无线网", "印务办公网", "骏彩办公网", "亦庄办公网", "科技亦庄办公网"], "destination_ip": ["viminal工单"], "destination_zone": "Trust", "services": ["https"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "4", "rule_name": "协同需求统筹管理工具访问", "action": "pass", "source_zone": "Untrust", "source_ip": ["中心有线网", "中心无线网"], "destination_ip": ["协同需求统筹管理工具"], "destination_zone": "Trust", "services": ["tcp_8080", "tcp_8081", "tcp_8082"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "5", "rule_name": "开发测试环境DNS服务器访问", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网DNS", "运营办公网DNS", "骏彩办公网DNS", "运营中心VDIDNS"], "destination_ip": ["开发测试环境DNS服务器"], "destination_zone": "Trust", "services": ["tcp_53"], "is_logging": "enable", "is_counting": "enable", "rule_status": "Disable"}, {"id": "8", "rule_name": "竞猜开发测试访问", "action": "pass", "source_zone": "Trust", "source_ip": ["any"], "destination_ip": ["竞猜开发测试"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "9", "rule_name": "自动化测试MAIL 服务器访问科技、运营和骏彩办公网MAIL服务器", "action": "pass", "source_zone": "Trust", "source_ip": ["\"自动化测试MAIL 服务器\""], "destination_ip": ["科技办公网MAIL服务器", "运营办公网MAIL服务器", "骏彩办公网MAIL服务器"], "destination_zone": "Untrust", "services": ["tcp_25"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "12", "rule_name": "桌面云VDI桌面访问运营公司百环办公区maven", "action": "pass", "source_zone": "Trust", "source_ip": ["桌面云VDI（**********）"], "destination_ip": ["运营公司百环办公区maven"], "destination_zone": "Untrust", "services": ["tcp_10082"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "17", "rule_name": "国2测试环境matser访问公网", "action": "pass", "source_zone": "any", "source_ip": ["***********"], "destination_ip": ["***********"], "destination_zone": "any", "services": ["https", "http"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "26", "rule_name": "访问办公网邮箱服务", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["访问办公网邮箱服务"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "27", "rule_name": "访问扫码终端二中心IDC", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["访问扫码终端二中心IDC"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "28", "rule_name": "访问coding-devops", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["访问coding-devops"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "29", "rule_name": "访问CSLO邮件服务器1", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["访问CSLO邮件服务器"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "30", "rule_name": "访问国家实验室", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["访问国家实验室"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "31", "rule_name": "访问UATtest", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["访问UATtest"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "32", "rule_name": "访问ump效验", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["访问ump效验"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "34", "rule_name": "研创中心到堡垒机", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技鹏龙有线网", "科技翌景无线网", "科技翌景有线网", "运营办公网", "中心无线网", "中心有线网"], "destination_ip": ["研创中心到堡垒机"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "49", "rule_name": "办公网BT终端访问开发测试环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网鹏龙BT终端"], "destination_ip": ["开发测试环境-BT终端服务01", "开发测试环境-BT终端服务02"], "destination_zone": "Trust", "services": ["TCP30000", "TCP8443"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "48", "rule_name": "办公网BT终端访问开发测环境NTP", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网鹏龙BT终端"], "destination_ip": ["开发测试环境-BT终端NTP"], "destination_zone": "Trust", "services": ["ntp"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "47", "rule_name": "办公网BT终端访问开发测试环境FTP", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网鹏龙BT终端"], "destination_ip": ["开发测试环境-BT终端FTP"], "destination_zone": "Trust", "services": ["ftp"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "37", "rule_name": "BT终端访问开发业务", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技鹏龙20层有线网", "科技鹏龙21层有线网"], "destination_ip": ["BT终端访问开发业务目的地址"], "destination_zone": "Trust", "services": ["BT终端访问开发业务端口"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "64", "rule_name": "科技办公网安卓服务器访问编译服务", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网-安卓编译务器"], "destination_ip": ["开发测试环境-办公网访问安卓服务"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "65", "rule_name": "科技办公网访问开发SVN01", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网-SVN访问源地址"], "destination_ip": ["开发测试环境-科技开发SVN01"], "destination_zone": "Trust", "services": ["TCP8088"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "51", "rule_name": "科技办公网数据中台访问开发测试环境02", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网-测试数据中台"], "destination_ip": ["开发测试环境-办公数据中台访问05"], "destination_zone": "Trust", "services": ["TCP22"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "50", "rule_name": "科技办公网数据中台访问开发测试环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网-测试数据中台"], "destination_ip": ["开发测试环境-办公数据中台访问01", "开发测试环境-办公数据中台访问03", "开发测试环境-办公数据中台访问02", "开发测试环境-办公数据中台访问04", "开发测试环境-办公数据中台访问06", "开发测试环境-办公数据中台访问07", "开发测试环境-办公数据中台访问08", "************-66"], "destination_zone": "Trust", "services": ["TCP10000", "TCP1521", "TCP3306", "TCP2883", "TCP8083", "TCP8889", "TCP21050", "TCP8020", "TCP21051", "TCP_2181"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "67", "rule_name": "科技办公网HADOOP访问开发测试PMC归集库", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网-测试HADOOP", "科技办公网-测试数据中台"], "destination_ip": ["开发测试环境-办公访问PMC归集库"], "destination_zone": "Trust", "services": ["TCP2883", "TCP9201", "ping"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "46", "rule_name": "运营办公访问开发测试环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["运营办公网"], "destination_ip": ["开发测试环境-运营UMP管理端", "开发测试环境-运营堡垒机", "开发测试环境-运营访问G3售票网关", "开发测试环境-运营访问网络", "开发测试环境-运营访问主机", "开发测试环境-运营文档访问", "开发测试环境-运营虚机", "开发测试环境-运营知识库", "开发测试环境-运营终端更新服务器"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "53", "rule_name": "安全月互联网访问", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["云峰为格亮梳理安全月", "***********"], "destination_zone": "Trust", "services": ["开发测试环境-互联网访问服务", "https", "http", "TCP8443", "tcp_8081", "tcp_8082", "ntp"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "58", "rule_name": "北单测试网络业务测试01", "action": "pass", "source_zone": "Untrust", "source_ip": ["北单测试网络"], "destination_ip": ["开发测试环境-北单访问业务01"], "destination_zone": "Trust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "59", "rule_name": "北单测试网络业务测试02", "action": "pass", "source_zone": "Untrust", "source_ip": ["北单测试网络"], "destination_ip": ["开发测试环境-北单访问业务02"], "destination_zone": "Trust", "services": ["TCP3389"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "60", "rule_name": "北单测试网络业务测试03", "action": "pass", "source_zone": "Untrust", "source_ip": ["北单测试网络"], "destination_ip": ["开发测试环境-北单访问业务03"], "destination_zone": "Trust", "services": ["tcp_8080", "80"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "158", "rule_name": "高德测试环境到G32环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["北单测试网络"], "destination_ip": ["**************"], "destination_zone": "Trust", "services": ["tcp_8443"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "175", "rule_name": "高德测试访问新建数据中台优化后kafka集群", "action": "pass", "source_zone": "Untrust", "source_ip": ["************", "************"], "destination_ip": ["*************-105"], "destination_zone": "Trust", "services": ["TCP9092"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "150", "rule_name": "高德测试环境到数据中台G31测试环境kafka", "action": "pass", "source_zone": "Untrust", "source_ip": ["北单测试网络"], "destination_ip": ["***********-15"], "destination_zone": "Trust", "services": ["TCP9092"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "205", "rule_name": "G32销服访问北单", "action": "pass", "source_zone": "Trust", "source_ip": ["************"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "197", "rule_name": "G31和G34的unit1&unit4的worker到北单接入地址", "action": "pass", "source_zone": "Trust", "source_ip": ["**********/24", "**********", "***********"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["http"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "196", "rule_name": "G31环境访问北单服务器", "action": "pass", "source_zone": "Trust", "source_ip": ["************"], "destination_ip": ["************"], "destination_zone": "Untrust", "services": ["TCP8001"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "74", "rule_name": "北单测试网络业务测试anytest", "action": "pass", "source_zone": "Untrust", "source_ip": ["北单测试网络"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": "Disable"}, {"id": "81", "rule_name": "办公到测试环境数据中台数据库", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技亦庄办公网"], "destination_ip": ["开发测试环境-办公数据中心访问09"], "destination_zone": "Trust", "services": ["TCP2181"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "61", "rule_name": "经营分析系统", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技亦庄办公网-即开经营分析系统"], "destination_ip": ["开发测试环境-经营分析系统"], "destination_zone": "Trust", "services": ["TCP2883", "TCP30010"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "68", "rule_name": "湖南专线访问实验室服务器", "action": "pass", "source_zone": "Untrust", "source_ip": ["湖南专线"], "destination_ip": ["湖南专线访问实验室"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "75", "rule_name": "喻坤需求20220815", "action": "pass", "source_zone": "Untrust", "source_ip": ["喻坤需求源地址20220815"], "destination_ip": ["喻坤需求目的地址20220815"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "78", "rule_name": "胡刚需求20220822", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["胡刚需求20220822"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "80", "rule_name": "G3需求20220818", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "66", "rule_name": "互联网访问USAP", "action": "pass", "source_zone": "Untrust", "source_ip": ["\"科技办公网F5 SNAT+MONITOR\""], "destination_ip": ["开发测试环境-互联网访问USAP"], "destination_zone": "Trust", "services": ["https"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "84", "rule_name": "20220906需求", "action": "pass", "source_zone": "Untrust", "source_ip": ["**************"], "destination_ip": ["*************/24"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "89", "rule_name": "云峰需求20221011", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "90", "rule_name": "20221012", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "91", "rule_name": "合规监管", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["***************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "92", "rule_name": "***********->***********", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********"], "destination_ip": ["***********", "************", "***************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "94", "rule_name": "云峰需求20221026（1）", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["*************、221"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "87", "rule_name": "访问建行", "action": "pass", "source_zone": "Trust", "source_ip": ["***********"], "destination_ip": ["建行"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "97", "rule_name": "20221027", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "99", "rule_name": "20221108", "action": "pass", "source_zone": "Untrust", "source_ip": ["*************"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "113", "rule_name": "20230112", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["*************00", "*************60"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "119", "rule_name": "20230206", "action": "pass", "source_zone": "Untrust", "source_ip": ["**********"], "destination_ip": ["*************", "***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "120", "rule_name": "20230213", "action": "pass", "source_zone": "Untrust", "source_ip": ["**********"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "121", "rule_name": "node集群访问实验室", "action": "pass", "source_zone": "Untrust", "source_ip": ["************", "************"], "destination_ip": ["************", "**********", "************", "***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "124", "rule_name": "20230403", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "130", "rule_name": "总局访问VDI", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********", "************", "************", "************", "************", "************"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "71", "rule_name": "统一门户", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技办公网F5selfip"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["https", "http", "TCP9080"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "105", "rule_name": "湖北农信访问策略", "action": "pass", "source_zone": "Untrust", "source_ip": ["湖北农信源地址"], "destination_ip": ["湖北农信目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "106", "rule_name": "20221116", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "114", "rule_name": "访问协同财务处系统管理", "action": "pass", "source_zone": "Untrust", "source_ip": ["中心有线网", "中心无线网", "************"], "destination_ip": ["协同财务处系统管理工具"], "destination_zone": "Trust", "services": ["80", "http", "https", "TCP22"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "100", "rule_name": "综合运营中心VDI访问桌面云", "action": "pass", "source_zone": "Untrust", "source_ip": ["综合运营中心VDI瘦客户端网段"], "destination_ip": ["\"桌面云connect server\""], "destination_zone": "Trust", "services": ["TCP-384", "TCP-44", "TCP-434", "TCP-172", "4172", "22443", "9427", "32111", "tcp_8443", "UDP8443", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "108", "rule_name": "运营VDI访问打印机", "action": "pass", "source_zone": "Trust", "source_ip": ["开发测试综合运营中心VDI网段"], "destination_ip": ["运营虚拟化打印机"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "102", "rule_name": "开发测试亦庄域控访问VDI-VC", "action": "pass", "source_zone": "Trust", "source_ip": ["开发测试亦庄域控"], "destination_ip": ["综合运营中心VDI-VC"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "118", "rule_name": "运营虚拟化文件服务器数据同步-2", "action": "pass", "source_zone": "Trust", "source_ip": ["*************", "************", "************", "************-**************"], "destination_ip": ["************", "*************-122"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "116", "rule_name": "vdi访问新环境文件服务器", "action": "pass", "source_zone": "Untrust", "source_ip": ["*************-***************", "***********", "***************-202", "***************-202", "***************-202"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["135-139", "TCP-445"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "38", "rule_name": "亦庄办公网访问测试环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["中心无线网", "中心有线网"], "destination_ip": ["**********/16", "**********/16", "**********/24", "************/24", "**********/16"], "destination_zone": "Trust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "135", "rule_name": "应急VPN", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "152", "rule_name": "统一开发测试环境业务系统", "action": "pass", "source_zone": "Untrust", "source_ip": ["***************", "***********"], "destination_ip": ["***********", "***********", "************-33"], "destination_zone": "Trust", "services": ["https", "TCP30080", "\"TCP 30088\"", "tcp_53", "TCP9080"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "180", "rule_name": "lousao", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["lousao"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": "Disable"}, {"id": "1", "rule_name": "Trust→Untrust_1_IPv4", "action": "drop", "source_zone": "Trust", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "0", "rule_name": "Untrust→Trust_0_IPv4", "action": "drop", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "6", "rule_name": "腾讯云测试环境HOST访问开发测试环境测试Server", "action": "pass", "source_zone": "Trust", "source_ip": ["腾讯云测试环境HOST", "开发测试环境测试Server"], "destination_ip": ["腾讯云测试环境HOST", "开发测试环境测试Server"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "7", "rule_name": "桌面云VDI桌面访问开发测试环境测试Server", "action": "pass", "source_zone": "Trust", "source_ip": ["桌面云VDI桌面"], "destination_ip": ["开发测试环境测试Server"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "21", "rule_name": "翌景访问虚拟化平台", "action": "pass", "source_zone": "Untrust", "source_ip": ["翌景访问虚拟化平台（源）"], "destination_ip": ["翌景访问虚拟化平台（目的）"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "23", "rule_name": "访问sementepro", "action": "pass", "source_zone": "Untrust", "source_ip": ["亦庄办公网", "骏彩办公网", "运营办公网", "印务办公网", "中心无线网", "中心有线网", "科技二中心无线网", "科技二中心有线网", "科技鹏龙20层有线网", "科技鹏龙21层有线网", "科技鹏龙无线网", "科技翌景无线网", "科技鹏龙有线网", "科技翌景有线网", "综合运营中心VDI网段", "综合运营中心业务运营老网段", "科技亦庄办公网"], "destination_ip": ["访问sementepro"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "13", "rule_name": "桌面云VDI桌面访问运营公司百环办公区Sonar", "action": "pass", "source_zone": "Trust", "source_ip": ["桌面云VDI（**********）"], "destination_ip": ["运营公司百环办公区Sonar"], "destination_zone": "Untrust", "services": ["tcp_10081"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "16", "rule_name": "国1测试环境需要访问北京彩票销售管理系统", "action": "pass", "source_zone": "Trust", "source_ip": ["国1骏彩跳板机"], "destination_ip": ["北京彩票销售管理系统数据同步", "北京彩票销售管理系统接入ip"], "destination_zone": "Untrust", "services": ["tcp_8080"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "24", "rule_name": "总局中心七层706会议室", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "22", "rule_name": "访问国二环境（2台虚机机)", "action": "pass", "source_zone": "Untrust", "source_ip": ["访问国二环境（2台虚机机)（源）"], "destination_ip": ["访问国二环境（2台虚机机目的）"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "25", "rule_name": "访问csl-svn", "action": "pass", "source_zone": "Untrust", "source_ip": ["中心有线网", "中心无线网", "运营办公网", "运营办公网DNS", "运营办公网MAIL服务器", "运营公司百环办公区maven", "运营公司百环办公区Sonar", "科技翌景有线网", "科技翌景无线网", "科技鹏龙有线网", "科技鹏龙无线网", "科技鹏龙21层有线网", "科技鹏龙20层有线网", "科技二中心有线网", "科技二中心无线网", "骏彩办公网", "骏彩办公网DNS", "科技办公网DNS", "骏彩办公网MAIL服务器", "科技办公网MAIL服务器"], "destination_ip": ["访问sementepro"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "39", "rule_name": "骏彩办公网访问实验环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩办公网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "79", "rule_name": "李景一需求20220823", "action": "pass", "source_zone": "Untrust", "source_ip": ["李景一需求源地址20220823"], "destination_ip": ["李景一需求目的地址20220823"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "126", "rule_name": "亦庄办公网访问测试存储", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********"], "destination_ip": ["*************/24"], "destination_zone": "Trust", "services": ["TCP22", "TCP8088", "tcp_8080"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "115", "rule_name": "20230202", "action": "pass", "source_zone": "Untrust", "source_ip": ["**********"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "93", "rule_name": "李景一需求20221024", "action": "pass", "source_zone": "Untrust", "source_ip": ["李景一需求源地址20221024"], "destination_ip": ["李景一需求目的地址20221024"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "76", "rule_name": "办公网访问测试NAS", "action": "pass", "source_zone": "Untrust", "source_ip": ["办公网访问NAS地址"], "destination_ip": ["测试NAS地址"], "destination_zone": "Trust", "services": ["\"NAS service port\""], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "70", "rule_name": "李景一需求20220811（2）", "action": "pass", "source_zone": "Untrust", "source_ip": ["李景一需求20220811源地址2", "李景一需求20220811源地址3"], "destination_ip": ["李景一需求20220811目的地址2"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "63", "rule_name": "G32环境数据中台网络", "action": "pass", "source_zone": "Untrust", "source_ip": ["G32环境数据中台网络"], "destination_ip": ["G32环境数据中台网络目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "33", "rule_name": "百环访问二中心实验室虚拟机", "action": "pass", "source_zone": "Untrust", "source_ip": ["运营办公网"], "destination_ip": ["百环二中心实验室虚拟机"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "82", "rule_name": "刘栋-网络互联需求20220901", "action": "pass", "source_zone": "Untrust", "source_ip": ["刘栋20220901需求源地址"], "destination_ip": ["刘栋20220901需求目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "69", "rule_name": "李景一需求20220811（1）", "action": "pass", "source_zone": "Untrust", "source_ip": ["李景一需求20220811源地址1"], "destination_ip": ["李景一需求20220811目的地址1"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "85", "rule_name": "密服项目测试环境建设网络需求", "action": "pass", "source_zone": "Untrust", "source_ip": ["密服测试环境建设网络需求源地址"], "destination_ip": ["密服测试环境建设需求目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "73", "rule_name": "李景一需求20220812", "action": "pass", "source_zone": "Untrust", "source_ip": ["李景一需求20220812源地址"], "destination_ip": ["李景一需求20220812目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "83", "rule_name": "尹健需求20220902", "action": "pass", "source_zone": "Untrust", "source_ip": ["尹健需求源地址20220902"], "destination_ip": ["尹健需求目的地址20220902"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "95", "rule_name": "云峰需求20221026（2）", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "96", "rule_name": "20221026", "action": "pass", "source_zone": "Untrust", "source_ip": ["**********-2"], "destination_ip": ["***************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "103", "rule_name": "开发测试综合运营中心VDI访问RMOAS", "action": "pass", "source_zone": "Untrust", "source_ip": ["开发测试综合运营中心VDI网段"], "destination_ip": ["RMOAS"], "destination_zone": "Trust", "services": ["80", "TCP-1433"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "77", "rule_name": "数字化中心三端新环境网络需求", "action": "pass", "source_zone": "Untrust", "source_ip": ["数字化中心三端新环境源地址"], "destination_ip": ["数字化中心三端新环境目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "86", "rule_name": "骏彩魏琨昱需求20220908", "action": "pass", "source_zone": "Untrust", "source_ip": ["骏彩魏琨昱需求源地址"], "destination_ip": ["骏彩魏琨昱需求目的地址"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "88", "rule_name": "李景一需求20220914", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "98", "rule_name": "20221107", "action": "pass", "source_zone": "Untrust", "source_ip": ["***********"], "destination_ip": ["************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "128", "rule_name": "20230505", "action": "pass", "source_zone": "Untrust", "source_ip": ["*************、9、38"], "destination_ip": ["**************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "101", "rule_name": "综合运营中心VDI-VC访问域控", "action": "pass", "source_zone": "Untrust", "source_ip": ["综合运营中心VDI-VC"], "destination_ip": ["开发测试亦庄域控"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "110", "rule_name": "20221226", "action": "pass", "source_zone": "Untrust", "source_ip": ["*************"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "117", "rule_name": "运营虚拟化文件服务器数据同步-1", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["*************"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "104", "rule_name": "综合运营中心VDI访问运营支撑文件服务器", "action": "pass", "source_zone": "Untrust", "source_ip": ["开发测试综合运营中心VDI网段"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "122", "rule_name": "骏彩T1访问**********", "action": "pass", "source_zone": "Untrust", "source_ip": ["any"], "destination_ip": ["***********"], "destination_zone": "Trust", "services": ["34443"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "44", "rule_name": "印务办公网访问实验环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["印务办公网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "10", "rule_name": "科技鹏龙20层有线网访问", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技鹏龙20层有线网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "109", "rule_name": "运营虚拟化打印机到VDI", "action": "pass", "source_zone": "Untrust", "source_ip": ["运营虚拟化打印机"], "destination_ip": ["开发测试综合运营中心VDI网段"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "14", "rule_name": "互联网业务访问网络", "action": "pass", "source_zone": "Trust", "source_ip": ["any"], "destination_ip": ["互联网业务访问网络"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "52", "rule_name": "运营公司办公网访问实验环境02", "action": "pass", "source_zone": "Untrust", "source_ip": ["运营办公网", "运营办公网DNS", "运营办公网MAIL服务器", "运营公司百环办公区maven", "运营公司百环办公区Sonar", "运营中心VDI白名单"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "43", "rule_name": "运营公司办公网访问实验环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["运营办公网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "40", "rule_name": "射击场访问实验环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技二中心无线网", "科技二中心有线网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "41", "rule_name": "鹏龙办公网访问实验环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技鹏龙无线网", "科技鹏龙有线网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "42", "rule_name": "翌景办公网访问实验环境", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技翌景无线网", "科技翌景有线网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "107", "rule_name": "20221121", "action": "pass", "source_zone": "Untrust", "source_ip": ["************"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "11", "rule_name": "科技鹏龙21层有线网访问", "action": "pass", "source_zone": "Untrust", "source_ip": ["科技鹏龙21层有线网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "55", "rule_name": "印务办公网访问开发测试环境01", "action": "pass", "source_zone": "Untrust", "source_ip": ["印务办公网"], "destination_ip": ["开发测试环境-印务访问业务"], "destination_zone": "Trust", "services": ["TCP2883"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "54", "rule_name": "印务办公网访问实验环境01", "action": "pass", "source_zone": "Untrust", "source_ip": ["印务办公网"], "destination_ip": ["开发测试环境-印务访问网络"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "125", "rule_name": "运营公司至测试mail", "action": "pass", "source_zone": "Untrust", "source_ip": ["***************-102"], "destination_ip": ["**********"], "destination_zone": "Trust", "services": ["tcp_25"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "154", "rule_name": "鹏龙BT访问DNS", "action": "pass", "source_zone": "Untrust", "source_ip": ["************/24"], "destination_ip": ["新建DNS服务器"], "destination_zone": "Trust", "services": ["icmp-info", "icmp-host-unreachable", "icmp-fragment-reassembly", "icmp-fragment-needed", "icmp-parameter-problem", "icmp-port-unreachable", "icmp-protocol-unreach", "icmp-redirect", "icmp-redirect-host", "icmp-redirect-tos-host", "icmp-redirect-tos-net", "icmp-source-quench", "icmp-source-route-fail", "icmp-time-exceeded", "icmp-timestamp", "icmp-traceroute", "icmp-address-mask", "icmp-dest-unreachable", "dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "123", "rule_name": "ca-to-outside", "action": "drop", "source_zone": "Trust", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "Untrust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "35", "rule_name": "480_CY_JIANHANG", "action": "drop", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["***********"], "destination_zone": "any", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "45", "rule_name": "亦庄办公网访问any", "action": "pass", "source_zone": "Untrust", "source_ip": ["亦庄办公网"], "destination_ip": ["any"], "destination_zone": "Trust", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "178", "rule_name": "K8SNODE-to-DUANXIN", "action": "pass", "source_zone": "TENANT02_CORE_Inside", "source_ip": ["V3_CORE_K8SNODE_**********/24", "V3_CORE_K8SNODE_4.190.85.0/24", "V3_CORE_K8SNODE_4.190.86.0/24", "V3_CORE_**********/22"], "destination_ip": ["************"], "destination_zone": "TENANT02_CORE_Outside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "179", "rule_name": "K8SNODE-to-URSF5-30514", "action": "pass", "source_zone": "TENANT02_GW_Outside", "source_ip": ["**********/22", "**********/22"], "destination_ip": ["************"], "destination_zone": "TENANT02_GW_Inside", "services": ["TCP_30514"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "199", "rule_name": "AOPS-to-MySQLMHA", "action": "pass", "source_zone": "TENANT02_CORE_Outside", "source_ip": ["************-36"], "destination_ip": ["************-104", "SBSG2GRSDS01-***********"], "destination_zone": "TENANT02_CORE_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}]
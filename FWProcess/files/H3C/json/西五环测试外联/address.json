[{"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "10.209.2-9", "security-zone": "yuny_vdi", "ip_address": [{"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}]}, {"address_name": "**********", "security-zone": "yuny_vdi", "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "**********/16", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** ***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "**********/16", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** ***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "************/32", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "***********-15", "security-zone": null, "ip_address": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "**********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** ***********"}]}, {"address_name": "**********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "***********/24", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "***********/24", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "***********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "*************", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "10.217.130-137/139-140.0/24", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "************ **************"}, {"type": "range", "value": "************ **************"}]}, {"address_name": "10.217.130-139.0/24", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "10.217.132.0 *************"}, {"type": "subnet", "value": "10.217.133.0 *************"}, {"type": "subnet", "value": "10.217.134.0 *************"}, {"type": "subnet", "value": "10.217.135.0 *************"}, {"type": "subnet", "value": "10.217.136.0 *************"}, {"type": "subnet", "value": "10.217.137.0 *************"}, {"type": "subnet", "value": "10.217.138.0 *************"}, {"type": "subnet", "value": "10.217.139.0 *************"}]}, {"address_name": "************", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "************-**************", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "************ **************"}]}, {"address_name": "10.217.131-137.0", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "10.217.132.0 *************"}, {"type": "subnet", "value": "10.217.133.0 *************"}, {"type": "subnet", "value": "10.217.134.0 *************"}, {"type": "subnet", "value": "10.217.135.0 *************"}, {"type": "subnet", "value": "10.217.136.0 *************"}, {"type": "subnet", "value": "10.217.137.0 *************"}]}, {"address_name": "10.217.140.0", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "10.217.140.0 *************"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "10.218.129.0", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "10.218.129.0 *************"}]}, {"address_name": "10.218.129.0/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.218.129.0 *************"}]}, {"address_name": "************-3", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "************8", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************8"}]}, {"address_name": "10.218.45-46", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "10.218.45.0 *************"}, {"type": "subnet", "value": "10.218.46.0 *************"}]}, {"address_name": "10.221.0.21", "security-zone": null, "ip_address": [{"type": "host", "value": "10.221.0.21"}]}, {"address_name": "10.222.0.1-2", "security-zone": null, "ip_address": [{"type": "host", "value": "10.222.0.1"}, {"type": "host", "value": "10.222.0.2"}]}, {"address_name": "10.248.121.0", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "10.248.121.0 *************"}]}, {"address_name": "10.248.121.200-220", "security-zone": null, "ip_address": [{"type": "range", "value": "10.248.121.200 10.248.121.220"}]}, {"address_name": "10.248.133.0", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.248.133.0 *************"}]}, {"address_name": "10.248.133.52、9、38", "security-zone": null, "ip_address": [{"type": "host", "value": "10.248.133.52"}, {"type": "host", "value": "10.248.133.9"}, {"type": "host", "value": "10.248.133.38"}]}, {"address_name": "10.248.135.0/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.248.135.0 *************"}]}, {"address_name": "10.248.144.0", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.248.144.0 *************"}]}, {"address_name": "10.248.150.0", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "10.248.150.0 *************"}]}, {"address_name": "************/24", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "10.248.255.249", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "10.248.255.249"}, {"type": "host", "value": "192.168.32.57"}]}, {"address_name": "10.88.128.65", "security-zone": null, "ip_address": [{"type": "host", "value": "10.88.128.65"}]}, {"address_name": "104.11.11.101-105", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "104.11.11.101 104.11.11.105"}]}, {"address_name": "104.12.1.100/32", "security-zone": null, "ip_address": [{"type": "host", "value": "104.12.1.100"}]}, {"address_name": "104.12.1.150", "security-zone": null, "ip_address": [{"type": "host", "value": "104.12.1.150"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "104.12.10.0", "security-zone": null, "ip_address": [{"type": "subnet", "value": "104.12.10.0 *************"}]}, {"address_name": "*************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "*************/32", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "*************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "*************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "*************、221", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}]}, {"address_name": "************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "*************/32", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "*************/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************* *************"}]}, {"address_name": "104.126.246-248.0", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "************* *************"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "**********50", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********50"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***************", "security-zone": null, "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "*************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************* *************"}]}, {"address_name": "*************", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "***************", "security-zone": null, "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "***************", "security-zone": null, "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "***************", "security-zone": null, "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "**************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "**************", "security-zone": null, "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "***************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "***************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "***************", "security-zone": null, "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "**************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "*************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************* *************"}]}, {"address_name": "***************", "security-zone": null, "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "**********/16", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** ***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "************", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "***********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "************-33", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "*************", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "104.23.11.58", "security-zone": null, "ip_address": [{"type": "host", "value": "104.23.11.58"}]}, {"address_name": "104.23.15.0", "security-zone": null, "ip_address": [{"type": "subnet", "value": "104.23.15.0 *************"}]}, {"address_name": "104.255.225.51/56", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.255.225.51"}, {"type": "host", "value": "104.255.225.56"}]}, {"address_name": "104.255.244.0/24", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "104.255.244.0 *************"}]}, {"address_name": "104.9.0.50/32", "security-zone": null, "ip_address": [{"type": "host", "value": "104.9.0.50"}]}, {"address_name": "15.1.164.171", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "15.1.164.171"}]}, {"address_name": "172.100.1.121-122", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "172.100.1.121 172.100.1.122"}]}, {"address_name": "***************-102", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*************** ***************"}, {"type": "host", "value": "172.100.49.101"}]}, {"address_name": "***************-112", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*************** ***************"}]}, {"address_name": "172.100.101.200", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "172.100.101.200"}]}, {"address_name": "172.100.151.1-172.100.156.254", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "172.100.151.1 172.100.156.254"}]}, {"address_name": "172.100.156.0", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "172.100.156.0 *************"}]}, {"address_name": "***************-202", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*************** 172.100.201.202"}]}, {"address_name": "***************", "security-zone": null, "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "***************-202", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*************** 172.100.202.202"}]}, {"address_name": "***************-202", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*************** 172.100.207.202"}]}, {"address_name": "172.100/200.0.0", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "172.100.0.0 ***********"}, {"type": "subnet", "value": "172.200.0.0 ***********"}]}, {"address_name": "**********/32", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "**********2/32", "security-zone": null, "ip_address": [{"type": "host", "value": "**********2"}]}, {"address_name": "**********2", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**********2"}]}, {"address_name": "**********2/32", "security-zone": null, "ip_address": [{"type": "host", "value": "**********2"}]}, {"address_name": "172.16.20.6", "security-zone": null, "ip_address": [{"type": "host", "value": "172.16.20.6"}]}, {"address_name": "172.16.23.1", "security-zone": null, "ip_address": [{"type": "host", "value": "172.16.23.1"}]}, {"address_name": "172.16.30.10", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "172.16.30.10"}]}, {"address_name": "************-55", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************ 172.16.30.55"}]}, {"address_name": "172.20.13.167/32", "security-zone": null, "ip_address": [{"type": "host", "value": "172.20.13.167"}]}, {"address_name": "172.20.16.245", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "172.20.16.245"}]}, {"address_name": "172.20.16.245/32", "security-zone": null, "ip_address": [{"type": "host", "value": "172.20.16.245"}]}, {"address_name": "172.20.17.0", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "172.20.17.0 *************"}]}, {"address_name": "172.20.17.88", "security-zone": null, "ip_address": [{"type": "host", "value": "172.20.17.88"}]}, {"address_name": "***********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "172.20.28.0/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "172.20.28.0 *************"}]}, {"address_name": "172.20.29.22/32", "security-zone": null, "ip_address": [{"type": "host", "value": "172.20.29.22"}]}, {"address_name": "172.20.29.45/32", "security-zone": null, "ip_address": [{"type": "host", "value": "172.20.29.45"}]}, {"address_name": "172.20.29.84/32", "security-zone": null, "ip_address": [{"type": "host", "value": "172.20.29.84"}]}, {"address_name": "17**********/32", "security-zone": null, "ip_address": [{"type": "host", "value": "17**********"}]}, {"address_name": "************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "***********", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "***********0", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "***********0"}]}, {"address_name": "***********00", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "***********00"}]}, {"address_name": "************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "**********", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "********** ***********"}]}, {"address_name": "***********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "***********", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "**********-2", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "********** **********"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "*************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "**************", "security-zone": null, "ip_address": [{"type": "range", "value": "************** **************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "**************/32", "security-zone": null, "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "************/32", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "*************/32", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "***********", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "************-12", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "**********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "***********", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "***********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "**********", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "192.168", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** ***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**********"}]}, {"address_name": "***********", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "*************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************* *************"}]}, {"address_name": "***************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "***************-174", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*************** ***************"}]}, {"address_name": "***************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "***************"}]}, {"address_name": "**************", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "*************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************* *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "192.168.205.1", "security-zone": null, "ip_address": [{"type": "host", "value": "192.168.205.1"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "************", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "192.168.9.0/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "192.168.9.0 *************"}]}, {"address_name": "198.3.100.53-57", "security-zone": null, "ip_address": [{"type": "range", "value": "198.3.100.53 198.3.100.57"}]}, {"address_name": "198.3.100.62-66", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "198.3.100.62 198.3.100.66"}]}, {"address_name": "20230726目标地址01", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "104.21.53.41"}, {"type": "host", "value": "104.21.53.2"}, {"type": "host", "value": "104.21.53.20"}, {"type": "host", "value": "104.21.9.17"}, {"type": "host", "value": "104.21.9.18"}, {"type": "host", "value": "10.211.5.38"}]}, {"address_name": "20230726孙磊需求目标地址", "security-zone": null, "ip_address": [{"type": "host", "value": "104.200.100.20"}, {"type": "host", "value": "***************"}, {"type": "range", "value": "*********** ***********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "range", "value": "************ ************"}, {"type": "host", "value": "************"}, {"type": "range", "value": "************ 104.21.19.83"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "14.13.0.52"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "104.21.57.219"}]}, {"address_name": "20231204王玉玲需求源地址", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "104.12.1.22"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "104.12.1.25"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}]}, {"address_name": "3.98.13.140", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "3.98.13.140"}]}, {"address_name": "4.13.10.161-172", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "4.13.10.161 4.13.10.172"}]}, {"address_name": "4.190.161.10", "security-zone": null, "ip_address": [{"type": "host", "value": "4.190.161.10"}]}, {"address_name": "**********/22", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "**********/22", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "4.190.88.101-104", "security-zone": null, "ip_address": [{"type": "range", "value": "4.190.88.101 4.190.88.104"}]}, {"address_name": "4.255.205.34-36", "security-zone": null, "ip_address": [{"type": "range", "value": "4.255.205.34 4.255.205.36"}]}, {"address_name": "4.255.240.50", "security-zone": null, "ip_address": [{"type": "host", "value": "4.255.240.50"}]}, {"address_name": "78.47.82.46", "security-zone": null, "ip_address": [{"type": "host", "value": "78.47.82.46"}]}, {"address_name": "bmms项目", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "10.216.40.2"}, {"type": "host", "value": "104.200.101.70"}]}, {"address_name": "BT终端IP", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "172.20.28.214"}, {"type": "host", "value": "172.20.28.215"}]}, {"address_name": "BT终端访问开发业务目的地址", "security-zone": null, "ip_address": [{"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "VDI&104", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "G3172.20", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "********** *************"}]}, {"address_name": "G32环境数据中台网络", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "G32环境数据中台网络目的地址", "security-zone": null, "ip_address": [{"type": "range", "value": "********** ***********"}]}, {"address_name": "lousao", "security-zone": null, "ip_address": [{"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "******** *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "RMOAS", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "*************** ***************"}, {"type": "range", "value": "*************** ***************"}]}, {"address_name": "SBSG2GRSDS01-***********", "security-zone": null, "ip_address": []}, {"address_name": "V3_CORE_**********/22", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "V3_CORE_K8SNODE_**********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "V3_CORE_K8SNODE_**********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "V3_CORE_K8SNODE_**********/24", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "viminal工单", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "YW-Print", "security-zone": "yuny_vdi", "ip_address": [{"type": "range", "value": "************ ************"}, {"type": "range", "value": "************ ************"}]}, {"address_name": "yyvdi-10.209", "security-zone": "yuny_vdi", "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "YZYUNYINGOSBOCCC", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "白名单", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "百环二中心实验室虚拟机", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***********"}]}, {"address_name": "办公网访问NAS地址", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "办公网访问协调考勤主机", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "************* *************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "*************"}]}, {"address_name": "办公网蜜罐", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "10.248.255.105"}]}, {"address_name": "北单测试网络", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "北京彩票销售管理系统接入ip", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "北京彩票销售管理系统数据同步", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "彩银测试服务器地址", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}]}, {"address_name": "测试", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "104.200.100.67 **************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "subnet", "value": "*************** ***************"}, {"type": "host", "value": "************"}]}, {"address_name": "测试NAS地址", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "104.200.150.205 ***************"}]}, {"address_name": "测试环境操作终端业务", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}]}, {"address_name": "测试环境管理（源地址）", "security-zone": null, "ip_address": [{"type": "host", "value": "172.20.17.138"}, {"type": "host", "value": "172.20.50.188"}, {"type": "host", "value": "172.20.17.10"}, {"type": "host", "value": "************"}, {"type": "host", "value": "172.21.11.179"}, {"type": "host", "value": "172.20.17.13"}, {"type": "host", "value": "172.21.12.150"}, {"type": "host", "value": "172.20.16.5"}]}, {"address_name": "带外地址", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "172.100.199.254"}, {"type": "subnet", "value": "172.200.10.0 *************"}, {"type": "host", "value": "172.200.11.2"}, {"type": "host", "value": "172.200.11.6"}]}, {"address_name": "访问coding-devops", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "访问csl-svn", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "访问CSLO邮件服务器", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}]}, {"address_name": "访问sementepro", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}]}, {"address_name": "访问UATtest", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "访问ump效验", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "访问办公网邮箱服务", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "访问国二环境（2台虚机机)（源）", "security-zone": null, "ip_address": [{"type": "host", "value": "192.168.6.102"}]}, {"address_name": "访问国二环境（2台虚机机目的）", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}]}, {"address_name": "访问国家实验室", "security-zone": null, "ip_address": [{"type": "host", "value": "10.210.3.25"}]}, {"address_name": "访问扫码终端二中心IDC", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**************"}, {"type": "subnet", "value": "********** *************"}, {"type": "host", "value": "**************"}]}, {"address_name": "国1骏彩跳板机", "security-zone": null, "ip_address": [{"type": "range", "value": "104.23.0.113 104.23.0.114"}]}, {"address_name": "胡刚需求20220822", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "湖北农信目的地址", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}]}, {"address_name": "湖北农信源地址", "security-zone": null, "ip_address": [{"type": "subnet", "value": "169.80.0.0 *************"}]}, {"address_name": "湖南专线", "security-zone": null, "ip_address": [{"type": "host", "value": "172.25.0.201"}]}, {"address_name": "湖南专线访问实验室", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "源地址", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "123.207.171.243"}, {"type": "host", "value": "152.136.151.240"}, {"type": "host", "value": "152.136.150.195"}, {"type": "host", "value": "36.112.66.98"}, {"type": "host", "value": "**************"}]}, {"address_name": "互联网访问实验室设备", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}]}, {"address_name": "互联网业务访问网络", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}]}, {"address_name": "建行", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "竞猜开发测试", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "********* *************"}, {"type": "subnet", "value": "********* *************"}]}, {"address_name": "骏彩办公网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "骏彩办公网DNS", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************* *************"}]}, {"address_name": "骏彩办公网MAIL服务器", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "骏彩办公网（192.168段）", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "骏彩办公网（NAT************）", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "************ *************"}]}, {"address_name": "骏彩办公网鹏龙BT终端", "security-zone": "Untrust", "ip_address": []}, {"address_name": "骏彩访问服务器", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "subnet", "value": "********** *************"}, {"type": "host", "value": "**********"}]}, {"address_name": "骏彩魏琨昱需求目的地址", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "骏彩魏琨昱需求源地址", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "开发测试环境-BT终端FTP", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "*********** ***********"}, {"type": "range", "value": "************ ************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "开发测试环境-BT终端NTP", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "************"}]}, {"address_name": "开发测试环境-BT终端服务01", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}]}, {"address_name": "开发测试环境-BT终端服务02", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}]}, {"address_name": "开发测试环境-办公访问PMC归集库", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "10.212.1.16"}, {"type": "host", "value": "104.200.101.154"}, {"type": "host", "value": "10.212.0.240"}]}, {"address_name": "开发测试环境-办公数据中台访问01", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "10.213.3.150 ***************"}, {"type": "host", "value": "104.24.0.61"}]}, {"address_name": "开发测试环境-办公数据中台访问02", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "10.212.0.18"}]}, {"address_name": "开发测试环境-办公数据中台访问03", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "104.200.101.151 104.200.101.152"}]}, {"address_name": "开发测试环境-办公数据中台访问04", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.21.51.67"}]}, {"address_name": "开发测试环境-办公数据中台访问05", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.21.57.140"}, {"type": "host", "value": "18.5.32.143"}, {"type": "host", "value": "18.0.160.1"}, {"type": "range", "value": "10.219.10.1 10.219.10.8"}, {"type": "host", "value": "***********"}]}, {"address_name": "开发测试环境-办公数据中台访问06", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "10.219.10.1 10.219.10.8"}]}, {"address_name": "开发测试环境-办公数据中台访问07", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.24.0.61"}]}, {"address_name": "开发测试环境-办公数据中台访问08", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "开发测试环境-办公数据中心访问09", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "104.21.51.96 255.255.255.252"}]}, {"address_name": "开发测试环境-办公网访问安卓服务", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "开发测试环境-北单访问业务01", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}]}, {"address_name": "开发测试环境-北单访问业务02", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.21.1.51"}, {"type": "host", "value": "104.21.1.151"}]}, {"address_name": "开发测试环境-北单访问业务03", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "104.126.255.133"}]}, {"address_name": "开发测试环境-互联网访问USAP", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "***********"}]}, {"address_name": "开发测试环境-经营分析系统", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "*************65"}, {"type": "host", "value": "104.22.9.41"}]}, {"address_name": "开发测试环境-科技开发SVN01", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}]}, {"address_name": "开发测试环境-统一管理门户开发", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.22.9.41"}]}, {"address_name": "开发测试环境-需求统筹研发环境", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "104.12.10.220 104.12.10.222"}, {"type": "host", "value": "*************"}]}, {"address_name": "开发测试环境-印务访问网络", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "104.24.0.0 *************"}, {"type": "subnet", "value": "104.24.1.0 *************"}]}, {"address_name": "开发测试环境-印务访问业务", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.200.101.151"}]}, {"address_name": "开发测试环境-运营UMP管理端", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "开发测试环境-运营堡垒机", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.255.225.51"}, {"type": "host", "value": "10.210.3.25"}, {"type": "subnet", "value": "104.9.0.20 ***************"}, {"type": "host", "value": "***********"}, {"type": "subnet", "value": "104.255.225.56 255.255.255.252"}]}, {"address_name": "开发测试环境-运营访问G3售票网关", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "开发测试环境-运营访问网络", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "********** *************"}]}, {"address_name": "开发测试环境-运营访问主机", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}, {"type": "range", "value": "********** **********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}]}, {"address_name": "开发测试环境-运营文档访问", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "开发测试环境-运营虚机", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "********** **********"}]}, {"address_name": "开发测试环境-运营知识库", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "************* ***************"}]}, {"address_name": "开发测试环境-运营终端更新服务器", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**************"}]}, {"address_name": "开发测试环境DNS服务器", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "********** **********"}]}, {"address_name": "开发测试环境测试Server", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********* ***********"}]}, {"address_name": "开发测试亦庄域控", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "开发测试综合运营中心VDI网段", "security-zone": null, "ip_address": [{"type": "range", "value": "************ **************"}]}, {"address_name": "科技办公网-SVN访问源地址", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "科技办公网-安卓编译务器", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "科技办公网-测试HADOOP", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "科技办公网-测试数据中台", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************** **************"}, {"type": "range", "value": "************ ************"}]}, {"address_name": "科技办公网DNS", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}]}, {"address_name": "SNAT+MONITOR", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "科技办公网F5selfip", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************ ************"}]}, {"address_name": "科技办公网MAIL服务器", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**********2"}]}, {"address_name": "科技办公网服务器地址", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "科技办公网鹏龙BT终端", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "160.0.0.0 240.0.0.0"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "科技二中心无线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "科技二中心有线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "科技鹏龙20层有线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "科技鹏龙21层有线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "************* *************"}]}, {"address_name": "科技鹏龙无线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "科技鹏龙有线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "*********** *************"}]}, {"address_name": "科技认证", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "********** **********"}]}, {"address_name": "科技西安办公网", "security-zone": null, "ip_address": [{"type": "subnet", "value": "************* *************"}]}, {"address_name": "科技亦庄办公网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "172.26.0.0 ***********"}]}, {"address_name": "科技亦庄办公网-即开经营分析系统", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "172.26.18.11 172.26.18.13"}]}, {"address_name": "科技翌景无线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "172.21.15.0 *************"}]}, {"address_name": "科技翌景有线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "172.20.1.0 *************"}, {"type": "subnet", "value": "172.20.2.0 *************"}, {"type": "subnet", "value": "********** *************"}, {"type": "subnet", "value": "172.20.4.0 *************"}]}, {"address_name": "李景一需求20220811目的地址1", "security-zone": null, "ip_address": [{"type": "host", "value": "18.6.21.248"}]}, {"address_name": "李景一需求20220811目的地址2", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.220.7.0 *************"}]}, {"address_name": "李景一需求20220811源地址1", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "李景一需求20220811源地址2", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}]}, {"address_name": "李景一需求20220811源地址3", "security-zone": null, "ip_address": [{"type": "host", "value": "104.11.1.13"}]}, {"address_name": "李景一需求20220812目的地址", "security-zone": null, "ip_address": [{"type": "host", "value": "18.5.32.143"}]}, {"address_name": "李景一需求20220812源地址", "security-zone": null, "ip_address": [{"type": "host", "value": "111.204.38.56"}, {"type": "host", "value": "111.204.38.57"}, {"type": "host", "value": "111.204.38.58"}, {"type": "host", "value": "111.204.38.59"}, {"type": "host", "value": "111.204.38.60"}, {"type": "host", "value": "111.204.38.61"}, {"type": "host", "value": "111.204.38.62"}, {"type": "host", "value": "111.204.38.63"}]}, {"address_name": "李景一需求目的地址20220823", "security-zone": null, "ip_address": [{"type": "range", "value": "10.220.7.21 10.220.7.25"}, {"type": "range", "value": "********** ***********"}]}, {"address_name": "李景一需求目的地址20221024", "security-zone": null, "ip_address": [{"type": "host", "value": "104.255.239.145"}, {"type": "host", "value": "104.255.239.146"}, {"type": "host", "value": "104.255.239.147"}, {"type": "host", "value": "10.222.0.63"}, {"type": "host", "value": "10.222.0.64"}, {"type": "subnet", "value": "104.12.4.0 *************"}, {"type": "host", "value": "104.200.101.63"}]}, {"address_name": "李景一需求源地址20220823", "security-zone": null, "ip_address": [{"type": "host", "value": "104.200.101.150"}, {"type": "host", "value": "104.14.0.60"}, {"type": "host", "value": "104.21.99.31"}]}, {"address_name": "李景一需求源地址20221024", "security-zone": null, "ip_address": [{"type": "host", "value": "10.217.129.6"}, {"type": "host", "value": "18.5.81.1"}, {"type": "host", "value": "18.5.81.2"}, {"type": "host", "value": "18.5.81.3"}, {"type": "host", "value": "18.5.80.120"}]}, {"address_name": "刘栋20220901需求目的地址", "security-zone": null, "ip_address": [{"type": "subnet", "value": "********** *************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "104.21.53.28"}, {"type": "subnet", "value": "********** *************"}, {"type": "host", "value": "***********"}, {"type": "subnet", "value": "18.6.3.0 *************"}, {"type": "subnet", "value": "18.6.4.0 *************"}]}, {"address_name": "刘栋20220901需求源地址", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.73.64.0 *************"}]}, {"address_name": "密服测试环境建设网络需求源地址", "security-zone": null, "ip_address": [{"type": "range", "value": "104.21.94.31 104.21.94.32"}, {"type": "host", "value": "18.6.115.4"}, {"type": "host", "value": "18.6.21.237"}]}, {"address_name": "密服测试环境建设需求目的地址", "security-zone": null, "ip_address": [{"type": "host", "value": "18.6.115.4"}, {"type": "host", "value": "18.6.21.237"}, {"type": "range", "value": "104.21.94.33 104.21.94.34"}, {"type": "host", "value": "104.14.10.231"}]}, {"address_name": "宁夏农行服务IP", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "************* *************"}]}, {"address_name": "鹏龙21层部分机器", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}]}, {"address_name": "鹏龙21层开发终端连接测试环境", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}]}, {"address_name": "生产工单系统", "security-zone": null, "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "***********"}]}, {"address_name": "数字化中心三端新环境目的地址", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "数字化中心三端新环境源地址", "security-zone": null, "ip_address": [{"type": "host", "value": "**************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}]}, {"address_name": "腾讯云测试环境HOST", "security-zone": null, "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**********"}]}, {"address_name": "王庆帅0728需求", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}]}, {"address_name": "王玉玲服务器", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "range", "value": "*********** ***********"}, {"type": "host", "value": "***********"}, {"type": "range", "value": "********** **********"}, {"type": "range", "value": "*********** ***********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "***********"}, {"type": "range", "value": "*********** ***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}]}, {"address_name": "西安办公区开发终端连接测试环境", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}]}, {"address_name": "西安办公区终端组", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}]}, {"address_name": "系统访问NAS", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "172.20.16.3"}]}, {"address_name": "系统管理组", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "172.21.11.234"}]}, {"address_name": "系统组访问目标", "security-zone": null, "ip_address": [{"type": "host", "value": "104.200.150.205"}, {"type": "host", "value": "10.210.9.1"}]}, {"address_name": "协同财务处系统管理工具", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "10.210.11.0 *************"}, {"type": "subnet", "value": "10.210.8.0 *************"}]}, {"address_name": "协同考勤服务器", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "协同需求统筹管理工具", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "10.210.1.20"}, {"type": "host", "value": "10.210.4.13"}]}, {"address_name": "新建DNS服务器", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "104.21.100.103"}]}, {"address_name": "研创访问知识库国家实验室（源）", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.248.134.0 *************"}]}, {"address_name": "研创访问知识库以及国家实验室", "security-zone": null, "ip_address": [{"type": "host", "value": "104.9.0.20"}, {"type": "host", "value": "104.9.0.21"}, {"type": "host", "value": "104.255.225.51"}]}, {"address_name": "研创中心到堡垒机", "security-zone": null, "ip_address": [{"type": "subnet", "value": "104.255.225.56 255.255.255.252"}]}, {"address_name": "亦庄5鹏龙21测试团队目的地址", "security-zone": null, "ip_address": [{"type": "host", "value": "104.200.100.20"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "104.21.19.82"}, {"type": "host", "value": "104.21.19.83"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}]}, {"address_name": "亦庄5鹏龙21测试团队源地址", "security-zone": null, "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "172.20.28.0 *************"}]}, {"address_name": "亦庄办公网", "security-zone": null, "ip_address": [{"type": "subnet", "value": "172.20.0.0 ***********"}, {"type": "subnet", "value": "********** ***********"}, {"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "192.168.4.0 *************"}]}, {"address_name": "翌景访问虚拟化平台（目的）", "security-zone": null, "ip_address": [{"type": "host", "value": "104.255.225.51"}]}, {"address_name": "翌景访问虚拟化平台（源）", "security-zone": null, "ip_address": [{"type": "host", "value": "192.168.6.159"}, {"type": "host", "value": "192.168.6.160"}, {"type": "host", "value": "192.168.6.69"}]}, {"address_name": "尹健需求目的地址20220902", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}]}, {"address_name": "尹健需求源地址20220902", "security-zone": null, "ip_address": [{"type": "subnet", "value": "10.194.119.0 *************"}]}, {"address_name": "印务办公网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "192.168.8.0 *************"}, {"type": "subnet", "value": "192.168.9.0 *************"}]}, {"address_name": "喻坤需求目的地址20220815", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "喻坤需求源地址20220815", "security-zone": null, "ip_address": [{"type": "host", "value": "172.16.0.6"}, {"type": "host", "value": "172.31.2.1"}, {"type": "host", "value": "172.31.2.2"}]}, {"address_name": "云峰为格亮梳理安全月", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "************"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}]}, {"address_name": "运营办公网", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "************ **************"}, {"type": "range", "value": "************ **************"}, {"type": "subnet", "value": "************ *************"}, {"type": "range", "value": "************ **************"}, {"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "subnet", "value": "************ *************"}]}, {"address_name": "运营办公网DNS", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "**********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "**************"}]}, {"address_name": "运营办公网MAIL服务器", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "************"}]}, {"address_name": "运营公司百环办公区maven", "security-zone": "Untrust", "ip_address": [{"type": "host", "value": "**************"}]}, {"address_name": "运营公司百环办公区Sonar", "security-zone": "Untrust", "ip_address": []}, {"address_name": "运营虚拟化打印机", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*************** ***************"}, {"type": "range", "value": "*************** ***************"}, {"type": "range", "value": "*************** ***************"}]}, {"address_name": "运营中心VDIDNS", "security-zone": "Untrust", "ip_address": [{"type": "range", "value": "*********** ***********"}]}, {"address_name": "运营中心VDI白名单", "security-zone": null, "ip_address": [{"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "**************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "************"}, {"type": "host", "value": "*************"}]}, {"address_name": "张润苗需求源地址", "security-zone": null, "ip_address": [{"type": "host", "value": "***********"}, {"type": "range", "value": "*********** ***********"}, {"type": "range", "value": "********** **********"}, {"type": "host", "value": "**********06"}]}, {"address_name": "中心无线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************* *************"}]}, {"address_name": "中心有线网", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "*********** *************"}, {"type": "subnet", "value": "************ *************"}, {"type": "subnet", "value": "************ *************"}]}, {"address_name": "server", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "***********"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "host", "value": "*************"}, {"type": "subnet", "value": "************* ***************"}]}, {"address_name": "桌面云VDI（**********）", "security-zone": "Trust", "ip_address": [{"type": "subnet", "value": "********** *************"}]}, {"address_name": "桌面云VDI桌面", "security-zone": "Trust", "ip_address": [{"type": "range", "value": "********** *************"}, {"type": "range", "value": "************ **************"}]}, {"address_name": "服务器", "security-zone": "Trust", "ip_address": [{"type": "host", "value": "***********"}]}, {"address_name": "综合运营中心VDI-VC", "security-zone": null, "ip_address": [{"type": "host", "value": "***********00"}, {"type": "host", "value": "***********0"}]}, {"address_name": "综合运营中心VDI瘦客户端网段", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "************* *************"}, {"type": "subnet", "value": "************* *************"}]}, {"address_name": "综合运营中心VDI网段", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "172.100.0.0 *************"}, {"type": "subnet", "value": "172.200.0.0 *************"}]}, {"address_name": "综合运营中心业务运营老网段", "security-zone": "Untrust", "ip_address": [{"type": "subnet", "value": "172.18.0.0 ***********"}]}]
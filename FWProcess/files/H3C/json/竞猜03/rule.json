[{"id": "225", "rule_name": "XIUSHI", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["***********-91"], "destination_ip": ["************", "AlarmManager_4.190.121.71-72"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-29093"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "226", "rule_name": "XIUSHI2", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["***********-91"], "destination_ip": ["*************", "************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "227", "rule_name": "XIUSHI3", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["OPS"], "destination_ip": ["***********-91", "**********1-14"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "191", "rule_name": "YZBOCC_Deny_HTTPS", "action": "drop", "source_zone": "TENANT02_MS_Outside", "source_ip": ["YZBOCC", "YZECC-*********"], "destination_ip": ["G3_MS_VC_4.190.120.1/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "68", "rule_name": "Ji<PERSON>uan_to_V3", "action": "pass", "source_zone": "any", "source_ip": ["JIGUAN_***************"], "destination_ip": ["HARBOR01_4.190.120.31", "HARBOR02_4.190.120.32"], "destination_zone": "any", "services": ["http", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "97", "rule_name": "4.190.101_to_***********", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.101/32"], "destination_ip": ["DMZ_Zabbix-Proxy-***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "2", "rule_name": "1", "action": "pass", "source_zone": "any", "source_ip": ["*********/16"], "destination_ip": ["*********/16"], "destination_zone": "any", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "16", "rule_name": "2", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24", "V3_MS_4.190.120.0/22"], "destination_ip": ["Zookeeper"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "17", "rule_name": "3", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["Redis_oltp", "Redis_feioltp"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "18", "rule_name": "4", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_K8SNODE_4.190.84.0/24", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "19", "rule_name": "5", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24", "JianKong_4.190.121.0"], "destination_ip": ["V3_CORE_K8SNODE_4.190.84.0/24", "V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_12049", "TCP_9100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "20", "rule_name": "6", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_Tidb_Clus_4.190.81.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_31306", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "21", "rule_name": "7", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["nginx02_4.190.120.52/32", "nginx01_4.190.120.51/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "23", "rule_name": "8", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.122.12/32", "V3_MS_4.190.122.11/32"], "destination_ip": ["V3_CORE_4.190.80.81_83", "V3_CORE_4.190.81.21_25", "V3_CORE_F5_4.190.162.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "24", "rule_name": "9", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.122.12/32", "V3_MS_4.190.122.11/32"], "destination_ip": ["V3_CORE_**********01/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_3555"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "28", "rule_name": "10", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC", "4A-*********", "YJ-TS-*************/24", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_8088", "TCP_31306", "http", "https", "ssh", "TCP_3558", "TCP_3555", "TCP-31050-31051", "TCP-8022", "TCP-9600", "TCP_31050-31051", "TCP_3389", "TCP_5480", "TCP_8080", "TCP_8086", "TCP_9443", "TCP_25601", "TCP_23000", "TCP-31306", "TCP-8000-8010", "TCP_8082", "TCP-28000-30000", "TCP-27001", "TCP-5000", "TCP_28070", "TCP_28081", "TCP_19200", "TCP_19300", "TCP_29201", "TCP-29200", "TCP_29300", "TCP_29301", "TCP-8091"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "41", "rule_name": "11", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_CORE_Tidb_Clus_4.190.81.0/24"], "destination_ip": ["V3_MS_Tidb_Monitor4.190.121.221", "V3_MS_4.190.121.231/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "42", "rule_name": "12", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_ip": ["HARBOR", "OPS", "V3_MS_Harbor_4.190.163.2/32", "V3_MS_OPS_4.190.163.3"], "destination_zone": "TENANT02_MS_Inside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "43", "rule_name": "13", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_ip": ["Configcenter"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28081"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "44", "rule_name": "14", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_10250", "TCP_10255", "TCP_8080", "TCP_2379", "TCP_10256"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "45", "rule_name": "15", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "46", "rule_name": "16", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_ESXI_4.191.240.0/24", "G2-ESXI-**********/16"], "destination_ip": ["MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_902", "UDP_902", "TCP-8182", "UDP-8182"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "47", "rule_name": "17", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_CORE_4.190.80.0/21", "V3_GW_4.190.40.0/21"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "51", "rule_name": "V3-2", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["HARBOR", "harbor_F5_4.190.163.2", "OPS", "V3_MS_OPS_4.190.163.3"], "destination_ip": ["any"], "destination_zone": "TENANT02_MS_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "52", "rule_name": "V3-13", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_CORE_4.190.80.0/21"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_31306", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "53", "rule_name": "V3-14", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.0/22", "V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_4.190.80.0/21", "***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_31306", "ssh", "TCP_28081", "TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "54", "rule_name": "V3-18", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G2OCS-**********", "**********-162"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_31306", "TCP_3555", "TCP_3558", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "55", "rule_name": "V3-25", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["*********/16"], "destination_ip": ["T1_18.0.1.173/32", "T1_NTP_18.0.11.173"], "destination_zone": "TENANT02_MS_Outside", "services": ["ntp", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "56", "rule_name": "V3-26", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["***********"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_8086"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "57", "rule_name": "V3-27", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["***********"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_8086"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "58", "rule_name": "V3-29", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["\"GW ZABBIX PROXY-************\"", "DMZ_Zabbix-Proxy-***********"], "destination_ip": ["\"ZABBIX SERVER-************-42\"", "************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "59", "rule_name": "V3-30", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["\"ZABBIX SERVER-************-42\"", "************"], "destination_ip": ["\"GW ZABBIX PROXY-************\"", "V3_DMZ_*********/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "60", "rule_name": "V3-31", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["\"CORE ZABBIX PROXY-************\""], "destination_ip": ["\"ZABBIX SERVER-************-42\"", "************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "61", "rule_name": "V3-32", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["\"ZABBIX SERVER-************-42\"", "************"], "destination_ip": ["\"CORE ZABBIX PROXY-************\""], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "66", "rule_name": "V3-33", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G3OPERVM"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_2379", "TCP_3191", "TCP_3555", "TCP_3558", "TCP_6370", "TCP_7001", "TCP_8080", "UDP_8472", "http", "ssh", "TCP-5003", "TCP_31306", "TCP-10251-10252", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "TCP_7100", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "62", "rule_name": "V3-41", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["G2DC"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_389", "TCP_3268"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "63", "rule_name": "V3-42", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_MS_4.190.120.0/22", "************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28080", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "64", "rule_name": "V3-43", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["V3_MS_4.190.120.0/22", "************", "************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28088", "TCP-28180", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "65", "rule_name": "V3-44", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["*********/16"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_29092", "TCP-6443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "75", "rule_name": "V3-45", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["V3_GW_4.190.40.0/21", "V3_CORE_4.190.80.0/22"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-29090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "81", "rule_name": "V3-46", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["MGMT_4.176.0.0/16"], "destination_ip": ["************", "V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-514", "UDP-514", "syslog"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "84", "rule_name": "V3-47", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_GW_4.190.40.0/21", "V3_CORE_4.190.80.0/21"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-9100", "TCP_3191", "TCP-5000_5007", "TCP_5008", "TCP-4100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "85", "rule_name": "V3-48", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["************", "V3_MS_4.190.121.0/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28088", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "88", "rule_name": "V3-49", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["redis-**********-9", "Redis_feioltp", "Redis_oltp"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "89", "rule_name": "V3-50", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_4.190.80.0/22", "***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_28070"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "90", "rule_name": "V3-51", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["Storage_MGMT_4.191.249.0/24", "T1_18.0.0.0/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_8088", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "91", "rule_name": "V3-52", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Storage_MGMT_4.191.249.0/24", "T1_18.0.0.0/16"], "destination_ip": ["MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_8088", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "92", "rule_name": "V3-53", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_29000-29999"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "93", "rule_name": "V3-54", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["AlarmManager_4.190.121.71-72", "OPSTOOL_***********81-182"], "destination_ip": ["V3_DMZ_*********/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["smtp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "94", "rule_name": "V3-55", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["Zabbix-Proxy-*************"], "destination_ip": ["DES-**********-18"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-8018"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "95", "rule_name": "V3-56", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS_4.190.120.0/22"], "destination_ip": ["*********-2", "DMZ_Zabbix-Proxy-***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-31050-31051", "TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "96", "rule_name": "V3-57", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS-*************"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_4.190.80.0/22"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-30600", "TCP-6370"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "98", "rule_name": "V3-58", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["*******"], "destination_ip": ["T1_18.0.1.173/32", "T1_NTP_18.0.11.173"], "destination_zone": "TENANT02_MS_Outside", "services": ["http", "ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "102", "rule_name": "V3-59", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_4.190.80.0/22"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-3558", "TCP-31306", "TCP-3555"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "50", "rule_name": "Permit OSPF&Ping", "action": "pass", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["ospf", "ping"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "22", "rule_name": "V3_MS_4.190.122.1-/2 To V3_CORE_4.190.80.71-73", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.122.11/32", "V3_MS_4.190.122.12/32"], "destination_ip": ["V3_CORE_4.190.80.71_73"], "destination_zone": "TENANT02_MS_Outside", "services": ["http", "TCP_3558"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "37", "rule_name": "V3_GW&CORE_K8SNODE To V3_MS_K8SNODE", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_2379", "TCP_8080", "TCP_10250", "TCP_10255", "TCP-6443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "12", "rule_name": "V3_MS_K8SNODE To V3_GW&CORE_K8SNODE", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_2379", "TCP_8080", "TCP_10250", "TCP_10255"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "13", "rule_name": "V3_MS_K8SNODE To V3_GW&CORE_K8SNODE Flannel", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "38", "rule_name": "V3_GW_K8SNODE To V3_MS_K8SNODE Flannel", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "11", "rule_name": "V3_MS To V3_DNS", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["any"], "destination_ip": ["V3_DNS_4.190.80.52/32", "V3_DNS_4.190.80.51/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "40", "rule_name": "V3_CORE&GW_K8SNODE To V3_MS_F5_4.190.163.1", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_K8SNODE_4.190.84.0/24"], "destination_ip": ["V3_MS_F5_4.190.163.1"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "39", "rule_name": "V3_CORE_K8SNODE To V3_MS_K8SNODE Flannel", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_CORE_K8SNODE_4.190.84.0/24"], "destination_ip": ["V3_MS_K8SNODE_***********/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["UDP_8472"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "8", "rule_name": "V3-VC To  V3-ESXI", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS_4.190.120.0/22"], "destination_ip": ["V3_ESXI_4.191.240.0/24", "G2-ESXI-**********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["http", "https", "UDP_902", "TCP_902", "TCP-8182", "UDP-8182"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "4", "rule_name": "V3_MS_4.190.120.101  To  V3MNYY_CORE_4.191.80.0/24", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.101/32"], "destination_ip": ["V3MNYY_CORE_4.191.80.0/24", "*********"], "destination_zone": "TENANT02_MS_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "14", "rule_name": "V3_MS_Tidb_Monitor To V3_CORE_Tidb_Cluster", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_Tidb_Monitor4.190.121.221", "V3_MS_4.190.121.231/32"], "destination_ip": ["V3_CORE_Tidb_Clus_4.190.81.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_10250", "TCP_10255", "TCP_8080", "TCP_2379", "ssh", "TCP_9090-9091", "TCP-9100-20182", "TCP-2379", "TCP-2380", "TCP-8249", "TCP-8250", "TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "15", "rule_name": "V3_MS_TIDB_Monitor To V3_CORE_TIDB_4.190.162.1_2", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_Tidb_Monitor4.190.121.221"], "destination_ip": ["V3_CORE_TIDB_F5_4.190.162.2", "V3_CORE_TIDB_F5_4.190.162.1"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "79", "rule_name": "To_V3_DMZ_*********/24", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.101/32"], "destination_ip": ["V3_DMZ_*********/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "70", "rule_name": "T1_HarBor_V3", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["T1_18.0.1.101/32"], "destination_ip": ["HARBOR"], "destination_zone": "TENANT02_MS_Inside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "69", "rule_name": "V3-T1_harbor", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["HARBOR"], "destination_ip": ["T1_18.0.1.101/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "74", "rule_name": "Wangxuan-CeShi", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["************1", "*************"], "destination_ip": ["*******/8"], "destination_zone": "TENANT02_MS_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "31", "rule_name": "LuYu_Test_Storage_MGMT To V3_MS", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_ip": ["V3_MS_4.190.122.1/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["https", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "6", "rule_name": "LuYu-Test-V3-MS To Storage-MGMT", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.122.1/32"], "destination_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["https", "ssh"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "35", "rule_name": "T1_To_V3_VC_4.190.120.1", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["T1_18.0.0.0/16", "BOCC", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16", "T1_18.0.1.238/32"], "destination_ip": ["V3_VC_4.190.120.1", "*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_5480", "http", "https", "ssh", "TCP_9443", "TCP_29090", "TCP_29093", "TCP_25601", "TCP_23000", "TCP_29411", "TCP_28080", "TCP-19200", "TCP-29200-29201", "TCP-29091"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "35", "rule_name": "T1_To_V3_VC_4.190.120.1", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["T1_18.0.0.0/16", "BOCC", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16", "T1_18.0.1.238/32"], "destination_ip": ["V3_VC_4.190.120.1", "*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_5480", "http", "https", "ssh", "TCP_9443", "TCP_29090", "TCP_29093", "TCP_25601", "TCP_23000", "TCP_29411", "TCP_28080", "TCP-19200", "TCP-29200-29201", "TCP-29091"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "36", "rule_name": "T1_To_V3_MS_4.190.120.0/22", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["T1_18.0.0.0/16"], "destination_ip": ["V3_MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_5000", "http", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "86", "rule_name": "V3-VC_To_Backup-Server-***********", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_VC_4.190.120.1"], "destination_ip": ["Backup_Server_***********/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["https", "TCP_902"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "87", "rule_name": "Backup-Server-***********_To_V3-VC", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Backup_Server_***********/32"], "destination_ip": ["V3_VC_4.190.120.1"], "destination_zone": "TENANT02_MS_Inside", "services": ["https", "TCP_902"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "120", "rule_name": "G3_MS_To_Backup-Server", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_MS_4.190.120.0/24"], "destination_ip": ["Backup_Server_***********/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "99", "rule_name": "TO_Backup_Server_***********", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["TENANT02_MS_4.190.120.63/32", "TENANT02_MS_4.190.120.31-32"], "destination_ip": ["Backup_Server_***********/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh", "TCP-8400-8900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "100", "rule_name": "Backup_Server_To_TENANT02_MS", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Backup_Server_***********/32"], "destination_ip": ["TENANT02_MS_4.190.120.63/32", "TENANT02_MS_4.190.120.31-32"], "destination_zone": "TENANT02_MS_Inside", "services": ["ssh", "TCP-8400-8900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "103", "rule_name": "G3-V1.3-20200429-01", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G2OCS_**********/32"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["ssh", "TCP_31306", "TCP_3555", "TCP_3558"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "104", "rule_name": "G3-V1.3-20200429-02", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["ssh", "TCP_31306", "TCP_3555", "TCP_3558", "TCP_8080", "http", "https", "TCP_8000", "UDP_902", "TCP_902", "TCP-15000", "TCP-9090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "105", "rule_name": "G3-V1.3-20200429-03", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["G3_MS_VC_4.190.120.1/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_5480", "TCP_9443", "http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "129", "rule_name": "BOCC-To-TIDB", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["configcenter01_************", "MS-*************", "G3_MS_TIDB-Mon_4.190.121.221/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_3000", "TCP-9090"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "106", "rule_name": "G3-V1.3-20200429-04", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "CSLC-baoleiji-*********", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["G3_MS_CAS_4.190.121.231-234", "G3_MS_CAS-F5_************/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28080", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "107", "rule_name": "G3-V1.3-20200429-05", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_GATEWAY_4.190.121.241-244", "G3_MS_GW-F5_************/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28088", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "108", "rule_name": "G3-V1.3-20200429-06", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_4.190.121.61-63", "AlarmManager_4.190.121.71-72"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-29093", "TCP-29090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "109", "rule_name": "G3-V1.3-20200429-07", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_4.190.121.81-82", "G3_MS_4.190.163.5/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_25061"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "110", "rule_name": "G3-V1.3-20200429-08", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_MONITORGRAFAN"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_23000"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "111", "rule_name": "G3-V1.3-20200429-09", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_MONITOREDI"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_29411"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "112", "rule_name": "G3-V1.3-20200429-10", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_4.190.163.9-11", "G3_MS_***********51-212"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28080", "TCP-28090", "TCP-28180"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "113", "rule_name": "G3-V1.3-20200429-11", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["\"ZABBIX SERVER-************-42\""], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_31050-31051", "TCP_8022"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "114", "rule_name": "G3-V1.3-20200429-12", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_*************-212"], "destination_zone": "TENANT03_MS_Inside", "services": ["TCP_8000"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "115", "rule_name": "G3-V1.3-20200429-13", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_***********1-26"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-9600"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "116", "rule_name": "G3-V1.3-20200429-14", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_4.190.121.31-32", "G3_MS_4.190.163.4/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_29411"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "117", "rule_name": "G3-V1.3-20200429-15", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_4.190.121.41-42"], "destination_zone": "TENANT02_MS_Inside", "services": ["http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "118", "rule_name": "G3-V1.3-20200429-16", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_4.190.163.17/32", "G3_MS_4.190.121.41-42"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_8022", "TCP_23000", "TCP_25601", "TCP-28000-30000", "TCP-27001", "http", "https", "TCP-8091"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "119", "rule_name": "G3-V1.3-20200429-17", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_*********/16"], "destination_ip": ["G2_NTP"], "destination_zone": "TENANT02_MS_Outside", "services": ["ntp", "UDP_123"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "121", "rule_name": "G3-V1.3-20200429-18", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_MS_CAS_4.190.121.231-234", "G3_MS_***********1-24"], "destination_ip": ["G2_DC_18.0.1.1/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_3268", "TCP_389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "122", "rule_name": "G3-V1.3-20200429-19", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_***********81-182"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28180"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "173", "rule_name": "G3-V1.3-20200429-20", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["G3_MS_4.190.163.5/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_25601"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "124", "rule_name": "Solarwinds", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Solarwinds-*************", "Solarwinds-**********"], "destination_ip": ["V3_MS_4.190.122.1/32", "***********", "***********"], "destination_zone": "TENANT02_MS_Inside", "services": ["http", "https", "TCP-8088", "TCP-5988-5989"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "123", "rule_name": "Storage_MGMT To V3_MS", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_ip": ["V3_MS_4.190.122.1/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["https", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "125", "rule_name": "K8SNODE", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS_4.190.120.0/22", "***********/24"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24", "V3_CORE_4.190.80.0/22"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-6370", "TCP-5000_5007", "TCP-5003", "TCP-5001-5030"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "126", "rule_name": "CSLC-baoleiji", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["CSLC-baoleiji-**********", "CSLC-baoleiji-*********"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["ssh", "https", "http", "TCP_3389", "TCP-8888", "TCP-8889", "TCP-8013", "TCP-8090", "TCP_8000", "TCP_3555", "TCP_3558", "TCP_31306", "TCP-5000", "TCP-28000-30000", "TCP-8400-8900", "TCP-8004", "TCP-8011", "TCP-8018", "TCP-8022", "TCP-8088", "TCP-8182", "TCP-8249", "TCP-5001-5030"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "127", "rule_name": "jingxiang", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["JiGuan-***************"], "destination_ip": ["TENANT02_MS_4.190.120.31-32"], "destination_zone": "TENANT02_MS_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "128", "rule_name": "TO_G2FTP", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["*********/16"], "destination_ip": ["G2FTP-**********"], "destination_zone": "TENANT02_MS_Outside", "services": ["ftp", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "130", "rule_name": "To-ELK", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G2-*********-92_94-95", "G2-**********-95", "G2TRANSROUTE_18.1.13.81-84", "G2_WEBDC_Group"], "destination_ip": ["G3ELK-***********-5"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_29092"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "131", "rule_name": "TO-MiMaGongJu", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_MS_***********81-182", "V3_MS_K8SNODE_***********/24"], "destination_ip": ["MiMaGongJu-***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["https", "TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "132", "rule_name": "TO-G2-VC", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.101/32"], "destination_ip": ["T1_18.0.0.0/16", "VC-************"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "133", "rule_name": "G301", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS-*************", "MS-*************"], "destination_ip": ["**********-3", "**********1-16"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-6370", "TCP-5000_5007"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "134", "rule_name": "G3", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.121.0/24", "***********/24"], "destination_ip": ["*********-2"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-25"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "135", "rule_name": "G303", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["*********-2"], "destination_ip": ["G3ELK-***********-5"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_29092"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "136", "rule_name": "TO-FAM", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["FAM-*********/24"], "destination_zone": "TENANT01_MS_Outside", "services": ["TCP-31306", "TCP-29200", "TCP-9090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "137", "rule_name": "TO-OTMServer", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS-*************", "MS-*************"], "destination_ip": ["OTMServerV01"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-8022"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "138", "rule_name": "G3_<PERSON><PERSON><PERSON>gerqi", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_MS_CAS_4.190.121.231-234", "MONITORALERTER01-*************", "MONITORALERTER02-*************"], "destination_ip": ["G2TOHERAPROXY01-temp"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-28083"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "139", "rule_name": "G3_JiaKongErqi", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G2TOHERAPROXY01-temp"], "destination_ip": ["************", "G3-MS-F5-************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28080", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "140", "rule_name": "JCVSC_G2DMZ", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.101/32"], "destination_ip": ["G2DMZ-*********"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "141", "rule_name": "G3_R141_0714", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["***********/24"], "destination_ip": ["V3_CORE_4.190.80.0/22"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-4100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "142", "rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["FOC-***********", "FOC-*********"], "destination_ip": ["any"], "destination_zone": "TENANT02_MS_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "143", "rule_name": "nfs", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS_4.190.120.0/22"], "destination_ip": ["nfs-**********", "**********"], "destination_zone": "TENANT02_MS_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "144", "rule_name": "OPCC-Flink", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "*********/16"], "destination_ip": ["flink-*************-119"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-28081", "TCP_10098-10099"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "145", "rule_name": "VPN_SYSLOG", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["VPN-************", "VPN-***********"], "destination_ip": ["V3_MS_4.190.121.0/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["UDP-514", "UDP-8514"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "146", "rule_name": "JCJK-1.3.7_01", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS_4.190.120.0/22"], "destination_ip": ["G3TOHERAPROXY-F5-***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-28083"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "147", "rule_name": "JCJK1.3.7_02", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G3TOHERAPROXY-*********1-12"], "destination_ip": ["G3GAIA-F5-************", "************", "G3-MS-F5-************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_28080", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "148", "rule_name": "JCJK1.3.7_03", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3MONITORGAIA-*************-162"], "destination_ip": ["V3_GW_K8SNODE_4.190.44.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "149", "rule_name": "JCVSC-Solarwinds", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.101/32", "MS_4.190.120.0/22"], "destination_ip": ["G2-Solarwinds-*************/32", "Network_Mgt_**********/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-17778"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "150", "rule_name": "G3_MS-AIDB_To_G3_Mail", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_MS_AIDB_4.190.122.121-123"], "destination_ip": ["G3_Mail_*********"], "destination_zone": "TENANT02_MS_Outside", "services": ["smtp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "151", "rule_name": "To_G3_CORE_NTP", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_*********/16"], "destination_ip": ["G3_NTP_4.190.80.251-252"], "destination_zone": "TENANT02_MS_Outside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "152", "rule_name": "SSM-Ansbile", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["Ansbile-************"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "153", "rule_name": "MHA", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MHA-***********91", "Ansbile-************"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "154", "rule_name": "OPSFTP", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_*********/16"], "destination_ip": ["SBSG2OPSFTP01-************"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh", "ftp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "155", "rule_name": "IRM1.15.0_01", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["CORE-**********/24"], "destination_ip": ["V3_MS_4.190.121.0/24"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "156", "rule_name": "IRM1.15.0_02", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G3OPERVM-**********-2"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-2379", "TCP-3555", "TCP-3558", "TCP-5003", "TCP_8080", "UDP_8472", "TCP_6370", "TCP_7001", "TCP_3191", "http", "ssh", "TCP-8022"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "159", "rule_name": "IRM1.15.0_03", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["CORE-**********/24"], "destination_ip": ["MonitorZK-***********31-135"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_3191"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "157", "rule_name": "ItoSchedule", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["ItoSchedule-*************-172"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-3555", "TCP-31306", "ssh", "TCP-3557", "TCP_3558"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "158", "rule_name": "ItoSchedule_to_Mail", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_*********/16"], "destination_ip": ["*********-2"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-25"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "160", "rule_name": "G3-R160_01", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["SBSG2OTJob-***********"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "161", "rule_name": "G3-R160_02", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G3_*********/16"], "destination_ip": ["JianKong_4.190.121.0"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_29092"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "162", "rule_name": "20201208", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["T1_18.0.0.0/16", "T1_18.2.1.0/24", "*********/16"], "destination_ip": ["MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "165", "rule_name": "G2-SDAS-Grafana_To_G3-MS-ELK", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G2-SDAS-Grafana-*********/32"], "destination_ip": ["G3ELK-***********-5", "G3-MS-ELKF5-************/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-19200", "TCP-29200-29201"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "166", "rule_name": "ECC01", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS-*************", "MS-*************"], "destination_ip": ["G3OPERVM-**********-2"], "destination_zone": "TENANT02_MS_Outside", "services": ["tcp-9044"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "167", "rule_name": "ECC02", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3ECCSYS01-*************", "G3ECCSYS01-*************"], "destination_ip": ["MGMT_4.176.0.0/16", "Storage_MGMT_4.191.249.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["snmp-request", "snmp-trap"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "168", "rule_name": "ECC06-", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["AlarmManager_4.190.121.71-72"], "destination_ip": ["ShuMeiPai-**********"], "destination_zone": "TENANT02_MS_Outside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "169", "rule_name": "TO-DIP", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS-*************", "MS-*************"], "destination_ip": ["DIP-************-93", "DIP-************-57"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-9092"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "170", "rule_name": "Solarwinds-SSH", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Solarwinds-**********"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "171", "rule_name": "TO-OPeratorVM", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["***********/24"], "destination_ip": ["G3OPERVM-**********-2"], "destination_zone": "TENANT02_MS_Outside", "services": ["tcp-9044"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "172", "rule_name": "CAS-TO-RDC", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_MS_CAS_4.190.121.231-234", "G3_MS_***********1-24"], "destination_ip": ["RDC-F5-************"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_389", "TCP_3268"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "174", "rule_name": "JCVSC-TO-Solar<PERSON>s", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.101/32"], "destination_ip": ["Solarwinds-**********"], "destination_zone": "TENANT01_MS_Outside", "services": ["TCP-17778"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "189", "rule_name": "YZBOCC_Deny", "action": "drop", "source_zone": "TENANT02_MS_Outside", "source_ip": ["YZBOCC", "YZECC-*********"], "destination_ip": ["any"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_31306", "TCP_3558", "ssh", "TCP_3555", "TCP_3389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "175", "rule_name": "TO-SFTP", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["*********/16"], "destination_ip": ["SFTP-************"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "176", "rule_name": "G3_163_01", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["BISMONITORCOLLECT-F5"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-26100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "177", "rule_name": "G3_163_02", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["G3SIMULATIONTRANSROUTER01"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_8082"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "178", "rule_name": "G3_JianCe2.0_01", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["V3_CORE_K8SNODE_4.190.84.0/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-26100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "179", "rule_name": "G3_JianCe2.0_02", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["G3TOHERAPROXY-F5-***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-28083"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "180", "rule_name": "G3_JianCe2.0_03", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC&4A", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["G3_MS_4.190.163.17/32", "ItoSchedule-*************-172"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-8089", "TCP-8090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "181", "rule_name": "TENANT02_MS_InsideNANT02_MS_Outside_181_IPv4", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["Zabbix-Proxy-*************"], "destination_ip": ["CSLRMSFSP01-********", "W5RRMSFSC01-*********", "W5RRDCC-*********-2", "*********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "182", "rule_name": "TENANT02_MS_OutsideNANT02_MS_Inside_182_IPv4", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["CSLRMSFSP01-********", "W5RRDCC-*********-2", "W5RRMSFSC01-*********", "*********/16"], "destination_ip": ["Zabbix-Proxy-*************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "183", "rule_name": "linshtest", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["V3_CORE_4.190.80.0/22"], "destination_ip": ["MS_4.190.120.0/22"], "destination_zone": "TENANT02_MS_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "184", "rule_name": "MS_K8SNODE_TO_NAS", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["NAS_**********"], "destination_zone": "TENANT02_MS_Outside", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "185", "rule_name": "Zabbix_for_Windows01", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Network_Mgt_**********/24", "Backup_Server_***********/32", "W5R-*********/24"], "destination_ip": ["Zabbix-Proxy-*************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "186", "rule_name": "Zabbix_for_Windows02", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["Zabbix-Proxy-*************"], "destination_ip": ["Network_Mgt_**********/24", "Backup_Server_***********/32", "W5R-*********/24"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "187", "rule_name": "SSM-Ansbile-New", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["Ansbile-************"], "destination_ip": ["G3_*********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "188", "rule_name": "OPERVM-TO-ELK", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G3OPERVM-**********-2", "G3BISMONTORCOLLECT"], "destination_ip": ["G3ELK-***********-5", "G3-MS-ELKF5-************/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-29200"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "190", "rule_name": "<PERSON><PERSON>", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MS_4.190.120.0/22"], "destination_ip": ["Radius-**********"], "destination_zone": "TENANT02_MS_Outside", "services": ["UDP-1812"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "192", "rule_name": "R240-01", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3TSPAPP01-*************", "G3TSPAPP02-*************"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_7001", "TCP_6370", "TCP-5000_5007", "TCP_31306", "TCP_3558", "TCP_3555", "TCP_3191", "UDP_8472", "ssh", "http", "TCP_8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "193", "rule_name": "R240-02", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["BOCC&4A", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["G3TSPAPP01-*************", "G3TSPAPP02-*************"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_28080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "194", "rule_name": "R240-03", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_4.190.120.0/22"], "destination_ip": ["NAS-**********/32"], "destination_zone": "TENANT02_MS_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "195", "rule_name": "AlarmManager_To_CSLC-Syslog-gateway", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["AlarmManager_4.190.121.71-72"], "destination_ip": ["CSLC-Syslog-gateway-**********"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-38082"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "198", "rule_name": "BOCC_BingDuServer", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["BOCC"], "destination_ip": ["************"], "destination_zone": "TENANT02_MS_Inside", "services": ["https", "http", "TCP-4343", "TCP-8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "197", "rule_name": "OPERVM-TO-ItoSchedule", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G3OPERVM-**********-2"], "destination_ip": ["ItoSchedule-*************-172"], "destination_zone": "TENANT02_MS_Inside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "199", "rule_name": "BINGDUtansfer", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["**********"], "destination_ip": ["************"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "200", "rule_name": "MHA-To-G3BOSDB", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["MHA-***********91"], "destination_ip": ["G3BOSDB-************-104"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-31306", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "196", "rule_name": "BingDuServer_BOCC", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["************"], "destination_ip": ["BOCC"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-51153"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "201", "rule_name": "***********-**********", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["************-14"], "destination_ip": ["************-103"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-31306"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "202", "rule_name": "R330-01", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["USAP-***********"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-19080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "210", "rule_name": "VDI-TO-KIBANA", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["Test-<PERSON><PERSON><PERSON>"], "destination_ip": ["G3_MS_4.190.121.81-82", "G3_MS_4.190.163.5/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP_25601"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "212", "rule_name": "Hermes-TO-Core", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["************-14"], "destination_ip": ["************-220"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-5000-5011"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "213", "rule_name": "G3BOSRedis-TO-GW", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["************-14"], "destination_ip": ["***********-59"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_7001"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "214", "rule_name": "G3BISMONTORCOLLECT_R340", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["G3BISMONTORCOLLECT"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-31306", "TCP_3558", "TCP_3555", "TCP_3191", "TCP_7001", "TCP_6370", "TCP-10251-10252", "TCP_2379", "TCP_8080", "TCP-4100-4130", "TCP-5000-5030", "TCP-9100", "ssh", "http", "https", "TCP_7100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "215", "rule_name": "VulnerabilityScan_Network", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["VulnerabilityScan-************"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "216", "rule_name": "YIXIANYUNWEI_to_*************/32", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["YIXIANYUNWEI"], "destination_ip": ["*************/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["TCP-31000", "TCP-32000"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "217", "rule_name": "OPS_BOS", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["OPS"], "destination_ip": ["BOS"], "destination_zone": "TENANT02_MS_Outside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "221", "rule_name": "K8SNODE_to_*********/16", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_MS_K8SNODE_***********/24"], "destination_ip": ["*********/16"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_31306", "TCP_3558", "TCP_3555", "TCP_3191", "TCP_7001", "TCP_6370", "TCP_2379", "TCP-10251-10252", "https", "http", "TCP-4100-4130", "TCP-5000-5030", "TCP_9100", "TCP_8080", "ssh", "TCP_7100"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "500", "rule_name": "linshi", "action": "pass", "source_zone": "TENANT02_MS_Outside", "source_ip": ["YZECC-*********"], "destination_ip": ["V3_MS_4.190.120.101/32"], "destination_zone": "TENANT02_MS_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": "Disable"}, {"id": "222", "rule_name": "flink_**********", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["flink-*************-119"], "destination_ip": ["**********"], "destination_zone": "TENANT02_MS_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "223", "rule_name": "TO-NFS", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["any"], "destination_ip": ["NFS-*********"], "destination_zone": "TENANT02_MS_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "224", "rule_name": "MS_SJZT", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["G3_MS_*************-212"], "destination_ip": ["SJZT"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP-8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "228", "rule_name": "20231026-dc-to-socSyslog", "action": "pass", "source_zone": "TENANT02_MS_Inside", "source_ip": ["V3_VC_4.190.120.1"], "destination_ip": ["SOC-*************-112"], "destination_zone": "TENANT02_MS_Outside", "services": ["TCP_8400", "UDP-514"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "49", "rule_name": "deny", "action": "drop", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}]
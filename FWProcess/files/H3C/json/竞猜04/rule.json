[{"id": "116", "rule_name": "YZBOCC_Deny", "action": "drop", "source_zone": "MGMT_Outside", "source_ip": ["YZBOCC", "ECC-*********/24"], "destination_ip": ["any"], "destination_zone": "MGMT_Inside", "services": ["TCP_31306", "TCP_3558", "ssh", "TCP_3555", "TCP_3389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "117", "rule_name": "YZBOCC_Deny_HTTPS", "action": "drop", "source_zone": "MGMT_Outside", "source_ip": ["YZBOCC", "ECC-*********/24"], "destination_ip": ["SR_NET_4.176.1.0/24", "G3_Network_MGT_*********/24"], "destination_zone": "MGMT_Inside", "services": ["https", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "107", "rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "drop", "source_zone": "MGMT_Inside", "source_ip": ["G3-TEST-*********/24"], "destination_ip": ["Radius-**********"], "destination_zone": "MGMT_Outside", "services": ["UDP-1812", "ping"], "is_logging": "", "is_counting": "", "rule_status": "Disable"}, {"id": "26", "rule_name": "Ji<PERSON>uan_to_V3", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["JiGuan_192.168.182.130"], "destination_ip": ["V3_MS_4.190.120.31/32", "V3_MS_4.190.120.32/32"], "destination_zone": "MGMT_Outside", "services": ["http", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "66", "rule_name": "Solarwinds_to_G3", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["TEST_*********/16"], "destination_ip": ["Solarwinds-*************"], "destination_zone": "MGMT_Outside", "services": ["syslog"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "136", "rule_name": "TO-ECC", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["***********"], "destination_ip": ["TEST_*********/16"], "destination_zone": "MGMT_Inside", "services": ["https", "ssh", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "126", "rule_name": "SNMP-*********/24", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["*********/24", "*********/16", "************"], "destination_ip": ["*********-8", "*********/16", "************"], "destination_zone": "MGMT_Outside", "services": ["TCP_UDP161", "UDP_161", "UDP_162", "UDP-514"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "63", "rule_name": "G3-Solarwinds", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["Solarwinds-*************"], "destination_ip": ["TEST_*********/16"], "destination_zone": "MGMT_Inside", "services": ["snmp-request", "snmp-trap", "TCP-161-162", "ssh"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "64", "rule_name": "BOCC_G3", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["4A-*********", "BOCC_18.2.1.0/24", "ECC-*********/24", "BOCC-*********/24", "CSLC-baoleiji-**********", "*********/16"], "destination_ip": ["TEST_*********/16", "Backup-Server-***********/32"], "destination_zone": "MGMT_Inside", "services": ["ssh", "http", "https", "TCP_3389", "TCP-8080", "TCP-3601", "TCP-8090"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "62", "rule_name": "Zabbix_Proxy_to_DES", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["Zabbix_Prosy-*************"], "destination_ip": ["DES-*********7-18"], "destination_zone": "MGMT_Inside", "services": ["TCP-8018"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "30", "rule_name": "Storage_Monitor", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["GS_MS_***********/22", "BOCC_18.2.1.0/24", "BOCC-*********/24", "ECC-*********/24", "*********/16", "*************"], "destination_ip": ["Storage_MGMT_4.191.249.0/24", "T1_18.0.0.0/16"], "destination_zone": "MGMT_Inside", "services": ["TCP-8088", "ssh", "https", "http", "telnet", "TCP-2198", "TCP-8208"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "150", "rule_name": "Storage-MGT", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["Storage_MGMT_4.191.249.0/24", "*********/24"], "destination_ip": ["Storage_MGMT_4.191.249.0/24", "*********/24"], "destination_zone": "MGMT_Outside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "29", "rule_name": "Storage-Monitor", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Storage_MGMT_4.191.249.0/24", "T1_18.0.0.0/16"], "destination_ip": ["GS_MS_***********/22", "BOCC-*********/24", "BOCC_18.2.1.0/24", "ECC-*********/24"], "destination_zone": "MGMT_Outside", "services": ["TCP-8088", "ssh", "http", "https", "telnet"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "60", "rule_name": "G2_TRANSROUTE_V3_30400", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["G2_TRANSROUTE_18.1.13.81-84"], "destination_ip": ["*********/16"], "destination_zone": "MGMT_Outside", "services": ["TCP-30400", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "59", "rule_name": "V3_Network_Log", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["TEST_*********/16"], "destination_ip": ["MS-***********/22", "***********/24"], "destination_zone": "MGMT_Outside", "services": ["syslog", "TCP-514", "UDP-514"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "53", "rule_name": "V3_MGMT", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["************1", "*************"], "destination_ip": ["TEST_*********/16", "*********/16"], "destination_zone": "MGMT_Inside", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "8", "rule_name": "1", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["T1_18.2.1.0/24", "T1_18.0.1.238/32", "YZECC-*********/24", "W5R-*********/24", "4A-*********/24", "YUNYINGCLIENT_10.0.103.0", "YJ-TS-T1_192.168.214.0/24", "YJ-TS-T1_192.168.215.0/24", "yunying-**********"], "destination_ip": ["*********/16", "*********/16"], "destination_zone": "MGMT_Outside", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "10", "rule_name": "T1_To_V3_VC_4.190.120.1", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["T1_18.0.0.0/16"], "destination_ip": ["V3_VC_4.190.120.1"], "destination_zone": "MGMT_Outside", "services": ["TCP_5480", "http", "https", "ssh", "TCP_9443"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "11", "rule_name": "V3_VC To V3_ESXI", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["MS-***********/22"], "destination_ip": ["V3_ESXI_4.191.240.0/24"], "destination_zone": "MGMT_Inside", "services": ["UDP_902", "TCP_902", "https", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "13", "rule_name": "3", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["V3_ESXI_4.191.240.0/24"], "destination_ip": ["MS-***********/22"], "destination_zone": "MGMT_Outside", "services": ["TCP_902", "UDP_902", "http", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "14", "rule_name": "4", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["T1_18.0.0.0/16"], "destination_ip": ["GS_MS_***********/22"], "destination_zone": "MGMT_Outside", "services": ["TCP_5000", "ssh", "http", "TCP_3000"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "18", "rule_name": "Luyu_Test CunChu_MGMT To V3_MS", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_ip": ["V3_MS_4.190.122.1/32"], "destination_zone": "MGMT_Outside", "services": ["https", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "19", "rule_name": "Luyu_Test_V3_MS To Storage", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["V3_MS_4.190.122.1/32"], "destination_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_zone": "MGMT_Inside", "services": ["https", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "21", "rule_name": "OSPF ICMP SNMP", "action": "pass", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["ospf", "ping", "snmp-request", "snmp-trap", "syslog", "ntp"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "22", "rule_name": "5", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["Test_4.190.80.0/22", "CAS_F5_4.190.162.3", "CASGW_F5_4.190.162.4"], "destination_ip": ["T1_18.0.1.1"], "destination_zone": "MGMT_Inside", "services": ["TCP_389"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "35", "rule_name": "Backserer_to_V3_VC", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Backup-Server-***********/32"], "destination_ip": ["V3_VC_4.190.120.1"], "destination_zone": "MGMT_Outside", "services": ["TCP_902", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "34", "rule_name": "V3_VC_to_Backserver", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["V3_VC_4.190.120.1"], "destination_ip": ["Backup-Server-***********/32"], "destination_zone": "MGMT_Inside", "services": ["TCP_902", "https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "67", "rule_name": "BOCC_To_G3-Server-ILO", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["BOCC_18.2.1.0/24"], "destination_ip": ["G3-Server-ILO-***********/24"], "destination_zone": "MGMT_Inside", "services": ["https", "TCP-3900", "TCP-2198", "TCP-8208"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "57", "rule_name": "R2201_to_", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["yunying-************", "yunying-**********"], "destination_ip": ["*********/16"], "destination_zone": "MGMT_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "56", "rule_name": "V3_to_Mail", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["*********/16"], "destination_ip": ["Mail_106.39.30.49", "DNS-*******", "DNS-***************"], "destination_zone": "MGMT_Inside", "services": ["pop3", "smtp", "dns-tcp", "dns-udp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "25", "rule_name": "V3_to_<PERSON><PERSON><PERSON>", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["V3_MS_4.190.120.31/32", "V3_MS_4.190.120.32/32"], "destination_ip": ["JiGuan_192.168.182.130"], "destination_zone": "MGMT_Inside", "services": ["http", "https"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "24", "rule_name": "_to_**********", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["tiaobanji-***********"], "destination_ip": ["T1Ceshi-**********"], "destination_zone": "MGMT_Inside", "services": ["http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "4", "rule_name": "_to_T1_AD", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["V3MNYY_CORE_4.191.80.0/24"], "destination_ip": ["T1_18.0.1.1"], "destination_zone": "MGMT_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "1", "rule_name": "_to_T1_harbor", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["V3MNYY_CORE_4.191.80.0/24"], "destination_ip": ["T1_harbor_18.0.1.104", "T1_18.0.1.0/24", "***********/32"], "destination_zone": "MGMT_Inside", "services": ["http", "ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "72", "rule_name": "Backup_Server_***********", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["TENANT02_CORE_4.190.80.153/32", "TENANT02_MS_4.190.120.63/32", "TENANT02-CORE-***********/32", "TENANT02-CORE-***********-16", "TENANT-CORE-************-173", "TENANT02_MS_4.190.120.31-32"], "destination_ip": ["Backup-Server-***********/32"], "destination_zone": "MGMT_Inside", "services": ["ssh", "TCP-8400-8900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "73", "rule_name": "Backup_Server_To_TENANT02", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Backup-Server-***********/32"], "destination_ip": ["TENANT02_CORE_4.190.80.153/32", "TENANT02_MS_4.190.120.63/32", "TENANT-CORE-************-173", "TENANT02-CORE-***********-16", "TENANT02-CORE-***********/32", "TENANT02_MS_4.190.120.31-32"], "destination_zone": "MGMT_Outside", "services": ["ssh", "TCP-8400-8900"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "77", "rule_name": "G3_MS_To_Backup-Server", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["***********/24", "G3_CORE_4.190.83.1-2"], "destination_ip": ["Backup-Server-***********/32"], "destination_zone": "MGMT_Inside", "services": ["https", "http"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "76", "rule_name": "G3-V1.3-20200429-01", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["*********/16"], "destination_ip": ["G2_NTP"], "destination_zone": "MGMT_Outside", "services": ["ntp", "UDP_123"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "83", "rule_name": "G3_OPERHOST_INSIDEZONE4.176", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["G3_CORE_4.190.83.1-2"], "destination_ip": ["*********/16"], "destination_zone": "MGMT_Inside", "services": ["ssh", "http", "TCP_6370", "TCP-5003", "TCP_3555", "TCP_3558", "TCP_3191", "TCP-7001", "TCP-2379", "TCP-8080"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "78", "rule_name": "G3-V1.3-20200429-02", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["BOCC_18.2.1.0/24"], "destination_ip": ["*********/16"], "destination_zone": "MGMT_Inside", "services": ["https"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "79", "rule_name": "CSLC-baoleiji", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["CSLC-baoleiji-**********", "CSLC-*********", "4A-*********", "BOCC_18.2.1.0/24", "*********/16", "ECC-*********/24"], "destination_ip": ["*********/16", "*********/16", "*********/16", "*********/16"], "destination_zone": "MGMT_Inside", "services": ["ssh", "http", "https", "TCP_3389", "TCP-8888", "TCP-8889", "TCP-8013", "TCP-8090", "TCP-8000", "TCP_3555", "TCP_3558", "TCP_31306", "TCP-3900", "TCP-8208", "TCP-2198", "TCP-8088", "TCP-8080"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}, {"id": "80", "rule_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["FOC-*********", "FOC-***********"], "destination_ip": ["any"], "destination_zone": "MGMT_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "81", "rule_name": "H3C_Shengji_20201001", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["G3_Network_MGT_*********/24"], "destination_ip": ["BOCC_18.2.1.0/24"], "destination_zone": "MGMT_Outside", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "82", "rule_name": "H3C-Shengji-20201001_2", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["BOCC_18.2.1.0/24"], "destination_ip": ["G3_Network_MGT_*********/24"], "destination_zone": "MGMT_Inside", "services": ["any"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "84", "rule_name": "VMqianyi", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["V3_ESXI_4.191.240.0/24"], "destination_ip": ["G2-ESXI-**********/16"], "destination_zone": "MGMT_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "85", "rule_name": "VMqianyi02", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["G2-ESXI-**********/16"], "destination_ip": ["V3_ESXI_4.191.240.0/24"], "destination_zone": "MGMT_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "86", "rule_name": "G3_AIDB_To_Mail", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["G3_MS_AIDB_4.190.122.122/32", "G3_MS_AIDB_4.190.122.121/32", "G3_MS_AIDB_4.190.122.123/32", "Solarwinds-**********"], "destination_ip": ["G3_DMZ_MAIL_4.190.0.1/32"], "destination_zone": "MGMT_Outside", "services": ["smtp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "87", "rule_name": "G3_To_CSLC_NTP", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["*********/16", "*********/16", "*********/16"], "destination_ip": ["CSLC_NTP_4.9.0.1/32"], "destination_zone": "MGMT_Outside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "88", "rule_name": "NTP", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["*********/16"], "destination_ip": ["G3NTP-************-252"], "destination_zone": "MGMT_Outside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "89", "rule_name": "SSM-Ansbile", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["Ansbile-************"], "destination_ip": ["*********/16", "*********/16", "*********/16"], "destination_zone": "MGMT_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "90", "rule_name": "ECC02", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["G3ECCSYS01-*************", "G3ECCSYS02-*************"], "destination_ip": ["Storage_MGMT_4.191.249.0/24", "*********/16"], "destination_zone": "MGMT_Inside", "services": ["snmp-request", "snmp-trap"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "91", "rule_name": "Solarwinds-SSH", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Solarwinds-**********"], "destination_ip": ["*********/16", "TEST_*********/16"], "destination_zone": "MGMT_Outside", "services": ["ssh", "http", "https", "TCP-8088", "TCP-5988-5989"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "92", "rule_name": "JCVSC-TO-Solar<PERSON>s", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["JCVSC-*************", "GS_MS_***********/22"], "destination_ip": ["Solarwinds-**********"], "destination_zone": "MGMT_Inside", "services": ["TCP-17778"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "93", "rule_name": "SCAN", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["FOC-*********36-137"], "destination_ip": ["any"], "destination_zone": "MGMT_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "94", "rule_name": "TO-SFTP", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["*********/16"], "destination_ip": ["SFTP-************"], "destination_zone": "MGMT_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "95", "rule_name": "WangZha", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["SFTP-************"], "destination_ip": ["NG-NeiWang-************"], "destination_zone": "MGMT_Outside", "services": ["TCP-33445"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "96", "rule_name": "BACK-NAS", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Backup-Server-***********/32"], "destination_ip": ["*********/16"], "destination_zone": "MGMT_Outside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "97", "rule_name": "XWHBOCC-NTP", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["**********/24"], "destination_ip": ["NET-NTP-**********"], "destination_zone": "MGMT_Inside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "98", "rule_name": "XWHBOCC-Solarwinds", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Solarwinds-**********"], "destination_ip": ["**********/24"], "destination_zone": "MGMT_Outside", "services": ["snmp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "99", "rule_name": "XWHBOCC-syslog", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["**********/24"], "destination_ip": ["Solarwinds-**********"], "destination_zone": "MGMT_Inside", "services": ["syslog"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "100", "rule_name": "XWHBOCC_G3", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["W5RBOCC", "YZ_BOCC"], "destination_ip": ["TEST_*********/16", "Backup-Server-***********/32"], "destination_zone": "MGMT_Inside", "services": ["ssh", "http", "https", "TCP_3389", "TCP-8080", "TCP-3601", "TCP-8090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "101", "rule_name": "XWHBOCC_G3Storage_Monitor", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["W5RBOCC", "YZ_BOCC"], "destination_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_zone": "MGMT_Inside", "services": ["TCP-8088", "ssh", "https", "http", "telnet", "TCP-2198", "TCP-8208"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "102", "rule_name": "XWHBOCC_G3Storage-Monitor", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Storage_MGMT_4.191.249.0/24", "YZ_BOCC"], "destination_ip": ["W5RBOCC"], "destination_zone": "MGMT_Outside", "services": ["TCP-8088", "ssh", "http", "https", "telnet"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "103", "rule_name": "Zabbix_for_Windows01", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Network_Mgt_**********/24", "Backup-Server-***********/32", "W5R-*********/24"], "destination_ip": ["Zabbix_Prosy-*************"], "destination_zone": "MGMT_Outside", "services": ["TCP-31050-31051", "TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "104", "rule_name": "Zabbix_for_Windows02", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["Zabbix_Prosy-*************"], "destination_ip": ["Network_Mgt_**********/24", "Backup-Server-***********/32", "W5R-*********/24"], "destination_zone": "MGMT_Inside", "services": ["TCP_31050-31051"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "106", "rule_name": "<PERSON><PERSON>", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["G3-TEST-*********/24"], "destination_ip": ["Radius-**********"], "destination_zone": "MGMT_Outside", "services": ["UDP-1812"], "is_logging": "", "is_counting": "enable", "rule_status": ""}, {"id": "108", "rule_name": "Ansbile", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["Ansbile-************"], "destination_ip": ["*********/16"], "destination_zone": "MGMT_Inside", "services": ["ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "109", "rule_name": "YZBOCC-NTP", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["YZBOCC"], "destination_ip": ["NET-NTP-**********"], "destination_zone": "MGMT_Inside", "services": ["ntp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "110", "rule_name": "YZBOCC_G3", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["YZBOCC"], "destination_ip": ["TEST_*********/16", "Backup-Server-***********/32"], "destination_zone": "MGMT_Inside", "services": ["ssh", "http", "https", "TCP_3389", "TCP-8080", "TCP-3601", "TCP-8090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "111", "rule_name": "YZBOCCG3Storage_Monitor", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["YZBOCC"], "destination_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_zone": "MGMT_Inside", "services": ["TCP-8088", "ssh", "https", "http", "telnet", "TCP-2198", "TCP-8208"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "112", "rule_name": "YZBOCC_G3Storage-Monitor", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_ip": ["YZBOCC"], "destination_zone": "MGMT_Outside", "services": ["TCP-8088", "ssh", "http", "https", "telnet"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "114", "rule_name": "YZBOCC_G3Storage_Monitor", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["YZBOCC"], "destination_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_zone": "MGMT_Inside", "services": ["TCP-8088", "ssh", "https", "http", "telnet", "TCP-2198", "TCP-8208"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "115", "rule_name": "YZBOCC_G3storage-Monitor01", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["Storage_MGMT_4.191.249.0/24"], "destination_ip": ["YZBOCC"], "destination_zone": "MGMT_Outside", "services": ["TCP-8088", "ssh", "http", "https", "telnet"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "113", "rule_name": "YZBOCC_G301", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["YZBOCC"], "destination_ip": ["TEST_*********/16", "Backup-Server-***********/32"], "destination_zone": "MGMT_Inside", "services": ["ssh", "http", "https", "TCP_3389", "TCP-8080", "TCP-3601", "TCP-8090"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "118", "rule_name": "VulnerabilityScan_Network", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["VulnerabilityScan-************"], "destination_ip": ["NetworkOOB-*********/23", "*********/16"], "destination_zone": "MGMT_Inside", "services": ["any"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "119", "rule_name": "monitor_prove", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["************-************"], "destination_ip": ["*********-*********"], "destination_zone": "MGMT_Inside", "services": ["https", "ssh"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "120", "rule_name": "H3Cxunjian", "action": "pass", "source_zone": "MGMT_Outside", "source_ip": ["*************"], "destination_ip": ["TEST_*********/16"], "destination_zone": "MGMT_Inside", "services": ["ssh", "snmp-request", "ftp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "121", "rule_name": "H3Cxunjian2", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["TEST_*********/16"], "destination_ip": ["*************"], "destination_zone": "MGMT_Outside", "services": ["ftp"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "125", "rule_name": "*********_************", "action": "pass", "source_zone": "MGMT_Inside", "source_ip": ["*********/16"], "destination_ip": ["************"], "destination_zone": "MGMT_Outside", "services": ["UDP_161", "UDP_162", "UDP-514"], "is_logging": "", "is_counting": "", "rule_status": ""}, {"id": "23", "rule_name": "deny", "action": "drop", "source_zone": "any", "source_ip": ["any"], "destination_ip": ["any"], "destination_zone": "any", "services": ["any"], "is_logging": "enable", "is_counting": "enable", "rule_status": ""}]
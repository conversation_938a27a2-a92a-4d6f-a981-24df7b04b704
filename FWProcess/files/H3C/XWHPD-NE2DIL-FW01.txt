<XWHPD-NE2DIL-FW01>dis curr
#
 version 7.1.064, Release 9141P38
#
 sysname XWHPD-NE2DIL-FW01
#
 clock timezone BeiJing add 08:00:00
#
context Admin id 1
#
failover group 1
 bind chassis 1 slot 6 cpu 1 primary
 bind chassis 2 slot 6 cpu 1 secondary
#
ip vpn-instance management
 route-distinguisher 1000000000:1
 vpn-target 1000000000:1 import-extcommunity
 vpn-target 1000000000:1 export-extcommunity
#
 irf domain 51
 irf mac-address persistent always
 irf auto-update enable
 irf auto-merge enable
 undo irf link-delay
 irf member 1 priority 32
 irf member 2 priority 1
#
 undo link-aggregation load-sharing mode local-first
#
track 1 interface Bridge-Aggregation1
#
track 3 interface Bridge-Aggregation3
#
track 101 interface Blade1/6/0/1 physical
#
track 102 interface Blade1/6/0/2 physical
#
track 201 interface Blade2/6/0/1 physical
#
track 202 interface Blade2/6/0/2 physical
#
 ip ttl-expires enable
#
 forwarding policy per-packet
#
 nat static-load-balance enable
#
 lldp global enable
#
 system-working-mode standard
 password-recovery enable
#
vlan 1
#
vlan 3000
 name TENANT-PUBLIC
#
vlan 3006
 name TENANT01_CORE
#
vlan 3007
 name TENANT02_CORE
#
vlan 3008
 name TENANT03_CORE
#
vlan 3009
 name TENANT04_CORE
#
vlan 3010
 name TENANT05_CORE
#
vlan 3551
#
irf-port 1/2
 port group interface Ten-GigabitEthernet1/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet1/9/1/12 mode enhanced
#
irf-port 2/1
 port group interface Ten-GigabitEthernet2/0/1/12 mode enhanced
 port group interface Ten-GigabitEthernet2/9/1/12 mode enhanced
#
object-group ip address ************
 0 network host address ************
#
object-group ip address *************
 0 network host address *************
#
object-group ip address *************
 0 network host address *************
#
object-group ip address ************
 0 network host address ************
#              
object-group ip address ************
 0 network host address ************
#
object-group ip address *************
 0 network host address *************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address **********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address **********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#              
object-group ip address **********-95
 0 network subnet ********** ***************
#
object-group ip address **********-162
 security-zone TENANT02_CORE_Outside
 0 network range ********** **********
#
object-group ip address *********-95
 0 network subnet ********* ***************
#
object-group ip address *********-26
 security-zone TENANT02_CORE_Outside
 0 network range ********* *********
#
object-group ip address **********-55
 0 network range ********** **********
#
object-group ip address **********-74
 0 network range ********** **********
#
object-group ip address *******/8
 security-zone TENANT02_CORE_Inside
 0 network subnet ******* *********
#
object-group ip address ************
 0 network host address ************
#
object-group ip address *********/16
 security-zone TENANT02_CORE_Outside
 0 network subnet ********* ***********
#
object-group ip address 4.176.11-13.0/24
 0 network subnet ********** *************
 10 network subnet ********** *************
 20 network subnet ********** *************
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address *********
 security-zone TENANT02_CORE_Inside
 0 network subnet ********* ***********
#
object-group ip address *********/16
 0 network subnet ********* ***********
#              
object-group ip address **********
 0 network host address **********
#
object-group ip address *************
 security-zone TENANT02_CORE_Outside
 0 network host address *************
#
object-group ip address *************
 security-zone TENANT02_CORE_Outside
 0 network host address *************
#
object-group ip address *************
 0 network host address *************
#
object-group ip address ************-72
 0 network range ************ ************
#
object-group ip address ***********-2
 security-zone TENANT02_CORE_Inside
 0 network range *********** ***********
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************-14
 0 network range ************ ************
#
object-group ip address ***********/24
 security-zone TENANT02_CORE_Outside
 0 network subnet *********** *************
#
object-group ip address ***********
 security-zone TENANT02_CORE_Inside
 0 network subnet *********** *************
#
object-group ip address ***********/21
 security-zone TENANT02_CORE_Outside
 0 network subnet *********** *************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address ************
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address ***********
 security-zone TENANT02_CORE_Inside
 0 network host address ***********
#
object-group ip address ***********2
 0 network host address ***********2
#
object-group ip address ***********5
 security-zone TENANT02_CORE_Outside
 0 network host address ***********5
#
object-group ip address 4.190.164.0/24
 security-zone TENANT02_CORE_Inside
 0 network subnet 4.190.164.0 *************
#
object-group ip address 4.190.165.0/24
 security-zone TENANT02_CORE_Outside
 0 network subnet 4.190.165.0 *************
#
object-group ip address **********/24
 0 network subnet ********** *************
#
object-group ip address ***********
 security-zone TENANT02_CORE_Outside
 0 network host address ***********
#
object-group ip address ***********-12
 0 network range *********** 4.190.40.12
#
object-group ip address ***********-14
 0 network range *********** 4.190.40.14
#
object-group ip address 4.190.40.33
 security-zone TENANT02_CORE_Outside
 0 network host address 4.190.40.33
#
object-group ip address ***********-59
 security-zone TENANT02_CORE_Outside
 0 network range *********** 4.190.40.59
#
object-group ip address **********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address **********/22
 security-zone TENANT_PUBLIC_Outside
 0 network subnet ********** *************
#
object-group ip address **********-4
 0 network range ********** 4.190.48.4
#
object-group ip address **********-3
 security-zone TENANT02_CORE_Inside
 0 network range ********** 4.190.80.3
#
object-group ip address ***********8-220
 security-zone TENANT02_CORE_Inside
 0 network range ***********8 ***********0
#
object-group ip address ***********-91
 0 network subnet *********** ***************
#              
object-group ip address **********1
 0 network host address **********1
#
object-group ip address **********2
 0 network host address **********2
#
object-group ip address **********-2
 0 network range ********** **********
#
object-group ip address **********01
 security-zone TENANT02_CORE_Inside
 0 network host address **********01
#
object-group ip address **********
 security-zone TENANT02_CORE_Inside
 0 network subnet ********** *************
#
object-group ip address **********
 security-zone TENANT02_CORE_Inside
 0 network host address **********
#
object-group ip address **********/22
 security-zone TENANT_PUBLIC_Outside
 0 network subnet ********** *************
#
object-group ip address **********/24
 security-zone TENANT02_CORE_Inside
 0 network subnet ********** *************
#
object-group ip address **********5
 0 network host address **********5
#
object-group ip address ************-103
 0 network range ************ **********03
#
object-group ip address 4.190.90.0
 0 network subnet 4.190.90.0 *************
#
object-group ip address *********/16
 security-zone TENANT01_CORE_Inside
 0 network subnet ********* ***********
#
object-group ip address **********
 0 network host address **********
#
object-group ip address ************
 0 network host address ************
#
object-group ip address 4A-*********
 security-zone TENANT02_CORE_Outside
 0 network subnet ********* *************
#
object-group ip address 4A-18.2.64.30
 security-zone TENANT02_CORE_Outside
 0 network host address 18.2.64.30
#
object-group ip address ********
 0 network subnet ******** *************
#
object-group ip address ABSRDCB01-*********
 0 network host address *********
#
object-group ip address AMS-CHANNEL-***********0
 security-zone TENANT02_CORE_Inside
 0 network host address ***********0
#
object-group ip address AMSRPT-F5-***********6
 security-zone TENANT02_CORE_Inside
 0 network host address ***********6
#
object-group ip address Ansbile-************
 security-zone TENANT02_CORE_Outside
 0 network host address ************
#
object-group ip address Ansbile-************
 0 network host address ************
#
object-group ip address ARMDB-***********
 security-zone TENANT02_CORE_Inside
 0 network host address 4.190.80.64
#
object-group ip address Backup-Server_***********/32
 security-zone TENANT02_CORE_Outside
 0 network host address ***********
#
object-group ip address BASDB-*********-75
 security-zone TENANT02_CORE_Outside
 0 network range ********* 18.0.1.75
#
object-group ip address BASDB-***********-43
 security-zone TENANT02_CORE_Inside
 0 network range *********** ***********
#
object-group ip address BASDB-***********
 security-zone TENANT02_CORE_Inside
 0 network host address ***********
#
object-group ip address BISMONITORCOLLECT-F5
 0 network host address ***********7
#
object-group ip address BISRPT-F5-***********1
 security-zone TENANT02_CORE_Inside
 0 network host address ***********1
#
object-group ip address BizInfo-*************-212
 description *************-212
 security-zone TENANT02_CORE_Outside
 0 network range ************* 4.190.121.212
#
object-group ip address BMSDB-**********1-15
 security-zone TENANT02_CORE_Inside
 0 network range **********1 **********5
#
object-group ip address BOCC
 security-zone TENANT02_CORE_Outside
 0 network subnet ******** *************
 10 network subnet ********* *************
 20 network subnet ********* *************
 30 network subnet ********* *************
 40 network subnet ********* *************
 50 network subnet ********** *************
 60 network subnet ********* *************
 70 network subnet ******** *************
 80 network subnet ******** *************
 90 network subnet ******** *************
 100 network subnet ********* *************
#
object-group ip address BOCC&4A
 0 network subnet ******** *************
 10 network subnet ********* *************
 20 network subnet ********* *************
 30 network subnet ********* *************
 40 network subnet ********* *************
 50 network subnet ********* *************
 60 network subnet ********** *************
 70 network subnet ******** *************
 80 network subnet ******** *************
 90 network subnet ******** *************
 100 network subnet ********* *************
#
object-group ip address CaiWu-FTP-**********/32
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address Caiwuzhong_FTP-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address CAS-**********/22
 security-zone TENANT02_CORE_Inside
 0 network subnet ********** *************
#
object-group ip address CAS_F5_***********
 0 network host address ***********
#
object-group ip address CASGW-F5-************
 security-zone TENANT02_CORE_Outside
 0 network host address ************
#
object-group ip address CASGW-F5-************/32
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address casgw_***********
 0 network host address ***********
#
object-group ip address Configcenter
 0 network group-object configcenter01_************
 1 network group-object configcenter02_************
 2 network group-object configcenter03_************
#
object-group ip address configcenter01_************
 0 network host address ************
#
object-group ip address configcenter02_************
 0 network host address ************
#
object-group ip address configcenter03_************
 0 network host address ************
#
object-group ip address "CORE ZABBIX PROXY-************"
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#              
object-group ip address CORE-F5-***********/24
 security-zone TENANT02_CORE_Inside
 0 network subnet *********** *************
#
object-group ip address CSLC-********/8
 0 network subnet ******** *********
#
object-group ip address CSLC-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address CSLC-*******
 0 network host address *******
#
object-group ip address CSLC-*******
 security-zone TENANT02_CORE_Outside
 0 network host address *******
#
object-group ip address CSLC-*******/8
 0 network subnet ******* *********
#
object-group ip address CSLC-baoleiji-*********
 0 network subnet ********* *************
#
object-group ip address CSLC-baoleiji-**********
 0 network subnet ********** *************
#
object-group ip address CSLC-DIP-***********
 security-zone TENANT02_CORE_Outside
 0 network subnet *********** *************
#
object-group ip address CSLC-Display-DB-*************
 0 network host address *************
#
object-group ip address CSLC-financialFTP-**********-12
 0 network range ********** **********
#
object-group ip address CSLC-K8S-********/24
 0 network subnet ******** *************
#
object-group ip address CSLC_*******/32
 security-zone TENANT02_CORE_Outside
 0 network host address *******
#
object-group ip address CSLC_OPENAPI_**********/32
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address CSLC_SJZT-*********
 security-zone TENANT02_CORE_Outside
 0 network host address *********
#
object-group ip address CSLCNTP-*******
 security-zone TENANT02_CORE_Outside
 0 network host address *******
#
object-group ip address CSLCOPCC-*********/24
 security-zone TENANT02_CORE_Outside
 0 network subnet ********* *************
#
object-group ip address CSLCSFTP-*******
 security-zone TENANT02_CORE_Inside
 0 network host address *******
#
object-group ip address CSLRDCA-********-2
 0 network range ******** 18.0.4.2
#
object-group ip address CSLRDCB-*********-2
 0 network range ********* 18.0.11.2
#
object-group ip address CSLRDCP-********-2
 0 network range ******** 18.0.1.2
#
object-group ip address CWZX
 0 network host address 10.194.120.94
#
object-group ip address DES-F5-***********
 security-zone TENANT02_CORE_Inside
 0 network host address ***********
#
object-group ip address DMZ-*********/24
 security-zone TENANT02_CORE_Outside
 0 network subnet ********* *************
#
object-group ip address DMZ_NAS_4.191.56.0/22
 security-zone TENANT_PUBLIC_Outside
 0 network subnet 4.191.56.0 *************
#
object-group ip address EmailServer-*********
 security-zone TENANT02_CORE_Outside
 0 network host address *********
#              
object-group ip address F5-AMS-***********0
 security-zone TENANT02_CORE_Inside
 0 network host address ***********0
#
object-group ip address flink-***********11-119
 0 network range ***********11 ***********19
#
object-group ip address flink-***********24-126
 0 network range ***********24 ***********26
#
object-group ip address FOC-*********
 0 network host address *********
#
object-group ip address FOC-***********
 0 network host address ***********
#
object-group ip address FOC-***********-137
 0 network subnet *********** ***************
#
object-group ip address FTP-**********
 security-zone TENANT02_CORE_Inside
 0 network host address **********
#              
object-group ip address G2-RTQ
 security-zone TENANT02_CORE_Outside
 0 network range ********* *********
 10 network subnet ********** ***************
#
object-group ip address G2-Solarwinds-*************/32
 0 network host address *************
#
object-group ip address G2_18.0.1.75/32
 security-zone TENANT02_CORE_Outside
 0 network host address 18.0.1.75
#
object-group ip address G2_18.0.2.189/32
 security-zone TENANT02_CORE_Outside
 0 network host address 18.0.2.189
#
object-group ip address G2_*********/32
 security-zone TENANT02_CORE_Outside
 0 network host address *********
#
object-group ip address G2_AD_18.0.10.200/32
 security-zone TENANT02_CORE_Outside
 0 network host address 18.0.10.200
#
object-group ip address G2_AMS
 security-zone TENANT02_CORE_Outside
 0 network range 18.0.2.51 18.0.2.52
 10 network range 18.0.12.51 18.0.12.52
#
object-group ip address G2_DC_********/32
 security-zone TENANT02_CORE_Outside
 0 network host name ********
#
object-group ip address G2_NTP
 security-zone TENANT02_CORE_Outside
 0 network host address ********73
 10 network host address *********73
#
object-group ip address G2_RMXDB_**********/32
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G2_SBCDB_18.0.2.37/32
 security-zone TENANT02_CORE_Outside
 0 network host address 18.0.2.37
#              
object-group ip address G2ActiveMQ-*********1-213
 security-zone TENANT02_CORE_Outside
 0 network range *********1 *********3
#
object-group ip address G2ARMDB-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G2BISDB-*********
 security-zone TENANT02_CORE_Outside
 0 network host address *********
#
object-group ip address G2BMSDB
 security-zone TENANT02_CORE_Outside
 0 network range ********* 18.0.2.36
 10 network subnet ********** ***************
#
object-group ip address G2FTP-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G2FTP-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G2MATSERVER
 security-zone TENANT02_CORE_Outside
 0 network host address 18.1.14.5
 10 network host address 18.1.14.6
 20 network host address 18.1.14.68
 30 network host address 18.1.14.69
#
object-group ip address G2OCS-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G2OCS_**********/32
 0 network host address **********
#
object-group ip address G2REDIS-********/24
 security-zone TENANT02_CORE_Outside
 0 network subnet ******** *************
#
object-group ip address G2RMXDB-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G2RTQDB
 security-zone TENANT02_CORE_Outside
 0 network range ********* *********
 10 network subnet ********** ***************
#
object-group ip address G2SBCDB-**********-36
 security-zone TENANT02_CORE_Outside
 0 network range ********** 18.0.12.36
#
object-group ip address G2SBCDB-*********-37
 security-zone TENANT02_CORE_Outside
 0 network range ********* 18.0.2.37
#
object-group ip address G2TRANSROUTE-**********-84
 security-zone TENANT02_CORE_Outside
 0 network range ********** 18.1.13.84
#
object-group ip address "G3 RMX REPORT-**********21"
 security-zone TENANT02_CORE_Inside
 0 network host address **********21
 10 network host address ************
#              
object-group ip address G3-CORE-**********/22
 security-zone TENANT02_CORE_Inside
 0 network subnet ********** *************
#
object-group ip address G3-CORE_RMXDB_***********1-113
 security-zone TENANT02_CORE_Inside
 0 network range ***********1 ***********3
#
object-group ip address G3-DMZ-SDAS-**********/32
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G3-GW-**********/24
 security-zone TENANT_PUBLIC_Outside
 0 network subnet ********** *************
#
object-group ip address G3-GW-**********/22
 security-zone TENANT02_CORE_Outside
 0 network subnet ********** *************
#
object-group ip address G3-MS-ELKF5-************
 0 network host address ************
#              
object-group ip address G3_*********/16
 0 network subnet ********* ***********
#
object-group ip address G3_*********/16
 security-zone TENANT02_CORE_Outside
 0 network subnet ********* ***********
#
object-group ip address G3_CORE_************/32
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address G3_CORE_************/32
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address G3_CORE_************-143
 0 network range ************ ************
#
object-group ip address G3_CORE__***********/32
 security-zone TENANT02_CORE_Inside
 0 network host address ***********
#
object-group ip address G3_CORE_CAS_**********1-42
 security-zone TENANT02_CORE_Inside
 0 network range **********1 **********2
#
object-group ip address G3_CORE_CAS_***********-52
 security-zone TENANT02_CORE_Inside
 0 network range *********** ***********
#
object-group ip address G3_CORE_F5_***********/32
 0 network host address ***********
#
object-group ip address G3_CORE_K8SNODE_**********/24
 security-zone TENANT02_CORE_Inside
 0 network subnet ********** *************
#
object-group ip address G3_CORE_SDAS01_************/32
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address G3_CORE_SDAS02_************/32
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address G3_MS_*************-234
 0 network range ************* *************
#
object-group ip address G3_MS_***********1-24
 0 network range ***********1 ***********4
#
object-group ip address G3_RMX-Report_************/32
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address G3AMS-************-102
 security-zone TENANT02_CORE_Inside
 0 network range ************ ************
#
object-group ip address G3AMS-************-132
 security-zone TENANT02_CORE_Inside
 0 network range ************ ************
#
object-group ip address G3AMS-TEMP-************
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address G3ARESRISK
 0 network range ************ ************
#
object-group ip address G3BISMONTORCOLLECT
 0 network range ********** **********
#
object-group ip address G3BOSDB-************-104
 0 network range ************ ************
#
object-group ip address G3ELK-***********-5
 0 network range *********** ***********
#
object-group ip address G3IRMDB-**********-2
 security-zone TENANT02_CORE_Inside
 0 network range ********** **********
#
object-group ip address G3IRMDBVIP-**********
 security-zone TENANT02_CORE_Inside
 0 network host address **********
#
object-group ip address G3LINSHISBCCOPYTOWCS01-0.200
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address G3MATGW-F5-***********
 security-zone TENANT02_CORE_Outside
 0 network host address ***********
#
object-group ip address G3NTP-************-252
 security-zone TENANT02_CORE_Inside
 0 network range ************ ************
#
object-group ip address G3OPERVM
 security-zone TENANT02_CORE_Inside
 0 network range ********** **********
#
object-group ip address G3OPERVM01-**********-2
 security-zone TENANT02_CORE_Inside
 0 network host address **********
 10 network host address **********
#
object-group ip address G3RDC-**********-2
 0 network range ********** **********
#
object-group ip address G3RMXLOGON-************
 0 network host address ************
#
object-group ip address G3TSPAPP01-***********01
 0 network host address ***********01
#
object-group ip address G3TSPAPP02-***********02
 0 network host address ***********02
#
object-group ip address HARBOR
 0 network group-object HARBOR01_************
 1 network group-object HARBOR02_************
#
object-group ip address Harbor-F5-***********/32
 0 network host address ***********
#
object-group ip address HARBOR01_************
 0 network host address ************
#
object-group ip address HARBOR02_************
 0 network host address ************
#
object-group ip address hermes-***********-16
 security-zone TENANT02_CORE_Inside
 0 network range *********** ***********
#
object-group ip address Hermes-************-216
 0 network range ************ ************
#
object-group ip address Hermes-mata-************-203
 0 network range ************ ************
#
object-group ip address Hive-DaShuJu-*********
 0 network host address *********
#
object-group ip address IHSF5-************
 security-zone TENANT02_CORE_Inside
 0 network host address ************
#
object-group ip address IRMDB-***********-133
 security-zone TENANT02_CORE_Outside
 0 network range *********** ***********
#
object-group ip address ItoSchedule-***********71-172
 security-zone TENANT02_CORE_Outside
 0 network range ***********71 ***********72
#
object-group ip address JAUTH-F5-************
 security-zone TENANT02_CORE_Outside
 0 network host address ************
#
object-group ip address JianKong_***********
 0 network subnet *********** *************
#
object-group ip address JiGuan-***************
 security-zone TENANT02_CORE_Outside
 0 network host address ***************
#
object-group ip address K8S-node
 0 network subnet ********** *************
#
object-group ip address linshi-OCS-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address LinShi-RTQ_*********
 security-zone TENANT02_CORE_Inside
 0 network host address *********
#
object-group ip address linShi_*********
 security-zone TENANT02_CORE_Outside
 0 network host address *********
#              
object-group ip address LinShi_***********
 security-zone TENANT02_CORE_Outside
 0 network host address ***********
#
object-group ip address LinShi_***********
 security-zone TENANT02_CORE_Outside
 0 network host address ***********
#
object-group ip address LinShi_18.5.95.36
 security-zone TENANT02_CORE_Outside
 0 network host address 18.5.95.36
#
object-group ip address LinShi_Cbgw_18.1.13.0
 security-zone TENANT02_CORE_Outside
 0 network subnet 18.1.13.0 *************
#
object-group ip address LinShi_Webdc_*********
 security-zone TENANT02_CORE_Outside
 0 network subnet ********* *************
#
object-group ip address LinShiYaChe_4.190.45.1-4
 security-zone TENANT02_CORE_Outside
 0 network range 4.190.45.1 4.190.45.4
#
object-group ip address Luyu-test-4.190.80.66/32
 security-zone TENANT02_CORE_Inside
 0 network host address 4.190.80.66
#
object-group ip address Mail-*********-2
 security-zone TENANT02_CORE_Outside
 0 network range ********* 4.190.0.2
#
object-group ip address Meta
 0 network host address 10.194.151.11
 1 network host address 10.194.151.3
 2 network host address 10.194.151.12
#
object-group ip address MHA-***********91
 security-zone TENANT02_CORE_Outside
 0 network host address ***********91
#
object-group ip address MonitorZK-***********31-135
 security-zone TENANT02_CORE_Outside
 0 network range ***********31 ***********35
#
object-group ip address NAS-**********
 security-zone TENANT_PUBLIC_Inside
 0 network subnet ********** *************
#
object-group ip address NAS-**********/21
 0 network subnet ********** *************
#
object-group ip address NAS-**********/32
 0 network host address **********
#
object-group ip address NAS-**********-6
 security-zone TENANT02_CORE_Outside
 0 network range ********** **********
#
object-group ip address NAS_4.191.40.3
 security-zone TENANT02_CORE_Outside
 0 network host address 4.191.40.3
#
object-group ip address NAS_**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address NAS_**********_PUBLIC_OUTSIDE
 0 network host address **********
#
object-group ip address NAS_4.191.41.1
 0 network host address 4.191.41.1
#
object-group ip address Network_Mgt_**********/24
 0 network subnet ********** *************
#
object-group ip address NetworkOOB-4.176.0.0/23
 0 network subnet 4.176.0.0 *************
 1 network subnet 4.176.1.0 *************
#
object-group ip address nfs-**********
 0 network host address **********
#
object-group ip address NFS-*********
 0 network host address *********
#
object-group ip address nginx01_4.190.120.51/32
 0 network host address 4.190.120.51
#
object-group ip address nginx02_4.190.120.52/32
 0 network host address 4.190.120.52
#              
object-group ip address OCS_4.190.80.63
 security-zone TENANT02_CORE_Inside
 0 network host address 4.190.80.63
#
object-group ip address OPS
 0 network group-object OPS01_4.190.120.41
 1 network group-object OPS02_4.190.120.42
#
object-group ip address OPS01_4.190.120.41
 0 network host address 4.190.120.41
#
object-group ip address OPS02_4.190.120.42
 0 network host address 4.190.120.42
#
object-group ip address OPSTOOL_***********81-182
 security-zone TENANT02_CORE_Outside
 0 network range ***********81 ***********82
#
object-group ip address PLSQL-gongju
 0 network subnet ********** *************
#
object-group ip address Radius-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address RDC-F5-***********3
 0 network host address ***********3
#
object-group ip address Redis_feioltp
 0 network group-object Redis_feioltp01_4.190.80.31
 1 network group-object Redis_feioltp02_4.190.80.32
 2 network group-object Redis_feioltp03_4.190.80.33
 3 network group-object Redis_feioltp04_4.190.80.34
 4 network group-object Redis_feioltp05_4.190.80.35
 5 network group-object Redis_feioltp06_4.190.80.36
 6 network group-object Redis_feioltp07_4.190.80.37
 7 network group-object Redis_feioltp08_4.190.80.38
 8 network group-object Redis_feioltp09_***********
#
object-group ip address Redis_feioltp01_4.190.80.31
 0 network host address 4.190.80.31
#
object-group ip address Redis_feioltp02_4.190.80.32
 0 network host address 4.190.80.32
#
object-group ip address Redis_feioltp03_4.190.80.33
 0 network host address 4.190.80.33
#
object-group ip address Redis_feioltp04_4.190.80.34
 0 network host address 4.190.80.34
#
object-group ip address Redis_feioltp05_4.190.80.35
 0 network host address 4.190.80.35
#
object-group ip address Redis_feioltp06_4.190.80.36
 0 network host address 4.190.80.36
#
object-group ip address Redis_feioltp07_4.190.80.37
 0 network host address 4.190.80.37
#
object-group ip address Redis_feioltp08_4.190.80.38
 0 network host address 4.190.80.38
#
object-group ip address Redis_feioltp09_***********
 0 network host address ***********
#
object-group ip address Redis_oltp
 0 network group-object Redis_oltp01_***********
 1 network group-object Redis_oltp02_***********
 2 network group-object Redis_oltp03_***********
 3 network group-object Redis_oltp04_***********
 4 network group-object Redis_oltp05_***********
 5 network group-object Redis_oltp06_***********
 6 network group-object Redis_oltp07_***********
 7 network group-object Redis_oltp08_***********
 8 network group-object Redis_oltp09_***********
#
object-group ip address Redis_oltp01_***********
 0 network host address ***********
#
object-group ip address Redis_oltp02_***********
 0 network host address ***********
#
object-group ip address Redis_oltp03_***********
 0 network host address ***********
#
object-group ip address Redis_oltp04_***********
 0 network host address ***********
#
object-group ip address Redis_oltp05_***********
 0 network host address ***********
#              
object-group ip address Redis_oltp06_***********
 0 network host address ***********
#
object-group ip address Redis_oltp07_***********
 0 network host address ***********
#
object-group ip address Redis_oltp08_***********
 0 network host address ***********
#
object-group ip address Redis_oltp09_***********
 0 network host address ***********
#
object-group ip address RMXAS-VIP_**********33
 security-zone TENANT02_CORE_Inside
 0 network host address **********33
#
object-group ip address RTQDB-**********1-33
 security-zone TENANT02_CORE_Inside
 0 network range **********1 **********3
#
object-group ip address RTQDB-**********3
 0 network host address **********3
#              
object-group ip address SBSG2BISRV-**********-42
 security-zone TENANT02_CORE_Outside
 0 network range ********** **********
#
object-group ip address SBSG2BMSDB-**********1-13
 security-zone TENANT02_CORE_Inside
 0 network range **********1 **********3
#
object-group ip address SBSG2GRSAS-***********-32
 security-zone TENANT02_CORE_Outside
 0 network range *********** ***********
#
object-group ip address SBSG2GRSDS01-***********
 security-zone TENANT02_CORE_Inside
 0 network host address ***********
#
object-group ip address SBSG2IHS-**********41-142
 security-zone TENANT02_CORE_Inside
 0 network range **********41 **********42
#
object-group ip address SBSG2IRMAS-***********-62
 security-zone TENANT02_CORE_Outside
 0 network range *********** ***********
#
object-group ip address SBSG2IRMBMO-**********-32
 security-zone TENANT02_CORE_Outside
 0 network range ********** **********
#
object-group ip address SBSG2MATSERVER-***********-22
 security-zone TENANT02_CORE_Outside
 0 network range *********** ***********
#
object-group ip address SBSG2OPSFTP01-**********31
 security-zone TENANT02_CORE_Inside
 0 network host address **********31
#
object-group ip address SBSG2OTJob-***********
 security-zone TENANT02_CORE_Inside
 0 network host address ***********
#
object-group ip address SBSG2OTJOB_***********
 security-zone TENANT_PUBLIC_Outside
 0 network host address ***********
#
object-group ip address SBSG2TRANSROUTERB01-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address SBSG2WEBDC-***********-12
 security-zone TENANT02_CORE_Outside
 0 network range *********** ***********
#
object-group ip address SDAS-BLIDB-**********04
 security-zone TENANT02_CORE_Inside
 0 network host address **********04
#
object-group ip address SDASBISer-**********11-112
 security-zone TENANT02_CORE_Inside
 0 network range **********11 **********12
#
object-group ip address SFTP-************
 security-zone TENANT02_CORE_Outside
 0 network host address ************
#
object-group ip address SG_10.196.128_129.0/24
 0 network subnet ************ *************
 1 network subnet ************ *************
#
object-group ip address ShuJuJiChengPingTai-**********
 0 network host address **********
#
object-group ip address ShuJuZhongTai-***********/24
 0 network subnet *********** *************
#
object-group ip address ShuJuZhongTai-************/32
 0 network host address ************
#
object-group ip address ShuJuZhongTai-************-58
 0 network range ************ ************
#
object-group ip address ShuJuZhongTai-************
 0 network host address ************
#
object-group ip address SJZT-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address SOC-*************-112
 0 network range ************* *************
#
object-group ip address Solarwinds-*************
 security-zone TENANT02_CORE_Outside
 0 network host address *************
#
object-group ip address Solarwinds-**********
 0 network subnet ********** *************
#
object-group ip address T1-********/8
 0 network subnet ******** *********
#
object-group ip address T1-*********
 security-zone TENANT02_CORE_Outside
 0 network host address *********
#
object-group ip address T1_********/16
 0 network subnet ******** ***********
#
object-group ip address T1_********
 0 network subnet ******** *************
#
object-group ip address T1_********73
 0 network host address ********73
#
object-group ip address T1_**********/32
 0 network host address **********
#
object-group ip address T1_**********/32
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address T1_********/24
 0 network subnet ******** *************
 100 network subnet ********* *************
#
object-group ip address T1_**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#
object-group ip address T1_CeShi_********
 0 network host address ********
#
object-group ip address T1_harboe_********04
 0 network host address ********04
#
object-group ip address T1_NTP_*********73
 0 network host address *********73
#
object-group ip address TCPCOPYINCEPT-4190.40.91
 security-zone TENANT02_CORE_Outside
 0 network host address 4.190.40.91
#
object-group ip address TCPCOPYINTERCEPT01-4.190.40.91
 security-zone TENANT02_CORE_Inside
 0 network host address 4.190.40.91
#
object-group ip address TCPCOPYSEVER-4.190.40.92
 security-zone TENANT02_CORE_Outside
 0 network host address 4.190.40.92
#
object-group ip address TENANT01-Tiaobanji-4.191.80.254
 0 network host address 4.191.80.254
#
object-group ip address TENANT02-CORE-4.190.120.63/32
 security-zone TENANT02_CORE_Inside
 0 network host address 4.190.120.63
#
object-group ip address TENANT02-CORE-***********-16
 security-zone TENANT02_CORE_Inside
 0 network range *********** ***********
#
object-group ip address TENANT02-CORE-**********53/32
 security-zone TENANT02_CORE_Inside
 0 network host address **********53
#
object-group ip address TENANT02-CORE-**********71-173
 security-zone TENANT02_CORE_Inside
 0 network range **********71 **********73
#
object-group ip address TENANT02-CORE-***********/32
 security-zone TENANT02_CORE_Inside
 0 network host address ***********
#
object-group ip address Test-Baoleiji
 0 network range 4.101.90.10 4.101.90.14
#
object-group ip address Test-Port1-*************/32
 security-zone TENANT03_CORE_Inside
 0 network host address *************
#
object-group ip address Test-Port2-************/32
 0 network host address ************
#
object-group ip address Test_192.168.214.0
 security-zone TENANT01_CORE_Outside
 0 network subnet 192.168.214.0 *************
#
object-group ip address Test_192.168.214.0/24
 security-zone TENANT02_CORE_Outside
 0 network subnet 192.168.214.0 *************
#
object-group ip address TEST_4.176.0.0/16
 0 network subnet 4.176.0.0 ***********
#
object-group ip address tiaobanji-4.191.81.11
 security-zone TENANT01_CORE_Inside
 0 network host address 4.191.81.11
#
object-group ip address tiaobanji_4.191.80.243
 security-zone TENANT01_CORE_Inside
 0 network host address 4.191.80.243
#
object-group ip address tiaobanji_4.191.80.244
 security-zone TENANT01_CORE_Inside
 0 network host name 4.191.80.244
#
object-group ip address TicaiDC-********-2
 security-zone TENANT02_CORE_Outside
 0 network range ******** 4.20.1.2
#
object-group ip address V3_CORE_**********/21
 0 network subnet ********** *************
#
object-group ip address V3_CORE_**********/22
 0 network subnet ********** *************
#
object-group ip address V3_CORE_************/32
 0 network host address ************
#
object-group ip address V3_CORE_4.190.80.71
 0 network host address 4.190.80.71
#
object-group ip address V3_CORE_4.190.80.71_73
 0 network group-object V3_CORE_4.190.80.71
 1 network group-object V3_CORE_4.190.80.72
 2 network group-object V3_CORE_4.190.80.73
#
object-group ip address V3_CORE_4.190.80.72
 0 network host address 4.190.80.72
#
object-group ip address V3_CORE_4.190.80.73
 0 network host address 4.190.80.73
#
object-group ip address V3_CORE_4.190.80.81
 0 network host address 4.190.80.81
#
object-group ip address V3_CORE_4.190.80.81_83
 0 network group-object V3_CORE_4.190.80.81
 1 network group-object V3_CORE_4.190.80.82
 2 network group-object V3_CORE_***********
#
object-group ip address V3_CORE_4.190.80.82
 0 network host address 4.190.80.82
#
object-group ip address V3_CORE_***********
 0 network host address ***********
#
object-group ip address V3_CORE_4.190.81.21
 0 network host address 4.190.81.21
#
object-group ip address V3_CORE_4.190.81.21_25
 0 network group-object V3_CORE_4.190.81.21
 1 network group-object V3_CORE_4.190.81.22
 2 network group-object V3_CORE_4.190.81.23
 3 network group-object V3_CORE_4.190.81.24
 4 network group-object V3_CORE_4.190.81.25
#
object-group ip address V3_CORE_4.190.81.22
 0 network host address 4.190.81.22
#
object-group ip address V3_CORE_4.190.81.23
 0 network host address 4.190.81.23
#
object-group ip address V3_CORE_4.190.81.24
 0 network host address 4.190.81.24
#
object-group ip address V3_CORE_4.190.81.25
 0 network host address 4.190.81.25
#
object-group ip address V3_CORE_CA_4.190.160.1/32
 0 network host address 4.190.160.1
#
object-group ip address V3_CORE_CA_4.190.160.2/32
 0 network host address 4.190.160.2
#
object-group ip address V3_CORE_F5_***********/24
 0 network subnet *********** *************
#
object-group ip address V3_CORE_K8SNODE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_CORE_K8SNODE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_CORE_K8SNODE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_CORE_Tidb_**********/32
 security-zone TENANT02_CORE_Inside
 0 network host address **********
#
object-group ip address V3_CORE_Tidb_Clus_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_CORE_TIDB_F5_***********
 0 network host address ***********
#
object-group ip address V3_CORE_TIDB_F5_***********
 0 network host address ***********
#              
object-group ip address V3_CORE_TIDB_F5_***********
 0 network host address ***********
#
object-group ip address V3_CORE_TIDB_F5_***********
 0 network host address ***********
#
object-group ip address V3_DNS_***********
 0 network host address ***********
#
object-group ip address V3_DNS_***********
 0 network host address ***********
#
object-group ip address V3_GW_**********/21
 0 network subnet ********** *************
#
object-group ip address V3_GW_**********/22
 security-zone TENANT_PUBLIC_Outside
 0 network subnet ********** *************
#
object-group ip address V3_GW_K8SNODE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_MS_***********/22
 0 network subnet *********** *************
#
object-group ip address V3_MS_*************/32
 0 network host address *************
#
object-group ip address V3_MS_*************/32
 0 network host address *************
#
object-group ip address V3_MS_***********1/32
 0 network host address ***********1
#
object-group ip address V3_MS_***********2/32
 0 network host address ***********2
#
object-group ip address V3_MS_F5_***********
 0 network host address ***********
#
object-group ip address V3_MS_K8SNODE_***********/24
 0 network subnet *********** *************
#
object-group ip address V3_MS_OPS_***********
 security-zone TENANT02_CORE_Outside
 0 network host address ***********
#
object-group ip address V3_MS_Tidb_Monitor*************
 0 network host address *************
#
object-group ip address V3MNYY_CORE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3MNYY_GW_*********/24
 description *********/24
 security-zone TENANT01_CORE_Outside
#
object-group ip address VulnerabilityScan-************
 security-zone TENANT02_CORE_Outside
 0 network host address ************
#
object-group ip address W5RFTP-*********
 security-zone TENANT02_CORE_Outside
 0 network host address *********
#
object-group ip address W5RFTP-**********
 security-zone TENANT02_CORE_Outside
 0 network host address **********
#              
object-group ip address W5ROPCC-*********02
 security-zone TENANT02_CORE_Outside
 0 network host address *********02
#
object-group ip address W5RRDC-*********-2
 0 network range ********* *********
#
object-group ip address YHTYtiaobanji_************
 security-zone TENANT01_CORE_Inside
 0 network host address ************
#
object-group ip address YHTYtiaobanji_************
 security-zone TENANT01_CORE_Inside
 0 network host address ************
#
object-group ip address YHTYtiaobanji_************
 security-zone TENANT01_CORE_Inside
 0 network host address ************
#
object-group ip address YHTYtiaobanji_************
 security-zone TENANT01_CORE_Inside
 0 network host address ************
#              
object-group ip address YHTYtiaobanji_************
 security-zone TENANT01_CORE_Inside
 0 network host address ************
#
object-group ip address YJ-TS-*************/24
 security-zone TENANT02_CORE_Outside
 0 network subnet ************* *************
#
object-group ip address YJ-TS_*************/24
 security-zone TENANT01_CORE_Outside
 0 network subnet ************* *************
#
object-group ip address yungying_RMX
 security-zone TENANT02_CORE_Outside
 0 network range *********** ***********
 10 network range ********** **********
#
object-group ip address yunwei-***********1-12
 security-zone TENANT02_CORE_Outside
 0 network range ***********1 ***********2
#
object-group ip address yunying-**********
 security-zone TENANT01_CORE_Outside
 0 network subnet ********** *************
#
object-group ip address YunYing_**********/24
 security-zone TENANT02_CORE_Outside
 0 network subnet ********** *************
#
object-group ip address yunyingclient_**********
 0 network subnet ********** *************
#
object-group ip address YZ-ECC-*********
 0 network subnet ********* *************
#
object-group ip address YZBOCC
 0 network subnet ********** *************
 10 network subnet ******** *************
#
object-group ip address YZBOCC_**********
 0 network host address **********
#
object-group ip address YZECC-*********
 0 network subnet ********* *************
#
object-group ip address YZGTM
 0 network host address **********
#
object-group ip address "ZABBIX SERVER-************-42"
 security-zone TENANT02_CORE_Outside
 0 network range ************ ************
#
object-group ip address Zookeeper
 0 network group-object Zookeeper01_***********
 1 network group-object Zookeeper02_***********
 2 network group-object Zookeeper03_***********
 3 network group-object Zookeeper04_***********
 4 network group-object Zookeeper05_***********
#
object-group ip address Zookeeper01_***********
 0 network host address ***********
#
object-group ip address Zookeeper02_***********
 0 network host address ***********
#
object-group ip address Zookeeper03_***********
 0 network host address ***********
#
object-group ip address Zookeeper04_***********
 0 network host address ***********
#
object-group ip address Zookeeper05_***********
 0 network host address ***********
#
object-group service TCP-10080
 0 service tcp destination eq 10080
#
object-group service TCP-10251-10252
 0 service tcp destination range 10251 10252
#
object-group service TCP-1521
 0 service tcp destination eq 1521
#
object-group service TCP-16000
 0 service tcp destination eq 16000
#
object-group service TCP-16010
 0 service tcp destination eq 16010
#
object-group service TCP-16020
 0 service tcp destination eq 16020
#              
object-group service TCP-18081
 0 service tcp destination eq 18081
#
object-group service TCP-1812
 0 service tcp destination eq 1812
#
object-group service TCP-19765
 0 service tcp destination eq 19765
#
object-group service TCP-21
 0 service tcp destination eq 21
#
object-group service TCP-2181
 0 service tcp destination eq 2181
#
object-group service TCP-22
 0 service tcp destination eq 22
#
object-group service TCP-2379-2380
 0 service tcp destination range 2379 2380
#
object-group service TCP-26100
 0 service tcp destination eq 26100
#
object-group service TCP-28080
 0 service tcp destination eq 28080
#
object-group service TCP-28081
 0 service tcp destination eq 28081
#
object-group service TCP-29090
 0 service tcp destination eq 29090
#
object-group service TCP-29200
 0 service tcp destination eq 29200
#
object-group service TCP-30001
 0 service tcp destination eq 30001
#
object-group service TCP-30002
 0 service tcp destination eq 30002
#
object-group service TCP-30020
 0 service tcp destination eq 30020
#
object-group service TCP-30021
 0 service tcp destination eq 30021
#
object-group service TCP-30350
 0 service tcp destination eq 30350
#
object-group service TCP-30400
 0 service tcp destination eq 30400
#
object-group service TCP-30416
 0 service tcp destination eq 30416
#
object-group service TCP-30900
 0 service tcp destination eq 30900
#
object-group service TCP-31099
 0 service tcp destination eq 31099
#
object-group service TCP-31100
 0 service tcp destination eq 31100
#
object-group service TCP-31306
 0 service tcp destination eq 31306
#              
object-group service TCP-31399
 0 service tcp destination eq 31399
#
object-group service TCP-31400
 0 service tcp destination eq 31400
#
object-group service TCP-3268
 0 service tcp destination eq 3268
#
object-group service TCP-3389
 0 service tcp destination eq 3389
#
object-group service TCP-34443
 0 service tcp destination eq 34443
#
object-group service TCP-35302
 0 service tcp destination eq 35302
#
object-group service TCP-3555
 0 service tcp destination eq 3555
#
object-group service TCP-3556
 0 service tcp destination eq 3556
#
object-group service TCP-3557
 0 service tcp destination eq 3557
#
object-group service TCP-3558
 0 service tcp destination eq 3558
#
object-group service TCP-3601
 0 service tcp destination eq 3601
#
object-group service TCP-36524
 0 service tcp destination eq 36524
#
object-group service TCP-4100
 0 service tcp destination eq 4100
#
object-group service TCP-4100-4130
 0 service tcp destination range 4100 4130
#
object-group service TCP-4555
 0 service tcp destination eq 4555
#
object-group service TCP-5000
 0 service tcp destination eq 5000
#
object-group service TCP-5000-5011
 0 service tcp destination range 5000 5011
#
object-group service TCP-5000-5030
 0 service tcp destination range 5000 5030
#
object-group service TCP-5000_5007
 0 service tcp destination range 5000 5007
#
object-group service TCP-5001-5030
 0 service tcp destination range 5001 5030
#
object-group service TCP-52704
 0 service tcp destination eq 52704
#
object-group service TCP-6000
 0 service tcp destination eq 6000
#
object-group service TCP-6100-6200
 0 service tcp destination range 6100 6200
#              
object-group service TCP-62738
 0 service tcp destination eq 62738
#
object-group service TCP-6370
 0 service tcp destination eq 6370
#
object-group service TCP-6379
 0 service tcp destination eq 6379
#
object-group service TCP-6443
 0 service tcp destination eq 6443
#
object-group service TCP-7070
 0 service tcp destination eq 7070
#
object-group service TCP-8013
 0 service tcp destination eq 8013
#
object-group service TCP-8018
 0 service tcp destination eq 8018
#
object-group service TCP-8022
 0 service tcp destination eq 8022
#
object-group service TCP-8023-8024
 0 service tcp destination range 8023 8024
#
object-group service TCP-8080
 0 service tcp destination eq 8080
#
object-group service TCP-8085
 0 service tcp destination eq 8085
#
object-group service TCP-8090
 0 service tcp destination eq 8090
#
object-group service TCP-8161
 0 service tcp destination eq 8161
#
object-group service TCP-8249-8250
 0 service tcp destination range 8249 8250
#
object-group service TCP-8400
 0 service tcp destination eq 8400
#
object-group service TCP-8400-8900
 0 service tcp destination range 8400 8900
#
object-group service TCP-8888
 0 service tcp destination eq 8888
#
object-group service TCP-8889
 0 service tcp destination eq 8889
#
object-group service tcp-9044
 description tcp-9044
 0 service tcp destination eq 9044
#
object-group service TCP-9090
 0 service tcp destination eq 9090
#
object-group service TCP-9092
 0 service tcp destination eq 9092
#
object-group service TCP-9100-20182
 0 service tcp destination range 9100 20182
#
object-group service TCP-9110
 0 service tcp destination eq 9110
#
object-group service TCP-9130
 0 service tcp destination eq 9130
#
object-group service TCP-9131
 0 service tcp destination eq 9131
#
object-group service TCP_10250
 0 service tcp destination eq 10250
#
object-group service TCP_10255
 0 service tcp destination eq 10255
#
object-group service TCP_10256
 0 service tcp destination eq 10256
#
object-group service TCP_12049
 0 service tcp destination eq 12049
#
object-group service TCP_23000
 0 service tcp destination eq 23000
#
object-group service TCP_2379
 0 service tcp destination eq 2379
#
object-group service TCP_25601
 0 service tcp destination eq 25601
#
object-group service TCP_28070
 0 service tcp destination eq 28070
#
object-group service TCP_28080
 0 service tcp destination eq 28080
#
object-group service TCP_28081
 0 service tcp destination eq 28081
#
object-group service TCP_28088
 0 service tcp destination eq 28088
#
object-group service TCP_28180
 0 service tcp destination eq 28180
#
object-group service TCP_29092
 0 service tcp destination eq 29092
#              
object-group service TCP_29093
 0 service tcp destination eq 29093
#
object-group service TCP_29411
 0 service tcp destination eq 29411
#
object-group service TCP_30200
 0 service tcp destination eq 30200
#
object-group service TCP_30201
 0 service tcp destination eq 30201
#
object-group service TCP_30514
 0 service tcp destination eq 30514
#
object-group service TCP_30902
 0 service tcp destination eq 30902
#
object-group service TCP_31050-31051
 0 service tcp destination range 31050 31051
#
object-group service TCP_31306
 0 service tcp destination eq 31306
#
object-group service TCP_3191
 0 service tcp destination eq 3191
#
object-group service TCP_3389
 0 service tcp destination eq 3389
#
object-group service TCP_3555
 0 service tcp destination eq 3555
#
object-group service TCP_3557
 0 service tcp destination eq 3557
#
object-group service TCP_3558
 0 service tcp destination eq 3558
#
object-group service TCP_389
 0 service tcp destination eq 389
#
object-group service TCP_5001
 0 service tcp destination eq 5001
#
object-group service TCP_5003
 0 service tcp destination eq 5003
#
object-group service TCP_5004
 0 service tcp destination eq 5004
#
object-group service TCP_5008
 0 service tcp destination eq 5008
#
object-group service TCP_5480
 0 service tcp destination eq 5480
#
object-group service TCP_6370
 0 service tcp destination eq 6370
#
object-group service TCP_7001
 0 service tcp destination eq 7001
#
object-group service TCP_7100
 0 service tcp destination eq 7100
#
object-group service TCP_8000
 0 service tcp destination eq 8000
#              
object-group service TCP_8001
 0 service tcp destination eq 8001
#
object-group service TCP_8080
 0 service tcp destination eq 8080
#
object-group service TCP_8082
 0 service tcp destination eq 8082
#
object-group service TCP_8086
 0 service tcp destination eq 8086
#
object-group service TCP_8088
 0 service tcp destination eq 8088
#
object-group service TCP_8112
 0 service tcp destination eq 8112
#
object-group service TCP_8400
 0 service tcp destination eq 8400
#
object-group service TCP_8400-8450
 0 service tcp destination range 8400 8450
#
object-group service TCP_8472
#
object-group service TCP_9090-9091
 0 service tcp destination range 9090 9091
#
object-group service TCP_9100
 0 service tcp destination eq 9100
#
object-group service TCP_9110
 0 service tcp destination eq 9110
#
object-group service TCP_9120
 0 service tcp destination eq 9120
#
object-group service TCP_9130
#
object-group service TCP_9443
 0 service tcp destination eq 9443
#
object-group service TCP_9600
 0 service tcp destination eq 9600
#              
object-group service UDP-1812
 0 service udp destination eq 1812
#
object-group service UDP_123
 0 service udp destination eq 123
#
object-group service UDP_161
 0 service udp destination eq 161
#
object-group service UDP_162
 0 service udp destination eq 162
#
object-group service UDP_514
 0 service udp destination eq 514
#
object-group service UDP_8472
 0 service udp destination eq 8472
#
interface Bridge-Aggregation1
 description "TO-XWHPD-NE2ACL-SW01 Agg1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 link-aggregation mode dynamic
#
interface Bridge-Aggregation3
 description "TO-XWHPD-NE1DIL-SW01 Agg4"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 link-aggregation mode dynamic
#
interface Route-Aggregation100
 description IRF-BFD_MAD
 mad bfd enable
 mad ip address ************* *************** member 1
 mad ip address ************* *************** member 2
#
interface NULL0
#
interface GigabitEthernet1/0/1/1
 port link-mode route
 description "TO-XWHPD-NE2DIL-FW01 G2/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/0/1/2
 port link-mode route
#
interface GigabitEthernet1/0/1/3
 port link-mode route
#
interface GigabitEthernet1/0/1/4
 port link-mode route
#
interface GigabitEthernet1/9/1/1
 port link-mode route
 description "TO-XWHPD-NE2DIL-FW01 G2/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet1/9/1/2
 port link-mode route
#
interface GigabitEthernet1/9/1/3
 port link-mode route
#
interface GigabitEthernet1/9/1/4
 port link-mode route
#
interface GigabitEthernet2/0/1/1
 port link-mode route
 description "TO-XWHPD-NE2DIL-FW01 G1/0/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/0/1/2
 port link-mode route
#
interface GigabitEthernet2/0/1/3
 port link-mode route
#
interface GigabitEthernet2/0/1/4
 port link-mode route
#
interface GigabitEthernet2/9/1/1
 port link-mode route
 description "TO-XWHPD-NE2DIL-FW01 G1/9/1/1 BFD"
 port link-aggregation group 100
#
interface GigabitEthernet2/9/1/2
 port link-mode route
#
interface GigabitEthernet2/9/1/3
 port link-mode route
#
interface GigabitEthernet2/9/1/4
 port link-mode route
#
interface M-GigabitEthernet1/0/0/0
 ip binding vpn-instance management
 ip address ********** *************
#
interface Ten-GigabitEthernet1/0/1/5
 port link-mode route
#
interface Ten-GigabitEthernet1/9/1/5
 port link-mode route
#
interface Ten-GigabitEthernet2/0/1/5
 port link-mode route
#
interface Ten-GigabitEthernet2/9/1/5
 port link-mode route
#
interface Ten-GigabitEthernet1/0/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/2/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/2/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/2/0/8"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/0/1/9
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T1/0/1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/10
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T1/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/11
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T1/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/3/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/3/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet1/9/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T1/3/0/8"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#              
interface Ten-GigabitEthernet1/9/1/9
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T1/0/5"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/10
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T1/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/9/1/11
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T1/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/2/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/2/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/2/0/8"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/0/1/9
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T2/0/1"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/10
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T2/0/2"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/0/1/11
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T2/0/3"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/6
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/3/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/7
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/3/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/8
 port link-mode bridge
 description "TO-XWHPD-NE1DIL-SW01 T2/3/0/8"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 3
#
interface Ten-GigabitEthernet2/9/1/9
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T2/0/5"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/10
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T2/0/6"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet2/9/1/11
 port link-mode bridge
 description "TO-XWHPD-NE2ACL-SW01 T2/0/7"
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 3000 3006 to 3010
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/0/1/12
 description "TO-XWHPD-NE2DIL-FW01 T2/0/1/12 IRF"
#
interface Ten-GigabitEthernet1/9/1/12
 description "TO-XWHPD-NE2DIL-FW01 T2/9/1/12 IRF"
#
interface Ten-GigabitEthernet2/0/1/12
 description "TO-XWHPD-NE2DIL-FW01 T1/0/1/12 IRF"
#
interface Ten-GigabitEthernet2/9/1/12
 description "TO-XWHPD-NE2DIL-FW01 T1/9/1/12 IRF"
#
interface Blade1/6/0/1
#
interface Blade1/6/0/2
#
interface Blade2/6/0/1
#              
interface Blade2/6/0/2
#
interface Blade-Aggregation1
 link-aggregation blade Blade4fw
#
interface Blade-Aggregation257
#
security-zone name Local
#
security-zone name Trust
 import interface Route-Aggregation100
#
security-zone name DMZ
#
security-zone name Untrust
#
security-zone name Management
 import interface M-GigabitEthernet1/0/0/0
#
security-zone name TENANT01_CORE_Inside
 import interface Bridge-Aggregation1 vlan 3006
#
security-zone name TENANT01_CORE_Outside
 import interface Bridge-Aggregation3 vlan 3006
#
security-zone name TENANT02_CORE_Inside
 import interface Bridge-Aggregation1 vlan 3007
#
security-zone name TENANT02_CORE_Outside
 import interface Bridge-Aggregation3 vlan 3007
#
security-zone name TENANT03_CORE_Inside
 import interface Bridge-Aggregation1 vlan 3008
#
security-zone name TENANT03_CORE_Outside
 import interface Bridge-Aggregation3 vlan 3008
#
security-zone name TENANT04_CORE_Inside
 import interface Bridge-Aggregation1 vlan 3009
#
security-zone name TENANT04_CORE_Outside
 import interface Bridge-Aggregation3 vlan 3009
#
security-zone name TENANT05_CORE_Inside
 import interface Bridge-Aggregation1 vlan 3010
#              
security-zone name TENANT05_CORE_Outside
 import interface Bridge-Aggregation3 vlan 3010
#
security-zone name TENANT_PUBLIC_Inside
 import interface Bridge-Aggregation1 vlan 3000
#
security-zone name TENANT_PUBLIC_Outside
 import interface Bridge-Aggregation3 vlan 3000
#
zone-pair security source Local destination Trust
 packet-filter 2000
#
zone-pair security source Trust destination Local
 packet-filter 2000
#
 scheduler logfile size 16
#
line class console
 user-role network-admin
#
line class vty
 user-role network-operator
#              
line con 1/0 1/1
 authentication-mode scheme
 user-role network-admin
#
line con 2/0 2/1
 user-role network-admin
#
line con 1/6
 authentication-mode scheme
 user-role network-admin
#
line con 2/6
 user-role network-admin
#
line vty 0 63
 authentication-mode scheme
 user-role network-admin
#
 ip route-static vpn-instance management 0.0.0.0 0 ***********
#
 info-center timestamp loghost iso
 info-center loghost source M-GigabitEthernet1/0/0/0
 info-center loghost vpn-instance MGMT ***********
 info-center loghost vpn-instance management **********
 info-center loghost vpn-instance MGMT ***********
 info-center loghost vpn-instance MGMT ***********6
 info-center loghost vpn-instance MGMT ************
 info-center loghost vpn-instance MGMT *************
#
 mad exclude interface M-GigabitEthernet1/0/0/0
#
 snmp-agent
 snmp-agent local-engineid 800063A280542BDE1039D200000001
 snmp-agent community read cipher $c$3$yoxxgVYBSGIjgwwD4d3vQCRUQmVwjExuebtarE6s acl name ACL-SNMP
 snmp-agent sys-info location fw01-w5r-D-15-3-20&D-16-3-20
 snmp-agent sys-info version v2c v3 
 snmp-agent target-host trap address udp-domain ************ vpn-instance MGMT params securityname cslc_snmp v2c
 snmp-agent target-host trap address udp-domain ************* vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain *********** params securityname cslpubaclic v2c
 snmp-agent target-host trap address udp-domain ********** vpn-instance MGMT params securityname cslpubaclic v2c
 snmp-agent trap enable arp 
 snmp-agent trap enable radius 
 snmp-agent trap enable stp 
 snmp-agent trap enable syslog 
 snmp-agent trap source M-GigabitEthernet1/0/0/0
#              
 ssh server enable
 sftp server enable
#
redundancy group 1
 member failover group 1
 node 1
  bind chassis 1
  priority 100
  track 101 interface Blade1/6/0/1
  track 102 interface Blade1/6/0/2
 node 2
  bind chassis 2
  priority 50
  track 201 interface Blade2/6/0/1
  track 202 interface Blade2/6/0/2
#
 ntp-service enable
 ntp-service unicast-server ********** vpn-instance management source M-GigabitEthernet1/0/0/0
#
acl basic name ACL-SNMP
 description "Network monitor system"
 rule 0 permit vpn-instance management source ********** *********
 rule 5 permit vpn-instance management source ************* 0
 rule 10 permit vpn-instance management source *********** *********
 rule 15 permit vpn-instance management source *********** 0
 rule 20 permit vpn-instance MGMT source ************ 0
 rule 1000 deny vpn-instance management
#
domain system
#
 domain default enable system
#
role name level-0
 description Predefined level-0 role
#
role name level-1
 description Predefined level-1 role
#
role name level-2
 description Predefined level-2 role
#
role name level-3
 description Predefined level-3 role
#
role name level-4
 description Predefined level-4 role
#
role name level-5
 description Predefined level-5 role
#
role name level-6
 description Predefined level-6 role
#
role name level-7
 description Predefined level-7 role
#
role name level-8
 description Predefined level-8 role
#
role name level-9
 description Predefined level-9 role
#
role name level-10
 description Predefined level-10 role
#
role name level-11
 description Predefined level-11 role
#
role name level-12
 description Predefined level-12 role
#
role name level-13
 description Predefined level-13 role
#
role name level-14
 description Predefined level-14 role
#
role name systemadmin
 rule 1 permit read web-menu m_monitor/m_atklog/m_blacklistlog
 rule 2 permit read web-menu m_monitor/m_atklog/m_singleatk
 rule 3 permit read web-menu m_monitor/m_atklog/m_scanatk
 rule 4 permit read web-menu m_monitor/m_atklog/m_floodatk
 rule 5 permit read web-menu m_monitor/m_atklog/m_threatlog
 rule 6 permit read web-menu m_monitor/m_atklog/m_urllog
 rule 7 permit read web-menu m_monitor/m_atklog/m_filefilterlog
 rule 8 permit read web-menu m_monitor/m_atklog/m_zonepairlog
 rule 9 permit read web-menu m_monitor/m_auditlogs/m_auditimchatlog
 rule 10 permit read web-menu m_monitor/m_auditlogs/m_auditcommunitylog
 rule 11 permit read web-menu m_monitor/m_auditlogs/m_auditsearchenginelog
 rule 12 permit read web-menu m_monitor/m_auditlogs/m_auditmaillog
 rule 13 permit read web-menu m_monitor/m_auditlogs/m_auditfiletransferlog
 rule 14 permit read web-menu m_monitor/m_auditlogs/m_auditrelaxstocklog
 rule 15 permit read web-menu m_monitor/m_auditlogs/m_auditotherapplog
 rule 16 permit read web-menu m_monitor/m_monitorlog/m_trafficlog
 rule 17 permit read web-menu m_monitor/m_rank/m_trafficrank
 rule 18 permit read web-menu m_monitor/m_rank/m_threadrank
 rule 19 permit read web-menu m_monitor/m_rank/m_urlfilterrank
 rule 20 permit read web-menu m_monitor/m_rank/m_ffilterrank
 rule 21 permit read web-menu m_monitor/m_rank/m_securityaudit
 rule 22 permit read web-menu m_monitor/m_rank/m_lb_serverreport
 rule 23 permit read web-menu m_monitor/m_rank/m_lb_linkreport
 rule 24 permit read web-menu m_monitor/m_rank/m_lb_dnsproxyreport
 rule 25 permit read web-menu m_monitor/m_trend/m_traffictrend
 rule 26 permit read web-menu m_monitor/m_trend/m_threadtrend
 rule 27 permit read web-menu m_monitor/m_trend/m_urlfiltertrend
 rule 28 permit read web-menu m_monitor/m_trend/m_ffiltertrend
 rule 29 permit read web-menu m_monitor/m_trend/m_lb_urltrend
 rule 30 permit read web-menu m_monitor/m_report
 rule 31 permit read web-menu m_monitor/m_session
 rule 32 permit read web-menu m_monitor/m_lb_dnscaches
 rule 33 permit read web-menu m_monitor/m_userinfocenter
 rule 34 permit read web-menu m_policy/m_firewall/m_secpolicy
 rule 35 permit read web-menu m_policy/m_firewall/m_redundancyrules
 rule 36 permit read web-menu m_policy/m_firewall/m_targetpolicy
 rule 37 permit read web-menu m_policy/m_attackdefense/m_atkpolicy
 rule 38 permit read web-menu m_policy/m_attackdefense/m_clientverifyprotectip
 rule 39 permit read web-menu m_policy/m_attackdefense/m_blacklistmanual
 rule 40 permit read web-menu m_policy/m_attackdefense/m_whitelistmanual
 rule 41 permit read web-menu m_policy/m_attackdefense/m_clientverifyzone
 rule 42 permit read web-menu m_policy/m_attackdefense/m_connlimitpolicies
 rule 43 permit read web-menu m_policy/m_attackdefense/m_urpf
 rule 44 permit read web-menu m_policy/m_nat/m_natoutboundconfig
 rule 45 permit read web-menu m_policy/m_nat/m_natserverconfig
 rule 46 permit read web-menu m_policy/m_nat/m_natstaticchange
 rule 47 permit read web-menu m_policy/m_nat/m_natoutbound444config
 rule 48 permit read web-menu m_policy/m_nat/m_natoutboundstatic444config
 rule 49 permit read web-menu m_policy/m_nat/m_natsettings
 rule 50 permit read web-menu m_policy/m_aft/m_aftaddrgrp
 rule 51 permit read web-menu m_policy/m_aft/m_aftnat64
 rule 52 permit read web-menu m_policy/m_aft/m_aftoutbound
 rule 53 permit read web-menu m_policy/m_aft/m_aftset
 rule 54 permit read web-menu m_policy/m_appaudit/m_auditpolicy
 rule 55 permit read web-menu m_policy/m_appaudit/m_keywordgroups
 rule 56 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthpolicy
 rule 57 permit read web-menu m_policy/m_bandwidthmanagement/m_bandwidthchannel
 rule 58 permit read web-menu m_policy/m_bandwidthmanagement/m_interfacebandwidth
 rule 59 permit read web-menu m_policy/m_loadbalance/m_lb_globalconfig
 rule 60 permit read web-menu m_policy/m_loadbalance/m_lb_server
 rule 61 permit read web-menu m_policy/m_loadbalance/m_lb_link
 rule 62 permit read web-menu m_policy/m_netshare/m_netsharepolicy
 rule 63 permit read web-menu m_policy/m_netshare/m_netsharestatus
 rule 64 permit read web-menu m_policy/m_proxymanagement/m_proxypolicy
 rule 65 permit read web-menu m_policy/m_proxymanagement/m_whitelisthostname
 rule 66 permit read web-menu m_policy/m_proxymanagement/m_sslcertificate
 rule 67 permit read web-menu m_resource/m_healthmonitor
 rule 68 permit read web-menu m_resource/m_user/m_usercontrol
 rule 69 permit read web-menu m_resource/m_user/m_authentication
 rule 70 permit read web-menu m_resource/m_user/m_access
 rule 71 permit read web-menu m_resource/m_dpi/m_ipscfg
 rule 72 permit read web-menu m_resource/m_dpi/m_antiviruscfg
 rule 73 permit read web-menu m_resource/m_dpi/m_dfltcfg
 rule 74 permit read web-menu m_resource/m_dpi/m_ufltcfg
 rule 75 permit read web-menu m_resource/m_dpi/m_ffltcfg
 rule 76 permit read web-menu m_resource/m_dpi/m_apprecognition
 rule 77 permit read web-menu m_resource/m_dpi/m_securityaction
 rule 78 permit read web-menu m_resource/m_dpi/m_dpicfg
 rule 79 permit read web-menu m_resource/m_objectgroup/m_ipv4objectgroup
 rule 80 permit read web-menu m_resource/m_objectgroup/m_ipv6objectgroup
 rule 81 permit read web-menu m_resource/m_objectgroup/m_macobjectgroup
 rule 82 permit read web-menu m_resource/m_objectgroup/m_serviceobjectgroup
 rule 83 permit read web-menu m_resource/m_objectgroup/m_timerange
 rule 84 permit read web-menu m_resource/m_acl/m_ipv4acl
 rule 85 permit read web-menu m_resource/m_acl/m_ipv6acl
 rule 86 permit read web-menu m_resource/m_acl/m_macacl
 rule 87 permit read web-menu m_resource/m_ssl/m_sslserver
 rule 88 permit read web-menu m_resource/m_ssl/m_sslclient
 rule 89 permit read web-menu m_resource/m_ssl/m_ssladvancesettiing
 rule 90 permit read web-menu m_resource/m_publickey/m_publickeylocal
 rule 91 permit read web-menu m_resource/m_publickey/m_publickeypeer
 rule 92 permit read web-menu m_resource/m_pki_cert/m_pki
 rule 93 permit read web-menu m_resource/m_pki_cert/m_certificatepolicy
 rule 94 permit read web-menu m_resource/m_pki_cert/m_certificatesubject
 rule 95 permit read web-menu m_network/m_vrf
 rule 96 permit read web-menu m_network/m_if/m_interface
 rule 97 permit read web-menu m_network/m_if/m_inlineall
 rule 98 permit read web-menu m_network/m_if/m_lagg
 rule 99 permit read web-menu m_network/m_seczone
 rule 100 permit read web-menu m_network/m_link/m_vlan
 rule 101 permit read web-menu m_network/m_link/m_mac_sum
 rule 102 permit read web-menu m_network/m_dns_sum/m_dnshosts
 rule 103 permit read web-menu m_network/m_dns_sum/m_dns
 rule 104 permit read web-menu m_network/m_dns_sum/m_ddns
 rule 105 permit read web-menu m_network/m_dns_sum/m_dnsadvance
 rule 106 permit read web-menu m_network/m_ip_net/m_ip
 rule 107 permit read web-menu m_network/m_ip_net/m_arp
 rule 108 permit read web-menu m_network/m_ipv6_net/m_ipv6
 rule 109 permit read web-menu m_network/m_ipv6_net/m_nd
 rule 110 permit read web-menu m_network/m_vpn/m_gre
 rule 111 permit read web-menu m_network/m_vpn/m_ipsec
 rule 112 permit read web-menu m_network/m_vpn/m_advpn
 rule 113 permit read web-menu m_network/m_vpn/m_l2tp
 rule 114 permit read web-menu m_network/m_sslvpn/m_sslvpn_context
 rule 115 permit read web-menu m_network/m_sslvpn/m_sslvpn_gateway
 rule 116 permit read web-menu m_network/m_sslvpn/m_sslvpn_ipv4addrpool
 rule 117 permit read web-menu m_network/m_sslvpn/m_sslvpn_snatpool
 rule 118 permit read web-menu m_network/m_sslvpn/m_sslvpn_acif
 rule 119 permit read web-menu m_network/m_sslvpn/m_sslvpn_globalconfig
 rule 120 permit read web-menu m_network/m_sslvpn/m_sslvpn_tempmanagement
 rule 121 permit read web-menu m_network/m_sslvpn/m_sslvpn_statistics
 rule 122 permit read web-menu m_network/m_routing/m_routingtable
 rule 123 permit read web-menu m_network/m_routing/m_staticrouting
 rule 124 permit read web-menu m_network/m_routing/m_policyrouting
 rule 125 permit read web-menu m_network/m_routing/m_ospf
 rule 126 permit read web-menu m_network/m_routing/m_bgp
 rule 127 permit read web-menu m_network/m_routing/m_rip
 rule 128 permit read web-menu m_network/m_multicast/m_multicastrouting
 rule 129 permit read web-menu m_network/m_multicast/m_pim
 rule 130 permit read web-menu m_network/m_multicast/m_igmp
 rule 131 permit read web-menu m_network/m_dhcp/m_dhcpservice
 rule 132 permit read web-menu m_network/m_dhcp/m_dhcppool
 rule 133 permit read web-menu m_network/m_ipservice/m_ssh
 rule 134 permit read web-menu m_network/m_ipservice/m_ntp
 rule 135 permit read web-menu m_network/m_ipservice/m_ftp
 rule 136 permit read web-menu m_network/m_ipservice/m_telnet
 rule 137 permit read web-menu m_device/m_diagnosis/m_ping
 rule 138 permit read web-menu m_device/m_diagnosis/m_tracert
#
user-group system
#
local-user admin class manage
 password hash $h$6$a1RKH2Uh4ZoWDHsY$qfikbbJJYS4M0PeczFv9TR6yfiN+CdJRtXwriq8s3958M/jZBFv6ai7dNKyLg2nOMacEh2n2qX8v9ED3pQvakA==
 service-type ssh terminal https
 authorization-attribute user-role level-3
 authorization-attribute user-role network-admin
 authorization-attribute user-role network-operator
#
local-user operator class manage
 password hash $h$6$mS2ZXvtLYRi3XnoJ$eUB7PSIgVrdZFgq6WcYv4iE3lsKrdwJeGiyxEin6VMgsj7DmWSeVFTKXmtQk85+GKyJmdepZxUvwiF5iijq2/Q==
 service-type ssh terminal https
 authorization-attribute user-role level-1
 authorization-attribute user-role systemadmin
#
 session statistics enable
 session synchronization enable 
#
 ip https enable
#
 inspect optimization no-acsignature disable
 inspect optimization raw disable
 inspect optimization uncompress disable
 inspect optimization url-normalization disable
 inspect optimization chunk disable
#
security-policy ip
 rule 268 name YZBOCC_**********_3555
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip YZBOCC_**********
  destination-ip RTQDB-**********3
  service TCP_3555
 rule 269 name YZBOCC_Deny
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip YZBOCC
  source-ip YZECC-*********
  service TCP_31306
  service TCP_3558
  service ssh
  service TCP_3555
  service TCP_3389
 rule 184 name G3_To_CSLC_NTP
  action pass
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_*********/16
  destination-ip CSLCNTP-*******
  service ntp
 rule 125 name G3-WCSDB-G2-SBCDB
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_4.190.80.71
  source-ip V3_CORE_4.190.80.72
  source-ip V3_CORE_4.190.80.73
  destination-ip G2SBCDB-*********-37
  service TCP-3556
 rule 120 name _to_**********
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip G3LINSHISBCCOPYTOWCS01-0.200
  service ssh
  service TCP_8080
 rule 121 name **********_WCSDB
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G3LINSHISBCCOPYTOWCS01-0.200
  destination-ip V3_CORE_4.190.80.73
  service TCP_3558
 rule 15 name "V3-CORE-K8SNODE To V3-MS-K8SNODE"
  description V3-7
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_MS_K8SNODE_***********/24
  service TCP_2379
  service TCP_8080
  service TCP_10255
  service TCP_10250
  service TCP-6443
 rule 28 name "V3_MS-K8SNODE To V3_CORE_K8SNODE"
  description V3-8
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_2379
  service TCP_8080
  service TCP_10250
  service TCP_10255
  service TCP_28081
 rule 29 name "V3-MS-K8SNODE To V3-CORE-K8SNODE Flannel"
  description V3-9
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_8472
 rule 16 name "V3-CORE-K8SNODE To V3-GW&MS-K8SNODE Flannel"
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_K8SNODE_**********/24
  service UDP_8472
 rule 30 name "V3-GW-K8SNODE To V3-CORE-K8SNODE Flannel"
  description V3-10
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_8472
 rule 53 name "To TENANT-PUBLIC-NFS"
  action pass
  source-zone TENANT_PUBLIC_Outside
  destination-zone TENANT_PUBLIC_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3MNYY_CORE_**********/24
  destination-ip NAS_4.191.41.1
 rule 47 name "V3-CORE To Harbor"
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip HARBOR
  destination-ip OPS
  destination-ip nginx01_4.190.120.51/32
  destination-ip nginx02_4.190.120.52/32
  destination-ip Harbor-F5-***********/32
  service http
  service https
 rule 17 name "V3-CORE-K8SNODE To V3-MS-F5-***********"
  description V3-16
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_MS_F5_***********
  service TCP_8080
 rule 18 name "V3-CORE-Tidb-Cluster To V3-MS-Tidb-Monitor"
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_Tidb_Clus_**********/24
  destination-ip V3_MS_Tidb_Monitor*************
  destination-ip V3_MS_*************/32
 rule 261 name R1.6.5.1
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip ShuJuJiChengPingTai-**********
  service TCP-22
 rule 262 name Ansbile
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip Ansbile-************
  destination-ip G3_*********/16
  service ssh
 rule 263 name OPERVM-TO-ELK
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  source-ip G3BISMONTORCOLLECT
  destination-ip G3ELK-***********-5
  destination-ip G3-MS-ELKF5-************
  service TCP-29200
 rule 264 name R165patch02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip T1_********/24
  source-ip *********/16
  source-ip YZ-ECC-*********
  source-ip BOCC
  destination-ip G3RMXLOGON-************
  service TCP-9130
  service TCP-9131
 rule 49 name "G3-CORE-Tidb-Cluster To G3-MS-Tidb-Monitor"
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_Tidb_Clus_**********/24
  destination-ip V3_MS_Tidb_Monitor*************
  destination-ip V3_MS_*************/32
 rule 5 name 1
  description V3-11
  action pass
  source-ip *********/16
  destination-ip *********/16
  service ssh
 rule 11 name 3
  action pass
  logging enable
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_MS_***********/22
  destination-ip NAS_4.191.41.1
  destination-ip nfs-**********
 rule 19 name 4
  description V3-5
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip Configcenter
  service TCP_28081
 rule 20 name 5
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_MS_K8SNODE_***********/24
  service TCP_10250
  service TCP_10255
  service TCP_8080
  service TCP_2379
  service TCP_10256
 rule 21 name 6
  description V3-17
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_GW_K8SNODE_**********/24
  service UDP_8472
 rule 22 name 7
  description V3-33
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip CAS_F5_***********
  source-ip **********1
  source-ip **********2
  source-ip casgw_***********
  source-ip V3_CORE_**********/22
  destination-ip T1_CeShi_********
  service TCP_389
 rule 23 name 8
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/21
  destination-ip V3_MS_***********/22
  service TCP_28070
 rule 24 name 9
  description V3-21
  action pass  
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BOCC
  source-ip 4A-*********
  source-ip YJ-TS-*************/24
  source-ip *********/16
  destination-ip *********/16
  service TCP_3558
  service TCP_28080
  service TCP_28081
  service ssh
  service http
  service https
  service TCP_31306
  service TCP_8088
  service TCP_3555
  service TCP_7001
  service TCP_3389
  service TCP_8080
  service TCP_28070
  service TCP-29090
  service TCP_8086
  service TCP_31050-31051
  service TCP_8000
  service TCP_28088
  service TCP_28180
  service TCP_23000
  service TCP_29411
  service TCP_25601
  service TCP_9600
  service TCP_9443
  service TCP_5480
  service TCP_29093
  service TCP-8022
  service TCP-9090
  service TCP-3601
  service TCP-8018
  service TCP-5000
  service TCP-8161
  service TCP-8080
  service TCP-30900
 rule 25 name 10
  description V3-6
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_**********/21
  source-ip V3_MS_***********/22
  destination-ip V3_CORE_**********/21
  service TCP_6370
  service TCP_5004
  service TCP_5003
  service TCP-5000_5007
  service TCP_5008
  service TCP-4100
 rule 31 name 11
  description V3-28
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_Tidb_Monitor*************
  source-ip V3_MS_*************/32
  destination-ip V3_CORE_Tidb_Clus_**********/24
  service TCP_10250
  service TCP_10255
  service TCP_8080
  service TCP_2379
  service ssh
  service TCP_9090-9091
  service TCP-2379-2380
  service TCP-9100-20182
  service TCP-8249-8250
  service TCP_31306
  service TCP-10080
 rule 32 name 12
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_Tidb_Monitor*************
  destination-ip V3_CORE_TIDB_F5_***********
  destination-ip V3_CORE_TIDB_F5_***********
  service TCP_31306
 rule 33 name 13
  description V3-3
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_MS_***********/22
  destination-ip Zookeeper
  service TCP_3191
 rule 34 name 14
  description V3-4
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip Redis_oltp
  destination-ip Redis_feioltp
  service TCP_7001
 rule 35 name 15
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service UDP_8472
 rule 36 name 16
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service UDP_8472
  service TCP-30350
 rule 37 name 17
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip JianKong_***********
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_12049
  service TCP_9100
 rule 38 name 18
  description V3-20
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_Tidb_Clus_**********/24
  service TCP_31306
  service ssh
 rule 40 name 20
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip casgw_***********
  destination-ip CAS_F5_***********
  service https
  service TCP_28080
  service TCP_28081
 rule 41 name 21
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_***********2/32
  source-ip V3_MS_***********1/32
  destination-ip V3_CORE_4.190.80.71_73
  service http
  service TCP_3558
 rule 42 name 22
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_***********2/32
  source-ip V3_MS_***********1/32
  destination-ip V3_CORE_4.190.80.81_83
  destination-ip V3_CORE_F5_***********/24
  destination-ip V3_CORE_4.190.81.21_25
  service TCP_31306
 rule 43 name 23
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_***********2/32
  source-ip V3_MS_***********1/32
  destination-ip V3_CORE_************/32
  service TCP_3555
 rule 57 name V3-1
  action pass
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip HARBOR
  destination-ip OPS
  destination-ip Harbor-F5-***********/32
  destination-ip V3_MS_OPS_***********
  service http
  service https
 rule 58 name V3-2
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip HARBOR
  source-ip Harbor-F5-***********/32
  source-ip OPS
  source-ip V3_MS_OPS_***********
  destination-ip V3_CORE_K8SNODE_**********/24
  service http
  service https
 rule 59 name V3-13
  action pass
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/21
  destination-ip V3_MS_***********/22
  service TCP_31306
  service ssh
 rule 60 name V3-14
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_***********/22
  source-ip V3_MS_K8SNODE_***********/24
  source-ip DMZ-*********/24
  destination-ip V3_CORE_**********/21
  destination-ip ***********
  service TCP_31306
  service ssh
  service TCP_28081
  service TCP_28070
 rule 61 name V3-18
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2OCS-**********
  source-ip **********-162
  destination-ip *********/16
  service TCP_31306
  service TCP_3555
  service TCP_3558
  service ssh
 rule 62 name V3-19
  action pass  
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip yunwei-***********1-12
  destination-ip *********/16
  service ssh
  service TCP_3555
  service TCP_3558
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP_2379
  service TCP_8472
  service UDP_8472
  service TCP_8080
 rule 77 name V3-20
  action pass
  logging enable
  source-zone TENANT_PUBLIC_Outside
  destination-zone TENANT_PUBLIC_Inside
  source-ip V3_CORE_**********/22
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_GW_**********/22
  source-ip DMZ_NAS_4.191.56.0/22
  source-ip V3_MS_***********/22
  source-ip G3-GW-**********/24
  source-ip **********/22
  source-ip **********/22
  destination-ip NAS-**********
 rule 63 name V3-25
  action pass
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip *********/16
  destination-ip T1_********73
  destination-ip T1_NTP_*********73
  service ntp
  service http
 rule 64 name V3-31
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip "CORE ZABBIX PROXY-************"
  destination-ip "ZABBIX SERVER-************-42"
  destination-ip ***********5
  service TCP_31050-31051
 rule 65 name V3-32
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip "ZABBIX SERVER-************-42"
  source-ip ***********5
  destination-ip "CORE ZABBIX PROXY-************"
  service TCP_31050-31051
 rule 66 name V3-34
  action pass
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/22
  destination-ip G2RMXDB-**********
  service TCP_3555
 rule 67 name V3-35
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip "G3 RMX REPORT-**********21"
  destination-ip W5RFTP-**********
  destination-ip G2FTP-**********
  destination-ip ********
  service ftp
  service ssh
 rule 68 name V3-38
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3AMS-************-132
  destination-ip G2BMSDB
  service TCP_3555
 rule 69 name V3-39
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********
  destination-ip G2RTQDB
  service TCP_3555
 rule 70 name V3-40
  action pass
  source-ip **********
  destination-ip W5RFTP-**********
  destination-ip G2FTP-**********
  destination-ip CSLC-*******
  service ssh
 rule 76 name V3-41
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/22
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip CAS-**********/22
  destination-ip NAS_4.191.40.3
  destination-ip NAS_**********
  destination-ip NAS-**********-6
 rule 71 name V3-44
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip *********/16
  destination-ip V3_MS_***********/22
  service TCP_29092
 rule 72 name V3-45
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip DMZ-*********/24
  destination-ip V3_CORE_**********/22
  destination-ip ***********
  service TCP_28081
 rule 73 name V3-46
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BOCC
  source-ip *********/16
  destination-ip V3_CORE_**********/22
  destination-ip ***********
  service TCP_28070
 rule 74 name V3-47
  action pass
  logging enable
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip CSLC-*******
  destination-ip CSLC-*******
  service ssh  
  service TCP-34443
 rule 78 name V3-48
  description 

  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM
  destination-ip *********/16
  service ssh
  service TCP_3191
  service TCP_3555
  service TCP_3558
  service TCP_5003
  service TCP_6370
  service TCP_7001
  service TCP_8080
  service TCP_8472
  service UDP_8472
  service TCP_2379
  service http
  service TCP_31306
  service TCP-10251-10252
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service TCP_7100
  service https
 rule 90 name V3-49
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip V3_GW_K8SNODE_**********/24
  service TCP_10250
  service TCP_10255
 rule 91 name V3-50
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_10250
  service TCP_10255
 rule 97 name V3_51
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_***********/22
  destination-ip V3_CORE_**********/21
  service TCP-29090
 rule 103 name V3_52
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip **********
  destination-ip **********
 rule 98 name V3-52
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip V3_CORE_K8SNODE_**********/24
  service TCP_9100
 rule 108 name V3-53
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_**********/21
  service TCP_3191
  service TCP_9100
  service TCP-5000_5007
  service TCP-6379
 rule 113 name V3-54
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip Redis_feioltp
  destination-ip Redis_oltp
  service TCP_7001
 rule 116 name V3-55
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/21
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip G2SBCDB-*********-37
  destination-ip G2SBCDB-**********-36
  service TCP_3555
  service TCP-3556
 rule 117 name V3-56
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_**********/22
  destination-ip ***********
  service TCP_28070
 rule 118 name V3-57
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip DMZ-*********/24
  source-ip *********/16
  source-ip CSLC-baoleiji-**********
  source-ip TicaiDC-********-2
  destination-ip V3_DNS_***********
  destination-ip V3_DNS_***********
  service dns-tcp
  service dns-udp
 rule 122 name V3-58
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip hermes-***********-16
  service TCP_5003
 rule 123 name V3-59
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_***********/22
  destination-ip V3_CORE_**********/22
  service TCP_6370
 rule 124 name V3-60
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip *******/8
  destination-ip T1_********73
  destination-ip T1_NTP_*********73
  service http
  service ntp
 rule 137 name V3-61
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_**********/22
  service TCP-3558
  service TCP-31306
  service TCP-3555
 rule 119 name V3_AMS-To-JiGuan
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip F5-AMS-***********0
  destination-ip JiGuan-***************
  service https
  service http
 rule 4 name ospf
  description V3-12
  action pass
  logging enable
  service ospf
  service ping
 rule 26 name "T1 To G3-**********/22"
  action pass
  counting enable
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip T1_********/16
  source-ip TEST_4.176.0.0/16
  destination-ip *********/16
  service ssh
  service ftp
  service tftp
 rule 80 name V3-T1_Harbor
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_MS_***********/22
  destination-ip **********
  service http
  service https
 rule 109 name T1_DNS_V3_DNS
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip T1_CeShi_********
  destination-ip V3_DNS_***********
  destination-ip V3_DNS_***********
  service dns-tcp
  service dns-udp
 rule 126 name G3-RMX-G2RMX
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/22
  destination-ip T1_**********/32
  service TCP_3555
 rule 133 name Backups-***********
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip TENANT02-CORE-**********53/32
  source-ip TENANT02-CORE-***********/32
  source-ip TENANT02-CORE-***********-16
  source-ip TENANT02-CORE-**********71-173
  destination-ip Backup-Server_***********/32
  service ssh
  service TCP-8400-8900
 rule 134 name Backup_Server_To_TENANT02_CORE
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip Backup-Server_***********/32
  destination-ip TENANT02-CORE-**********53/32
  destination-ip TENANT02-CORE-***********-16
  destination-ip TENANT02-CORE-**********71-173
  destination-ip TENANT02-CORE-***********/32
  service ssh
  service TCP-8400-8900
 rule 136 name G3-Network-Test
  action pass
  source-zone TENANT03_CORE_Inside
  destination-zone TENANT03_CORE_Outside
  source-ip Test-Port1-*************/32
  destination-ip Test-Port2-************/32
 rule 138 name G3-V1.3-20200429-01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2OCS_**********/32
  destination-ip G3_*********/16
  service ssh
  service TCP_31306
  service TCP_3555
  service TCP_3558
 rule 139 name G3-V1.3-20200429-02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_*********/16
  service ssh
  service TCP_31306
  service TCP_3555
  service TCP_3558
  service TCP_8080
  service http
  service https
  service TCP_8000
  service TCP-3557
 rule 140 name G3-V1.3-20200429-03
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip G3_CORE_************-143
  destination-ip G3_CORE_F5_***********/32
  service TCP_28070
 rule 141 name G3-V1.3-20200429-04
  action pass
  counting enable
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BOCC&4A
  source-ip *********/16
  destination-ip RMXAS-VIP_**********33
  destination-ip G3_CORE__***********/32
  destination-ip G3_CORE_************/32
  destination-ip CAS_F5_***********
  destination-ip G3AMS-************-132
  service TCP-3601
  service TCP-9090
  service https
  service TCP-8022
  service TCP_9110
  service TCP_9100
  service TCP_9130
  service TCP_9120
  service TCP-9130
 rule 142 name G3-V1.3-20200429-05
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_*********/16
  destination-ip G2_NTP
  service ntp
  service UDP_123
 rule 143 name G3-V1.3-20200429-06
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********1
  source-ip **********2
  source-ip CAS_F5_***********
  destination-ip G2_DC_********/32
  service TCP_389
 rule 144 name G3-V1.3-20200429-07
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_4.190.80.71_73
  destination-ip G2_SBCDB_18.0.2.37/32
  service TCP-3556
 rule 145 name G3-V1.3-20200429-08
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3-CORE_RMXDB_***********1-113
  destination-ip G2_RMXDB_**********/32
  service TCP_3555
 rule 146 name G3-V1.3-20200429-09
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_RMX-Report_************/32
  source-ip "G3 RMX REPORT-**********21"
  destination-ip W5RFTP-**********
  service TCP-21
 rule 147 name G3-V1.3-20200429-10
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2MATSERVER
  destination-ip F5-AMS-***********0
  destination-ip G3AMS-TEMP-************
  service TCP-30001
 rule 148 name G3-V1.3-20200429-11
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_CORE_************/32
  source-ip V3_CORE_************/32
  destination-ip G2SBCDB-*********-37
  destination-ip G2SBCDB-**********-36
  service TCP_3555
 rule 149 name G3-V1.3-20200429-12
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip G2-RTQ
  service TCP_3555
 rule 150 name G3-V1.3-20200429-13
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip "G3 RMX REPORT-**********21"
  source-ip G3_RMX-Report_************/32
  destination-ip W5RFTP-**********
  destination-ip G2FTP-**********
  service TCP-21
 rule 151 name G3-V1.3-20200429-14
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip W5RFTP-**********
  destination-ip G2FTP-**********
  destination-ip CSLC_*******/32
  service ssh
 rule 152 name G3-V1.3-20200429-15
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2_AMS
  destination-ip Redis_feioltp01_4.190.80.31
  destination-ip Redis_feioltp02_4.190.80.32
  destination-ip Redis_feioltp03_4.190.80.33
  destination-ip Redis_feioltp04_4.190.80.34
  destination-ip Redis_feioltp05_4.190.80.35
  destination-ip Redis_feioltp06_4.190.80.36
  destination-ip Redis_feioltp07_4.190.80.37
  destination-ip Redis_feioltp08_4.190.80.38
  destination-ip Redis_feioltp09_***********
  service TCP_7001
 rule 153 name G3-V1.3-20200429-16
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_CORE_SDAS01_************/32
  source-ip G3_CORE_SDAS02_************/32
  destination-ip G2_*********/32
  destination-ip G2_18.0.1.75/32
  destination-ip G2_18.0.2.189/32
  service TCP-3555
  service TCP-31306
 rule 154 name To_Backup_Server-***********
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip Backup-Server_***********/32
  service https
  service http
 rule 155 name G3-V1.3-OPERVM-G2RTQ
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip G2-RTQ
  service TCP-3555
 rule 156 name G3CORE-to-G2ARMREDIS
  action pass
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********
  destination-ip *********-26
  service TCP_7001
 rule 157 name _to_

  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip CSLC_*******/32
  service ssh  
 rule 158 name RMXDB_TO_CSLC
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3-CORE_RMXDB_***********1-113
  source-ip V3_CORE_4.190.80.71_73
  destination-ip CSLC-**********
  service TCP-1521
 rule 159 name CSLC-baoleiji
  action pass
  logging enable
  counting enable
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip CSLC-baoleiji-**********
  source-ip CSLC-baoleiji-*********
  destination-ip *********/16
  service ssh
  service https
  service http
  service TCP_3389
  service TCP-8888
  service TCP-8889
  service TCP-8013
  service TCP-8090
  service TCP_8000
  service TCP_3555
  service TCP_3558
  service TCP_31306
  service TCP-5001-5030
  service TCP-8080
  service TCP-4555
  service TCP-30900
  service TCP_30902
  service TCP-8022
  service TCP-8018
 rule 160 name TO_G2FTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip *********/16
  destination-ip G2FTP-**********
  service ftp
  service ssh
 rule 161 name G3-V131
  action pass  
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BASDB-*********-75
  destination-ip DES-F5-***********
  service TCP-8018
 rule 162 name G3-V131-01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BASDB-*********-75
  destination-ip G3_CORE_F5_***********/32
  service TCP_28070
  service TCP_28081
 rule 163 name G3-V131-02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BASDB-*********-75
  destination-ip V3_CORE_TIDB_F5_***********
  service TCP-31306
 rule 164 name To-ƽ̨
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_CORE_**********/22
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip CSLC-DIP-***********
  service TCP-9092
  service TCP-2181
 rule 165 name V3-62
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_CORE_************/32
  source-ip V3_CORE_************/32
  destination-ip G3MATGW-F5-***********
  service TCP-8085
 rule 166 name G3OPERVM-TO-TRANSROUTE
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip G2TRANSROUTE-**********-84
  service ssh  
 rule 167 name IRM_170_IRM_G3
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip IRMDB-***********-133
  destination-ip G3-CORE_RMXDB_***********1-113
  destination-ip V3_CORE_4.190.80.71_73
  service TCP-3558
  service TCP-3555
 rule 168 name IRM_170_G3_IRM
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip IRMDB-***********-133
  service TCP-3555
 rule 169 name G3

  action pass
  counting enable
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip JianKong_***********
  destination-ip hermes-***********-16
  destination-ip **********-3
  service TCP-5000_5007
  service TCP_6370
 rule 170 name G3V140
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/22
  source-ip **********
  source-ip **********/24
  destination-ip G2_18.0.2.189/32
  destination-ip G2ARMDB-**********
  destination-ip G2ActiveMQ-*********1-213
  destination-ip G2_*********/32
  destination-ip G2_18.0.1.75/32
  service TCP-31306
  service TCP-3555
  service TCP-62738
 rule 171 name G3V140-1
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_**********/21
  source-ip V3_MS_***********/22
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip V3_CORE_**********/21
  service TCP-4100
  service TCP-6370
  service TCP-5000_5007
  service TCP_5008
 rule 172 name anquanlousao
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip FOC-*********
  source-ip FOC-***********
 rule 173 name R1401_20200724
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip G2_18.0.1.75/32
  service ssh
 rule 174 name G3_R1.4.0.2
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip Caiwuzhong_FTP-**********
  service TCP-34443
 rule 175 name Xshell
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********/24
  destination-ip T1_********/24
  service TCP-6000
 rule 176 name G2-BASDB_G3-BASDB
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BASDB-*********-75
  destination-ip **********/24
  service ssh
  service TCP-3555
 rule 177 name TO_CSLC_SJZT
  action pass
  logging enable
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_4.190.80.71
  source-ip V3_CORE_4.190.80.72
  source-ip G3-CORE_RMXDB_***********1-113
  destination-ip SJZT-**********
  service TCP-1521
 rule 178 name TO_G2_BASDB
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip G2_18.0.1.75/32
  destination-ip G2BISDB-*********
  service TCP-3555
 rule 179 name "G3_CORE_K8SNODE To CSLC_OPENAPI"
  action pass
  disable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_CORE_K8SNODE_**********/24
  destination-ip CSLC_OPENAPI_**********/32
  service TCP_7001
 rule 180 name G3RDC-G2RDC
  action pass
  logging enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3RDC-**********-2
  destination-ip ABSRDCB01-*********
  destination-ip CSLRDCA-********-2
  destination-ip CSLRDCB-*********-2
  destination-ip CSLRDCP-********-2
  destination-ip W5RRDC-*********-2
  destination-ip *********/16
 rule 181 name G2RDC-G3RDC
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ABSRDCB01-*********
  source-ip CSLRDCA-********-2
  source-ip CSLRDCB-*********-2
  source-ip CSLRDCP-********-2
  source-ip W5RRDC-*********-2
  source-ip *********/16
  destination-ip G3RDC-**********-2
 rule 182 name G3NTP-CSLCNTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3NTP-************-252
  destination-ip CSLCNTP-*******
  service ntp
 rule 183 name G3RDC_CSLCNTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3RDC-**********-2
  destination-ip CSLCNTP-*******
  service ntp
 rule 185 name To_G3_CORE_NTP
  action pass
  counting enable
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G3_*********/16
  source-ip G3_*********/16
  destination-ip G3NTP-************-252
  service ntp  
 rule 186 name G3_CAS_To_G2_AD
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_CORE_CAS_**********1-42
  source-ip G3_CORE_CAS_***********-52
  destination-ip G2_AD_18.0.10.200/32
  service TCP_389
 rule 187 name SDAS-V2.15.1_01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2BISRV-**********-42
  destination-ip BASDB-***********
  service TCP-3555
 rule 188 name SDAS-2.15.1_02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2BISRV-**********-42
  source-ip SBSG2GRSAS-***********-32
  destination-ip SDAS-BLIDB-**********04
  service TCP-31306
 rule 229 name SDAS-V2.15.1_02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2BISRV-**********-42
  destination-ip RTQDB-**********1-33
  service TCP-3555
 rule 231 name SDAS-2.15.1_03
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_CORE_SDAS01_************/32
  source-ip G3_CORE_SDAS02_************/32
  destination-ip CSLC-DIP-***********
  service TCP-2181
  service TCP-7070
 rule 189 name G2IRM_G3IRM
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip IRMDB-***********-133
  destination-ip G3IRMDBVIP-**********
  service ssh  
 rule 190 name SSM-Ansbile
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip Ansbile-************
  source-ip *************
  destination-ip G3_*********/16
  service ssh
 rule 191 name MHA
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip MHA-***********91
  source-ip Ansbile-************
  destination-ip G3_*********/16
  service TCP-31306
 rule 192 name DIP-IRMDB
  action pass
  counting enable
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip CSLC-DIP-***********
  source-ip **********-55
  source-ip **********-74
  destination-ip G3IRMDBVIP-**********
  service TCP-3555
 rule 193 name IRMDB_W5RFTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3IRMDB-**********-2
  destination-ip W5RFTP-*********
  destination-ip ShuJuJiChengPingTai-**********
  service ssh
 rule 194 name IRM1.15.0_01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2IRMAS-***********-62
  source-ip SBSG2IRMBMO-**********-32
  destination-ip G3IRMDBVIP-**********
  service TCP-3555
 rule 195 name IRM1.15.0_02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2IRMAS-***********-62
  destination-ip CORE-F5-***********/24
  service https
 rule 196 name OPSFTP
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip *********/16
  destination-ip SBSG2OPSFTP01-**********31
  service ssh
  service ftp
 rule 197 name IRM1.15.0_03
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********/24
  destination-ip JianKong_***********
  service TCP_3191
 rule 198 name IRM1.15.0_04
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip G3_*********/16
  service TCP-3555
  service TCP-3558
  service TCP-6370
  service TCP_3191
  service TCP_7001
  service TCP_2379
  service TCP_5003
  service ssh
  service http
  service UDP_8472
  service TCP-8022
 rule 200 name IRM1.15.0_05
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********/24
  destination-ip MonitorZK-***********31-135
  service TCP_3191
 rule 199 name ItoSchedule
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ItoSchedule-***********71-172
  destination-ip G3_*********/16
  service TCP-31306
  service TCP-3555
  service TCP-3557
  service TCP-3558
  service ssh
 rule 201 name G3-R160_01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2MATSERVER-***********-22
  destination-ip BMSDB-**********1-15
  destination-ip RTQDB-**********1-33
  service TCP-3555
 rule 202 name G3-R160_02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2MATSERVER-***********-22
  destination-ip G3AMS-************-102
  destination-ip AMS-CHANNEL-***********0
  service TCP-30001
  service TCP-30002
 rule 203 name G3-R160_03
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2WEBDC-***********-12
  destination-ip SBSG2IHS-**********41-142
  service TCP-31099
  service TCP-31100
  service TCP-31399
  service TCP-52704
  service TCP-35302
 rule 204 name G3-R160_04
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip SBSG2IHS-**********41-142
  destination-ip SBSG2WEBDC-***********-12
  service TCP-31399
  service TCP-31400
 rule 205 name G3-R160_05
  description BDE
  action pass  
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip BASDB-***********-43
  destination-ip W5ROPCC-*********02
  destination-ip *********/16
  service TCP-21
 rule 206 name G3-R160_06
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip SBSG2IHS-**********41-142
  destination-ip SBSG2GRSAS-***********-32
  service TCP-31399
  service TCP-31400
 rule 207 name G3-R160_07
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********/24
  destination-ip G3_*********/16
  destination-ip BOCC
  destination-ip *********/16
  service ssh  
  service ftp
 rule 208 name G3-R160_08
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2WEBDC-***********-12
  destination-ip IHSF5-************
  service TCP-31099
 rule 209 name G3-R160_09
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2GRSAS-***********-32
  destination-ip SDAS-BLIDB-**********04
  service TCP-31306
 rule 210 name G3-R160_10
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_**********/21
  source-ip G3-GW-**********/22
  destination-ip F5-AMS-***********0
  service TCP-30001
 rule 211 name SBSG2BMSDB_To_CSLC-Display-DB
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip SBSG2BMSDB-**********1-13
  destination-ip CSLC-Display-DB-*************
  service TCP-1521
 rule 212 name _To_CaiWu-FTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip CaiWu-FTP-**********/32
  service TCP-34443
 rule 213 name SBSG2BIBASDB_To_CSLC-financialFTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip BASDB-***********-43
  source-ip RTQDB-**********1-33
  destination-ip CSLC-financialFTP-**********-12
  service ftp
 rule 214 name RTQDB_To_UMP-FTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip RTQDB-**********1-33
  destination-ip CSLC-*******
  service TCP-34443
 rule 215 name BASDB_To_ShuJuJIChengPingTai
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip BASDB-***********-43
  destination-ip ShuJuJiChengPingTai-**********
  service TCP-22
 rule 216 name SBSG2_To_HiveDaShuJu
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip BASDB-***********-43
  source-ip SBSG2GRSDS01-***********
  destination-ip Hive-DaShuJu-*********
  service TCP-6100-6200
 rule 217 name SBSG2GRSDS01_To_ShuJuZhongTai
  action pass  
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip SBSG2GRSDS01-***********
  destination-ip ShuJuZhongTai-***********/24
  service TCP-16000
  service TCP-16010
  service TCP-16020
  service TCP-2181
  service TCP-7070
 rule 218 name SDASBISer
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip SDASBISer-**********11-112
  destination-ip ShuJuZhongTai-************-58
  destination-ip ShuJuZhongTai-************
  service TCP-7070
 rule 219 name SDASBISer_To_ShuJuZhongTai-2181
  action pass
  logging enable
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip SDASBISer-**********11-112
  destination-ip ShuJuZhongTai-************-58
  service TCP-2181
 rule 220 name RTQDB_To_ReXian-FTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip RTQDB-**********1-33
  destination-ip CSLC-*******
  service TCP-21
 rule 221 name G3-Core_To_CSLC-*******
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3-CORE-**********/22
  destination-ip CSLC_*******/32
  service ssh
 rule 222 name G3-CORE_To_CSLC-**********
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3-CORE-**********/22
  destination-ip CSLC-**********
  service TCP-1521
  service http
 rule 223 name ShuJuZhongTai_To_SBSGRSDBVIP
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ShuJuZhongTai-************/32
  source-ip **********-55
  destination-ip SDAS-BLIDB-**********04
  service TCP-31306
 rule 224 name ShuJuZhongTai_To_SBSG2IRMDBVIP
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ShuJuZhongTai-***********/24
  destination-ip G3IRMDBVIP-**********
  service TCP-3555
 rule 225 name CSLC-K8S_To_CAS
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip CSLC-K8S-********/24
  destination-ip CAS-**********/22
  service ssh
  service TCP-31306
  service TCP-18081
  service TCP-28080
 rule 226 name CSLC_To_CASGW-F5
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip CSLC-********/8
  source-ip CSLC-*******/8
  source-ip CSLC-K8S-********/24
  destination-ip CASGW-F5-************/32
  service TCP-28080
  service TCP-28081
 rule 227 name IRM1.15.0_06
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********/24
  destination-ip G2_AD_18.0.10.200/32
  service TCP-3268
  service TCP_389
 rule 228 name MAIL
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_*********/16
  destination-ip Mail-*********-2
  service smtp
 rule 230 name DBlinshichuanshu
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2SBCDB-**********-36
  source-ip **********-95
  source-ip *********-95
  source-ip G2SBCDB-*********-37
  destination-ip **********/24
  service ssh
 rule 232 name G3-GRSDS_TO_G2-RTQDB
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip SBSG2GRSDS01-***********
  destination-ip G2_*********/32
  service TCP-3555
 rule 233 name G3-MS-SDAS_To_G3-DMZ-SDAS
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3_CORE_SDAS01_************/32
  source-ip G3_CORE_SDAS02_************/32
  destination-ip G3-DMZ-SDAS-**********/32
  service TCP-8023-8024
 rule 234 name ECC01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BizInfo-*************-212
  destination-ip G3OPERVM01-**********-2
  service tcp-9044
 rule 235 name Solarwinds
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip Solarwinds-**********
  destination-ip G3_*********/16
  service ssh
 rule 236 name LinShirleyARMDB
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2ARMDB-**********
  destination-ip ARMDB-***********
  service ssh
 rule 237 name LinShiFTP
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2FTP-**********
  destination-ip FTP-**********
  service ssh
 rule 238 name LINSHI-RESID
  action pass
  counting enable
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G2REDIS-********/24
  destination-ip G3-CORE-**********/22
  service TCP_7001
 rule 239 name LINSHIRPT
  action pass  
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip **********
  destination-ip **********/24
  service TCP-22
 rule 240 name G3OPERVM_XWHFTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  source-ip **********/24
  destination-ip W5RFTP-**********
  service ssh
  service ftp
 rule 241 name XWHBOCC_FTP
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BOCC
  source-ip *********/16
  destination-ip SBSG2OPSFTP01-**********31
  service ftp
  service ssh  
 rule 242 name TO_OperatorVM
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip G3OPERVM01-**********-2
  service tcp-9044
 rule 243 name AMSRPT
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2MATSERVER-***********-22
  destination-ip AMSRPT-F5-***********6
  destination-ip BISRPT-F5-***********1
  service TCP_8080
 rule 244 name 

  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip T1_********/24
  destination-ip G3RDC-**********-2
  action pass  
---- More ----
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3RDC-**********-2
  destination-ip T1_********/24
 rule 246 name CAS-TO-RDC
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G3_MS_***********1-24
  source-ip G3_MS_*************-234
  destination-ip RDC-F5-***********3
  service TCP-3268
  service TCP_389
 rule 247 name SCAN
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip FOC-***********-137
  destination-ip G3RDC-**********-2
 rule 248 name TO-SFTP
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip *********/16
  destination-ip SFTP-************
  service ssh
 rule 249 name G3_163_01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip BISMONITORCOLLECT-F5
  service TCP-26100
 rule 250 name G3_JianCe2.0_01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip G3_CORE_K8SNODE_**********/24
  service TCP-26100
 rule 251 name CSLOPCC
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip CSLCOPCC-*********/24
  destination-ip ************
  destination-ip ************
  service https
 rule 252 name linshtest
  description 20210621

  action pass
  disable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3-CORE-**********/22
  destination-ip V3_MS_***********/22
  service http
 rule 253 name ***********
  action pass
  source-zone TENANT_PUBLIC_Outside
  destination-zone TENANT_PUBLIC_Inside
  source-ip ***********
  destination-ip NAS-**********/21
 rule 254 name SDAS-1.18.0
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip CSLC-baoleiji-**********
  destination-ip G3_CORE_************/32
  destination-ip G3_CORE_SDAS01_************/32
  destination-ip G3_CORE_SDAS02_************/32
  service TCP-8022
 rule 255 name XWHBOCC_TO_NTP
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip *********/16
  source-ip 4.176.11-13.0/24
  destination-ip G3NTP-************-252
  service ntp
 rule 256 name YZECC_TO_RDC
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip YZ-ECC-*********
  destination-ip G3RDC-**********-2
 rule 257 name RDC_TO_YZECC
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3RDC-**********-2
  destination-ip YZ-ECC-*********
 rule 258 name MS_K8SNODE_TO_NAS
  action pass
  source-zone TENANT_PUBLIC_Outside
  destination-zone TENANT_PUBLIC_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip NAS_**********_PUBLIC_OUTSIDE
 rule 259 name G3RMX-R130-01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2IRMAS-***********-62
  destination-ip G3ARESRISK
  service TCP-4100
 rule 260 name G3RMX-R130-02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2IRMAS-***********-62
  destination-ip V3_CORE_**********/21
  service TCP-5001-5030
  service TCP-6370
 rule 266 name YZBOCC-ANY
  action pass  
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip YZBOCC
  destination-ip G3RDC-**********-2
  destination-ip RDC-F5-***********3
 rule 267 name ANY-YZBOCC
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3RDC-**********-2
  source-ip RDC-F5-***********3
  destination-ip YZBOCC
 rule 270 name Radius
  action pass
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3-CORE-**********/22
  source-ip CAS-**********/22
  destination-ip Radius-**********
  service UDP-1812
 rule 271 name RMX131
  action pass  
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip BOCC&4A
  destination-ip RMXAS-VIP_**********33
  service TCP_9120
 rule 272 name R240-01
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G3TSPAPP01-***********01
  source-ip G3TSPAPP02-***********02
  destination-ip V3_CORE_**********/21
  service TCP_7001
  service TCP_6370
  service TCP-5000_5007
  service TCP_31306
  service TCP_3558
  service TCP_3555
  service TCP_3191
 rule 273 name R240-02
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip G3TSPAPP01-***********01
  source-ip G3TSPAPP02-***********02
  destination-ip V3_CORE_TIDB_F5_***********
  destination-ip V3_CORE_TIDB_F5_***********
  service TCP_31306
 rule 274 name R240-03
  action pass
  source-zone TENANT_PUBLIC_Outside
  destination-zone TENANT_PUBLIC_Inside
  source-ip V3_MS_***********/22
  destination-ip NAS-**********/32
 rule 278 name OPERVM-TO-ItoSchedule
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip ItoSchedule-***********71-172
  service https
 rule 279 name *************-*********
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_*************/32
  destination-ip *********
  service TCP-31306
 rule 281 name K8s-To-Hermes-sharding
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_GW_K8SNODE_**********/24
  destination-ip Hermes-************-216
  destination-ip ***********8-220
  service TCP-5001-5030
 rule 282 name MHA-To-G3BOSDB
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip MHA-***********91
  destination-ip G3BOSDB-************-104
  service TCP-31306
  service ssh
 rule 283 name **********-To-************
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip ************
  service TCP_8082
 rule 284 name **********-To-*************
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip *************
  service TCP_30200
  service TCP_30201
 rule 285 name ***********-**********
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ************-14
  destination-ip ************-103
  service TCP-31306
 rule 291 name IRM-1.27.0
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SBSG2IRMAS-***********-62
  destination-ip V3_CORE_**********/21
  service TCP_7001
 rule 290 name CORE-K8S-TO-NAS
  action pass
  source-zone TENANT_PUBLIC_Outside
  destination-zone TENANT_PUBLIC_Inside
  source-ip G3_CORE_K8SNODE_**********/24
  destination-ip NAS_**********
 rule 292 name VDI-TO-debugtool
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip Test-Baoleiji
  destination-ip G3OPERVM01-**********-2
  service TCP-30900
 rule 293 name Hermes-TO-Core
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ************-14
  destination-ip ***********8-220
  service TCP-5000-5011
 rule 294 name bos-oddsdata-TO-G3BOSRedis
  action pass  
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip ***********-59
  service TCP_7001
 rule 295 name G3BISMONTORCOLLECT_R340
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3BISMONTORCOLLECT
  destination-ip *********/16
  service TCP-31306
  service TCP-3558
  service TCP_3555
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP_2379
  service TCP-10251-10252
  service TCP_8080
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service TCP-22
  service http
  service TCP_7100
  service https
 rule 296 name VulnerabilityScan_Network
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip VulnerabilityScan-************
  destination-ip *********/16
 rule 298 name G3OPERVM01_************
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3OPERVM01-**********-2
  destination-ip ************
  service TCP_8082
 rule 297 name K8SNODE_to_*********/16
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip V3_MS_K8SNODE_***********/24
  destination-ip *********/16
  service TCP_31306
  service TCP_3558
  service TCP_3555
  service TCP_3191
  service TCP_7001
  service TCP_6370
  service TCP_2379
  service TCP-10251-10252
  service https
  service http
  service TCP-4100-4130
  service TCP-5000-5030
  service TCP_9100
  service TCP_8080
  service ssh
  service TCP_7100
 rule 300 name *********_************
  action pass
  source-zone TENANT_PUBLIC_Inside
  destination-zone TENANT_PUBLIC_Outside
  source-ip *********/16
  destination-ip ************
  service UDP_161
  service UDP_162
  service UDP_514
 rule 299 name flink_**********
  action pass
  source-zone TENANT_PUBLIC_Outside
  destination-zone TENANT_PUBLIC_Inside
  source-ip flink-***********11-119
  source-ip flink-***********24-126
  destination-ip **********
  destination-ip nfs-**********
 rule 301 name PLSQL-lianjiegongju
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip PLSQL-gongju
  destination-ip **********5
  service TCP_3557
 rule 302 name TO-NFS
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  destination-ip NFS-*********
 rule 303 name TO-523
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip **********
  destination-ip SDAS-BLIDB-**********04
  destination-ip BASDB-***********
  service TCP-31306
  service TCP-3555
 rule 304 name TC-************-203
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SG_10.196.128_129.0/24
  destination-ip Hermes-mata-************-203
  service TCP-6370
 rule 305 name TC-************-216
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip SG_10.196.128_129.0/24
  destination-ip Hermes-************-216
  service TCP-5000-5011
 rule 306 name *********_************
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip *********/16
  destination-ip ************
  service TCP-30002
 rule 307 name TO_EDRagent
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip ************-103
  source-ip CAS-**********/22
  source-ip G3NTP-************-252
  destination-ip-host ************ 
  service-port tcp destination eq 8443
  service-port tcp destination eq 6677
  service-port tcp destination eq 7788
  service-port tcp destination eq 8001
  service-port tcp destination eq 8002
  service-port tcp destination eq 80
 rule 308 name K8S-node_Meta
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip K8S-node
  destination-ip Meta
  service TCP-6370
 rule 309 name K8S-node_CWZX
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip K8S-node
  destination-ip CWZX
  service TCP-30021
 rule 310 name DNS_YZGTM
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_DNS_***********
  source-ip V3_DNS_***********
  destination-ip YZGTM
  service dns-tcp
  service dns-udp
 rule 311 name XIUSHI
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip ***********-91
  destination-ip **********-4
  destination-ip **********/24
  service ssh
 rule 312 name XIUSHI2
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********-2
  destination-ip ***********-14
  destination-ip ***********-12
  service ssh
 rule 313 name XIUSHI3
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip ***********-91
  destination-ip ************-72
  destination-ip ***********2
  service TCP_29093
 rule 314 name XIUSHI4
  action pass  
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip OPS
  destination-ip ***********-91
  service ssh
 rule 315 name XIUSHI5
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip ***********-91
  destination-ip ************
  destination-ip *************
  service TCP-31306
 rule 316 name XIUSHI6
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ***********-14
  destination-ip ***********-91
  service TCP-30020
 rule 318 name AMoffline
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip *********/16
  destination-ip ************
  destination-ip ************
  destination-ip *************
  service TCP_8001
  service TCP_5001
 rule 317 name IRM_KYT
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip **********
  destination-ip ************
  service TCP_8088
 rule 319 name dc-to-socSyslog
  action pass
  logging enable
  counting enable
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip G3RDC-**********-2
  destination-ip SOC-*************-112
  service TCP_8400
  service UDP_514
 rule 320 name K8SNODE-to-DUANXIN
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/22
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip ************
  service http
 rule 321 name K8SNODE-to-URSF5-30514
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_**********/22
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_**********/24
  destination-ip ************
  service TCP_30514
 rule 46 name deny
#              
security-policy ipv6
#
return

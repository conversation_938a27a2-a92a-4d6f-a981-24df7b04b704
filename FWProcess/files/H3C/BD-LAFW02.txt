<BD-LAFW02>dis cu 
#
 version 7.1.064, Release 9616P17
#
 sysname BD-LAFW02
#
context Admin id 1
#
ip vpn-instance management
 route-distinguisher 1000000000:1
 vpn-target 1000000000:1 import-extcommunity
 vpn-target 1000000000:1 export-extcommunity
#
 irf mac-address persistent timer
 irf auto-update enable
 undo irf link-delay
 irf member 1 priority 1
#
 security-zone intra-zone default permit
#
 nat static inbound ************ ***********
 nat static outbound ******** ********** disable counting
 nat static outbound ********** ********** disable counting
 nat static outbound ********* ********** disable counting
 nat static outbound ********** ********** disable counting
 nat static outbound ********** ********** disable counting
 nat static outbound ********** ********** disable counting
 nat static outbound ********** ********** disable counting
 nat static outbound ********** **********0 disable counting
 nat static outbound ********** **********1 disable counting
 nat static outbound ********** ********** disable counting
 nat static outbound *********** ********** disable counting
 nat static outbound ************ ************
 nat static outbound ************ ************
 nat static outbound ************ ************
 nat static outbound ************ **********
 nat static outbound ************ **********
 nat static outbound ************ **********
 nat static outbound ************ **********
 nat static outbound ************ **********
#
 lldp global enable
#
 password-recovery enable
#
vlan 1
#
vlan 66        
#
vlan 200
#
object-group ip address ************
 0 network host address ************
#
object-group ip address 10.209.2-9
 security-zone yuny_vdi
 0 network subnet ********** *************
 10 network subnet ********** *************
 20 network subnet ********** *************
 30 network subnet ********** *************
#
object-group ip address **********
 security-zone yuny_vdi
 0 network host address **********
#
object-group ip address **********/16
 0 network subnet ********** ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address **********/16
 0 network subnet ********** ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address **********
 description **********
 0 network host address **********
#
object-group ip address ************/32
 0 network host address ************
#
object-group ip address **********
 security-zone Trust
 0 network subnet ********** *************
#
object-group ip address ***********-15
 0 network range *********** ***********
#
object-group ip address **********
 description **********
 0 network subnet ********** *************
#
object-group ip address **********
 0 network subnet ********** *************
#
object-group ip address ************
 security-zone Trust
 0 network host address ************
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address **********/24
 0 network subnet ********** *************
#
object-group ip address **********
 0 network subnet ********** ***********
#
object-group ip address **********/24
 0 network subnet ********** *************
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ************
 0 network host address ************
#
object-group ip address 10.216.40.0
 0 network subnet 10.216.40.0 *************
#
object-group ip address ************
 description ************
 0 network host address ************
#
object-group ip address ************
 security-zone Trust
 0 network host address ************
#
object-group ip address 10.216.41.0/24
 security-zone Trust
 0 network subnet 10.216.41.0 *************
#
object-group ip address 10.216.44.0/24
 security-zone Trust
 0 network subnet 10.216.44.0 *************
#
object-group ip address 10.216.48.0/24
 0 network subnet 10.216.48.0 *************
#
object-group ip address 10.216.5.0
 0 network subnet 10.216.5.0 *************
#
object-group ip address ***********
 security-zone Trust
 0 network subnet *********** *************
#
object-group ip address **********
 security-zone Trust
 0 network subnet ********** *************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address 10.217.129.38
 0 network host address 10.217.129.38
#
object-group ip address 10.217.130-137/139-140.0/24
 security-zone Trust
 0 network range ************ 10.217.137.254
 10 network range 10.217.139.1 **************
#              
object-group ip address 10.217.130-139.0/24
 security-zone Trust
 0 network subnet ************ *************
 10 network subnet ************ *************
 20 network subnet 10.217.132.0 *************
 30 network subnet 10.217.133.0 *************
 40 network subnet 10.217.134.0 *************
 50 network subnet 10.217.135.0 *************
 60 network subnet 10.217.136.0 *************
 70 network subnet 10.217.137.0 *************
 80 network subnet 10.217.138.0 *************
 90 network subnet 10.217.139.0 *************
#
object-group ip address ************
 security-zone Trust
 0 network subnet ************ *************
#
object-group ip address ************-**************
 security-zone Trust
 0 network range ************ **************
#
object-group ip address 10.217.131-137.0
 security-zone Trust
 0 network subnet ************ *************
 10 network subnet 10.217.132.0 *************
 20 network subnet 10.217.133.0 *************
 30 network subnet 10.217.134.0 *************
 40 network subnet 10.217.135.0 *************
 50 network subnet 10.217.136.0 *************
 60 network subnet 10.217.137.0 *************
#
object-group ip address ************
 security-zone Trust
 0 network subnet ************ *************
#
object-group ip address **********
 security-zone Trust
 0 network host address **********
#
object-group ip address **********
 security-zone Trust
 0 network host address **********
#
object-group ip address ************
 security-zone Trust
 0 network subnet ************ *************
#
object-group ip address ************/24
 0 network subnet ************ *************
#
object-group ip address ************-3
 security-zone Trust
 0 network range ************ ************
#
object-group ip address *************
 security-zone Trust
 0 network host address *************
#
object-group ip address 10.218.45-46
 security-zone Trust
 0 network subnet 10.218.45.0 *************
 10 network subnet 10.218.46.0 *************
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address **********-2
 0 network host address **********
 10 network host address 10.222.0.2
#
object-group ip address ************
 security-zone Untrust
 0 network subnet ************ *************
#
object-group ip address 10.248.121.200-220
 0 network range 10.248.121.200 10.248.121.220
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address *************、9、38
 0 network host address *************
 10 network host address 10.248.133.9
 20 network host address 10.248.133.38
#
object-group ip address ************/24
 description 路宇需求20230831
 0 network subnet ************ *************
#
object-group ip address ************
 0 network subnet ************ *************
#              
object-group ip address ************
 security-zone Untrust
 0 network subnet ************ *************
#
object-group ip address ************/24
 description BT
 security-zone Untrust
 0 network subnet ************ *************
#
object-group ip address **************
 security-zone Untrust
 0 network host address **************
 10 network host address 192.168.32.57
#
object-group ip address 10.88.128.65
 0 network host address 10.88.128.65
#
object-group ip address *************-105
 security-zone Trust
 0 network range ************* 104.11.11.105
#
object-group ip address 104.12.1.100/32
 0 network host address 104.12.1.100
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address **********
 security-zone Trust
 0 network host address **********
#
object-group ip address **********
 security-zone Trust
 0 network host address **********
#
object-group ip address **********
 0 network host address **********
#              
object-group ip address ***********
 0 network subnet *********** *************
#
object-group ip address *************
 security-zone Trust
 0 network host address *************
#
object-group ip address *************/32
 0 network host address *************
#
object-group ip address *************
 security-zone Trust
 0 network host address *************
#
object-group ip address *************
 security-zone Trust
 0 network host address *************
#
object-group ip address *************、221
 description *************、221
 0 network host address *************
 10 network host address 104.12.10.221
#              
object-group ip address ************
 security-zone Trust
 0 network host address ************
#
object-group ip address *************/32
 0 network host address *************
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address *************/24
 0 network subnet ************* *************
#
object-group ip address 104.126.246-248.0
 security-zone Trust
 0 network subnet 104.126.246.0 *************
 10 network subnet 104.126.247.0 *************
 20 network subnet ************* *************
#
object-group ip address **********
 security-zone Trust
 0 network host address **********
#
object-group ip address **********50
 security-zone Trust
 0 network host address **********50
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ***************
 0 network host address ***************
#
object-group ip address *************
 0 network subnet ************* *************
#
object-group ip address *************
 description *************
 0 network host address *************
#              
object-group ip address *************00
 0 network host address *************00
#
object-group ip address ***************
 0 network host address ***************
#
object-group ip address ***************
 description ***************
 0 network host address ***************
#
object-group ip address **************
 security-zone Trust
 0 network host address **************
#
object-group ip address **************
 0 network host address **************
#
object-group ip address ***************
 security-zone Trust
 0 network host address ***************
#
object-group ip address ***************
 security-zone Trust
 0 network host address ***************
#
object-group ip address ***************
 0 network host address ***************
#
object-group ip address **************
 security-zone Trust
 0 network host address **************
#
object-group ip address *************
 0 network subnet ************* *************
#
object-group ip address ***************
 0 network host address ***************
#
object-group ip address **********/16
 0 network subnet ********** ***********
#
object-group ip address ***********
 security-zone Trust
 0 network host address ***********
#
object-group ip address ************
 security-zone Trust
 0 network host address ************
#
object-group ip address ***********
 0 network subnet *********** *************
#
object-group ip address ***********
 0 network subnet *********** *************
#
object-group ip address ***********/24
 0 network subnet *********** *************
#
object-group ip address ***********
 0 network subnet *********** *************
#
object-group ip address ************-33
 security-zone Trust
 0 network range ************ ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address **********
 0 network subnet ********** *************
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address *************
 0 network host address *************
#
object-group ip address ************
 description ************
 0 network host address ************
#
object-group ip address ***********
 0 network subnet *********** *************
#
object-group ip address **************/56
 security-zone Trust
 0 network host address **************
 10 network host address **************
#
object-group ip address *************/24
 security-zone Trust
 0 network subnet ************* *************
#
object-group ip address **********/32
 0 network host address **********
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address *************-122
 security-zone Untrust
 0 network range ************* *************
#
object-group ip address ***************-102
 security-zone Untrust
 0 network range *************** 172.100.101.102
 10 network host address 172.100.49.101
#
object-group ip address ***************-112
 security-zone Untrust
 0 network range *************** 172.100.101.112
#
object-group ip address ***************
 security-zone Untrust
 0 network host address ***************
#
object-group ip address *************-***************
 security-zone Untrust
 0 network range ************* ***************
#
object-group ip address 172.100.156.0
 security-zone Untrust
 0 network subnet 172.100.156.0 *************
#
object-group ip address ***************-202
 security-zone Untrust
 0 network range *************** 172.100.201.202
#
object-group ip address ***************
 0 network host address ***************
#
object-group ip address ***************-202
 security-zone Untrust
 0 network range *************** 172.100.202.202
#
object-group ip address ***************-202
 security-zone Untrust
 0 network range *************** 172.100.207.202
#
object-group ip address 172.100/200.0.0
 security-zone Untrust
 0 network subnet *********** ***********
 10 network subnet *********** ***********
#
object-group ip address **********/32
 0 network host address **********
#
object-group ip address **********2/32
 0 network host address **********2
#
object-group ip address **********2
 security-zone Untrust
 0 network host address **********2
#
object-group ip address **********2/32
 0 network host address **********2
#
object-group ip address 172.16.20.6
 0 network host address 172.16.20.6
#
object-group ip address 172.16.23.1
 0 network host address 172.16.23.1
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address ************-55
 security-zone Untrust
 0 network range ************ 172.16.30.55
#              
object-group ip address 172.20.13.167/32
 0 network host address 172.20.13.167
#
object-group ip address *************
 security-zone Untrust
 0 network host address *************
#
object-group ip address *************/32
 0 network host address *************
#
object-group ip address ***********
 security-zone Untrust
 0 network subnet *********** *************
#
object-group ip address 172.20.17.88
 0 network host address 172.20.17.88
#
object-group ip address ***********/24
 0 network subnet *********** *************
#
object-group ip address ***********/24
 0 network subnet *********** *************
#              
object-group ip address 172.20.29.22/32
 0 network host address 172.20.29.22
#
object-group ip address 172.20.29.45/32
 0 network host address 172.20.29.45
#
object-group ip address 172.20.29.84/32
 0 network host address 172.20.29.84
#
object-group ip address 172.20.3.152/32
 0 network host address 172.20.3.152
#
object-group ip address 172.200.10.1
 security-zone Untrust
 0 network host address 172.200.10.1
#
object-group ip address ***********
 security-zone Untrust
 0 network subnet *********** *************
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address *************
 security-zone Untrust
 0 network host address *************
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address **********
 security-zone Untrust
 0 network subnet ********** ***********
#
object-group ip address ***********/24
 0 network subnet *********** *************
#
object-group ip address ***********
 security-zone Untrust
 0 network subnet *********** *************
#
object-group ip address ************
 0 network host address ************
#              
object-group ip address 172.25.5.1-2
 security-zone Untrust
 0 network range 172.25.5.1 172.25.5.2
#
object-group ip address ************
 description ************
 0 network subnet ************ *************
#
object-group ip address *************
 security-zone Untrust
 0 network host address *************
#
object-group ip address **************
 0 network range ************** **************
 10 network host address ************
 20 network host address ************
#
object-group ip address **************/32
 0 network host address **************
#
object-group ip address ***********
 0 network subnet *********** *************
#              
object-group ip address 172.26.18.10/32
 0 network host address 172.26.18.10
#
object-group ip address *************/32
 0 network host address *************
#
object-group ip address 172.31.10.0
 security-zone Untrust
 0 network subnet 172.31.10.0 *************
#
object-group ip address ************-12
 security-zone Trust
 0 network range ************ ************
#
object-group ip address **********
 0 network subnet ********** *************
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address ************
 security-zone Untrust
 0 network host address ************
#
object-group ip address ***********
 security-zone Untrust
 0 network host address ***********
#
object-group ip address 18.6.203.50
 security-zone Trust
 0 network host address 18.6.203.50
#
object-group ip address **********
 security-zone Trust
 0 network host address **********
#
object-group ip address 192.168
 0 network subnet *********** ***********
 10 network host address 10.10.2.249
 20 network host address 10.10.9.82
#
object-group ip address ***********
 0 network subnet *********** *************
#
object-group ip address *************
 0 network subnet ************* *************
#
object-group ip address ***************
 security-zone Untrust
 0 network host address ***************
#
object-group ip address ***************-174
 security-zone Untrust
 0 network range *************** ***************
#
object-group ip address ***************
 security-zone Untrust
 0 network host address ***************
#
object-group ip address **************
 security-zone Untrust
 0 network host address **************
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address *************
 0 network subnet ************* *************
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address *************
 0 network host address *************
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address ************
 0 network subnet ************ *************
#
object-group ip address ***********/24
 0 network subnet *********** *************
#
object-group ip address ************-57
 0 network range ************ ************
#
object-group ip address ************-66
 security-zone Trust
 0 network range ************ ************
#
object-group ip address 20230726目标地址01
 0 network host address ************
 10 network host address ************
 20 network host address ***********
 30 network host address ***********0
 40 network host address ***********
 50 network host address ***********
 60 network host address ***********
#
object-group ip address 20230726孙磊需求目标地址
 0 network host address **************
 10 network host address *************00
 20 network range *********** ***********
 30 network host address **********
 40 network host address **************
 50 network host address ***************
 60 network range ************ ************
 70 network host address ************
 100 network range ************ ************
 110 network host address *************73
 120 network host address *************72
 130 network host address ************
 140 network host address **********
 150 network host address ***********
 160 network host address **************
 170 network host address **************
 180 network host address *************
 190 network host address **************
 200 network host address *************
 210 network host address *************
 220 network host address ************
 230 network host address *************
#
object-group ip address 20231204王玉玲需求源地址
 0 network host address ***********
 10 network host address **********4
 20 network host address **********7
 30 network host address ***********
 40 network host address ***********
 50 network host address ***********
 60 network host address ***********
 70 network host address **********
 80 network host address **********
 90 network host address ***********
 100 network host address ***********
 110 network host address **********
 120 network host address **********
 130 network host address **********
 140 network host address ***********
 150 network host address ***********
 160 network host address ***********
 170 network host address ***********
 180 network host address ***********
 190 network host address ***********
 200 network host address ***********
 210 network host address ************
 220 network host address ************
 230 network host address *************
 240 network host address *************
 250 network host address *************
#
object-group ip address ***********
 security-zone Untrust
 0 network host address ***********
#
object-group ip address ***********-172
 security-zone Untrust
 0 network range *********** ***********
#
object-group ip address ************
 0 network host address ************
#
object-group ip address **********/22
 0 network subnet ********** *************
#              
object-group ip address **********/22
 0 network subnet ********** *************
#
object-group ip address ************-104
 0 network range ************ 4.190.88.104
#
object-group ip address ************-36
 0 network range ************ 4.255.205.36
#
object-group ip address ************
 0 network host address ************
#
object-group ip address ***********
 0 network host address ***********
#
object-group ip address bmms项目
 security-zone Trust
 0 network host address 10.216.40.2
 10 network host address 104.200.101.70
#
object-group ip address BT终端IP
 security-zone Untrust
 0 network host address 172.20.28.214
 10 network host address 172.20.28.215
#
object-group ip address BT终端访问开发业务目的地址
 0 network host address **************
 10 network host address *************00
 20 network host address **************
 30 network host address ***************
 40 network host address *************68
 50 network host address **********
 60 network host address ************
 70 network host address ***********
 80 network host address ***********
 90 network host address ***********
 100 network host address 104.21.1.202
 110 network host address ************
 120 network host address ************
 130 network host address ************
 140 network host address 104.21.18.82
#
object-group ip address "G3 VDI&104"
 security-zone Trust
 0 network subnet 104.12.12.0 *************
 10 network host address 10.211.3.154
 20 network host address 10.211.3.182
 30 network host address 10.211.4.110
#
object-group ip address G3172.20
 security-zone Untrust
 0 network subnet 172.20.8.0 *************
 10 network subnet 172.20.13.0 *************
 20 network subnet 172.16.0.0 *************
#
object-group ip address G32环境数据中台网络
 0 network subnet 10.216.41.0 *************
 10 network subnet 10.216.40.0 *************
 20 network subnet 10.216.42.0 *************
 30 network subnet 10.216.43.0 *************
 40 network subnet 10.216.44.0 *************
 50 network subnet 10.216.45.0 *************
#
object-group ip address G32环境数据中台网络目的地址
 0 network range 10.220.7.6 10.220.7.10
#
object-group ip address lousao
 0 network subnet 4.10.1.0 *************
 5 network subnet 4.10.2.0 *************
 10 network subnet 4.10.3.0 *************
 15 network subnet 4.255.239.0 *************
#
object-group ip address RMOAS
 security-zone Trust
 0 network range *************** 172.100.101.102
 10 network range *************** 172.100.101.112
 20 network host name ***************
#
object-group ip address SBSG2GRSDS01-***********
#
object-group ip address V3_CORE_**********/22
 0 network subnet ********** *************
#
object-group ip address V3_CORE_K8SNODE_**********/24
 0 network subnet ********** *************
#
object-group ip address V3_CORE_K8SNODE_4.190.85.0/24
 0 network subnet 4.190.85.0 *************
#
object-group ip address V3_CORE_K8SNODE_4.190.86.0/24
 0 network subnet 4.190.86.0 *************
#              
object-group ip address viminal工单
 security-zone Trust
 0 network host address 104.12.1.44
#
object-group ip address YW-Print
 security-zone yuny_vdi
 0 network range 10.209.2.241 10.209.2.244
 10 network range 10.209.9.241 10.209.9.246
#
object-group ip address yyvdi-10.209
 security-zone yuny_vdi
 0 network subnet ********** 255.255.224.0
#
object-group ip address YZYUNYINGOSBOCCC
 security-zone Untrust
 0 network host address 192.168.6.85
 10 network host address 192.168.6.87
#
object-group ip address 白名单
 security-zone Untrust
 0 network host address 10.248.130.23
 10 network host address 10.248.130.122
 20 network host address 172.20.17.247
 30 network host address 172.20.22.251
 40 network host address 172.20.16.251
 50 network host address 172.20.3.250
 60 network host address 172.20.3.251
 70 network host address 10.248.130.52
 80 network host address 172.21.11.118
 90 network host address 10.248.130.55
 100 network host address 192.168.6.85
 110 network host address 192.168.6.87
#
object-group ip address 百环二中心实验室虚拟机
 0 network host address *************
 10 network host address 104.21.50.171
 20 network host address ***********
#
object-group ip address 办公网访问NAS地址
 security-zone Untrust
 0 network range 172.20.18.11 172.20.18.16
#
object-group ip address 办公网访问协调考勤主机
 security-zone Untrust
 0 network subnet *********** *************
 10 network subnet 172.20.3.0 *************
 20 network subnet ************* *************
 30 network host address ***********1
 40 network host address 192.168.63.133
 50 network host address 10.248.130.123
 60 network host address 192.168.254.1
#
object-group ip address 办公网蜜罐
 security-zone Untrust
 0 network host address 192.168.252.110
 10 network host address 192.168.210.251
 20 network host address 172.25.2.228
 30 network host address 10.248.255.105
#
object-group ip address 北单测试网络
 security-zone Untrust
 0 network subnet ********** *************
#
object-group ip address 北京彩票销售管理系统接入ip
 0 network subnet ********** *************
#
object-group ip address 北京彩票销售管理系统数据同步
 0 network subnet ********** *************
#              
object-group ip address 彩银测试服务器地址
 security-zone Trust
 0 network host address ***********
 10 network host address ***********
#
object-group ip address 测试
 description **************、60、51、100、160、68、69、67、173、172、************
 security-zone Trust
 0 network range 104.200.100.67 **************
 10 network host address **************
 20 network host address **************
 30 network host address **************
 40 network host address *************00
 50 network host address ***************
 60 network subnet *************72 ***************
 70 network host address ************
#
object-group ip address 测试NAS地址
 security-zone Trust
 0 network range *************** ***************
#
object-group ip address 测试环境操作终端业务
 security-zone Trust
 0 network host address **************
 10 network host address *************72
 20 network host address *************73
#
object-group ip address 测试环境管理（源地址）
 110 network host address 172.20.17.138
 120 network host address 172.20.50.188
 130 network host address 172.20.17.10
 140 network host address ***********1
 150 network host address 172.21.11.179
 170 network host address 172.20.17.13
 180 network host address 172.21.12.150
 190 network host address 172.20.16.5
#
object-group ip address 带外地址
 security-zone Untrust
 0 network host address 172.100.199.254
 10 network subnet 172.200.10.0 *************
 20 network host address ************
 30 network host address ************
#
object-group ip address 访问coding-devops
 0 network host address **********
#
object-group ip address 访问csl-svn
 0 network host address **********
#
object-group ip address 访问CSLO邮件服务器
 0 network host address **********
 10 network host address **********
#
object-group ip address 访问sementepro
 0 network host address **********
#
object-group ip address 访问UATtest
 0 network host address ***********
#
object-group ip address 访问ump效验
 0 network host address ************
#
object-group ip address 访问办公网邮箱服务
 0 network host address ***********
#
object-group ip address 访问国二环境（2台虚机机)（源）
 0 network host address *************
#              
object-group ip address 访问国二环境（2台虚机机目的）
 0 network host address **********
 10 network host address **********
#
object-group ip address 访问国家实验室
 0 network host address ***********
#
object-group ip address 访问扫码终端二中心IDC
 0 network host address ***********
 10 network host address ************
 20 network host address ************
 30 network host address ***********
 40 network host address **************
 50 network subnet ********** *************
 60 network host address **************
#
object-group ip address 国1骏彩跳板机
 0 network range **********13 **********14
#
object-group ip address 胡刚需求20220822
 0 network host address ***********
#
object-group ip address 湖北农信目的地址
 0 network host address ***********
 10 network host address ***********
#
object-group ip address 湖北农信源地址
 0 network subnet ********** *************
#
object-group ip address 湖南专线
 0 network host address ************
#
object-group ip address 湖南专线访问实验室
 0 network host address ************
#
object-group ip address "互联网侧USAP 源地址"
 security-zone Untrust
 0 network host address ***************
 10 network host address ***************
 20 network host address ***************
 30 network host address ************
 40 network host address **************
#
object-group ip address 互联网访问实验室设备
 0 network host address *************
 10 network host address ************
 20 network host address ************
 30 network host address ***********
 40 network host address *************
 50 network host address *************
 60 network host address *************68
 70 network host address ************
 80 network host address ***********
 90 network host address ************
 100 network host address ***********
 110 network host address ***********
 120 network host address ************
 130 network host address ***********
 140 network host address *************00
 150 network host address ************
 160 network host address **************
 170 network host address ***********
 180 network host address ***********
 190 network host address ************
 200 network host address ***********
 210 network host address *************
 220 network host address ************
 230 network host address *************
#              
object-group ip address 互联网业务访问网络
 security-zone Untrust
 0 network host address *************
 10 network host address ************
 20 network host address **************
 30 network host address *************
 40 network host address ***************
 50 network host address **************
 60 network host address ***************
#
object-group ip address 建行
 security-zone Untrust
 0 network host address *************
#
object-group ip address 竞猜开发测试
 security-zone Untrust
 0 network subnet ********* *************
 10 network subnet ********* *************
#
object-group ip address 骏彩办公网
 security-zone Untrust
 0 network subnet ************ *************
#              
object-group ip address 骏彩办公网DNS
 security-zone Untrust
 0 network range ************* *************
#
object-group ip address 骏彩办公网MAIL服务器
 security-zone Untrust
 0 network host address **************
#
object-group ip address 骏彩办公网（192.168段）
 security-zone Untrust
 0 network subnet ************ *************
 10 network subnet ************* *************
 20 network subnet ************ *************
 30 network subnet ************ *************
 40 network subnet ************* *************
 50 network subnet ************ *************
 60 network subnet ************* *************
 70 network subnet ************ *************
 80 network subnet ************ *************
 90 network subnet ************ *************
 100 network subnet ************ *************
 110 network subnet *********** *************
#              
object-group ip address 骏彩办公网（NAT************）
 security-zone Untrust
 0 network subnet ************ *************
#
object-group ip address 骏彩办公网鹏龙BT终端
 security-zone Untrust
#
object-group ip address 骏彩访问服务器
 security-zone Trust
 0 network host address ***********
 10 network host address ***********
 20 network host address ***********
 30 network subnet ********** *************
 40 network subnet 10.211.4.0 *************
 50 network subnet ********** *************
 60 network subnet 10.211.7.0 *************
 70 network subnet 10.211.6.0 *************
 80 network subnet 10.211.8.0 *************
 90 network host address ************1
 100 network host address ************2
 110 network host address *************
 120 network host address 10.218.219.21
 130 network host address 10.218.129.29
 140 network subnet 10.219.4.0 *************
 150 network host address **********
#
object-group ip address 骏彩魏琨昱需求目的地址
 0 network host address 104.23.1.82
#
object-group ip address 骏彩魏琨昱需求源地址
 0 network host address 192.168.6.53
 10 network host address 192.168.6.94
 20 network host address 10.248.131.4
 30 network host address 10.248.131.5
#
object-group ip address 开发测试环境-BT终端FTP
 security-zone Trust
 0 network range *********** ***********
 10 network range ************ ************
 20 network host address 104.21.1.202
 30 network host address ************
#
object-group ip address 开发测试环境-BT终端NTP
 security-zone Trust
 0 network host address **********
 10 network host address ************
#
object-group ip address 开发测试环境-BT终端服务01
 security-zone Trust
 0 network host address **************
 10 network host address **************
#
object-group ip address 开发测试环境-BT终端服务02
 security-zone Trust
 0 network host address *************00
 10 network host address ***************
 20 network host address *************68
#
object-group ip address 开发测试环境-办公访问PMC归集库
 security-zone Trust
 0 network host address 10.212.1.16
 10 network host address 104.200.101.154
 20 network host address 10.212.0.240
#
object-group ip address 开发测试环境-办公数据中台访问01
 security-zone Trust
 0 network subnet 10.213.3.150 ***************
 10 network host address 104.24.0.61
#              
object-group ip address 开发测试环境-办公数据中台访问02
 security-zone Trust
 0 network host address ************
 10 network host address 10.212.0.18
#
object-group ip address 开发测试环境-办公数据中台访问03
 security-zone Trust
 0 network range 104.200.101.151 104.200.101.152
#
object-group ip address 开发测试环境-办公数据中台访问04
 security-zone Trust
 0 network host address 104.21.51.67
#
object-group ip address 开发测试环境-办公数据中台访问05
 security-zone Trust
 0 network host address 104.21.57.140
 10 network host address 18.5.32.143
 20 network host address 18.0.160.1
 30 network range 10.219.10.1 10.219.10.8
 40 network host address ***********
#
object-group ip address 开发测试环境-办公数据中台访问06
 description 历史归档
 security-zone Trust
 0 network range 10.219.10.1 10.219.10.8
#
object-group ip address 开发测试环境-办公数据中台访问07
 description 即开历史备库
 security-zone Trust
 0 network host address 104.24.0.61
#
object-group ip address 开发测试环境-办公数据中台访问08
 security-zone Trust
 0 network host address ************
#
object-group ip address 开发测试环境-办公数据中心访问09
 security-zone Trust
 0 network subnet 104.21.51.96 ***************
#
object-group ip address 开发测试环境-办公网访问安卓服务
 security-zone Trust
 0 network host address ***********
#
object-group ip address 开发测试环境-北单访问业务01
 security-zone Trust
 0 network host address **************
 20 network host address **************
#
object-group ip address 开发测试环境-北单访问业务02
 security-zone Trust
 0 network host address 104.21.1.51
 10 network host address 104.21.1.151
#
object-group ip address 开发测试环境-北单访问业务03
 security-zone Trust
 0 network host address ************
 10 network host address 104.126.255.133
#
object-group ip address 开发测试环境-互联网访问USAP
 security-zone Trust
 0 network host address *************
 10 network host address ***********
#
object-group ip address 开发测试环境-经营分析系统
 security-zone Trust
 0 network host address *************65
 10 network host address 104.22.9.41
#
object-group ip address 开发测试环境-科技开发SVN01
 security-zone Trust
 0 network host address **********
 10 network host address **********
#
object-group ip address 开发测试环境-统一管理门户开发
 security-zone Trust
 0 network host address 104.22.9.41
#
object-group ip address 开发测试环境-需求统筹研发环境
 security-zone Trust
 0 network range 104.12.10.220 104.12.10.222
 10 network host address *************
#
object-group ip address 开发测试环境-印务访问网络
 security-zone Trust
 0 network subnet 104.24.0.0 *************
 10 network subnet 104.24.1.0 *************
#
object-group ip address 开发测试环境-印务访问业务
 security-zone Trust
 0 network host address 104.200.101.151
#
object-group ip address 开发测试环境-运营UMP管理端
 security-zone Trust
 0 network host address ************
#
object-group ip address 开发测试环境-运营堡垒机
 security-zone Trust
 0 network host address **************
 10 network host address ***********
 20 network subnet ********** ***************
 30 network host address ***********
 40 network subnet ************** ***************
#
object-group ip address 开发测试环境-运营访问G3售票网关
 security-zone Trust
 0 network host address **************
#
object-group ip address 开发测试环境-运营访问网络
 description 这部分需要运营公司办公网侧确认，确认后细化策略
 security-zone Trust
 0 network subnet ********** *************
 10 network subnet ********** *************
 20 network subnet ********** *************
#
object-group ip address 开发测试环境-运营访问主机
 description 需运营公司办公网侧确认，明确访问用途
 security-zone Trust
 0 network host address **********
 10 network range ********** **********
 20 network host address ************
 30 network host address ***********
#
object-group ip address 开发测试环境-运营文档访问
 security-zone Trust
 0 network host address ***********
#
object-group ip address 开发测试环境-运营虚机
 security-zone Trust
 0 network range ********** **********
#
object-group ip address 开发测试环境-运营知识库
 security-zone Trust
 0 network subnet ************* ***************
#
object-group ip address 开发测试环境-运营终端更新服务器
 security-zone Trust
 0 network host address ***********
 10 network host address ************
 20 network host address **************
#
object-group ip address 开发测试环境DNS服务器
 security-zone Trust
 0 network range ********** **********
#
object-group ip address 开发测试环境测试Server
 0 network subnet ********* ***********
#
object-group ip address 开发测试亦庄域控
 security-zone Trust
 0 network range ************ ************
#
object-group ip address 开发测试综合运营中心VDI网段
 0 network range ************ **************
#
object-group ip address 科技办公网-SVN访问源地址
 security-zone Untrust
 0 network host address *************
#
object-group ip address 科技办公网-安卓编译务器
 security-zone Untrust
 0 network host address *************
#
object-group ip address 科技办公网-测试HADOOP
 security-zone Untrust
 0 network range ************ ************
#
object-group ip address 科技办公网-测试数据中台
 description 临时增加，计划迁移到开发测试环境内部，迁移后删除-马征
 security-zone Untrust
 0 network range ************** **************
 10 network range ************ ************
#
object-group ip address 科技办公网DNS
 security-zone Untrust
 0 network host address **********
 10 network host address **********
#
object-group ip address "科技办公网F5 SNAT+MONITOR"
 security-zone Untrust
 0 network range ************ ************
#
object-group ip address 科技办公网F5selfip
 security-zone Untrust
 0 network range ************ ************
#
object-group ip address 科技办公网MAIL服务器
 security-zone Untrust
 0 network host address **********2
#
object-group ip address 科技办公网服务器地址
 0 network subnet *********** *************
#
object-group ip address 科技办公网鹏龙BT终端
 security-zone Untrust
 0 network subnet *********** *************
 10 network subnet *********** *************
 20 network subnet *********** *************
 30 network subnet *********** *************
 40 network subnet *********** *************
 50 network subnet 160.0.0.0 240.0.0.0
 60 network subnet *********** *************
 70 network subnet *********** *************
#
object-group ip address 科技二中心无线网
 security-zone Untrust
 0 network subnet *********** *************
#              
object-group ip address 科技二中心有线网
 security-zone Untrust
 0 network subnet *********** *************
#
object-group ip address 科技鹏龙20层有线网
 security-zone Untrust
 0 network subnet *********** *************
#
object-group ip address 科技鹏龙21层有线网
 security-zone Untrust
 0 network subnet *********** *************
 10 network subnet *********** *************
 20 network subnet *********** *************
 30 network subnet *********** *************
 40 network subnet *********** *************
 50 network subnet *********** *************
 60 network subnet *********** *************
 70 network subnet ************* *************
#
object-group ip address 科技鹏龙无线网
 security-zone Untrust
 0 network subnet ********** *************
 10 network subnet *********** *************
#
object-group ip address 科技鹏龙有线网
 description 科技鹏龙有线网
 security-zone Untrust
 0 network subnet *********** *************
 10 network subnet *********** *************
 20 network subnet *********** *************
 30 network subnet *********** *************
 40 network subnet *********** *************
 50 network subnet *********** *************
 60 network subnet *********** *************
 70 network subnet *********** *************
#
object-group ip address 科技认证
 security-zone Untrust
 0 network range ********** 172.16.0.4
#
object-group ip address 科技西安办公网
 0 network subnet ************* *************
#
object-group ip address 科技亦庄办公网
 security-zone Untrust
 0 network subnet 172.26.0.0 ***********
#
object-group ip address 科技亦庄办公网-即开经营分析系统
 security-zone Untrust
 0 network range 172.26.18.11 172.26.18.13
#
object-group ip address 科技翌景无线网
 security-zone Untrust
 0 network subnet ********** *************
 10 network subnet 172.21.15.0 *************
#
object-group ip address 科技翌景有线网
 security-zone Untrust
 0 network subnet 172.20.1.0 *************
 10 network subnet 172.20.2.0 *************
 20 network subnet 172.20.3.0 *************
 30 network subnet 172.20.4.0 *************
#
object-group ip address 李景一需求20220811目的地址1
 0 network host address 18.6.21.248
#
object-group ip address 李景一需求20220811目的地址2
 0 network subnet 10.220.7.0 *************
#              
object-group ip address 李景一需求20220811源地址1
 0 network subnet 10.216.40.0 *************
#
object-group ip address 李景一需求20220811源地址2
 0 network subnet 10.216.48.0 *************
#
object-group ip address 李景一需求20220811源地址3
 0 network host address 104.11.1.13
#
object-group ip address 李景一需求20220812目的地址
 0 network host address 18.5.32.143
#
object-group ip address 李景一需求20220812源地址
 0 network host address 111.204.38.56
 10 network host address 111.204.38.57
 20 network host address 111.204.38.58
 30 network host address 111.204.38.59
 40 network host address 111.204.38.60
 50 network host address 111.204.38.61
 60 network host address 111.204.38.62
 70 network host address 111.204.38.63
#
object-group ip address 李景一需求目的地址20220823
 0 network range 10.220.7.21 10.220.7.25
 10 network range 10.220.7.6 10.220.7.10
#
object-group ip address 李景一需求目的地址20221024
 description 李景一需求目的地址20221024
 0 network host address 104.255.239.145
 10 network host address 104.255.239.146
 20 network host address 104.255.239.147
 30 network host address 10.222.0.63
 40 network host address 10.222.0.64
 50 network subnet 104.12.4.0 *************
 60 network host address 104.200.101.63
#
object-group ip address 李景一需求源地址20220823
 0 network host address 104.200.101.150
 10 network host address 104.14.0.60
 20 network host address 104.21.99.31
#
object-group ip address 李景一需求源地址20221024
 description 李景一需求源地址20221024
 0 network host address 10.217.129.6
 10 network host address 18.5.81.1
 20 network host address 18.5.81.2
 30 network host address 18.5.81.3
 40 network host address 18.5.80.120
#
object-group ip address 刘栋20220901需求目的地址
 0 network subnet ********** *************
 10 network host address ***********
 20 network host address ************
 30 network host address ***********8
 40 network subnet ********** *************
 50 network host address ***********
 60 network subnet ******** *************
 70 network subnet 18.6.4.0 *************
#
object-group ip address 刘栋20220901需求源地址
 0 network subnet 10.73.64.0 255.255.224.0
#
object-group ip address 密服测试环境建设网络需求源地址
 0 network range 104.21.94.31 104.21.94.32
 10 network host address 18.6.115.4
 20 network host address 18.6.21.237
#
object-group ip address 密服测试环境建设需求目的地址
 0 network host address 18.6.115.4
 10 network host address 18.6.21.237
 20 network range 104.21.94.33 104.21.94.34
 30 network host address 104.14.10.231
#
object-group ip address 宁夏农行服务IP
 security-zone Untrust
 0 network subnet 192.168.207.0 *************
#
object-group ip address 鹏龙21层部分机器
 security-zone Untrust
 0 network host address 172.20.24.59
 10 network host address 172.20.24.145
 20 network host address 172.20.30.229
 30 network host address 172.20.29.29
 40 network host address 172.20.24.115
 50 network host address 172.20.34.190
 60 network host address 172.20.30.230
 70 network host address 172.20.34.163
#
object-group ip address 鹏龙21层开发终端连接测试环境
 security-zone Trust
 0 network host address 104.200.100.29
 10 network host address **************
 20 network host address *************00
 30 network host address **************
 40 network host address ***********
 50 network host address **********
 60 network host address **************
 70 network host address ***************
 80 network host address **************
 90 network host address ************
 100 network host address ************
 110 network host address *************
 120 network host address *************73
 130 network host address *************72
 140 network host address **************
 150 network host address ************
 160 network host address ************
 170 network host address 104.13.0.52
 180 network host address ***********
 190 network host address *************
 200 network host address *************
 210 network host address ************
 220 network host address ************
 230 network host address ************
 240 network host address ************
 250 network host address ************
 260 network host address ***********
 270 network host address ************
 280 network host address ***********
#
object-group ip address 生产工单系统
 0 network host address **********
 10 network host address ***********
#
object-group ip address 数字化中心三端新环境目的地址
 0 network host address *************
#
object-group ip address 数字化中心三端新环境源地址
 0 network host address **************
 10 network host address ************
 20 network host address ************
 30 network host address ************
#
object-group ip address 腾讯云测试环境HOST
 0 network host address ************
 10 network host address ***********
 20 network host address ***********
 30 network host address ***********
 40 network host address **********
#
object-group ip address 王庆帅0728需求
 security-zone Untrust
 0 network host address ************
 10 network host address ************
 20 network host address ************
 30 network host address ************
 40 network host address ************
 50 network host address ************
 60 network host address *************
 70 network host address ***********
 80 network host address ***********
 90 network host address *************
 100 network host address ************
#
object-group ip address 王玉玲服务器
 security-zone Trust
 0 network host address ***********
 10 network host address **********4
 20 network host address **********7
 30 network range *********** ***********
 40 network host address ***********
 50 network range ********** **********
 60 network range *********** ***********
 70 network host address **********
 80 network host address **********
 90 network host address **********
 100 network host address ***********
 110 network range *********** ***********
 120 network host address ***********
 130 network host address ***********
 140 network host address ***********
 150 network host address ************
 160 network host address ************
 170 network host address *************
 180 network host address *************
 190 network host address *************
 200 network host address ***********
 210 network host address ***********
#
object-group ip address 西安办公区开发终端连接测试环境
 security-zone Trust
 0 network host address **********
 10 network host address **********
 20 network host address ***********
 30 network host address ************
 40 network host address ***********
#
object-group ip address 西安办公区终端组
 security-zone Untrust
 0 network host address **************
 10 network host address **************
 20 network host address ***************
 30 network host address ***************
 40 network host address ***************
 50 network host address ***************
#
object-group ip address 系统访问NAS
 30 network host address *************
 40 network host address ***********
#
object-group ip address 系统管理组
 0 network host address *************
 10 network host address *************
#
object-group ip address 系统组访问目标
 0 network host address ***************
 10 network host address **********
#
object-group ip address 协同财务处系统管理工具
 security-zone Trust
 0 network subnet *********** *************
 10 network subnet ********** *************
#
object-group ip address 协同考勤服务器
 security-zone Trust
 0 network host address ***********
#
object-group ip address 协同需求统筹管理工具
 security-zone Trust
 0 network host address ***********
 10 network host address ***********
#
object-group ip address 新建DNS服务器
 description ************** 为************/24段解析域名 cslc.com.cn
 security-zone Trust
 0 network host address **************
#
object-group ip address 研创访问知识库国家实验室（源）
 0 network subnet ************ *************
#              
object-group ip address 研创访问知识库以及国家实验室
 0 network host address **********
 10 network host address **********
 20 network host address **************
#
object-group ip address 研创中心到堡垒机
 0 network subnet ************** ***************
#
object-group ip address 亦庄5鹏龙21测试团队目的地址
 0 network host address **************
 10 network host address *************00
 20 network host address ***********
 30 network host address ***********
 40 network host address ***********
 50 network host address **********
 60 network host address **************
 70 network host address ***************
 80 network host address ************
 90 network host address 104.21.18.82
 100 network host address ************
 110 network host address ************
 120 network host address *************73
 130 network host address *************72
 140 network host address ************
 150 network host address 104.21.19.82
 160 network host address ************
 170 network host address ************
 180 network host address 104.13.0.52
 190 network host address ***********
#
object-group ip address 亦庄5鹏龙21测试团队源地址
 0 network subnet *********** *************
 10 network subnet *********** *************
#
object-group ip address 亦庄办公网
 0 network subnet 172.20.0.0 ***********
 10 network subnet ********** ***********
 50 network subnet ************* *************
 60 network subnet 192.168.4.0 *************
#
object-group ip address 翌景访问虚拟化平台（目的）
 0 network host address **************
#
object-group ip address 翌景访问虚拟化平台（源）
 0 network host address 192.168.6.159
 10 network host address 192.168.6.160
 20 network host address 192.168.6.69
#
object-group ip address 尹健需求目的地址20220902
 0 network host address 104.21.50.171
#
object-group ip address 尹健需求源地址20220902
 0 network subnet 10.194.119.0 *************
#
object-group ip address 印务办公网
 security-zone Untrust
 0 network subnet 192.168.8.0 *************
 10 network subnet *********** *************
#
object-group ip address 喻坤需求目的地址20220815
 0 network host address 104.12.1.44
#
object-group ip address 喻坤需求源地址20220815
 0 network host address 172.16.0.6
 10 network host address 172.31.2.1
 20 network host address 172.31.2.2
#
object-group ip address 云峰为格亮梳理安全月
 description 安全月
 security-zone Trust
 0 network host address *************
 10 network host address ************
 20 network host address ************
 30 network host address ***********
 40 network host address *************
 50 network host address *************
 60 network host address *************68
 70 network host address ************
 80 network host address ***********
 90 network host address ************
 100 network host address ***********
 110 network host address ***********
 120 network host address ************
 130 network host address ***********
 140 network host address *************00
 150 network host address ************
 160 network host address **************
 170 network host address ***********
 180 network host address ***********
 190 network host address ************
 200 network host address ***********
 210 network host address *************
 220 network host address ************
 230 network host address *************
#
object-group ip address 运营办公网
 security-zone Untrust
 0 network range 10.248.128.0 10.248.138.254
 10 network range 10.248.142.0 10.248.158.254
 20 network subnet 10.248.162.0 *************
 30 network range 10.248.166.0 10.248.191.254
 40 network subnet 192.168.6.0 *************
 50 network subnet 192.168.22.0 255.255.254.0
 60 network subnet 192.168.50.0 *************
 70 network subnet ************ *************
 80 network host address 10.248.131.25
 90 network host address 10.248.131.22
 100 network subnet ************ *************
#
object-group ip address 运营办公网DNS
 security-zone Untrust
 0 network host address 172.18.0.5
 10 network host address 172.18.0.7
 20 network host address 172.18.0.8
 30 network host address 192.168.6.2
 40 network host address 192.168.6.3
 50 network host address 192.168.22.214
#
object-group ip address 运营办公网MAIL服务器
 security-zone Untrust
 0 network host address 192.168.51.6
#
object-group ip address 运营公司百环办公区maven
 security-zone Untrust
 0 network host address 192.168.22.245
#
object-group ip address 运营公司百环办公区Sonar
 security-zone Untrust
 0 network host name 192.168.22.252
#
object-group ip address 运营虚拟化打印机
 security-zone Untrust
 0 network range *************** 172.100.201.204
 10 network range *************** 172.100.202.204
 20 network range *************** 172.100.207.204
#
object-group ip address 运营中心VDIDNS
 security-zone Untrust
 0 network range 172.200.3.1 172.200.3.2
#
object-group ip address 运营中心VDI白名单
 0 network host address 10.248.131.22
 10 network host address 10.248.131.13
 20 network host address 10.248.130.28
 30 network host address 10.248.130.98
 40 network host address 10.248.130.120
 50 network host address 192.168.6.80
 60 network host address 192.168.6.39
 70 network host address 10.248.131.23
#
object-group ip address 张润苗需求源地址
 0 network host address 172.16.0.93
 10 network range 172.16.0.70 172.16.0.73
 20 network range ********** 172.16.0.4
 30 network host address **********06
#
object-group ip address 中心无线网
 security-zone Untrust
 0 network subnet ************ *************
 10 network subnet ************ *************
 20 network subnet ************ *************
 30 network subnet ************ *************
 40 network subnet ************ *************
 50 network subnet 192.168.252.0 *************
#
object-group ip address 中心有线网
 security-zone Untrust
 0 network subnet *********** *************
 10 network subnet 192.168.45.0 *************
 20 network subnet 192.168.67.0 *************
#
object-group ip address "桌面云connect server"
 description 桌面云connect server
 security-zone Trust
 0 network host address ***********
 10 network host address ***********
 20 network host address ***********
 30 network host address ************1
 40 network host address ************2
 50 network host address *************
 60 network subnet ************* ***************
#
object-group ip address 桌面云VDI（**********）
 security-zone Trust
 0 network subnet ********** *************
#
object-group ip address 桌面云VDI桌面
 security-zone Trust
 0 network range ********** *************
 10 network range ************ **************
#
object-group ip address "自动化测试MAIL 服务器"
 security-zone Trust
 0 network host address ***********
#
object-group ip address 综合运营中心VDI-VC
 0 network host address *************
 10 network host address ************
#
object-group ip address 综合运营中心VDI瘦客户端网段
 security-zone Untrust
 0 network subnet ************* *************
 10 network subnet ************* *************
 20 network subnet ************* *************
#
object-group ip address 综合运营中心VDI网段
 security-zone Untrust
 0 network subnet *********** *************
 10 network subnet *********** *************
#
object-group ip address 综合运营中心业务运营老网段
 security-zone Untrust
 0 network subnet ********** ***********
#
object-group ipv6 address ipv6test
#
object-group service 135-139
 0 service tcp destination range 135 139
 10 service udp destination range 135 139
#
object-group service 22443
 0 service tcp destination eq 22443
 10 service udp destination eq 22443
#
object-group service 32111
 0 service tcp destination eq 32111
 10 service udp destination eq 32111
#
object-group service 34443
 0 service tcp destination eq 34443
#
object-group service 4172
 0 service tcp destination eq 4172
 10 service udp destination eq 4172
#
object-group service 80
#
object-group service 9427
 0 service tcp destination eq 9427
 10 service udp destination eq 9427
#
object-group service BT终端访问开发业务端口
 0 service tcp destination eq 8443
 10 service tcp destination eq 30000
 20 service group-object ntp
 30 service tcp destination eq 21
#
object-group service "NAS service port"
 0 service tcp destination eq 111
 10 service udp destination eq 111
 20 service tcp destination eq 2049
 30 service udp destination eq 2049
 40 service tcp destination eq 4046
 50 service udp destination eq 4046
 60 service tcp destination eq 635
 70 service udp destination eq 635
 80 service tcp destination eq 1234
 90 service udp destination eq 1234
#
object-group service "TCP 30088"
 0 service tcp destination eq 30088
#
object-group service TCP-10050
 0 service tcp destination eq 10050
#
object-group service tcp-123
 0 service tcp destination eq 123
#
object-group service TCP-139
 0 service tcp destination eq 139
#
object-group service TCP-1433
 0 service tcp destination eq 1433
#
object-group service TCP-1443
 0 service tcp destination eq 1443
#
object-group service TCP-172
 0 service tcp destination eq 172
#
object-group service TCP-2181
#
object-group service tcp-24432
 0 service tcp destination eq 24432
#
object-group service tcp-30000
 0 service tcp destination eq 30000
#
object-group service tcp-30071
 0 service tcp destination eq 30071
#
object-group service TCP-31306
 0 service tcp destination eq 31306
#
object-group service TCP-384
 0 service tcp destination eq 384
#
object-group service tcp-389
 0 service tcp destination eq 389
#
object-group service TCP-434
 0 service tcp destination eq 434
#
object-group service TCP-44
 0 service tcp destination eq 44
#
object-group service tcp-4422
 0 service tcp destination eq 4422
#
object-group service TCP-445
 0 service tcp destination eq 445
#
object-group service tcp-5080
 0 service tcp destination eq 5080
#
object-group service TCP-5222
 0 service tcp destination eq 5222
#
object-group service TCP-5480
 0 service tcp destination eq 5480
#
object-group service TCP-8090
 0 service tcp destination eq 8090
#
object-group service TCP10000
 0 service tcp destination eq 10000
#
object-group service TCP1521
 0 service tcp destination eq 1521
#
object-group service TCP21050
 0 service tcp destination eq 21050
#
object-group service TCP21051
 0 service tcp destination eq 21051
#
object-group service TCP2181
 0 service tcp destination eq 2181
#
object-group service TCP22
 0 service tcp destination eq 22
#
object-group service TCP2883
 0 service tcp destination eq 2883
#              
object-group service TCP30000
 0 service tcp destination eq 30000
#
object-group service TCP30010
 0 service tcp destination eq 30010
#
object-group service TCP30080
 0 service tcp destination eq 30080
#
object-group service TCP3306
 0 service tcp destination eq 3306
#
object-group service TCP3389
 0 service tcp destination eq 3389
#
object-group service TCP6677
 0 service tcp destination eq 6677
#
object-group service TCP7788
 0 service tcp destination eq 7788
#
object-group service TCP8001
 0 service tcp destination eq 8001
#
object-group service TCP8002
 0 service tcp destination eq 8002
#
object-group service TCP8020
 0 service tcp destination eq 8020
#
object-group service TCP8082
 0 service tcp destination eq 8082
#
object-group service TCP8083
 0 service tcp destination eq 8083
#
object-group service TCP8088
 0 service tcp destination eq 8088
#
object-group service TCP8443
 0 service tcp destination eq 8443
#
object-group service TCP8510
 0 service tcp destination eq 8510
#
object-group service TCP8889
 0 service tcp destination eq 8889
#
object-group service TCP9080
 0 service tcp destination eq 9080
#
object-group service TCP9092
 0 service tcp destination eq 9092
#
object-group service TCP9201
 0 service tcp destination eq 9201
#
object-group service tcp_10081
 0 service tcp destination eq 10081
#
object-group service tcp_10082
 0 service tcp destination eq 10082
#
object-group service TCP_2181
 0 service tcp destination eq 2181
#
object-group service tcp_25
 0 service tcp destination eq 25
#              
object-group service TCP_30071
 0 service tcp destination eq 30071
#
object-group service TCP_30514
 0 service tcp destination eq 30514
#
object-group service TCP_39092
 0 service tcp destination eq 39092
#
object-group service tcp_4172
 0 service tcp destination eq 4172
#
object-group service TCP_49092
 0 service tcp destination eq 49092
#
object-group service tcp_53
 0 service tcp destination eq 53
#
object-group service tcp_80
 0 service tcp destination eq 80
#
object-group service tcp_8080
 0 service tcp destination eq 8080
#
object-group service tcp_8081
 0 service tcp destination eq 8081
#
object-group service tcp_8082
 0 service tcp destination eq 8082
#
object-group service tcp_8443
 0 service tcp destination eq 8443
#
object-group service TCP_9093
 0 service tcp destination eq 9093
#
object-group service UDP-53
 0 service udp destination eq 53
#
object-group service UDP443
 0 service udp destination eq 443
#
object-group service UDP8443
 0 service udp destination eq 8443
#
object-group service udp_4172
 0 service udp destination eq 4172
#
object-group service 骏彩访问服务
 0 service udp destination lt 54
 10 service tcp destination lt 390
 20 service tcp destination lt 22444
 30 service udp destination lt 22444
 40 service tcp destination lt 4173
 50 service udp destination lt 4173
 60 service tcp destination lt 9428
 70 service tcp destination lt 32112
 80 service tcp destination lt 444
 90 service tcp destination lt 18444
 100 service tcp destination range 4001 4002
 110 service tcp destination lt 8010
 120 service tcp destination lt 8444
 130 service icmp
 140 service tcp destination lt 446
 150 service tcp destination lt 8026
 160 service tcp destination lt 8090
 170 service tcp destination range 66 68
#
object-group service 开发测试环境-互联网访问服务
 description 互联网访问服务组
 0 service tcp destination eq 8319
 10 service tcp destination eq 8090
 20 service tcp destination range 30000 30001
 30 service tcp destination range 8080 8082
 40 service tcp destination eq 10248
 50 service tcp destination eq 5080
 60 service tcp destination eq 34443
 70 service tcp destination eq 14433
 80 service tcp destination eq 24433
 90 service tcp destination eq 8889
 100 service tcp destination eq 81
#
object-group mac mac_test
#
interface Reth3
 member interface Route-Aggregation5 priority 200
#
interface Bridge-Aggregation6
 port access vlan 200
 link-aggregation mode dynamic
#
interface Route-Aggregation5
 link-aggregation mode dynamic
#
interface NULL0
#
interface GigabitEthernet1/0/0
 port link-mode route
 combo enable copper
 ip address *********** *************
#
interface GigabitEthernet1/0/1
 port link-mode route
 combo enable fiber
#
interface GigabitEthernet1/0/2
 port link-mode route
 combo enable fiber
 ip address ************ ***************
#
interface GigabitEthernet1/0/3
 port link-mode route
 combo enable fiber
#
interface GigabitEthernet1/8/0
 port link-mode route
 ip binding vpn-instance management
 ip address ************** *************
#
interface GigabitEthernet1/8/1
 port link-mode route
#
interface GigabitEthernet1/8/2
 port link-mode route
#
interface GigabitEthernet1/8/3
 port link-mode route
#
interface GigabitEthernet1/8/4
 port link-mode route
#
interface GigabitEthernet1/8/5
 port link-mode route
#
interface GigabitEthernet1/8/6
 port link-mode route
#
interface GigabitEthernet1/8/7
 port link-mode route
 ip address ************* ***************
 nat static enable
#
interface Ten-GigabitEthernet1/1/0
 port link-mode route
#
interface Ten-GigabitEthernet1/1/1
 port link-mode route
#
interface Ten-GigabitEthernet1/1/2
 port link-mode route
#
interface Ten-GigabitEthernet1/1/3
 port link-mode route
#
interface Ten-GigabitEthernet1/1/4
 port link-mode route
#
interface Ten-GigabitEthernet1/1/5
 port link-mode route
#
interface Ten-GigabitEthernet1/1/6
 port link-mode route
#
interface Ten-GigabitEthernet1/1/7
 port link-mode route
 ip address ************* ***************
#
security-zone name Local
#
security-zone name Trust
 import interface Ten-GigabitEthernet1/1/7
#
security-zone name DMZ
#
security-zone name Untrust
 import interface GigabitEthernet1/8/7
#
security-zone name Management
 import interface GigabitEthernet1/0/0
 import interface GigabitEthernet1/8/0
#
security-zone name TENANT02_CORE_Inside
 import interface Ten-GigabitEthernet1/1/6
#              
security-zone name TENANT02_CORE_Outside
 import interface GigabitEthernet1/8/6
#
security-zone name TENANT02_GW_Inside
 import interface Ten-GigabitEthernet1/1/5
#
security-zone name TENANT02_GW_Outside
 import interface GigabitEthernet1/8/5
#
security-zone name yuny_vdi
 import interface GigabitEthernet1/0/2
 import interface Route-Aggregation5
#
zone-pair security source Any destination Any
 packet-filter 2000
#
 scheduler logfile size 16
#
line class console
 authentication-mode scheme
 user-role network-admin
#
line class vty 
 user-role network-operator
#
line con 0
 authentication-mode scheme
 user-role network-admin
#
line vty 0 63
 authentication-mode scheme
 user-role network-admin
#
 ip route-static 0.0.0.0 0 *************
 ip route-static ******** 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 24 *************
 ip route-static ********** 16 ************* description G3
 ip route-static ********** 16 ************* description G3
 ip route-static ********** 19 ************
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ********** 16 ************* description 敏态云
 ip route-static ********** 16 ************* description 敏太云overlay
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ********** 16 *************
 ip route-static ************ 24 *************
 ip route-static ************ 24 ************* preference 50
 ip route-static ********** 16 *************
 ip route-static ******** 16 *************
 ip route-static ******** 16 *************
 ip route-static ******** 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 24 *************
 ip route-static ******** 24 *************
 ip route-static ******** 24 *************
 ip route-static ******** 24 *************
 ip route-static ******** 24 *************
 ip route-static ******** 24 *************
 ip route-static ********* 24 *************
 ip route-static ********* 8 *************
 ip route-static ********* 8 *************
 ip route-static ************* 32 ************* description G3
 ip route-static ************ 32 ************* description G3
 ip route-static ************** 32 ************* description G3
 ip route-static ************* 32 ************* description G3
 ip route-static *************** 32 ************* description G3
 ip route-static ************** 32 ************* description G3
 ip route-static *************** 32 ************* description G3
 ip route-static ********* 8 ************* description 公司办公网
 ip route-static *********** 16 *************
 ip route-static *********** 24 ************* description 中心办公网（临时）
 ip route-static 192.168.4.0 24 *************
 ip route-static ************ 24 *************
 ip route-static vpn-instance management 0.0.0.0 0 104.255.253.254
#
 snmp-agent
 snmp-agent local-engineid 800063A280DCDA807AEAD400000001
 snmp-agent community read cipher $c$3$Y2J2leXG4jUc+2lF9kmlJqyfxdFK4y5KGFNANA==
 snmp-agent community write private
 snmp-agent community read public
 snmp-agent sys-info contact LiHang,ZhangYue,JiangDawei
 snmp-agent sys-info location LAFW02-W5R-A-9-30-2
 snmp-agent sys-info version all 
#
 ssh server enable
#
 time-range test_jdw 15:51 to 15:52 Sun 
 time-range 安全月数字化测试定时开启 00:00 to 01:30 Sat Fri Thu Wed Tue 
 time-range 安全月数字化测试定时开启 21:30 to 24:00 working-day 
#
acl basic 2000
 rule 0 permit
#
domain system
#
 domain default enable system
#
role name level-0
 description Predefined level-0 role
#
role name level-1
 description Predefined level-1 role
#              
role name level-2
 description Predefined level-2 role
#
role name level-3
 description Predefined level-3 role
#
role name level-4
 description Predefined level-4 role
#
role name level-5
 description Predefined level-5 role
#
role name level-6
 description Predefined level-6 role
#
role name level-7
 description Predefined level-7 role
#
role name level-8
 description Predefined level-8 role
#
role name level-9
 description Predefined level-9 role
#
role name level-10
 description Predefined level-10 role
#
role name level-11
 description Predefined level-11 role
#
role name level-12
 description Predefined level-12 role
#
role name level-13
 description Predefined level-13 role
#
role name level-14
 description Predefined level-14 role
#
user-group system
#
local-user guest class manage
 password hash $h$6$3o0be7MiBVem7rjR$3gXL5dy3U6646uPogJre9f6NXzUeSiuQbATigUNVi+7RMU6m9ifVznP4QgO60SIq5z3eutTWiNnSVeC3JadH6Q==
 service-type ssh https
 authorization-attribute user-role system-admin
#              
local-user netadmin class manage
 password hash $h$6$EnIg3r8xdKga3fcM$OX7ZGcelQpRZqAQZzq9ebZkCFpbjxc9wQOq+Qlu/1lHoAbNIczhQe1Rpz5ynKT+Gb3cWFlzejHQJzIlaVdw8HA==
 service-type ssh terminal https
 authorization-attribute user-role network-admin
#
public-key peer 104.255.253.23
 public-key-code begin
   30819F300D06092A864886F70D010101050003818D0030818902818100BA577D278C847003
   DAEF2D3DC7607829DC42FD3EF92600416EFB8037E604D7FDA6EB50B14B26F518D06FD5039D
   09AE1D44143E59229B3DA6F313A196E913C51B413413F6508C640F4D6D9497B90BE75D59D6
   7CC90912DF678649D67F04DE62438C5D29024BA39CB94E62987DB4FC245A5507DB657E9D44
   E5399102F03BFD91FF0203010001
 public-key-code end
 peer-public-key end
#
 ip https enable
 webui log enable
#
inspect logging parameter-profile av_logging_default_parameter
#
inspect logging parameter-profile ips_logging_default_parameter
#
inspect logging parameter-profile url_logging_default_parameter
#
security-policy ip
 rule 195 name 20231205丁楠需求2
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip ************/32
  destination-ip 172.26.18.10/32
  service TCP22
 rule 194 name 20231205丁楠需求
  description 与王玉玲需求相同，访问科技ldap
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 104.12.1.100/32
  destination-ip **********/32
  service tcp-389
 rule 193 name 测试监控_to_辅助运营
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip **********50
  destination-ip 172.200.10.1
 rule 192 name 20231204王玉玲需求
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 20231204王玉玲需求源地址
  destination-ip **********2/32
  service tcp_25
 rule 204 name uat-epb业务
  description 微信网页授权服务器
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网F5selfip
  destination-ip ************
  service http
 rule 201 name 办公网访问测试环境邮箱
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 办公网蜜罐
  destination-ip **********
 rule 200 name bmms业务
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网F5selfip
  destination-ip bmms项目
  service TCP30010
 rule 191 name 魔方项目
  description 朱云峰魔方项目
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网F5selfip
  destination-ip ***************
  destination-ip ***************
 rule 187 name G3_jiance
  description G3监测项目VDI 临时开通-于娜
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip "G3 VDI&104"
  destination-ip G3172.20
 rule 186 name 北京单场20231128（2）
  description 腾讯云环境与北单专线,包含G32实体渠道服务
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 10.216.48.0/24
  source-ip 10.216.41.0/24
  source-ip 10.216.44.0/24
  destination-ip ************
  service tcp_80
 rule 185 name 北京单场20231128
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip **********/24
  destination-ip ************
  service tcp_80
 rule 184 name 20231128
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip **********/16
  destination-ip 172.16.20.6
  destination-ip 172.16.23.1
 rule 183 name 访问sement
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  destination-ip 生产工单系统
 rule 182 name 王玉玲需求
  description 各个服务器到科技认证
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 王玉玲服务器
  destination-ip 科技认证
 rule 149 name 张润苗230829服务器安装agent
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 172.25.5.1-2
  source-ip 张润苗需求源地址
  destination-ip **********
  service TCP6677
  service TCP7788
  service TCP8001
  service TCP8002
  service http
  service tcp_8443
 rule 174 name 骏彩办公网访问测试VDI
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网（192.168段）
  source-ip 骏彩办公网（NAT************）
  destination-ip 骏彩访问服务器
  service 骏彩访问服务
 rule 176 name 西安办公区策略test
  description 临时开通，后期西安办公区建好专网关闭
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***************-174
  destination-ip 西安办公区开发终端连接测试环境
  destination-ip 鹏龙21层开发终端连接测试环境
  service TCP8088
  service tcp_8080
  service ftp
  service ssh
  service TCP-139
  service TCP-445
  service TCP8443
  service TCP30000
  service https
 rule 181 name Test_VDI_To_YY-VDI
  action pass
  counting enable
  source-zone Trust
  destination-zone yuny_vdi
  source-ip **********/16
  source-ip ************
  source-ip **********
  source-ip 10.218.45-46
  destination-ip yyvdi-10.209
 rule 189 name YW-TC_To_TestVDI
  action pass
  counting enable
  source-zone yuny_vdi
  destination-zone Trust
  source-ip 10.209.2-9
  destination-ip ***********
  destination-ip ************
  destination-ip ***********
  destination-ip ***********
 rule 190 name YY-VDI_To_GongXiangPan
  action pass
  counting enable
  source-zone yuny_vdi
  destination-zone Trust
  source-ip yyvdi-10.209
  destination-ip *************
 rule 198 name YW-VDI_To_JiaMi
  action pass
  counting enable
  source-zone yuny_vdi
  destination-zone Trust
  source-ip yyvdi-10.209
  destination-ip ***********
  service TCP8443
  service TCP-8090
  service TCP-5222
 rule 202 name LotteryVDI_To_YW-Print
  action pass
  counting enable
  source-zone Trust
  destination-zone yuny_vdi
  source-ip ************-**************
  destination-ip YW-Print
 rule 203 name LotteryVDI_To_YW-GongXiangPan
  action pass
  counting enable
  source-zone Trust
  destination-zone yuny_vdi
  source-ip ************-**************
  destination-ip **********
 rule 72 name ping操作
  action pass
  counting enable
  source-zone Untrust
  source-zone yuny_vdi
  source-zone Local
  source-zone Trust
  destination-zone Trust
  destination-zone Local
  destination-zone yuny_vdi
  destination-zone Untrust
  service icmp-address-mask
  service icmp-dest-unreachable
  service icmp-fragment-needed
  service icmp-fragment-reassembly
  service icmp-host-unreachable
  service icmp-info
  service icmp-parameter-problem
  service icmp-port-unreachable
  service icmp-protocol-unreach
  service icmp-redirect
  service icmp-redirect-host
  service icmp-redirect-tos-host
  service icmp-redirect-tos-net
  service icmp-source-quench
  service icmp-source-route-fail
  service icmp-time-exceeded
  service icmp-timestamp
  service icmp-traceroute
 rule 172 name 骏彩办公网访问**********
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  destination-ip ***********
  service icmp-address-mask
  service icmp-redirect-host
  service icmp-port-unreachable
  service icmp-parameter-problem
  service icmp-info
  service icmp-host-unreachable
  service icmp-fragment-reassembly
  service icmp-fragment-needed
  service icmp-dest-unreachable
  service icmp-protocol-unreach
  service icmp-redirect
  service icmp-redirect-tos-host
  service icmp-redirect-tos-net
  service icmp-source-quench
  service icmp-source-route-fail
  service icmp-time-exceeded
  service icmp-timestamp
  service icmp-traceroute
  service dns-tcp
  service dns-udp
 rule 188 name 彩银测试出向访问
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 彩银测试服务器地址
  destination-ip 192.168
 rule 112 name 彩银测试
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 192.168
  destination-ip 彩银测试服务器地址
  service 34443
  service tcp-5080
 rule 169 name vdi访问网络设备带外地址
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip **********
  destination-ip 带外地址
 rule 168 name 访问外网
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 10.217.130-137/139-140.0/24
  destination-ip *************-122
 rule 173 name 西安办公BT链接测试
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***************-174
  destination-ip 测试
  service TCP8443
  service https
  service TCP30000
 rule 167 name "lotteryVDI 访问 sporttery VDI共享盘"
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip *************
  source-ip ************
  source-ip ************
  source-ip 10.217.130-139.0/24
  destination-ip ************
 rule 166 name lotteryVDI访问打印机
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 10.217.131-137.0
  destination-ip 运营虚拟化打印机
 rule 165 name "lotteryVDI访问sportyy VDI数据库"
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 10.217.131-137.0
  destination-ip ***************-112
  destination-ip ***************
  service TCP-1433
 rule 164 name "lotteryVDI 访问 rmoas系统"
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 10.217.131-137.0
  source-ip V3_CORE_K8SNODE_4.190.85.0/24
  source-ip V3_CORE_K8SNODE_4.190.86.0/24
  source-ip V3_CORE_**********/22
  destination-ip ***************-102
  service http
  service https
  service TCP-2181
 rule 163 name 路宇访问vdi管理端
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip ************-3
  destination-ip *************
  destination-ip ************
 rule 162 name 辅助运营策略
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip **************/56
  destination-ip 172.100/200.0.0
  service ssh
  service https
  service TCP3389
  service http
  service tcp_53
  service UDP-53
  service TCP-5480
 rule 161 name 辅助运营zbx监控
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 104.126.246-248.0
  destination-ip 172.100/200.0.0
  service TCP-10050
 rule 160 name 同步传足、竞彩、北单数据及数据分析使用
  description 待迁移
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 桌面云VDI桌面
  destination-ip ************-55
 rule 159 name 工程中心考勤系统传数据到骏彩工时系统
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip ***********
  destination-ip **************
  service TCP-31306
 rule 170 name 统一开发测试专网访问业务系统
  description 后续此六台打印机将会改造到lottery VDI网络环境内，再关闭
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  source-ip ************
  destination-ip ***********
  service http
 rule 157 name 王庆帅1011
  description 2023年10月20日关闭
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************
  destination-ip ***********
  destination-ip **********
  destination-ip **********
  service ssh
  service ftp
  service icmp-address-mask
  service icmp-dest-unreachable
  service icmp-fragment-needed
  service icmp-fragment-reassembly
  service icmp-host-unreachable
  service icmp-info
  service icmp-parameter-problem
  service icmp-port-unreachable
  service icmp-protocol-unreach
  service icmp-redirect
  service icmp-redirect-host
  service icmp-redirect-tos-host
  service icmp-redirect-tos-net
  service icmp-source-quench
  service icmp-source-route-fail
  service icmp-time-exceeded
  service icmp-timestamp
  service icmp-traceroute
  service TCP8088
 rule 156 name 西安办公区策略
  description 临时开通，后期西安办公区建好专网关闭
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 西安办公区终端组
  destination-ip 西安办公区开发终端连接测试环境
  destination-ip 鹏龙21层开发终端连接测试环境
  service TCP8088
  service tcp_8080
  service ftp
  service ssh
  service TCP-139
  service TCP-445
  service TCP8443
  service TCP30000
  service https
 rule 153 name BT访问测试网策略
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip BT终端IP
  destination-ip 20230726孙磊需求目标地址
  destination-ip 亦庄5鹏龙21测试团队目的地址
  destination-ip 开发测试环境-BT终端服务01
  destination-ip 开发测试环境-BT终端服务02
  destination-ip 开发测试环境-BT终端FTP
  destination-ip 开发测试环境-BT终端NTP
  destination-ip BT终端访问开发业务目的地址
  service TCP8443
  service tcp-30000
  service https
  service TCP-1443
  service tcp_8080
  service 80
  service tcp-123
  service tcp-24432
  service ntp
  service ftp
 rule 148 name 定位终端
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip **********
  destination-ip *************00
 rule 155 name 张婷旭运营支持打印机到共享文件夹
  description 运营支持打印机到共享文件夹
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***************-202
  source-ip ***************-202
  source-ip ***************-202
  destination-ip *************
 rule 136 name 管理测试环境
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 测试环境管理（源地址）
 rule 151 name 系统访问NAS
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 系统访问NAS
  destination-ip 系统组访问目标
 rule 132 name 20230719路宇需求
  action pass  
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************/24
  destination-ip ***********/24
 rule 129 name 20230519
  description 等待百环专网建设，建设好后关闭该策略
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  destination-ip **************
  destination-ip *************00
  destination-ip 测试环境操作终端业务
  destination-ip **************
 rule 144 name 安全月应急策略
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
 rule 145 name 网闸关闭
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************
  destination-ip *************
 rule 146 name 网闸关闭02
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip ***********
  destination-ip *************
 rule 137 name 20230726需求01
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************/32
  destination-ip *************/32
 rule 138 name 20230726需求02
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********/24
  source-ip ***********
  destination-ip 20230726目标地址01
 rule 139 name 20230726需求03
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip **************/32
  destination-ip ***********-15
 rule 140 name 20230726需求04
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********/24
  source-ip ***********/24
  destination-ip 20230726孙磊需求目标地址
  service tcp_8443
  service tcp-30000
  service https
  service TCP-1443
  service tcp_8080
  service tcp_80
  service tcp-123
  service tcp-24432
 rule 141 name 20230726需求05
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************/32
  destination-ip *************/32
  service tcp-4422
 rule 142 name 王庆帅0728
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 王庆帅0728需求
  destination-ip ***********
  service TCP8088
 rule 143 name 王庆帅0801
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 鹏龙21层部分机器
  source-ip 西安办公区终端组
  destination-ip 西安办公区开发终端连接测试环境
  destination-ip 鹏龙21层开发终端连接测试环境
  service TCP-139
  service TCP-445
  service ssh
  service ftp
  service TCP8088
  service tcp_8080
  service TCP8443
  service tcp-30000
  service https
  service tcp-24432
  service tcp-123
  service TCP-1443
  service http
 rule 147 name 朱云峰0817拉新项目互联网发布
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网F5selfip
  destination-ip ************
  service http
 rule 133 name 朱云峰测试
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网F5selfip
  destination-ip *************
  destination-ip *************
  destination-ip ***********
  destination-ip ***********
  destination-ip ************
  destination-ip ************-12
  destination-ip ***********
  destination-ip **************
  service tcp_8080
  service http
  service https
  service TCP9080
  service tcp_8081
  service TCP8082
 rule 19 name 可以访问**********的用户
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 运营中心VDI白名单
  destination-ip **********
 rule 18 name **********
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip **********
 rule 127 name 办公网访问协同考勤服务器
  action pass
  disable      
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 办公网访问协调考勤主机
  destination-ip 协同考勤服务器
  service 80
  service TCP3389
  service TCP-1443
  service TCP-1433
 rule 131 name 20230627王庆帅需求
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip ***********
 rule 134 name 亦庄5鹏龙21测试团队需求
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 亦庄5鹏龙21测试团队源地址
  destination-ip 亦庄5鹏龙21测试团队目的地址
 rule 2 name 科技鹏龙有线网访问桌面云
  description 桌面云connect server访问
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技鹏龙有线网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙无线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 综合运营中心VDI网段
  source-ip 综合运营中心业务运营老网段
  source-ip 中心有线网
  source-ip 中心无线网
  source-ip 印务办公网
  source-ip 骏彩办公网
  source-ip 亦庄办公网
  source-ip 科技亦庄办公网
  destination-ip "桌面云connect server"
  service tcp_80
  service tcp_8443
  service https
 rule 62 name "Viminal 工单"
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技鹏龙有线网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙无线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 综合运营中心VDI网段
  source-ip 综合运营中心业务运营老网段
  source-ip 中心有线网
  source-ip 中心无线网
  source-ip 印务办公网
  source-ip 骏彩办公网
  source-ip 亦庄办公网
  source-ip 科技亦庄办公网
  destination-ip viminal工单
  service https
 rule 4 name 协同需求统筹管理工具访问
  description 协同需求统筹管理工具访问
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 中心有线网
  source-ip 中心无线网
  destination-ip 协同需求统筹管理工具
  service tcp_8080
  service tcp_8081
  service tcp_8082
 rule 5 name 开发测试环境DNS服务器访问
  description 开发测试环境DNS服务器访问
  action pass
  disable
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网DNS
  source-ip 运营办公网DNS
  source-ip 骏彩办公网DNS
  source-ip 运营中心VDIDNS
  destination-ip 开发测试环境DNS服务器
  service tcp_53
 rule 8 name 竞猜开发测试访问
  description 竞猜开发测试访问
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  destination-ip 竞猜开发测试
 rule 9 name "自动化测试MAIL 服务器访问科技、运营和骏彩办公网MAIL服务器"
  description 自动化测试MAIL服务器访问科技、运营和骏彩办公网MAIL服务器
  action pass
  logging enable
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip "自动化测试MAIL 服务器"
  destination-ip 科技办公网MAIL服务器
  destination-ip 运营办公网MAIL服务器
  destination-ip 骏彩办公网MAIL服务器
  service tcp_25
 rule 12 name 桌面云VDI桌面访问运营公司百环办公区maven
  description 过渡阶段（目标系统迁移后删除）
  action pass
  logging enable
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 桌面云VDI（**********）
  destination-ip 运营公司百环办公区maven
  service tcp_10082
 rule 17 name 国2测试环境matser访问公网
  action pass
  disable
  counting enable
  source-ip ***********
  destination-ip ***********
  service https
  service http
 rule 26 name 访问办公网邮箱服务
  action pass  
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 访问办公网邮箱服务
 rule 27 name 访问扫码终端二中心IDC
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 访问扫码终端二中心IDC
 rule 28 name 访问coding-devops
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 访问coding-devops
 rule 29 name 访问CSLO邮件服务器1
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 访问CSLO邮件服务器
 rule 30 name 访问国家实验室
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 访问国家实验室
 rule 31 name 访问UATtest
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 访问UATtest
 rule 32 name 访问ump效验
  action pass
  disable      
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 访问ump效验
 rule 34 name 研创中心到堡垒机
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
  source-ip 运营办公网
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip 研创中心到堡垒机
 rule 49 name 办公网BT终端访问开发测试环境
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网鹏龙BT终端
  destination-ip 开发测试环境-BT终端服务01
  destination-ip 开发测试环境-BT终端服务02
  service TCP30000
  service TCP8443
 rule 48 name 办公网BT终端访问开发测环境NTP
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网鹏龙BT终端
  destination-ip 开发测试环境-BT终端NTP
  service ntp
 rule 47 name 办公网BT终端访问开发测试环境FTP
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网鹏龙BT终端
  destination-ip 开发测试环境-BT终端FTP
  service ftp
 rule 37 name BT终端访问开发业务
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  destination-ip BT终端访问开发业务目的地址
  service BT终端访问开发业务端口
 rule 64 name 科技办公网安卓服务器访问编译服务
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网-安卓编译务器
  destination-ip 开发测试环境-办公网访问安卓服务
 rule 65 name 科技办公网访问开发SVN01
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网-SVN访问源地址
  destination-ip 开发测试环境-科技开发SVN01
  service TCP8088
 rule 51 name 科技办公网数据中台访问开发测试环境02
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网-测试数据中台
  destination-ip 开发测试环境-办公数据中台访问05
  service TCP22
 rule 50 name 科技办公网数据中台访问开发测试环境
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网-测试数据中台
  destination-ip 开发测试环境-办公数据中台访问01
  destination-ip 开发测试环境-办公数据中台访问03
  destination-ip 开发测试环境-办公数据中台访问02
  destination-ip 开发测试环境-办公数据中台访问04
  destination-ip 开发测试环境-办公数据中台访问06
  destination-ip 开发测试环境-办公数据中台访问07
  destination-ip 开发测试环境-办公数据中台访问08
  destination-ip ************-66
  service TCP10000
  service TCP1521
  service TCP3306
  service TCP2883
  service TCP8083
  service TCP8889
  service TCP21050
  service TCP8020
  service TCP21051
  service TCP_2181
 rule 67 name 科技办公网HADOOP访问开发测试PMC归集库
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网-测试HADOOP
  source-ip 科技办公网-测试数据中台
  destination-ip 开发测试环境-办公访问PMC归集库
  service TCP2883
  service TCP9201
  service ping
 rule 46 name 运营办公访问开发测试环境
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 运营办公网
  destination-ip 开发测试环境-运营UMP管理端
  destination-ip 开发测试环境-运营堡垒机
  destination-ip 开发测试环境-运营访问G3售票网关
  destination-ip 开发测试环境-运营访问网络
  destination-ip 开发测试环境-运营访问主机
  destination-ip 开发测试环境-运营文档访问
  destination-ip 开发测试环境-运营虚机
  destination-ip 开发测试环境-运营知识库
  destination-ip 开发测试环境-运营终端更新服务器
 rule 53 name 安全月互联网访问
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip 云峰为格亮梳理安全月
  destination-ip ***********
  service 开发测试环境-互联网访问服务
  service https
  service http
  service TCP8443
  service tcp_8081
  service tcp_8082
  service ntp
 rule 58 name 北单测试网络业务测试01
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 北单测试网络
  destination-ip 开发测试环境-北单访问业务01
 rule 59 name 北单测试网络业务测试02
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 北单测试网络
  destination-ip 开发测试环境-北单访问业务02
  service TCP3389
 rule 60 name 北单测试网络业务测试03
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 北单测试网络
  destination-ip 开发测试环境-北单访问业务03
  service tcp_8080
  service 80
 rule 158 name 高德测试环境到G32环境
  description 马征20231016需求
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 北单测试网络
  destination-ip **************
  service tcp_8443
 rule 175 name 高德测试访问新建数据中台优化后kafka集群
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  source-ip ************
  destination-ip *************-105
  service TCP9092
 rule 150 name 高德测试环境到数据中台G31测试环境kafka
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 北单测试网络
  destination-ip ***********-15
  service TCP9092
 rule 205 name G32销服访问北单
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip ************
  destination-ip ************
  service http
 rule 197 name G31和G34的unit1&unit4的worker到北单接入地址
  action pass  
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip **********/24
  source-ip **********
  source-ip ***********
  destination-ip ************
  service http
 rule 196 name G31环境访问北单服务器
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip ************
  destination-ip ************
  service TCP8001
 rule 74 name 北单测试网络业务测试anytest
  action pass
  disable
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 北单测试网络
 rule 81 name 办公到测试环境数据中台数据库
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技亦庄办公网
  destination-ip 开发测试环境-办公数据中心访问09
  service TCP2181
 rule 61 name 经营分析系统
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技亦庄办公网-即开经营分析系统
  destination-ip 开发测试环境-经营分析系统
  service TCP2883
  service TCP30010
 rule 68 name 湖南专线访问实验室服务器
  action pass
  disable      
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 湖南专线
  destination-ip 湖南专线访问实验室
 rule 75 name 喻坤需求20220815
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 喻坤需求源地址20220815
  destination-ip 喻坤需求目的地址20220815
 rule 78 name 胡刚需求20220822
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip 胡刚需求20220822
 rule 80 name G3需求20220818
  action pass
  disable      
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip ************
 rule 66 name 互联网访问USAP
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip "科技办公网F5 SNAT+MONITOR"
  destination-ip 开发测试环境-互联网访问USAP
  service https
 rule 84 name 20220906需求
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip **************
  destination-ip *************/24
 rule 89 name 云峰需求20221011
  action pass  
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip ************
 rule 90 name 20221012
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  destination-ip **********
 rule 91 name 合规监管
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip ***************
 rule 92 name ***********->***********
  action pass
  disable      
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********
  destination-ip ***********
  destination-ip ************
  destination-ip ***************
 rule 94 name 云峰需求20221026（1）
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip *************、221
 rule 87 name 访问建行
  action pass
  logging enable
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip ***********
  destination-ip 建行
 rule 97 name 20221027
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip *************
 rule 99 name 20221108
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************
  destination-ip **********
 rule 113 name 20230112
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  destination-ip *************00
  destination-ip ***************
 rule 119 name 20230206
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip **********
  destination-ip *************
  destination-ip ***********
 rule 120 name 20230213
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip **********
  destination-ip **********
 rule 121 name node集群访问实验室
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  source-ip ************
  destination-ip ************
  destination-ip **********
  destination-ip ************
  destination-ip ***********
 rule 124 name 20230403
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip ***********
 rule 130 name 总局访问VDI
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********
  source-ip ************
  source-ip ************
  source-ip ************
  source-ip ************
  source-ip ************
  destination-ip ***********
 rule 71 name 统一门户
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技办公网F5selfip
  destination-ip ***********
  service https
  service http
  service TCP9080
 rule 105 name 湖北农信访问策略
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 湖北农信源地址
  destination-ip 湖北农信目的地址
 rule 106 name 20221116
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********
  destination-ip *************
 rule 114 name 访问协同财务处系统管理
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 中心有线网
  source-ip 中心无线网
  source-ip ************
  destination-ip 协同财务处系统管理工具
  service 80
  service http
  service https
  service TCP22
 rule 100 name 综合运营中心VDI访问桌面云
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 综合运营中心VDI瘦客户端网段
  destination-ip "桌面云connect server"
  service TCP-384
  service TCP-44
  service TCP-434
  service TCP-172
  service 4172
  service 22443
  service 9427
  service 32111
  service tcp_8443
  service UDP8443
  service https
 rule 108 name 运营VDI访问打印机
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 开发测试综合运营中心VDI网段
  destination-ip 运营虚拟化打印机
 rule 102 name 开发测试亦庄域控访问VDI-VC
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 开发测试亦庄域控
  destination-ip 综合运营中心VDI-VC
 rule 118 name 运营虚拟化文件服务器数据同步-2
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip *************
  source-ip ************
  source-ip ************
  source-ip ************-**************
  destination-ip ************
  destination-ip *************-122
 rule 116 name vdi访问新环境文件服务器
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************-***************
  source-ip ***********
  source-ip ***************-202
  source-ip ***************-202
  source-ip ***************-202
  destination-ip *************
  service 135-139
  service TCP-445
 rule 38 name 亦庄办公网访问测试环境
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 中心无线网
  source-ip 中心有线网
  destination-ip **********/16
  destination-ip **********/16
  destination-ip **********/24
  destination-ip ************/24
  destination-ip **********/16
 rule 135 name 应急VPN
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
 rule 152 name 统一开发测试环境业务系统
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***************
  source-ip ***********
  destination-ip ***********
  destination-ip ***********
  destination-ip ************-33
  service https
  service TCP30080
  service "TCP 30088"
  service tcp_53
  service TCP9080
 rule 180 name lousao
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  destination-ip lousao
 rule 1 name Trust→Untrust_1_IPv4
  counting enable
  source-zone Trust
  destination-zone Untrust
 rule 0 name Untrust→Trust_0_IPv4
  counting enable
  source-zone Untrust
  destination-zone Trust
 rule 6 name 腾讯云测试环境HOST访问开发测试环境测试Server
  description 腾讯云测试环境HOST访问开发测试环境测试Serve
  action pass
  logging enable
  counting enable
  source-zone Untrust
  source-zone Trust
  destination-zone Trust
  destination-zone Untrust
  source-ip 腾讯云测试环境HOST
  source-ip 开发测试环境测试Server
  destination-ip 腾讯云测试环境HOST
  destination-ip 开发测试环境测试Server
 rule 7 name 桌面云VDI桌面访问开发测试环境测试Server
  description 桌面云VDI桌面访问开发测试环境测试Server
  action pass
  logging enable
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 桌面云VDI桌面
  destination-ip 开发测试环境测试Server
 rule 21 name 翌景访问虚拟化平台
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 翌景访问虚拟化平台（源）
  destination-ip 翌景访问虚拟化平台（目的）
 rule 23 name 访问sementepro
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 亦庄办公网
  source-ip 骏彩办公网
  source-ip 运营办公网
  source-ip 印务办公网
  source-ip 中心无线网
  source-ip 中心有线网
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙无线网
  source-ip 科技翌景无线网
  source-ip 科技鹏龙有线网
  source-ip 科技翌景有线网
  source-ip 综合运营中心VDI网段
  source-ip 综合运营中心业务运营老网段
  source-ip 科技亦庄办公网
  destination-ip 访问sementepro
 rule 13 name 桌面云VDI桌面访问运营公司百环办公区Sonar
  description 过渡阶段（目标系统迁移后删除）
  action pass
  logging enable
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 桌面云VDI（**********）
  destination-ip 运营公司百环办公区Sonar
  service tcp_10081
 rule 16 name 国1测试环境需要访问北京彩票销售管理系统
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip 国1骏彩跳板机
  destination-ip 北京彩票销售管理系统数据同步
  destination-ip 北京彩票销售管理系统接入ip
  service tcp_8080
 rule 24 name 总局中心七层706会议室
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip ***********
 rule 22 name 访问国二环境（2台虚机机)
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 访问国二环境（2台虚机机)（源）
  destination-ip 访问国二环境（2台虚机机目的）
 rule 25 name 访问csl-svn
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 中心有线网
  source-ip 中心无线网
  source-ip 运营办公网
  source-ip 运营办公网DNS
  source-ip 运营办公网MAIL服务器
  source-ip 运营公司百环办公区maven
  source-ip 运营公司百环办公区Sonar
  source-ip 科技翌景有线网
  source-ip 科技翌景无线网
  source-ip 科技鹏龙有线网
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙21层有线网
  source-ip 科技鹏龙20层有线网
  source-ip 科技二中心有线网
  source-ip 科技二中心无线网
  source-ip 骏彩办公网
  source-ip 骏彩办公网DNS
  source-ip 科技办公网DNS
  source-ip 骏彩办公网MAIL服务器
  source-ip 科技办公网MAIL服务器
  destination-ip 访问sementepro
 rule 39 name 骏彩办公网访问实验环境
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩办公网
 rule 79 name 李景一需求20220823
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 李景一需求源地址20220823
  destination-ip 李景一需求目的地址20220823
 rule 126 name 亦庄办公网访问测试存储
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********
  destination-ip *************/24
  service TCP22
  service TCP8088
  service tcp_8080
 rule 115 name 20230202
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip **********
  destination-ip ***********
 rule 93 name 李景一需求20221024
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 李景一需求源地址20221024
  destination-ip 李景一需求目的地址20221024
 rule 76 name 办公网访问测试NAS
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 办公网访问NAS地址
  destination-ip 测试NAS地址
  service "NAS service port"
 rule 70 name 李景一需求20220811（2）
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 李景一需求20220811源地址2
  source-ip 李景一需求20220811源地址3
  destination-ip 李景一需求20220811目的地址2
 rule 63 name G32环境数据中台网络
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip G32环境数据中台网络
  destination-ip G32环境数据中台网络目的地址
 rule 33 name 百环访问二中心实验室虚拟机
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 运营办公网
  destination-ip 百环二中心实验室虚拟机
 rule 82 name 刘栋-网络互联需求20220901
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 刘栋20220901需求源地址
  destination-ip 刘栋20220901需求目的地址
 rule 69 name 李景一需求20220811（1）
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 李景一需求20220811源地址1
  destination-ip 李景一需求20220811目的地址1
 rule 85 name 密服项目测试环境建设网络需求
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 密服测试环境建设网络需求源地址
  destination-ip 密服测试环境建设需求目的地址
 rule 73 name 李景一需求20220812
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 李景一需求20220812源地址
  destination-ip 李景一需求20220812目的地址
 rule 83 name 尹健需求20220902
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 尹健需求源地址20220902
  destination-ip 尹健需求目的地址20220902
 rule 95 name 云峰需求20221026（2）
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
 rule 96 name 20221026
  action pass  
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip **********-2
  destination-ip ***************
 rule 103 name 开发测试综合运营中心VDI访问RMOAS
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 开发测试综合运营中心VDI网段
  destination-ip RMOAS
  service 80
  service TCP-1433
 rule 77 name 数字化中心三端新环境网络需求
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 数字化中心三端新环境源地址
  destination-ip 数字化中心三端新环境目的地址
 rule 86 name 骏彩魏琨昱需求20220908
  action pass  
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 骏彩魏琨昱需求源地址
  destination-ip 骏彩魏琨昱需求目的地址
 rule 88 name 李景一需求20220914
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip *************
 rule 98 name 20221107
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********
  destination-ip ************
 rule 128 name 20230505
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************、9、38
  destination-ip **************
 rule 101 name 综合运营中心VDI-VC访问域控
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 综合运营中心VDI-VC
  destination-ip 开发测试亦庄域控
 rule 110 name 20221226
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip *************
  destination-ip **********
 rule 117 name 运营虚拟化文件服务器数据同步-1
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
  destination-ip *************
 rule 104 name 综合运营中心VDI访问运营支撑文件服务器
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 开发测试综合运营中心VDI网段
  destination-ip **********
 rule 122 name 骏彩T1访问**********
  description ***********-***********
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-ip ***********
  service 34443
 rule 44 name 印务办公网访问实验环境
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 印务办公网
 rule 10 name 科技鹏龙20层有线网访问
  description 科技鹏龙20层有线网访问
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技鹏龙20层有线网
 rule 109 name 运营虚拟化打印机到VDI
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 运营虚拟化打印机
  destination-ip 开发测试综合运营中心VDI网段
 rule 14 name 互联网业务访问网络
  description LOG(过渡期策略梳理,梳理后补上源地址）
  action pass
  logging enable
  counting enable
  source-zone Trust
  destination-zone Untrust
  destination-ip 互联网业务访问网络
 rule 52 name 运营公司办公网访问实验环境02
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 运营办公网
  source-ip 运营办公网DNS
  source-ip 运营办公网MAIL服务器
  source-ip 运营公司百环办公区maven
  source-ip 运营公司百环办公区Sonar
  source-ip 运营中心VDI白名单
 rule 43 name 运营公司办公网访问实验环境
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 运营办公网
 rule 40 name 射击场访问实验环境
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技二中心无线网
  source-ip 科技二中心有线网
 rule 41 name 鹏龙办公网访问实验环境
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技鹏龙无线网
  source-ip 科技鹏龙有线网
 rule 42 name 翌景办公网访问实验环境
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技翌景无线网
  source-ip 科技翌景有线网
 rule 107 name 20221121
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************
 rule 11 name 科技鹏龙21层有线网访问
  description 科技鹏龙20层有线网访问
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 科技鹏龙21层有线网
 rule 55 name 印务办公网访问开发测试环境01
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 印务办公网
  destination-ip 开发测试环境-印务访问业务
  service TCP2883
 rule 54 name 印务办公网访问实验环境01
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 印务办公网
  destination-ip 开发测试环境-印务访问网络
 rule 125 name 运营公司至测试mail
  action pass  
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***************-102
  destination-ip **********
  service tcp_25
 rule 154 name 鹏龙BT访问DNS
  description ************/24 鹏龙BT访问DNS
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ************/24
  destination-ip 新建DNS服务器
  service icmp-info
  service icmp-host-unreachable
  service icmp-fragment-reassembly
  service icmp-fragment-needed
  service icmp-parameter-problem
  service icmp-port-unreachable
  service icmp-protocol-unreach
  service icmp-redirect
  service icmp-redirect-host
  service icmp-redirect-tos-host
  service icmp-redirect-tos-net
  service icmp-source-quench
  service icmp-source-route-fail
  service icmp-time-exceeded
  service icmp-timestamp
  service icmp-traceroute
  service icmp-address-mask
  service icmp-dest-unreachable
  service dns-tcp
  service dns-udp
 rule 123 name ca-to-outside
  counting enable
  source-zone Trust
  destination-zone Untrust
 rule 35 name 480_CY_JIANHANG
  destination-ip ***********
 rule 45 name 亦庄办公网访问any
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip 亦庄办公网
 rule 178 name K8SNODE-to-DUANXIN
  action pass
  source-zone TENANT02_CORE_Inside
  destination-zone TENANT02_CORE_Outside
  source-ip V3_CORE_K8SNODE_**********/24
  source-ip V3_CORE_K8SNODE_4.190.85.0/24
  source-ip V3_CORE_K8SNODE_4.190.86.0/24
  source-ip V3_CORE_**********/22
  destination-ip ************
  service http
 rule 179 name K8SNODE-to-URSF5-30514
  action pass
  source-zone TENANT02_GW_Outside
  destination-zone TENANT02_GW_Inside
  source-ip **********/22
  source-ip **********/22
  destination-ip ************
  service TCP_30514
 rule 199 name AOPS-to-MySQLMHA
  action pass
  source-zone TENANT02_CORE_Outside
  destination-zone TENANT02_CORE_Inside
  source-ip ************-36
  destination-ip ************-104
  destination-ip SBSG2GRSDS01-***********
  service ssh
#
security-policy ipv6
#
ips logging parameter-profile ips_logging_default_parameter
#
anti-virus logging parameter-profile av_logging_default_parameter
#
return

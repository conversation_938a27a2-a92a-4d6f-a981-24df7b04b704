[BEGIN] 2023/12/1 16:12:01
<XWCAFW01-B4F08>dis cur
#
 version 7.1.064, Release 9616P23
#
 sysname XWCAFW01-B4F08
#
 clock timezone beijing add 08:00:00
#
context Admin id 1
#
 telnet server enable
#
 irf mac-address persistent timer
 irf auto-update enable
 undo irf link-delay
 irf member 1 priority 1
 irf member 2 priority 1
#
 lacp system-priority 100
#
track 3 interface Route-Aggregation3
#
track 5 interface Route-Aggregation1
#
track 8 interface Route-Aggregation4
#
track 10 interface Route-Aggregation2
#
 lldp global enable
#
 password-recovery enable
#
vlan 1
#
irf-port 1/1
 port group interface GigabitEthernet1/0/2
 port group interface GigabitEthernet1/0/3
#
irf-port 2/2
 port group interface GigabitEthernet2/0/2
 port group interface GigabitEthernet2/0/3
#
object-group ip address ***********-172
 security-zone Untrust
 0 network range *********** *********72
#
object-group ip address ************-93
 security-zone Trust
 0 network range ************ ************
#
object-group ip address ************
 0 network host address ************
#
object-group ip address citrix??
 security-zone Untrust
 0 network subnet ********* *************
 10 network subnet ********* *************
#
object-group ip address local-ip
 0 network host address ************
 20 network host address **********
 30 network host address *************
 40 network host address **********
 50 network host address *************
 60 network host address *************
 70 network host address ************
 80 network host address ************
 90 network host address ************
 100 network host address ************
 110 network host address ************
#              
object-group ip address lousao
 0 network subnet ******** *************
 5 network subnet ******** *************
 10 network subnet ******** *************
 15 network subnet *********** *************
#
object-group ip address manage
 0 network subnet ********* *************
 10 network subnet ********* *************
 20 network subnet ********* ***********
 30 network host address *********0
#
object-group ip address newntp
 0 network host address *******
 10 network host address *******
#
object-group ip address newpkiippool
 0 network range *********** ************
 20 network subnet ******** *************
 30 network subnet ******** *************
#
object-group ip address PKI??IP
 security-zone Trust
 0 network subnet ******** *************
 10 network subnet *********** *************
#
object-group ip address 1??? 
 security-zone Trust
 0 network subnet *********** *************
#
object-group service NAS1?
 description NAS1?
 0 service tcp destination eq 111
 10 service udp destination eq 111
 20 service tcp destination eq 2049
 30 service udp destination eq 2049
 40 service tcp destination eq 635
 50 service udp destination eq 635
 60 service tcp destination range 4045 4049
 70 service udp destination range 4045 4049
 80 service icmp
#
object-group service NBU??
 0 service tcp destination eq 1556
 10 service tcp destination eq 13724
 20 service tcp destination eq 13782
 30 service tcp destination eq 13720
#
object-group service tcp-10050
 0 service tcp destination eq 10050
#
object-group service tcp-10051
 0 service tcp destination eq 10051
#
object-group service TCP-10289
 0 service tcp destination eq 10289
#
object-group service TCP-11001
 0 service tcp destination eq 11001
#
object-group service TCP-11002
 0 service tcp destination eq 11002
#
object-group service tcp-111
 0 service tcp destination eq 111
#
object-group service TCP-1443
 0 service tcp destination eq 1443
#              
object-group service TCP-1521
 0 service tcp destination eq 1521
#
object-group service TCP-17060
 0 service tcp destination eq 17060
#
object-group service TCP-17080
 0 service tcp destination eq 17080
#
object-group service TCP-17090
 0 service tcp destination eq 17090
#
object-group service TCP-18060
 0 service tcp destination eq 18060
#
object-group service TCP-18443
 0 service tcp destination eq 18443
#
object-group service tcp-22
 0 service tcp destination eq 22
#
object-group service TCP-3306
 0 service tcp destination eq 3306
#
object-group service TCP-3389
 0 service tcp destination eq 3389
#
object-group service TCP-389
 0 service tcp destination eq 389
#
object-group service TCP-443
 0 service tcp destination eq 443
#
object-group service TCP-4444
 0 service tcp destination eq 4444
#
object-group service TCP-6006
 0 service tcp destination eq 6006
#
object-group service TCP-623
 0 service tcp destination eq 623
#
object-group service TCP-7809-7849
 0 service tcp destination range 7809 7849
#
object-group service TCP-8080
 0 service tcp destination eq 8080
#
object-group service TCP-8088
 0 service tcp destination eq 8088
#
object-group service TCP-8443
 0 service tcp destination eq 8443
#
object-group service TCP-8888-8889
 0 service tcp destination range 8888 8889
#
object-group service TCP-8999
 0 service tcp destination eq 8999
#
object-group service TCP-9080
 0 service tcp destination eq 9080
#
object-group service TCP-9998-9999
 0 service tcp destination range 9998 9999
#
object-group service TCP_8081
 0 service tcp destination eq 8081
#              
object-group service UDP-10050-10051
 0 service udp destination range 10050 10051
#
object-group service UDP-111
 0 service udp destination eq 111
#
object-group service UDP-161
 0 service udp destination eq 161
#
object-group service UDP-162
 0 service udp destination eq 162
#
object-group service UDP-514
 0 service tcp destination eq 514
#
object-group service UDP-623
 0 service tcp destination eq 623
#
object-group service UDP-8514
 0 service udp destination eq 8514
#
object-group service UDP-8516
 0 service udp destination eq 8516
#
interface Reth1
 description link-up
 ip address ************ ***************
 member interface Route-Aggregation1 priority 200
 member interface Route-Aggregation2 priority 100
#
interface Reth2
 description link-up
 ip address ************ ***************
 member interface Route-Aggregation3 priority 200
 member interface Route-Aggregation4 priority 100
#
interface Reth3
 description link-up PKICA
 ip address ************ ***************
 member interface Route-Aggregation5 priority 200
 member interface Route-Aggregation6 priority 100
#
interface Reth4
 description link-up PKIKM
 ip address ************ ***************
 member interface Route-Aggregation7 priority 200
 member interface Route-Aggregation8 priority 100
#
interface Bridge-Aggregation1
 description link-vlan-1001
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 1001
 link-aggregation mode dynamic
 link-aggregation selected-port maximum 2
#
interface Bridge-Aggregation2
 description link-vlan-313
 port link-type trunk
 undo port trunk permit vlan 1
 port trunk permit vlan 313
 link-aggregation mode dynamic
 link-aggregation selected-port maximum 2
#
interface Route-Aggregation1
 description link-up-1
#
interface Route-Aggregation2
 description link-up-2
#
interface Route-Aggregation3
 description XWPASW-B4F08
#
interface Route-Aggregation4
 description XWPASW-B4F08
#
interface Route-Aggregation5
 description XW-PKICA-SW01
#
interface Route-Aggregation6
 description XW-PKICA-SW01
#
interface Route-Aggregation7
 description XW-PKIKM-SW01
#
interface Route-Aggregation8
 description XW-PKIKM-SW01
#
interface Route-Aggregation63
 description bfd-mad
 mad bfd enable
 mad ip address *************** ************* member 1
 mad ip address *************** ************* member 2
#
interface NULL0
#
interface GigabitEthernet1/0/0
 port link-mode route
 combo enable copper
#
interface GigabitEthernet1/0/1
 port link-mode route
 combo enable fiber
 port link-aggregation group 63
#
interface GigabitEthernet1/5/0
 port link-mode route
 port link-aggregation group 3
#
interface GigabitEthernet1/5/1
 port link-mode route
 port link-aggregation group 3
#
interface GigabitEthernet1/5/2
 port link-mode route
#
interface GigabitEthernet1/5/3
 port link-mode route
#
interface GigabitEthernet1/5/4
 port link-mode route
#
interface GigabitEthernet1/5/5
 port link-mode route
#
interface GigabitEthernet1/5/6
 port link-mode route
#
interface GigabitEthernet1/5/7
 port link-mode route
#
interface GigabitEthernet2/0/0
 port link-mode route
 combo enable copper
 ip address ************* *************
#
interface GigabitEthernet2/0/1
 port link-mode route
 combo enable fiber
 port link-aggregation group 63
#
interface GigabitEthernet2/5/0
 port link-mode route
 port link-aggregation group 4
#
interface GigabitEthernet2/5/1
 port link-mode route
 port link-aggregation group 4
#
interface GigabitEthernet2/5/2
 port link-mode route
#
interface GigabitEthernet2/5/3
 port link-mode route
#
interface GigabitEthernet2/5/4
 port link-mode route
#
interface GigabitEthernet2/5/5
 port link-mode route
#              
interface GigabitEthernet2/5/6
 port link-mode route
#
interface GigabitEthernet2/5/7
 port link-mode route
#
interface GigabitEthernet1/0/2
 combo enable fiber
#
interface GigabitEthernet1/0/3
 combo enable fiber
#
interface GigabitEthernet2/0/2
 combo enable fiber
#
interface GigabitEthernet2/0/3
 combo enable fiber
#
interface Ten-GigabitEthernet1/1/0
 port link-mode route
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/1/1
 port link-mode route
 port link-aggregation group 1
#
interface Ten-GigabitEthernet1/1/2
 port link-mode route
 port link-aggregation group 5
#
interface Ten-GigabitEthernet1/1/3
 port link-mode route
 port link-aggregation group 5
#
interface Ten-GigabitEthernet1/1/4
 port link-mode route
 port link-aggregation group 7
#
interface Ten-GigabitEthernet1/1/5
 port link-mode route
 port link-aggregation group 7
#
interface Ten-GigabitEthernet1/1/6
 port link-mode route
#
interface Ten-GigabitEthernet1/1/7
 port link-mode route
#
interface Ten-GigabitEthernet2/1/0
 port link-mode route
 port link-aggregation group 2
#
interface Ten-GigabitEthernet2/1/1
 port link-mode route
 port link-aggregation group 2
#
interface Ten-GigabitEthernet2/1/2
 port link-mode route
 port link-aggregation group 6
#
interface Ten-GigabitEthernet2/1/3
 port link-mode route
 port link-aggregation group 6
#
interface Ten-GigabitEthernet2/1/4
 port link-mode route
 port link-aggregation group 8
#
interface Ten-GigabitEthernet2/1/5
 port link-mode route
 port link-aggregation group 8
#
interface Ten-GigabitEthernet2/1/6
 port link-mode route
#
interface Ten-GigabitEthernet2/1/7
 port link-mode route
#
security-zone name Local
#
security-zone name Trust
 import interface GigabitEthernet1/5/0
 import interface GigabitEthernet1/5/1
 import interface GigabitEthernet2/5/0
 import interface GigabitEthernet2/5/1
 import interface Reth2
 import interface Reth3
 import interface Route-Aggregation3
 import interface Route-Aggregation4
 import interface Route-Aggregation5
 import interface Route-Aggregation6
 import interface Ten-GigabitEthernet1/1/2
 import interface Ten-GigabitEthernet1/1/3
 import interface Ten-GigabitEthernet2/1/2
 import interface Ten-GigabitEthernet2/1/3
 import interface Bridge-Aggregation1 vlan 1001
 import interface Bridge-Aggregation2 vlan 313
#
security-zone name DMZ
#
security-zone name Untrust
 import interface Reth1
 import interface Route-Aggregation1
 import interface Route-Aggregation2
 import interface Ten-GigabitEthernet1/1/0
 import interface Ten-GigabitEthernet1/1/1
 import interface Ten-GigabitEthernet2/1/0
 import interface Ten-GigabitEthernet2/1/1
#
security-zone name Management
 import interface GigabitEthernet1/0/0
 import interface GigabitEthernet2/0/0
#
security-zone name PKIKM
 import interface Reth4
 import interface Route-Aggregation7
 import interface Route-Aggregation8
 import interface Ten-GigabitEthernet1/1/4
 import interface Ten-GigabitEthernet1/1/5
 import interface Ten-GigabitEthernet2/1/4
 import interface Ten-GigabitEthernet2/1/5
#
 scheduler logfile size 16
#
line class console
 authentication-mode scheme
 user-role network-admin
#
line class vty
 user-role network-operator
#
line con 0 1
 user-role network-admin
#
line vty 0 63
 authentication-mode scheme
 user-role network-admin
#              
 ip route-static 0.0.0.0 0 ************
 ip route-static ******** 24 ************
 ip route-static ******** 24 ************
 ip route-static ******** 24 ************
 ip route-static *********** 24 ************
 ip route-static *********** 24 ************
#
 info-center logbuffer size 1024
 info-center loghost source Reth1
 info-center loghost ************ facility local6
 info-center loghost ************
 info-center loghost *********** facility local6
#
 snmp-agent
 snmp-agent local-engineid 800063A280542BDEE3CA7600000001
 snmp-agent sys-info version all 
 snmp-agent target-host trap address udp-domain *********** params securityname cslc_snmp v2c
 snmp-agent target-host trap address udp-domain ************ params securityname cslc_snmp v2c
 snmp-agent target-host trap address udp-domain ************ params securityname cslc_snmp v2c
 snmp-agent trap source Reth1
#
 ssh server enable
#              
redundancy group aaa
 member interface Reth1
 member interface Reth2
 node 1
  bind slot 1
  priority 100
  track 3 interface Route-Aggregation3
  track 5 interface Route-Aggregation1
 node 2
  bind slot 2
  priority 50
  track 8 interface Route-Aggregation4
  track 10 interface Route-Aggregation2
#
 ntp-service enable
 ntp-service source Reth1
 ntp-service unicast-server *******
#
domain system
#
 domain default enable system
#
role name level-0
 description Predefined level-0 role
#
role name level-1
 description Predefined level-1 role
#
role name level-2
 description Predefined level-2 role
#
role name level-3
 description Predefined level-3 role
#
role name level-4
 description Predefined level-4 role
#
role name level-5
 description Predefined level-5 role
#
role name level-6
 description Predefined level-6 role
#
role name level-7
 description Predefined level-7 role
#              
role name level-8
 description Predefined level-8 role
#
role name level-9
 description Predefined level-9 role
#
role name level-10
 description Predefined level-10 role
#
role name level-11
 description Predefined level-11 role
#
role name level-12
 description Predefined level-12 role
#
role name level-13
 description Predefined level-13 role
#
role name level-14
 description Predefined level-14 role
#
user-group system
#              
local-user netadmin class manage
 password hash $h$6$lUtMuVH0umvVBv7E$bGV55xfI7g2GeXXjP8tr0w2gQ4bMLPLlIitM8jHbw2u7EJF1XMkgi6qFZwGCEBe0yTWM8np8grm1PoQnzUdprA==
 service-type ssh telnet terminal http https
 authorization-attribute user-role network-admin
 authorization-attribute user-role network-operator
#
 ipsec logging negotiation enable
#
 ike logging negotiation enable
#
 ip http enable
 ip https enable
 webui log enable
#
inspect logging parameter-profile av_logging_default_parameter
#
inspect logging parameter-profile ips_logging_default_parameter
#
inspect logging parameter-profile url_logging_default_parameter
#
security-policy ip
 rule 46 name lousao
  action pass  
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-zone PKIKM
  destination-zone Management
  source-ip ************
  destination-ip lousao
 rule 1 name ping
  action pass
  disable
  counting enable
  service ping
 rule 0 name management
  action pass
  counting enable
  source-zone Untrust
  source-zone Trust
  destination-zone Local
  destination-zone Trust
  destination-zone PKIKM
  source-ip-host ********* 
  source-ip-host ********* 
  destination-ip local-ip
  destination-ip-host ********0 
  destination-ip-host ********0 
  destination-ip-host ********1 
  destination-ip-host ********0 
  destination-ip-host ********* 
  destination-ip-range ******** ******** 
  destination-ip-range ******** ******** 
  destination-ip-range ******** ******** 
  destination-ip-range ******** ******** 
  destination-ip-range *********** ************ 
  service http
  service https
  service ssh
  service telnet
  service ping
  service TCP-18060
  service TCP-17080
  service TCP-3306
  service TCP-17090
  service TCP-1521
 rule 3 name ntp
  action pass  
  counting enable
  source-zone Trust
  source-zone Local
  source-zone PKIKM
  destination-zone Untrust
  source-ip 1??? 
  source-ip newpkiippool
  destination-ip newntp
  destination-ip-host ******* 
  destination-ip-host ******* 
 rule 5 name ??§æ¡¤??t?§æPKI???
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-range ********* ********* 
  destination-ip-range ********* ********* 
  service TCP-7809-7849
 rule 6 name ?t?§æ¡¤??¬DKI???
  action pass
  disable
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-range ********* ********* 
  destination-ip-range ********* ********* 
  service TCP-7809-7849
 rule 7 name NBU¡¤??KI???
  action pass
  disable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-host ******** 
  source-ip-host ******** 
  source-ip-host ******** 
  destination-ip-host ********* 
  destination-ip-host ********* 
  service NBU??
 rule 8 name PKI?????NBU
  action pass
  disable
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-host ********* 
  source-ip-host ********* 
  destination-ip-host ******** 
  destination-ip-host ******** 
  destination-ip-host ******** 
  service NBU??
 rule 9 name InsideToOutside
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-subnet ******** ************* 
  source-ip-subnet *********** ************* 
 rule 11 name UMP??¡¤?
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********-172
  source-ip-range ********* *********2 
  source-ip-range *********31 *********34 
  source-ip-range *********51 *********54 
  destination-ip-range ********* ********* 
  service https
 rule 12 name ¡À¡è]??
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip citrix??
  source-ip-range ************ ************ 
  source-ip-range ************ ************ 
  destination-ip-host ********* 
  destination-ip-host ********* 
  service https
  service TCP-3389
 rule 13 name SOC??
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-range *********** ***********6 
  destination-ip-host ********* 
  destination-ip-host ********* 
  destination-ip-host ********* 
  destination-ip-host ********* 
  destination-ip-host ********* 
  destination-ip-host ********* 
  service UDP-8516
  service TCP-8888-8889
  service TCP-8999
  service TCP-9998-9999
  service UDP-10050-10051
  service UDP-8514
 rule 15 name NAS1????
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-range ********* ********* 
  destination-ip-range ********* ********* 
  service NAS1?
 rule 16 name NAS1????
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-range ********* ********* 
  destination-ip-range ********* ********* 
  service NAS1?
 rule 17 name ??zabbix¡¤??KI
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-subnet *********** ************* 
  destination-ip-range ********* ********* 
  destination-ip-range ********* ********* 
  service tcp-10050
 rule 18 name PKI¡¤???zabbix
  action pass
  source-zone Trust
  destination-zone Untrust
  source-ip-range ********* ********* 
  source-ip-range ********* ********* 
  destination-ip-subnet *********** ************* 
  service tcp-10051
 rule 21 name ??
  action pass
  counting enable
  source-zone Local
  source-zone Trust
  source-zone Management
  destination-zone Untrust
  source-ip PKI??IP
  source-ip-host ************ 
  destination-ip-host ************ 
  service syslog
  service snmp-trap
 rule 22 name PKI¡¤??cafee
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-range ********* ********* 
  source-ip-range ********* ********* 
  destination-ip-host 3.9.20.30 
  service TCP-8080
  service TCP-443
  service TCP-1443
 rule 23 name mcafee¡¤??KI
  action pass
  logging enable
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-host 3.9.20.30 
  destination-ip-range ********* ********* 
  destination-ip-range ********* ********* 
  service TCP_8081
 rule 24 name Storage_To_Monitor
  action pass
  counting enable
  source-zone Local
  source-zone Trust
  source-zone Management
  destination-zone Untrust
  source-ip ************-93
  destination-ip-host ************ 
  service syslog
  service snmp-trap
  service ping
 rule 25 name ca-to-outside
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  destination-zone Trust
  source-ip-range ******** ******** 
  destination-ip-host ********** 
  destination-ip-host ********** 
  destination-ip-range ********* ********* 
  destination-ip-range ********** ********** 
  service TCP-6006
  service TCP-389
  service TCP-4444
  service TCP-9080
 rule 26 name ca-to-pkikm
  action pass
  counting enable
  source-zone Trust
  destination-zone PKIKM
  source-ip-range ******** ******** 
  destination-ip-host ********* 
  destination-ip-range ******** ******** 
  service TCP-17090
 rule 27 name pkikm-to-IBMLDAP
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-subnet ******** *************** 
  destination-ip-range ********** ********** 
  service TCP-389
 rule 28 name pkikm-to-ca
  action pass
  counting enable
  source-zone PKIKM
  destination-zone Trust
  source-ip-range ******** ******** 
  destination-ip-range ******** ******** 
  service TCP-3306
 rule 29 name UMP-to-radk
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip ***********-172
  source-ip-range ********* *********2 
  source-ip-range *********31 *********34 
  source-ip-range *********51 *********54 
  destination-ip-host ********1 
  destination-ip-range ******** ******** 
  service TCP-10289
 rule 30 name ZABBIX-to-caguanli
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-zone PKIKM
  source-ip-subnet *********** ************* 
  destination-ip-subnet *********** ************* 
  destination-ip-range ******** ******** 
  destination-ip-range ******** ******** 
  service tcp-10050
 rule 31 name caguanli-to-ZABBIX
  action pass
  counting enable
  source-zone Trust
  source-zone PKIKM
  destination-zone Untrust
  source-ip-subnet *********** ************* 
  source-ip-range ******** ******** 
  source-ip-range ******** ******** 
  destination-ip-subnet *********** ************* 
  service tcp-10051
 rule 32 name caguanli-to-syslog-SNMP
  action pass
  counting enable
  source-zone Local
  source-zone Trust
  source-zone Management
  destination-zone Untrust
  source-ip-subnet *********** ************* 
  destination-ip-host ************ 
  service UDP-162
  service UDP-514
 rule 33 name UMP-to-radk-01
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-range ********* *********2 
  source-ip-range *********31 *********34 
  source-ip-range *********51 *********54 
  source-ip-range *********** *********72 
  destination-ip-host ********0 
  destination-ip-host ********0 
  destination-ip-range ******** ******** 
  service TCP-10289
  service https
 rule 34 name mifu-to-ca
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-range ********* ********* 
  destination-ip-host ********0 
  destination-ip-range ******** ******** 
  service TCP-18443
  service TCP-17060
  service https
  service ssh
  service TCP-17080
 rule 35 name ca-to-mifutuisong
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-range ******** ******** 
  destination-ip-host ********* 
  destination-ip-range ********* ********* 
  service TCP-11001
  service TCP-11002
  service https
  service ssh
 rule 36 name test-to-ca
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-subnet *********** ************* 
  destination-ip-host ********1 
  destination-ip-range ******** ******** 
  service TCP-18443
  service TCP-17080
  service https
  service ssh
 rule 37 name ca-to-pkikm-01
  action pass
  counting enable
  source-zone Trust
  destination-zone PKIKM
  source-ip-range ******** ******** 
  destination-ip-host ********0 
  destination-ip-range ******** ******** 
  service TCP-17090
 rule 38 name jiamishuju
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  destination-zone PKIKM
  source-ip-subnet ********* ************* 
  destination-ip-host ******** 
  destination-ip-host ******** 
  destination-ip-range ************ ************ 
  service UDP-161
 rule 39 name canginx-to-oldca
  action pass
  counting enable
  source-ip-range ******** ******** 
  destination-ip-range ********* ********* 
  service https
 rule 40 name pkikm-to-yum
  action pass
  counting enable
  source-zone Trust
  source-zone PKIKM
  destination-zone Untrust
  source-ip-subnet ******** ************* 
  source-ip-subnet ******** ************* 
  destination-ip-host ************ 
  destination-ip-host ************0 
  service http
 rule 41 name nas-in
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-host ********* 
  destination-ip-range ******** ******** 
 rule 42 name nas-out
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-range ******** ******** 
  destination-ip-host ********* 
 rule 43 name dialing-test
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-subnet *********** ************* 
  destination-ip-host ********0 
  service-port tcp destination eq 17080
  service-port tcp destination eq 18443
 rule 44 name zabbixMonitor-in
  action pass
  counting enable
  source-zone Untrust
  destination-zone Trust
  source-ip-subnet *********** ************* 
  destination-ip-subnet ******** ************* 
  service-port tcp destination eq 10050
 rule 45 name zabbixMonitor-out
  action pass
  counting enable
  source-zone Trust
  destination-zone Untrust
  source-ip-subnet ******** ************* 
  destination-ip-subnet *********** ************* 
  service-port tcp destination eq 10051
#              
ips logging parameter-profile ips_logging_default_parameter
#
anti-virus logging parameter-profile av_logging_default_parameter
#
return

[END] 2023/12/1 16:12:13

import subprocess

# 按顺序执行多个Python文件
H3CFWP_to_execute = ['H3CFWP_西五环PKI.py',
                     'H3CFWP_西五环竞猜01.py',
                     'H3CFWP_西五环竞猜02.py',
                     'H3CFWP_西五环竞猜03.py',
                     'H3CFWP_西五环竞猜04.py']

JTE_to_execute = [
    'JTE_西五环PKI.py',
    'JTE_西五环竞猜01.py',
    'JTE_西五环竞猜02.py',
    'JTE_西五环竞猜03.py',
    'JTE_西五环竞猜04.py'
]

for file in H3CFWP_to_execute:
    subprocess.run(['python', file])

for file in JTE_to_execute:
    subprocess.run(['python', file])

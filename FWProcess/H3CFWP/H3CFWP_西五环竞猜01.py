import os
import json
import re

''' 
统一处理配置文件，不再拆分多个文件 


这段代码首先定义了输入和输出文件的路径。
然后通过调用check_directory函数，检查并创建输出文件夹（如果不存在）。
接下来，依次调用process_zone、process_service、process_rules、process_address和process_nat函数来处理相应的输入文件，并将处理结果写入输出文件中。
最后，输出处理完成的提示信息。
'''


def process_service(input_filepath, output_filepath):
    service = []
    current_service = None

    with open(input_filepath, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()

            if line.startswith('object-group service'):
                if current_service is not None:
                    service.append(current_service)

                service_name = line.split()[-1]
                current_service = {"service": service_name}
            elif re.match(r'\d+ service (tcp|udp)', line):
                protocol, _, port = re.search(r'(tcp|udp) destination (eq|range) (.+)', line).groups()
                dst_port = "dst-port"

                if protocol in current_service:
                    current_service[protocol][dst_port] += " " + port
                else:
                    current_service[protocol] = {dst_port: port}

        if current_service is not None:
            service.append(current_service)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(service, file, ensure_ascii=False, indent=4)



def process_address(input_filepath, output_filepath):
    address = []
    current_address = None

    with open(input_filepath, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()

            if line.startswith('object-group ip address'):
                if current_address is not None:
                    address.append(current_address)
                address_name = line.split(' ')[-1].strip("\"")
                current_address = {
                    'address_name': address_name,
                    'security-zone': None,
                    'ip_address': []
                }
            elif line.startswith('security-zone'):
                if current_address is None:
                    print(f"Unexpected 'security-zone' line: {line}")
                    continue
                zone_name = line.split(' ')[1].strip()
                current_address['security-zone'] = zone_name
            elif line and line[0].isdigit():
                if current_address is None:
                    print(f"Unexpected address line: {line}")
                    continue
                line = re.sub(r'^\d+\s*', '', line)
                if line.startswith('network host address'):
                    ip_value = ' '.join(line.split(' ')[3:]).strip()
                    current_address['ip_address'].append({'type': 'host', 'value': ip_value})
                elif line.startswith('network subnet'):
                    ip_value = ' '.join(line.split(' ')[2:]).strip()
                    current_address['ip_address'].append({'type': 'subnet', 'value': ip_value})
                elif line.startswith('network range'):
                    ip_value = ' '.join(line.split(' ')[2:]).strip()
                    current_address['ip_address'].append({'type': 'range', 'value': ip_value})
            elif line.startswith('#'):
                if current_address is not None:
                    address.append(current_address)
                current_address = None

    if current_address is not None:
        address.append(current_address)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(address, file, ensure_ascii=False, indent=4)


def process_zone(input_filepath, output_filepath):
    zones = []

    zone_pattern = r'security-zone name (\w+)'

    with open(input_filepath, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()

            zone_match = re.match(zone_pattern, line)

            if zone_match:
                zone_name = zone_match.group(1)
                zone_data = {"zone_name": zone_name}
                zones.append(zone_data)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(zones, file, ensure_ascii=False, indent=4)


def process_rules(input_filepath, output_filepath):
    rules = []

    with open(input_filepath, 'r', encoding='utf-8') as file:
        current_rule = None
        is_source_ip = False
        is_source_zone = False
        is_destination_zone = False
        is_destination_ip = False
        is_service = False

        for line in file:
            line = line.strip()

            if line.startswith('rule'):
                if current_rule:
                    if not is_source_ip:
                        current_rule['source_ip'] = ['any']
                    if not is_source_zone:
                        current_rule['source_zone'] = 'any'
                    if not is_destination_zone:
                        current_rule['destination_zone'] = 'any'
                    if not is_destination_ip:
                        current_rule['destination_ip'] = ['any']
                    if not is_service:
                        current_rule['services'] = ['any']
                    rules.append(current_rule)

                match = re.match(r'rule (\d+) name (.+)', line)
                if match:
                    rule_number, rule_name = match.groups()
                    current_rule = {
                        'id': rule_number,
                        'rule_name': rule_name.strip("\""),
                        'action': 'drop',
                        'source_zone': None,
                        'source_ip': [],
                        'destination_ip': [],
                        'destination_zone': None,
                        'services': [],
                        'is_logging': '',
                        'is_counting': '',
                        'rule_status': ''
                    }
                    is_source_ip = False
                    is_source_zone = False
                    is_destination_zone = False
                    is_destination_ip = False
                    is_service = False

            elif current_rule:
                parts = line.split(' ', 1)
                value = parts[1] if len(parts) > 1 else None

                if line.startswith('action'):
                    current_rule['action'] = value if value else 'drop'
                elif line.startswith('source-zone'):
                    current_rule['source_zone'] = value
                    is_source_zone = True
                elif line.startswith('source-ip'):
                    current_rule['source_ip'].append(value)
                    is_source_ip = True
                elif line.startswith('destination-zone'):
                    current_rule['destination_zone'] = value
                    is_destination_zone = True
                elif line.startswith('destination-ip'):
                    current_rule['destination_ip'].append(value)
                    is_destination_ip = True
                elif line.startswith('service'):
                    current_rule['services'].append(value)
                    is_service = True
                elif line.startswith('counting'):
                    current_rule['is_counting'] = value if value else ''
                elif line.startswith('logging'):
                    current_rule['is_logging'] = value if value else ''

                if line.startswith('disable'):
                    current_rule['rule_status'] = "Disable"

        # Don't forget the last rule
        if current_rule:
            if not is_source_ip:
                current_rule['source_ip'] = ['any']
            if not is_source_zone:
                current_rule['source_zone'] = 'any'
            if not is_destination_zone:
                current_rule['destination_zone'] = 'any'
            if not is_destination_ip:
                current_rule['destination_ip'] = ['any']
            if not is_service:
                current_rule['services'] = ['any']
            rules.append(current_rule)

    with open(output_filepath, 'w', encoding='utf-8') as file:
        json.dump(rules, file, ensure_ascii=False, indent=4)


# 创建输出文件夹，如果不存在
def check_directory(output_filepath):
    os.makedirs(os.path.dirname(output_filepath), exist_ok=True)


if __name__ == "__main__":
    print('开始处理!')

    # 检查并创建输出文件夹（如果不存在）
    input_filepath = "../files/H3C/XWHPD-NE1DIL-FW01.txt"
    zone_output_filepath = "../files/H3C/json/竞猜01/zone.json"
    service_output_filepath = "../files/H3C/json/竞猜01/service.json"
    rules_output_filepath = "../files/H3C/json/竞猜01/rule.json"
    address_output_filepath = "../files/H3C/json/竞猜01/address.json"
    # nat_output_filepath = '../files/H3C/json/竞猜04/nat.json'

    check_directory(zone_output_filepath)
    check_directory(service_output_filepath)
    check_directory(rules_output_filepath)
    check_directory(address_output_filepath)
    # check_directory(nat_output_filepath)

    # 调用函数处理文件
    process_service(input_filepath, service_output_filepath)
    process_address(input_filepath, address_output_filepath)
    process_zone(input_filepath, zone_output_filepath)
    # process_nat(input_filepath, nat_output_filepath)
    process_rules(input_filepath, rules_output_filepath)
    print('处理完成!')

import pandas as pd
import json
import os
import openpyxl

def process_address_json(address_data):
    processed_address_data = []
    for entry in address_data:
        address_name = entry.get('address_name', '')
        security_zone = entry.get('security-zone', '')
        ip_values = []
        ip_list = entry.get('ip_address', [])
        for ip_entry in ip_list:
            ip_type = ip_entry.get('type', '')
            ip_value = ip_entry.get('value', '')
            if not ip_value.startswith('255'):
                ip_values.append(f"{ip_value}")

        ip_values_str = ', '.join(ip_values)
        processed_address_data.append(
            {'address_name': address_name, 'security-zone': security_zone, 'value': ip_values_str})

    return processed_address_data


def process_service_json(service_data):
    processed_service_data = []
    for entry in service_data:
        service_name = entry.get('service', '')
        tcp_value = entry.get('tcp', '')
        udp_value = entry.get('udp', '')
        processed_service_data.append({'service': service_name, 'tcp': tcp_value, 'udp': udp_value})

    return processed_service_data


def load_json_files(input_directory, filenames):
    data_dict = {}

    for filename in filenames:
        filepath = os.path.join(input_directory, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data_dict[filename] = json.load(f)
        except FileNotFoundError:
            print(f"文件未找到：{filepath}")
            data_dict[filename] = []
        except json.JSONDecodeError:
            print(f"读取JSON文件时出错：{filepath}")
            data_dict[filename] = []

    return data_dict


def process_json_files(input_directory, output_filepath):
    data_dict = load_json_files(input_directory, ['address.json', 'service.json', 'zone.json', 'rule.json',
                                                  'static_nat.json'])

    address_data = data_dict['address.json']
    service_data = data_dict['service.json']
    zone_data = data_dict['zone.json']
    rules_data = data_dict['rule.json']
    static_nat_data = data_dict['static_nat.json']

    processed_address_data = process_address_json(address_data)
    processed_service_data = process_service_json(service_data)
    data_dict = {
        'address': (processed_address_data, ['address_name', 'security-zone', 'value']),
        'service': (processed_service_data, ['service', 'tcp', 'udp']),
        'zone': (zone_data, ['zone_name']),
        'rule': (
            rules_data,
            ['id', 'rule_name', 'action', 'source_zone', 'source_ip', 'destination_ip', 'destination_zone', 'services',
             'is_logging', 'is_counting', 'rule_status']),
        'static_nat': (static_nat_data, list(static_nat_data[0].keys()))
    }

    os.makedirs(os.path.dirname(output_filepath), exist_ok=True)

    with pd.ExcelWriter(output_filepath, engine='openpyxl') as writer:
        for sheet, (data, columns) in data_dict.items():
            df = pd.DataFrame(data)
            print(f"{sheet}的DataFrame中的列：{df.columns.tolist()}")
            if not set(columns).issubset(df.columns):
                print(f"错误：{sheet}的DataFrame中不存在所有指定的列。跳过该工作表。")
                continue

            # 将source_ip和destination_ip列中的列表转换为逗号分隔的字符串
            for col in ['source_ip', 'destination_ip', 'services']:
                if col in df.columns:
                    df[col] = df[col].str.join(', ')

            # 去掉JSON值中的引号和花括号
            df = df.astype(str).replace({"'": "", "{": "", "}": ""}, regex=True)

            # Remove the "dst-port:" prefix in the values
            if sheet == 'service':
                for col in ['tcp', 'udp']:
                    if col in df.columns:
                        df[col] = df[col].str.replace('dst-port:', '')

            try:
                df.to_excel(writer, sheet_name=sheet, index=False, columns=columns)
            except Exception as e:
                print(f"Error writing the {sheet} sheet: {e}")


input_directory = '../files/H3C/json/西五环测试外联/'
output_filepath = '../files/H3C/Excel/西五环测试外联_BD-LAFW02_104.255.253.22.xlsx'

process_json_files(input_directory, output_filepath)

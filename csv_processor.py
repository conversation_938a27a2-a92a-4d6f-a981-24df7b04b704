#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import csv
import os
import re
import argparse
import logging
import glob
from datetime import datetime
import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Alignment
from copy import copy

# 配置日志
def setup_logger():
    """设置日志配置"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f"csv_processor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger()

def find_file_with_extension(file_path, logger):
    """
    查找文件，允许省略扩展名
    
    Args:
        file_path: 文件路径，可以省略扩展名
        logger: 日志记录器
    
    Returns:
        str: 找到的完整文件路径
    """
    # 检查文件是否已经存在（包含扩展名）
    if os.path.exists(file_path):
        return file_path
        
    # 检查文件是否存在扩展名
    _, ext = os.path.splitext(file_path)
    if ext:  # 如果已有扩展名但文件不存在
        logger.error(f"文件 {file_path} 不存在")
        return None
    
    # 尝试查找匹配的文件（没有扩展名的情况）
    supported_exts = ['.csv', '.xlsx', '.xls']
    matched_files = []
    
    # 遍历支持的扩展名
    for ext in supported_exts:
        potential_file = file_path + ext
        if os.path.exists(potential_file):
            matched_files.append(potential_file)
    
    # 根据匹配结果处理
    if not matched_files:
        logger.error(f"未找到与 {file_path} 匹配的文件")
        return None
    elif len(matched_files) == 1:
        logger.info(f"找到匹配文件: {matched_files[0]}")
        return matched_files[0]
    else:
        # 多个匹配时，优先选择xlsx，其次csv，最后xls
        priority_order = {'.xlsx': 0, '.csv': 1, '.xls': 2}
        matched_files.sort(key=lambda x: priority_order.get(os.path.splitext(x)[1].lower(), 999))
        logger.warning(f"找到多个匹配文件: {', '.join(matched_files)}，使用: {matched_files[0]}")
        return matched_files[0]

def read_file(file_path, logger):
    """
    读取文件（支持CSV和Excel），根据文件扩展名自动处理
    
    Args:
        file_path: 文件路径
        logger: 日志记录器
    
    Returns:
        DataFrame: 读取的数据
        workbook: Excel工作簿对象(如果是Excel文件)
    """
    try:
        logger.info(f"开始读取文件: {file_path}")
        
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        # 尝试检测编码
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'gb18030']
        
        # Excel文件
        if ext in ['.xlsx', '.xls']:
            try:
                # 使用openpyxl读取Excel以保留格式
                workbook = openpyxl.load_workbook(file_path)
                sheet = workbook.active
                
                # 将工作表转换为DataFrame，保留换行符
                data = []
                for row in sheet.rows:
                    # 保留单元格原始值，包括换行符
                    data.append([cell.value for cell in row])
                
                # 创建DataFrame，使用第一行作为列名
                if data:
                    # 确保列名不是None，如果是None则使用位置索引
                    columns = []
                    for i, col in enumerate(data[0]):
                        if col is None:
                            columns.append(f"Col_{i}")
                        else:
                            columns.append(str(col))
                    
                    df = pd.DataFrame(data[1:], columns=columns)
                else:
                    df = pd.DataFrame()
                
                logger.info(f"成功读取Excel文件 {file_path}")
                return df, workbook
            except Exception as e:
                logger.error(f"读取Excel文件失败: {e}")
                return None, None
        
        # CSV文件
        elif ext in ['.csv']:
            for encoding in encodings:
                try:
                    # 使用pandas读取CSV，处理换行符
                    # 设置lineterminator确保正确处理单元格内的换行符
                    df = pd.read_csv(file_path, encoding=encoding, lineterminator='\n', 
                                   quotechar='"', quoting=csv.QUOTE_ALL)
                    logger.info(f"使用编码 {encoding} 成功读取CSV文件")
                    return df, None
                except UnicodeDecodeError:
                    logger.warning(f"尝试使用编码 {encoding} 失败，尝试下一种编码")
                    if encoding == encodings[-1]:
                        logger.error("所有编码尝试均失败，无法读取文件")
                        return None, None
                    continue
                except Exception as e:
                    logger.error(f"读取CSV文件失败: {e}")
                    return None, None
        else:
            logger.error(f"不支持的文件格式: {ext}，只支持CSV和Excel文件")
            return None, None
            
    except Exception as e:
        logger.error(f"读取文件时出错：{e}", exc_info=True)
        return None, None

def read_mapping_file(file_path, logger):
    """
    读取映射文件，创建从A列到B列的映射字典
    
    Args:
        file_path: 映射文件路径（域名.csv）
        logger: 日志记录器
    
    Returns:
        dict: 域名到IP的映射字典
    """
    try:
        logger.info(f"开始读取映射文件: {file_path}")
        
        # 使用direct_read_mapping为True来直接读取Excel文件
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            return direct_read_mapping_excel(file_path, logger)
        
        # 读取文件（CSV和其他格式）
        df, workbook = read_file(file_path, logger)
        
        if df is None:
            return {}
            
        # 确保文件至少有两列
        if len(df.columns) < 2:
            logger.error(f"错误：映射文件 {file_path} 至少需要两列（域名和IP）")
            return {}
            
        # 提取前两列作为映射关系
        col_a = df.columns[0]
        col_b = df.columns[1]
        
        logger.info(f"使用列 '{col_a}' 作为键，列 '{col_b}' 作为值")
            
        # 创建从第一列到第二列的映射字典
        mapping = {}
        for idx, row in df.iterrows():
            try:
                # 安全处理可能的NaN值
                key_value = row[col_a]
                value_value = row[col_b]
                
                # 使用pd.isna()单独检查每个值，避免Series的真值歧义
                if pd.isna(key_value) or pd.isna(value_value):
                    continue
                    
                # 将值转换为字符串
                key = str(key_value).strip()
                value = str(value_value)  # 完全保留原始格式，包括换行符和空格
                
                # 跳过表头或空值
                if key and key != col_a and key != 'nan' and key.strip():
                    mapping[key] = value
                    # 在日志中显示换行符
                    log_value = value.replace('\n', '\\n').replace('\r', '\\r')
                    logger.debug(f"添加映射: {key} -> {log_value}")
            except Exception as e:
                logger.warning(f"处理映射记录时出错: {e}")
                continue
        
        logger.info(f"成功创建映射字典，共 {len(mapping)} 条记录")
        return mapping
    except Exception as e:
        logger.error(f"读取映射文件时出错：{e}", exc_info=True)
        return {}

def direct_read_mapping_excel(file_path, logger):
    """
    直接通过openpyxl读取Excel映射文件，处理特殊格式的Excel
    
    Args:
        file_path: 映射文件路径
        logger: 日志记录器
    
    Returns:
        dict: 域名到IP的映射字典
    """
    try:
        mapping = {}
        
        # 直接使用openpyxl读取Excel
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active
        
        # 在屏幕上打印一些信息，帮助调试
        logger.info(f"Excel文件信息: 行数={sheet.max_row}, 列数={sheet.max_column}")
        
        # 检查文件名特征判断特殊格式
        if "ump域名" in file_path:
            logger.info("检测到UMP域名映射文件, 使用特殊处理逻辑")
            
            # 基于检查结果，UMP域名文件的结构比较特殊，尝试直接读取相应位置的数据
            # 由于我们已经知道pandas读取时识别出了"域名"和"IP"两列，使用这两列的数据
            try:
                df = pd.read_excel(file_path)
                logger.info(f"尝试使用pandas读取域名文件, 列名: {list(df.columns)}")
                
                # 检查列名
                if '域名' in df.columns and 'IP' in df.columns:
                    logger.info("在pandas读取结果中找到'域名'和'IP'列，使用这些列")
                    
                    # 创建映射
                    for idx, row in df.iterrows():
                        try:
                            domain = row['域名']
                            ip = row['IP']
                            
                            # 跳过空值
                            if pd.isna(domain) or pd.isna(ip):
                                continue
                                
                            domain_str = str(domain).strip()
                            ip_str = str(ip)
                            
                            if domain_str and domain_str.lower() != 'nan' and ip_str and ip_str.lower() != 'nan':
                                mapping[domain_str] = ip_str
                                log_ip = ip_str.replace('\n', '\\n').replace('\r', '\\r')
                                logger.debug(f"添加映射: {domain_str} -> {log_ip}")
                        except Exception as e:
                            logger.warning(f"处理行 {idx} 时出错: {e}")
                    
                    logger.info(f"通过pandas读取成功创建 {len(mapping)} 条记录")
                    return mapping
            except Exception as e:
                logger.warning(f"使用pandas读取方式失败: {e}")
        
        # 使用更通用的方法处理
        # 查找"源(含IP)"和"目标(含域名及IP)"列
        domain_col = None
        ip_col = None
        start_row = None
        
        # 打印前10行的内容，帮助调试
        logger.info("扫描文件内容查找有效数据:")
        for r in range(1, min(10, sheet.max_row + 1)):
            row_data = []
            for c in range(1, min(10, sheet.max_column + 1)):
                cell_value = sheet.cell(row=r, column=c).value
                if cell_value:
                    row_data.append(f"{c}:{cell_value}")
                    
                    # 检测可能的列头
                    cell_text = str(cell_value).strip().lower()
                    if ('域名' in cell_text or 'lb' in cell_text) and domain_col is None:
                        domain_col = c
                        logger.info(f"在行 {r} 列 {c} 找到可能的域名列: {cell_value}")
                    elif ('ip' in cell_text or '地址' in cell_text) and ip_col is None:
                        ip_col = c 
                        logger.info(f"在行 {r} 列 {c} 找到可能的IP列: {cell_value}")
            
            logger.info(f"行 {r} 内容: {', '.join(row_data) if row_data else '无数据'}")
        
        # 如果都没找到，尝试基于常规内容检测
        if domain_col is None and ip_col is None:
            # 查找常见域名样式的单元格(如包含.com, .cn等)
            for r in range(1, min(20, sheet.max_row + 1)):
                for c in range(1, min(10, sheet.max_column + 1)):
                    cell_value = sheet.cell(row=r, column=c).value
                    if cell_value and isinstance(cell_value, str):
                        if ('.com' in cell_value.lower() or 
                            '.cn' in cell_value.lower() or 
                            '.net' in cell_value.lower() or
                            '.prod' in cell_value.lower()):
                            domain_col = c
                            # 推测下一列是IP
                            ip_col = c + 1
                            start_row = r
                            logger.info(f"在行 {r} 列 {c} 找到疑似域名: {cell_value}")
                            logger.info(f"推测数据开始于行 {r}")
                            break
                if domain_col is not None:
                    break
        
        # 如果没有找到列，使用默认值
        if domain_col is None:
            domain_col = 1  # 第一列
            logger.warning("未找到域名列，使用第一列作为域名")
        if ip_col is None:
            ip_col = 2  # 第二列
            logger.warning("未找到IP列，使用第二列作为IP")
        if start_row is None:
            start_row = 1  # 默认从第1行开始
            logger.warning(f"未找到数据开始行，使用第 {start_row} 行作为开始")
        
        # 读取数据行
        for r in range(start_row, sheet.max_row + 1):
            try:
                domain = sheet.cell(row=r, column=domain_col).value
                ip = sheet.cell(row=r, column=ip_col).value
                
                # 跳过空值
                if domain is None or ip is None:
                    continue
                
                # 转换为字符串
                domain_str = str(domain).strip()
                ip_str = str(ip)
                
                # 添加有效映射
                if domain_str and domain_str.lower() != 'nan' and ip_str and ip_str.lower() != 'nan':
                    mapping[domain_str] = ip_str
                    log_ip = ip_str.replace('\n', '\\n').replace('\r', '\\r')
                    logger.debug(f"添加映射: {domain_str} -> {log_ip}")
            except Exception as e:
                logger.warning(f"处理行 {r} 时出错: {e}")
                continue
        
        logger.info(f"成功创建映射字典，共 {len(mapping)} 条记录")
        return mapping
    except Exception as e:
        logger.error(f"直接读取Excel映射文件时出错: {e}", exc_info=True)
        return {}

def process_target_file(target_file_path, mapping, output_file_path, logger):
    """
    处理目标文件，查找包含映射键的行，在末尾添加对应的值
    
    Args:
        target_file_path: 目标文件路径（需求.csv）
        mapping: 域名到IP的映射字典
        output_file_path: 输出文件路径
        logger: 日志记录器
    """
    try:
        logger.info(f"开始处理目标文件: {target_file_path}")
        
        # 读取目标文件
        df, workbook = read_file(target_file_path, logger)
        
        if df is None:
            return
        
        # 添加新列用于存储对应的IP
        if '对应IP' not in df.columns:
            df['对应IP'] = ''
        
        # 用于匹配的域名模式，不区分大小写
        domain_patterns = {re.compile(re.escape(domain), re.IGNORECASE): ip for domain, ip in mapping.items()}
        
        # 统计匹配成功的记录数
        match_count = 0
        matched_domains = {}
        
        # 检查每一行的每一列
        total_rows = len(df)
        logger.info(f"开始处理 {total_rows} 行数据")
        
        for i, row in df.iterrows():
            row_matched = False
            
            # 将行转换为字符串并处理
            for col in df.columns:
                if col == '对应IP':  # 跳过我们自己添加的列
                    continue
                
                # 安全处理单元格值
                cell_value = row[col]
                
                # 跳过空值或nan
                if pd.isna(cell_value):
                    continue
                
                if cell_value is None:
                    continue
                
                # 转换为字符串进行匹配
                cell_str = str(cell_value)
                if cell_str.lower() == 'nan' or not cell_str.strip():
                    continue
                
                # 尝试匹配每个域名
                for pattern, ip in domain_patterns.items():
                    domain = pattern.pattern.replace('\\', '')
                    
                    if pattern.search(cell_str):
                        # 找到匹配项，添加对应的IP，完全保留原始格式
                        df.at[i, '对应IP'] = ip
                        row_matched = True
                        match_count += 1
                        
                        # 记录匹配成功的域名及次数
                        if domain in matched_domains:
                            matched_domains[domain] += 1
                        else:
                            matched_domains[domain] = 1
                        
                        # 在日志中显示换行符
                        log_ip = ip.replace('\n', '\\n').replace('\r', '\\r')
                        logger.debug(f"行 {i+1}: 在列 '{col}' 中找到匹配 '{domain}', 添加IP '{log_ip}'")
                        break
                
                if row_matched:
                    break
            
            # 每处理100行输出一次进度
            if (i + 1) % 100 == 0 or i + 1 == total_rows:
                logger.info(f"已处理 {i+1}/{total_rows} 行，当前匹配数: {match_count}")
        
        # 打印匹配统计信息
        logger.info("匹配域名统计:")
        for domain, count in sorted(matched_domains.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"  - {domain}: {count} 次")
        
        # 设置输出文件格式
        _, ext = os.path.splitext(output_file_path)
        # 如果没有指定扩展名，使用与目标文件相同的扩展名
        if not ext:
            _, target_ext = os.path.splitext(target_file_path)
            output_file_path += target_ext
            logger.info(f"输出文件未指定扩展名，使用与目标文件相同的扩展名: {target_ext}")
            ext = target_ext
        
        ext = ext.lower()
        
        # 根据文件扩展名决定输出格式
        if ext in ['.xlsx', '.xls']:
            if workbook:
                # 保留原始格式的Excel文件
                logger.info(f"使用保留格式的方式保存Excel文件")
                try:
                    # 获取活动工作表
                    ws = workbook.active
                    # 添加新列表头
                    max_col = ws.max_column
                    ws.cell(row=1, column=max_col+1).value = '对应IP'
                    
                    # 保存原始单元格格式
                    cell_style = None
                    if ws.max_row > 1 and max_col > 0:
                        # 尝试复制第一行的样式
                        try:
                            cell_style = ws.cell(row=1, column=max_col).style
                        except:
                            pass
                    
                    # 更新数据，保留换行符
                    for i, row in df.iterrows():
                        ip_value = row['对应IP']
                        if not pd.isna(ip_value) and ip_value:  # 只更新有值的单元格
                            try:
                                # 创建新单元格并保留换行格式
                                cell = ws.cell(row=i+2, column=max_col+1)
                                cell.value = ip_value
                                
                                # 设置文本换行（确保多行正确显示）
                                cell.alignment = Alignment(wrapText=True, vertical='top')
                                
                                # 复制样式
                                if cell_style:
                                    cell.style = cell_style
                            except Exception as e:
                                logger.warning(f"设置单元格 {i+2},{max_col+1} 时出错: {e}")
                    
                    # 自动调整列宽
                    ws.column_dimensions[openpyxl.utils.get_column_letter(max_col+1)].width = 30
                    
                    # 保存工作簿
                    workbook.save(output_file_path)
                except Exception as e:
                    logger.error(f"保存Excel文件时出错: {e}", exc_info=True)
                    # 失败时使用pandas保存为备选方案
                    logger.info("使用备选方案保存Excel文件...")
                    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
                        df.to_excel(writer, index=False)
                        
                        # 设置自动换行
                        workbook = writer.book
                        worksheet = writer.sheets['Sheet1']
                        for row_idx in range(len(df)):
                            ip_value = df.iloc[row_idx]['对应IP']
                            if not pd.isna(ip_value) and ip_value:
                                cell = worksheet.cell(row=row_idx+2, column=len(df.columns))
                                cell.alignment = Alignment(wrapText=True, vertical='top')
                        
                        logger.info("使用pandas导出Excel作为备选方案")
            else:
                # 如果没有原始格式，则直接保存DataFrame
                # 使用ExcelWriter以保留换行符
                with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False)
                    
                    # 调整列宽和单元格格式
                    workbook = writer.book
                    worksheet = writer.sheets['Sheet1']
                    
                    # 设置IP列宽度
                    ip_col_idx = len(df.columns)
                    worksheet.column_dimensions[openpyxl.utils.get_column_letter(ip_col_idx)].width = 30
                    
                    # 为IP列设置文本换行
                    for row_idx in range(len(df)):
                        ip_value = df.iloc[row_idx]['对应IP']
                        if not pd.isna(ip_value) and ip_value:
                            cell = worksheet.cell(row=row_idx+2, column=ip_col_idx)
                            cell.alignment = Alignment(wrapText=True, vertical='top')
            
            logger.info(f"结果已保存为Excel文件: {output_file_path}")
        else:  # 默认为CSV
            # 使用引号包围字段以正确处理换行符
            df.to_csv(output_file_path, index=False, encoding='utf-8-sig', 
                      quoting=csv.QUOTE_ALL, line_terminator='\n')
            logger.info(f"结果已保存为CSV文件: {output_file_path}")
            
        logger.info(f"处理完成，找到 {match_count} 条匹配记录，结果已保存至：{output_file_path}")
    except Exception as e:
        logger.error(f"处理目标文件时出错：{e}", exc_info=True)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='CSV/Excel文件关联处理工具')
    parser.add_argument('-m', '--mapping', help='映射文件路径（包含域名和IP的文件）')
    parser.add_argument('-t', '--target', help='目标文件路径（需要查找和更新的文件）')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细日志')
    return parser.parse_args()

def main():
    """主函数，处理用户输入并执行文件处理"""
    # 设置日志
    logger = setup_logger()
    
    print("CSV/Excel文件关联处理工具")
    print("=" * 50)
    print("提示: 输入文件路径时可以省略扩展名，程序会自动查找匹配的文件")
    
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志级别
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # 获取用户输入（优先使用命令行参数）
    mapping_file_input = args.mapping or input("请输入映射文件路径: ")
    mapping_file = find_file_with_extension(mapping_file_input, logger)
    if not mapping_file:
        print(f"错误：找不到映射文件 {mapping_file_input}")
        return
    
    target_file_input = args.target or input("请输入目标文件路径: ")
    target_file = find_file_with_extension(target_file_input, logger)
    if not target_file:
        print(f"错误：找不到目标文件 {target_file_input}")
        return
    
    # 设置默认输出文件路径，保留原始文件扩展名
    _, ext = os.path.splitext(target_file)
    default_output = os.path.splitext(target_file)[0] + "_处理结果" + ext
    output_file_input = args.output or input(f"请输入输出文件路径: ")
    
    # 如果输出文件路径为空，使用默认路径
    if not output_file_input:
        output_file = default_output
        logger.info(f"未指定输出文件路径，使用默认路径: {default_output}")
    else:
        # 确保输出文件有扩展名
        _, ext = os.path.splitext(output_file_input)
        if not ext:
            # 使用与目标文件相同的扩展名
            _, target_ext = os.path.splitext(target_file)
            output_file = output_file_input + target_ext
            logger.info(f"输出文件未指定扩展名，使用与目标文件相同的扩展名: {target_ext}")
        else:
            output_file = output_file_input
    
    # 读取映射文件
    logger.info(f"正在读取映射文件 {mapping_file}...")
    mapping = read_mapping_file(mapping_file, logger)
    
    if not mapping:
        logger.error("映射文件为空或读取失败，程序退出")
        return
        
    logger.info(f"成功读取 {len(mapping)} 条映射记录")
    
    # 处理目标文件
    logger.info(f"正在处理目标文件 {target_file}...")
    process_target_file(target_file, mapping, output_file, logger)

if __name__ == "__main__":
    main() 
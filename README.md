# CSV/Excel文件关联处理工具

这个工具用于处理两个CSV或Excel文件之间的关联查询，并更新数据。

## 功能说明

该工具可以：

1. 从第一个文件（映射文件）中读取A列和B列的对应关系（例如：域名和IP）
2. 在第二个文件（目标文件）中查找包含映射文件中A列值的记录
3. 将匹配记录末尾添加对应的B列值
4. 支持CSV和Excel文件格式
5. 自动检测文件编码
6. 提供详细的日志记录
7. **保留原始Excel文件的格式**，包括合并单元格、单元格颜色和样式等
8. **保留单元格内的换行符**，确保多行内容的显示格式一致
9. **允许省略文件扩展名**，自动查找匹配的文件

## 使用方法

### 命令行参数

```bash
python csv_processor.py [-h] [-m MAPPING] [-t TARGET] [-o OUTPUT] [-v]
```

参数说明：
- `-h, --help`: 显示帮助信息
- `-m, --mapping`: 映射文件路径（包含域名和IP的文件）
- `-t, --target`: 目标文件路径（需要查找和更新的文件）
- `-o, --output`: 输出文件路径
- `-v, --verbose`: 显示详细日志

### 交互式使用

如果不提供命令行参数，程序将交互式地询问输入信息：

1. 映射文件路径（**可以省略扩展名**）
2. 目标文件路径（**可以省略扩展名**）
3. 输出文件路径（如果不指定，将使用默认路径：目标文件名+"_处理结果"+原始扩展名）

### 文件路径简化

工具支持以下文件路径简化功能：

1. **省略文件扩展名**：输入`data/domains`时，会自动查找`data/domains.csv`、`data/domains.xlsx`或`data/domains.xls`
2. **处理同名不同扩展名文件**：如果存在多个扩展名不同的同名文件，会按照`.xlsx` > `.csv` > `.xls`的优先级选择
3. **保持输出文件格式一致**：如果未指定输出文件的扩展名，将使用与目标文件相同的扩展名

### 示例

```bash
# 使用命令行参数（省略扩展名）
python csv_processor.py -m data/domains -t data/requests -o data/result -v

# 交互式使用
python csv_processor.py
# 然后输入：
# 映射文件路径: domains（会自动查找domains.csv或domains.xlsx等）
# 目标文件路径: requests（会自动查找requests.csv或requests.xlsx等）
# 输出文件路径: result（会使用与目标文件相同的扩展名）
```

## 文件格式要求

1. 映射文件：至少包含两列，第一列为键（如域名），第二列为值（如IP）
2. 目标文件：任意格式，程序会搜索每一行中是否包含映射文件中的键，并在末尾添加对应的值

## 输出内容

1. 在目标文件的基础上，添加一列"对应IP"，包含匹配到的映射值
2. 生成详细的日志，记录程序执行过程和匹配情况
3. 支持输出为CSV或Excel格式（根据输出文件扩展名自动选择）
4. **保留Excel文件的原始格式**：使用openpyxl库处理Excel文件，保留原始格式，仅添加新的"对应IP"列
5. **保留单元格内的换行符**：确保多行文本在输出文件中正确显示，与原始格式保持一致

## 处理细节

1. 当读取Excel文件时，会保留所有原始格式信息，并使用openpyxl直接操作工作簿
2. 对于包含换行符的单元格，会启用自动换行（wrapText）属性
3. 自动调整新添加列的宽度，以适应内容
4. 在CSV输出中，使用双引号包裹字段以正确处理包含换行符的内容
5. 当找到多个扩展名不同的同名文件时，会在日志中记录选择过程

## 注意事项

1. 程序会尝试多种编码（utf-8, utf-8-sig, gbk, gb2312, gb18030）读取文件
2. 匹配过程不区分大小写
3. 会跳过空值和表头
4. 日志文件保存在logs目录下
5. 需要安装openpyxl库以支持Excel格式保留功能: `pip install openpyxl`

## 依赖库

- pandas：数据处理
- openpyxl：Excel格式保留处理
- 其他标准库：csv, os, re, argparse, logging, datetime, glob 
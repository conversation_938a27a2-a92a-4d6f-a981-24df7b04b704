import pandas as pd
import os
# 引入 prompt_toolkit 用于改善交互体验
from prompt_toolkit import prompt
from prompt_toolkit.history import FileHistory # 用于保存输入历史
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory # 用于从历史记录中自动建议

def get_user_inputs():
    """
    交互式获取用户输入：输入文件路径、操作字段名和输出文件路径。
    使用 prompt_toolkit 提升交互体验。

    返回:
        tuple: (input_file_path, column_name, output_file_path) 或 (None, None, None) 如果用户中途退出或输入无效。
    """
    # 为输入历史创建一个文件，可以根据需要自定义路径
    history_file = os.path.join(os.path.expanduser("~"), ".domain_compare_history")
    history = FileHistory(history_file)

    # 获取输入文件路径
    while True:
        # 使用 prompt_toolkit.prompt 代替 input
        input_file_path = prompt(
            "请输入Excel或CSV文件的完整路径（或输入 'q' 退出）：",
            history=history,
            auto_suggest=AutoSuggestFromHistory()
        ).strip()
        if input_file_path.lower() == 'q':
            return None, None, None
        if os.path.exists(input_file_path):
            if input_file_path.lower().endswith(('.csv', '.xlsx', '.xls')):
                break
            else:
                print("错误：文件格式不支持，请输入.csv, .xlsx或.xls文件。")
        else:
            print(f"错误：文件 '{input_file_path}' 不存在，请重新输入。")

    # 获取操作字段名
    while True:
        column_name = prompt(
            "请输入需要操作的字段名（列名）（或输入 'q' 退出）：",
            history=history,
            auto_suggest=AutoSuggestFromHistory()
        ).strip()
        if column_name.lower() == 'q':
            return None, None, None
        if column_name: # 确保字段名不为空
            break
        else:
            print("错误：字段名不能为空，请重新输入。")

    # 获取输出文件路径
    while True:
        output_file_path = prompt(
            "请输入输出CSV文件的完整路径（例如 /path/to/output.csv）（或输入 'q' 退出）：",
            history=history,
            auto_suggest=AutoSuggestFromHistory()
        ).strip()
        if output_file_path.lower() == 'q':
            return None, None, None
        if output_file_path.lower().endswith('.csv'):
            output_dir = os.path.dirname(output_file_path)
            if not output_dir:  # 如果用户只输入文件名，则默认为当前目录
                output_dir = "."
            
            # 检查输出目录是否存在，如果不存在则尝试创建
            if os.path.isdir(output_dir):
                break
            else:
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    print(f"提示：输出目录 '{output_dir}' 已创建。")
                    break
                except OSError as e:
                    print(f"错误：无法创建输出目录 '{output_dir}'。原因: {e}。请检查路径或权限，然后重新输入。")
        else:
            print("错误：输出文件必须是CSV格式，请以 .csv 结尾。")
            
    return input_file_path, column_name, output_file_path

def load_data(file_path):
    """
    根据文件扩展名加载CSV或Excel文件。

    参数:
        file_path (str): 文件路径。

    返回:
        pandas.DataFrame or None: 加载的数据，如果失败则返回None。
    """
    try:
        print(f"正在加载文件: {file_path} ...")
        if file_path.lower().endswith('.csv'):
            # 尝试使用不同的编码读取CSV，以应对常见编码问题
            try:
                df = pd.read_csv(file_path, dtype=str, keep_default_na=False) # 读取所有列为字符串，避免类型推断问题，并将空字符串视为空
            except UnicodeDecodeError:
                print("尝试用GBK编码读取CSV...")
                df = pd.read_csv(file_path, encoding='gbk', dtype=str, keep_default_na=False)
        elif file_path.lower().endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path, dtype=str, keep_default_na=False) # 读取所有列为字符串
        else:
            print(f"错误：不支持的文件格式 '{os.path.basename(file_path)}'。")
            return None
        print(f"文件 '{os.path.basename(file_path)}' 加载成功，共 {len(df)} 条记录。")
        return df
    except FileNotFoundError:
        print(f"错误：文件 '{file_path}' 未找到。")
        return None
    except pd.errors.EmptyDataError:
        print(f"错误：文件 '{file_path}' 为空。")
        return None
    except Exception as e:
        print(f"加载文件 '{file_path}' 时发生未知错误：{e}")
        return None

def process_domains(df, column_name):
    """
    处理域名数据：找出符合特定三元组模式（base.tc, base.xw.tc, base.yz.tc）的记录，
    并按该顺序排列。

    参数:
        df (pandas.DataFrame): 包含域名数据的DataFrame。
        column_name (str): 需要处理的域名所在的列名。

    返回:
        pandas.DataFrame: 包含符合条件并排序后的三元组原始记录。
    """
    if column_name not in df.columns:
        print(f"错误：指定的字段名 '{column_name}' 在文件中不存在。可用字段有: {', '.join(df.columns)}")
        return pd.DataFrame() # 返回空的DataFrame

    # 复制一份数据进行操作，避免修改原始DataFrame
    df_processed = df.copy()
    
    # 确保操作列是字符串类型，并将None/NaN转换为空字符串，方便后续处理
    df_processed[column_name] = df_processed[column_name].astype(str).fillna('')

    # 定义要检查的完整后缀，按此顺序查找和输出
    target_suffixes_ordered = ['.tc', '.xw.tc', '.yz.tc']
    # 用于初步筛选，包含所有可能的后缀变体
    all_suffixes_to_check = tuple(target_suffixes_ordered)

    # 1. 初步筛选出可能相关的记录 (以任一目标后缀结尾)
    condition = df_processed[column_name].str.endswith(all_suffixes_to_check, na=False)
    df_relevant = df_processed[condition].copy()

    if df_relevant.empty:
        print(f"在字段 '{column_name}' 中未找到以 {', '.join(all_suffixes_to_check)} 结尾的记录。")
        return pd.DataFrame()
    print(f"初步筛选后，找到 {len(df_relevant)} 条记录可能属于目标三元组。")

    # 2. 提取"基础部分" (Base String Extraction)
    def get_base_prefix(domain_str):
        # 必须从最长到最短检查后缀，以正确处理如 .xw.tc 和 .tc 的情况
        if domain_str.endswith('.xw.tc'):
            return domain_str[:-len('.xw.tc')]
        elif domain_str.endswith('.yz.tc'):
            return domain_str[:-len('.yz.tc')]
        elif domain_str.endswith('.tc'): # 这个应该在更长的之后检查
            return domain_str[:-len('.tc')]
        return None # 如果没有匹配到任何已知后缀（理论上不应发生在此阶段）

    df_relevant.loc[:, 'base_prefix'] = df_relevant[column_name].apply(get_base_prefix)
    
    # 移除那些无法提取有效 base_prefix 的行（例如，如果原始数据不规范）
    df_relevant = df_relevant.dropna(subset=['base_prefix'])
    if df_relevant.empty:
        print("无法从筛选的记录中提取有效的基础前缀。")
        return pd.DataFrame()

    # 3. 按"基础部分"分组并检查三元组
    grouped = df_relevant.groupby('base_prefix')
    
    result_records_list = [] # 用于收集所有符合条件的三元组记录
    triplet_pattern_found_count = 0 # 统计有多少个基础前缀形成了三元组模式

    for base_prefix, group_df in grouped:
        if not base_prefix: # 跳过空的 base_prefix (如果有的话)
            continue

        # 获取当前组内所有的原始域名值
        domains_in_group = set(group_df[column_name].tolist())

        # 构建期望的三个域名变体
        expected_domain_tc = base_prefix + '.tc'
        expected_domain_xw_tc = base_prefix + '.xw.tc'
        expected_domain_yz_tc = base_prefix + '.yz.tc'

        # 检查这三个变体是否都存在于当前组中
        has_tc = expected_domain_tc in domains_in_group
        has_xw_tc = expected_domain_xw_tc in domains_in_group
        has_yz_tc = expected_domain_yz_tc in domains_in_group

        if has_tc and has_xw_tc and has_yz_tc:
            # 如果三者都存在，则按顺序提取它们的原始记录
            # 我们需要从原始的 df (或 df_processed) 中查找，以获取所有列
            # 或者从 group_df 中查找，因为它已经是筛选过的
            
            record_tc = group_df[group_df[column_name] == expected_domain_tc]
            record_xw_tc = group_df[group_df[column_name] == expected_domain_xw_tc]
            record_yz_tc = group_df[group_df[column_name] == expected_domain_yz_tc]
            
            # 确保每种类型的记录都至少存在一条
            if not record_tc.empty and not record_xw_tc.empty and not record_yz_tc.empty:
                # 将所有匹配的记录（可能是多行DataFrame）按顺序添加到结果列表
                result_records_list.append(record_tc)
                result_records_list.append(record_xw_tc)
                result_records_list.append(record_yz_tc)
                triplet_pattern_found_count += 1 # 增加计数器
            else:
                # 这种情况理论上不应该发生，因为我们已经检查了它们在 domains_in_group 中
                print(f"警告：对于基础前缀 '{base_prefix}', 找到了三元组模式但无法提取所有记录。跳过此组。")
    
    if not result_records_list:
        print("未找到符合完整三元组模式（base.tc, base.xw.tc, base.yz.tc 同时存在）的记录组。")
        return pd.DataFrame()

    # 4. 合并结果并移除辅助列
    final_df = pd.concat(result_records_list, ignore_index=True)
    
    # 可以选择删除辅助列 'base_prefix'，如果它还存在于 final_df 中
    if 'base_prefix' in final_df.columns:
        final_df = final_df.drop(columns=['base_prefix'])
    
    print(f"成功为 {triplet_pattern_found_count} 个基础前缀找到了完整的三元组模式，共提取了 {len(final_df)} 条相关记录。")
    return final_df

def save_data(df, output_path):
    """
    将DataFrame保存到CSV文件。

    参数:
        df (pandas.DataFrame): 要保存的数据。
        output_path (str): 输出CSV文件的路径。
    """
    if df.empty:
        print("没有数据可保存。")
        return

    try:
        # index=False 表示不将DataFrame的索引写入到CSV文件中
        df.to_csv(output_path, index=False, encoding='utf-8-sig') # utf-8-sig 确保中文在Excel中正确显示
        print(f"结果已成功保存到: {output_path}")
    except Exception as e:
        print(f"保存文件到 '{output_path}' 时发生错误: {e}")

def main():
    """
    主函数，执行整个流程。
    """
    print("欢迎使用域名对比工具！")
    input_file, target_column, output_file = get_user_inputs()

    if not all((input_file, target_column, output_file)):
        print("操作已取消或输入不足，程序退出。")
        return

    data_df = load_data(input_file)

    if data_df is None or data_df.empty:
        print("无法加载数据或文件为空，程序退出。")
        return

    processed_df = process_domains(data_df, target_column)

    if processed_df is None or processed_df.empty:
        print("没有符合条件的记录需要保存，程序退出。")
        return
        
    save_data(processed_df, output_file)
    print("处理完成！")

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
需求版本受理跟踪记录表数据驾驶舱分析器

功能描述：
- 读取和分析需求版本受理跟踪记录表CSV文件
- 生成五大核心模块的数据分析：
  1. 顶层核心指标区 (KPI-Driven Header)
  2. 整体态势分析区 (Overall Status & Trends)
  3. 效率与瓶颈洞察区 (Efficiency & Bottleneck Insights)
  4. 需求与资源分析区 (Demand & Resource Analysis)
  5. 全局交互与筛选区 (Global Interactivity & Filtering)
- 输出可视化图表和HTML报告

技术栈：
- pandas: 数据处理和分析
- matplotlib/seaborn: 静态图表生成
- plotly: 交互式图表生成
- jinja2: HTML模板渲染

操作说明：
1. 确保CSV文件位于files/目录下
2. 运行脚本：python requirement_dashboard_analyzer.py
3. 查看output/requirement_dashboard_report/目录下的生成结果

作者：AI助手
创建时间：2025年6月6日
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo
from datetime import datetime, timedelta
import os
import logging
import json
import re
from collections import Counter
import warnings
from pathlib import Path

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

class RequirementDashboardAnalyzer:
    """需求管理数据驾驶舱分析器"""
    
    def __init__(self, csv_file_path, output_dir):
        """
        初始化分析器
        
        Args:
            csv_file_path (str): CSV文件路径
            output_dir (str): 输出目录路径
        """
        self.csv_file_path = csv_file_path
        self.output_dir = output_dir
        self.data = None
        self.analysis_results = {}
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'charts'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'data'), exist_ok=True)
        
        # 配置日志
        self.setup_logging()
        
    def setup_logging(self):
        """配置日志系统"""
        log_file = os.path.join(self.output_dir, 'analysis.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_and_clean_data(self):
        """加载和清洗数据"""
        try:
            self.logger.info(f"开始加载数据文件: {self.csv_file_path}")
            
            # 读取CSV文件
            self.data = pd.read_csv(self.csv_file_path, encoding='utf-8')
            
            # 删除第一列（序号列）
            if self.data.columns[0] == 'Unnamed: 0' or self.data.iloc[0, 0] == '1':
                self.data = self.data.drop(self.data.columns[0], axis=1)
            
            # 重命名列名
            column_mapping = {
                '需求接收日期': 'receive_date',
                '需求ID': 'requirement_id', 
                '需求名称': 'requirement_name',
                '期望完成日期': 'expected_completion_date',
                '开发侧需求对接人': 'dev_contact_person',
                '需求状态': 'status',
                '基础架构师': 'architect',
                '计划交底日期': 'planned_handover_date',
                '实际交底日期': 'actual_handover_date',
                '实施人员': 'implementation_staff',
                '提交IT基础架构监控需求': 'it_monitoring_submitted',
                '实施状态': 'implementation_status',
                '确认完成日期': 'completion_date',
                '备注': 'remarks'
            }
            
            self.data = self.data.rename(columns=column_mapping)
            
            # 数据清洗
            self.clean_data()
            
            self.logger.info(f"数据加载完成，共 {len(self.data)} 条记录")
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {str(e)}")
            raise
            
    def clean_data(self):
        """数据清洗"""
        # 处理日期列
        date_columns = ['receive_date', 'expected_completion_date', 'planned_handover_date', 
                       'actual_handover_date', 'completion_date']
        
        for col in date_columns:
            if col in self.data.columns:
                self.data[col] = pd.to_datetime(self.data[col], errors='coerce')
        
        # 清理状态字段
        self.data['status'] = self.data['status'].fillna('未知状态')
        self.data['implementation_status'] = self.data['implementation_status'].fillna('未知状态')
        
        # 清理人员字段
        self.data['dev_contact_person'] = self.data['dev_contact_person'].fillna('未分配')
        self.data['architect'] = self.data['architect'].fillna('未分配')
        self.data['implementation_staff'] = self.data['implementation_staff'].fillna('未分配')
        
        # 计算处理周期（天）
        self.data['processing_days'] = (
            self.data['completion_date'] - self.data['receive_date']
        ).dt.days
        
        # 计算交底延期天数
        self.data['handover_delay_days'] = (
            self.data['actual_handover_date'] - self.data['planned_handover_date']
        ).dt.days
        
        self.logger.info("数据清洗完成")
        
    def analyze_kpi_indicators(self):
        """分析顶层核心指标"""
        self.logger.info("开始分析顶层核心指标")
        
        total_requirements = len(self.data)
        completed_requirements = len(self.data[self.data['status'] == '已完成'])
        in_progress_requirements = len(self.data[self.data['status'] == '进行中'])
        paused_cancelled_requirements = len(self.data[self.data['status'].isin(['暂停', '取消'])])
        
        # 平均处理周期
        avg_processing_days = self.data['processing_days'].mean()
        if pd.isna(avg_processing_days):
            avg_processing_days = 0
            
        # 交底准时率
        on_time_handover = len(self.data[self.data['handover_delay_days'] <= 0])
        total_with_handover = len(self.data.dropna(subset=['handover_delay_days']))
        handover_on_time_rate = (on_time_handover / total_with_handover * 100) if total_with_handover > 0 else 0
        
        kpi_data = {
            'total_requirements': total_requirements,
            'completed_requirements': completed_requirements,
            'in_progress_requirements': in_progress_requirements,
            'paused_cancelled_requirements': paused_cancelled_requirements,
            'avg_processing_days': round(avg_processing_days, 1),
            'handover_on_time_rate': round(handover_on_time_rate, 1),
            'completion_rate': round(completed_requirements / total_requirements * 100, 1)
        }
        
        self.analysis_results['kpi_indicators'] = kpi_data
        self.logger.info("顶层核心指标分析完成")
        
    def analyze_overall_status(self):
        """分析整体态势"""
        self.logger.info("开始分析整体态势")
        
        # 需求状态分布
        status_distribution = self.data['status'].value_counts().to_dict()
        
        # 需求接收月度趋势
        self.data['receive_month'] = self.data['receive_date'].dt.to_period('M')
        monthly_received = self.data.groupby('receive_month').size()
        
        # 月度完成趋势
        completed_data = self.data[self.data['status'] == '已完成'].copy()
        if not completed_data.empty:
            completed_data['completion_month'] = completed_data['completion_date'].dt.to_period('M')
            monthly_completed = completed_data.groupby('completion_month').size()
        else:
            monthly_completed = pd.Series()
        
        self.analysis_results['overall_status'] = {
            'status_distribution': status_distribution,
            'monthly_received': monthly_received.to_dict(),
            'monthly_completed': monthly_completed.to_dict()
        }
        
        self.logger.info("整体态势分析完成")
        
    def analyze_efficiency_bottlenecks(self):
        """分析效率与瓶颈"""
        self.logger.info("开始分析效率与瓶颈")
        
        # 处理周期分布
        processing_days_dist = self.data['processing_days'].dropna()
        
        # 长周期停滞需求
        current_date = datetime.now()
        in_progress_data = self.data[self.data['status'].isin(['进行中', '资源需求未提交'])].copy()
        in_progress_data['days_since_receive'] = (current_date - in_progress_data['receive_date']).dt.days
        long_stagnant = in_progress_data.nlargest(10, 'days_since_receive')[
            ['requirement_id', 'requirement_name', 'status', 'days_since_receive', 'dev_contact_person']
        ]
        
        # 交底延期分析
        delayed_handover = self.data[self.data['handover_delay_days'] > 0].nlargest(5, 'handover_delay_days')[
            ['requirement_id', 'requirement_name', 'handover_delay_days', 'architect']
        ]
        
        self.analysis_results['efficiency_bottlenecks'] = {
            'processing_days_distribution': processing_days_dist.tolist(),
            'long_stagnant_requirements': long_stagnant.to_dict('records'),
            'delayed_handover_requirements': delayed_handover.to_dict('records')
        }
        
        self.logger.info("效率与瓶颈分析完成")
        
    def analyze_demand_resources(self):
        """分析需求与资源"""
        self.logger.info("开始分析需求与资源")
        
        # 需求类型分析（基于需求名称关键词）
        requirement_types = self.extract_requirement_types()
        
        # 人员工作量分析
        staff_workload = self.analyze_staff_workload()
        
        # 大型项目跟踪
        large_projects = self.analyze_large_projects()
        
        self.analysis_results['demand_resources'] = {
            'requirement_types': requirement_types,
            'staff_workload': staff_workload,
            'large_projects': large_projects
        }
        
        self.logger.info("需求与资源分析完成")
        
    def extract_requirement_types(self):
        """提取需求类型"""
        keywords = {
            '上云改造': ['上云改造', '上云'],
            '系统下线': ['下线'],
            '扩容': ['扩容'],
            '安全相关': ['安全', 'PKI', 'API安全', '防病毒'],
            '网络调整': ['网络', '专线', '域名', 'IP'],
            '数据中台': ['数据中台', '数据'],
            '监控系统': ['监控'],
            '竞猜系统': ['竞猜'],
            '其他': []
        }
        
        type_counts = {k: 0 for k in keywords.keys()}
        
        for _, row in self.data.iterrows():
            req_name = str(row['requirement_name']).lower()
            categorized = False
            
            for type_name, type_keywords in keywords.items():
                if type_name == '其他':
                    continue
                for keyword in type_keywords:
                    if keyword in req_name:
                        type_counts[type_name] += 1
                        categorized = True
                        break
                if categorized:
                    break
            
            if not categorized:
                type_counts['其他'] += 1
                
        return type_counts
        
    def analyze_staff_workload(self):
        """分析人员工作量"""
        # 实施人员工作量
        implementation_workload = {}
        for _, row in self.data[self.data['status'] == '进行中'].iterrows():
            staff_list = str(row['implementation_staff']).split('、')
            for staff in staff_list:
                staff = staff.strip()
                if staff and staff != '未分配' and staff != 'nan':
                    implementation_workload[staff] = implementation_workload.get(staff, 0) + 1
        
        # 架构师工作量
        architect_workload = {}
        for _, row in self.data[self.data['status'] == '进行中'].iterrows():
            architect_list = str(row['architect']).split('、')
            for architect in architect_list:
                architect = architect.strip()
                if architect and architect != '未分配' and architect != 'nan':
                    architect_workload[architect] = architect_workload.get(architect, 0) + 1
        
        return {
            'implementation_staff': dict(sorted(implementation_workload.items(), key=lambda x: x[1], reverse=True)[:10]),
            'architects': dict(sorted(architect_workload.items(), key=lambda x: x[1], reverse=True)[:10])
        }
        
    def analyze_large_projects(self):
        """分析大型项目"""
        # 基于需求ID前缀分组，识别大型项目
        project_groups = {}
        
        for _, row in self.data.iterrows():
            req_id = str(row['requirement_id'])
            # 提取项目基础ID（去除版本号等后缀）
            base_id = re.sub(r'[V\d\.\-]+$', '', req_id)
            
            if base_id not in project_groups:
                project_groups[base_id] = []
            project_groups[base_id].append(row)
        
        # 筛选出有多个子任务的大型项目
        large_projects = []
        for project_id, tasks in project_groups.items():
            if len(tasks) >= 3:  # 至少3个子任务才算大型项目
                completed_tasks = sum(1 for task in tasks if task['status'] == '已完成')
                completion_rate = completed_tasks / len(tasks) * 100
                
                large_projects.append({
                    'project_id': project_id,
                    'total_tasks': len(tasks),
                    'completed_tasks': completed_tasks,
                    'completion_rate': round(completion_rate, 1),
                    'project_name': tasks[0]['requirement_name'][:50] + '...' if len(tasks[0]['requirement_name']) > 50 else tasks[0]['requirement_name']
                })
        
        # 按任务数量排序
        large_projects.sort(key=lambda x: x['total_tasks'], reverse=True)
        
        return large_projects[:10]  # 返回前10个大型项目
        
    def generate_charts(self):
        """生成所有图表"""
        self.logger.info("开始生成图表")
        
        # 生成KPI指标卡片
        self.generate_kpi_cards()
        
        # 生成状态分布饼图
        self.generate_status_pie_chart()
        
        # 生成月度趋势图
        self.generate_monthly_trend_chart()
        
        # 生成处理周期分布图
        self.generate_processing_days_histogram()
        
        # 生成人员工作量图
        self.generate_staff_workload_chart()
        
        # 生成需求类型分布图
        self.generate_requirement_types_chart()
        
        self.logger.info("图表生成完成")
        
    def generate_kpi_cards(self):
        """生成KPI指标卡片"""
        kpi_data = self.analysis_results['kpi_indicators']
        
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=('总需求数', '已完成数', '进行中数', '暂停/取消数', '平均处理周期(天)', '交底准时率(%)'),
            specs=[[{"type": "indicator"}, {"type": "indicator"}, {"type": "indicator"}],
                   [{"type": "indicator"}, {"type": "indicator"}, {"type": "indicator"}]]
        )
        
        # 添加指标
        indicators = [
            (kpi_data['total_requirements'], "总需求数", 1, 1),
            (kpi_data['completed_requirements'], "已完成数", 1, 2),
            (kpi_data['in_progress_requirements'], "进行中数", 1, 3),
            (kpi_data['paused_cancelled_requirements'], "暂停/取消数", 2, 1),
            (kpi_data['avg_processing_days'], "平均处理周期(天)", 2, 2),
            (kpi_data['handover_on_time_rate'], "交底准时率(%)", 2, 3)
        ]
        
        for value, title, row, col in indicators:
            fig.add_trace(
                go.Indicator(
                    mode="number",
                    value=value,
                    title={"text": title, "font": {"size": 16}},
                    number={"font": {"size": 24}}
                ),
                row=row, col=col
            )
        
        fig.update_layout(
            title="核心KPI指标",
            height=400,
            font=dict(family="SimHei", size=12)
        )
        
        # 保存图表
        chart_path = os.path.join(self.output_dir, 'charts', 'kpi_indicators.html')
        pyo.plot(fig, filename=chart_path, auto_open=False)
        
    def generate_status_pie_chart(self):
        """生成需求状态分布饼图"""
        status_data = self.analysis_results['overall_status']['status_distribution']
        
        fig = go.Figure(data=[go.Pie(
            labels=list(status_data.keys()),
            values=list(status_data.values()),
            hole=0.4,
            textinfo='label+percent+value',
            textfont_size=12
        )])
        
        fig.update_layout(
            title="需求状态分布",
            font=dict(family="SimHei", size=12),
            height=500
        )
        
        chart_path = os.path.join(self.output_dir, 'charts', 'status_distribution.html')
        pyo.plot(fig, filename=chart_path, auto_open=False)
        
    def generate_monthly_trend_chart(self):
        """生成月度趋势图"""
        monthly_received = self.analysis_results['overall_status']['monthly_received']
        monthly_completed = self.analysis_results['overall_status']['monthly_completed']
        
        # 转换数据格式
        months = sorted(set(list(monthly_received.keys()) + list(monthly_completed.keys())))
        received_values = [monthly_received.get(month, 0) for month in months]
        completed_values = [monthly_completed.get(month, 0) for month in months]
        
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=months,
            y=received_values,
            name='接收需求数',
            marker_color='lightblue'
        ))
        
        fig.add_trace(go.Scatter(
            x=months,
            y=completed_values,
            mode='lines+markers',
            name='完成需求数',
            line=dict(color='red', width=3),
            marker=dict(size=8)
        ))
        
        fig.update_layout(
            title="需求接收与完成月度趋势",
            xaxis_title="月份",
            yaxis_title="需求数量",
            font=dict(family="SimHei", size=12),
            height=500
        )
        
        chart_path = os.path.join(self.output_dir, 'charts', 'monthly_trend.html')
        pyo.plot(fig, filename=chart_path, auto_open=False)
        
    def generate_processing_days_histogram(self):
        """生成处理周期分布直方图"""
        processing_days = self.analysis_results['efficiency_bottlenecks']['processing_days_distribution']
        
        if processing_days:
            fig = go.Figure(data=[go.Histogram(
                x=processing_days,
                nbinsx=20,
                marker_color='skyblue',
                opacity=0.7
            )])
            
            fig.update_layout(
                title="需求处理周期分布",
                xaxis_title="处理天数",
                yaxis_title="需求数量",
                font=dict(family="SimHei", size=12),
                height=500
            )
            
            chart_path = os.path.join(self.output_dir, 'charts', 'processing_days_histogram.html')
            pyo.plot(fig, filename=chart_path, auto_open=False)
        
    def generate_staff_workload_chart(self):
        """生成人员工作量图表"""
        staff_data = self.analysis_results['demand_resources']['staff_workload']
        
        # 实施人员工作量
        impl_staff = staff_data['implementation_staff']
        if impl_staff:
            fig_impl = go.Figure(data=[go.Bar(
                x=list(impl_staff.values()),
                y=list(impl_staff.keys()),
                orientation='h',
                marker_color='lightgreen'
            )])
            
            fig_impl.update_layout(
                title="实施人员当前工作负载（进行中需求数）",
                xaxis_title="需求数量",
                yaxis_title="实施人员",
                font=dict(family="SimHei", size=12),
                height=400
            )
            
            chart_path = os.path.join(self.output_dir, 'charts', 'implementation_staff_workload.html')
            pyo.plot(fig_impl, filename=chart_path, auto_open=False)
        
        # 架构师工作量
        architects = staff_data['architects']
        if architects:
            fig_arch = go.Figure(data=[go.Bar(
                x=list(architects.values()),
                y=list(architects.keys()),
                orientation='h',
                marker_color='lightcoral'
            )])
            
            fig_arch.update_layout(
                title="基础架构师当前工作负载（进行中需求数）",
                xaxis_title="需求数量",
                yaxis_title="基础架构师",
                font=dict(family="SimHei", size=12),
                height=400
            )
            
            chart_path = os.path.join(self.output_dir, 'charts', 'architects_workload.html')
            pyo.plot(fig_arch, filename=chart_path, auto_open=False)
            
    def generate_requirement_types_chart(self):
        """生成需求类型分布图"""
        req_types = self.analysis_results['demand_resources']['requirement_types']
        
        fig = go.Figure(data=[go.Bar(
            x=list(req_types.keys()),
            y=list(req_types.values()),
            marker_color='lightsteelblue'
        )])
        
        fig.update_layout(
            title="需求类型分布",
            xaxis_title="需求类型",
            yaxis_title="需求数量",
            font=dict(family="SimHei", size=12),
            height=500,
            xaxis_tickangle=-45
        )
        
        chart_path = os.path.join(self.output_dir, 'charts', 'requirement_types.html')
        pyo.plot(fig, filename=chart_path, auto_open=False)
        
    def generate_html_report(self):
        """生成HTML综合报告"""
        self.logger.info("开始生成HTML报告")
        
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求管理数据驾驶舱</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .kpi-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .kpi-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .kpi-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        .chart-container {
            margin: 30px 0;
            text-align: center;
        }
        .chart-frame {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .data-table th {
            background-color: #3498db;
            color: white;
        }
        .data-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .summary-section {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            margin-top: 30px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>需求管理数据驾驶舱</h1>
        
        <div class="summary-section">
            <h2>📊 执行摘要</h2>
            <p>本报告基于需求版本受理跟踪记录表数据，从五个维度全面分析了需求管理现状：</p>
            <ul>
                <li><strong>顶层核心指标：</strong>总体需求处理情况和关键效率指标</li>
                <li><strong>整体态势分析：</strong>需求状态分布和时间趋势</li>
                <li><strong>效率与瓶颈：</strong>处理周期分析和风险识别</li>
                <li><strong>需求与资源：</strong>工作类型分布和人员负载</li>
                <li><strong>数据洞察：</strong>基于数据的管理建议</li>
            </ul>
        </div>
        
        <h2>🎯 核心KPI指标</h2>
        <div class="kpi-grid">
            <div class="kpi-card">
                <div class="kpi-value">{{ kpi_data.total_requirements }}</div>
                <div class="kpi-label">总需求数</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{{ kpi_data.completed_requirements }}</div>
                <div class="kpi-label">已完成数</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{{ kpi_data.completion_rate }}%</div>
                <div class="kpi-label">完成率</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{{ kpi_data.in_progress_requirements }}</div>
                <div class="kpi-label">进行中数</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{{ kpi_data.avg_processing_days }}</div>
                <div class="kpi-label">平均处理周期(天)</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">{{ kpi_data.handover_on_time_rate }}%</div>
                <div class="kpi-label">交底准时率</div>
            </div>
        </div>
        
        <h2>📈 需求状态分布</h2>
        <div class="chart-container">
            <iframe src="charts/status_distribution.html" class="chart-frame"></iframe>
        </div>
        
        <h2>📅 月度趋势分析</h2>
        <div class="chart-container">
            <iframe src="charts/monthly_trend.html" class="chart-frame"></iframe>
        </div>
        
        <h2>⏱️ 处理周期分布</h2>
        <div class="chart-container">
            <iframe src="charts/processing_days_histogram.html" class="chart-frame"></iframe>
        </div>
        
        <h2>👥 人员工作负载</h2>
        <div class="chart-container">
            <iframe src="charts/implementation_staff_workload.html" class="chart-frame"></iframe>
        </div>
        <div class="chart-container">
            <iframe src="charts/architects_workload.html" class="chart-frame"></iframe>
        </div>
        
        <h2>🏷️ 需求类型分布</h2>
        <div class="chart-container">
            <iframe src="charts/requirement_types.html" class="chart-frame"></iframe>
        </div>
        
        <h2>⚠️ 长期停滞需求预警</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>需求ID</th>
                    <th>需求名称</th>
                    <th>当前状态</th>
                    <th>停滞天数</th>
                    <th>对接人</th>
                </tr>
            </thead>
            <tbody>
                {% for req in long_stagnant_requirements %}
                <tr>
                    <td>{{ req.requirement_id }}</td>
                    <td>{{ req.requirement_name[:50] }}...</td>
                    <td>{{ req.status }}</td>
                    <td>{{ req.days_since_receive }}</td>
                    <td>{{ req.dev_contact_person }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <h2>🚀 大型项目进展</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>项目ID</th>
                    <th>项目名称</th>
                    <th>总任务数</th>
                    <th>已完成数</th>
                    <th>完成率</th>
                </tr>
            </thead>
            <tbody>
                {% for project in large_projects %}
                <tr>
                    <td>{{ project.project_id }}</td>
                    <td>{{ project.project_name }}</td>
                    <td>{{ project.total_tasks }}</td>
                    <td>{{ project.completed_tasks }}</td>
                    <td>{{ project.completion_rate }}%</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div class="summary-section">
            <h2>💡 数据洞察与建议</h2>
            <ul>
                <li><strong>完成率分析：</strong>当前需求完成率为 {{ kpi_data.completion_rate }}%，{{ "表现良好" if kpi_data.completion_rate > 70 else "需要改进" }}</li>
                <li><strong>处理效率：</strong>平均处理周期为 {{ kpi_data.avg_processing_days }} 天，建议关注长周期需求的瓶颈原因</li>
                <li><strong>交底管理：</strong>交底准时率为 {{ kpi_data.handover_on_time_rate }}%，{{ "流程规范性较好" if kpi_data.handover_on_time_rate > 80 else "需要加强计划管理" }}</li>
                <li><strong>资源配置：</strong>建议关注工作负载较高的人员，适当进行任务分配调整</li>
                <li><strong>风险管控：</strong>重点关注长期停滞需求，及时跟进和解决阻塞问题</li>
            </ul>
        </div>
        
        <div class="timestamp">
            报告生成时间：{{ generation_time }}
        </div>
    </div>
</body>
</html>
        """
        
        # 使用Jinja2模板渲染
        from jinja2 import Template
        template = Template(html_template)
        
        html_content = template.render(
            kpi_data=self.analysis_results['kpi_indicators'],
            long_stagnant_requirements=self.analysis_results['efficiency_bottlenecks']['long_stagnant_requirements'],
            large_projects=self.analysis_results['demand_resources']['large_projects'],
            generation_time=datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        )
        
        # 保存HTML报告
        report_path = os.path.join(self.output_dir, 'dashboard_report.html')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        self.logger.info(f"HTML报告已生成：{report_path}")
        
    def export_analysis_data(self):
        """导出分析数据"""
        self.logger.info("开始导出分析数据")
        
        # 导出JSON格式的分析结果
        json_path = os.path.join(self.output_dir, 'data', 'analysis_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        # 导出清洗后的数据
        csv_path = os.path.join(self.output_dir, 'data', 'cleaned_data.csv')
        self.data.to_csv(csv_path, index=False, encoding='utf-8')
        
        # 导出Excel格式的汇总报告
        excel_path = os.path.join(self.output_dir, 'data', 'summary_report.xlsx')
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            # KPI指标
            kpi_df = pd.DataFrame([self.analysis_results['kpi_indicators']])
            kpi_df.to_excel(writer, sheet_name='KPI指标', index=False)
            
            # 状态分布
            status_df = pd.DataFrame(list(self.analysis_results['overall_status']['status_distribution'].items()),
                                   columns=['状态', '数量'])
            status_df.to_excel(writer, sheet_name='状态分布', index=False)
            
            # 长期停滞需求
            if self.analysis_results['efficiency_bottlenecks']['long_stagnant_requirements']:
                stagnant_df = pd.DataFrame(self.analysis_results['efficiency_bottlenecks']['long_stagnant_requirements'])
                stagnant_df.to_excel(writer, sheet_name='长期停滞需求', index=False)
            
            # 大型项目
            if self.analysis_results['demand_resources']['large_projects']:
                projects_df = pd.DataFrame(self.analysis_results['demand_resources']['large_projects'])
                projects_df.to_excel(writer, sheet_name='大型项目', index=False)
        
        self.logger.info("分析数据导出完成")
        
    def run_analysis(self):
        """运行完整分析流程"""
        try:
            self.logger.info("开始需求管理数据驾驶舱分析")
            
            # 1. 加载和清洗数据
            self.load_and_clean_data()
            
            # 2. 执行各模块分析
            self.analyze_kpi_indicators()
            self.analyze_overall_status()
            self.analyze_efficiency_bottlenecks()
            self.analyze_demand_resources()
            
            # 3. 生成可视化图表
            self.generate_charts()
            
            # 4. 生成HTML报告
            self.generate_html_report()
            
            # 5. 导出分析数据
            self.export_analysis_data()
            
            self.logger.info(f"分析完成！所有结果已保存到：{self.output_dir}")
            print(f"\n✅ 需求管理数据驾驶舱分析完成！")
            print(f"📁 输出目录：{self.output_dir}")
            print(f"📊 查看报告：{os.path.join(self.output_dir, 'dashboard_report.html')}")
            
        except Exception as e:
            self.logger.error(f"分析过程中发生错误: {str(e)}")
            raise


def main():
    """主函数"""
    # 配置路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    csv_file_path = os.path.join(current_dir, 'files', '需求版本受理跟踪记录表.csv')
    output_dir = os.path.join(current_dir, 'output', 'requirement_dashboard_report')
    
    # 检查输入文件是否存在
    if not os.path.exists(csv_file_path):
        print(f"❌ 错误：找不到输入文件 {csv_file_path}")
        print("请确保CSV文件位于 files/ 目录下")
        return
    
    # 创建分析器并运行分析
    analyzer = RequirementDashboardAnalyzer(csv_file_path, output_dir)
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
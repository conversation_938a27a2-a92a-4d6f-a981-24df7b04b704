#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import xml.etree.ElementTree as ET
import re
from pathlib import Path


def strip_html_tags(text):
    """
    Remove HTML tags from text
    :param text: Text with HTML tags
    :return: Clean text
    """
    if not text:
        return ""
    
    # Remove HTML tags
    clean_text = re.sub(r'<[^>]*>', '', text)
    
    # Remove extra whitespace
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()
    
    return clean_text


def extract_values_and_labels(file_path):
    """
    Extract all values and labels from a .drawio file
    :param file_path: Path to the .drawio file
    :return: List of tuples (value, label)
    """
    try:
        # Parse the XML file
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # Initialize results list
        results = []
        
        # Function to recursively search for elements with value or label attributes
        def search_elements(element):
            # Check for elements with value or label attributes
            value = element.attrib.get('value', '')
            label = element.attrib.get('label', '')
            
            # If element has either value or label, add to results
            if value or label:
                # Clean the text by removing HTML tags
                clean_value = strip_html_tags(value) if value else strip_html_tags(label)
                clean_label = strip_html_tags(label) if label else strip_html_tags(value)
                
                if clean_value:  # Only add if there's a value
                    results.append((clean_value, clean_label))
            
            # Search in all child elements
            for child in element:
                search_elements(child)
        
        # Start the search from root
        search_elements(root)
        
        return results
    
    except Exception as e:
        print(f"Error processing file {file_path}: {str(e)}")
        return []


def process_drawio_files(input_dir, output_dir):
    """
    Process all .drawio files in input directory and write results to output directory
    :param input_dir: Directory containing .drawio files
    :param output_dir: Directory to write output files
    """
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all .drawio files
    input_path = Path(input_dir)
    drawio_files = list(input_path.glob('*.drawio'))
    
    if not drawio_files:
        print(f"在 {input_dir} 中未找到 .drawio 文件")
        return
    
    print(f"找到 {len(drawio_files)} 个 .drawio 文件")
    
    # Process each .drawio file
    for file_path in drawio_files:
        file_name = file_path.stem
        print(f"正在处理 {file_name}...")
        
        # Extract values and labels
        extracted_data = extract_values_and_labels(file_path)
        
        if not extracted_data:
            print(f"在 {file_name} 中未找到值或标签")
            continue
        
        # 只写入details文件，包含values和labels信息
        details_file = os.path.join(output_dir, f"{file_name}_details.txt")
        with open(details_file, 'w', encoding='utf-8') as f:
            for value, label in extracted_data:
                if value == label:
                    f.write(f"{value}\n")
                else:
                    f.write(f"Value: {value} | Label: {label}\n")
        
        print(f"提取了 {len(extracted_data)} 个值/标签，来自 {file_name}")
        print(f"详细信息已写入 {details_file}")


def main():
    # Set default directories
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_dir = os.path.join(script_dir, 'files')
    output_dir = os.path.join(script_dir, 'output')
    
    print("DrawIO 数据提取工具")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    
    # Process the files
    process_drawio_files(input_dir, output_dir)
    

if __name__ == "__main__":
    main()

# Description: 统计指定目录下的 CSV 文件中的 IP 字段的数量和百分比，并保存到 Excel 文件中

import os
import pandas as pd

def process_csv_files(input_dir, output_file, ip_field="解析结果IP"):
    if not os.path.exists(input_dir):
        print(f"目录 {input_dir} 不存在，请检查路径！")
        return

    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for file_name in os.listdir(input_dir):
            if file_name.endswith(".csv"):
                file_path = os.path.join(input_dir, file_name)

                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                except UnicodeDecodeError:
                    print(f"文件 {file_name} 编码错误，请检查文件编码！")
                    continue
                except Exception as e:
                    print(f"无法读取文件 {file_name}，错误：{e}")
                    continue

                if ip_field not in df.columns:
                    print(f"文件 {file_name} 不包含字段 {ip_field}，跳过")
                    continue

                # 统计 IP 字段的数量和百分比
                value_counts = df[ip_field].value_counts(normalize=True) * 100
                result_df = pd.DataFrame({
                    "IP 值": value_counts.index,
                    "百分比 (%)": value_counts.values.round(2)
                })

                # 写入到 Excel 的单独 Sheet
                sheet_name = os.path.splitext(file_name)[0]  # 取文件名作为 Sheet 名
                result_df.to_excel(writer, index=False, sheet_name=sheet_name)
                print(f"文件 {file_name} 的统计结果已写入 Sheet: {sheet_name}")

    print(f"所有统计结果已保存到 {output_file}")

if __name__ == "__main__":
    input_directory = "files/domain"  # 输入目录
    output_excel = "output/result_statistics.xlsx"  # 输出 Excel 文件
    process_csv_files(input_directory, output_excel)
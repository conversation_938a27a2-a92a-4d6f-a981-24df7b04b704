#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件搜索工具 - 输出到Excel
功能：读取files目录下包含"西五环"的Excel文件，遍历所有sheet页，查找包含'4.100.5'的字符串
将包含该字符串的完整记录保存到Excel文件中，按源文件名+sheet名创建工作表
作者：自动生成
日期：2025年
"""

import os
import pandas as pd
import glob
from datetime import datetime
import logging
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def ensure_output_directory():
    """
    确保output目录存在
    """
    output_dir = 'output'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logger.info(f"创建output目录: {output_dir}")
    return output_dir

def find_excel_files_with_keyword(files_dir, keyword):
    """
    在指定目录中查找包含关键字的Excel文件
    
    参数:
        files_dir (str): 要搜索的目录路径
        keyword (str): 要搜索的关键字
    
    返回:
        list: 匹配的Excel文件路径列表
    """
    # 支持的Excel文件扩展名
    excel_extensions = ['*.xlsx', '*.xls']
    matched_files = []
    
    for extension in excel_extensions:
        # 构建搜索模式
        pattern = os.path.join(files_dir, extension)
        files = glob.glob(pattern)
        
        # 筛选包含关键字的文件
        for file_path in files:
            filename = os.path.basename(file_path)
            if keyword in filename:
                matched_files.append(file_path)
                logger.info(f"找到匹配文件: {filename}")
    
    return matched_files

def clean_filename_for_sheet(filename):
    """
    清理文件名，使其适合作为Excel工作表名
    
    参数:
        filename (str): 原始文件名
    
    返回:
        str: 清理后的文件名
    """
    # 移除文件扩展名
    name = os.path.splitext(filename)[0]
    
    # Excel工作表名限制：最长31个字符，不能包含特殊字符
    invalid_chars = ['\\', '/', '*', '?', ':', '[', ']']
    for char in invalid_chars:
        name = name.replace(char, '_')
    
    # 如果名称太长，截取前25个字符
    if len(name) > 25:
        name = name[:25]
    
    return name

def search_in_excel_file(file_path, search_string):
    """
    在Excel文件的所有sheet页中搜索指定字符串，返回按sheet分组的完整行记录
    
    参数:
        file_path (str): Excel文件路径
        search_string (str): 要搜索的字符串
    
    返回:
        dict: 按sheet名分组的搜索结果字典
    """
    results = defaultdict(list)  # 按sheet名分组的结果
    filename = os.path.basename(file_path)
    clean_filename = clean_filename_for_sheet(filename)
    
    try:
        # 读取Excel文件的所有sheet名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        logger.info(f"正在搜索文件: {filename}, 共有 {len(sheet_names)} 个sheet页")
        
        for sheet_name in sheet_names:
            try:
                # 读取每个sheet页
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                logger.info(f"  - 正在搜索sheet: {sheet_name}, 数据形状: {df.shape}")
                
                # 记录已处理的行，避免重复添加同一行
                processed_rows = set()
                matched_rows = []
                
                # 遍历DataFrame中的每个单元格
                for row_idx, row in df.iterrows():
                    # 如果这一行已经被处理过，跳过
                    if row_idx in processed_rows:
                        continue
                        
                    # 检查这一行是否包含搜索字符串
                    row_contains_search = False
                    
                    for col_idx, cell_value in enumerate(row):
                        # 将单元格值转换为字符串进行搜索
                        cell_str = str(cell_value) if pd.notna(cell_value) else ''
                        
                        # 检查是否包含搜索字符串
                        if search_string in cell_str:
                            row_contains_search = True
                            break
                    
                    # 如果这一行包含搜索字符串，保存完整的行数据
                    if row_contains_search:
                        matched_rows.append(row.to_dict())
                        processed_rows.add(row_idx)
                        logger.info(f"    找到匹配行: Row {row_idx + 2}")
                
                # 如果找到匹配行，创建DataFrame并保存到结果中
                if matched_rows:
                    matched_df = pd.DataFrame(matched_rows)
                    # 创建工作表名：清理后的文件名 + sheet名
                    sheet_key = f"{clean_filename}_{sheet_name}"
                    # 确保工作表名不超过31个字符
                    if len(sheet_key) > 31:
                        # 如果太长，截取文件名部分
                        max_filename_len = 31 - len(sheet_name) - 1  # -1 for underscore
                        if max_filename_len > 0:
                            sheet_key = f"{clean_filename[:max_filename_len]}_{sheet_name}"
                        else:
                            sheet_key = sheet_name[:31]
                    
                    results[sheet_key] = matched_df
                    logger.info(f"    {sheet_name} 找到 {len(matched_rows)} 条匹配记录，保存为工作表: {sheet_key}")
                            
            except Exception as e:
                logger.error(f"读取sheet '{sheet_name}' 时出错: {str(e)}")
                continue
                
    except Exception as e:
        logger.error(f"读取文件 '{filename}' 时出错: {str(e)}")
    
    return results

def save_results_to_excel(all_results, output_dir, search_string):
    """
    将搜索结果保存到Excel文件
    
    参数:
        all_results (dict): 所有搜索结果，按工作表名分组
        output_dir (str): 输出目录路径
        search_string (str): 搜索字符串
    
    返回:
        str: 保存的Excel文件路径
    """
    if not all_results:
        logger.warning("没有搜索结果需要保存")
        return None
        
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_filename = f'搜索结果_{search_string.replace(".", "_")}_{timestamp}.xlsx'
    excel_path = os.path.join(output_dir, excel_filename)
    
    try:
        # 使用ExcelWriter保存多个工作表
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            total_records = 0
            
            for sheet_name, df in all_results.items():
                # 保存每个工作表
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                total_records += len(df)
                logger.info(f"工作表 '{sheet_name}' 已保存，包含 {len(df)} 条记录")
        
        logger.info(f"搜索结果已保存到: {excel_path}")
        logger.info(f"共保存 {len(all_results)} 个工作表，总计 {total_records} 条匹配记录")
        
    except Exception as e:
        logger.error(f"保存Excel文件时出错: {str(e)}")
        raise
    
    return excel_path

def main():
    """
    主函数：执行完整的搜索流程
    """
    # 配置参数
    files_dir = 'files'  # Excel文件所在目录
    keyword = '西五环'    # 文件名关键字
    search_string = 'TCloud-LG'  # 要搜索的字符串
    
    logger.info("开始执行Excel文件搜索任务（输出到Excel）")
    logger.info(f"搜索目录: {files_dir}")
    logger.info(f"文件名关键字: {keyword}")
    logger.info(f"搜索字符串: {search_string}")
    
    try:
        # 1. 确保输出目录存在
        output_dir = ensure_output_directory()
        
        # 2. 查找包含关键字的Excel文件
        excel_files = find_excel_files_with_keyword(files_dir, keyword)
        
        if not excel_files:
            logger.warning(f"在目录 '{files_dir}' 中没有找到包含 '{keyword}' 的Excel文件")
            return
        
        logger.info(f"共找到 {len(excel_files)} 个匹配的Excel文件")
        
        # 3. 在每个Excel文件中搜索指定字符串
        all_results = {}
        
        for file_path in excel_files:
            file_results = search_in_excel_file(file_path, search_string)
            # 合并结果
            all_results.update(file_results)
        
        # 4. 保存搜索结果到Excel文件
        if all_results:
            excel_path = save_results_to_excel(all_results, output_dir, search_string)
            if excel_path:
                logger.info(f"任务完成！结果已保存到: {excel_path}")
                
                # 显示保存的工作表信息
                logger.info("保存的工作表列表:")
                for sheet_name, df in all_results.items():
                    logger.info(f"  - {sheet_name}: {len(df)} 条记录，{len(df.columns)} 个字段")
        else:
            logger.info(f"在所有文件中都没有找到包含 '{search_string}' 的记录")
            
            # 创建一个空的Excel文件记录本次搜索
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            excel_filename = f'搜索结果_{search_string.replace(".", "_")}_{timestamp}_empty.xlsx'
            excel_path = os.path.join(output_dir, excel_filename)
            
            # 创建空的DataFrame
            empty_df = pd.DataFrame({'搜索结果': [f'未找到包含"{search_string}"的记录'], 
                                   '搜索时间': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')]})
            
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                empty_df.to_excel(writer, sheet_name='搜索结果', index=False)
            
            logger.info(f"空结果文件已保存到: {excel_path}")
        
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main() 
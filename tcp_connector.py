#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TCP连接工具
用于连接指定的公网IP或域名和端口，并将结果保存到output/tcp/目录下
"""

import socket
import os
import json
import time
from datetime import datetime
from typing import Optional, Dict, Any
import argparse
import sys


class TCPConnector:
    def __init__(self, output_dir: str = "output/tcp"):
        """
        初始化TCP连接器
        
        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = output_dir
        self.ensure_output_dir()
    
    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
            print(f"创建输出目录: {self.output_dir}")
    
    def connect_and_test(self, host: str, port: int, timeout: int = 10, 
                        send_data: Optional[str] = None) -> Dict[str, Any]:
        """
        连接到指定主机和端口，并进行测试
        
        Args:
            host: 主机名或IP地址
            port: 端口号
            timeout: 连接超时时间（秒）
            send_data: 要发送的数据（可选）
            
        Returns:
            包含连接结果的字典
        """
        result = {
            "host": host,
            "port": port,
            "timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "response_time": None,
            "sent_data": send_data,
            "received_data": None,
            "connection_info": {}
        }
        
        start_time = time.time()
        
        try:
            # 创建socket连接
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(timeout)
                
                print(f"正在连接到 {host}:{port}...")
                
                # 尝试连接
                sock.connect((host, port))
                connection_time = time.time() - start_time
                result["response_time"] = round(connection_time * 1000, 2)  # 毫秒
                
                print(f"连接成功! 响应时间: {result['response_time']}ms")
                
                # 获取连接信息
                local_addr = sock.getsockname()
                remote_addr = sock.getpeername()
                result["connection_info"] = {
                    "local_address": f"{local_addr[0]}:{local_addr[1]}",
                    "remote_address": f"{remote_addr[0]}:{remote_addr[1]}"
                }
                
                # 如果有数据要发送
                if send_data:
                    print(f"发送数据: {send_data}")
                    sock.send(send_data.encode('utf-8'))
                    
                    # 尝试接收响应
                    try:
                        sock.settimeout(5)  # 设置接收超时
                        response = sock.recv(4096)
                        if response:
                            result["received_data"] = response.decode('utf-8', errors='ignore')
                            print(f"接收到响应: {result['received_data'][:100]}...")
                    except socket.timeout:
                        print("接收响应超时")
                    except Exception as e:
                        print(f"接收数据时出错: {e}")
                
                result["success"] = True
                
        except socket.timeout:
            result["error"] = f"连接超时 ({timeout}秒)"
            print(f"连接失败: {result['error']}")
        except socket.gaierror as e:
            result["error"] = f"域名解析失败: {e}"
            print(f"连接失败: {result['error']}")
        except ConnectionRefusedError:
            result["error"] = "连接被拒绝"
            print(f"连接失败: {result['error']}")
        except Exception as e:
            result["error"] = f"连接错误: {e}"
            print(f"连接失败: {result['error']}")
        
        return result
    
    def save_result(self, result: Dict[str, Any], filename: Optional[str] = None):
        """
        保存连接结果到文件
        
        Args:
            result: 连接结果字典
            filename: 文件名（可选，默认根据时间戳生成）
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tcp_test_{result['host']}_{result['port']}_{timestamp}.json"
        
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {filepath}")
        except Exception as e:
            print(f"保存结果时出错: {e}")
    
    def batch_test(self, targets: list, timeout: int = 10, send_data: Optional[str] = None):
        """
        批量测试多个目标
        
        Args:
            targets: 目标列表，每个元素为 (host, port) 元组
            timeout: 连接超时时间
            send_data: 要发送的数据
        """
        results = []
        
        for i, (host, port) in enumerate(targets, 1):
            print(f"\n[{i}/{len(targets)}] 测试 {host}:{port}")
            result = self.connect_and_test(host, port, timeout, send_data)
            results.append(result)
            self.save_result(result)
            
            # 避免过于频繁的连接
            if i < len(targets):
                time.sleep(1)
        
        # 保存汇总结果
        summary = {
            "total_tests": len(results),
            "successful_connections": sum(1 for r in results if r["success"]),
            "failed_connections": sum(1 for r in results if not r["success"]),
            "test_timestamp": datetime.now().isoformat(),
            "results": results
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_filename = f"tcp_batch_test_summary_{timestamp}.json"
        self.save_result(summary, summary_filename)
        
        print(f"\n批量测试完成:")
        print(f"总测试数: {summary['total_tests']}")
        print(f"成功连接: {summary['successful_connections']}")
        print(f"失败连接: {summary['failed_connections']}")


def interactive_mode(connector: TCPConnector):
    """交互式模式"""
    print("=== TCP连接测试工具 - 交互模式 ===")
    print("输入 'quit' 或 'exit' 退出程序")
    print("输入 'batch' 进入批量测试模式")
    print()
    
    while True:
        try:
            # 获取主机名或IP
            host_input = input("请输入IP地址或域名: ").strip()
            if host_input.lower() in ['quit', 'exit', 'q']:
                print("退出程序")
                break
            elif host_input.lower() == 'batch':
                batch_file = input("请输入批量测试文件路径 (默认: tcp_targets_example.txt): ").strip()
                if not batch_file:
                    batch_file = "tcp_targets_example.txt"
                
                if os.path.exists(batch_file):
                    try:
                        targets = []
                        with open(batch_file, 'r', encoding='utf-8') as f:
                            for line in f:
                                line = line.strip()
                                if line and not line.startswith('#'):
                                    if ':' in line:
                                        host, port = line.rsplit(':', 1)
                                        targets.append((host, int(port)))
                        
                        if targets:
                            timeout = input("请输入连接超时时间（秒，默认10）: ").strip()
                            timeout = int(timeout) if timeout.isdigit() else 10
                            
                            send_data = input("请输入要发送的数据（可选，直接回车跳过）: ").strip()
                            send_data = send_data if send_data else None
                            
                            print(f"\n开始批量测试 {len(targets)} 个目标...")
                            connector.batch_test(targets, timeout, send_data)
                        else:
                            print("批量测试文件中没有找到有效目标")
                    except Exception as e:
                        print(f"批量测试时出错: {e}")
                else:
                    print(f"文件不存在: {batch_file}")
                continue
            
            if not host_input:
                print("请输入有效的IP地址或域名")
                continue
            
            # 获取端口号
            port_input = input("请输入端口号: ").strip()
            if not port_input.isdigit():
                print("请输入有效的端口号")
                continue
            
            port = int(port_input)
            if port < 1 or port > 65535:
                print("端口号必须在1-65535之间")
                continue
            
            # 获取超时时间（可选）
            timeout_input = input("请输入连接超时时间（秒，默认10）: ").strip()
            timeout = int(timeout_input) if timeout_input.isdigit() else 10
            
            # 获取要发送的数据（可选）
            send_data = input("请输入要发送的数据（可选，直接回车跳过）: ").strip()
            send_data = send_data if send_data else None
            
            print(f"\n开始测试连接...")
            result = connector.connect_and_test(host_input, port, timeout, send_data)
            connector.save_result(result)
            
            # 显示结果摘要
            if result["success"]:
                print(f"✅ 连接成功! 响应时间: {result['response_time']}ms")
                if result["received_data"]:
                    print(f"📥 接收到数据: {result['received_data'][:100]}...")
            else:
                print(f"❌ 连接失败: {result['error']}")
            
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")
            print("-" * 50)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="TCP连接测试工具")
    parser.add_argument("host", nargs='?', help="目标主机名或IP地址")
    parser.add_argument("port", nargs='?', type=int, help="目标端口号")
    parser.add_argument("-t", "--timeout", type=int, default=10, help="连接超时时间（秒，默认10）")
    parser.add_argument("-d", "--data", help="要发送的数据")
    parser.add_argument("-o", "--output", default="output/tcp", help="输出目录（默认output/tcp）")
    parser.add_argument("--batch", help="批量测试文件路径（每行格式：host:port）")
    parser.add_argument("-i", "--interactive", action="store_true", help="启动交互模式")
    
    args = parser.parse_args()
    
    connector = TCPConnector(args.output)
    
    # 如果没有提供参数或明确指定交互模式，则启动交互模式
    if args.interactive or (not args.host and not args.batch):
        interactive_mode(connector)
        return
    
    if args.batch:
        # 批量测试模式
        try:
            targets = []
            with open(args.batch, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        if ':' in line:
                            host, port = line.rsplit(':', 1)
                            targets.append((host, int(port)))
                        else:
                            print(f"跳过无效行: {line}")
            
            if targets:
                connector.batch_test(targets, args.timeout, args.data)
            else:
                print("未找到有效的测试目标")
        except FileNotFoundError:
            print(f"批量测试文件不存在: {args.batch}")
        except Exception as e:
            print(f"读取批量测试文件时出错: {e}")
    elif args.host and args.port:
        # 单个测试模式
        result = connector.connect_and_test(args.host, args.port, args.timeout, args.data)
        connector.save_result(result)
    else:
        print("请提供主机名和端口号，或使用 -i 参数启动交互模式")


if __name__ == "__main__":
    main()

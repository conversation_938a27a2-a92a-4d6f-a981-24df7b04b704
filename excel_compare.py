import pandas as pd
import os
from openpyxl import load_workbook
from openpyxl.styles import Pat<PERSON>Fill, Font
import ntpath
import sys
import time


def path_leaf(path):
    """从文件路径中提取文件名"""
    head, tail = ntpath.split(path)
    return tail or ntpath.basename(head)


def get_valid_file_path(prompt, must_exist=True):
    """获取有效的文件路径，支持重试功能"""
    while True:
        file_path = input(prompt).strip()

        # 检查用户是否想要退出
        if file_path.lower() in ['q', 'quit', 'exit']:
            print("程序已取消。")
            sys.exit(0)

        # 检查文件是否存在
        if must_exist and not os.path.exists(file_path):
            print(f"错误: 文件 '{file_path}' 不存在!")
            retry = input("是否重新输入路径? (y/n): ").strip().lower()
            if retry != 'y':
                print("程序已取消。")
                sys.exit(0)
            continue

        # 对于输出文件，检查目录是否存在
        if not must_exist:
            output_dir = os.path.dirname(file_path)
            if output_dir and not os.path.exists(output_dir):
                print(f"错误: 目录 '{output_dir}' 不存在!")
                create = input("是否创建该目录? (y/n): ").strip().lower()
                if create == 'y':
                    try:
                        os.makedirs(output_dir, exist_ok=True)
                    except Exception as e:
                        print(f"创建目录时出错: {e}")
                        continue
                else:
                    retry = input("是否重新输入路径? (y/n): ").strip().lower()
                    if retry != 'y':
                        print("程序已取消。")
                        sys.exit(0)
                    continue

        # 检查文件扩展名（对于输出文件）
        if not must_exist:
            if not file_path.lower().endswith(('.xlsx', '.xlsm', '.xltx', '.xltm')):
                print("错误: 输出文件必须是支持的Excel格式 (.xlsx, .xlsm, .xltx, .xltm)")
                print("将自动添加.xlsx扩展名")
                file_path = file_path + ".xlsx"

        return file_path


def get_valid_column_name(df, file_name, prompt):
    """获取有效的列名，支持重试功能"""
    while True:
        col_name = input(prompt).strip()

        # 检查用户是否想要退出
        if col_name.lower() in ['q', 'quit', 'exit']:
            print("程序已取消。")
            sys.exit(0)

        # 检查列名是否存在
        if col_name not in df.columns:
            print(f"错误: {file_name} 中不存在列 '{col_name}'!")
            # 显示可用的列名
            print(f"{file_name} 的可用列名:")
            for i, col in enumerate(df.columns):
                print(f"{i + 1}. {col}")

            retry = input("是否重新输入列名? (y/n): ").strip().lower()
            if retry != 'y':
                print("程序已取消。")
                sys.exit(0)
            continue

        return col_name


def read_excel_with_retry(file_path, file_name):
    """读取Excel文件，支持重试功能"""
    while True:
        try:
            return pd.read_excel(file_path)
        except Exception as e:
            print(f"读取 {file_name} 时出错: {e}")
            retry = input("是否重新输入文件路径? (y/n): ").strip().lower()
            if retry != 'y':
                print("程序已取消。")
                sys.exit(0)

            file_path = get_valid_file_path(f"请输入 {file_name} 的文件路径: ", must_exist=True)


def compare_excel_files():
    """
    交互式Excel文件对比程序，包含错误处理和重试功能
    """
    print("===== Excel文件对比工具 =====")
    print("在任何输入提示下输入 'q'、'quit' 或 'exit' 可退出程序。")

    # 获取有效的文件路径
    file1_path = get_valid_file_path("请输入第一个Excel文件路径: ", must_exist=True)
    file2_path = get_valid_file_path("请输入第二个Excel文件路径: ", must_exist=True)
    output_path = get_valid_file_path("请输入结果文件保存路径: ", must_exist=False)

    # 确保输出路径有正确的Excel扩展名
    if not output_path.lower().endswith(('.xlsx', '.xlsm', '.xltx', '.xltm')):
        output_path = output_path + ".xlsx"
        print(f"输出文件路径已调整为: {output_path}")

    # 提取真实文件名（不含路径和扩展名）
    file1_name = os.path.splitext(path_leaf(file1_path))[0]
    file2_name = os.path.splitext(path_leaf(file2_path))[0]

    print(f"\n对比文件: {file1_name} 和 {file2_name}")

    # 读取Excel文件（包含重试功能）
    try:
        df1 = read_excel_with_retry(file1_path, file1_name)
        df2 = read_excel_with_retry(file2_path, file2_name)
    except Exception as e:
        print(f"无法读取文件: {e}")
        return

    # 显示文件1的列名
    print(f"\n{file1_name} 的列名:")
    for i, col in enumerate(df1.columns):
        print(f"{i + 1}. {col}")

    # 显示文件2的列名
    print(f"\n{file2_name} 的列名:")
    for i, col in enumerate(df2.columns):
        print(f"{i + 1}. {col}")

    # 获取要对比的有效列名
    col1_name = get_valid_column_name(df1, file1_name, f"\n请输入 {file1_name} 中要对比的列名: ")
    col2_name = get_valid_column_name(df2, file2_name, f"请输入 {file2_name} 中要对比的列名: ")

    # 添加源文件标识列
    df1['来源'] = file1_name
    df2['来源'] = file2_name

    # 添加行号和位置信息以便于后续参考
    df1['原始行号'] = df1.index + 2  # +2 因为Excel是从1开始计数，且有标题行
    df2['原始行号'] = df2.index + 2

    # 创建比较列的小写版本(用于不区分大小写的比较)
    df1['比较值'] = df1[col1_name].astype(str).str.lower()
    df2['比较值'] = df2[col2_name].astype(str).str.lower()

    # 标记匹配状态
    df1['匹配状态'] = f'仅在 {file1_name} 中'
    df2['匹配状态'] = f'仅在 {file2_name} 中'

    # 查找匹配项
    matched_values = set(df1['比较值']) & set(df2['比较值'])

    # 标记匹配的行
    df1.loc[df1['比较值'].isin(matched_values), '匹配状态'] = '匹配'
    df2.loc[df2['比较值'].isin(matched_values), '匹配状态'] = '匹配'

    # 创建详细匹配信息
    match_details = []
    for value in matched_values:
        rows1 = df1[df1['比较值'] == value]
        rows2 = df2[df2['比较值'] == value]

        for _, row1 in rows1.iterrows():
            for _, row2 in rows2.iterrows():
                match_details.append({
                    '匹配值': row1[col1_name],  # 使用原始值而不是小写值
                    f'{file1_name}行号': row1['原始行号'],
                    f'{file2_name}行号': row2['原始行号'],
                    f'{file1_name}单元格': f"{col1_name}{row1['原始行号']}",
                    f'{file2_name}单元格': f"{col2_name}{row2['原始行号']}",
                    f'{file1_name}_{col1_name}': row1[col1_name],
                    f'{file2_name}_{col2_name}': row2[col2_name]
                })

    # 创建匹配详情DataFrame
    match_details_df = pd.DataFrame(match_details)

    # 删除临时列
    df1 = df1.drop(columns=['比较值'])
    df2 = df2.drop(columns=['比较值'])

    # 重命名可能冲突的列
    duplicate_columns = set(df1.columns) & set(df2.columns) - {'来源', '匹配状态', '原始行号'}
    for col in duplicate_columns:
        if col != col1_name or col != col2_name:  # 对比列保持原名
            df1 = df1.rename(columns={col: col + f'_{file1_name}'})
            df2 = df2.rename(columns={col: col + f'_{file2_name}'})

    # 合并文件
    result_df = pd.concat([df1, df2], ignore_index=True)

    # 获取统计信息
    file1_only = df1[df1['匹配状态'] == f'仅在 {file1_name} 中']
    file2_only = df2[df2['匹配状态'] == f'仅在 {file2_name} 中']
    file1_matched = df1[df1['匹配状态'] == '匹配']
    file2_matched = df2[df2['匹配状态'] == '匹配']

    # 创建匹配统计DataFrame
    match_stats = []
    for value in matched_values:
        value_lower = value.lower() if isinstance(value, str) else value
        count1 = len(df1[df1[col1_name].astype(str).str.lower() == value_lower])
        count2 = len(df2[df2[col2_name].astype(str).str.lower() == value_lower])

        # 找到一个匹配的原始值用于显示
        original_value = next(
            (row[col1_name] for _, row in df1.iterrows()
             if str(row[col1_name]).lower() == value_lower), value)

        match_stats.append({
            '匹配值': original_value,
            f'{file1_name}记录数': count1,
            f'{file2_name}记录数': count2
        })

    match_stats_df = pd.DataFrame(match_stats)

    # 保存结果，带重试功能
    while True:
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 写入主结果表
                result_df.to_excel(writer, sheet_name='合并结果', index=False)

                # 写入匹配详情表
                if not match_details_df.empty:
                    match_details_df.to_excel(writer, sheet_name='匹配详情', index=False)

                # 写入匹配统计表
                if not match_stats_df.empty:
                    match_stats_df.to_excel(writer, sheet_name='匹配统计', index=False)

                # 写入文件1独有项
                if not file1_only.empty:
                    sheet_name = f'{file1_name}独有项'[:31]  # Excel工作表名最长31个字符
                    file1_only.to_excel(writer, sheet_name=sheet_name, index=False)

                # 写入文件2独有项
                if not file2_only.empty:
                    sheet_name = f'{file2_name}独有项'[:31]  # Excel工作表名最长31个字符
                    file2_only.to_excel(writer, sheet_name=sheet_name, index=False)

            # 先等待文件写入完成
            time.sleep(1)

            # 添加格式
            apply_formatting(output_path, file1_name, file2_name)
            break

        except Exception as e:
            print(f"保存结果文件时出错: {e}")
            retry = input("是否重新输入输出文件路径? (y/n): ").strip().lower()
            if retry != 'y':
                print("结果文件未保存。程序已退出。")
                return

            output_path = get_valid_file_path("请输入结果文件保存路径: ", must_exist=False)
            # 确保输出路径有正确的Excel扩展名
            if not output_path.lower().endswith(('.xlsx', '.xlsm', '.xltx', '.xltm')):
                output_path = output_path + ".xlsx"
                print(f"输出文件路径已调整为: {output_path}")

    print(f"\n比较完成! 结果已保存到: {output_path}")
    print(f"匹配项数量: {len(matched_values)}")
    print(f"仅在{file1_name}中的行数: {len(file1_only)}")
    print(f"仅在{file2_name}中的行数: {len(file2_only)}")


def apply_formatting(file_path, file1_name, file2_name):
    """为Excel文件添加格式，带重试功能"""
    # 确保文件路径有正确的扩展名
    if not file_path.lower().endswith(('.xlsx', '.xlsm', '.xltx', '.xltm')):
        print(f"错误: '{file_path}' 不是有效的Excel文件格式.")
        print("无法应用格式，但数据已保存。")
        return

    max_retries = 3
    for attempt in range(max_retries):
        try:
            # 确认文件存在
            if not os.path.exists(file_path):
                print(f"错误: 文件 '{file_path}' 不存在，无法应用格式。")
                return

            # 尝试打开文件前先等待一下以确保文件完全写入
            time.sleep(1)

            wb = load_workbook(filename=file_path)

            # 定义填充样式
            match_fill = PatternFill(start_color="CCFFCC", end_color="CCFFCC", fill_type="solid")  # 浅绿色
            file1_only_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")  # 浅红色
            file2_only_fill = PatternFill(start_color="CCCCFF", end_color="CCCCFF", fill_type="solid")  # 浅蓝色

            # 格式化合并结果工作表
            if '合并结果' in wb.sheetnames:
                ws = wb['合并结果']
                # 为标题行添加加粗
                for cell in ws[1]:
                    cell.font = Font(bold=True)

                # 确定匹配状态列
                status_col = None
                for idx, col in enumerate(ws[1]):
                    if col.value == '匹配状态':
                        status_col = idx + 1  # 转换为1-based索引
                        break

                if status_col:
                    # 从第二行开始应用格式
                    for row in range(2, ws.max_row + 1):
                        status = ws.cell(row=row, column=status_col).value
                        if status == '匹配':
                            for col in range(1, ws.max_column + 1):
                                ws.cell(row=row, column=col).fill = match_fill
                        elif status == f'仅在 {file1_name} 中':
                            for col in range(1, ws.max_column + 1):
                                ws.cell(row=row, column=col).fill = file1_only_fill
                        elif status == f'仅在 {file2_name} 中':
                            for col in range(1, ws.max_column + 1):
                                ws.cell(row=row, column=col).fill = file2_only_fill

            # 保存带有格式的工作簿
            wb.save(file_path)
            print("格式应用成功!")
            return

        except Exception as e:
            if attempt < max_retries - 1:
                print(f"添加格式时出错: {e}. 正在重试 ({attempt + 1}/{max_retries})...")
                # 延迟以防止文件访问冲突
                time.sleep(2)
            else:
                print(f"添加格式时出错: {e}. 已达到最大重试次数.")
                print("结果已保存，但未应用格式。")


if __name__ == "__main__":
    try:
        compare_excel_files()
    except KeyboardInterrupt:
        print("\n程序被用户中断。")
    except Exception as e:
        print(f"\n程序出错: {e}")
        input("按Enter键退出...")
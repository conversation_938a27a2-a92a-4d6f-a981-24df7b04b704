#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频学习网站自动化核心模块
"""

import asyncio
import time
from typing import Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, Dialog

from config import Config
from utils import Logger, random_delay, get_speed_control_script, get_video_status_script, get_skip_to_end_script


class VideoAutomation:
    """视频自动化主类"""

    def __init__(self):
        self.logger = Logger()
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None

    async def initialize_browser(self):
        """初始化浏览器"""
        self.logger.info("启动浏览器...")
        playwright = await async_playwright().start()

        # 使用系统安装的Chrome浏览器（网站只支持Chrome）
        try:
            self.browser = await playwright.chromium.launch(
                headless=Config.BROWSER_CONFIG['headless'],
                executable_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            self.logger.info("使用系统Chrome浏览器")
        except Exception as e:
            self.logger.warning(f"无法找到Chrome: {e}")
            self.logger.info("尝试使用Chromium...")
            self.browser = await playwright.chromium.launch(
                headless=Config.BROWSER_CONFIG['headless'],
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--single-process'
                ]
            )

        self.page = await self.browser.new_page()
        self.page.on("dialog", self._handle_dialog)

        self.logger.success("浏览器启动成功")

    async def _handle_dialog(self, dialog: Dialog):
        """处理对话框"""
        self.logger.info(f"对话框: {dialog.message}")
        await dialog.accept()
    async def navigate_to_video(self, video_url: str):
        """导航到视频页面"""
        self.logger.info(f"访问视频页面: {video_url}")

        await self.page.goto(video_url, timeout=Config.TIMEOUT_CONFIG['page_load'] * 1000)
        await random_delay()

        # 检查是否重定向到登录页面
        if "login" in self.page.url.lower():
            self.logger.info("重定向到登录页面，开始登录")
            await self._handle_login()
            # 登录后重新访问视频页面
            await self.page.goto(video_url)

        self.logger.success("成功进入视频页面")
    async def _handle_login(self):
        """处理登录流程"""
        self.logger.info("开始登录...")

        # 等待页面加载
        await self.page.wait_for_load_state()

        # 根据实际页面结构填写登录信息
        # 身份证号或手机号输入框
        username_input = await self.page.wait_for_selector('input[placeholder*="身份证号或手机号"]')
        await username_input.fill(Config.USERNAME)

        # 密码输入框
        password_input = await self.page.wait_for_selector('input[placeholder*="密码"]')
        await password_input.fill(Config.PASSWORD)

        # 手机验证码输入框
        sms_input = await self.page.wait_for_selector('input[placeholder*="手机验证码"]')

        # 点击获取验证码按钮
        get_code_btn = await self.page.query_selector('text=获 取')
        if get_code_btn:
            await get_code_btn.click()
            self.logger.info("已点击获取验证码")

        # 等待用户输入验证码
        self.logger.warning("请在浏览器中输入手机验证码...")

        # 等待验证码输入完成（检查输入框是否有值）
        start_time = time.time()
        while time.time() - start_time < Config.TIMEOUT_CONFIG['login_wait']:
            sms_value = await sms_input.input_value()
            if sms_value and len(sms_value) >= 4:  # 假设验证码至少4位
                break
            await asyncio.sleep(1)

        # 点击登录按钮
        login_btn = await self.page.wait_for_selector('button:has-text("登 录")')
        await login_btn.click()

        # 等待登录完成
        await self.page.wait_for_url(lambda url: "login" not in url.lower(), timeout=30000)
        self.logger.success("登录成功")
    async def control_video_playback(self):
        """控制视频播放"""
        self.logger.info("查找视频元素...")

        # 等待视频元素加载
        await self.page.wait_for_selector('video', timeout=Config.TIMEOUT_CONFIG['video_load'] * 1000)

        # 设置高倍速播放
        playback_rate = Config.VIDEO_CONFIG['playback_rate']
        self.logger.info(f"设置 {playback_rate} 倍速播放...")

        success = await self.page.evaluate(get_speed_control_script(playback_rate))
        if success:
            self.logger.success(f"已设置 {playback_rate} 倍速")

        # 如果配置允许，直接跳到末尾
        if Config.VIDEO_CONFIG['skip_to_end']:
            self.logger.info("跳转到视频末尾...")
            skip_success = await self.page.evaluate(get_skip_to_end_script())
            if skip_success:
                self.logger.success("已跳转到末尾")

        # 监控播放进度
        await self._monitor_video_progress()
    async def _monitor_video_progress(self):
        """监控视频播放进度"""
        self.logger.info("监控播放进度...")

        last_progress = 0

        while True:
            try:
                # 获取视频状态
                status = await self.page.evaluate(get_video_status_script())

                if not status:
                    await asyncio.sleep(2)
                    continue

                progress = float(status.get('progress', 0))
                ended = status.get('ended', False)

                # 显示进度
                if progress != last_progress:
                    self.logger.info(f"播放进度: {progress}%")
                    last_progress = progress

                # 检查是否播放完成
                if ended or progress >= 99:
                    self.logger.success("视频播放完成！")
                    break

                await asyncio.sleep(3)

            except Exception as e:
                self.logger.error(f"监控进度出错: {str(e)}")
                await asyncio.sleep(5)
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        self.logger.info("清理完成")

    async def run(self, video_url: str):
        """运行自动化流程"""
        try:
            await self.initialize_browser()
            await self.navigate_to_video(video_url)
            await self.control_video_playback()

            self.logger.info("等待页面自动关闭...")
            await asyncio.sleep(10)

        except Exception as e:
            self.logger.error(f"执行失败: {str(e)}")
            raise
        finally:
            await self.cleanup()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频学习网站自动化核心模块
"""

import asyncio
import time
from typing import Optional, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, <PERSON>, Dialog

from config import Config
from utils import Lo<PERSON>, DelayHelper, RetryHelper, VideoSpeedController, format_time


class VideoAutomation:
    """视频自动化主类"""
    
    def __init__(self):
        self.logger = Logger()
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.video_url: Optional[str] = None
        
    async def initialize_browser(self):
        """初始化浏览器"""
        try:
            self.logger.info("正在启动浏览器...")
            playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await playwright.chromium.launch(
                headless=Config.BROWSER_CONFIG['headless'],
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport=Config.BROWSER_CONFIG['viewport'],
                user_agent=Config.BROWSER_CONFIG['user_agent']
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置对话框处理
            self.page.on("dialog", self._handle_dialog)
            
            self.logger.success("浏览器启动成功")
            
        except Exception as e:
            self.logger.error(f"浏览器启动失败: {str(e)}")
            raise
    
    async def _handle_dialog(self, dialog: Dialog):
        """处理页面对话框"""
        self.logger.info(f"检测到对话框: {dialog.type} - {dialog.message}")
        
        if dialog.type in ['alert', 'confirm']:
            self.logger.info("自动点击确定按钮")
            await dialog.accept()
        else:
            await dialog.dismiss()
    
    async def navigate_to_video(self, video_url: str):
        """导航到视频页面"""
        try:
            self.video_url = video_url
            self.logger.info(f"正在访问视频页面: {video_url}")
            
            await self.page.goto(video_url, timeout=Config.TIMEOUT_CONFIG['page_load'] * 1000)
            await DelayHelper.random_delay()
            
            # 检查是否重定向到登录页面
            current_url = self.page.url
            if "login" in current_url.lower():
                self.logger.info("检测到重定向到登录页面，开始登录流程")
                await self._handle_login()
            else:
                self.logger.info("直接进入视频页面")
                
        except Exception as e:
            self.logger.error(f"访问视频页面失败: {str(e)}")
            raise
    
    async def _handle_login(self):
        """处理登录流程"""
        try:
            self.logger.info("开始登录流程...")
            
            # 等待登录表单加载
            await self.page.wait_for_selector('input[type="text"], input[name*="user"], input[id*="user"]', 
                                            timeout=Config.TIMEOUT_CONFIG['element_wait'] * 1000)
            
            # 查找用户名输入框
            username_selectors = [
                'input[name="username"]',
                'input[name="user"]',
                'input[id="username"]',
                'input[id="user"]',
                'input[type="text"]'
            ]
            
            username_input = None
            for selector in username_selectors:
                try:
                    username_input = await self.page.query_selector(selector)
                    if username_input:
                        break
                except:
                    continue
            
            if not username_input:
                raise Exception("未找到用户名输入框")
            
            # 输入用户名
            self.logger.info("输入用户名...")
            await username_input.clear()
            await DelayHelper.typing_delay()
            await username_input.type(Config.USERNAME, delay=100)
            
            # 查找密码输入框
            password_selectors = [
                'input[name="password"]',
                'input[name="pwd"]',
                'input[id="password"]',
                'input[id="pwd"]',
                'input[type="password"]'
            ]
            
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = await self.page.query_selector(selector)
                    if password_input:
                        break
                except:
                    continue
            
            if not password_input:
                raise Exception("未找到密码输入框")
            
            # 输入密码
            self.logger.info("输入密码...")
            await password_input.clear()
            await DelayHelper.typing_delay()
            await password_input.type(Config.PASSWORD, delay=100)
            
            await DelayHelper.human_like_delay()
            
            # 查找并点击登录按钮
            login_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("登录")',
                'button:has-text("登陆")',
                'button:has-text("确定")',
                '.login-btn',
                '#login-btn'
            ]
            
            login_button = None
            for selector in login_selectors:
                try:
                    login_button = await self.page.query_selector(selector)
                    if login_button:
                        break
                except:
                    continue
            
            if login_button:
                self.logger.info("点击登录按钮...")
                await login_button.click()
            else:
                # 尝试按回车键提交
                self.logger.info("未找到登录按钮，尝试按回车键提交...")
                await self.page.keyboard.press('Enter')
            
            # 等待短信验证码或登录成功
            self.logger.warning("请在浏览器中完成短信验证码输入（如果需要）...")
            self.logger.info(f"等待登录完成，最多等待 {Config.TIMEOUT_CONFIG['login_wait']} 秒...")
            
            # 等待页面跳转或URL变化
            start_time = time.time()
            while time.time() - start_time < Config.TIMEOUT_CONFIG['login_wait']:
                current_url = self.page.url
                if "login" not in current_url.lower() or self.video_url in current_url:
                    self.logger.success("登录成功！")
                    break
                await asyncio.sleep(2)
            else:
                raise Exception("登录超时，请检查登录状态")
            
            await DelayHelper.random_delay()
            
        except Exception as e:
            self.logger.error(f"登录失败: {str(e)}")
            raise
    
    async def control_video_playback(self):
        """控制视频播放"""
        try:
            self.logger.info("正在查找视频元素...")
            
            # 等待视频元素加载
            await self.page.wait_for_selector('video', timeout=Config.TIMEOUT_CONFIG['video_load'] * 1000)
            
            # 获取视频状态
            video_status = await self.page.evaluate(VideoSpeedController.get_video_status_script())
            
            if not video_status['success']:
                raise Exception(video_status.get('message', '未找到视频元素'))
            
            videos = video_status['videos']
            self.logger.info(f"找到 {len(videos)} 个视频元素")
            
            for i, video in enumerate(videos):
                duration = video.get('duration', 0)
                if duration > 0:
                    self.logger.info(f"视频 {i+1}: 时长 {format_time(duration)}")
                else:
                    self.logger.warning(f"视频 {i+1}: 时长未知")
            
            # 设置视频播放速度
            playback_rate = Config.VIDEO_CONFIG['playback_rate']
            self.logger.info(f"设置视频播放速度为 {playback_rate} 倍速...")
            
            speed_result = await self.page.evaluate(
                VideoSpeedController.get_speed_control_script(playback_rate)
            )
            
            if speed_result['success']:
                self.logger.success(f"视频播放速度设置成功: {playback_rate}倍速")
                
                # 如果配置允许，直接跳到视频末尾
                if Config.VIDEO_CONFIG['skip_to_end']:
                    self.logger.info("跳转到视频末尾...")
                    skip_result = await self.page.evaluate(VideoSpeedController.get_skip_to_end_script())
                    if skip_result['success']:
                        self.logger.success("已跳转到视频末尾")
            else:
                self.logger.error("设置视频播放速度失败")
            
            # 监控视频播放进度
            await self._monitor_video_progress()
            
        except Exception as e:
            self.logger.error(f"视频播放控制失败: {str(e)}")
            raise
    
    async def _monitor_video_progress(self):
        """监控视频播放进度"""
        self.logger.info("开始监控视频播放进度...")
        
        last_progress = 0
        stall_count = 0
        
        while True:
            try:
                # 获取视频状态
                status = await self.page.evaluate(VideoSpeedController.get_video_status_script())
                
                if not status['success']:
                    break
                
                videos = status['videos']
                all_ended = True
                
                for i, video in enumerate(videos):
                    progress = float(video.get('progress', 0))
                    ended = video.get('ended', False)
                    paused = video.get('paused', True)
                    
                    if not ended:
                        all_ended = False
                        
                        # 如果视频暂停，尝试播放
                        if paused:
                            await self.page.evaluate('document.querySelectorAll("video").forEach(v => v.play())')
                        
                        # 显示进度
                        if progress != last_progress:
                            self.logger.info(f"视频 {i+1} 播放进度: {progress}%")
                            last_progress = progress
                            stall_count = 0
                        else:
                            stall_count += 1
                            
                            # 如果进度停滞太久，尝试重新设置播放速度
                            if stall_count > 10:
                                self.logger.warning("检测到播放停滞，重新设置播放速度...")
                                await self.page.evaluate(
                                    VideoSpeedController.get_speed_control_script(
                                        Config.VIDEO_CONFIG['playback_rate']
                                    )
                                )
                                stall_count = 0
                
                # 如果所有视频都播放完成
                if all_ended:
                    self.logger.success("所有视频播放完成！")
                    break
                
                await asyncio.sleep(2)
                
            except Exception as e:
                self.logger.error(f"监控视频进度时出错: {str(e)}")
                await asyncio.sleep(5)
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            self.logger.info("资源清理完成")
        except Exception as e:
            self.logger.error(f"清理资源时出错: {str(e)}")
    
    async def run(self, video_url: str):
        """运行自动化流程"""
        try:
            await self.initialize_browser()
            await self.navigate_to_video(video_url)
            await self.control_video_playback()
            
            # 等待用户确认或自动关闭
            self.logger.info("视频播放完成，等待页面自动关闭或用户确认...")
            await asyncio.sleep(10)
            
        except Exception as e:
            self.logger.error(f"自动化流程执行失败: {str(e)}")
            raise
        finally:
            await self.cleanup()

# 视频学习网站自动化工具

专为中国体育彩票远程培训平台设计的自动化工具，支持两种快速完成视频观看的方式。

## 🚀 两种快速完成方式

### 方式1: 高倍速播放 ⚡
- 设置32倍速播放
- 实时监控播放进度
- 适合所有视频类型

### 方式2: 时长参数技巧 🎯
- 获取视频真实时长
- 修改URL中的`currlocation`参数
- 直接标记为已观看完成
- **推荐方式**（最快最稳定）

## 功能特性

- 🧠 **智能处理模式**（自动选择最佳方式）
- 🔐 自动登录（支持短信验证码）
- 📺 支持直接视频URL（无需登录）
- 🎯 **URL参数智能修改**
- 💬 自动处理完成对话框

## 快速开始

### 1. 安装Chrome浏览器

**重要**: 该网站只支持Google Chrome浏览器，请先安装：
- 下载地址: https://www.google.com/chrome/

### 2. 安装依赖

```bash
pip install -r requirements.txt
playwright install chromium
```

### 3. 配置登录信息

创建 `.env` 文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的登录信息：

```
VIDEO_USERNAME=your_username
VIDEO_PASSWORD=your_password
```

### 4. 运行程序

```bash
# 命令行方式
python main.py https://learning.lottery.gov.cn/video/12345

# 交互式输入
python main.py
```

或者使用启动脚本：

```bash
./run.sh
```

## 使用流程

1. 运行程序，输入视频URL
2. 程序自动填写用户名密码
3. 手动输入短信验证码（如需要）
4. 程序自动设置高倍速播放
5. 监控播放进度直到完成
6. 自动处理完成对话框

## 配置选项

在 `config.py` 中可以调整：

```python
VIDEO_CONFIG = {
    'playback_rate': 16,     # 播放速度（倍数）
    'skip_to_end': False,    # 是否直接跳到末尾
}
```

## 注意事项

- 仅支持 Mac/Linux 系统
- **必须安装Google Chrome浏览器**（网站限制）
- 短信验证码需要手动输入
- 请合理使用，遵守网站条款
- 建议适当间隔使用

## 故障排除

1. **登录失败** - 检查用户名密码是否正确
2. **视频无法播放** - 检查网络连接和URL有效性
3. **程序卡住** - 检查是否有未处理的弹窗

---

**免责声明**: 仅供学习研究使用，使用者自行承担风险。

# 视频学习网站自动化工具

专为中国体育彩票远程培训平台设计的自动化工具，支持自动登录和高速视频播放。

## 功能特性

- 🔐 自动登录（支持短信验证码）
- ⚡ 高倍速视频播放（16倍速）
- 🎯 播放进度监控
- 💬 自动处理完成对话框

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
playwright install chromium
```

### 2. 配置登录信息

创建 `.env` 文件：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的登录信息：

```
VIDEO_USERNAME=your_username
VIDEO_PASSWORD=your_password
```

### 3. 运行程序

```bash
# 命令行方式
python main.py https://learning.lottery.gov.cn/video/12345

# 交互式输入
python main.py
```

或者使用启动脚本：

```bash
./run.sh
```

## 使用流程

1. 运行程序，输入视频URL
2. 程序自动填写用户名密码
3. 手动输入短信验证码（如需要）
4. 程序自动设置高倍速播放
5. 监控播放进度直到完成
6. 自动处理完成对话框

## 配置选项

在 `config.py` 中可以调整：

```python
VIDEO_CONFIG = {
    'playback_rate': 16,     # 播放速度（倍数）
    'skip_to_end': False,    # 是否直接跳到末尾
}
```

## 注意事项

- 仅支持 Mac/Linux 系统
- 短信验证码需要手动输入
- 请合理使用，遵守网站条款
- 建议适当间隔使用

## 故障排除

1. **登录失败** - 检查用户名密码是否正确
2. **视频无法播放** - 检查网络连接和URL有效性
3. **程序卡住** - 检查是否有未处理的弹窗

---

**免责声明**: 仅供学习研究使用，使用者自行承担风险。

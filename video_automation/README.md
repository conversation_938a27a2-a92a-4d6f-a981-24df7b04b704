# 视频学习网站自动化工具

一个专为视频学习网站设计的自动化工具，支持自动登录、智能视频播放控制和完成确认处理。

## ✨ 功能特性

- 🔐 **智能登录处理** - 自动填写用户名密码，支持短信验证码等待
- ⚡ **高速视频播放** - 支持最高32倍速播放，快速完成学习任务
- 🎯 **智能进度监控** - 实时监控播放进度，自动处理播放异常
- 💬 **自动对话框处理** - 自动点击完成确认对话框
- 🤖 **人性化操作模拟** - 模拟真实用户操作，避免检测
- 📊 **详细日志记录** - 完整的操作日志，便于问题排查

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

### 2. 配置用户信息

创建 `.env` 文件：

```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件，填入你的用户名和密码
VIDEO_USERNAME=your_username_here
VIDEO_PASSWORD=your_password_here
```

### 3. 运行程序

```bash
# 方式1: 命令行参数
python main.py https://learning.lottery.gov.cn/video/12345

# 方式2: 交互式输入
python main.py

# 方式3: 环境变量
export VIDEO_URL="https://learning.lottery.gov.cn/video/12345"
python main.py
```

## ⚙️ 配置说明

### 视频播放配置

在 `config.py` 中可以调整以下参数：

```python
VIDEO_CONFIG = {
    'playback_rate': 16,        # 播放速度倍率（推荐16倍）
    'max_playback_rate': 32,    # 最大播放速度
    'skip_to_end': False,       # 是否直接跳到视频末尾（谨慎使用）
    'wait_for_complete': True,  # 是否等待视频完全播放完成
}
```

### 超时配置

```python
TIMEOUT_CONFIG = {
    'page_load': 30,      # 页面加载超时（秒）
    'login_wait': 300,    # 登录等待超时（包括短信验证码）
    'video_load': 60,     # 视频加载超时（秒）
    'sms_code_wait': 180, # 短信验证码等待时间（秒）
}
```

## 🎮 使用流程

1. **启动程序** - 运行main.py，输入视频URL
2. **自动登录** - 程序自动填写用户名密码
3. **验证码处理** - 如需短信验证码，在浏览器中手动输入
4. **视频播放** - 程序自动设置高倍速播放
5. **进度监控** - 实时显示播放进度
6. **完成处理** - 自动点击完成确认对话框

## 🔧 高级功能

### 快速完成视频的技术方案

1. **高倍速播放**（推荐）
   ```javascript
   // 设置16倍速播放
   document.querySelector('video').playbackRate = 16;
   ```

2. **智能跳转**（谨慎使用）
   ```javascript
   // 跳转到视频末尾
   video.currentTime = video.duration - 1;
   ```

3. **播放状态监控**
   - 自动检测播放停滞
   - 自动重新设置播放速度
   - 智能处理播放异常

### 反检测机制

- 随机操作延迟
- 模拟人类打字速度
- 自然的鼠标移动轨迹
- 避免明显的自动化特征

## 📝 日志说明

程序会生成详细的日志文件 `video_automation.log`，包含：

- 操作步骤记录
- 错误信息详情
- 视频播放进度
- 性能统计信息

## ⚠️ 注意事项

1. **合规使用** - 请遵守网站使用条款，合理使用本工具
2. **验证码处理** - 短信验证码需要手动输入，程序会等待
3. **网络环境** - 确保网络连接稳定，避免播放中断
4. **浏览器兼容** - 推荐使用Chrome浏览器内核
5. **反检测** - 避免频繁使用，建议适当间隔

## 🐛 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名密码是否正确
   - 确认网站是否需要验证码
   - 查看日志文件获取详细错误信息

2. **视频无法播放**
   - 检查网络连接
   - 确认视频URL是否有效
   - 尝试降低播放速度

3. **程序卡住**
   - 检查页面是否有弹窗
   - 查看浏览器控制台错误
   - 重启程序重试

### 调试模式

设置环境变量启用调试：

```bash
export DEBUG=1
python main.py
```

## 📄 许可证

本项目仅供学习和研究使用，请勿用于商业用途。使用本工具时请遵守相关网站的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**免责声明**: 本工具仅供学习研究使用，使用者需自行承担使用风险，开发者不承担任何责任。

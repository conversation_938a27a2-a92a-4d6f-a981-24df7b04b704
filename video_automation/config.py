#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频学习网站自动化配置文件
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""
    
    # 网站URL配置
    LOGIN_URL = "https://learning.lottery.gov.cn/static/htmls/login.html"
    
    # 用户凭据配置（建议使用环境变量）
    USERNAME = os.getenv('VIDEO_USERNAME', '')
    PASSWORD = os.getenv('VIDEO_PASSWORD', '')
    
    # 浏览器配置
    BROWSER_CONFIG = {
        'headless': False,  # 设置为True可以无头模式运行
        'viewport': {'width': 1920, 'height': 1080},
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    # 视频播放配置
    VIDEO_CONFIG = {
        'playback_rate': 16,  # 播放速度倍率（16倍速）
        'max_playback_rate': 32,  # 最大播放速度
        'skip_to_end': False,  # 是否直接跳到视频末尾（谨慎使用）
        'wait_for_complete': True,  # 是否等待视频完全播放完成
    }
    
    # 超时配置（秒）
    TIMEOUT_CONFIG = {
        'page_load': 30,  # 页面加载超时
        'element_wait': 10,  # 元素等待超时
        'login_wait': 300,  # 登录等待超时（包括短信验证码输入时间）
        'video_load': 60,  # 视频加载超时
        'sms_code_wait': 180,  # 短信验证码等待时间
    }
    
    # 重试配置
    RETRY_CONFIG = {
        'max_retries': 3,  # 最大重试次数
        'retry_delay': 2,  # 重试间隔（秒）
    }
    
    # 日志配置
    LOG_CONFIG = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'file': 'video_automation.log'
    }
    
    # 随机延迟配置（模拟人类操作）
    DELAY_CONFIG = {
        'min_delay': 1,  # 最小延迟（秒）
        'max_delay': 3,  # 最大延迟（秒）
        'typing_delay': 0.1,  # 打字延迟（秒）
    }

    @classmethod
    def validate_config(cls):
        """验证配置"""
        errors = []
        
        if not cls.USERNAME:
            errors.append("用户名未配置，请设置环境变量 VIDEO_USERNAME 或在config.py中直接设置")
        
        if not cls.PASSWORD:
            errors.append("密码未配置，请设置环境变量 VIDEO_PASSWORD 或在config.py中直接设置")
        
        if cls.VIDEO_CONFIG['playback_rate'] > cls.VIDEO_CONFIG['max_playback_rate']:
            errors.append(f"播放速度 {cls.VIDEO_CONFIG['playback_rate']} 超过最大值 {cls.VIDEO_CONFIG['max_playback_rate']}")
        
        return errors

    @classmethod
    def print_config(cls):
        """打印当前配置（隐藏敏感信息）"""
        print("=== 当前配置 ===")
        print(f"登录URL: {cls.LOGIN_URL}")
        print(f"用户名: {'*' * len(cls.USERNAME) if cls.USERNAME else '未设置'}")
        print(f"密码: {'*' * len(cls.PASSWORD) if cls.PASSWORD else '未设置'}")
        print(f"播放速度: {cls.VIDEO_CONFIG['playback_rate']}倍速")
        print(f"无头模式: {cls.BROWSER_CONFIG['headless']}")
        print("=" * 20)

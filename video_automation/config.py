#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频学习网站自动化配置文件
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置类"""

    # 网站URL配置
    LOGIN_URL = "https://learning.lottery.gov.cn/static/htmls/login.html"

    # 用户凭据配置
    USERNAME = os.getenv('VIDEO_USERNAME', '')
    PASSWORD = os.getenv('VIDEO_PASSWORD', '')

    # 浏览器配置
    BROWSER_CONFIG = {
        'headless': False,
        'viewport': {'width': 1920, 'height': 1080},
    }

    # 视频播放配置
    VIDEO_CONFIG = {
        'playback_rate': 32,  # 播放速度倍率
        'skip_to_end': False,  # 是否直接跳到视频末尾
    }

    # 超时配置（秒）
    TIMEOUT_CONFIG = {
        'page_load': 30,
        'element_wait': 10,
        'login_wait': 180,  # 登录等待超时
        'video_load': 30,
    }

    @classmethod
    def validate_config(cls):
        """验证配置"""
        errors = []
        if not cls.USERNAME:
            errors.append("用户名未配置")
        if not cls.PASSWORD:
            errors.append("密码未配置")
        return errors

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频自动化工具安装脚本
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🔄 {description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description}成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败")
        print(f"错误: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True


def install_dependencies():
    """安装Python依赖"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        return False
    
    # 安装依赖
    if not run_command(f"{sys.executable} -m pip install -r {requirements_file}", "安装Python依赖"):
        return False
    
    return True


def install_playwright():
    """安装Playwright浏览器"""
    # 安装playwright
    if not run_command(f"{sys.executable} -m pip install playwright", "安装Playwright"):
        return False
    
    # 安装浏览器
    if not run_command("playwright install chromium", "安装Chromium浏览器"):
        return False
    
    return True


def create_config_files():
    """创建配置文件"""
    env_file = Path(__file__).parent / ".env"
    env_example = Path(__file__).parent / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("\n📝 创建配置文件...")
        try:
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 已创建 .env 配置文件")
            print("⚠️  请编辑 .env 文件，填入你的用户名和密码")
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False
    
    return True


def test_installation():
    """测试安装"""
    print("\n🧪 测试安装...")
    
    try:
        # 测试导入
        import playwright
        from playwright.async_api import async_playwright
        print("✅ Playwright导入成功")
        
        import asyncio
        print("✅ asyncio模块正常")
        
        # 测试配置文件
        sys.path.append(str(Path(__file__).parent))
        from config import Config
        print("✅ 配置文件加载成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入测试失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def print_next_steps():
    """打印后续步骤"""
    print("\n" + "="*60)
    print("🎉 安装完成！")
    print("="*60)
    print("\n📋 后续步骤:")
    print("1. 编辑 .env 文件，填入你的用户名和密码:")
    print("   VIDEO_USERNAME=your_username")
    print("   VIDEO_PASSWORD=your_password")
    print("\n2. 运行程序:")
    print("   python main.py https://your-video-url")
    print("\n3. 或者交互式运行:")
    print("   python main.py")
    print("\n📖 更多信息请查看 README.md")
    print("="*60)


def main():
    """主函数"""
    print("🚀 视频自动化工具安装程序")
    print("="*40)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败，请检查网络连接或手动安装")
        return 1
    
    # 安装Playwright
    if not install_playwright():
        print("\n❌ Playwright安装失败，请检查网络连接")
        return 1
    
    # 创建配置文件
    if not create_config_files():
        print("\n⚠️  配置文件创建失败，请手动创建")
    
    # 测试安装
    if not test_installation():
        print("\n❌ 安装测试失败，可能存在问题")
        return 1
    
    # 打印后续步骤
    print_next_steps()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程出错: {e}")
        sys.exit(1)

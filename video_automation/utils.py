#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频自动化工具函数
"""

import asyncio
import random
import logging
import time
from typing import Optional, Any
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

class Logger:
    """日志工具类"""
    
    def __init__(self, name: str = "VideoAutomation", level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建文件处理器
        file_handler = logging.FileHandler('video_automation.log', encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 创建格式器
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        # 添加处理器
        if not self.logger.handlers:
            self.logger.addHandler(console_handler)
            self.logger.addHandler(file_handler)
    
    def info(self, message: str):
        """信息日志"""
        print(f"{Fore.GREEN}[INFO]{Style.RESET_ALL} {message}")
        self.logger.info(message)
    
    def warning(self, message: str):
        """警告日志"""
        print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {message}")
        self.logger.warning(message)
    
    def error(self, message: str):
        """错误日志"""
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {message}")
        self.logger.error(message)
    
    def success(self, message: str):
        """成功日志"""
        print(f"{Fore.CYAN}[SUCCESS]{Style.RESET_ALL} {message}")
        self.logger.info(f"SUCCESS: {message}")

class DelayHelper:
    """延迟工具类"""
    
    @staticmethod
    async def random_delay(min_delay: float = 1.0, max_delay: float = 3.0):
        """随机延迟"""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    @staticmethod
    async def typing_delay():
        """打字延迟"""
        await asyncio.sleep(random.uniform(0.05, 0.15))
    
    @staticmethod
    async def human_like_delay():
        """模拟人类操作延迟"""
        await asyncio.sleep(random.uniform(0.5, 2.0))

class RetryHelper:
    """重试工具类"""
    
    @staticmethod
    async def retry_async(func, max_retries: int = 3, delay: float = 2.0, *args, **kwargs):
        """异步重试装饰器"""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < max_retries - 1:
                    await asyncio.sleep(delay)
                    continue
                else:
                    raise last_exception

class VideoSpeedController:
    """视频速度控制器"""
    
    @staticmethod
    def get_speed_control_script(playback_rate: float = 16) -> str:
        """获取视频速度控制脚本"""
        return f"""
        (function() {{
            const videos = document.querySelectorAll('video');
            if (videos.length === 0) {{
                return {{ success: false, message: '未找到视频元素' }};
            }}
            
            let results = [];
            videos.forEach((video, index) => {{
                try {{
                    // 设置播放速度
                    video.playbackRate = {playback_rate};
                    
                    // 确保视频开始播放
                    if (video.paused) {{
                        video.play();
                    }}
                    
                    results.push({{
                        index: index,
                        duration: video.duration,
                        currentTime: video.currentTime,
                        playbackRate: video.playbackRate,
                        paused: video.paused
                    }});
                }} catch (error) {{
                    results.push({{
                        index: index,
                        error: error.message
                    }});
                }}
            }});
            
            return {{ success: true, videos: results }};
        }})();
        """
    
    @staticmethod
    def get_video_status_script() -> str:
        """获取视频状态检查脚本"""
        return """
        (function() {
            const videos = document.querySelectorAll('video');
            if (videos.length === 0) {
                return { success: false, message: '未找到视频元素' };
            }
            
            let results = [];
            videos.forEach((video, index) => {
                results.push({
                    index: index,
                    duration: video.duration,
                    currentTime: video.currentTime,
                    playbackRate: video.playbackRate,
                    paused: video.paused,
                    ended: video.ended,
                    readyState: video.readyState,
                    progress: video.duration > 0 ? (video.currentTime / video.duration * 100).toFixed(2) : 0
                });
            });
            
            return { success: true, videos: results };
        })();
        """
    
    @staticmethod
    def get_skip_to_end_script() -> str:
        """获取跳转到视频末尾的脚本（谨慎使用）"""
        return """
        (function() {
            const videos = document.querySelectorAll('video');
            if (videos.length === 0) {
                return { success: false, message: '未找到视频元素' };
            }
            
            let results = [];
            videos.forEach((video, index) => {
                try {
                    if (video.duration && video.duration > 0) {
                        // 跳转到视频末尾前1秒
                        video.currentTime = Math.max(0, video.duration - 1);
                        results.push({
                            index: index,
                            success: true,
                            currentTime: video.currentTime,
                            duration: video.duration
                        });
                    } else {
                        results.push({
                            index: index,
                            success: false,
                            message: '视频时长未知'
                        });
                    }
                } catch (error) {
                    results.push({
                        index: index,
                        success: false,
                        error: error.message
                    });
                }
            });
            
            return { success: true, videos: results };
        })();
        """

def format_time(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}分{secs:.1f}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}小时{minutes}分{secs:.1f}秒"

def print_banner():
    """打印程序横幅"""
    banner = f"""
{Fore.CYAN}
╔══════════════════════════════════════════════════════════════╗
║                    视频学习网站自动化工具                      ║
║                                                              ║
║  功能特性:                                                    ║
║  • 自动登录处理 (支持短信验证码)                               ║
║  • 智能视频加速播放 (最高32倍速)                               ║
║  • 自动处理完成对话框                                         ║
║  • 人性化操作模拟                                             ║
║                                                              ║
║  注意: 请合理使用，遵守网站使用条款                            ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
    """
    print(banner)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频自动化工具函数
"""

import asyncio
import random
import time

class Logger:
    """简单日志类"""

    def info(self, message: str):
        print(f"[INFO] {message}")

    def warning(self, message: str):
        print(f"[WARNING] {message}")

    def error(self, message: str):
        print(f"[ERROR] {message}")

    def success(self, message: str):
        print(f"[SUCCESS] {message}")

class DelayHelper:
    """延迟工具类"""
    
    @staticmethod
    async def random_delay(min_delay: float = 1.0, max_delay: float = 3.0):
        """随机延迟"""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    @staticmethod
    async def typing_delay():
        """打字延迟"""
        await asyncio.sleep(random.uniform(0.05, 0.15))
    
    @staticmethod
    async def human_like_delay():
        """模拟人类操作延迟"""
        await asyncio.sleep(random.uniform(0.5, 2.0))

class RetryHelper:
    """重试工具类"""
    
    @staticmethod
    async def retry_async(func, max_retries: int = 3, delay: float = 2.0, *args, **kwargs):
        """异步重试装饰器"""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < max_retries - 1:
                    await asyncio.sleep(delay)
                    continue
                else:
                    raise last_exception

class VideoSpeedController:
    """视频速度控制器"""

    @staticmethod
    def get_speed_control_script(playback_rate: float = 16) -> str:
        """获取视频速度控制脚本"""
        return f"""
        (function() {{
            const videos = document.querySelectorAll('video');
            if (videos.length === 0) {{
                return {{ success: false, message: '未找到视频元素' }};
            }}

            let results = [];
            videos.forEach((video, index) => {{
                try {{
                    // 移除播放速度限制
                    Object.defineProperty(video, 'playbackRate', {{
                        value: {playback_rate},
                        writable: true,
                        configurable: true
                    }});

                    // 设置播放速度
                    video.playbackRate = {playback_rate};

                    // 移除可能的播放限制
                    video.removeAttribute('controlslist');
                    video.controls = false;

                    // 确保视频开始播放
                    if (video.paused) {{
                        video.play();
                    }}

                    // 禁用右键菜单
                    video.oncontextmenu = () => false;

                    results.push({{
                        index: index,
                        duration: video.duration,
                        currentTime: video.currentTime,
                        playbackRate: video.playbackRate,
                        paused: video.paused
                    }});
                }} catch (error) {{
                    results.push({{
                        index: index,
                        error: error.message
                    }});
                }}
            }});

            return {{ success: true, videos: results }};
        }})();
        """
    
    @staticmethod
    def get_video_status_script() -> str:
        """获取视频状态检查脚本"""
        return """
        (function() {
            const videos = document.querySelectorAll('video');
            if (videos.length === 0) {
                return { success: false, message: '未找到视频元素' };
            }
            
            let results = [];
            videos.forEach((video, index) => {
                results.push({
                    index: index,
                    duration: video.duration,
                    currentTime: video.currentTime,
                    playbackRate: video.playbackRate,
                    paused: video.paused,
                    ended: video.ended,
                    readyState: video.readyState,
                    progress: video.duration > 0 ? (video.currentTime / video.duration * 100).toFixed(2) : 0
                });
            });
            
            return { success: true, videos: results };
        })();
        """
    
    @staticmethod
    def get_skip_to_end_script() -> str:
        """获取跳转到视频末尾的脚本（谨慎使用）"""
        return """
        (function() {
            const videos = document.querySelectorAll('video');
            if (videos.length === 0) {
                return { success: false, message: '未找到视频元素' };
            }

            let results = [];
            videos.forEach((video, index) => {
                try {
                    if (video.duration && video.duration > 0) {
                        // 跳转到视频末尾前1秒
                        video.currentTime = Math.max(0, video.duration - 1);

                        // 触发时间更新事件
                        video.dispatchEvent(new Event('timeupdate'));
                        video.dispatchEvent(new Event('progress'));

                        results.push({
                            index: index,
                            success: true,
                            currentTime: video.currentTime,
                            duration: video.duration
                        });
                    } else {
                        results.push({
                            index: index,
                            success: false,
                            message: '视频时长未知'
                        });
                    }
                } catch (error) {
                    results.push({
                        index: index,
                        success: false,
                        error: error.message
                    });
                }
            });

            return { success: true, videos: results };
        })();
        """

    @staticmethod
    def get_advanced_speed_script() -> str:
        """获取高级视频加速脚本"""
        return """
        (function() {
            const videos = document.querySelectorAll('video');
            if (videos.length === 0) {
                return { success: false, message: '未找到视频元素' };
            }

            let results = [];
            videos.forEach((video, index) => {
                try {
                    // 方法1: 直接修改播放速度
                    video.playbackRate = 32;

                    // 方法2: 修改视频时间流逝速度
                    const originalCurrentTime = Object.getOwnPropertyDescriptor(HTMLMediaElement.prototype, 'currentTime');
                    let acceleratedTime = video.currentTime;

                    Object.defineProperty(video, 'currentTime', {
                        get: function() {
                            return acceleratedTime;
                        },
                        set: function(value) {
                            acceleratedTime = value;
                            originalCurrentTime.set.call(this, value);
                        }
                    });

                    // 方法3: 拦截时间更新事件
                    const timeUpdateHandler = () => {
                        if (!video.paused && video.duration > 0) {
                            acceleratedTime = Math.min(video.duration, acceleratedTime + 0.5);
                            video.dispatchEvent(new Event('timeupdate'));
                        }
                    };

                    setInterval(timeUpdateHandler, 100);

                    // 方法4: 模拟播放完成
                    if (video.duration > 0) {
                        setTimeout(() => {
                            video.currentTime = video.duration - 0.1;
                            video.dispatchEvent(new Event('ended'));
                        }, 5000);
                    }

                    results.push({
                        index: index,
                        success: true,
                        playbackRate: video.playbackRate,
                        duration: video.duration
                    });

                } catch (error) {
                    results.push({
                        index: index,
                        success: false,
                        error: error.message
                    });
                }
            });

            return { success: true, videos: results };
        })();
        """

def format_time(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}分{secs:.1f}秒"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}小时{minutes}分{secs:.1f}秒"

def print_banner():
    """打印程序横幅"""
    banner = f"""
{Fore.CYAN}
╔══════════════════════════════════════════════════════════════╗
║                    视频学习网站自动化工具                      ║
║                                                              ║
║  功能特性:                                                    ║
║  • 自动登录处理 (支持短信验证码)                               ║
║  • 智能视频加速播放 (最高32倍速)                               ║
║  • 自动处理完成对话框                                         ║
║  • 人性化操作模拟                                             ║
║                                                              ║
║  注意: 请合理使用，遵守网站使用条款                            ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
    """
    print(banner)

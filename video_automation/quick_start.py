#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频自动化工具快速启动脚本
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from utils import print_banner, Logger


def quick_setup():
    """快速设置"""
    logger = Logger()
    
    print_banner()
    print("🚀 快速启动向导")
    print("="*50)
    
    # 检查配置文件
    env_file = Path(".env")
    if not env_file.exists():
        logger.warning("未找到配置文件，开始快速配置...")
        
        print("\n📝 请输入登录信息:")
        username = input("用户名: ").strip()
        password = input("密码: ").strip()
        
        if not username or not password:
            logger.error("用户名和密码不能为空")
            return False
        
        # 创建配置文件
        config_content = f"""# 视频自动化工具配置文件
VIDEO_USERNAME={username}
VIDEO_PASSWORD={password}
"""
        
        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            logger.success("配置文件创建成功")
        except Exception as e:
            logger.error(f"创建配置文件失败: {e}")
            return False
    
    # 获取视频URL
    print("\n🎥 请输入视频信息:")
    video_url = input("视频URL: ").strip()
    
    if not video_url:
        logger.error("视频URL不能为空")
        return False
    
    if not video_url.startswith(('http://', 'https://')):
        logger.error("请输入有效的URL")
        return False
    
    return video_url


async def quick_run(video_url):
    """快速运行"""
    try:
        from video_automation import VideoAutomation
        
        automation = VideoAutomation()
        await automation.run(video_url)
        
    except ImportError as e:
        logger = Logger()
        logger.error("依赖未安装，请先运行: python install.py")
        return False
    except Exception as e:
        logger = Logger()
        logger.error(f"运行失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    try:
        # 快速设置
        video_url = quick_setup()
        if not video_url:
            return 1
        
        # 运行自动化
        logger = Logger()
        logger.info("开始执行自动化流程...")
        
        success = asyncio.run(quick_run(video_url))
        
        if success:
            logger.success("自动化流程完成！")
            return 0
        else:
            return 1
            
    except KeyboardInterrupt:
        print("\n用户中断程序")
        return 1
    except Exception as e:
        print(f"程序执行出错: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

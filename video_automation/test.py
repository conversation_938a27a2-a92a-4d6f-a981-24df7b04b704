#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import asyncio
from playwright.async_api import async_playwright

async def test_login_page():
    """测试登录页面"""
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        print("访问登录页面...")
        await page.goto("https://learning.lottery.gov.cn/static/htmls/login.html")
        
        print("等待页面加载...")
        await page.wait_for_load_state()
        
        # 查找页面元素
        username_input = await page.query_selector('input[placeholder*="身份证号或手机号"]')
        password_input = await page.query_selector('input[placeholder*="密码"]')
        sms_input = await page.query_selector('input[placeholder*="手机验证码"]')
        
        print(f"用户名输入框: {'找到' if username_input else '未找到'}")
        print(f"密码输入框: {'找到' if password_input else '未找到'}")
        print(f"验证码输入框: {'找到' if sms_input else '未找到'}")
        
        # 等待用户查看
        input("按回车键关闭浏览器...")
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(test_login_page())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Chrome浏览器安装状态
"""

import os
import subprocess
import sys

def check_chrome_mac():
    """检查Mac上的Chrome安装"""
    chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    return os.path.exists(chrome_path)

def check_chrome_linux():
    """检查Linux上的Chrome安装"""
    try:
        result = subprocess.run(['which', 'google-chrome'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def main():
    """主函数"""
    print("🔍 检查Chrome浏览器安装状态...")
    
    if sys.platform == "darwin":  # Mac
        if check_chrome_mac():
            print("✅ 找到Chrome浏览器 (Mac)")
            print("   路径: /Applications/Google Chrome.app")
        else:
            print("❌ 未找到Chrome浏览器 (Mac)")
            print("   请从以下地址下载安装:")
            print("   https://www.google.com/chrome/")
            return False
            
    elif sys.platform.startswith("linux"):  # Linux
        if check_chrome_linux():
            print("✅ 找到Chrome浏览器 (Linux)")
        else:
            print("❌ 未找到Chrome浏览器 (Linux)")
            print("   请安装Google Chrome:")
            print("   sudo apt-get install google-chrome-stable")
            return False
    else:
        print("❌ 不支持的操作系统")
        return False
    
    print("✅ Chrome检查通过，可以运行程序")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量视频处理脚本 - 支持最多5个视频同时处理
"""

import asyncio
import sys
import os
from concurrent.futures import ThreadPoolExecutor
from typing import List
import time

class BatchLogger:
    def __init__(self, prefix=""):
        self.prefix = prefix
    
    def info(self, msg): 
        print(f"[{self.prefix}INFO] {msg}")
    
    def success(self, msg): 
        print(f"[{self.prefix}SUCCESS] {msg}")
    
    def error(self, msg): 
        print(f"[{self.prefix}ERROR] {msg}")
    
    def warning(self, msg): 
        print(f"[{self.prefix}WARNING] {msg}")

class BatchVideoProcessor:
    """批量视频处理器"""
    
    def __init__(self, max_concurrent=5):
        self.max_concurrent = max_concurrent
        self.logger = BatchLogger()
        self.results = []
    
    def load_urls_from_file(self, filename: str) -> List[str]:
        """从文件加载URL列表"""
        urls = []
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and line.startswith('http'):
                        urls.append(line)
            self.logger.success(f"从文件加载了 {len(urls)} 个URL")
            return urls
        except FileNotFoundError:
            self.logger.error(f"文件 {filename} 不存在")
            return []
        except Exception as e:
            self.logger.error(f"读取文件失败: {e}")
            return []
    
    def get_urls_from_input(self) -> List[str]:
        """从用户输入获取URL列表"""
        urls = []
        self.logger.info("请输入视频URL（每行一个，输入空行结束）:")
        
        while len(urls) < 20:  # 最多20个URL
            try:
                url = input(f"URL {len(urls)+1}: ").strip()
                if not url:
                    break
                if url.startswith('http'):
                    urls.append(url)
                    self.logger.info(f"已添加URL {len(urls)}")
                else:
                    self.logger.warning("请输入有效的URL")
            except KeyboardInterrupt:
                break
        
        return urls
    
    async def process_single_video(self, url: str, index: int, method: str = 'auto') -> dict:
        """处理单个视频"""
        logger = BatchLogger(f"视频{index+1}|")
        
        try:
            # 导入simple_video模块
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from simple_video import SimpleVideoProcessor
            
            processor = SimpleVideoProcessor()
            processor.logger = logger
            
            start_time = time.time()
            logger.info(f"开始处理...")
            
            success = await processor.process_video(url, method)
            
            end_time = time.time()
            duration = end_time - start_time
            
            result = {
                'index': index,
                'url': url[:50] + '...' if len(url) > 50 else url,
                'success': success,
                'duration': duration,
                'method': method
            }
            
            if success:
                logger.success(f"处理完成，耗时 {duration:.1f}秒")
            else:
                logger.error(f"处理失败，耗时 {duration:.1f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"处理异常: {e}")
            return {
                'index': index,
                'url': url[:50] + '...' if len(url) > 50 else url,
                'success': False,
                'duration': 0,
                'error': str(e),
                'method': method
            }
    
    async def process_batch(self, urls: List[str], method: str = 'auto'):
        """批量处理视频"""
        if not urls:
            self.logger.error("没有要处理的URL")
            return
        
        total_urls = len(urls)
        self.logger.info(f"开始批量处理 {total_urls} 个视频")
        self.logger.info(f"最大并发数: {self.max_concurrent}")
        self.logger.info(f"处理方式: {method}")
        
        # 分批处理，每批最多5个
        batches = [urls[i:i+self.max_concurrent] for i in range(0, len(urls), self.max_concurrent)]
        
        all_results = []
        
        for batch_index, batch_urls in enumerate(batches):
            self.logger.info(f"\n🚀 处理第 {batch_index+1}/{len(batches)} 批，共 {len(batch_urls)} 个视频")
            
            # 创建并发任务
            tasks = []
            for i, url in enumerate(batch_urls):
                global_index = batch_index * self.max_concurrent + i
                task = self.process_single_video(url, global_index, method)
                tasks.append(task)
            
            # 并发执行当前批次
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for result in batch_results:
                if isinstance(result, Exception):
                    self.logger.error(f"任务异常: {result}")
                else:
                    all_results.append(result)
            
            # 批次间隔
            if batch_index < len(batches) - 1:
                self.logger.info("⏳ 等待5秒后处理下一批...")
                await asyncio.sleep(5)
        
        # 显示总结
        self.show_summary(all_results)
    
    def show_summary(self, results: List[dict]):
        """显示处理结果总结"""
        if not results:
            return
        
        print("\n" + "="*80)
        print("📊 批量处理结果总结")
        print("="*80)
        
        success_count = sum(1 for r in results if r.get('success', False))
        total_count = len(results)
        total_time = sum(r.get('duration', 0) for r in results)
        
        print(f"总计处理: {total_count} 个视频")
        print(f"成功: {success_count} 个")
        print(f"失败: {total_count - success_count} 个")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        print(f"总耗时: {total_time:.1f}秒")
        print(f"平均耗时: {total_time/total_count:.1f}秒/个")
        
        print("\n📋 详细结果:")
        for result in results:
            status = "✅" if result.get('success', False) else "❌"
            duration = result.get('duration', 0)
            url = result.get('url', 'Unknown')
            error = result.get('error', '')
            
            print(f"{status} 视频{result.get('index', 0)+1}: {duration:.1f}s - {url}")
            if error:
                print(f"   错误: {error}")
        
        print("="*80)

def main():
    """主函数"""
    print("📦 批量视频处理工具")
    print("="*50)
    
    processor = BatchVideoProcessor(max_concurrent=5)
    
    # 选择输入方式
    print("选择URL输入方式:")
    print("1. 从文件读取 (urls.txt)")
    print("2. 手动输入")
    
    try:
        choice = input("请选择 (1-2): ").strip()
        
        if choice == '1':
            # 从文件读取
            filename = input("请输入文件名 (默认: urls.txt): ").strip() or 'urls.txt'
            urls = processor.load_urls_from_file(filename)
        elif choice == '2':
            # 手动输入
            urls = processor.get_urls_from_input()
        else:
            print("❌ 无效选择")
            return
        
        if not urls:
            print("❌ 没有有效的URL")
            return
        
        # 选择处理方式
        print(f"\n找到 {len(urls)} 个URL")
        print("选择处理方式:")
        print("1. 智能处理 (推荐)")
        print("2. 时长参数技巧")
        print("3. 高倍速播放")
        
        method_choice = input("请选择 (1-3): ").strip()
        method_map = {
            '1': 'auto',
            '2': 'duration', 
            '3': 'speed'
        }
        method = method_map.get(method_choice, 'auto')
        
        # 确认开始
        print(f"\n📋 处理配置:")
        print(f"URL数量: {len(urls)}")
        print(f"处理方式: {method}")
        print(f"最大并发: {processor.max_concurrent}")
        
        confirm = input("\n确认开始处理? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消")
            return
        
        # 开始批量处理
        asyncio.run(processor.process_batch(urls, method))
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()

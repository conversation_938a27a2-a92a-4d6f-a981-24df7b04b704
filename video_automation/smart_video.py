#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能视频处理脚本 - 支持两种快速完成方式
方式1: 高倍速播放
方式2: 时长参数技巧（修改currlocation）
"""

import asyncio
import sys
import re
import urllib.parse
from playwright.async_api import async_playwright
from utils import Logger
from config import Config

class SmartVideoProcessor:
    """智能视频处理器"""
    
    def __init__(self):
        self.logger = Logger()
        self.original_url = None
        self.video_duration = None
    
    def analyze_url(self, url):
        """分析URL结构"""
        self.original_url = url
        self.logger.info("🔍 分析URL结构...")
        
        # 解析URL参数
        parsed = urllib.parse.urlparse(url)
        params = urllib.parse.parse_qs(parsed.query)
        
        # 提取关键参数
        session_id = params.get('sessionId', [''])[0]
        item_location = params.get('itemlocation', [''])[0]
        curr_location = params.get('currlocation', [''])[0]
        
        self.logger.info(f"📋 SessionID: {session_id[:20]}...")
        self.logger.info(f"📹 视频地址: {item_location.split('/')[-1] if item_location else 'None'}")
        self.logger.info(f"⏱️  当前位置: {curr_location if curr_location else '空'}")
        
        return {
            'session_id': session_id,
            'item_location': item_location,
            'curr_location': curr_location,
            'params': params
        }
    
    def create_duration_url(self, url_info, duration_seconds):
        """创建带时长的URL"""
        if not duration_seconds:
            return self.original_url
        
        # 解析原始URL
        parsed = urllib.parse.urlparse(self.original_url)
        params = urllib.parse.parse_qs(parsed.query)
        
        # 修改currlocation参数
        params['currlocation'] = [str(int(duration_seconds))]
        
        # 重新构建URL
        new_query = urllib.parse.urlencode(params, doseq=True)
        new_url = urllib.parse.urlunparse((
            parsed.scheme, parsed.netloc, parsed.path,
            parsed.params, new_query, parsed.fragment
        ))
        
        self.logger.success(f"🎯 生成时长URL: currlocation={int(duration_seconds)}")
        return new_url
    
    async def get_video_duration(self, page):
        """获取视频时长"""
        self.logger.info("⏱️  获取视频时长...")
        
        try:
            # 等待视频元素加载
            await page.wait_for_selector('video', timeout=10000)
            
            # 获取视频时长
            duration = await page.evaluate("""
                (function() {
                    const videos = document.querySelectorAll('video');
                    if (videos.length === 0) return null;
                    
                    const video = videos[0];
                    
                    // 如果视频还没加载完成，等待一下
                    if (!video.duration || video.duration === 0) {
                        return new Promise((resolve) => {
                            const checkDuration = () => {
                                if (video.duration && video.duration > 0) {
                                    resolve(video.duration);
                                } else {
                                    setTimeout(checkDuration, 500);
                                }
                            };
                            checkDuration();
                        });
                    }
                    
                    return video.duration;
                })();
            """)
            
            if duration:
                self.video_duration = duration
                minutes = int(duration // 60)
                seconds = int(duration % 60)
                self.logger.success(f"✅ 视频时长: {minutes}分{seconds}秒 ({duration}秒)")
                return duration
            else:
                self.logger.warning("⚠️  无法获取视频时长")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 获取时长失败: {e}")
            return None
    
    async def method_speed_playback(self, page):
        """方式1: 高倍速播放"""
        self.logger.info("⚡ 方式1: 高倍速播放")
        
        playback_rate = Config.VIDEO_CONFIG['playback_rate']
        
        result = await page.evaluate(f"""
            (function() {{
                const videos = document.querySelectorAll('video');
                if (videos.length === 0) return false;
                
                videos.forEach(video => {{
                    // 设置超高倍速
                    video.playbackRate = {playback_rate};
                    
                    // 移除控制限制
                    video.removeAttribute('controlslist');
                    video.controls = true;
                    
                    // 确保播放
                    if (video.paused) {{
                        video.play();
                    }}
                    
                    console.log('设置播放速度为 {playback_rate} 倍');
                }});
                
                return true;
            }})();
        """)
        
        if result:
            self.logger.success(f"✅ 已设置 {playback_rate} 倍速播放")
            return True
        else:
            self.logger.error("❌ 设置倍速失败")
            return False
    
    async def method_duration_trick(self, page):
        """方式2: 时长参数技巧"""
        self.logger.info("🎯 方式2: 时长参数技巧")
        
        if not self.video_duration:
            self.logger.error("❌ 无法使用时长技巧：未获取到视频时长")
            return False
        
        # 创建带时长的URL
        url_info = self.analyze_url(self.original_url)
        duration_url = self.create_duration_url(url_info, self.video_duration)
        
        # 跳转到新URL
        self.logger.info("🔄 跳转到时长URL...")
        await page.goto(duration_url)
        
        # 等待页面加载
        await page.wait_for_load_state('networkidle')
        
        self.logger.success("✅ 已使用时长技巧完成视频")
        return True
    
    async def process_video(self, video_url: str, method: str = 'auto'):
        """处理视频URL"""
        self.logger.info("🚀 启动智能视频处理...")
        
        # 分析URL
        url_info = self.analyze_url(video_url)
        
        async with async_playwright() as p:
            # 启动浏览器
            try:
                browser = await p.chromium.launch(
                    headless=False,
                    executable_path="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    args=['--no-sandbox', '--disable-dev-shm-usage']
                )
                self.logger.info("✅ 使用Chrome浏览器")
            except Exception:
                browser = await p.chromium.launch(
                    headless=False,
                    args=['--no-sandbox', '--disable-dev-shm-usage', '--single-process']
                )
                self.logger.info("✅ 使用Chromium浏览器")
            
            page = await browser.new_page()
            
            # 设置对话框自动处理
            page.on("dialog", lambda dialog: asyncio.create_task(dialog.accept()))
            
            # 访问原始视频页面
            self.logger.info("🌐 访问视频页面...")
            await page.goto(video_url, timeout=30000)
            await page.wait_for_load_state('networkidle')
            
            # 获取视频时长
            duration = await self.get_video_duration(page)
            
            # 根据方法选择处理方式
            if method == 'auto':
                method = Config.VIDEO_CONFIG.get('method', 'auto')
            
            success = False
            
            if method == 'duration' and duration:
                # 优先使用时长技巧
                success = await self.method_duration_trick(page)
            elif method == 'speed':
                # 使用高倍速播放
                success = await self.method_speed_playback(page)
                if success:
                    await self._monitor_progress(page)
            else:
                # 自动选择：优先时长技巧，失败则用倍速
                if duration and Config.VIDEO_CONFIG.get('use_duration_trick', True):
                    self.logger.info("🎯 自动选择：优先使用时长技巧")
                    success = await self.method_duration_trick(page)
                
                if not success:
                    self.logger.info("⚡ 回退到高倍速播放")
                    success = await self.method_speed_playback(page)
                    if success:
                        await self._monitor_progress(page)
            
            if success:
                self.logger.success("🎉 视频处理完成！")
            else:
                self.logger.error("❌ 视频处理失败")
            
            # 等待用户确认
            self.logger.info("⏳ 等待10秒后关闭...")
            await asyncio.sleep(10)
            
            await browser.close()
    
    async def _monitor_progress(self, page):
        """监控播放进度（仅用于倍速播放）"""
        self.logger.info("📊 监控播放进度...")
        
        for i in range(30):  # 最多监控30次
            try:
                status = await page.evaluate("""
                    (function() {
                        const videos = document.querySelectorAll('video');
                        if (videos.length === 0) return null;
                        
                        const video = videos[0];
                        return {
                            duration: video.duration,
                            currentTime: video.currentTime,
                            ended: video.ended,
                            progress: video.duration > 0 ? (video.currentTime / video.duration * 100).toFixed(1) : 0
                        };
                    })();
                """)
                
                if status:
                    progress = float(status.get('progress', 0))
                    ended = status.get('ended', False)
                    
                    self.logger.info(f"📈 播放进度: {progress}%")
                    
                    if ended or progress >= 95:
                        self.logger.success("🎉 视频播放完成！")
                        break
                
                await asyncio.sleep(2)
                
            except Exception as e:
                self.logger.warning(f"⚠️  监控出错: {e}")
                await asyncio.sleep(2)

async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python smart_video.py <视频URL> [方法]")
        print("  方法: speed | duration | auto (默认)")
        print("")
        print("示例:")
        print("  python smart_video.py 'https://...' duration")
        return
    
    video_url = sys.argv[1]
    method = sys.argv[2] if len(sys.argv) > 2 else 'auto'
    
    processor = SmartVideoProcessor()
    
    try:
        await processor.process_video(video_url, method)
    except Exception as e:
        print(f"❌ 处理失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())

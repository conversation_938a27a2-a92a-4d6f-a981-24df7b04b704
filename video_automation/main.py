#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频学习网站自动化工具
"""

import asyncio
import sys
import os

from config import Config
from video_automation import VideoAutomation
from utils import Logger


async def main():
    """主函数"""
    print("=== 视频学习网站自动化工具 ===")

    logger = Logger()

    # 验证配置
    config_errors = Config.validate_config()
    if config_errors:
        logger.error("配置错误:")
        for error in config_errors:
            logger.error(f"  - {error}")
        logger.info("请在 .env 文件中设置 VIDEO_USERNAME 和 VIDEO_PASSWORD")
        return 1

    # 获取视频URL
    video_url = get_video_url()
    if not video_url:
        return 1

    # 运行自动化
    automation = VideoAutomation()
    try:
        await automation.run(video_url)
        logger.success("完成！")
        return 0
    except Exception as e:
        logger.error(f"执行失败: {str(e)}")
        return 1


def get_video_url():
    """获取视频URL"""
    # 从命令行参数获取
    if len(sys.argv) > 1:
        return sys.argv[1]

    # 交互式输入
    try:
        video_url = input("请输入视频URL: ").strip()
        if not video_url.startswith(('http://', 'https://')):
            print("请输入有效的URL")
            return None
        return video_url
    except KeyboardInterrupt:
        return None


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(1)

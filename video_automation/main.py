#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频学习网站自动化工具 - 主入口
"""

import asyncio
import sys
import os
from typing import Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from video_automation import VideoAutomation
from utils import Logger, print_banner


async def main():
    """主函数"""
    # 打印程序横幅
    print_banner()
    
    logger = Logger()
    
    # 验证配置
    config_errors = Config.validate_config()
    if config_errors:
        logger.error("配置验证失败:")
        for error in config_errors:
            logger.error(f"  - {error}")
        return
    
    # 打印当前配置
    Config.print_config()
    
    # 获取视频URL
    video_url = get_video_url()
    if not video_url:
        logger.error("未提供视频URL")
        return
    
    # 创建自动化实例
    automation = VideoAutomation()
    
    try:
        logger.info("开始执行视频自动化流程...")
        await automation.run(video_url)
        logger.success("自动化流程执行完成！")
        
    except KeyboardInterrupt:
        logger.warning("用户中断程序执行")
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        return 1
    
    return 0


def get_video_url() -> Optional[str]:
    """获取视频URL"""
    logger = Logger()
    
    # 从命令行参数获取
    if len(sys.argv) > 1:
        return sys.argv[1]
    
    # 从环境变量获取
    video_url = os.getenv('VIDEO_URL')
    if video_url:
        return video_url
    
    # 交互式输入
    try:
        print("\n" + "="*60)
        print("请输入视频页面URL:")
        print("示例: https://learning.lottery.gov.cn/video/12345")
        print("="*60)
        
        video_url = input("视频URL: ").strip()
        
        if not video_url:
            logger.error("URL不能为空")
            return None
        
        if not video_url.startswith(('http://', 'https://')):
            logger.error("请输入有效的URL（以http://或https://开头）")
            return None
        
        return video_url
        
    except (KeyboardInterrupt, EOFError):
        logger.warning("用户取消输入")
        return None


def setup_environment():
    """设置环境"""
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 设置环境变量示例文件
    env_example = """# 视频自动化工具环境变量配置
# 复制此文件为 .env 并填入实际值

# 用户凭据
VIDEO_USERNAME=your_username_here
VIDEO_PASSWORD=your_password_here

# 可选：视频URL（也可以通过命令行参数或交互输入）
VIDEO_URL=https://learning.lottery.gov.cn/video/12345
"""
    
    env_example_file = ".env.example"
    if not os.path.exists(env_example_file):
        with open(env_example_file, 'w', encoding='utf-8') as f:
            f.write(env_example)


def print_usage():
    """打印使用说明"""
    usage = """
使用方法:
    python main.py [视频URL]

参数:
    视频URL    可选，视频页面的完整URL

环境变量:
    VIDEO_USERNAME    用户名
    VIDEO_PASSWORD    密码
    VIDEO_URL         视频URL（可选）

示例:
    python main.py https://learning.lottery.gov.cn/video/12345
    
配置文件:
    创建 .env 文件来设置用户名和密码:
    VIDEO_USERNAME=your_username
    VIDEO_PASSWORD=your_password

注意事项:
    1. 首次运行需要安装依赖: pip install -r requirements.txt
    2. 需要安装Playwright浏览器: playwright install chromium
    3. 登录时如需短信验证码，请在浏览器中手动输入
    4. 程序会自动处理视频播放和完成对话框
    5. 请合理使用，遵守网站使用条款
"""
    print(usage)


if __name__ == "__main__":
    # 检查是否需要显示帮助
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
        sys.exit(0)
    
    # 设置环境
    setup_environment()
    
    # 运行主程序
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n程序执行出错: {str(e)}")
        sys.exit(1)

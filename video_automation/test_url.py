#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试URL参数修改功能
"""

import urllib.parse

def test_url_modification():
    """测试URL参数修改"""
    
    # 测试URL
    original_url = "https://v-bj.learning.lottery.gov.cn/csw/static/csw/clientAPI/mp4/coursePlay.html?sessionId=2CC627DA402384091CE9D66BBAEFFA59&itemlocation=https://v-bj.learning.lottery.gov.cn/csw/static/csw/courseware/CV001/250424211635009341/2504242116355533.mp4&lmslocation=HOST/csw/coursewareLearning/nonstandard&attemptnum=0&itemid=undefined&cswid=8ad4802b-97b80565-0197-bb6cf380-0170&viewmode=&currlocation=&clazzid=8ad48041-96d034d0-0196-d35b207c-0278&userId=4028819f-540e47f8-0154-13d84094-0ce0&courseId=8ad48041-9666c1eb-0196-66c3d05c-0005&cswServerAddress=https://v-bj.learning.lottery.gov.cn/csw&serverPath=https://learning.lottery.gov.cn/"
    
    print("🔍 原始URL分析:")
    print("=" * 60)
    
    # 解析URL
    parsed = urllib.parse.urlparse(original_url)
    params = urllib.parse.parse_qs(parsed.query)
    
    print(f"域名: {parsed.netloc}")
    print(f"路径: {parsed.path}")
    print("\n📋 关键参数:")
    
    key_params = ['sessionId', 'itemlocation', 'currlocation', 'userId', 'courseId']
    for param in key_params:
        value = params.get(param, [''])[0]
        if param == 'itemlocation' and value:
            # 只显示文件名
            filename = value.split('/')[-1]
            print(f"  {param}: {filename}")
        elif param == 'sessionId' and value:
            # 只显示前20个字符
            print(f"  {param}: {value[:20]}...")
        else:
            print(f"  {param}: {value if value else '空'}")
    
    # 模拟视频时长
    video_duration = 900  # 15分钟 = 900秒
    
    print(f"\n🎯 模拟视频时长: {video_duration}秒 ({video_duration//60}分{video_duration%60}秒)")
    
    # 修改currlocation参数
    params['currlocation'] = [str(video_duration)]
    
    # 重新构建URL
    new_query = urllib.parse.urlencode(params, doseq=True)
    new_url = urllib.parse.urlunparse((
        parsed.scheme, parsed.netloc, parsed.path,
        parsed.params, new_query, parsed.fragment
    ))
    
    print("\n✅ 修改后的URL:")
    print("=" * 60)
    
    # 解析新URL验证
    new_parsed = urllib.parse.urlparse(new_url)
    new_params = urllib.parse.parse_qs(new_parsed.query)
    
    print(f"新的currlocation: {new_params.get('currlocation', [''])[0]}")
    
    # 显示URL差异
    print(f"\n📊 URL长度对比:")
    print(f"  原始: {len(original_url)} 字符")
    print(f"  修改: {len(new_url)} 字符")
    
    # 保存到文件供测试
    with open('test_urls.txt', 'w') as f:
        f.write("原始URL:\n")
        f.write(original_url + "\n\n")
        f.write("修改后URL:\n")
        f.write(new_url + "\n")
    
    print(f"\n💾 URL已保存到 test_urls.txt")
    
    return new_url

if __name__ == "__main__":
    test_url_modification()

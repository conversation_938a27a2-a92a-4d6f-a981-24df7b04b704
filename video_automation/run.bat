@echo off
chcp 65001 >nul
title 视频学习网站自动化工具

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    视频学习网站自动化工具                      ║
echo ║                                                              ║
echo ║  选择操作:                                                    ║
echo ║  1. 安装依赖                                                  ║
echo ║  2. 快速启动                                                  ║
echo ║  3. 正常启动                                                  ║
echo ║  4. 退出                                                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set /p choice="请选择操作 (1-4): "

if "%choice%"=="1" goto install
if "%choice%"=="2" goto quick_start
if "%choice%"=="3" goto normal_start
if "%choice%"=="4" goto exit
goto invalid

:install
echo.
echo 🔄 正在安装依赖...
python install.py
pause
goto menu

:quick_start
echo.
echo 🚀 快速启动...
python quick_start.py
pause
goto menu

:normal_start
echo.
echo 📋 正常启动...
python main.py
pause
goto menu

:invalid
echo.
echo ❌ 无效选择，请重新选择
pause
goto menu

:menu
cls
goto start

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
exit

:start
goto menu

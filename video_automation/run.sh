#!/bin/bash

# 视频学习网站自动化工具启动脚本

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m'

# 打印横幅
print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    视频学习网站自动化工具                      ║"
    echo "║                                                              ║"
    echo "║  选择操作:                                                    ║"
    echo "║  1. 检查Chrome浏览器                                          ║"
    echo "║  2. 安装依赖                                                  ║"
    echo "║  3. 快速视频处理 (直接视频URL)                                ║"
    echo "║  4. 完整流程 (需要登录)                                       ║"
    echo "║  5. 测试登录页面                                              ║"
    echo "║  6. 查看帮助                                                  ║"
    echo "║  7. 退出                                                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查Chrome浏览器
check_chrome() {
    echo -e "${CYAN}🔍 检查Chrome浏览器...${NC}"
    python3 check_chrome.py
    read -p "按回车键继续..."
}

# 安装依赖
install_deps() {
    echo -e "${YELLOW}🔄 正在安装依赖...${NC}"
    pip3 install -r requirements.txt
    echo -e "${YELLOW}🔄 安装浏览器...${NC}"
    playwright install chromium
    echo ""
    echo -e "${CYAN}📋 重要提示:${NC}"
    echo "该网站只支持Google Chrome浏览器"
    echo "请确保已安装Chrome: https://www.google.com/chrome/"
    echo ""
    echo -e "${GREEN}✅ 安装完成${NC}"
    read -p "按回车键继续..."
}

# 运行程序
run_program() {
    echo -e "${GREEN}🚀 启动程序...${NC}"
    if [ $# -gt 0 ]; then
        python3 main.py "$1"
    else
        python3 main.py
    fi
    read -p "按回车键继续..."
}

# 测试登录页面
test_login() {
    echo -e "${CYAN}🧪 测试登录页面...${NC}"
    python3 test.py
    read -p "按回车键继续..."
}

# 显示帮助
show_help() {
    echo -e "${CYAN}📖 使用帮助${NC}"
    echo ""
    echo "命令行用法:"
    echo "  ./run.sh                    # 交互式菜单"
    echo "  ./run.sh [视频URL]          # 直接运行"
    echo "  ./run.sh check             # 检查Chrome浏览器"
    echo "  ./run.sh install           # 安装依赖"
    echo "  ./run.sh test              # 测试登录页面"
    echo "  ./run.sh help              # 显示帮助"
    echo ""
    echo "配置文件:"
    echo "  1. 复制配置文件: cp .env.example .env"
    echo "  2. 编辑 .env 文件设置用户名和密码"
    echo ""
    echo "示例:"
    echo "  ./run.sh https://learning.lottery.gov.cn/video/12345"
    echo ""
    read -p "按回车键继续..."
}

# 主菜单
show_menu() {
    while true; do
        clear
        print_banner
        echo ""
        read -p "请选择操作 (1-6): " choice

        case $choice in
            1)
                check_chrome
                ;;
            2)
                install_deps
                ;;
            3)
                run_program
                ;;
            4)
                test_login
                ;;
            5)
                show_help
                ;;
            6)
                echo -e "${GREEN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新选择${NC}"
                sleep 2
                ;;
        esac
    done
}

# 检查Python
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ 未找到Python3，请先安装Python3${NC}"
        exit 1
    fi
}

# 主函数
main() {
    check_python

    case "$1" in
        "check")
            check_chrome
            ;;
        "install")
            install_deps
            ;;
        "test")
            test_login
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        "")
            show_menu
            ;;
        *)
            # 视频URL
            run_program "$1"
            ;;
    esac
}

# 运行主函数
main "$@"

---
description: 
globs: 
alwaysApply: true
---
你是一名资深的架构师，请生成一个详细的应用系统架构图。图表内容要求：架构分层清晰： 必须展示以下核心层级：展示层 (Presentation Layer)网关层 (Gateway Layer)服务层 (Service Layer)数据层 (Data Layer)调用关系明确：使用虚线表示异步调用。使用实线表示同步调用。关键技术栈和中间件： 标注图表中每个主要组件所使用的关键技术栈和中间件（例如：Nginx, Kafka, MySQL, Redis, Spring Boot, React 等）。文字说明： 添加必要的文字说明，但请保持简洁，避免冗余。架构要素： 考虑并尽可能体现高可用、负载均衡等重要的架构要素。图表风格要求：文字： 所有文字必须使用简体中文。简洁清晰： 保持图表简洁清晰，避免过度复杂。图标和符号： 使用标准的、业界通用的技术图标和符号。配色方案：展示层： 蓝色系 (例如：#3498db)服务层 (业务层)： 绿色系 (例如：#2ecc71)数据层： 橙色系 (例如：#e67e22)其他组件可使用专业、技术感的色彩，但整体配色应保持协调。层次分明： 图表应层次分明，主次关系清晰。输出格式要求：根据图表的类型和复杂程度，选择适合的图表描述语言，例如：Mermaid 语法PlantUML 语法draw.io 语法 (如果需要更复杂的图形)如果是复杂架构，请考虑分模块逐步展示，以提高可读性。请确保生成的架构图能够直观、专业、准确地表达应用系统的结构和工作原理。
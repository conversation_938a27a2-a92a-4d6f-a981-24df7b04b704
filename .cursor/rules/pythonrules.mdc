---
description: 
globs: 
alwaysApply: true
---
Python 开发规范 
为确保代码质量、提高开发效率并实现高效团队协作，所有 Python 项目开发必须严格遵循以下规范：
1. 依赖管理 (Dependency Management)
精确管理：项目的所有外部库依赖必须通过 requirements.txt 文件进行精确管理，确保环境可复现。
导出依赖：必须使用 pip freeze > requirements.txt 命令导出当前虚拟环境的精确依赖列表。
安装依赖：新环境部署或协作者加入时，必须使用 pip install -r requirements.txt 命令安装所有依赖。
虚拟环境：强制要求为每个项目创建独立的 Python 虚拟环境 (例如使用 venv 或 conda)，以隔离项目依赖，避免全局环境污染和版本冲突。
2. 日志管理 (Log Management)
专用目录：所有应用程序产生的日志文件必须统一存放在项目根目录下的 logs/ 专用目录中。
详尽内容：日志内容必须记录详尽的信息，包含但不限于：操作流程、程序执行关键步骤、所有错误信息（含完整的堆栈跟踪）、以及最终执行结果。
结构化日志：必须使用 Python 的 logging 模块进行结构化日志记录，禁止使用 print() 函数输出日志。
合理分级：应确保日志记录级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）的合理使用，便于在不同环境（开发/生产）下进行高效的监控和问题排查。
3. 文件 I/O 标准化 (File I/O Standardization)
输入目录：所有程序所需的输入文件（如数据源、配置文件模板等）必须从项目根目录下的 files/ 目录读取。
输出目录：程序生成的任何输出结果（包括数据文件、报告、图像等）必须保存到项目根目录下的 output/ 目录下。
4. 目录结构自动化 (Directory Structure Automation)
自动创建：在项目启动或执行前，程序必须内置检查机制，自动检测并创建必要的目录（如 logs/, files/, output/）。若这些目录不存在，程序必须负责创建它们，而非依赖手动操作。
5. 配置管理 (Configuration Management)
禁止硬编码：严禁在代码中硬编码任何可变参数（如文件路径、API 密钥、数据库连接字符串、执行模式等）。
外部配置文件：所有可变参数必须通过外部配置文件进行管理。必须设置专用的配置文件（推荐使用 .ini, .json, .yaml 格式），供代码调用和访问。
环境变量：要求使用 python-dotenv 库管理环境变量。将敏感信息（如 API 密钥）或环境相关配置（如开发/生产模式切换）存储在 .env 文件中，并通过代码加载，以实现配置与代码的彻底分离。.env 文件必须加入到 .gitignore 中，避免敏感信息泄露。
统一加载：代码中必须包含一个统一的配置加载模块或函数，负责解析和提供所有外部配置参数，供项目各模块统一调用。
6. 输出简洁性 (Output Conciseness)
简洁明确：代码的返回结果、函数输出或控制台打印必须简洁明了，仅包含用户或下游程序所必需的核心信息。
禁止冗余：禁止在非报告或用户交互的主体输出中包含冗余的总结性语句、主观价值判断或过多的解释性文字。
7. 代码注释与文档 (Code Comments & Documentation)
详尽注释：代码必须包含详尽的中文注释，用于解释复杂的业务逻辑、关键算法和非直观的操作。
文档字符串 (Docstring)：在每个主要代码文件或模块的头部，必须包含清晰的文档字符串（Docstring），并遵循以下结构：
功能描述：简述文件/模块的用途、所使用的主要技术栈以及实现的核心功能。
操作说明：详细说明如何运行和操作代码，特别是涉及命令行参数、用户交互或特定环境配置的部分。
8. 代码风格 (Code Style)
PEP 8 规范：所有 Python 代码必须严格遵循 PEP 8 规范，包括但不限于命名约定、缩进（4个空格）、空行、最大行长度（推荐 88-120 之间，团队统一）等。
自动化格式化：必须使用代码格式化工具（如 Black, isort）和代码检查工具（如 Flake8）进行自动化风格检查和格式化，以保证代码风格的高度一致性。
9. 错误处理 (Error Handling)
预期异常：必须适当使用 try-except 块来捕获和处理预期的异常情况，防止程序因可预见的错误而意外崩溃。
精准捕获：严禁使用裸露的 except:，必须捕获具体的异常类型（如 ValueError, FileNotFoundError）。
优雅退出：对于不可恢复的严重错误，必须在 except 块中记录详细的错误日志，然后通过 sys.exit(1) 或其他方式优雅地退出程序，并返回非零状态码。
10. 模块化与可重用性 (Modularity & Reusability)
单一职责：必须将大型代码逻辑拆分为小型的、职责单一的函数、类和模块。
提高复用：要求编写通用性强、易于复用、接口清晰的代码，避免在项目中重复编写功能相似的代码。
import json

def generate_city_list_json():
    """
    Generates a JSON file containing a list of cities with their names,
    provinces, latitudes, and longitudes, based on the provided data.
    """

    try:
        input_file_path = input("Enter the path to the input JSON file (city.json): ")
        output_file_path = input("Enter the path to the output JSON file (e.g., city_list.json): ")

        with open(input_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        city_list = []
        for adcode, city_data in data.items():
            if city_data['level'] == 'province':
                province_name = city_data['name']
                for child in city_data['children']:
                    city_name = child['name']
                    lng, lat = child['center']
                    city_list.append({
                        "name": city_name,
                        "province": province_name,
                        "lat": lat,
                        "lng": lng
                    })

        # Group cities by province for better readability in the output file
        grouped_cities = {}
        for city in city_list:
            province = city['province']
            if province not in grouped_cities:
                grouped_cities[province] = []
            grouped_cities[province].append({
                "name": city['name'],
                "lat": city['lat'],
                "lng": city['lng']
            })

        output_data = {}
        for province, cities in grouped_cities.items():
            output_data[province] = cities

        with open(output_file_path, 'w', encoding='utf-8') as outfile:
            json.dump(output_data, outfile, indent=2, ensure_ascii=False)

        print(f"Successfully generated city list JSON file: {output_file_path}")

    except FileNotFoundError:
        print("Error: Input file not found.")
    except json.JSONDecodeError:
        print("Error: Invalid JSON format in the input file.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    generate_city_list_json()
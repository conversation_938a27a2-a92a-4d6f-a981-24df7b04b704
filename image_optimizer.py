#!/usr/bin/env python3
"""
图片优化工具
遍历 files/photoes 目录下的所有图片，按照指定格式进行优化：
- 宽高比：16:9
- 分辨率：1920×1080 (最小分辨率 1280×720)
- 文件格式：JPG
- 色彩模式：RGB
- 图片质量：90% (85-95%范围内)
- 保持图片完整性，不进行裁剪
"""

import os
import sys
from PIL import Image, ImageOps, ImageFilter
import logging
from pathlib import Path
import colorsys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/image_optimization.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ImageOptimizer:
    def __init__(self, input_dir="files/photoes", output_dir="output/photoes", overwrite_original=False):
        """
        初始化图片优化器
        
        Args:
            input_dir: 输入图片目录
            output_dir: 输出图片目录（当overwrite_original=True时忽略）
            overwrite_original: 是否直接覆盖原文件
        """
        self.input_dir = Path(input_dir)
        self.overwrite_original = overwrite_original
        
        if self.overwrite_original:
            self.output_dir = self.input_dir  # 直接覆盖原文件
        else:
            self.output_dir = Path(output_dir)
            # 创建输出目录
            self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 目标规格
        self.target_width = 1920
        self.target_height = 1080
        self.min_width = 1280
        self.min_height = 720
        self.target_ratio = 16/9
        self.quality = 90  # JPG压缩质量 (85-95%范围内)
        
        # 支持的图片格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff'}
        
        # 创建必要目录
        Path('logs').mkdir(exist_ok=True)
        
    def is_image_file(self, file_path):
        """检查文件是否为支持的图片格式"""
        return file_path.suffix.lower() in self.supported_formats
    
    def get_dominant_edge_color(self, img):
        """
        获取图片边缘的主要颜色，用于智能填充
        
        Args:
            img: PIL Image对象
            
        Returns:
            tuple: RGB颜色值
        """
        try:
            # 缩小图片以提高处理速度
            small_img = img.resize((100, 100), Image.Resampling.LANCZOS)
            
            # 提取边缘像素
            width, height = small_img.size
            edge_pixels = []
            
            # 上边缘和下边缘
            for x in range(width):
                edge_pixels.extend([small_img.getpixel((x, 0)), small_img.getpixel((x, height-1))])
            
            # 左边缘和右边缘
            for y in range(height):
                edge_pixels.extend([small_img.getpixel((0, y)), small_img.getpixel((width-1, y))])
            
            # 统计颜色频率
            color_count = {}
            for pixel in edge_pixels:
                if len(pixel) == 3:  # RGB
                    color_count[pixel] = color_count.get(pixel, 0) + 1
                elif len(pixel) == 4:  # RGBA
                    rgb_pixel = pixel[:3]
                    color_count[rgb_pixel] = color_count.get(rgb_pixel, 0) + 1
            
            # 返回最频繁的颜色，如果没有则返回白色
            if color_count:
                dominant_color = max(color_count, key=color_count.get)
                return dominant_color
            else:
                return (255, 255, 255)  # 白色
                
        except Exception as e:
            logger.warning(f"无法获取边缘颜色，使用白色填充: {e}")
            return (255, 255, 255)  # 白色
    
    def create_gradient_background(self, size, color):
        """
        创建渐变背景，使填充区域更自然
        
        Args:
            size: 背景尺寸 (width, height)
            color: 基础颜色
            
        Returns:
            PIL Image: 渐变背景图片
        """
        width, height = size
        
        # 创建基础背景
        background = Image.new('RGB', (width, height), color)
        
        # 如果颜色不是纯白或纯黑，创建轻微渐变效果
        r, g, b = color
        if not ((r == g == b == 255) or (r == g == b == 0)):
            # 创建轻微的亮度变化
            for y in range(height):
                brightness_factor = 0.95 + 0.1 * (y / height)  # 从95%到105%的亮度变化
                for x in range(width):
                    if x < width // 10 or x > width * 9 // 10 or y < height // 10 or y > height * 9 // 10:
                        # 在边缘区域应用轻微的亮度调整
                        new_r = min(255, int(r * brightness_factor))
                        new_g = min(255, int(g * brightness_factor))
                        new_b = min(255, int(b * brightness_factor))
                        background.putpixel((x, y), (new_r, new_g, new_b))
        
        return background
    
    def calculate_optimal_size(self, img_width, img_height):
        """
        计算最优的目标尺寸，保持图片完整性
        
        Args:
            img_width: 原图宽度
            img_height: 原图高度
            
        Returns:
            tuple: (canvas_width, canvas_height, scale_factor)
        """
        current_ratio = img_width / img_height
        
        # 计算需要填充的画布尺寸
        if current_ratio > self.target_ratio:
            # 图片比目标宽高比更宽，基于宽度计算
            canvas_width = img_width
            canvas_height = int(img_width / self.target_ratio)
        else:
            # 图片比目标宽高比更高，基于高度计算
            canvas_height = img_height
            canvas_width = int(img_height * self.target_ratio)
        
        # 确定最终尺寸和缩放因子
        if canvas_width >= self.target_width and canvas_height >= self.target_height:
            # 如果画布尺寸大于等于目标尺寸，缩放到目标尺寸
            scale_factor = min(self.target_width / canvas_width, self.target_height / canvas_height)
            final_width = self.target_width
            final_height = self.target_height
        elif canvas_width >= self.min_width and canvas_height >= self.min_height:
            # 如果画布尺寸在最小和目标之间，保持原画布尺寸
            scale_factor = 1.0
            final_width = canvas_width
            final_height = canvas_height
        else:
            # 如果画布尺寸小于最小尺寸，放大到最小尺寸
            scale_factor = min(self.min_width / canvas_width, self.min_height / canvas_height)
            final_width = self.min_width
            final_height = self.min_height
        
        return final_width, final_height, scale_factor
    
    def add_smart_padding(self, img):
        """
        智能填充图片到16:9比例，保持完整内容，图片置于右侧，左侧填充黑色
        
        Args:
            img: PIL Image对象
            
        Returns:
            tuple: (padded_image, scale_factor, processing_info)
        """
        img_width, img_height = img.size
        original_ratio = img_width / img_height
        
        # 使用黑色作为填充色
        fill_color = (0, 0, 0)  # 固定使用黑色填充
        
        # 计算最优尺寸
        final_width, final_height, scale_factor = self.calculate_optimal_size(img_width, img_height)
        
        # 计算缩放后的图片尺寸
        scaled_img_width = int(img_width * scale_factor)
        scaled_img_height = int(img_height * scale_factor)
        
        # 缩放原图
        if scale_factor != 1.0:
            img_scaled = img.resize((scaled_img_width, scaled_img_height), Image.Resampling.LANCZOS)
        else:
            img_scaled = img
        
        # 创建黑色背景画布
        background = Image.new('RGB', (final_width, final_height), fill_color)
        
        # 计算图片在右侧的位置
        x_offset = final_width - scaled_img_width  # 贴右边
        y_offset = (final_height - scaled_img_height) // 2  # 垂直居中
        
        # 将缩放后的图片粘贴到背景右侧
        background.paste(img_scaled, (x_offset, y_offset))
        
        # 处理信息
        processing_info = {
            'original_size': (img_width, img_height),
            'original_ratio': f"{original_ratio:.3f}",
            'target_ratio': f"{self.target_ratio:.3f}",
            'final_size': (final_width, final_height),
            'scale_factor': f"{scale_factor:.3f}",
            'fill_color': fill_color,
            'position': 'right_aligned',
            'padding_area': ((final_width * final_height) - (scaled_img_width * scaled_img_height)) / (final_width * final_height) * 100
        }
        
        return background, scale_factor, processing_info
    
    def optimize_image(self, input_path, output_path):
        """
        优化单张图片，确保完整性
        
        Args:
            input_path: 输入图片路径
            output_path: 输出图片路径
        """
        try:
            # 记录原始文件大小
            input_size = input_path.stat().st_size
            
            # 打开图片
            with Image.open(input_path) as img:
                logger.info(f"开始处理: {input_path.name}")
                logger.info(f"原始尺寸: {img.size[0]}×{img.size[1]}")
                logger.info(f"原始格式: {img.format}")
                logger.info(f"原始模式: {img.mode}")
                
                # 转换为RGB模式（如果不是的话）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                    logger.info(f"已转换色彩模式为RGB")
                
                # 自动旋转图片（基于EXIF信息）
                img = ImageOps.exif_transpose(img)
                
                # 智能填充处理
                final_img, scale_factor, processing_info = self.add_smart_padding(img)
                
                # 记录详细处理信息
                logger.info(f"处理详情:")
                logger.info(f"  - 原始比例: {processing_info['original_ratio']} (目标: {processing_info['target_ratio']})")
                logger.info(f"  - 最终尺寸: {processing_info['final_size'][0]}×{processing_info['final_size'][1]}")
                logger.info(f"  - 缩放系数: {processing_info['scale_factor']}")
                logger.info(f"  - 图片位置: 右对齐")
                logger.info(f"  - 填充颜色: 黑色 RGB{processing_info['fill_color']}")
                logger.info(f"  - 填充区域: {processing_info['padding_area']:.1f}%")
                
                # 保存为JPG格式
                final_img.save(
                    output_path,
                    'JPEG',
                    quality=self.quality,
                    optimize=True,
                    progressive=True
                )
                
                # 计算文件大小变化
                output_size = output_path.stat().st_size
                size_change = ((output_size - input_size) / input_size) * 100
                compression_ratio = (1 - output_size / input_size) * 100
                
                logger.info(f"文件大小: {input_size:,} → {output_size:,} bytes ({size_change:+.1f}%)")
                if compression_ratio > 0:
                    logger.info(f"压缩比例: {compression_ratio:.1f}%")
                
                action = "覆盖" if self.overwrite_original else "保存到"
                logger.info(f"✅ 成功{action}: {output_path.name}")
                logger.info("-" * 60)
                
                return True
                
        except Exception as e:
            logger.error(f"❌ 处理图片 {input_path.name} 时出错: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def process_all_images(self):
        """处理所有图片"""
        if not self.input_dir.exists():
            logger.error(f"输入目录不存在: {self.input_dir}")
            return
        
        # 获取所有图片文件
        image_files = [f for f in self.input_dir.iterdir() 
                      if f.is_file() and self.is_image_file(f)]
        
        if not image_files:
            logger.warning(f"在目录 {self.input_dir} 中未找到支持的图片文件")
            return
        
        logger.info(f"发现 {len(image_files)} 张图片需要处理")
        logger.info(f"输入目录: {self.input_dir.absolute()}")
        
        if self.overwrite_original:
            logger.info(f"处理模式: 直接覆盖原文件")
        else:
            logger.info(f"输出目录: {self.output_dir.absolute()}")
            
        logger.info("=" * 60)
        
        success_count = 0
        failed_files = []
        
        for i, image_file in enumerate(image_files, 1):
            logger.info(f"进度: {i}/{len(image_files)}")
            
            # 生成输出文件名（统一为.jpg扩展名）
            output_filename = image_file.stem + '.jpg'
            output_path = self.output_dir / output_filename
            
            # 如果目标文件已存在且不是原文件本身，先删除它
            if output_path.exists() and output_path != image_file:
                try:
                    output_path.unlink()
                    logger.info(f"已删除目标目录中的重名文件: {output_path.name}")
                except Exception as e:
                    logger.warning(f"删除重名文件 {output_path.name} 时出错: {e}")
            
            # 优化并保存图片
            if self.optimize_image(image_file, output_path):
                success_count += 1
            else:
                failed_files.append(image_file.name)
        
        # 输出处理结果摘要
        logger.info("=" * 60)
        logger.info(f"处理完成！")
        logger.info(f"成功: {success_count}/{len(image_files)} 张图片")
        
        if failed_files:
            logger.warning(f"失败的文件 ({len(failed_files)}):")
            for failed_file in failed_files:
                logger.warning(f"  - {failed_file}")
        
        success_rate = (success_count / len(image_files)) * 100
        logger.info(f"成功率: {success_rate:.1f}%")

def main():
    """主函数"""
    print("图片优化工具 v2.3")
    print("=" * 50)
    print("优化规格:")
    print("- 宽高比: 16:9 (保持完整性，不裁剪)")
    print("- 分辨率: 1920×1080 (最小 1280×720)")
    print("- 格式: JPG")
    print("- 色彩模式: RGB")
    print("- 质量: 90%")
    print("- 图片位置: 右对齐")
    print("- 填充策略: 左侧黑色填充")
    print("- 处理模式: 覆盖目标目录中的重名文件")
    print("=" * 50)
    
    # 创建优化器并处理图片（覆盖目标目录中的重名文件）
    optimizer = ImageOptimizer(overwrite_original=True)
    optimizer.process_all_images()

if __name__ == "__main__":
    main()

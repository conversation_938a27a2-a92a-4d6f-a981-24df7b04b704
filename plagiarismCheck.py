# Description: 查找 Excel 表格中指定列的重复值及其所在位置，并输出到文本文件。

import os
import pandas as pd


def get_column_letter(index):
    """
    将列的数字索引转换为 Excel 的列字母（如 1 -> A, 2 -> B）。

    Args:
        index (int): 列的数字索引（从 1 开始）。

    Returns:
        str: Excel 列字母。
    """
    letter = ""
    while index > 0:
        index -= 1
        letter = chr(index % 26 + 65) + letter
        index //= 26
    return letter


def find_duplicates(file_path, column_name, output_dir):
    """
    查找指定列中的重复值及其所在位置，并输出到文本文件。

    Args:
        file_path (str): 输入文件路径 (CSV 或 Excel 文件)。
        column_name (str): 要查重的列名。
        output_dir (str): 输出文件的目录路径。
    """
    try:
        # 根据文件扩展名选择读取方法
        if file_path.endswith(".csv"):
            df = pd.read_csv(file_path)
        elif file_path.endswith(".xlsx") or file_path.endswith(".xls"):
            df = pd.read_excel(file_path)
        else:
            print("仅支持 CSV 或 Excel 文件！")
            return

        # 检查列名是否存在
        if column_name not in df.columns:
            print(f"列名 '{column_name}' 不存在于文件中！请检查输入！")
            return

        # 查找重复值
        duplicate_counts = df[column_name].value_counts()
        duplicates = duplicate_counts[duplicate_counts > 1]

        if duplicates.empty:
            print(f"列 '{column_name}' 中没有重复值。")
            return

        # 定位重复值所在的单元格
        duplicate_locations = {}
        column_index = df.columns.get_loc(column_name) + 1  # 获取列的索引
        column_letter = get_column_letter(column_index)  # 转换为 Excel 列字母
        for value in duplicates.index:
            locations = df.index[df[column_name] == value].tolist()
            duplicate_locations[value] = [f"{column_letter}{idx + 2}" for idx in locations]  # +2: Excel 从第2行开始（加上标题）

        # 自动生成文件名
        output_file = os.path.join(output_dir, f"{column_name}.txt")

        # 输出到文本文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"列名: {column_name}\n")
            f.write(f"{'值':<20}{'出现次数':<10}{'单元格位置':<30}\n")
            f.write("=" * 60 + "\n")
            for value, count in duplicates.items():
                locations = ", ".join(duplicate_locations[value])
                f.write(f"{value:<20}{count:<10}{locations:<30}\n")
            f.write("=" * 60 + "\n")
            f.write(f"总计重复值: {len(duplicates)}\n")

        print(f"重复值统计已保存到 {output_file}")

    except Exception as e:
        print(f"处理文件时发生错误：{e}")


def main():
    while True:
        # 用户交互 - 输入文件路径
        file_path = input("请输入文件路径 (CSV 或 Excel 文件): ").strip()
        if not file_path:
            print("文件路径不能为空，程序退出。")
            break
        if not os.path.exists(file_path):
            print("输入的文件路径不存在，请重新检查！")
            continue

        # 用户交互 - 输入列名
        column_name = input("请输入要查重的列名: ").strip()
        if not column_name:
            print("列名不能为空，程序退出。")
            break

        # 用户交互 - 输入输出目录
        output_dir = input("请输入输出文件的目录: ").strip()
        if not output_dir:
            print("输出目录不能为空，程序退出。")
            break
        if not os.path.exists(output_dir):
            print("输出目录不存在，程序退出。")
            break

        # 执行查重操作
        find_duplicates(file_path, column_name, output_dir)

        # 询问是否继续
        should_continue = input("是否继续查重？输入 1 继续，输入 0 或留空退出: ").strip()
        if should_continue != "1":
            print("程序退出。")
            break


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Excel输出文件内容
功能：检查search_excel_to_excel.py生成的Excel文件
作者：自动生成
日期：2025年
"""

import pandas as pd
import os
import glob
from datetime import datetime

def find_latest_excel_result():
    """
    查找最新的Excel搜索结果文件
    """
    pattern = "output/搜索结果_4_98_9_*.xlsx"
    result_files = glob.glob(pattern)
    
    if not result_files:
        print("没有找到Excel搜索结果文件")
        return None
    
    # 找到最新的文件
    latest_file = max(result_files, key=os.path.getmtime)
    return latest_file

def verify_excel_file(excel_path):
    """
    验证Excel文件内容
    """
    print(f"=== Excel文件验证报告 ===")
    print(f"文件路径: {excel_path}")
    print(f"文件大小: {os.path.getsize(excel_path)} 字节")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(excel_path)
        sheet_names = excel_file.sheet_names
        
        print(f"工作表数量: {len(sheet_names)}")
        print("工作表列表:")
        for i, sheet_name in enumerate(sheet_names, 1):
            print(f"  {i}. {sheet_name}")
        print()
        
        # 详细检查每个工作表
        total_records = 0
        for sheet_name in sheet_names:
            print(f"=== 工作表: {sheet_name} ===")
            
            # 读取工作表数据
            df = pd.read_excel(excel_path, sheet_name=sheet_name)
            
            print(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
            print(f"列名: {list(df.columns)}")
            
            total_records += df.shape[0]
            
            # 显示前几行数据
            if df.shape[0] > 0:
                print("前3行数据预览:")
                for idx, row in df.head(3).iterrows():
                    print(f"  行 {idx + 1}:")
                    for col in df.columns:
                        value = row[col]
                        if pd.notna(value) and str(value).strip():
                            print(f"    {col}: {value}")
                print()
                
                # 检查是否包含搜索字符串
                search_string = "4.98.9"
                found_cells = []
                
                for idx, row in df.iterrows():
                    for col in df.columns:
                        cell_value = str(row[col]) if pd.notna(row[col]) else ''
                        if search_string in cell_value:
                            found_cells.append({
                                'row': idx + 1,
                                'column': col,
                                'value': cell_value
                            })
                
                print(f"包含'{search_string}'的单元格数量: {len(found_cells)}")
                if found_cells:
                    print("匹配示例:")
                    for cell in found_cells[:3]:  # 只显示前3个
                        print(f"  - 行{cell['row']} {cell['column']}: {cell['value']}")
            else:
                print("该工作表无数据")
            
            print("-" * 50)
        
        print(f"总记录数: {total_records}")
        print("验证完成！")
        
    except Exception as e:
        print(f"验证过程中出错: {str(e)}")

def main():
    """
    主函数
    """
    print("Excel搜索结果文件验证工具")
    print("=" * 50)
    
    # 查找最新的Excel结果文件
    latest_file = find_latest_excel_result()
    
    if not latest_file:
        return
    
    # 验证文件
    verify_excel_file(latest_file)

if __name__ == "__main__":
    main() 
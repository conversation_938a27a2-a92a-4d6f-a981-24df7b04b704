# Description: 调用系统命令 nslookup 查询域名解析结果，并保存到文件中。

import concurrent.futures
import subprocess
import os

# 定义 nslookup 查询函数
def nslookup_query(domain):
    try:
        # 使用 nslookup 查询域名
        result = subprocess.run(
            ["nslookup", domain],
            capture_output=True,
            text=True,
            check=False  # 允许非零状态返回
        )
        if result.returncode != 0:
            return domain, f"Error: {result.stderr.strip()}"
        return domain, result.stdout.strip()  # 返回域名和查询结果
    except FileNotFoundError:
        return domain, "Error: nslookup command not found"
    except Exception as e:
        return domain, f"Exception: {e}"

# 读取域名列表
def read_domains(file_path):
    try:
        with open(file_path, 'r') as f:
            return [line.strip() for line in f.readlines() if line.strip()]
    except FileNotFoundError:
        print(f"输入文件 {file_path} 不存在，请检查路径！")
        exit(1)

# 保存结果到文件
def save_results(results, output_file):
    try:
        with open(output_file, 'w') as f:
            for domain, result in results:
                f.write(f"{domain}:\n{result}\n{'-' * 40}\n")
    except PermissionError:
        print(f"无法写入文件 {output_file}，请检查文件权限！")
        exit(1)

# 主函数
def main():
    input_file = "files/domains.txt"  # 包含域名的输入文件
    output_file = "output/domain_nslookup_results.txt"  # 保存结果的输出文件

    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在，请提供域名列表！")
        return

    # 读取域名列表
    domains = read_domains(input_file)

    # 使用线程池进行并发 nslookup 查询
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        future_to_domain = {executor.submit(nslookup_query, domain): domain for domain in domains}
        for future in concurrent.futures.as_completed(future_to_domain):
            domain = future_to_domain[future]
            try:
                results.append(future.result())
            except Exception as exc:
                results.append((domain, f"Exception: {exc}"))

    # 保存结果到文件
    save_results(results, output_file)
    print(f"查询完成，结果已保存到 {output_file}")

if __name__ == "__main__":
    main()
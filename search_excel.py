#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件搜索工具
功能：读取files目录下包含"西五环"的Excel文件，遍历所有sheet页，查找包含'4.98.9'的字符串
当找到匹配时，保存包含该字符串的整条记录
作者：自动生成
日期：2025年
"""

import os
import pandas as pd
import glob
import csv
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def ensure_output_directory():
    """
    确保output目录存在
    """
    output_dir = 'output'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logger.info(f"创建output目录: {output_dir}")
    return output_dir

def find_excel_files_with_keyword(files_dir, keyword):
    """
    在指定目录中查找包含关键字的Excel文件
    
    参数:
        files_dir (str): 要搜索的目录路径
        keyword (str): 要搜索的关键字
    
    返回:
        list: 匹配的Excel文件路径列表
    """
    # 支持的Excel文件扩展名
    excel_extensions = ['*.xlsx', '*.xls']
    matched_files = []
    
    for extension in excel_extensions:
        # 构建搜索模式
        pattern = os.path.join(files_dir, extension)
        files = glob.glob(pattern)
        
        # 筛选包含关键字的文件
        for file_path in files:
            filename = os.path.basename(file_path)
            if keyword in filename:
                matched_files.append(file_path)
                logger.info(f"找到匹配文件: {filename}")
    
    return matched_files

def search_in_excel_file(file_path, search_string):
    """
    在Excel文件的所有sheet页中搜索指定字符串，返回包含该字符串的完整行记录
    
    参数:
        file_path (str): Excel文件路径
        search_string (str): 要搜索的字符串
    
    返回:
        list: 包含搜索结果的列表，每个元素是一个字典，包含完整的行数据
    """
    results = []
    filename = os.path.basename(file_path)
    
    try:
        # 读取Excel文件的所有sheet名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        logger.info(f"正在搜索文件: {filename}, 共有 {len(sheet_names)} 个sheet页")
        
        for sheet_name in sheet_names:
            try:
                # 读取每个sheet页
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                logger.info(f"  - 正在搜索sheet: {sheet_name}, 数据形状: {df.shape}")
                
                # 记录已处理的行，避免重复添加同一行
                processed_rows = set()
                
                # 遍历DataFrame中的每个单元格
                for row_idx, row in df.iterrows():
                    # 如果这一行已经被处理过，跳过
                    if row_idx in processed_rows:
                        continue
                        
                    # 检查这一行是否包含搜索字符串
                    row_contains_search = False
                    matched_cells = []  # 记录匹配的单元格信息
                    
                    for col_idx, cell_value in enumerate(row):
                        # 将单元格值转换为字符串进行搜索
                        cell_str = str(cell_value) if pd.notna(cell_value) else ''
                        
                        # 检查是否包含搜索字符串
                        if search_string in cell_str:
                            row_contains_search = True
                            col_name = df.columns[col_idx] if col_idx < len(df.columns) else f'Column_{col_idx}'
                            matched_cells.append({
                                'column': col_name,
                                'value': cell_str
                            })
                    
                    # 如果这一行包含搜索字符串，保存完整的行数据
                    if row_contains_search:
                        # 构建包含完整行数据的结果记录
                        result = {
                            'fileName': filename,
                            'sheetName': sheet_name,
                            'rowIndex': row_idx + 2,  # +2是因为pandas从0开始，Excel从1开始，且通常有表头
                            'searchString': search_string,
                            'foundTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'matchedCells': ', '.join([f"{cell['column']}:{cell['value']}" for cell in matched_cells])
                        }
                        
                        # 添加原始数据的所有列
                        for col_idx, col_name in enumerate(df.columns):
                            cell_value = row.iloc[col_idx] if col_idx < len(row) else ''
                            # 处理空值和特殊值
                            if pd.isna(cell_value):
                                cell_value = ''
                            else:
                                cell_value = str(cell_value)
                            result[f'原始_{col_name}'] = cell_value
                        
                        results.append(result)
                        processed_rows.add(row_idx)
                        
                        # 记录日志
                        matched_info = ', '.join([f"{cell['column']}={cell['value']}" for cell in matched_cells])
                        logger.info(f"    找到匹配行: Row {result['rowIndex']}, 匹配字段: {matched_info}")
                            
            except Exception as e:
                logger.error(f"读取sheet '{sheet_name}' 时出错: {str(e)}")
                continue
                
    except Exception as e:
        logger.error(f"读取文件 '{filename}' 时出错: {str(e)}")
    
    return results

def save_results_to_csv(results, output_dir):
    """
    将搜索结果保存到CSV文件
    
    参数:
        results (list): 搜索结果列表
        output_dir (str): 输出目录路径
    
    返回:
        str: 保存的CSV文件路径
    """
    if not results:
        logger.warning("没有搜索结果需要保存")
        return None
        
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_filename = f'excel_search_results_{timestamp}.csv'
    csv_path = os.path.join(output_dir, csv_filename)
    
    # 收集所有可能的字段名（动态列处理）
    all_fieldnames = set()
    basic_fields = ['fileName', 'sheetName', 'rowIndex', 'searchString', 'foundTime', 'matchedCells']
    
    for result in results:
        all_fieldnames.update(result.keys())
    
    # 将基础字段放在前面，原始数据字段放在后面
    original_fields = [field for field in all_fieldnames if field.startswith('原始_')]
    original_fields.sort()  # 按字母顺序排序
    
    fieldnames = basic_fields + original_fields
    
    try:
        with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入数据
            for result in results:
                # 确保所有字段都存在，缺失的字段用空字符串填充
                row_data = {}
                for field in fieldnames:
                    row_data[field] = result.get(field, '')
                writer.writerow(row_data)
        
        logger.info(f"搜索结果已保存到: {csv_path}")
        logger.info(f"共找到 {len(results)} 条匹配记录")
        
    except Exception as e:
        logger.error(f"保存CSV文件时出错: {str(e)}")
        raise
    
    return csv_path

def main():
    """
    主函数：执行完整的搜索流程
    """
    # 配置参数
    files_dir = 'files'  # Excel文件所在目录
    keyword = '西五环'    # 文件名关键字
    search_string = '4.98.9'  # 要搜索的字符串
    
    logger.info("开始执行Excel文件搜索任务")
    logger.info(f"搜索目录: {files_dir}")
    logger.info(f"文件名关键字: {keyword}")
    logger.info(f"搜索字符串: {search_string}")
    
    try:
        # 1. 确保输出目录存在
        output_dir = ensure_output_directory()
        
        # 2. 查找包含关键字的Excel文件
        excel_files = find_excel_files_with_keyword(files_dir, keyword)
        
        if not excel_files:
            logger.warning(f"在目录 '{files_dir}' 中没有找到包含 '{keyword}' 的Excel文件")
            return
        
        logger.info(f"共找到 {len(excel_files)} 个匹配的Excel文件")
        
        # 3. 在每个Excel文件中搜索指定字符串
        all_results = []
        for file_path in excel_files:
            results = search_in_excel_file(file_path, search_string)
            all_results.extend(results)
        
        # 4. 保存搜索结果到CSV文件
        if all_results:
            csv_path = save_results_to_csv(all_results, output_dir)
            if csv_path:
                logger.info(f"任务完成！结果已保存到: {csv_path}")
        else:
            logger.info(f"在所有文件中都没有找到包含 '{search_string}' 的单元格")
            
            # 仍然创建一个空的CSV文件记录本次搜索
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            csv_filename = f'excel_search_results_{timestamp}_empty.csv'
            csv_path = os.path.join(output_dir, csv_filename)
            
            fieldnames = ['fileName', 'sheetName', 'rowIndex', 'searchString', 'foundTime', 'matchedCells', '搜索结果']
            
            with open(csv_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                # 写入一行说明信息
                writer.writerow({
                    'fileName': '搜索完成',
                    'sheetName': f'未找到包含"{search_string}"的记录',
                    'rowIndex': '',
                    'searchString': search_string,
                    'foundTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'matchedCells': '',
                    '搜索结果': '无匹配记录'
                })
            
            logger.info(f"空结果文件已保存到: {csv_path}")
        
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main() 
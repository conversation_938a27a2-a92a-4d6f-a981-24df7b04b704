# 导入所需的库
import pandas as pd  # 用于数据处理，特别是读写 Excel/CSV
import os          # 用于操作系统相关功能，如路径操作和目录创建
import re          # 用于正则表达式，这里用来清理文件名
import logging     # 用于日志记录

def sanitize_filename(filename):
    """
    清理文件名，移除或替换 Windows 和 macOS/Linux 文件系统中不允许的字符。
    Args:
        filename (str): 原始文件名 (通常来自单元格的值)。
    Returns:
        str: 清理后的安全文件名。
    """
    # 将值转换为字符串，以防原始值不是字符串类型
    filename = str(filename)
    # 移除或替换非法字符： / \ : * ? " < > |
    # 这里我们将这些字符替换为下划线 '_'
    sanitized = re.sub(r'[\\/*?:"<>|]', "_", filename)
    # 移除文件名开头和结尾的空格或点，这在Windows上可能导致问题
    sanitized = sanitized.strip('. ')
    # 防止文件名过长 (Windows 文件路径最大长度约为 260 字符，保守起见)
    # 这里限制文件名本身长度，具体路径长度限制还需要考虑目录部分
    max_len = 100
    if len(sanitized) > max_len:
        sanitized = sanitized[:max_len]
    # 如果清理后文件名为空 (例如，原始值就是非法字符)，提供一个默认名
    if not sanitized:
        sanitized = "空值或非法字符"
    return sanitized

def split_excel_by_column():
    """
    交互式读取 Excel 或 CSV 文件，根据指定列的值将数据拆分保存到不同的 Excel 文件中。
    """
    print("--- Excel/CSV 数据拆分工具 ---")

    # 1. 获取输入文件路径
    while True:
        input_file_path = input("请输入源 Excel 或 CSV 文件的完整路径: ").strip()
        # 检查路径是否存在并且是一个文件
        if os.path.exists(input_file_path) and os.path.isfile(input_file_path):
            break # 文件有效，跳出循环
        else:
            print(f"错误: 文件 '{input_file_path}' 不存在或不是一个有效的文件。请重新输入。")

    # 2. 获取筛选列名
    column_name = input("请输入要根据其值进行拆分的列名: ").strip()

    # 3. 获取输出目录路径
    output_dir_path = input("请输入保存拆分后文件的目录路径 (如果目录不存在，将自动创建): ").strip()

    try:
        # 4. 读取文件
        print(f"正在读取文件: {input_file_path} ...")
        # 获取文件扩展名，转换为小写以便比较
        file_extension = os.path.splitext(input_file_path)[1].lower()

        # 根据扩展名选择合适的 pandas 读取函数
        if file_extension == '.xlsx':
            # 读取 Excel 文件，使用 openpyxl 引擎（需要安装）
            df = pd.read_excel(input_file_path, engine='openpyxl')
        elif file_extension == '.csv':
            # 尝试使用默认（通常是 utf-8）编码读取 CSV
            try:
                df = pd.read_csv(input_file_path)
            except UnicodeDecodeError:
                # 如果 UTF-8 解码失败，尝试使用 'gbk' 编码（常见于中文环境下的旧文件）
                print("默认编码读取失败，尝试使用 'gbk' 编码...")
                try:
                    df = pd.read_csv(input_file_path, encoding='gbk')
                except Exception as e_gbk:
                     print(f"使用 'gbk' 编码读取 CSV 文件时也发生错误: {e_gbk}")
                     return # 如果 gbk 也失败，则退出
            except Exception as e_utf8:
                 print(f"读取 CSV 文件时发生错误: {e_utf8}")
                 return # 其他读取错误则退出
        else:
            # 如果文件扩展名不是 .xlsx 或 .csv，则提示错误并退出
            print(f"错误: 不支持的文件类型 '{file_extension}'。仅支持 .xlsx 和 .csv 文件。")
            return

        print("文件读取成功。")
        # 记录原始数据行数（不含表头）
        original_row_count = len(df)

        # 5. 验证列名是否存在于 DataFrame 的列中
        if column_name not in df.columns:
            print(f"错误: 指定的列名 '{column_name}' 在文件中不存在。")
            print(f"文件包含的列有: {list(df.columns)}") # 打印所有可用列名，方便用户检查
            return # 列名无效，退出

        # 6. 获取指定列的唯一值
        # unique() 方法返回一个包含该列所有不重复值的数组 (NumPy array)
        # 使用 dropna=False (Pandas 1.x+) 或手动检查 NaN 以兼容旧版
        # 这里我们将在循环中处理 NaN
        unique_values = df[column_name].unique()
        print(f"在列 '{column_name}' 中找到以下唯一值: {list(unique_values)}") # 打印找到的唯一值列表

        # 7. 创建输出目录
        try:
            # os.makedirs 会创建所有必需的中间目录
            # exist_ok=True 表示如果目标目录已存在，则不会引发错误
            os.makedirs(output_dir_path, exist_ok=True)
            print(f"输出目录 '{output_dir_path}' 已准备好或已存在。")

            # ---- 配置日志记录 ----
            log_file_path = os.path.join(output_dir_path, 'split_log.txt')
            # 创建一个 logger
            logger = logging.getLogger('excel_splitter')
            logger.setLevel(logging.INFO) # 设置日志级别
            # 创建一个 handler，用于写入日志文件 (覆盖模式)
            # 使用 encoding='utf-8' 以支持中文等字符
            file_handler = logging.FileHandler(log_file_path, mode='w', encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            # 创建一个日志格式
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            # 如果 logger 没有 handler，则添加 handler (防止重复添加)
            if not logger.handlers:
                logger.addHandler(file_handler)
            # -----------------------

        except OSError as e:
            # 捕获可能的文件系统错误，例如权限不足
            print(f"错误: 创建输出目录 '{output_dir_path}' 失败: {e}")
            return # 目录创建失败，退出

        # 8. 筛选数据并保存到新文件
        print("开始拆分文件...")
        count_success = 0 # 记录成功保存的文件数量
        split_row_count = 0 # 初始化拆分后的总行数计数器
        count_total = len(unique_values) # 要处理的唯一值总数

        # 遍历每一个唯一值
        for value in unique_values:
            # 检查当前值是否是 NaN (Pandas 通常用 pd.isna() 来检查)
            if pd.isna(value):
                # 如果是 NaN，筛选出原始 DataFrame 中该列是 NaN 的所有行
                filtered_df = df[df[column_name].isna()]
                # 为 NaN 值的文件指定一个明确的文件名: 空值_列名.xlsx
                # 对列名也进行清理，以防列名包含非法字符
                output_filename = f"空值_{sanitize_filename(column_name)}.xlsx"
            else:
                # 如果不是 NaN，筛选出原始 DataFrame 中该列等于当前 value 的所有行
                filtered_df = df[df[column_name] == value]
                # 使用之前定义的 sanitize_filename 函数清理当前值，作为文件名
                sanitized_value = sanitize_filename(value)
                # 文件名格式: 清理后的字段值_列名.xlsx
                output_filename = f"{sanitized_value}_{sanitize_filename(column_name)}.xlsx"

            # 构建输出文件的完整路径，文件名使用清理后的基础名加上 .xlsx 扩展名
            # output_filename = f"{file_base_name}.xlsx" # 旧的文件名逻辑，注释掉或删除
            output_path = os.path.join(output_dir_path, output_filename)

            try:
                 # 将筛选出的数据 (filtered_df) 保存到 Excel 文件
                 # index=False 表示不将 DataFrame 的行索引写入 Excel 文件
                 # engine='openpyxl' 明确指定使用 openpyxl 引擎写入 .xlsx 文件
                filtered_df.to_excel(output_path, index=False, engine='openpyxl')
                print(f"  - 已将 '{value}' (文件: {output_filename}) 相关记录保存到: {output_path}")
                count_success += 1 # 成功保存，计数器加 1
                split_row_count += len(filtered_df) # 累加当前拆分文件的行数
            except Exception as e:
                # 如果保存过程中发生任何错误（如磁盘空间不足、权限问题等）
                print(f"  - 错误: 保存文件 '{output_path}' 时出错: {e}")
                print(f"    跳过值: {value}") # 打印提示，说明哪个值的文件保存失败

        # 9. 完成反馈
        print("-" * 30) # 打印分隔线，使输出更清晰
        print("处理完成！")
        print(f"总共处理了 {count_total} 个唯一值。")
        print(f"成功保存了 {count_success} 个文件到目录: {output_dir_path}")
        # 如果有文件保存失败，给出提示
        if count_success < count_total:
            print(f"有 {count_total - count_success} 个文件未能成功保存，请检查上面的错误信息。")

        # 添加数量校验结果
        print("-" * 30)
        print("数量校验:")
        print(f"  - 原始文件 '{os.path.basename(input_file_path)}' 总行数 (不含表头): {original_row_count}")
        print(f"  - 所有拆分出的文件累加总行数: {split_row_count}")
        # 同时记录到日志文件
        logger.info(f"原始文件 '{os.path.basename(input_file_path)}' 总行数 (不含表头): {original_row_count}")
        logger.info(f"所有拆分出的文件累加总行数: {split_row_count}")

        if original_row_count == split_row_count:
            validation_message = "校验结果: 行数一致。数据完整性校验通过。"
            print(f"  - {validation_message}")
            logger.info(validation_message)
        else:
            # 如果行数不一致，并且所有唯一值都尝试处理了（即使部分保存失败），才警告数据可能丢失
            # 如果是因为保存错误导致 split_row_count < original_row_count，前面的错误信息已提示
            if count_success == count_total: # 只有在所有文件都号称成功保存但行数仍不一致时，着重警告
                 warning_message = f"警告: 行数不一致！原始行数 {original_row_count}, 拆分后总行数 {split_row_count}。可能存在未知问题。"
                 print(f"  - {warning_message}")
                 logger.warning(warning_message) # 使用 warning 级别记录
            else: # 如果部分文件保存失败导致行数不一致，这是预期内的
                 notice_message = f"注意: 行数不一致是由于部分文件未能成功保存。"
                 print(f"  - {notice_message}")
                 logger.info(notice_message)
        print("-" * 30)
        logger.info("处理完成.") # 在日志中也标记处理完成

    except FileNotFoundError:
        # 这个错误理论上在输入验证时已被捕获，但为了健壮性再次添加
        print(f"错误: 文件 '{input_file_path}' 未找到。")
    except pd.errors.EmptyDataError:
        # 如果读取的文件是空的
        print(f"错误: 文件 '{input_file_path}' 是空的或格式不正确。")
    except KeyError:
        # 这个错误理论上在列名验证时已被捕获
        print(f"错误: 列名 '{column_name}' 不存在于文件中。")
    except ImportError:
         # 如果缺少必要的库 (pandas 或 openpyxl)
         print("错误: 缺少必要的库。请确保已安装 pandas 和 openpyxl。")
         print("可以通过 pip 安装: pip install pandas openpyxl")
    except Exception as e:
        # 捕获所有其他未预料到的错误
        print(f"处理过程中发生未知错误: {e}")

# --- 程序入口 ---
# 当脚本作为主程序直接运行时（而不是被其他脚本导入时），执行以下代码
if __name__ == "__main__":
    # 调用核心函数开始执行拆分逻辑
    split_excel_by_column() 
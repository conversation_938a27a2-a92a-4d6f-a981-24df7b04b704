#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索结果分析工具
功能：分析search_excel.py生成的CSV结果文件
作者：自动生成
日期：2025年
"""

import pandas as pd
import os
import glob
from datetime import datetime

def find_latest_result_file(output_dir='output'):
    """
    查找最新的搜索结果文件
    
    参数:
        output_dir (str): 输出目录路径
    
    返回:
        str: 最新结果文件的路径，如果没找到返回None
    """
    # 查找所有搜索结果文件
    pattern = os.path.join(output_dir, 'excel_search_results_*.csv')
    result_files = glob.glob(pattern)
    
    if not result_files:
        print(f"在目录 '{output_dir}' 中没有找到搜索结果文件")
        return None
    
    # 找到最新的文件（按修改时间排序）
    latest_file = max(result_files, key=os.path.getmtime)
    return latest_file

def analyze_search_results(csv_file):
    """
    分析搜索结果
    
    参数:
        csv_file (str): CSV结果文件路径
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"=== 搜索结果分析报告 ===")
        print(f"结果文件: {os.path.basename(csv_file)}")
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总记录数: {len(df)}")
        print()
        
        # 1. 按文件名统计
        print("1. 按文件名统计匹配记录:")
        file_counts = df['fileName'].value_counts()
        for filename, count in file_counts.items():
            print(f"   - {filename}: {count} 条记录")
        print()
        
        # 2. 按sheet页统计
        print("2. 按Sheet页统计匹配记录:")
        sheet_counts = df['sheetName'].value_counts()
        for sheet_name, count in sheet_counts.items():
            print(f"   - {sheet_name}: {count} 条记录")
        print()
        
        # 3. 分析匹配的字段类型
        print("3. 匹配字段分析:")
        if 'matchedCells' in df.columns:
            # 提取匹配字段名（在冒号之前的部分）
            matched_fields = []
            for matched_cells in df['matchedCells'].dropna():
                fields = matched_cells.split(', ')
                for field in fields:
                    if ':' in field:
                        field_name = field.split(':')[0]
                        matched_fields.append(field_name)
            
            field_counts = pd.Series(matched_fields).value_counts()
            for field_name, count in field_counts.items():
                print(f"   - {field_name}: {count} 次匹配")
        print()
        
        # 4. 显示一些样例记录
        print("4. 样例记录 (前3条):")
        display_columns = ['fileName', 'sheetName', 'rowIndex', 'matchedCells']
        
        # 只显示存在的列
        available_columns = [col for col in display_columns if col in df.columns]
        
        for idx, row in df.head(3).iterrows():
            print(f"   记录 {idx + 1}:")
            for col in available_columns:
                print(f"     {col}: {row[col]}")
            print()
        
        # 5. 如果有原始数据列，显示一些关键信息
        original_columns = [col for col in df.columns if col.startswith('原始_')]
        if original_columns:
            print("5. 包含的原始数据字段:")
            for col in sorted(original_columns):
                # 计算非空值数量
                non_null_count = df[col].notna().sum()
                if non_null_count > 0:
                    print(f"   - {col.replace('原始_', '')}: {non_null_count} 条有数据")
            print()
        
        return df
        
    except Exception as e:
        print(f"分析结果时出错: {str(e)}")
        return None

def export_summary(df, output_file):
    """
    导出汇总信息到新的CSV文件
    
    参数:
        df (DataFrame): 搜索结果数据
        output_file (str): 输出文件路径
    """
    try:
        # 创建汇总数据
        summary_data = []
        
        for idx, row in df.iterrows():
            # 提取关键信息
            summary_row = {
                '序号': idx + 1,
                '文件名': row['fileName'],
                'Sheet页': row['sheetName'],
                'Excel行号': row['rowIndex'],
                '匹配字段': row.get('matchedCells', ''),
                '搜索关键字': row['searchString'],
                '发现时间': row['foundTime']
            }
            
            # 添加一些关键的原始字段（如果存在）
            key_fields = ['原始_name', '原始_src-addr', '原始_dst-addr', '原始_address_name', '原始_value', '原始_service']
            for field in key_fields:
                if field in row and pd.notna(row[field]) and str(row[field]).strip():
                    field_name = field.replace('原始_', '')
                    summary_row[field_name] = row[field]
            
            summary_data.append(summary_row)
        
        # 创建DataFrame并保存
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"汇总信息已保存到: {output_file}")
        
    except Exception as e:
        print(f"导出汇总信息时出错: {str(e)}")

def main():
    """
    主函数
    """
    print("Excel搜索结果分析工具")
    print("=" * 40)
    
    # 查找最新的结果文件
    latest_file = find_latest_result_file()
    
    if not latest_file:
        return
    
    print(f"找到最新结果文件: {os.path.basename(latest_file)}")
    print()
    
    # 分析结果
    df = analyze_search_results(latest_file)
    
    if df is not None:
        # 生成汇总文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        summary_file = f"output/search_summary_{timestamp}.csv"
        export_summary(df, summary_file)

if __name__ == "__main__":
    main() 
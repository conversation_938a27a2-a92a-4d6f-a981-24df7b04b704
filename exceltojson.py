# Description: Excel转JSON工具

import pandas as pd
import json
import os

def get_valid_input(prompt, validation_func, error_msg):
    """获取带验证的输入"""
    while True:
        try:
            value = input(prompt)
            if validation_func(value):
                return value
            print(error_msg)
        except Exception as e:
            print(f"发生错误: {str(e)}")

def validate_file(path):
    """验证文件是否存在"""
    return os.path.isfile(path)

def validate_dir(path):
    """验证目录是否有效"""
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except:
        return False

def clean_data(value):
    """清理数据：处理空值和异常格式"""
    if pd.isna(value):
        return None
    if isinstance(value, str) and value.startswith('='):
        return None
    return value

def excel_to_json():
    """主转换函数"""
    try:
        # 获取输入路径
        excel_path = get_valid_input(
            "请输入Excel文件路径：",
            validate_file,
            "文件不存在或路径无效，请重新输入！"
        )

        # 获取输出路径
        output_dir = get_valid_input(
            "请输入输出目录路径：",
            validate_dir,
            "目录创建失败，请检查路径权限后重试！"
        )

        # 读取Excel文件
        with pd.ExcelFile(excel_path) as xls:
            sheets = xls.sheet_names  # 获取所有sheet名称

            for sheet_name in sheets:
                try:
                    # 读取工作表数据
                    df = pd.read_excel(xls, sheet_name=sheet_name)

                    # 数据清洗和转换
                    # df = df.applymap(clean_data)
                    df = df.apply(lambda x: x.map(clean_data))

                    records = df.to_dict(orient='records')

                    # 生成安全文件名
                    safe_name = "".join(
                        [c if c.isalnum() else "_" for c in sheet_name]
                    ).rstrip("_") + ".json"

                    # 写入JSON文件
                    output_path = os.path.join(output_dir, safe_name)
                    with open(output_path, 'w', encoding='utf-8') as f:
                        json.dump(
                            records,
                            f,
                            ensure_ascii=False,
                            indent=2,
                            default=str
                        )

                    print(f"成功转换工作表 [{sheet_name}] -> {output_path}")

                except Exception as e:
                    print(f"转换工作表 [{sheet_name}] 失败: {str(e)}")
                    continue

        print("\n转换完成！")
        return True

    except Exception as e:
        print(f"发生严重错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("=== Excel转JSON工具 ===")
    while True:
        if excel_to_json():
            break
        retry = input("是否重试？(y/n): ").lower()
        if retry != 'y':
            break
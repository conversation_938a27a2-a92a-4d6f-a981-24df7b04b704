# Description: 该脚本用于合并多个 Excel 文件中的数据，并保存到一个新的 Excel 文件中。

import os  # 导入模块，用于文件路径操作
import pandas as pd  # 导入 pandas 库，用于处理数据
from concurrent.futures import ThreadPoolExecutor  # 导入线程池多线程执行
import time  # 用于计算程序运行时长


def process_file_in_chunks(file, field_name, chunksize=10000):
    """
    分块读取文件数据，节省内存占用。
    """
    file_basename = os.path.basename(file)
    try:
        # 判断文件类型并创建数据加载器
        if file.endswith(".csv"):
            reader = pd.read_csv(file, chunksize=chunksize)
        elif file.endswith((".xlsx", ".xls")):
            reader = pd.read_excel(file, engine="openpyxl", chunksize=chunksize)
        else:
            print(f"文件 {file} 格式不支持，跳过。")
            return None

        for chunk in reader:
            # 检查字段是否存在于该分块中
            if field_name not in chunk.columns:
                print(f"文件 {file} 的分块中不存在字段: {field_name}，跳过该分块。")
                continue
            # 添加来源文件列
            chunk["来源文件"] = file_basename
            yield chunk  # 使用生成器逐块返回数据
    except Exception as e:
        print(f"处理文件 {file} 时出错：{e}")
        return None


def read_and_process_file(file, field_name):
    """
    处理单个文件，返回所有分块组成的完整 DataFrame。
    """
    processed_data = []
    for chunk in process_file_in_chunks(file, field_name):
        processed_data.append(chunk)
    if processed_data:
        return pd.concat(processed_data, ignore_index=True)  # 合并分块数据
    return pd.DataFrame()  # 如果没有任何分块数据，返回空数据框


def find_and_save_field_values(field_name, file_paths, output_file):
    """
    对比多个文件中某字段的值，整合所有数据（包含来源文件名），并将结果保存到新的 Excel 文件中。

    参数:
        field_name (str): 要对比的字段名（需要存在于文件的列中）。
        file_paths (list): 文件路径列表，支持 CSV 或 Excel 格式文件。
        output_file (str): 结果保存的 Excel 文件路径。
    """
    combined_data = []  # 用列表保存每个文件的处理结果

    # 使用多线程并行处理每个文件
    with ThreadPoolExecutor() as executor:
        results = list(executor.map(lambda f: read_and_process_file(f, field_name), file_paths))
        for result in results:
            if not result.empty:  # 添加非空数据
                combined_data.append(result)

    # 如果没有任何有效数据，直接退出
    if not combined_data:
        print("未找到任何有效数据，无法继续处理。")
        return

    # 合并所有文件数据为一个 DataFrame
    combined_data = pd.concat(combined_data, ignore_index=True)

    # 对指定字段的值进行统计，获取每个值出现的次数
    value_counts = combined_data[field_name].value_counts()

    # 筛选出“相同字段值”：值出现次数大于 1 的数据
    same_values_df = value_counts[value_counts > 1].reset_index()
    same_values_df.columns = [field_name, "出现次数"]  # 重命名列名

    # 筛选出“不同字段值”：值出现次数等于 1 的数据
    unique_values_df = value_counts[value_counts == 1].reset_index()
    unique_values_df.columns = [field_name, "出现次数"]  # 重命名列名

    # 将结果写入到 Excel 文件中
    with pd.ExcelWriter(output_file, engine="openpyxl") as writer:
        # 保存相同字段值的统计结果到第一张工作表
        same_values_df.to_excel(writer, index=False, sheet_name="相同字段值")
        # 保存不同字段值的统计结果到第二张工作表
        unique_values_df.to_excel(writer, index=False, sheet_name="不同字段值")

        # 将整合的原始数据分块写入第三张工作表
        rows_per_chunk = 10000  # 每次写入 10,000 行
        current_row = 0  # 跟踪当前写入到的行数

        for i in range(0, len(combined_data), rows_per_chunk):
            chunk = combined_data.iloc[i:i + rows_per_chunk]
            if current_row == 0:
                chunk.to_excel(writer, index=False, sheet_name="原始数据整合", startrow=current_row)  # 首次写入，包括标题
            else:
                chunk.to_excel(writer, index=False, sheet_name="原始数据整合", startrow=current_row,
                               header=False)  # 追加写入，无标题
            current_row += len(chunk)  # 累计当前写入的行数

    # 最终结果保存成功的提示
    print(f"结果已保存到文件: {output_file}")


if __name__ == "__main__":
    # 主程序：从命令行交互获取输入并调用上述函数

    # 记录程序开始时间
    start_time = time.time()

    # 提示用户输入文件数量
    try:
        file_count = int(input("请输入要对比的文件数量: ").strip())
    except ValueError:
        print("请输入有效的数字！")
        exit()

    if file_count <= 0:
        print("文件数量必须大于 0！")
        exit()

    file_paths = []  # 用于存储所有输入的文件路径

    # 循环接收每个文件的路径
    for i in range(file_count):
        file_path = input(f"请输入第 {i + 1} 个文件的路径: ").strip()
        # 检查文件路径是否存在
        if not os.path.exists(file_path):
            print(f"文件 {file_path} 不存在，请检查路径！")
        else:
            file_paths.append(file_path)  # 如果2文件存在，将路径添加到列表中

    # 如果输入的文件路径列表为空，提示错误并退出程序
    if not file_paths:
        print("未提供有效的文件，程序退出。")
        exit()

    # 提示用户输入要对比的字段名
    field_name = input("请输入要对比的字段名: ").strip()
    if not field_name:
        print("字段名不能为空，程序退出。")
        exit()

    # 提示用户输入输出文件的路径
    output_file = input("请输入输出文件路径 (例如: result.xlsx): ").strip()
    if not output_file.endswith(".xlsx"):
        output_file += ".xlsx"  # 自动补全扩展名

    # 调用主函数，执行字段值对比并保存结果
    find_and_save_field_values(field_name, file_paths, output_file)

    # 记录程序结束时间并计算运行时长
    end_time = time.time()
    print(f"程序执行完毕，总用时: {end_time - start_time:.2f} 秒")